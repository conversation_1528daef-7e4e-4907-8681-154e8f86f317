# Report Signing Functionality

## Overview

This document describes the implementation of the report signing functionality in the UiNotes reports system. The feature allows users to digitally sign reports that require signatures, using the `signReport` API from the dataServer backend.

## Components

### 1. ReportGroup Component (`/src/UiNotes/reports/components/ReportGroup.tsx`)

**Purpose**: Displays a group of reports by type and handles the sign button interaction.

**Key Features**:
- Shows sign button only for reports with `required_signature: true` and `signed: false`
- <PERSON><PERSON> sign button click and opens SignatureDialog
- Implements actual signing using `ReportAPI.signReport`
- Provides success/error feedback via snackbar notifications
- Refreshes report list after successful signing

**Props**:
```typescript
interface ReportGroupProps {
  type: string;
  reports: any[];
  onReportClick?: (report: any) => void;
  selectedReport?: any;
  campus: string;
  patID: string;
  onDelete?: () => void;
  onRefresh?: () => void; // New prop for refreshing after signing
}
```

### 2. SignatureDialog Component (`/src/UiNotes/reports/components/SignatureDialog.tsx`)

**Purpose**: Provides the UI for entering signature comments and confirming the signing action.

**Key Features**:
- Text area for entering signature comment (required)
- Validation to ensure comment is not empty
- Loading state during signing process
- Error handling and display
- Proper form validation and user feedback

**Props**:
```typescript
interface SignatureDialogProps {
  open: boolean;
  onClose: () => void;
  onSign: (comment: string) => Promise<void>; // Updated to accept comment
  reportTitle: string;
  reportType: string;
}
```

## API Integration

### ReportAPI.signReport Method

The signing functionality uses the `signReport` method from `ReportAPI`:

```typescript
static async signReport(
  campus: string,
  patID: string,
  noteID: string,
  noteType: string,
  comment: string,
  staffid?: number,
  ccitoken?: string
): Promise<SignNoteResponse>
```

**Parameters**:
- `campus`: Campus identifier
- `patID`: Patient ID
- `noteID`: Note/report ID to sign
- `noteType`: Type of note (e.g., "Progress Note", "ED Progress Note")
- `comment`: Signature comment (required)
- `staffid`: Optional staff ID (uses `Cci.util.Staff.getSid()` if not provided)
- `ccitoken`: Optional CCIToken (uses localStorage/sessionStorage if not provided)

## API Usage for Fetching Notes

When fetching notes from the backend, the following rules apply:

| Endpoint                                      | note_id | note_type | Returns                |
|-----------------------------------------------|---------|-----------|------------------------|
| `/api/patients/{campus}/{pat_id}/notes`       | ❌      | ❌        | All notes              |
| `/api/patients/{campus}/{pat_id}/notes/{note_id}?note_type=...` | ✅      | ✅        | Specific note (by key+type) |

- To fetch a specific note, you **must** provide both `note_id` (as a path parameter) and `note_type` (as a query parameter).
- If you provide `note_id` without `note_type`, the API will return a 400 error.
- To fetch all notes, use the `/notes` endpoint with no `note_id` or `note_type`.

**Example:**

Fetch all notes:
```http
GET /api/patients/campus1/12345/notes
```

Fetch a specific note:
```http
GET /api/patients/campus1/12345/notes/**********?note_type=Progress%20Note
```

If you call `/notes/{note_id}` without `note_type`, you will receive:
```json
{
  "detail": "note_type is required to fetch a specific note. Both note_id and note_type must be provided."
}
```

## User Flow

1. **Report Display**: Reports with `required_signature: true` and `signed: false` show a sign button (Create icon)
2. **Sign Button Click**: User clicks the sign button, which opens the SignatureDialog
3. **Comment Entry**: User enters a signature comment in the dialog
4. **Validation**: System validates that comment is not empty
5. **Signing Process**: 
   - Dialog shows loading state
   - API call is made to `signReport` endpoint
   - Staff ID and CCIToken are automatically retrieved
6. **Success/Error Handling**:
   - Success: Dialog closes, success message shown, report list refreshes
   - Error: Error message displayed in dialog, user can retry
7. **UI Update**: After successful signing, the report no longer shows the sign button

## Error Handling

### Client-Side Validation
- Comment field cannot be empty
- Sign button is disabled until comment is entered
- Loading states prevent multiple submissions

### API Error Handling
- Network errors are caught and displayed
- Server errors (4xx, 5xx) are handled gracefully
- Error messages are shown in the dialog

### User Feedback
- Success messages via snackbar notifications
- Error messages in the dialog
- Loading indicators during API calls

## Security Considerations

### Authentication
- Uses existing CCIDB authentication system
- Automatically retrieves staff ID from `Cci.util.Staff.getSid()`
- Uses stored CCIToken from localStorage/sessionStorage

### Authorization
- Only reports with `required_signature: true` can be signed
- Already signed reports cannot be signed again
- Staff must have appropriate permissions in CCIDB

## Testing

### Test Coverage
The implementation includes comprehensive Playwright tests (`report-group-sign.test.ts`):

1. **Sign Button Visibility**: Tests that sign buttons only appear for appropriate reports
2. **Dialog Functionality**: Tests dialog opening and form validation
3. **Comment Validation**: Tests that comment is required before signing
4. **Successful Signing**: Tests complete signing flow with success feedback
5. **Error Handling**: Tests error scenarios and user feedback
6. **Edge Cases**: Tests already signed reports and reports without required signatures

### Test Scenarios
- ✅ Show sign button for unsigned reports with required signature
- ✅ Open signature dialog when sign button is clicked
- ✅ Require comment before allowing signature
- ✅ Successfully sign a report with valid comment
- ✅ Show error message when signing fails
- ✅ Don't show sign button for already signed reports
- ✅ Don't show sign button for reports without required signature

## Integration Points

### Parent Components
- **ReportListPanel**: Passes `onRefresh` callback to ReportGroup
- **ReportList**: Groups reports by type and renders ReportGroup components

### Backend Integration
- **dataServer**: Provides `/api/patients/{campus}/{patID}/notes/{noteID}/sign` endpoint
- **CCIDB**: Stores signature data in `AI Notes Signature` table
- **Staff Database**: Retrieves full staff names for signature display

## Configuration

### Note Types Configuration
Signature requirements are configured in `dataServer/app/config/note_types_config.json`:

```json
{
  "Progress Note": { "nit": 0, "required_signature": true },
  "ED Progress Note": { "nit": 1, "required_signature": true },
  "Discharge Summary": { "nit": 2, "required_signature": false },
  "Consultation Note": { "nit": 3, "required_signature": false },
  "Procedure Note": { "nit": 4, "required_signature": false },
  "Radiology Note": { "nit": 5, "required_signature": true }
}
```

## Future Enhancements

### Potential Improvements
1. **Digital Signature Verification**: Add actual digital signature verification
2. **Signature Templates**: Pre-defined signature comment templates
3. **Batch Signing**: Sign multiple reports at once
4. **Signature History**: View detailed signature history
5. **Audit Trail**: Enhanced logging and audit capabilities

### UI Enhancements
1. **Signature Status Indicators**: Visual indicators for signature status
2. **Signature Preview**: Preview signature before confirming
3. **Keyboard Shortcuts**: Quick access to signing functionality
4. **Mobile Optimization**: Better mobile experience for signing

## Troubleshooting

### Common Issues

1. **Sign Button Not Visible**
   - Check that report has `required_signature: true`
   - Check that report has `signed: false`
   - Verify user has appropriate permissions

2. **Signing Fails**
   - Check network connectivity
   - Verify CCIDB authentication
   - Check server logs for detailed error messages
   - Ensure comment is not empty

3. **Staff ID Issues**
   - Verify `Cci.util.Staff.getSid()` returns valid staff ID
   - Check that staff exists in CCIDB staff database

### Debug Information
- Check browser console for API call details
- Verify localStorage/sessionStorage contains valid webtoken
- Check network tab for API request/response details
- Review server logs for backend errors 