{"[html]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[sql]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jinja-html]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.detectIndentation": false, "editor.insertSpaces": true, "[javascript]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.tabSize": 2}, "[typescript]": {"editor.tabSize": 2}, "[jsx]": {"editor.tabSize": 2}, "[tld]": {"editor.tabSize": 2}, "[jsp]": {"editor.tabSize": 2}, "[typescriptreact]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.rulers": [80, 120]}, "[python]": {"editor.tabSize": 4, "editor.insertSpaces": true, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.formatOnType": true, "editor.defaultFormatter": "charliermarsh.ruff"}, "python.formatting.provider": "none", "python.analysis.completeFunctionParens": true, "python.languageServer": "<PERSON><PERSON><PERSON>", "prettier.printWidth": 80, "prettier.trailingComma": "es5", "html.format.wrapAttributes": "aligned-multiple"}