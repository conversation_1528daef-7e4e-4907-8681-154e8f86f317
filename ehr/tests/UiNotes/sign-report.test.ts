import { test, expect } from '@playwright/test';
import { ReportAPI } from '../../src/UiNotes/reports/services/ReportAPI';

test.describe('ReportAPI signReport Function', () => {
  test('should successfully sign a report', async ({ page }) => {
    // Mock the sign endpoint
    await page.route('**/api/patients/*/notes/*/sign', async route => {
      const requestBody = await route.request().postDataJSON();
      
      // Verify the request body structure
      expect(requestBody).toHaveProperty('note_type');
      expect(requestBody).toHaveProperty('comment');
      expect(requestBody).toHaveProperty('staffid');
      expect(requestBody).toHaveProperty('ccitoken');
      
      // Mock successful response
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Note signed successfully'
        })
      });
    });

    // Mock localStorage and Cci for testing
    await page.addInitScript(() => {
      // Mock localStorage
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: (key: string) => {
            if (key === 'webtoken') return 'test-token';
            return null;
          },
          setItem: () => {},
          removeItem: () => {},
          clear: () => {}
        },
        writable: true
      });

      // Mock Cci
      (window as any).Cci = {
        util: {
          Staff: {
            getSid: () => 12345
          }
        }
      };
    });

    // Navigate to a page where we can test the API
    await page.goto('/uinotes');
    
    // Test the signReport function
    const result = await page.evaluate(async () => {
      const { ReportAPI } = await import('../../src/UiNotes/reports/services/ReportAPI');
      
      try {
        const response = await ReportAPI.signReport(
          'campus1',
          '12345',
          '**********',
          'Progress Note',
          'Reviewed and approved by Dr. Smith'
        );
        return { success: true, response };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(result.success).toBe(true);
    expect(result.response?.message).toBe('Note signed successfully');
  });

  test('should handle sign report errors', async ({ page }) => {
    // Mock the sign endpoint to return an error
    await page.route('**/api/patients/*/notes/*/sign', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: 'Failed to sign note'
        })
      });
    });

    // Mock localStorage and Cci for testing
    await page.addInitScript(() => {
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: (key: string) => {
            if (key === 'webtoken') return 'test-token';
            return null;
          },
          setItem: () => {},
          removeItem: () => {},
          clear: () => {}
        },
        writable: true
      });

      (window as any).Cci = {
        util: {
          Staff: {
            getSid: () => 12345
          }
        }
      };
    });

    await page.goto('/uinotes');
    
    const result = await page.evaluate(async () => {
      const { ReportAPI } = await import('../../src/UiNotes/reports/services/ReportAPI');
      
      try {
        const response = await ReportAPI.signReport(
          'campus1',
          '12345',
          '**********',
          'Progress Note',
          'Reviewed and approved by Dr. Smith'
        );
        return { success: true, response };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(result.success).toBe(false);
    expect(result.error).toContain('Failed to sign report');
  });

  test('should use provided staffid and ccitoken when available', async ({ page }) => {
    let capturedRequestBody: any = null;

    await page.route('**/api/patients/*/notes/*/sign', async route => {
      capturedRequestBody = await route.request().postDataJSON();
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Note signed successfully'
        })
      });
    });

    await page.goto('/uinotes');
    
    await page.evaluate(async () => {
      const { ReportAPI } = await import('../../src/UiNotes/reports/services/ReportAPI');
      
      await ReportAPI.signReport(
        'campus1',
        '12345',
        '**********',
        'Progress Note',
        'Reviewed and approved by Dr. Smith',
        99999, // Custom staffid
        'custom-token' // Custom ccitoken
      );
    });

    // Wait a moment for the request to complete
    await page.waitForTimeout(1000);

    // Verify the custom values were used
    expect(capturedRequestBody.staffid).toBe(99999);
    expect(capturedRequestBody.ccitoken).toBe('custom-token');
    expect(capturedRequestBody.note_type).toBe('Progress Note');
    expect(capturedRequestBody.comment).toBe('Reviewed and approved by Dr. Smith');
  });
}); 