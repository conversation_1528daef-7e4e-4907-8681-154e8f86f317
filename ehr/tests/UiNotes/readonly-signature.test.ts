import { test, expect } from '@playwright/test';

test.describe('UiNotes Read-Only Signature Functionality', () => {
  test('should disable save button for signed notes with required signatures', async ({ page }) => {
    // Mock the API response to include signature data
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: true,
          signatures: [
            {
              signedBy: 12345,
              signedByName: 'Dr. <PERSON>',
              signedAt: '2024-01-01T10:00:00Z',
              comment: 'Reviewed and approved'
            }
          ],
          lastSignedAt: '2024-01-01T10:00:00Z',
          lastSignedBy: 'Dr. <PERSON>'
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the signed note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Check that the save button is disabled
    const saveButton = page.locator('[data-testid="save-button"]');
    await expect(saveButton).toBeDisabled();
    
    // Check that the tooltip shows the correct message
    await saveButton.hover();
    await expect(page.locator('.MuiTooltip-tooltip')).toContainText('Note is read-only - has required signature and has been signed');
    
    // Check that the read-only indicator is visible
    await expect(page.locator('[data-testid="readonly-indicator"]')).toBeVisible();
  });

  test('should allow editing for unsigned notes with required signatures', async ({ page }) => {
    // Mock the API response for an unsigned note
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: false,
          signatures: [],
          lastSignedAt: null,
          lastSignedBy: null
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the unsigned note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Check that the save button is enabled
    const saveButton = page.locator('[data-testid="save-button"]');
    await expect(saveButton).toBeEnabled();
    
    // Check that the read-only indicator is not visible
    await expect(page.locator('[data-testid="readonly-indicator"]')).not.toBeVisible();
    
    // Verify that the editor is editable
    const editor = page.locator('.ProseMirror');
    await editor.click();
    await editor.type('Additional content');
    
    // Check that the content was added
    await expect(editor).toContainText('Additional content');
  });

  test('should allow editing for notes without required signatures', async ({ page }) => {
    // Mock the API response for a note without required signatures
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Consultation Note',
          type: 'Consultation Note',
          content: 'Consultation findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: false,
          signed: false,
          signatures: [],
          lastSignedAt: null,
          lastSignedBy: null
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Check that the save button is enabled
    const saveButton = page.locator('[data-testid="save-button"]');
    await expect(saveButton).toBeEnabled();
    
    // Check that the read-only indicator is not visible
    await expect(page.locator('[data-testid="readonly-indicator"]')).not.toBeVisible();
    
    // Verify that the editor is editable
    const editor = page.locator('.ProseMirror');
    await editor.click();
    await editor.type('Additional content');
    
    // Check that the content was added
    await expect(editor).toContainText('Additional content');
  });

  test('should display provider signature information for signed reports', async ({ page }) => {
    // Mock the API response to include signature data
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: true,
          signatures: [
            {
              signedBy: 12345,
              signedByName: 'John Eagle, MD',
              signedAt: '2025-06-08T12:00:00Z',
              comment: 'Reviewed and approved by Dr. Eagle'
            }
          ],
          lastSignedAt: '2025-06-08T12:00:00Z',
          lastSignedBy: 'John Eagle, MD'
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the signed note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Check that the signature section is displayed
    await expect(page.locator('text=Provider Signature')).toBeVisible();
    
    // Check that the provider name is displayed
    await expect(page.locator('text=John Eagle, MD')).toBeVisible();
    
    // Check that the signature date and time are displayed
    await expect(page.locator('text=Signed: 06/08/2025 | 12:00')).toBeVisible();
    
    // Check that the signature comment is displayed
    await expect(page.locator('text=Comment: Reviewed and approved by Dr. Eagle')).toBeVisible();
  });

  test('should display legacy signature information when signatures array is not available', async ({ page }) => {
    // Mock the API response with legacy signature data
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Consultation Note',
          type: 'Consultation Note',
          content: 'Consultation findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: true,
          signatures: [], // Empty signatures array
          lastSignedAt: '2025-06-08T12:00:00Z',
          lastSignedBy: 'Jane Smith, MD'
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the signed note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Check that the signature section is displayed
    await expect(page.locator('text=Provider Signature')).toBeVisible();
    
    // Check that the provider name is displayed using legacy data
    await expect(page.locator('text=Jane Smith, MD')).toBeVisible();
    
    // Check that the signature date and time are displayed
    await expect(page.locator('text=Signed: 06/08/2025 | 12:00')).toBeVisible();
  });

  test('should not display signature section for unsigned reports', async ({ page }) => {
    // Mock the API response for an unsigned note
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: false,
          signatures: [],
          lastSignedAt: null,
          lastSignedBy: null
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the unsigned note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Check that the signature section is not displayed
    await expect(page.locator('text=Provider Signature')).not.toBeVisible();
  });

  test('should hide delete button for signed reports', async ({ page }) => {
    // Mock the API response for a signed report
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: true,
          signatures: [
            {
              signedBy: 12345,
              signedByName: 'Dr. John Smith',
              signedAt: '2024-01-01T10:00:00Z',
              comment: 'Reviewed and approved'
            }
          ],
          lastSignedAt: '2024-01-01T10:00:00Z',
          lastSignedBy: 'Dr. John Smith'
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Hover over the note item to show the delete button
    await page.hover('[data-testid="note-item"]');
    
    // Check that the delete button is not visible for signed reports
    await expect(page.locator('[data-testid="delete-button"]')).not.toBeVisible();
  });

  test('should show delete button for unsigned reports', async ({ page }) => {
    // Mock the API response for an unsigned report
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: false,
          signatures: [],
          lastSignedAt: null,
          lastSignedBy: null
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Hover over the note item to show the delete button
    await page.hover('[data-testid="note-item"]');
    
    // Check that the delete button is visible for unsigned reports
    await expect(page.locator('[data-testid="delete-button"]')).toBeVisible();
  });

  test('should update report editor and provider signature section after signing', async ({ page }) => {
    // Mock the API response for an unsigned report
    await page.route('**/api/patients/*/notes', async route => {
      const mockNotes = [
        {
          noteID: '**********',
          title: 'Progress Note',
          type: 'Progress Note',
          content: 'Patient examination findings...',
          creationTime: '2024-01-01T00:00:00Z',
          modifiedTime: '2024-01-01T00:00:00Z',
          required_signature: true,
          signed: false,
          signatures: [],
          lastSignedAt: null,
          lastSignedBy: null
        }
      ];
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNotes)
      });
    });

    // Mock the sign report API response
    await page.route('**/api/patients/*/notes/*/sign', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Report signed successfully'
        })
      });
    });

    // Mock the updated report data after signing
    await page.route('**/api/patients/*/notes/*', async route => {
      const updatedReport = {
        noteID: '**********',
        title: 'Progress Note',
        type: 'Progress Note',
        content: 'Patient examination findings...\n\n[SIGNED]',
        creationTime: '2024-01-01T00:00:00Z',
        modifiedTime: '2024-01-01T00:00:00Z',
        required_signature: true,
        signed: true,
        signatures: [
          {
            signedBy: 12345,
            signedByName: 'Dr. John Smith',
            signedAt: '2024-01-01T10:00:00Z',
            comment: 'Reviewed and approved'
          }
        ],
        lastSignedAt: '2024-01-01T10:00:00Z',
        lastSignedBy: 'Dr. John Smith'
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(updatedReport)
      });
    });

    // Navigate to the UiNotes page
    await page.goto('/uinotes');
    
    // Wait for the notes to load
    await page.waitForSelector('[data-testid="note-item"]', { timeout: 10000 });
    
    // Click on the unsigned note
    await page.click('[data-testid="note-item"]');
    
    // Wait for the editor to load
    await page.waitForSelector('.ProseMirror', { timeout: 10000 });
    
    // Click the sign button
    await page.click('[data-testid="sign-button"]');
    
    // Wait for the signature dialog
    await page.waitForSelector('[data-testid="signature-dialog"]', { timeout: 10000 });
    
    // Enter a comment
    await page.fill('[data-testid="signature-comment"]', 'Test signature comment');
    
    // Click the sign button in the dialog
    await page.click('[data-testid="sign-confirm-button"]');
    
    // Wait for the signature process to complete
    await page.waitForTimeout(2000);
    
    // Check that the provider signature section is now visible
    await expect(page.locator('text=Provider Signature')).toBeVisible();
    
    // Check that the signature information is displayed
    await expect(page.locator('text=Dr. John Smith')).toBeVisible();
    await expect(page.locator('text=Test signature comment')).toBeVisible();
    
    // Check that the editor content has been updated
    await expect(page.locator('.ProseMirror')).toContainText('[SIGNED]');
    
    // Check that the save button is now disabled
    const saveButton = page.locator('[data-testid="save-button"]');
    await expect(saveButton).toBeDisabled();
  });
}); 