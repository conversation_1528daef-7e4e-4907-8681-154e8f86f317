import { test, expect } from '@playwright/test';

test.describe('ReportGroup Signing Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the API responses
    await page.route('**/api/patients/*/notes', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            noteID: '**********',
            title: 'Test Progress Note',
            type: 'Progress Note',
            required_signature: true,
            signed: false,
            creationTime: '2024-01-01T00:00:00Z',
            modifiedTime: '2024-01-01T00:00:00Z'
          }
        ])
      });
    });

    // Mock the sign endpoint
    await page.route('**/api/patients/*/notes/*/sign', async (route) => {
      const request = route.request();
      const postData = request.postDataJSON();
      if (postData.note_type === 'Progress Note' && postData.comment) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            message: 'Note signed successfully'
          })
        });
      } else {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Invalid signature request'
          })
        });
      }
    });

    // Mock Cci object
    await page.addInitScript(() => {
      (window as any).Cci = {
        util: {
          Staff: {
            getSid: () => 12345
          }
        }
      };
    });

    // Navigate to the reports page
    await page.goto('/reports');
  });

  test('should show sign button for unsigned reports with required signature', async ({ page }) => {
    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Check that the sign button is visible for the report
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await expect(signButton).toBeVisible();
  });

  test('should open signature dialog when sign button is clicked', async ({ page }) => {
    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Click the sign button
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await signButton.click();

    // Check that the signature dialog opens
    const dialog = page.locator('text=Sign Report').first();
    await expect(dialog).toBeVisible();

    // Check that the dialog shows the correct report information
    await expect(page.locator('text=Test Progress Note')).toBeVisible();
    await expect(page.locator('text=Progress Note')).toBeVisible();
  });

  test('should require comment before allowing signature', async ({ page }) => {
    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Click the sign button
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await signButton.click();

    // Wait for dialog to open
    await page.waitForSelector('text=Sign Report');

    // Try to click sign without entering a comment
    const signButtonInDialog = page.locator('button:has-text("Sign Report")');
    await expect(signButtonInDialog).toBeDisabled();

    // Enter a comment
    const commentField = page.locator('textarea[placeholder*="signature comment"]');
    await commentField.fill('Reviewed and approved by Dr. Smith');

    // Now the sign button should be enabled
    await expect(signButtonInDialog).toBeEnabled();
  });

  test('should successfully sign a report with valid comment', async ({ page }) => {
    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Click the sign button
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await signButton.click();

    // Wait for dialog to open
    await page.waitForSelector('text=Sign Report');

    // Enter a comment
    const commentField = page.locator('textarea[placeholder*="signature comment"]');
    await commentField.fill('Reviewed and approved by Dr. Smith');

    // Click sign
    const signButtonInDialog = page.locator('button:has-text("Sign Report")');
    await signButtonInDialog.click();

    // Wait for the dialog to close
    await expect(page.locator('text=Sign Report')).not.toBeVisible();

    // Check for success message
    await expect(page.locator('text=Report signed successfully')).toBeVisible();
  });

  test('should show error message when signing fails', async ({ page }) => {
    // Mock a failed sign request
    await page.route('**/api/patients/*/notes/*/sign', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal server error'
        })
      });
    });

    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Click the sign button
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await signButton.click();

    // Wait for dialog to open
    await page.waitForSelector('text=Sign Report');

    // Enter a comment
    const commentField = page.locator('textarea[placeholder*="signature comment"]');
    await commentField.fill('Reviewed and approved by Dr. Smith');

    // Click sign
    const signButtonInDialog = page.locator('button:has-text("Sign Report")');
    await signButtonInDialog.click();

    // Check for error message in dialog
    await expect(page.locator('text=Failed to sign report')).toBeVisible();
  });

  test('should not show sign button for already signed reports', async ({ page }) => {
    // Mock reports with signed status
    await page.route('**/api/patients/*/notes', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            noteID: '**********',
            title: 'Test Progress Note',
            type: 'Progress Note',
            required_signature: true,
            signed: true,
            signatures: [
              {
                signedBy: 12345,
                signedByName: 'Dr. Smith',
                signedAt: '2024-01-01T00:00:00Z',
                comment: 'Reviewed and approved'
              }
            ],
            creationTime: '2024-01-01T00:00:00Z',
            modifiedTime: '2024-01-01T00:00:00Z'
          }
        ])
      });
    });

    // Reload the page
    await page.reload();

    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Check that the sign button is not visible
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await expect(signButton).not.toBeVisible();
  });

  test('should not show sign button for reports without required signature', async ({ page }) => {
    // Mock reports without required signature
    await page.route('**/api/patients/*/notes', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            noteID: '**********',
            title: 'Test Consultation Note',
            type: 'Consultation Note',
            required_signature: false,
            signed: false,
            creationTime: '2024-01-01T00:00:00Z',
            modifiedTime: '2024-01-01T00:00:00Z'
          }
        ])
      });
    });

    // Reload the page
    await page.reload();

    // Wait for the report list to load
    await page.waitForSelector('[data-testid="report-list"]');

    // Check that the sign button is not visible
    const signButton = page.locator('button[aria-label="Sign Report"]');
    await expect(signButton).not.toBeVisible();
  });
}); 