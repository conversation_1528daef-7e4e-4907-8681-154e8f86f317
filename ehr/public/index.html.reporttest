
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#000000" />
  <meta name="description" content="CCI EHR App" />
  <link rel="manifest" href="/manifest.json" />
  <title>CCI EHR DEV</title>
  <link type="text/css" href="/reporttest/../common/jslibs/video-js-5.10.7/video-js.min.css" rel="stylesheet" />
<link type="text/css" href="/reporttest/../common/jslibs/webix-3.2.0/webix.css" rel="stylesheet" />
<link type="text/css" href="/reporttest/../common/jslibs/webix-3.2.0/pivot/pivot.css" rel="stylesheet" />
<link type="text/css" href="/reporttest/../common/jslibs/jquery-ui-1.10.4/themes/base/minified/jquery-ui.min.css" rel="stylesheet" />
<link type="text/css" href="/reporttest/web/resources/cps-theme/cps-theme-all.css" rel="stylesheet" />
<link type="text/css" href="/reporttest/web/resources/css/cci-all-min.css" rel="stylesheet" />
<link type="text/css" href="/reporttest/web/resources/css/ie.css" rel="stylesheet" />
  <script type="text/javascript">
        if (!window.cci) window.cci = {};
        if (!window.cci.cfg) window.cci.cfg = {};
        window.cci.cfg = { baseUrl: "/reporttest",bmcisurl : "/bmcis"};
        if (!window.cci.cfg.debug) window.cci.cfg.debug = {};
        window.cci.cfg.debug.showDebugUI = false;
        if (!window.cci.cfg.misc) window.cci.cfg.misc = {};
        window.cci.cfg.misc.checkServerTZInterval = 600;
        window.cci.cfg.misc.autoSaveRecentInterval = 40;
        window.cci.cfg.misc.activityMonitorDebug = false;
        window.cci.cfg.misc.svgeditpath = "/reporttest/../common/jslibs/svg-edit-2.8.1";
        window.cci.cfg.useCfgCache = false;
        window.cci.cfg.divisionId = 500;
        window.cci.cfg.dssTimeout = 180000;
        window.cci.cfg.dssConnMonitorInterval = 600000;
        window.cci.cfg.dssGetCurrentVistADateTimeTimeOut = 5000;
        window.cci.cfg.logoutMinsAfterLock = -1;
        window.cci.cfg.startOfDay = 0;
        window.cci.cfg.bcma = 0;
        window.cci.cfg.ndasasso= 0;
        window.cci.cfg.oncall = 0;
        window.cci.cfg.allowRemoteWrite = false;
        window.cci.cfg.wantCPRSMed = 0;
        window.cci.cfg.wantVistACampus = 0;
        window.cci.cfg.measureEcgTool = 0;
        window.cci.cfg.bWantOldOEPrint = 0;
        window.cci.cfg.requireBatchRequests = 0;
        window.cci.cfg.isFHIRMode = 0;
        window.cci.cfg.showSuggestMicrobiology = 0;
        window.cci.cfg.showSuggestBiology = 0;
        window.cci.cfg.showAmbPOCT = 0;
        window.cci.cfg.vncRepeaterIP = '';    </script>
<script type="text/javascript" src="/reporttest/web/cci/util/ErrorHandler.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/es6-promise-2.0.1.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/jsqr-1.0.2-min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/Moment.js-2.10.3/moment.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/Moment.js-2.10.3/moment-timezone-with-data.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/extjs-5.0.0/ext-all.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/extjs-5.0.0/ext-charts/ext-charts.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/webix-3.2.0/webix.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/webix-3.2.0/pivot/pivot.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/video-js-5.10.7/video.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/jquery-1.11.0.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/jquery-ui-1.10.4/ui/minified/jquery-ui.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/Highstock-1.3.7/js/highstock.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/d3.min.js"></script>
<script type="text/javascript" src="/reporttest/web/cci/chart/sankey.js"></script>
<script type="text/javascript" src="/reporttest/web/ess/util/date.js"></script>
<script type="text/javascript" src="/reporttest/web/resources/cps-theme/cps-theme.js"></script>
<script type="text/javascript" src="/reporttest/client/Essentris/ux/form/TimePickerField.js"></script>
<script type="text/javascript" src="/reporttest/client/Essentris/ux/DateTimePicker.js"></script>
<script type="text/javascript" src="/reporttest/client/libs/DateTime/DateTimeField.js"></script>
<script type="text/javascript" src="/reporttest/client/libs/jdataview.js"></script>
<script type="text/javascript" src="/reporttest/client/libs/jparser.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/html2canvas.js"></script>
<script type="text/javascript" src="/reporttest/web/cci/util/JisonFormula.js"></script>
<script type="text/javascript" src="/reporttest/web/HobjReq.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/marked-0.3.6/marked.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/math.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/popper-1.12.9.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/tooltip-1.1.7.min.js"></script>
<script type="text/javascript" src="/reporttest/../common/jslibs/autonumeric/autoNumeric.min.js"></script>
<script type="text/javascript" src="/reporttest/web/cci-all.js"></script>
<script>cci.cfg.staffname = "demo"; cci.cfg.stafffullname = "Demo User"; cci.cfg.staffUseArea = true; cci.cfg.staffAreaInfo = ""; cci.cfg.bWantMultipleArea = 0;cci.cfg.DEA = ""; cci.cfg.epcsLogin = ""; cci.cfg.perms = "12:E 13:E 15:E 16:E 17:E 18:E 19:E 23:E 24:E 25:E 26:E 27:E 28:E 29:E 30:E 31:E 32:E 36:E 37:E 39:E 43:E 45:E 46:E 47:E 48:E 51:E 52:E 55:E 57:E 58:E 59:E 60:E 65:E 66:E 67:E 68:E 73:E 75:E 76:E 77:E 78:E 80:E 81:E 82:E 83:E 84:E 85:E 86:E 87:E 88:E 89:E 90:E 91:E 92:E 93:E 95:E 96:E 97:E 98:E 99:E 100:E 101:E 102:E 103:E 104:E 105:E 106:E 108:E 110:E 111:E 112:E 113:E 114:E 115:E 116:E 117:E 119:E 120:E 121:E 122:E 123:E 124:E 125:E 126:E 127:E 128:E 129:E 130:E 131:E 132:E 133:E 134:E 135:E 136:E 137:E 138:E 139:E 140:E 141:E 142:E 143:E 144:E 145:E 146:E 147:E 150:E 151:E 152:E 153:E 154:E 155:E 156:E 157:E 158:E 159:E 160:E 161:E 200:E 201:E 202:E 203:E 204:E 205:E 206:E 207:E 208:E 209:E 210:E"; cci.cfg.staffid = "41943085"; cci.cfg.dbpath = ""; cci.cfg.env = ""; cci.cfg.hidepatientheader = ""; cci.cfg.screen = ""; cci.cfg.screenapp = ""; cci.cfg.gotoscreen = ""; cci.cfg.gototab = ""; cci.cfg.ischild = ""; cci.cfg.serverTZOffset = -7;cci.cfg.campus = "smh"; cci.cfg.serverTZ = "PST8PDT";cci.cfg.ctrl = "cps"; cci.cfg.next = ""; cci.cfg.writeFromDisch = 0;cci.cfg.maxFutureTime = 72000;cci.cfg.clienthost = "************"; cci.cfg.workprocess = {"metaData":{"totalProperty":"total","root":"data","idProperty":"id","successProperty":"success","messageProperty":"message","fields":[{"id":"conf","name":"conf","type":"string"},{"id":"label","name":"label","type":"string"}]},"success":true,"total":5,"data":[{"conf":"default","label":"Default"},{"conf":"provider","label":"Physicians"},{"conf":"pharmacist","label":"Pharmacists"},{"conf":"nursing","label":"Nursing"},{"conf":"ancillary","label":"Ancillary"}],"columns":[{"header":"conf","dataIndex":"conf","sortable":true,"type":"string"},{"header":"label","dataIndex":"label","sortable":true,"type":"string"}]};cci.cfg.smartcardconfig = {"wantSmartCardPKI":false};cci.cfg.autoUpdateConfig = {"enabled":0,"PollingInterval":60,"StartCloseAfter":120,"StartKillAfter":180,"remindDlgInterval":60,"RemindMessage":"CEF sessions are being moved from this server. Please save data and re-launch CEF. If no action is taken, your session will exit when the count-down timer reaches zero","PullingInterval":3600,"WantNonIntrusiveUpdate":0,"WantFastUpdate":0};cci.cfg.ActiveTimeAfterDischarge = 4320;cci.cfg.UnlockableTimeAfterDischarge = 10080;cci.cfg.bDodidDisplay = 1;cci.cfg.bOpenSeparatePopWin = 0;cci.cfg.bMultipleCefInstance = 0;cci.cfg.bBrowserAccessRestricted = 0;cci.cfg.bWantQueryCHCS = 0;cci.cfg.bWantQueryImmu = 0;cci.cfg.maxEhrLogSize = 2;cci.cfg.wantpatientheadernotification = 0;cci.cfg.scanIvpump = 0;cci.cfg.wantivpumpnotification = 0;cci.cfg.wantcalcweightnotification = 0;cci.cfg.ajaxIoLogcfg = {"logio": true, "maxlen": 512, "sensitive": ["password", "verifycode"]};cci.cfg.bWantEdu = 0;cci.cfg.proType = "";cci.cfg.maxUploadFileSize = **********;cci.cfg.CefRestartOnMemEnable = 0;cci.cfg.CefRestartOnMemTerms = "";cci.cfg.CefRestartOnMemMethod = 0;cci.cfg.CefRestartMemLimit = -1;cci.cfg.CefMaxMem = 800;cci.cfg.bWantLiveWave = 0;cci.cfg.bWaveWantVerticalLines = 0;cci.cfg.waveVitalPaneUnits = [];cci.cfg.bWaveWantAnnotating = 0;cci.cfg.bWantRemoteCampus = 0;cci.cfg.bWantScramblePHI = 0;cci.cfg.bRandomizeAge = 0;cci.cfg.bRandomizeDOB = 0;cci.cfg.bDontMaskDOBYear = 0;cci.cfg.bWantScrambleDA = 0;cci.cfg.bWantScrambleSex = 0;cci.cfg.bWantScrambleAdmitDate = 0;cci.cfg.bRandomizeAdmitDate = 0;cci.cfg.bWantScrambleCampus = 0;cci.cfg.DemoCampuses= [];cci.cfg.bNoColorPrint = 0;cci.cfg.bWantScramblePHIByStaffid = [];cci.cfg.bWantLicenseControl = 1;cci.cfg.bSQLStatemntBufferSize = 1;cci.cfg.bDisableWaveAutoScale = 0;cci.cfg.extraWavesTime = 0;cci.cfg.bWantLeftPanelCollapsed = 0;cci.cfg.bAllowSwitchHost = 0;cci.cfg.webHostList = "";cci.cfg.webOptHost = ""; cci.cfg.dateFormat = "";cci.cfg.screenAppName = "";cci.cfg.transports = "websocket";cci.cfg.updateUIInterval = -1;cci.cfg.staffDefaultScreen = "";cci.cfg.campuslist = [{"name":"smh","disp":"SEG Test","url":""}];cci.cfg.allcampuslist = [{"name":"smh","disp":"SEG Test","url":""}];cci.cfg.msiuserlist = [];cci.cfg.noSocketIO = true;cci.cfg.bShowSingleCampusNameInUnitTree = false;cci.cfg.enableEncounter = 0;cci.cfg.enableCareTeam = 0;cci.cfg.careteamColumns = [];cci.cfg.careteamInPatColumns = [];cci.cfg.careteamOutPatColumns = [];cci.cfg.printLocalPDF = 0;cci.cfg.localRouteNumber = -1;cci.cfg.radPACsViewerLink = "";cci.cfg.hideDiagnosis = "0";cci.cfg.rwrestrictions = 0;cci.cfg.usingalternativetheme = 0;cci.cfg.switchCampusFromTop = 0;cci.cfg.isDataCenter = 0;cci.cfg.replaceFrontHost = false;cci.cfg.requestTimeout = -1;cci.cfg.initialHomeApp = "";cci.cfg.bXmlCancerExp = 0;cci.cfg.enableWebsockPrinting = 0;cci.cfg.reseturlitems = "";cci.cfg.bADTLeaveInCurrentUnit = 1;cci.cfg.demotableUnits = ["DISCH","NOBED","ED","ED-DISCH","TEST","SDS"];cci.cfg.remoteCampusList = "smh";cci.cfg.campusType = "";cci.cfg.sitetz = "America/New_York";cci.cfg.hasAmbulatory = 0;if(typeof webix !== "undefined") webix.cdn = "https://san1devpacs01.clinicomp.com:443/reporttest/../common/jslibs/webix-3.2.0";cci.cfg.bDisableLegacyOrdersTab = 0;cci.cfg.bCPOE2Suspend = 0;cci.cfg.bCPOEReadOnly = 0;cci.cfg.wantstudychangednotification = 0;cci.cfg.doReplaceFrontHost && cci.cfg.doReplaceFrontHost();</script>
<script></script>
<script>Ext.Loader.setConfig({ enabled: true, paths:{'Auth': '/reporttest/web/auth','Cci': '/reporttest/web/cci','Ext.ux': '/reporttest/web/ext/ux','Oe': '/reporttest/web/oe','Qd': '/reporttest/web/qd','Iplot': '/reporttest/web/iplot','Fetalstrip': '/reporttest/web/fetalstrip','Notes': '/reporttest/web/notes','Waveevent': '/reporttest/web/waveevent','Rtr': '/reporttest/web/rtr','Essentris': '/reporttest/client/Essentris','Problems': '/reporttest/web/problems','FormularyEditor': '/reporttest/web/formularyeditor','Ewd': '/reporttest/web/ewd','UserPreference': '/reporttest/web/userPreference','Medrecon': '/reporttest/web/medrecon','Pamrecon': '/reporttest/web/pamrecon','Ess': '/reporttest/web/ess','Mail': '/reporttest/web/secm','Ensc': '/reporttest/web/ensc','RxInv': '/reporttest/web/rxinv','Encm': '/reporttest/web/encm', } }); </script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
    // Function to check and set the height of the first child
    function checkAndSetHeight() {
      var rootDiv = document.getElementById('root');
        if (rootDiv && rootDiv.firstElementChild) {
          // Set the height of the first child div to 100%
          rootDiv.firstElementChild.style.height = '100%';
          // Clear the interval once the element is found and style is set
          clearInterval(intervalId);
        }
    }
    // Check every 100 milliseconds if the first child is available
    var intervalId = setInterval(checkAndSetHeight, 100);
    });
  </script>
  <link href="ehr.css" rel="stylesheet">
  <script src="ehr.umd.js"></script>
  <script>
    const onInit = () => {
      var createRoot = ehr.createRoot;
      var renderScreen = ehr.renderScreen;
      var root = createRoot(document.getElementById("root"));
      renderScreen({
        root: root,
        screenPath: "ProgressNote",
        screenParams: {}
      });
    }
    const onLoad = (event) => {
      Cci.util.RunTime.init(this, null, null, null);
      Cci.util.RunTime.initPromise.then(onInit);
    }
    // window.addEventListener("load", onLoad);
    // window.addEventListener("DOMContentLoaded", onLoad);
    setTimeout(onLoad, 2000);
  </script>
</head>

<body>
  <div id="root" style="height: 100%;"></div>
</body>

</html>
