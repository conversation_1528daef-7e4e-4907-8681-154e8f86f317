/**
 * MainContent.tsx
 *
 * @description Main content for Patient Overview module
 * <AUTHOR>
 */
import Config from "@cci-monorepo/config/Config";
import { useRef, useEffect, useState, useCallback } from "react";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import Box from "@mui/material/Box";
import useResizeObserver from "use-resize-observer";
import { ModuleHeaderHeight, USERCONTEXT } from "./components/common/Constants";
import {
  editModuleAtom,
  changeEditModuleAtom,
  pldDbpathsAtom,
  userContextAtom,
} from "./context/CommonAtoms";
import { nowColumnTimeAtom } from "./context/VitalSignsAtoms";
import { patOverviewModulesAtom } from "./context/ModuleAtoms";
import ModuleBox from "./components/common/ModuleBox";
import { getVisitType } from "@cci-monorepo/Pld/util/DataAPI";
import { serverNow } from "@cci-monorepo/common";

const margin = 16;

const cfg = cci.cfg;

export default function MainContent() {
  const setUserContext = useSetAtom(userContextAtom);
  const userContext = useAtomValue(userContextAtom);
  const setNowColumnTime = useSetAtom(nowColumnTimeAtom);
  const modules = useAtomValue(patOverviewModulesAtom);

  const ref = useRef<HTMLDivElement | null>(null);
  const [isWrapped, setIsWrapped] = useState<boolean>(false);
  const [editModule, setEditModule] = useAtom(editModuleAtom);
  const [changeModule, setChangeModule] = useAtom(changeEditModuleAtom);
  const preEditModuleRef = useRef<string>("");
  const preTopMostElementRef = useRef<HTMLDivElement | null>(null);
  const { width = 1 } = useResizeObserver<HTMLDivElement>({ ref });

  const [, setPldDbpaths] = useAtom(pldDbpathsAtom);

  useEffect(() => {
    if (Cci.util.Staff.getLicensetype() === "RN") {
      setUserContext(USERCONTEXT.NURSE);
    } else {
      setUserContext(USERCONTEXT.PROVIDER);
    }
    const updateNow = () => {
      setNowColumnTime(Cci.util.DateTime.dateToServerUnixtime(serverNow()));
    };
    updateNow();
    const now = Cci.util.DateTime.getCurrentUnixtime();
    const delay = (60 - (now % 60)) * 1000;
    const timeout = setTimeout(() => {
      updateNow();
      const interval = setInterval(updateNow, 60000);
      cleanup = () => clearInterval(interval);
    }, delay);
    let cleanup = () => clearTimeout(timeout);
    return () => cleanup();
  }, []);

  useEffect(() => {
    const params: any = {};
    params.campus = Cci.RunTime.getEncounterInfo().campus || cfg.campus;
    const onGetVisitTypeSuccess = (data: any) => {
      if (Config.inDevMode) {
        let arrayData: any = [];
        if (
          data.data &&
          data.data.header &&
          data.data.data &&
          data.data.data.length > 0
        ) {
          arrayData.push(data.data.header);
          arrayData.push(data.data.data[0]);
          let records =
            Cci.util.DataUtil.arrayDataToArrayRecord(arrayData) || null;
          if (records && records.length > 0) {
            Cci.RunTime.setEncounterInfo(records[0]);
          }
        }
      }
      let dbpaths: any = [];
      let dbpath: any = {};
      const campusIdx = data.data.header.indexOf("campus");
      const dbpathIdx = data.data.header.indexOf("dbpath");
      const fromTimeIdx = data.data.header.indexOf("FromTime");
      const toTimeIdx = data.data.header.indexOf("ToTime");
      const admitkeyIdx = data.data.header.indexOf("admitkey");
      const visitkeyIdx = data.data.header.indexOf("visitkey");
      data.data.data.forEach((entry: any) => {
        if (
          !(dbpath = dbpaths.find(
            (r: any) =>
              r.campus === entry[campusIdx] && r.dbpath === entry[dbpathIdx]
          ))
        ) {
          dbpaths.push({
            campus: entry[campusIdx],
            dbpath: entry[dbpathIdx],
            fromTime: entry[fromTimeIdx],
            toTime: entry[toTimeIdx],
            admitkey: entry[admitkeyIdx],
            visitkey: entry[visitkeyIdx],
          });
        } else {
          //using min(fromtime) for each encounter/dbpath
          if (dbpath.fromTime > entry[fromTimeIdx]) {
            dbpath.fromTime = entry[fromTimeIdx];
          }
        }
      });
      dbpaths.sort((a: any, b: any) => {
        return b.fromTime < a.fromTime ? -1 : 1;
      });

      const filteredDbpaths = dbpaths.filter((entry: any) => {
        return entry.fromTime <= Cci.RunTime.getEncounterInfo().FromTime;
      });

      setPldDbpaths(filteredDbpaths);
    };

    const onGetVisitTypeFailure = (error: any) => {
      console.error("System has failed to get visit type. " + error);
    };

    getVisitType(params, onGetVisitTypeSuccess, onGetVisitTypeFailure);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      ref &&
      ref.current &&
      ref.current.getBoundingClientRect().width < 1150
    ) {
      setIsWrapped(true);
      setEditModule("");
    } else {
      setIsWrapped(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [width]);

  const resetAllModule = useCallback(() => {
    let allElement = [];
    if (isWrapped) {
      const all = document.getElementsByClassName("all-box");
      allElement = Array.from(all[0]?.children || []);
    } else {
      const left = document.getElementsByClassName("left-box");
      const right = document.getElementsByClassName("right-box");
      const leftElement = Array.from(left[0]?.children || []);
      const rightElement = Array.from(right[0]?.children || []);
      allElement = leftElement.concat(rightElement);
    }
    allElement.forEach((e: any) => {
      e.style.marginTop = "0";
      e.style.marginLeft = "0";
      e.style.width = "100%";
    });
  }, [isWrapped]);

  useEffect(() => {
    resetAllModule();
    if (isWrapped) return;
    if (editModule !== "") {
      const left = document.getElementsByClassName("left-box");
      const right = document.getElementsByClassName("right-box");
      const leftElement = Array.from(left[0]?.children || []);
      const rightElement = Array.from(right[0]?.children || []);
      setTimeout(
        () => {
          const module = modules.find((item) => {
            return item.title === editModule;
          });
          const editElement = document.getElementById(module.componentId);
          if (editElement) {
            const editRect = editElement.getBoundingClientRect();
            let topMostElement: any = null;
            if (
              !preTopMostElementRef.current ||
              (preEditModuleRef.current !== "" &&
                editModule !== preEditModuleRef.current)
            ) {
              if (module.isPldWidget) {
                leftElement.forEach((e) => {
                  const rect = e.getBoundingClientRect();
                  if (
                    rect.right <= editRect.left &&
                    rect.bottom >= editRect.top &&
                    rect.top <= editRect.bottom
                  ) {
                    if (
                      !topMostElement ||
                      rect.top < topMostElement.getBoundingClientRect().top
                    ) {
                      topMostElement = e;
                    }
                  }
                });
              } else {
                rightElement.forEach((e) => {
                  const rect = e.getBoundingClientRect();
                  if (
                    rect.left >= editRect.right &&
                    rect.bottom >= editRect.top &&
                    rect.top <= editRect.bottom
                  ) {
                    if (
                      !topMostElement ||
                      rect.top < topMostElement.getBoundingClientRect().top
                    ) {
                      topMostElement = e;
                    }
                  }
                });
              }
            } else {
              topMostElement = preTopMostElementRef.current;
            }

            if (topMostElement) {
              preTopMostElementRef.current = topMostElement;
              const topRect = topMostElement.getBoundingClientRect();
              let top = 0;
              if (editRect.height === ModuleHeaderHeight) {
                top = topRect.top - margin;
              } else {
                if (module.isPldWidget) {
                  top = topRect.top === margin ? margin : topRect.top - margin;
                } else {
                  top = topRect.top === margin ? 0 : topRect.top;
                }
              }

              topMostElement.style.marginTop = editRect.bottom - top + "px";
            }

            if (editElement.parentElement) {
              const allWidth = ref.current!.getBoundingClientRect().width;
              const parentWidth = (allWidth - 3 * margin) / 2;
              const marginAdjustedWidth = allWidth - margin * 2;
              const percentageWidth = (marginAdjustedWidth / parentWidth) * 100;
              const percentageMarginLeft =
                ((-marginAdjustedWidth + editRect.width) / parentWidth) * 100;
              if (module.isPldWidget) {
                editElement.parentElement.style.marginLeft = `calc(${percentageMarginLeft}% - 16px)`;
              }
              editElement.parentElement.style.width = percentageWidth + "%";
            }
          }
          preEditModuleRef.current = editModule;
        },
        preEditModuleRef.current !== "" &&
          editModule !== preEditModuleRef.current
          ? 500
          : 0
      );
    } else {
      preEditModuleRef.current = "";
      preTopMostElementRef.current = null;
    }
  }, [editModule, isWrapped, modules, resetAllModule]);

  useEffect(() => {
    if (isWrapped) return;
    if (changeModule) {
      setTimeout(() => {
        const module = modules.find((item) => {
          return item.title === editModule;
        });
        const editElement = document.getElementById(module.componentId);
        if (editElement) {
          const editRect = editElement.getBoundingClientRect();
          let topMostElement: any = preTopMostElementRef.current;
          const topRect = topMostElement.getBoundingClientRect();
          if (topMostElement) {
            if (editRect.bottom >= topRect.top) {
              topMostElement.style.marginTop =
                parseInt(topMostElement.style.marginTop, 10) +
                editRect.bottom -
                topRect.top +
                margin +
                "px";
            } else {
              topMostElement.style.marginTop =
                parseInt(topMostElement.style.marginTop, 10) -
                (topRect.top - editRect.bottom) +
                margin +
                "px";
            }
          }
        }
        setChangeModule(false);
      }, 500);
    }
  }, [changeModule, editModule, isWrapped, modules, setChangeModule]);

  const getModules = (type: string) => {
    return (
      <>
        {modules.map((module, index) =>
          module.enabled &&
          !module.hidden &&
          !(module.isNsWidget && userContext !== USERCONTEXT.NURSE) &&
          !(module.isNsWidget === false && userContext === USERCONTEXT.NURSE) &&
          (type === "left"
            ? !module.isPldWidget && !module.isLabWidget
            : type === "right"
              ? module.isPldWidget
              : type === "stickyRight"
                ? module.isLabWidget
                : true) ? (
            <ModuleBox module={module} key={module.name}>
              {editModule !== module.title
                ? module.component
                : module.editComponent}
            </ModuleBox>
          ) : null
        )}
      </>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "row",
        padding: `${margin}px`,
        overflowX: "hidden",
        overflowY: "auto",
        backgroundColor: "#fff",
        "& .left-box > div, .right-box > div": {
          transition:
            "margin-top 0.3s ease, margin-left 0.3s ease, width 0.3s ease, height 0.3s ease",
        },
      }}
    >
      <Box
        ref={ref}
        sx={{
          width: `calc(100% - 450px - 16px)`,
          display: "flex",
          flexDirection: "row",
          backgroundColor: "#fff",
        }}
      >
        {!isWrapped ? (
          <>
            <Box
              className="left-box"
              sx={{
                width: `calc(50% - 8px)`,
                display: "flex",
                flexDirection: "column",
                marginRight: "8px",
                "& .moduleBox:empty": {
                  margin: 0,
                },
              }}
            >
              {getModules("left")}
            </Box>
            <Box
              className="right-box"
              sx={{
                width: `calc(50% - 8px)`,
                display: "flex",
                flexDirection: "column",
                marginLeft: "8px",
              }}
            >
              {getModules("right")}
            </Box>
          </>
        ) : (
          <Box
            className="all-box"
            sx={{
              width: "100%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {getModules("all")}
          </Box>
        )}
      </Box>
      <Box
        sx={{
          width: "450px",
          display: "flex",
          flexDirection: "column",
          marginLeft: "16px",
        }}
      >
        {getModules("stickyRight")}
      </Box>
    </Box>
  );
}
