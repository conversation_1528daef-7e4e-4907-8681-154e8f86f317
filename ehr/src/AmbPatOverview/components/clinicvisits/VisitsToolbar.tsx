/**
 * VisitsToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Clinic Visits
 */

import { useAtomValue } from "jotai";
import { useMemo } from "react";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink } from "../../../common/assets";
import {
  pastRowsAtom,
  upcomingRowsAtom,
  totalVisitsAtom,
} from "../../context/ClinicVisitsAtoms";
import { regIdAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";
import { toRegistion } from "@cci-monorepo/AmbPatOverview/utils/utils";

export const VisitsToolbar = () => {
  const upcomingRows = useAtomValue(upcomingRowsAtom);
  const pastRows = useAtomValue(pastRowsAtom);
  const regId = useAtomValue(regIdAtom);
  const totalVisits = useAtomValue(totalVisitsAtom);

  const count = useMemo(() => {
    return upcomingRows.length + pastRows.length;
  }, [upcomingRows, pastRows]);

  const handleRedirect = () => {
    toRegistion(regId);
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "View In Patient Focus",
      icon: <IconExternalLink />,
      handler: handleRedirect,
    },
  ];

  return (
    <CommonToolbar
      name="Encounter History"
      isPldWidget={false}
      count={count}
      totalCount={totalVisits}
      menuItems={menuItems}
    />
  );
};
