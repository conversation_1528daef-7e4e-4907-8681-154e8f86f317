/**
 * VisitsSection.tsx
 *
 * @author: jfsys
 * @description Data grid accordion that contains clinic visits data
 */

import { useAtomValue, useAtom } from "jotai";
import {
  Box,
  styled,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import {
  pastRowsAtom,
  upcomingRowsAtom,
  pageAtom,
  totalVisitsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ClinicVisitsAtoms";
import { regIdAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";
import { toRegistionSelectRow } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { NoRowsOverlay } from "../common/CommonDataGrid";
import { Grid, Fade } from "@mui/material";
import { StyledNameTooltip, clinicVisitPageSize } from "../common/Constants";
import { AdditionalFooter } from "../common/AdditionalFooter";

export const VisitsSection = () => {
  const upcomingRows = useAtomValue(upcomingRowsAtom);
  const pastRows = useAtomValue(pastRowsAtom);
  const regId = useAtomValue(regIdAtom);
  const [page, setPage] = useAtom(pageAtom);
  const totalVisits = useAtomValue(totalVisitsAtom);

  interface Column {
    id: "date" | "specialty" | "provider" | "reason";
    label: string;
    minWidth?: number;
    align?: "left";
    format?: (value: number) => string;
  }

  const columns: Column[] = [
    { id: "date", label: "Date", minWidth: 130 },
    { id: "specialty", label: "Specialty", minWidth: 150 },
    {
      id: "provider",
      label: "Provider",
      minWidth: 150,
    },
    {
      id: "reason",
      label: "Reason for Visit",
      minWidth: 150,
    },
  ];

  const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: "#D8DCE3",
      color: "#000",
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 15,
      fontWeight: 400,
      color: "#000",
      overflow: "hidden",
      textWrap: "nowrap",
      lineHeight: 1,
      textOverflow: "ellipsis",
      maxWidth: 200,
    },
    [`&.${tableCellClasses.root}`]: {
      height: "32px",
      padding: "6px 10px",
    },
  }));

  const StyledTableCellNoWrap = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: "#EDEDED",
      color: "#000",
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 15,
      fontWeight: 400,
      color: "#000",
      overflow: "hidden",
      textWrap: "nowrap",
      lineHeight: 1,
    },
    [`&.${tableCellClasses.root}`]: {
      height: "32px",
      padding: "6px 10px",
    },
  }));

  const TooltipDetailRow = (data: any) => {
    const titleAndKeys = [
      ["Department", "department"],
      ["Date", "date"],
      ["Encounter #", "encounterId"],
      ["Reason for Visit", "reason"],
      ["Physician", "provider"],
      ["Encounter Status", "status"],
    ];
    return (
      <>
        <Grid container>
          {titleAndKeys.map((item, index) => (
            <Grid container key={index}>
              <Grid item xs={5}>
                <div
                  style={{
                    fontStyle: "bold",
                    fontSize: "13px",
                    fontWeight: "700",
                    textAlign: "left",
                  }}
                >
                  {item[0] + ":"}
                </div>
              </Grid>
              <Grid item xs={7}>
                <div style={{ fontSize: "13px", textAlign: "left" }}>
                  {item[1] && data ? data[item[1]] : ""}
                </div>
              </Grid>
            </Grid>
          ))}
        </Grid>
      </>
    );
  };

  const section = (past: boolean) => {
    if (
      (past && pastRows.length === 0) ||
      (!past && upcomingRows.length === 0)
    ) {
      return <></>;
    } else {
      return (
        <Box>
          <Typography
            sx={{
              padding: "8px 0px 8px 40px",
              fontFamily: "Roboto",
              fontSize: "16px",
              fontWeight: 500,
              color: "#000000",
              width: "100%",
              backgroundColor: "#F7F7F7",
            }}
          >
            {past ? "Historic" : "Scheduled"}
          </Typography>
          <TableContainer>
            <Table stickyHeader aria-label="sticky table">
              <TableHead sx={{ "& .MuiTableCell-head": { lineHeight: 1 } }}>
                <TableRow>
                  {columns.map((column) => (
                    <StyledTableCell
                      key={column.id}
                      align={column.align}
                      sx={{
                        minWidth: column.minWidth,
                      }}
                    >
                      {column.label}
                    </StyledTableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {(past ? pastRows : upcomingRows).map((row, index) => {
                  let isLastRow =
                    (past && index === pastRows.length - 1) ||
                    (!past &&
                      index === upcomingRows.length - 1 &&
                      pastRows.length === 0);
                  return (
                    <StyledNameTooltip
                      key={index}
                      enterDelay={800}
                      TransitionComponent={Fade}
                      TransitionProps={{ timeout: 200 }}
                      title={TooltipDetailRow(row)}
                      sx={{ maxWidth: 300 }}
                    >
                      <TableRow
                        hover
                        role="checkbox"
                        tabIndex={-1}
                        key={row.encounterId}
                        sx={{
                          "&:nth-of-type(even)": {
                            backgroundColor: "#F5F5F5",
                          },
                          "&:nth-of-type(odd)": {
                            backgroundColor: "#FFF",
                          },
                        }}
                        onDoubleClick={() => {
                          toRegistionSelectRow(regId, row.encounterId);
                        }}
                      >
                        {columns.map((column, columnIndex) => {
                          const value = row[column.id];
                          return column.id === "specialty" ? (
                            <StyledTableCellNoWrap
                              key={column.id}
                              align={column.align}
                            >
                              {value}
                            </StyledTableCellNoWrap>
                          ) : (
                            <StyledTableCell
                              key={column.id}
                              align={column.align}
                              sx={{
                                [`&.${tableCellClasses.root}`]: {
                                  borderBottomLeftRadius:
                                    columnIndex === 0 && isLastRow
                                      ? "8px"
                                      : "0px",
                                  borderBottomRightRadius:
                                    columnIndex === columns.length - 1 &&
                                    isLastRow
                                      ? "8px"
                                      : "0px",
                                },
                              }}
                            >
                              {value}
                            </StyledTableCell>
                          );
                        })}
                      </TableRow>
                    </StyledNameTooltip>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      );
    }
  };

  const emptyTable = () => {
    return (
      <TableContainer>
        <Table stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <StyledTableCell
                  key={column.id}
                  align={column.align}
                  sx={{
                    minWidth: column.minWidth,
                  }}
                >
                  {column.label}
                </StyledTableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell
                sx={{
                  border: "none",
                  padding: "0 !important",
                  "&.MuiTableCell-root": {
                    height: "150px",
                    border: "none",
                  },
                }}
                colSpan={5}
              >
                <NoRowsOverlay msg="No visit record" />
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        alignItems: "center",
        flexDirection: "column",
        borderRadius: "8px",
      }}
    >
      {totalVisits === 0 ? (
        emptyTable()
      ) : (
        <Box
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {section(false)}
          {section(true)}
        </Box>
      )}
      {totalVisits > clinicVisitPageSize * page && (
        <AdditionalFooter
          msg="Load More"
          handleClick={() => setPage((preVal) => preVal + 1)}
        />
      )}
    </Box>
  );
};
