/**
 * ClinicVisits.tsx
 *
 * @author: jfsys
 * @description Clinic Visits component
 */

import { useEffect, useState, useRef } from "react";
import { useAtomValue, useSetAtom } from "jotai";
import Box from "@mui/material/Box";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";

import { VisitsToolbar } from "./VisitsToolbar";
import { ModuleHeaderHeight, clinicVisitPageSize } from "../common/Constants";
import { VisitsSection } from "./VisitsSection";
import {
  pastRowsAtom,
  setParseDataAtom,
  upcomingRowsAtom,
  pageAtom,
  totalVisitsAtom,
} from "../../context/ClinicVisitsAtoms";
import { serverRequest } from "@cci-monorepo/Pld/util/DataRequests";
import { keyifyData } from "@cci-monorepo/Registration/util/Transform";
import { useSetErrorDialog } from "@cci-monorepo/common/hooks/dialog";

export const ClinicVisits = () => {
  const upcomingRows = useAtomValue(upcomingRowsAtom);
  const pastRows = useAtomValue(pastRowsAtom);
  const setParseData = useSetAtom(setParseDataAtom);
  const page = useAtomValue(pageAtom);
  const setTotalVisits = useSetAtom(totalVisitsAtom);
  const [expanded, setExpanded] = useState<boolean>(true);
  const setOpenErrorDialog = useSetErrorDialog();
  const encounterStatusListRef = useRef<any>(null);
  const resultRef = useRef<any>(null);

  useEffect(() => {
    const params = { dbpath: Cci.util.Patient.getDbpath() };
    const onSuccess = (result: any) => {
      if (result?.data?.data?.length > 0) {
        const keys = keyifyData(result.data.header);
        let encounterStatusList = JSON.parse(
          result.encounterstatuslist.data[0][0]
        );
        encounterStatusListRef.current = encounterStatusList;
        resultRef.current = result;
        let scheduledStatusList = encounterStatusList.scheduledstatuschc;
        let historicStatusList = encounterStatusList.historicstatuschc;
        setParseData(
          result.data.data.length >= clinicVisitPageSize * page
            ? result.data.data.slice(0, clinicVisitPageSize * page)
            : result.data.data,
          keys,
          scheduledStatusList,
          historicStatusList
        );
        setTotalVisits(
          result.data.data?.filter((data: { [key: string]: any }) => {
            return (
              scheduledStatusList.includes(data[keys["encounterStatus"]]) ||
              historicStatusList.includes(data[keys["encounterStatus"]])
            );
          }).length
        );
      }
    };

    const onFailure = (error: string) => {
      setOpenErrorDialog({
        text: "Failed to get clinic visits:" + error,
        open: true,
      });
    };

    serverRequest("registration/getencounter", params, onSuccess, onFailure);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setExpanded(upcomingRows.length > 0 || pastRows.length > 0 ? true : false);
  }, [upcomingRows, pastRows, setExpanded]);

  useEffect(() => {
    if (resultRef.current && resultRef.current.data.data.length > 0) {
      let data = resultRef.current.data.data;
      setParseData(
        data.length >= clinicVisitPageSize * page
          ? data.slice(0, clinicVisitPageSize * page)
          : data,
        keyifyData(resultRef.current.data.header),
        encounterStatusListRef.current.scheduledstatuschc,
        encounterStatusListRef.current.historicstatuschc
      );
    }
  }, [page, setParseData]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="visits"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="visits-content"
        id="visits-header"
      >
        <VisitsToolbar />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <Box>{expanded ? <VisitsSection /> : <></>}</Box>
      </AccordionDetails>
    </Accordion>
  );
};
