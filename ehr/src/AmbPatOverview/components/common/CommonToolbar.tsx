/**
 * CommonToolbar.tsx
 *
 * @author: jfsys
 * @description Commonn Toolbar for all modules
 */

import * as React from "react";
import { Grid, Typography, IconButton, Tooltip } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import EditIcon from "@mui/icons-material/Edit";
import Button from "@mui/material/Button";
import { WarningAmberIcon } from "@cci-monorepo/common/assets";
import { ModuleHeaderSx } from "./Constants";
import { editModuleAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";
import { useSetAtom, useAtomValue } from "jotai";
import { userContextAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";
import { USERCONTEXT } from "../common/Constants";
import _ from "lodash";

export type MenuItemProps = {
  label: string;
  icon: React.ReactNode;
  handler: () => void;
  disabled?: boolean;
  hidden?: boolean;
};

export type ToolbarProps = {
  name: string;
  isPldWidget?: boolean;
  isCritical?: boolean;
  hasReviewed?: boolean;
  reviewMsg?: string;
  count?: number;
  totalCount?: number;
  menuItems: MenuItemProps[];
  reviewCommit?: Function;
};

export const CommonToolbar = (props: ToolbarProps) => {
  const {
    name,
    isPldWidget,
    isCritical,
    hasReviewed,
    reviewMsg,
    count,
    totalCount,
    menuItems,
    reviewCommit,
  } = props;

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const setEditModule = useSetAtom(editModuleAtom);
  const userContext = useAtomValue(userContextAtom);
  const handleEditClick = React.useCallback(() => {
    setEditModule(name);
  }, [name, setEditModule]);
  const nsNeedReview =
    userContext === USERCONTEXT.NURSE &&
    _.includes(["Chief Complaint", "Health Maintenance"], name);

  const handleMoreClick = (event: React.MouseEvent<HTMLElement>) => {
    if (menuItems && menuItems.length > 0) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const countStr = React.useMemo(() => {
    if (count !== undefined && totalCount !== undefined) {
      if (count >= totalCount) {
        return `(${totalCount})`;
      } else {
        return `(${count} of ${totalCount})`;
      }
    } else if (count !== undefined) {
      return `(${count})`;
    } else {
      return "";
    }
  }, [count, totalCount]);

  const buttonStyle = {
    fontFamily: "Roboto",
    fontWeight: 500,
    fontSize: "14px",
    width: 85,
    borderRadius: "18px",
    padding: "0 8px",
    "&:hover": {
      backgroundColor: "transparent",
    },
  };

  const tipStyle = {
    backgroundColor: "white",
    color: "black",
    boxShadow: "0px 0px 15px 0px #00000033",
    fontSize: "12px",
    fontFamily: "helvetica,arial,verdana,sans-serif",
    borderRadius: "4px",
    display: "inline-block",
    border: "1px solid #B9B9B9",
    padding: "8px",
    maxWidth: "1000px",
    minWidth: "100px",
  };

  return (
    <>
      <Grid
        container
        spacing={"8px"}
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        sx={{
          marginTop: "-8px",
          opacity: 1,
        }}
      >
        {isPldWidget === true && (
          <Grid item sx={{ marginTop: "4px" }}>
            <Tooltip
              title={reviewMsg}
              placement="bottom-start"
              componentsProps={{
                tooltip: {
                  sx: {
                    ...tipStyle,
                  },
                },
              }}
            >
              {hasReviewed ? (
                <CheckCircleIcon htmlColor="#68BA6A" sx={{ fontSize: 18 }} />
              ) : isCritical ? (
                <ErrorIcon color="error" sx={{ fontSize: 18 }} />
              ) : (
                <WarningAmberIcon style={{ width: 18, height: 18 }} />
              )}
            </Tooltip>
          </Grid>
        )}
        <Grid item>
          <Typography sx={ModuleHeaderSx}>
            {name} {countStr}
          </Typography>
        </Grid>
        <Grid item xs sx={{ textAlign: "right" }}>
          {nsNeedReview &&
            (hasReviewed ? (
              <Tooltip
                title={reviewMsg}
                placement="bottom-start"
                componentsProps={{
                  tooltip: {
                    sx: {
                      ...tipStyle,
                    },
                  },
                }}
              >
                <Button
                  sx={{
                    ...buttonStyle,
                    color: "#fff",
                    background: "#98C99A",
                    "&:hover": {
                      backgroundColor: "#98C99A",
                    },
                  }}
                >
                  Reviewed
                </Button>
              </Tooltip>
            ) : (
              <Button
                sx={{
                  ...buttonStyle,
                  color: "#000",
                  background: "#fff",
                  border: "1px solid #B1B1B1",
                }}
                onClick={() => {
                  reviewCommit && reviewCommit();
                }}
              >
                Review
              </Button>
            ))}
          {name === "Problems & Diagnosis" && (
            <IconButton
              aria-label="edit"
              id="edit-button"
              aria-haspopup="true"
              color="primary"
              onClick={handleEditClick}
            >
              <EditIcon />
            </IconButton>
          )}
          <IconButton
            aria-label="more"
            id="more-button"
            aria-controls={open ? "more-menu" : undefined}
            aria-expanded={open ? "true" : undefined}
            aria-haspopup="true"
            color="primary"
            onClick={handleMoreClick}
            sx={{
              display: menuItems?.length === 0 ? "none" : "inline-flex",
            }}
          >
            <MoreVertIcon />
          </IconButton>
          <Menu
            id="more-menu"
            aria-labelledby="more-button"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            sx={{
              "& .MuiPaper-root": { borderRadius: "4px" },
            }}
          >
            {menuItems?.map(
              (item: MenuItemProps, index: number) =>
                !item.hidden && (
                  <MenuItem
                    key={index}
                    onClick={() => {
                      item.handler && item.handler();
                      setAnchorEl(null);
                    }}
                    disabled={item.disabled ? item.disabled : false}
                  >
                    <ListItemIcon>{item.icon}</ListItemIcon>
                    <Typography
                      sx={{
                        fontSize: "14px",
                        fontWeight: "400",
                        fontFamily: "Rotobo",
                        color: "#000",
                      }}
                    >
                      {item.label}
                    </Typography>
                  </MenuItem>
                )
            )}
          </Menu>
        </Grid>
      </Grid>
    </>
  );
};
