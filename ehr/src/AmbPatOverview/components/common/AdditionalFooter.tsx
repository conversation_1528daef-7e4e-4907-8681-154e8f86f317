/**
 * AdditionalFooter.tsx
 *
 * @author: jfsys
 * @description footer for Notes
 */

import { Button, Grid } from "@mui/material";

interface AdditionalFooterProps {
  msg: string;
  handleClick: () => void;
}

export const AdditionalFooter = (props: AdditionalFooterProps) => {
  const { msg, handleClick } = props;
  return (
    <Grid sx={{ backgroundColor: "#f2f2f2" }}>
      <Button
        onClick={handleClick}
        variant="text"
        sx={{
          border: "none",
          width: "100%",
          height: "36px",
          backgroundColor: "#fff",
          "&:hover": {
            backgroundColor: "#fff",
          },
          "&.MuiButton-text": {
            color: "#3C6CBB",
          },
        }}
      >
        {msg}
      </Button>
    </Grid>
  );
};
