/**
 * ReasonEditHistoryDialog.tsx
 *
 * @author: CCI
 * @description Renders ReasonEditHistoryDialog
 */

import { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import { TabPanel, TabContext } from "@mui/lab";
import { Box, Stack, Tabs, Tab } from "@mui/material";
import { Button, CciDialog } from "@cci/mui-components";

interface Tab {
  value: string;
  label: string;
  tabDetail: React.ReactNode;
}

interface EditHistoryDialogProps {
  tabs: Tab[];
  value: string;
  open: boolean;
  setOpen: (status: boolean) => void;
}

const StyledTabs = styled(Tabs)({
  "&.MuiTabs-root": {
    height: "30px",
    minHeight: 0,
  },
  "& .MuiTabs-flexContainer": {
    height: "30px",
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    "&:last-child": {
      marginLeft: "18px",
    },
  },
  "& .MuiTabs-indicator": {
    backgroundColor: "transparent",
  },
});

const StyledTab = styled(Tab)({
  "&.MuiTab-root": {
    font: "normal 700 16px Roboto",
    color: "white",
    cursor: "pointer",
    backgroundColor: "#a1a7af",
    width: "130px",
    lineHeight: 1.0,
    overflow: "visible",
    padding: 0,
    border: "none",
    borderRadius: "8px",
    display: "flex",
    textTransform: "none",
    justifyContent: "center",
    "&:not(:last-child)": {
      "&:after": {
        height: "100%",
        width: "28px",
        display: "block",
        content: '""',
        backgroundImage:
          "linear-gradient(to top right, #a1a7af 50%, transparent 50%)",
        position: "absolute",
        left: "100%",
        top: "6px",
      },
    },
    "&:last-child": {
      minWidth: 0,
      marginLeft: "18px",
      "&:after": {
        height: "100%",
        width: "28px",
        display: "block",
        content: '""',
        backgroundImage:
          "linear-gradient(to top right, #a1a7af  50%, transparent 50%)",
        position: "absolute",
        left: "100%",
        top: "6px",
      },
    },
  },
  "&.Mui-selected": {
    color: "black",
    backgroundColor: "white",
    "&:not(:last-child)": {
      "&:after": {
        height: "100%",
        width: "28px",
        display: "block",
        content: '""',
        backgroundImage:
          "linear-gradient(to top right, #ffffff 50%, transparent 50%)",
        position: "absolute",
        left: "100%",
        top: "6px",
      },
    },
    "&:last-child": {
      "&:after": {
        height: "100%",
        width: "28px",
        display: "block",
        content: '""',
        backgroundImage:
          "linear-gradient(to top right, #ffffff 50%, transparent 50%)",
        position: "absolute",
        right: "100%",
        top: "6px",
      },
    },
  },
});

const StyledTabPanel = styled(TabPanel)({
  width: `calc(100% - 32px)`,
  height: `calc(100% - 64px)`,
  padding: "24px 0px 0px 16px",
  background: "#ffffff",
  position: "absolute",
});

export const EditHistoryDialog = (props: EditHistoryDialogProps) => {
  const { tabs, value, open, setOpen } = props;
  const [selectedTabValue, setSelectedTabValue] = useState<string>(value);

  const handleTabChange = (event: React.ChangeEvent<{}>, newValue: string) => {
    setSelectedTabValue(newValue);
  };

  useEffect(() => {
    if (open) {
      setSelectedTabValue(value);
    }
  }, [value, open]);

  // Handle closing the dialog
  const handleClose = () => {
    setOpen(false);
  };

  const content = (
    <Box
      sx={{
        width: "1400px",
        height: "700px",
        position: "relative",
        padding: "16px",
      }}
    >
      <TabContext value={selectedTabValue}>
        <StyledTabs value={selectedTabValue} onChange={handleTabChange}>
          {tabs.map((tab) => (
            <StyledTab label={tab.label} value={tab.value} disableRipple />
          ))}
        </StyledTabs>

        {tabs.map((tab) => (
          <StyledTabPanel value={tab.value}>{tab.tabDetail}</StyledTabPanel>
        ))}
      </TabContext>
    </Box>
  );

  const buttons = (
    <Stack
      sx={{
        padding: "16px",
        justifyContent: "flex-end",
      }}
    >
      <Button onClick={handleClose}>OK</Button>
    </Stack>
  );

  return (
    <CciDialog
      open={open}
      setOpen={setOpen}
      handleClose={handleClose}
      title="Edit History"
      content={content}
      buttons={buttons}
      sx={{
        "& .MuiDialogTitle-root": {
          padding: "16px 24px",
        },
        "& .MuiDialogContent-root": {
          padding: "0px",
        },
        "& .MuiDialogActions-root": {
          padding: "0px",
        },
      }}
    />
  );
};
