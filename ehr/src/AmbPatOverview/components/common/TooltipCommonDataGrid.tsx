/**
 * TooltipCommonDataGrid.tsx
 *
 * @author: jfsys
 * @description Data grid with tooltip
 */

import { useMemo, useCallback } from "react";
import { CommonDataGrid } from "./CommonDataGrid";
import { GridRow, GridRowProps } from "@mui/x-data-grid-pro";
import { StyledNameTooltip } from "../common/Constants";
import { Grid, Fade, Divider } from "@mui/material";
import TooltipCommonRow from "./TooltipCommonRow";
import { nullorblank, ifnullblank } from "@cci-monorepo/common";
import { isFunction } from "lodash";
import { buildPhmText } from "@cci-monorepo/Pld/hook/useMeds/component/main/body/grid/Tooltip";

export const TooltipCommonDataGrid = (props: any) => {
  const { type } = props;

  const { titleAndKeys, firstSubTitleAndKeys, secSubTitleAndKeys, maxWidth } =
    useMemo(() => {
      let titleAndKeys: any[] = [];
      let firstSubTitleAndKeys: any = {
        field: "",
        label: "",
        titleAndKeys: [],
      };
      let secSubTitleAndKeys: any = { field: "", label: "", titleAndKeys: [] };
      let maxWidth = 500;
      if (type === "notes") {
        maxWidth = 300;
        titleAndKeys = [
          ["Note Name", "name"],
          ["Topic", "topic"],
          ["Stored At", "displayStoredAt"],
          ["Stored By", "storeby"],
          ["Status", "status"],
          ["Note Time", "notetimestr"],
        ];
      } else if (type === "allergies") {
        maxWidth = 300;
        titleAndKeys = [
          ["Allergen", "name"],
          ["Status", "status"],
          ["Type", "type"],
          ["Reaction", "symptom"],
          ["Severity", "severity"],
          ["Onset Date", "onsetdatestr"],
          ["Comments", "note"],
          ["source", "source"],
        ];
      } else if (type === "outstandingOrders") {
        titleAndKeys = [
          ["Name", "name"],
          ["Status", "status"],
          ["Divider_line", ""],
          ["Priority", "priority"],
          ["Associated Diagnosis", "assoc_diag"],
          ["Referral Type", "referral_class"],
          ["Referral To Location/Dept", "referral_to"],
          ["Reason for Referral", "reason_for_ref"],
          ["Divider_line", ""],
          ["Ordered Time", "ordertime"],
          ["Starting at", "fmtstarttime"],
          ["Authorizing Provider", "provider"],
          ["Entered By", "enteredby"],
          ["Order Mode", "mode"],
          ["Category", "category"],
          ["SIGN", "sign"],
          ["VERIFY", "verify"],
        ];
      } else if (type === "socialSU") {
        titleAndKeys = [
          ["Status", "status"],
          ["Type", "type"],
          ["Qty", "qty"],
          ["Unit", "unit"],
          ["Frequency", "frequency"],
          ["Route", "route"],
          ["Last Used", "lastused"],
          ["Quit Date", "quitdate"],
          ["Age Started", "agestarted"],
          ["Comments", "comments"],
        ];
      } else if (type === "family") {
        maxWidth = 300;
        titleAndKeys = [
          ["Status", "status"],
          ["Problem", "problem"],
          ["Family Member", "relation"],
          ["Onset Age", "onsetage"],
          ["Deceased", "deceased"],
          ["Deceased Age", "deceasedage"],
          ["Ethnicity", "ethnicity"],
          ["Comments", "comments"],
        ];
      } else if (type === "procedure") {
        maxWidth = 320;
        titleAndKeys = [
          ["Status", "status"],
          ["Procedure Name", "procedure"],
          ["Date of Procedure", "dateperformed"],
          ["Source", "source"],
          ["SNOMED", "snomedct"],
          ["Comments", "comment"],
        ];
      } else if (type === "implant") {
        titleAndKeys = [
          ["Status", "status"],
          ["UDI", "udi"],
          ["Serial", "serial"],
          ["Implanted Device", "device"],
          ["Laterality", "laterality"],
          ["Location of Device", "location"],
          ["Date & Time of Implant", "implantDate"],
          ["Reason for Implant", "reason"],
          ["Number of Implanted Devices", "quantity"],
          ["Is the Device Removed:", "removal"],
          ["Date & Time of Removal", "removalDate"],
          ["Comment", "comments"],
          ["Divider_line", ""],
          ["Lot or Batch Number", "lotbatch"],
          ["Expiration Date of Device", "expdate"],
          ["Date Manufactured", "manufactdate"],
          ["GDMN PT Name", "gmdnname"],
          ["Brand Name", "brandname"],
          ["Version or Model Number", "vermodel"],
          ["Company Name", "company"],
          ["MRI Safety Labeling", "mri"],
          [
            "Contains Natural Rubber Latex or Dry Natural Rubber?",
            "containnrl",
          ],
        ];
      } else if (type === "immunizationForecast") {
        maxWidth = 300;
        titleAndKeys = [
          ["Vaccine Family", "vaccineType"],
          ["Forecasted Dose", "doseNumber"],
          ["Recomm. Date", "nextDoseDate"],
          ["Min. Valid Date", "minDoseDate"],
          ["Overdue Date", "LatestDoseDate"],
          ["Status", "seriesStatus"],
        ];
      } else if (type === "medication") {
        maxWidth = 500;
        titleAndKeys = [
          [
            "Name",
            (data: any) =>
              ifnullblank(
                data.order_sig,
                ifnullblank(data.name, data.order_name)
              ),
          ],
          [
            "Dose",
            (data: any) =>
              !nullorblank(data.dose_value) && !nullorblank(data.dose_unit)
                ? `${data.dose_value} ${data.dose_unit}`
                : "",
          ],
          [
            "Amount per Dose",
            (data: any) =>
              `${ifnullblank(data.admin_amount)} ${ifnullblank(data.admin_unit)}`,
          ],
          [
            "Dose Form",
            (data: any) => ifnullblank(data.dosage_form, data.form),
          ],
          ["Route", "route"],
          ["Strength", "strength"],
          ["Frequency", "frequency"],
          ["Start Date", "starttime"],
          ["Last Taken", "lasttaken"],
          [
            "Duration",
            (data: any) =>
              `${ifnullblank(data.duration_value)} ${ifnullblank(data.duration_unit)}`,
          ],
          ["Last Filled Date", "lastfilldate"],
          [
            "Pharmacy History",
            (data: any) => {
              return (
                data.preferred_pharmacy &&
                ifnullblank(buildPhmText(data.preferred_pharmacy))
              );
            },
          ],
          ["Refills Remaining", "refillsremain"],
          ["Quantity", "quantity_value"],
          ["Indication", "indication"],
          ["PRN Reason/Comments", "PRN_reasons"],
          ["Compliance", "compliance"],
          ["Therapeutic Class", "thera_class"],
          ["Patient Directions", "comment"],
          ["Source", "source"],
          ["Status", "status"],
          ["Last Reconciled By", ""],
        ];
      } else if (type === "tobacco") {
        titleAndKeys = [
          ["Status", "status"],
          ["Type", "type"],
          ["Qty", "qty"],
          ["Unit", "unit"],
          ["Frequency", "frequency"],
          ["Age Started", "agestarted"],
          ["Start Date", "startDate"],
          ["Quit Date", "quitdate"],
          ["Restart Date", "restartDate"],
          ["Comments", "comments"],
          ["Smoking Status", "smokingStatus"],
          ["Smokeless Status", "smokelessStatus"],
          ["Smoking Pack-Years", "smokingPackYears"],
        ];
      } else if (type === "concern") {
        maxWidth = 650;
        titleAndKeys = [
          ["Concern", "name"],
          ["Status", "status"],
          ["Start Date", "startdate"],
          ["Resolved Date", "resolvedate"],
          ["Comment", "Comments"],
          ["Goals", "Goals"],
        ];
        firstSubTitleAndKeys.field = "Goals";
        firstSubTitleAndKeys.label = "Goal #";
        firstSubTitleAndKeys.titleAndKeys = [
          ["", "name"],
          ["Status", "status"],
          ["Start Date", "startdate"],
          ["Completion Date", "completedate"],
          ["Comment", "comments"],
        ];
        secSubTitleAndKeys.field = "interventions";
        secSubTitleAndKeys.label = "Intervention #";
        secSubTitleAndKeys.titleAndKeys = [
          ["", "name"],
          ["Status", "status"],
          ["Start Date", "startdate"],
          ["Completion Date", "completedate"],
          ["Comment", "comments"],
        ];
      }
      return {
        titleAndKeys,
        firstSubTitleAndKeys,
        secSubTitleAndKeys,
        maxWidth,
      };
    }, [type]);

  const TooltipDetailRow = useCallback(
    (params: GridRowProps) => {
      return (
        <StyledNameTooltip
          enterDelay={800}
          TransitionComponent={Fade}
          TransitionProps={{ timeout: 200 }}
          sx={{ maxWidth }}
          title={
            <>
              <Grid container>
                {titleAndKeys.map((item, index) => {
                  if (item[0] === "Divider_line") {
                    return (
                      <Grid item xs={12} key={index}>
                        <Divider sx={{ m: "5px 0" }} />
                      </Grid>
                    );
                  } else if (
                    type === "concern" &&
                    item[0] === firstSubTitleAndKeys.field
                  ) {
                    return (
                      params.row &&
                      params.row[firstSubTitleAndKeys.field].map(
                        (firstSub: any, firstSubIdx: number) => (
                          <>
                            <Grid item xs={12} key={index}>
                              <Divider sx={{ m: "5px 0" }} />
                            </Grid>
                            {firstSubTitleAndKeys.titleAndKeys.map(
                              (firstSubItem: any, firstSubItemIdx: number) => (
                                <TooltipCommonRow
                                  key={firstSubItemIdx}
                                  label={
                                    firstSubTitleAndKeys.label +
                                    (firstSubIdx + 1) +
                                    (firstSubItem[0]
                                      ? " " + firstSubItem[0]
                                      : "")
                                  }
                                  value={
                                    firstSub ? firstSub[firstSubItem[1]] : ""
                                  }
                                />
                              )
                            )}
                            {secSubTitleAndKeys.field &&
                              firstSub[secSubTitleAndKeys.field].map(
                                (secSub: any, secSubIdx: number) =>
                                  secSubTitleAndKeys.titleAndKeys.map(
                                    (
                                      secSubItem: any,
                                      secSubItemIdx: number
                                    ) => (
                                      <TooltipCommonRow
                                        key={secSubItemIdx}
                                        label={
                                          secSubTitleAndKeys.label +
                                          (secSubIdx + 1) +
                                          (secSubItem[0]
                                            ? " " + secSubItem[0]
                                            : "")
                                        }
                                        value={
                                          secSub ? secSub[secSubItem[1]] : ""
                                        }
                                      />
                                    )
                                  )
                              )}
                          </>
                        )
                      )
                    );
                  } else if (
                    type === "medication" &&
                    item[0] === "PRN Reason/Comments" &&
                    params.row &&
                    nullorblank(params.row[item[1]])
                  ) {
                    return <></>;
                  } else {
                    let value = "";
                    if (params.row) {
                      if (isFunction(item[1])) {
                        value = item[1](params.row);
                      } else {
                        value = ifnullblank(params.row[item[1]]);
                      }
                    }
                    return (
                      <TooltipCommonRow
                        key={index}
                        label={item[0]}
                        value={value}
                      />
                    );
                  }
                })}
              </Grid>
            </>
          }
        >
          <GridRow {...params} />
        </StyledNameTooltip>
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [titleAndKeys, maxWidth]
  );

  const outstandingOrdersProps = {
    getRowId: (row: any) => row.order_id,
    ...props,
  };
  return type === "outstandingOrders" ? (
    <CommonDataGrid
      {...outstandingOrdersProps}
      slots={{
        row: TooltipDetailRow,
      }}
    />
  ) : (
    <CommonDataGrid
      {...props}
      slots={{
        row: TooltipDetailRow,
      }}
    />
  );
};
