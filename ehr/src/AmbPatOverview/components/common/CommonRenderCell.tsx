import { GridRenderCellParams } from "@mui/x-data-grid-pro";

const CommonRenderCell = (params: GridRenderCellParams<any>) => {
  return (
    <div
      style={{
        font: "normal 400 15px Roboto",
        color: "#000",
        overflow: "hidden",
        textOverflow: "ellipsis",
        fontStyle: params.row.status !== "Active" ? "italic" : "normal",
        textDecoration:
          params.row.status.indexOf("Error") >= 0 ? "line-through" : "none",
      }}
    >
      {params.value}
    </div>
  );
};

export default CommonRenderCell;
