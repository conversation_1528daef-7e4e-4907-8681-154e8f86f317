import { GridColumnMenu, GridColumnMenuProps } from "@mui/x-data-grid-pro";

const CustomColumnMenu = (props: GridColumnMenuProps) => {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Hide `columnMenuPinningItem`
        columnMenuPinningItem: null,
        // Hide `columnMenuColumnsItem`
        columnMenuColumnsItem: null,
      }}
    />
  );
};

export default CustomColumnMenu;
