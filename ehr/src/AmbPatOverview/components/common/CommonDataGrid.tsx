/**
 * CommonDataGrid.tsx
 *
 * @author: jfsys
 * @description Common Data Grid
 */

import { useEffect, useState } from "react";
import {
  DataGridPro,
  gridClasses,
  getGridSingleSelectOperators,
} from "@mui/x-data-grid-pro";
import Stack from "@mui/material/Stack";
import Box from "@mui/material/Box";
import { styled } from "@mui/material";
import ListItemText from "@mui/material/ListItemText";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import Checkbox from "@mui/material/Checkbox";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";

import CustomColumnMenu from "./CustomColumnMenu";
import { RowHeight, columnHeaderHeight } from "../common/Constants";

const StyledDataGrid = styled(DataGridPro)({
  borderRadius: "0px 0px 8px 8px",
  background: "white",
  "& .MuiDataGrid-column:focus-within, .MuiDataGrid-cell:focus-within": {
    outline: "none",
  },
  "& .MuiDataGrid-columnHeaders": {
    background: "#D8DCE3",
    borderRadius: "0px",
    fontSize: "15px",
    fontFamily: "Roboto",
    color: "black",
    "& .MuiDataGrid-columnHeaderTitle": {
      fontWeight: "700",
    },
    "& .MuiDataGrid-columnHeader": {
      borderRight: "1px solid #C2C2C2",
      borderBottom: "1px solid #C2C2C2",
      "&:focus": {
        outline: "none",
      },
      "&:hover": {
        outline: "none",
      },
    },
  },
  "& .MuiDataGrid-cell": {
    fontSize: "14px",
    fontWeight: "400",
    borderRight: "1px solid #C2C2C2",
    borderBottom: "1px solid #C2C2C2",
    "&:focus": {
      outline: "none",
    },
  },
  "&.MuiDataGrid-root .MuiDataGrid-row": {
    "&.Mui-hovered, &.Mui-selected": {
      backgroundColor: "#F6E596",
    },
  },
  [`& .${gridClasses.row}.even`]: {
    backgroundColor: "#F5F5F5",
  },
});

export const NoRowsOverlay = (props: any) => {
  return (
    <Stack
      sx={{
        backgroundColor: "#F2F2F2",
        height: props.height ? props.height : "100%",
        borderBottomLeftRadius: "8px",
        borderBottomRightRadius: "8px",
      }}
      alignItems="center"
      justifyContent="center"
      fontSize={24}
      lineHeight="24px"
      color="#AAA9A9"
    >
      <span style={{ fontFamily: "Raleway" }}>{props.msg}</span>
    </Stack>
  );
};

const CustomSelectValue = (props: any) => {
  const { item, apiRef, applyValue, autosizeOptions } = props;
  const [value, setValue] = useState<string>(
    item.value !== undefined ? item.value : ["Active"]
  );
  const options = apiRef.current.getColumn("status").valueOptions ?? [];

  useEffect(() => {
    applyValue({ ...item, value: value });
    autosizeOptions &&
      setTimeout(() => {
        apiRef.current.autosizeColumns(autosizeOptions);
      }, 100);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const handleFilterChange: any = (event: SelectChangeEvent) => {
    setValue(event.target.value);
  };

  return (
    <FormControl variant="standard">
      <InputLabel id="multiple-checkbox-label">Value</InputLabel>
      <Select
        labelId="multiple-checkbox-label"
        id="multiple-checkbox"
        multiple
        value={value}
        onChange={handleFilterChange}
        renderValue={(selected: any) => selected.join(", ")}
      >
        {options?.map((name: any) => (
          <MenuItem key={name} value={name}>
            <Checkbox checked={value?.includes(name) ? true : false} />
            <ListItemText primary={name} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export const CommonDataGrid = (props: any) => {
  const {
    rows,
    columns,
    enableStatusColFilter,
    noDataMsg,
    slots,
    slotProps,
    ...other
  } = props;
  const noRows = rows?.length === 0 ? true : false;
  const customColumns = enableStatusColFilter
    ? columns.map((col: any) => {
        if (col.field === "status") {
          return {
            ...col,
            filterable: true,
            filterOperators: getGridSingleSelectOperators()
              .filter((operator) => operator.value === "isAnyOf")
              .map((operator) => ({
                ...operator,
                InputComponent: operator.InputComponent
                  ? CustomSelectValue
                  : undefined,
                InputComponentProps: { autosizeOptions: props.autosizeOptions },
              })),
          };
        } else {
          return {
            ...col,
            filterable: false,
            disableColumnMenu: true,
          };
        }
      })
    : columns;

  return (
    <Box sx={{ height: noRows ? 150 : "auto", width: "100%" }}>
      <StyledDataGrid
        hideFooter
        rowHeight={RowHeight}
        rows={rows}
        columns={customColumns}
        columnHeaderHeight={columnHeaderHeight}
        disableColumnMenu={enableStatusColFilter ? false : true}
        disableColumnResize={true}
        autosizeOnMount
        getRowClassName={(params: any) => {
          if (props.striped === false) return "";
          if (params.row && params.row.groupTitle) {
            return "title";
          } else {
            return params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even";
          }
        }}
        slots={{
          noRowsOverlay: NoRowsOverlay,
          columnMenu: CustomColumnMenu,
          ...slots,
        }}
        slotProps={{
          noRowsOverlay: { msg: noDataMsg },
          filterPanel: enableStatusColFilter
            ? {
                disableAddFilterButton: true,
                disableRemoveAllButton: true,
                filterFormProps: {
                  operatorInputProps: {
                    sx: { display: "none" },
                  },
                },
              }
            : {},
          ...slotProps,
        }}
        {...other}
      />
    </Box>
  );
};
