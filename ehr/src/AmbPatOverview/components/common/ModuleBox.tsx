import React, { ReactNode, useEffect, useRef } from "react";
import Box from "@mui/material/Box";
import useResizeObserver from "use-resize-observer";
import {
  editModuleAtom,
  changeEditModuleAtom,
} from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";
import { useAtomValue, useSetAtom } from "jotai";

interface ModuleBoxProps {
  module: any;
  children: ReactNode;
}
const ModuleBox: React.FC<ModuleBoxProps> = ({ module, children }) => {
  const ref = useRef<HTMLDivElement | null>(null);
  const { height } = useResizeObserver<HTMLDivElement>({ ref });
  const editModule = useAtomValue(editModuleAtom);
  const setChangeMoule = useSetAtom(changeEditModuleAtom);
  const preHeightRef = useRef<number | undefined>(0);

  useEffect(() => {
    if (preHeightRef.current !== height) {
      if (editModule === module.title) {
        setChangeMoule(true);
      }
      preHeightRef.current = height;
    }
  }, [editModule, height, module, setChangeMoule]);

  return (
    <Box
      ref={ref}
      className="moduleBox"
      sx={{
        width: "100%",
        margin: "0px 0px 16px 0px",
      }}
    >
      {children}
    </Box>
  );
};

export default ModuleBox;
