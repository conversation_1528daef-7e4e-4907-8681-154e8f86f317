/**
 * DateField.tsx
 *
 * @author: jfsys
 * @description Render allergy's Onset Date field
 */
import * as React from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import Divider from "@mui/material/Divider";
import Popover from "@mui/material/Popover";
import {
  GridRenderEditCellParams,
  useGridApiContext,
} from "@mui/x-data-grid-pro";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { YearCalendar } from "@mui/x-date-pickers/YearCalendar";
import { MonthCalendar } from "@mui/x-date-pickers/MonthCalendar";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import dayjs from "dayjs";
import { DateIcon } from "../../../common/assets";
import { nullorblank, checkDate } from "@cci-monorepo/common";

export const DateField = (params: GridRenderEditCellParams<any>) => {
  const { id, field, disableFuture, disabled, autoPopup, placeholder } = params;
  const isAllowedYear = params.isAllowedYear ?? false;
  const [isAutoPopuped, setIsAutoPopuped] = React.useState(false);
  const [value, setValue] = React.useState<any>(
    params.value && params.value !== "" && params.value !== "NO DATE"
      ? params.value
      : ""
  );
  const [page, setPage] = React.useState<any>("year");
  const [error, setError] = React.useState<boolean>(false);
  const [anchor, setAnchor] = React.useState<null | HTMLElement>(null);
  const [preYear, setPreYear] = React.useState(false);
  const textFieldRef = React.useRef<null | HTMLElement>(null);

  const apiRef = useGridApiContext();
  const open = Boolean(anchor);
  const popupId = open ? "simple-popup" : undefined;

  React.useEffect(() => {
    apiRef.current.setEditCellValue({
      id,
      field,
      value: error ? "Invalid Date" : value,
    });
    const target = textFieldRef.current as HTMLInputElement;
    if (autoPopup === true && !isAutoPopuped) {
      target?.click();
      setIsAutoPopuped(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, error]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (disabled) return;
    if (!anchor) {
      setPage("year");
      setError(false);
    }
    setAnchor(anchor ? null : event.currentTarget);
  };

  const handleClose = () => {
    const target = textFieldRef.current as HTMLInputElement;
    if (target) {
      if (target.value) {
        if (checkDate(target.value, isAllowedYear)) {
          setError(false);
        } else {
          setError(true);
        }
      } else {
        setError(false);
      }
    }
    setAnchor(null);
  };

  const handleToday = () => {
    const formattime = dayjs().format("MM/DD/YYYY");
    setValue(formattime);
    setTimeout(() => {
      handleClose();
    }, 200);
  };

  const handleOK = () => {
    handleClose();
  };

  const handleYear = (v: any) => {
    const formattime = dayjs(v).format("YYYY");
    const currentYear = new Date().getFullYear();
    setPreYear(parseInt(formattime) < currentYear);
    setPage("month");
    setValue(formattime);
  };

  const handleMonth = (v: any) => {
    const formattime = dayjs(v).format("MM") + "/" + value;
    setPage("day");
    setValue(formattime);
  };

  const handleDay = (v: any) => {
    const formattime = dayjs(v).format("MM/DD/YYYY");
    setValue(formattime);
  };

  return (
    <>
      <TextField
        inputRef={textFieldRef}
        sx={{
          "& .MuiInputBase-root.MuiOutlinedInput-root": {
            paddingRight: "10px",
          },
          "& .MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputAdornedEnd":
            {
              padding: "6px 0px 6px 8px",
              borderRadius: "4px",
            },
          background: disabled ? "#EBEBEB" : "#FFF",
          borderRadius: "4px",
          width: "calc(100% - 6px)",
          height: "calc(100% - 6px)",
          marginLeft: "3px",
        }}
        placeholder={placeholder ? placeholder : "MM/DD/YYYY"}
        error={error}
        value={value}
        disabled={disabled}
        aria-describedby={popupId}
        onClick={handleClick}
        onChange={(event: any) => {
          setValue(event.target.value);
          if (event.target.value) {
            if (checkDate(event.target.value, isAllowedYear)) {
              setError(false);
            } else {
              setError(true);
            }
          } else {
            setError(false);
          }
        }}
        InputProps={{
          endAdornment: disabled ? (
            <></>
          ) : (
            <InputAdornment position="end">
              <IconButton edge="end">
                <DateIcon />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
      <Popover
        id={popupId}
        open={open}
        anchorEl={anchor}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <Box
          sx={{
            backgroundColor: "white",
            border: "1px solid lightgray",
            boxShadow: "5px 10px 10px lightgray",
          }}
        >
          {page !== "year" ? (
            <Stack
              direction="row"
              sx={{ margin: "10px 20px" }}
              justifyContent="flex-start"
              alignItems="center"
            >
              <Typography
                variant="h6"
                sx={{ fontWeight: "bold", lineHeight: "40px" }}
              >
                {value}
              </Typography>
              <ArrowDropDownIcon />
            </Stack>
          ) : (
            ""
          )}
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            {page === "year" ? (
              <YearCalendar
                sx={{ height: "200px" }}
                disableFuture
                disableHighlightToday
                onChange={handleYear}
                defaultValue={
                  !nullorblank(value) && checkDate(value, isAllowedYear)
                    ? dayjs(value, "YYYY")
                    : null
                }
              />
            ) : page === "month" ? (
              <MonthCalendar
                sx={{ height: "200px" }}
                disableFuture={disableFuture && !preYear}
                disableHighlightToday
                onChange={handleMonth}
              />
            ) : (
              <DateCalendar
                autoFocus={false}
                sx={{ height: "270px" }}
                disableFuture={disableFuture && !preYear}
                disableHighlightToday
                defaultCalendarMonth={dayjs(value, "MM/YYYY")}
                onChange={handleDay}
                views={["day"]}
                slots={{
                  calendarHeader: () => <></>,
                }}
              />
            )}
          </LocalizationProvider>
          <Divider />
          <Stack
            direction="row"
            justifyContent="space-between"
            sx={{ height: "30px", margin: "10px 20px" }}
          >
            <Button variant="outlined" onClick={handleToday} size="small">
              Today
            </Button>
            {page !== "year" ? (
              <Button variant="outlined" onClick={handleOK} size="small">
                Done
              </Button>
            ) : null}
          </Stack>
        </Box>
      </Popover>
    </>
  );
};
