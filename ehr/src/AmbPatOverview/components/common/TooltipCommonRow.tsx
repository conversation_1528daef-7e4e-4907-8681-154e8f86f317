import { Grid } from "@mui/material";

export interface ITooltipCommonRowProps {
  key: number;
  label: string;
  value: string;
}

const TooltipCommonRow: React.FunctionComponent<ITooltipCommonRowProps> = ({
  key,
  label,
  value,
}): JSX.Element => {
  return (
    <Grid container key={key}>
      <Grid item xs={5}>
        <div
          style={{
            fontStyle: "bold",
            fontSize: "13px",
            fontWeight: "700",
            textAlign: "left",
          }}
        >
          {label + ":"}
        </div>
      </Grid>
      <Grid item xs={7}>
        <div style={{ fontSize: "13px", textAlign: "left" }}>{value}</div>
      </Grid>
    </Grid>
  );
};

export default TooltipCommonRow;
