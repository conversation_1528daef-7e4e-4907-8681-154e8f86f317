/**
 * DataUtils.tsx
 *
 * @author: RnD
 * @description Data utils
 */

import { debounce } from "@mui/material";
import { useEffect, useMemo, useRef } from "react";
import {
  getBoldDisplayTextWidth,
  getDisplayTextWidth,
} from "../common/StripedGrid";

export type GridDataType =
  | "MEDS"
  | "CCD"
  | "SURESCRIPT"
  | "ALLERGY"
  | "ALLERGY_CCD"
  | "IMMUNIZATION"
  | "SUBSTANCEUSE"
  | "IMPLANTS"
  | "PROCEDURE"
  | "DIAGNOSES"
  | "PROBLEMS"
  | "FAMILYHX"
  | "EXTERNALPROBLEMS";

export const problemsData: any = {
  key: "",
  nit: -1,
  status: "",
  name: "",
  onsetdate: "",
  resolveddate: "",
  comments: "",
  snomedcode: "",
  icd10code: "",
};

export const diagnosesDBitemsMap: any = {
  status: "diag_status.MGDB",
  name: "diag_name.MGDB",
  onsetdate: "diag_onsetDT.MGDB",
  resolveddate: "diag_resolvedDT.MGDB",
  comments: "diag_annotation.MGDB",
  snomedcode: "diag_SNOMEDcode.MGDB",
  icd10code: "diag_icd10code.MGDB",
  probid: "prob_ID.MGDB",
};

export const problemsDBitemsMap: any = {
  status: "Probs_status.MGDB",
  name: "Probs_name.MGDB",
  onsetdate: "Probs_onsetDT.MGDB",
  resolveddate: "Probs_resolvedDT.MGDB",
  comments: "Probs_annotation.MGDB",
  snomedcode: "Probs__SNOMEDcode.MGDB",
  icd10code: "Probs_icd10code.MGDB",
  diagid: "diag_ID.MGDB",
};

// Delay to call a callback after waiting for a period of time
export const useDebounce = (callback: any, wait: number) => {
  const ref = useRef();

  useEffect(() => {
    ref.current = callback;
  }, [callback]);

  const debouncedCallback = useMemo(() => {
    const func = () => {
      // @ts-ignore
      ref.current?.();
    };

    return debounce(func, wait);
  }, [wait]);

  return debouncedCallback;
};

// Return true if the string is a valid json.
export const isJSON = (str: string) => {
  try {
    const parsed = JSON.parse(str);
    if (parsed && typeof parsed === "object") {
      return true;
    }
  } catch {
    return false;
  }
  return false;
};

// Get tooltip widths (label and value) for a row
export const getTooltipWidths = (
  row: { [index: string]: string | number | Array<string> },
  toolTipDisplays: any
) => {
  let labelWidth = 0;
  toolTipDisplays.forEach((label: string) => {
    labelWidth = Math.max(labelWidth, getBoldDisplayTextWidth(label));
  });

  let valueWidth = 0;
  Object.values(row).forEach((value: string | Array<string> | number) => {
    if (value && !Array.isArray(value)) {
      valueWidth = Math.max(valueWidth, getDisplayTextWidth(String(value)));
    }
  });
  return [labelWidth, valueWidth];
};

// Tooltip for meds
export const getTooltipLabelOrder = (data: any) => {
  let toolTipData: any = data;

  let tooltipFields: Array<string> = [];
  let tooltipDisplays: Array<string> = [];

  const tooltipIdx = data[0].indexOf("tooltip");
  if (tooltipIdx >= 0) {
    toolTipData = data.filter((col: any, index: number) => {
      return index > 0 && col[tooltipIdx] !== "0";
    });

    tooltipFields = Array(toolTipData.length).fill("");
    tooltipDisplays = Array(toolTipData.length).fill("");

    toolTipData.map((col: any) => {
      tooltipFields[col[tooltipIdx]] = col[0];
      tooltipDisplays[col[tooltipIdx]] = col[1];

      return null;
    });
  }

  return [tooltipFields, tooltipDisplays];
};
