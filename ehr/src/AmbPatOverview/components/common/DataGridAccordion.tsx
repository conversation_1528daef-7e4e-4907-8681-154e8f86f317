/**
 * DataGridAccordion.tsx
 *
 * @author: RnD
 * @description Data grid accordion that contains import data
 */

import { ReactElement, useState, useMemo, useCallback } from "react";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import Box from "@mui/material/Box";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import AccordionSummary from "@mui/material/AccordionSummary";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import {
  GridRowParams,
  GridCellParams,
  GridSortModel,
  GridCallbackDetails,
  GridSortDirection,
} from "@mui/x-data-grid-pro";
import StripedGrid from "./StripedGrid";
import { GridDataType } from "./DataUtils";
import { Button } from "@cci/mui-components";
import { useSetToastFamily } from "@cci-monorepo/common";
import { GridRow, GridRowProps } from "@mui/x-data-grid-pro";
import { StyledNameTooltip } from "../common/Constants";
import Fade from "@mui/material/Fade";

export type SectionProps = {
  type: GridDataType;
  rowHeight: number;
  rows: any;
  columns: any;
  displayMsg: string;
  enableSummary: boolean;
  loading?: boolean;
  icon?: ReactElement;
  sectionName?: string;
  closeHandler?: any;
  onRowUpdate?: (newRow: any, oldRow: any) => void;
  rowModesModel?: any;
  setRowModesModel?: any;
  selectedRow?: any;
  setSelectedRow?: any;
  rowSelectionModel?: any;
  setRowSelectionModel?: any;
  onRowEditStop?: any;
  isCellEditable?: any;
  apiRef?: any;
  onRowMouseEnter?: any;
  onRowMouseLeave?: any;
  onCellClick?: (params: GridCellParams) => void;
  onRowClick?: (params: GridRowParams) => void;
  onRowDoubleClick?: (params: GridRowParams) => void;
  sortModel?: GridSortModel;
  onSortModelChange?: (
    model: GridSortModel,
    details: GridCallbackDetails
  ) => void;
  sortingMode?: string;
  sortingOrder?: GridSortDirection[];
  columnVisibilityModel?: any;
  enableStatusColFilter?: any;
  autosizeOptions?: any;
};

export const DataGridAccordion = (props: SectionProps) => {
  const {
    displayMsg,
    enableSummary,
    loading,
    type,
    icon,
    sectionName,
    rows,
    columns,
    closeHandler,
    onRowUpdate,
    rowModesModel,
    setRowModesModel,
    rowSelectionModel,
    setRowSelectionModel,
    onRowEditStop,
    isCellEditable,
    apiRef,
    onRowMouseEnter,
    onRowMouseLeave,
    onCellClick,
    onRowClick,
    onRowDoubleClick,
    columnVisibilityModel,
    enableStatusColFilter,
    autosizeOptions,
  } = props;
  const [accordionOpen, setAccordionOpen] = useState(type === "ALLERGY_CCD");
  const setShowToast = useSetToastFamily("AmbPatOverview");

  const titleAndKeys = useMemo(() => {
    let titleAndKeys: any[] = [];
    if (type === "PROBLEMS") {
      titleAndKeys = [
        ["Problem Name", "name"],
        ["Status", "status"],
        ["ICD10 Code", "icd10code"],
        ["SNOMED Code", "snomedcode"],
        ["Onset Date", "onsetdate"],
        ["Resolution Date", "resolveddate"],
        ["Comment", "comments"],
        ["Entered By", "lgname"],
        ["Date/Time Entered", "fmtlgtime"],
        ["Added to this Encounter", "add2encounter"],
        ["Time Added to this Encounter", "add2encdate"],
        ["Added to this Encounter By", "lgname"],
      ];
    } else if (type === "DIAGNOSES") {
      titleAndKeys = [
        ["Diagnosis", "name"],
        ["Status", "status"],
        ["SNOMED", "snomedcode"],
        ["ICD10", "icd10code"],
        ["Onset Date", "onsetdate"],
        ["Comment", "comments"],
        ["Entered By", "lgname"],
        ["Date/Time Entered", "fmtlgtime"],
        ["Added to Problems List", "add2encounter"],
        ["Time Added to Problems List", "add2encdate"],
        ["Added to Problems List By", "lgname"],
      ];
    }
    return titleAndKeys;
  }, [type]);

  const TooltipDetailRow = useCallback(
    (params: GridRowProps) => {
      return (
        <StyledNameTooltip
          enterDelay={800}
          TransitionComponent={Fade}
          TransitionProps={{ timeout: 200 }}
          title={
            <>
              <Grid container>
                {titleAndKeys.map((item, index) => (
                  <Grid container key={index}>
                    <Grid item xs={5}>
                      <div
                        style={{
                          fontStyle: "bold",
                          fontSize: "13px",
                          fontWeight: "700",
                          textAlign: "left",
                        }}
                      >
                        {item[0] + ":"}
                      </div>
                    </Grid>
                    <Grid item xs={7}>
                      <div style={{ fontSize: "13px", textAlign: "left" }}>
                        {item[1] && params.row ? params.row[item[1]] : ""}
                      </div>
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            </>
          }
        >
          <GridRow {...params} />
        </StyledNameTooltip>
      );
    },
    [titleAndKeys]
  );

  return (
    <Accordion
      disableGutters
      expanded={accordionOpen}
      square={true}
      sx={{
        boxShadow: "0",
        borderRadius: "8px",
        opacity: 1,
      }}
    >
      {enableSummary ? (
        <AccordionSummary
          sx={{
            height: 40,
            padding: "0px 0px 0px 30px",
            flexDirection: "row-reverse",
            backgroundColor: "#F2F2F2",
          }}
          expandIcon={
            <ArrowDropDownIcon
              color="primary"
              onClick={() => setAccordionOpen(!accordionOpen)}
            />
          }
          aria-controls="reviewsurescriptsimport-content"
          id="reviewsurescriptsimport-header"
        >
          <Box sx={{ flexGrow: 1 }}>
            <Grid
              container
              spacing={1}
              direction="row"
              justifyContent="flex-start"
              alignItems="flex-start"
            >
              <Grid item xs={0.25}>
                {icon ? icon : <></>}
              </Grid>
              <Grid item xs>
                <Typography sx={{ fontWeight: "bold", paddingTop: "5px" }}>
                  {sectionName || ""}
                </Typography>
              </Grid>
              {closeHandler ? (
                <Grid item xs sx={{ textAlign: "right" }}>
                  <Button
                    color={"secondary"}
                    variant="contained"
                    style={{ marginRight: "20px" }}
                    onClick={closeHandler}
                  >
                    Close
                  </Button>
                </Grid>
              ) : null}
            </Grid>
          </Box>
        </AccordionSummary>
      ) : null}
      <AccordionDetails
        sx={{
          padding: "0px",
        }}
      >
        <Box
          sx={{
            width: "100%",
            "& .headerCls": {
              backgroundColor: "rgba(216, 220, 227, 255)",
            },
          }}
        >
          {!enableSummary || accordionOpen ? (
            <StripedGrid
              apiRef={apiRef}
              type={type}
              loading={loading}
              onRowEditStop={onRowEditStop}
              onRowUpdate={onRowUpdate}
              rowModesModel={rowModesModel}
              setRowModesModel={setRowModesModel}
              setRowSelectionModel={setRowSelectionModel}
              rowSelectionModel={rowSelectionModel}
              onRowClick={(params: GridRowParams) => {
                if (onRowClick) {
                  onRowClick(params);
                }
              }}
              onRowDoubleClick={(params: GridRowParams) => {
                if (onRowDoubleClick) {
                  onRowDoubleClick(params);
                }
              }}
              onCellClick={onCellClick}
              columns={columns}
              rows={rows}
              noDataMsg={displayMsg}
              enableStatusColFilter={enableStatusColFilter}
              autosizeOptions={autosizeOptions}
              slotProps={{
                row: {
                  style: { cursor: "context-menu" },
                  onMouseEnter: onRowMouseEnter,
                  onMouseLeave: onRowMouseLeave,
                },
              }}
              isCellEditable={isCellEditable}
              onProcessRowUpdateError={(error: any) => {
                setShowToast({
                  type: "warning",
                  text: error.message,
                  open: true,
                });
              }}
              columnVisibilityModel={columnVisibilityModel}
              slots={{
                row: TooltipDetailRow,
              }}
            />
          ) : (
            <></>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};
