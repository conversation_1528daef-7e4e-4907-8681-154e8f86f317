/**
 * Constants.tsx
 *
 * @author: jfsys
 * @description Constants
 */
import { Tooltip, TooltipProps, styled, tooltipClasses } from "@mui/material";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";
import TabPanel from "@mui/lab/TabPanel";

export const ModuleHeaderHeight = 48;
export const OverlayHeight = 60;
export const RowHeight = 32;
export const columnHeaderHeight = 32;

export const DataGridProHeaderHeight = 42;
export const ProblemsOverlayHeight = 60;
export const ProblemsRowHeight = 40;

export const notePageSize = 7;
export const medPageSize = 10;
export const allergyPageSize = 10;
export const problemsPageSize = 10;
export const diagnosesPageSize = 10;
export const HMPageSize = 10;
export const clinicVisitPageSize = 10;
export const diagnosticsPageSize = 10;

export const ModuleHeaderSx = {
  color: "#11181F",
  fontFamily: "Roboto",
  fontSize: "18px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "normal",
  paddingTop: "0px",
  paddingRight: "10px",
};

export const StyledTabs = styled(Tabs)({
  "&.MuiTabs-root": {
    height: "30px",
    minHeight: 0,
  },
  "& .MuiTabs-flexContainer": {
    height: "30px",
    backgroundColor: "#F2F2F2",
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    "&:last-child": {
      marginLeft: "18px",
    },
  },
  "& .MuiButtonBase-root": {
    backgroundColor: "#F2F2F2",
    color: "#000",
    fontWeight: "normal",
  },
  "& .MuiTabs-indicator": {
    backgroundColor: "#f3d379",
    height: "3px",
  },
});

export const StyledTab = styled(Tab)({
  "&.MuiTab-root": {
    fontFamily: "Roboto",
    cursor: "pointer",
    fontSize: "16px",
    fontWeight: "500",
    color: "#000",
    backgroundColor: "#F2F2F2",
    lineHeight: "18px",
    margin: "2px",
    overflow: "visible",
    padding: "5px",
    border: "none",
    borderRadius: "8px",
    display: "flex",
    textTransform: "none",
    justifyContent: "center",
  },
  "&.Mui-selected": {
    color: "black",
  },
});

export const StyledTabPanel = styled(TabPanel)({
  width: "100%",
  height: "calc(100% - 30px)",
  padding: 0,
  background: "#ffffff",
  borderRadius: "0px 8px 8px 8px",
});

export const GridColumnHeaderTitleSx = {
  color: "#000000",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "normal",
};

const NameTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))({
  [`& .${tooltipClasses.tooltip}`]: {
    maxWidth: 500,
    backgroundColor: "#FFFFFF",
    color: "#000000",
    marginTop: "0px !important",
  },
});
export const StyledNameTooltip = styled(NameTooltip)(({ theme }) => ({
  borderRadius: "4px",
  border: "1px solid #dadde9",
  backgroundColor: "#FFFFFF",
  fontFamily: "Roboto",
}));

export const USERCONTEXT = {
  NURSE: "nurse",
  PROVIDER: "provider",
};
