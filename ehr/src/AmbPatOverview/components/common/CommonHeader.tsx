import { Stack } from "@mui/material";
import { IconButton, PrintIcon } from "@cci/mui-components";
import HelpIcon from "@mui/icons-material/Help";
import InfoIcon from "@mui/icons-material/Info";
import RedoSharpIcon from "@mui/icons-material/RedoSharp";
import UndoSharpIcon from "@mui/icons-material/UndoSharp";
import React from "react";
import { PrintManagement } from "@cci-monorepo/Pld/components/common/pld/PrintManagement";
import { useAtom, useSetAtom } from "jotai";
import {
  redoAtom,
  redoHistoryAtom,
  undoAtom,
  undoRedoFlagAtom,
  undoHistoryAtom,
} from "@cci-monorepo/AmbPatOverview/context/UndoRedoAtoms";

export const CommomHeader = () => {
  const [openPrintManagement, setOpenPrintManagement] = React.useState(false);
  const [, undo] = useAtom(undoAtom);
  const [, redo] = useAtom(redoAtom);
  const setUndoRedoFlag = useSetAtom(undoRedoFlagAtom);
  const [undoHistory] = useAtom(undoHistoryAtom);
  const [redoHistory] = useAtom(redoHistoryAtom);
  const [undoRedoFlag] = useAtom(undoRedoFlagAtom);

  const isUndoDisabled = undoHistory.length === 0 || undoRedoFlag;
  const isRedoDisabled = redoHistory.length === 0 || undoRedoFlag;

  const handlePrint = () => {
    setOpenPrintManagement(true);
  };

  const handleUndo = () => {
    setUndoRedoFlag(true);
    undo();
  };

  const handleRedo = () => {
    setUndoRedoFlag(true);
    redo();
  };

  return (
    <>
      <Stack direction={"row"} spacing={"8px"} marginRight={"16px"}>
        <IconButton
          color="secondary"
          disableElevation={true}
          icon={<UndoSharpIcon />}
          disabled={isUndoDisabled}
          onClick={handleUndo}
          data-testid="pldHeader-undo-IconButton"
        />
        <IconButton
          color="secondary"
          disableElevation={true}
          icon={<RedoSharpIcon />}
          disabled={isRedoDisabled}
          onClick={handleRedo}
          data-testid="pldHeader-redo-IconButton"
        />
        <IconButton
          color="secondary"
          disableElevation={true}
          icon={<InfoIcon />}
          disabled
        />
        <IconButton
          color="secondary"
          disableElevation={true}
          icon={<HelpIcon />}
          disabled
        />
        <IconButton
          color="secondary"
          disableElevation={true}
          icon={<PrintIcon />}
          disabled={false}
          onClick={handlePrint}
          data-testid="pldHeader-print-IconButton"
        />
      </Stack>
      <PrintManagement
        open={openPrintManagement}
        setOpen={setOpenPrintManagement}
        isAmb={true}
      />
    </>
  );
};
