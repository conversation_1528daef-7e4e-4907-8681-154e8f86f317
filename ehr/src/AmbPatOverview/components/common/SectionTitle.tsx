import { Grid, Typography } from "@mui/material";

interface SectionTitleProps {
  title: string;
  required: Boolean;
}

const SectionTitle: React.FC<SectionTitleProps> = ({ title, required }) => {
  return (
    <Grid container alignItems="center">
      <Grid item>
        <Typography
          variant="h6"
          sx={{
            color: "#000",
            fontSize: "14px",
            fontWeight: "500",
            fontFamily: "Roboto",
          }}
        >
          {title}
        </Typography>
      </Grid>
      {required && (
        <Grid item>
          <Typography variant="h6" sx={{ color: "#CF4C35" }}>
            *
          </Typography>
        </Grid>
      )}
    </Grid>
  );
};

export default SectionTitle;
