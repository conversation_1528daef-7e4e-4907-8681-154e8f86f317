/**
 * StripedGrid.tsx
 *
 * @author: RnD
 * @description common component striped data grid pro
 */
import { useRef } from "react";
import {
  GridRowEditStopReasons,
  GridRowModes,
  GridRowModesModel,
  GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import { ClickAwayListener } from "@mui/material";
import { CommonDataGrid } from "../common/CommonDataGrid";

export const rowHeight: number = 60;
export const titleRowHeight: number = 30;
export const fontCss = "16px Roboto";
export const boldFontCss = "bold " + fontCss;

const extraPadding = 40;
const maxNormalColumnWidth = 500;

export const getTextWidth = (text: string, font: string) => {
  const canvas: any = document.createElement("canvas");
  const context = canvas.getContext("2d");
  context.font = font;
  const metrics = context.measureText(text);
  return metrics.width + extraPadding;
};

export const getDisplayTextWidth = (text: string) => {
  let width = getTextWidth(text, fontCss);
  return Math.min(Math.ceil(width), maxNormalColumnWidth);
};

export const getBoldDisplayTextWidth = (text: string) => {
  return getTextWidth(text, boldFontCss);
};

const StripedGrid = (props: any) => {
  const currentFocusCell = useRef(null);
  const handleStateChange = (params: any, event: any, details: any) => {
    if (currentFocusCell.current === null) {
      currentFocusCell.current = params.focus.cell;
      return;
    }
    var _FocusedCell_curr: any = currentFocusCell.current;
    var _rowid = _FocusedCell_curr == null ? void 0 : _FocusedCell_curr.id;
    var _field = _FocusedCell_curr == null ? "" : _FocusedCell_curr.field;

    if (
      (params.focus.cell === null && currentFocusCell.current) ||
      _rowid !== params.focus.cell.id
    ) {
      const apiRef = details.api;
      if (apiRef.getRowMode(_rowid) === GridRowModes.View) {
        currentFocusCell.current = params.focus.cell;
        return;
      }
      const newParams = {
        id: _rowid,
        field: _field,
        reason: GridRowEditStopReasons.rowFocusOut,
      };
      currentFocusCell.current = params.focus.cell;
      apiRef.publishEvent("rowEditStop", newParams, event);
    }
  };

  return (
    <ClickAwayListener
      onClickAway={(envent: any) => {
        if (props.rowSelectionModel && props.rowSelectionModel.length > 0) {
          props.setRowSelectionModel([]);
        }
      }}
    >
      <CommonDataGrid
        editMode="row"
        experimentalFeatures={{ rowGrouping: true }}
        getRowClassName={(params: any) => {
          if (params.row && params.row.groupTitle) {
            return "title";
          } else {
            return params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even";
          }
        }}
        disableMultipleRowSelection
        disableColumnReorder
        onStateChange={handleStateChange}
        sx={{
          "&.MuiDataGrid-root .MuiDataGrid-row--editing .MuiDataGrid-cell": {
            backgroundColor: "#F6E596 !important",
          },
          "&.MuiDataGrid-root .MuiDataGrid-cell:focus-within": {
            outline: "none !important",
          },
        }}
        rowModesModel={props.rowModesModel}
        onRowModesModelChange={(newModel: GridRowModesModel) => {
          if (props.setRowModesModel) {
            props.setRowModesModel(newModel);
          }
        }}
        onRowSelectionModelChange={(rsmodel: GridRowSelectionModel) => {
          if (props.rowSelectionModel) {
            props.setRowSelectionModel(rsmodel);
          }
        }}
        processRowUpdate={(newRow: any, oldRow: any) => {
          if (props.onRowUpdate) {
            return props.onRowUpdate(newRow, oldRow);
          }
          return newRow;
        }}
        onProcessRowUpdateError={(error: any) => {
          if (props.onProcessRowUpdateError) {
            props.onProcessRowUpdateError(error);
          }
        }}
        {...props}
      />
    </ClickAwayListener>
  );
};

export default StripedGrid;
