/**
 * AllergyToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Allergies
 */

import { useAtom, useAtomValue } from "jotai";

import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink, IconEye } from "../../../common/assets";
import {
  hasReviewedAllergyAtom,
  rowDataAllergyAtom,
  isShowInactiveAllergyAtom,
  noKnownAllergyAtom,
  reviewMessageAllergyAtom,
  pageAtom,
} from "../../context/AllergyAtoms";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { allergyPageSize } from "../common/Constants";
import { useMemo } from "react";

export const AllergyToolbar = (props: any) => {
  const hasReviewed = useAtomValue(hasReviewedAllergyAtom);
  const noKnownAllergy = useAtomValue(noKnownAllergyAtom);
  const rowData = useAtomValue(rowDataAllergyAtom);
  const page = useAtomValue(pageAtom);
  const [isShowInactive, setIsShowInactive] = useAtom(
    isShowInactiveAllergyAtom
  );
  const reviewMessage = useAtomValue(reviewMessageAllergyAtom);
  const reviewMsg =
    hasReviewed || noKnownAllergy
      ? reviewMessage.length > 0 && reviewMessage[0].last_reviewed
      : "Allergies Not Reviewed";

  const totalActiveAllergies = useMemo(() => {
    if (isShowInactive) {
      return rowData.length;
    } else {
      return rowData.filter((data: { [key: string]: string | number }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowData]);

  const handleRedirect = () => {
    toPldModule("Allergies");
  };

  const handleShowInactiveClick = () => {
    setIsShowInactive(!isShowInactive);
  };

  const menuItems: MenuItemProps[] = [
    ...(props.readonly
      ? []
      : [
          {
            label: "View In Health Profile",
            icon: <IconExternalLink />,
            handler: handleRedirect,
          },
        ]),
    {
      label: `${isShowInactive ? "Hide" : "Show"} Inactive Items`,
      icon: <IconEye />,
      handler: handleShowInactiveClick,
    },
  ];

  return (
    <CommonToolbar
      name="Allergies"
      isPldWidget={true}
      isCritical={true}
      hasReviewed={hasReviewed || noKnownAllergy}
      count={page * allergyPageSize}
      totalCount={totalActiveAllergies}
      menuItems={menuItems}
      reviewMsg={reviewMsg}
    />
  );
};
