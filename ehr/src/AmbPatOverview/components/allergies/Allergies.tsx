/**
 * Allergies.tsx
 *
 * @author: jfsys
 * @description Allergies component
 */

import { useEffect, useState, useMemo } from "react";
import { useAtomValue, useSetAtom, useAtom } from "jotai";
import Box from "@mui/material/Box";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { AdditionalFooter } from "../common/AdditionalFooter";
import { AllergyToolbar } from "./AllergyToolbar";
import { ModuleHeaderHeight, allergyPageSize } from "../common/Constants";
import { AllergySection } from "./AllergySection";
import {
  rowDataAllergyAtom,
  setParseDataAllergyAtom,
  setReviewMessageAllergy<PERSON>tom,
  pageAtom,
  isShowInactiveAllergyAtom,
} from "../../context/AllergyAtoms";
import { getAllergies } from "@cci-monorepo/Pld/util/AllergyDataAPI";
import { useSetErrorDialog } from "@cci-monorepo/common/hooks/dialog";
import { getReviewHistory } from "@cci-monorepo/Pld/components/allergies/AllergyUtils";
import { serverRequest } from "@cci-monorepo/Pld/util/DataRequests";
import { unableAssessReasonListAtom } from "@cci-monorepo/Pld/context/AllergyAtoms";
import { getChoiceList } from "@cci-monorepo/Pld/util/DataAPI";

export const Allergies = (props: any) => {
  const rowData = useAtomValue(rowDataAllergyAtom);
  const parseDataAtom = useSetAtom(setParseDataAllergyAtom);
  const [hasData, setHasData] = useState<boolean>(false);
  const [expanded, setExpanded] = useState<boolean>(false);
  const setReviewMessageAllergy = useSetAtom(setReviewMessageAllergyAtom);
  const [page, setPage] = useAtom(pageAtom);
  const isShowInactive = useAtomValue(isShowInactiveAllergyAtom);
  const setOpenErrorDialog = useSetErrorDialog();
  const setUnableAssessReasonList = useSetAtom(unableAssessReasonListAtom);

  useEffect(() => {
    if (!hasData) {
      const onSuccess = (data: any) => {
        parseDataAtom(data);
        setHasData(true);
      };

      const onFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load allergy data:" + error,
          open: true,
        });
      };

      const onHistorySuccess = (data: any) => {
        setReviewMessageAllergy(
          getReviewHistory(data, "allergies_reviewhistory")
        );
      };

      const onHistoryFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load review history:" + error,
          open: true,
        });
      };

      const params = {
        campus: cci.cfg.campus,
        dbs: Cci.util.Patient.getDbpath(),
      };
      getAllergies(params, onSuccess, onFailure).then((ret: any) => {
        onSuccess(ret);
        return serverRequest(
          "pld/allergies/review_history",
          params,
          onHistorySuccess,
          onHistoryFailure
        );
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const params = {
      chcs: "allergy_unassess_reason",
    };

    const onSuccess = (result: any) => {
      setUnableAssessReasonList(result.allergy_unassess_reason.data.flat());
    };

    const onFailure = (error: string) => {
      console.error("Error in getting choice list for", params.chcs);
    };

    getChoiceList(params, onSuccess, onFailure);
  }, [setUnableAssessReasonList]);

  useEffect(() => {
    setExpanded(rowData.length > 0 ? true : false);
  }, [rowData, setExpanded]);

  const totalAllergies = useMemo(() => {
    if (isShowInactive) {
      return rowData.length;
    } else {
      return rowData.filter((data: { [key: string]: string | number }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowData]);

  if (hasData) {
    return (
      <Accordion
        square={true}
        sx={{
          borderRadius: "8px",
          boxShadow:
            "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
        }}
        disableGutters
        expanded={expanded}
        id="allergies"
      >
        <AccordionSummary
          sx={{
            height: ModuleHeaderHeight,
            flexDirection: "row-reverse",
            backgroundColor: "#F2F2F2",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
            [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
              {
                transform: "rotate(90deg)",
              },
          }}
          expandIcon={
            <ArrowRightIcon
              color="primary"
              onClick={() => setExpanded(!expanded)}
            />
          }
          aria-controls="allergies-content"
          id="allergies-header"
        >
          <AllergyToolbar readonly={props.readonly} />
        </AccordionSummary>
        <AccordionDetails
          sx={{
            padding: 0,
            margin: 0,
            borderBottomLeftRadius: "8px",
            borderBottomRightRadius: "8px",
          }}
        >
          <Box>{expanded ? <AllergySection /> : <></>}</Box>
          {totalAllergies > allergyPageSize * page && (
            <AdditionalFooter
              msg="Load More"
              handleClick={() => setPage((preVal) => preVal + 1)}
            />
          )}
        </AccordionDetails>
      </Accordion>
    );
  } else {
    return <></>;
  }
};
