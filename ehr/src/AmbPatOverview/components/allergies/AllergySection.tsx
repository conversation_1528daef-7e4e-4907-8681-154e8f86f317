/**
 * AllergiesSection.tsx
 *
 * @author: jfsys
 * @description Data grid accordion that contains allergy data
 */

import { useAtomValue } from "jotai";
import {
  rowDataAllergyAtom,
  isShowInactiveAllergyAtom,
  pageAtom,
} from "../../context/AllergyAtoms";
import { TooltipCommonDataGrid } from "../common/TooltipCommonDataGrid";
import { GridRenderCellParams, useGridApiRef } from "@mui/x-data-grid-pro";
import { renderGridDataCell } from "@cci-monorepo/Pld/components/allergies/AllergyUtils";
import { useEffect, useMemo } from "react";
import { toPldSelectRow } from "@cci-monorepo/AmbPatOverview/utils/utils";
import CommonRenderCell from "../common/CommonRenderCell";
import { dateComparator } from "@cci-monorepo/common";
import { allergyPageSize } from "../common/Constants";

export const AllergySection = () => {
  const rowData = useAtomValue(rowDataAllergyAtom);
  const isShowInactive = useAtomValue(isShowInactiveAllergyAtom);
  const page = useAtomValue(pageAtom);
  const apiRef = useGridApiRef();

  const displayRows = useMemo(() => {
    if (isShowInactive) {
      return rowData;
    } else {
      return rowData.filter((row: { [key: string]: string | number }) => {
        return String(row.status).toUpperCase() === "ACTIVE";
      });
    }
  }, [isShowInactive, rowData]);

  const displayData = useMemo(() => {
    return displayRows?.length >= allergyPageSize * page
      ? displayRows?.slice(0, allergyPageSize * page)
      : displayRows;
  }, [displayRows, page]);

  const columnData = [
    {
      field: "status",
      headerName: "Status",
      width: 120,
      valueOptions: ["Active", "Inactive", "Error"],
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params, "status", "");
      },
    },
    {
      field: "name",
      headerName: "Allergen",
      minWidth: 150,
      renderCell: CommonRenderCell,
    },
    {
      field: "type",
      headerName: "Type",
      width: 120,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params, "type", "");
      },
    },
    {
      field: "severity",
      headerName: "Severity",
      width: 120,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params, "severity", "");
      },
    },
    {
      field: "onsetdate",
      headerName: "Onset Date",
      flex: 1,
      minWidth: 150,
      sortComparator: dateComparator,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params, "onsetdate", "");
      },
    },
  ];

  const autosizeOptions = {
    columns: ["name"],
    includeHeaders: true,
    includeOutliers: true,
  };

  useEffect(() => {
    setTimeout(() => {
      apiRef.current.autosizeColumns(autosizeOptions);
    }, 100);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShowInactive]);

  return (
    <TooltipCommonDataGrid
      type="allergies"
      apiRef={apiRef}
      rows={displayData}
      columns={columnData}
      noDataMsg="No active allergies"
      enableStatusColFilter={true}
      autosizeOptions={autosizeOptions}
      onRowDoubleClick={(row: any) => {
        toPldSelectRow("Allergies", row.row.name);
      }}
      columnVisibilityModel={{
        status: isShowInactive,
      }}
    />
  );
};
