import { MultilineInputField } from "@cci-monorepo/common/mui-components/src/export";
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from "@mui/material";
import { ModuleHeaderHeight } from "../common/Constants";
import { HPIToolbar } from "./HPIToolbar";
import { useEffect } from "react";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { useSetErrorDialog, useSetToastFamily } from "@cci-monorepo/common";

import {
  savedHPIAtom,
  setParseDataHPIAtom,
  editedMessageHPIAtom,
} from "../../context/HPIAtoms";
import { serverRequest } from "@cci-monorepo/Pld/util/DataRequests";
import {
  formDataAtom,
  saveUndoAtom,
  undoRedoFlagAtom,
} from "@cci-monorepo/AmbPatOverview/context/UndoRedoAtoms";

export const HPI = (props: any) => {
  const [savedHPI, setSavedHPI] = useAtom(savedHPIAtom);
  const parseHPIData = useSetAtom(setParseDataHPIAtom);
  const editedMessageHPI = useAtomValue(editedMessageHPIAtom);
  const [formData, setFormData] = useAtom(formDataAtom);
  const saveUndo = useSetAtom(saveUndoAtom);
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setOpenErrorDialog = useSetErrorDialog();

  const [undoRedoFlag, setUndoRedoFlag] = useAtom(undoRedoFlagAtom);

  const dispText = "History of Present Illness";
  // Handle saving data
  const handleSave = (newHPI: string) => {
    const onSuccess = () => {
      const successCallback = (parseData: any) => {
        setShowToast({
          type: "success",
          text: `${dispText} updated!`,
          open: true,
        });
        saveUndo({
          ...formData,
          hpi: parseData.hpi,
        });
      };
      loadData(successCallback);
    };
    const onFailure = (error: string) => {
      console.error(`Failed to save ${dispText}.`, error);
      setOpenErrorDialog({
        text: `Failed to save: ${error}`,
        open: true,
      });
    };
    const data = [{ jit: "26263", nit: 0, ks: "admitkey", data: newHPI }];
    let params = { appname: "", dbpath: "", fsdata: "", perm: "", type: "" };
    params.appname = "UNLOCK";
    params.dbpath = Cci.Patient.getDbpath();
    params.fsdata = Ext.encode(data);
    params.perm = "E";
    params.type = "DEMO";
    serverRequest("DBI/savedata", params, onSuccess, onFailure);
  };

  const loadData = (successCallback?: any) => {
    let params = {
      dbpath: Cci.util.Patient.getDbpath(),
    };
    const onSuccess = (result: any) => {
      if (result && result.history) {
        const parseData = parseHPIData(result.history);
        successCallback(parseData);
      }
    };

    const onFailure = (error: string) => {
      console.error(`Failed to get ${dispText}.`, error);
    };

    serverRequest(
      "ambPatOverview/presentillness",
      params,
      onSuccess,
      onFailure
    );
  };

  useEffect(() => {
    loadData((parseData: any) => {
      setFormData((prev: any) => ({ ...prev, hpi: parseData.hpi }));
    });
  }, []);

  useEffect(() => {
    if (formData.hpi && undoRedoFlag && formData.hpi !== savedHPI) {
      handleSave(formData.hpi);
      setTimeout(() => {
        setUndoRedoFlag(false);
      }, 0);
    }
  }, [formData.hpi, undoRedoFlag]);

  return (
    <Box>
      <Accordion
        square={true}
        sx={{
          borderRadius: "8px",
          boxShadow:
            "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
        }}
        disableGutters
        expanded={true}
        id="hpi"
      >
        <AccordionSummary
          sx={{
            height: ModuleHeaderHeight,
            flexDirection: "row-reverse",
            backgroundColor: "#F2F2F2",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
          }}
          aria-controls="hpi-content"
          id="hpi-header"
        >
          <HPIToolbar dispText={dispText} readonly={props.readonly} />
        </AccordionSummary>
        <AccordionDetails
          sx={{
            padding: "0 8px",
            background: "#f2f2f2",
          }}
        >
          <MultilineInputField
            rows={undefined}
            maxRows={3}
            data={savedHPI}
            setData={setSavedHPI}
            placeholder={"No history of present illness for this encounter"}
            onBlur={() => {
              if (savedHPI !== formData.hpi) handleSave(savedHPI);
            }}
            sx={{
              "& .MuiInputBase-input": {
                font: "15px 400 Roboto",
                overflowY: "scroll",
              },
            }}
          />
        </AccordionDetails>
        {true && (
          <AccordionActions
            sx={{
              borderRadius: "0px 0px 8px 8px",
              backgroundColor: "#F2F2F2",
              justifyContent: "flex-start",
            }}
          >
            <Typography
              sx={{
                font: "normal 400 12px Roboto",
                color: "rgba(0, 0, 0, 0.58)",
              }}
            >
              {editedMessageHPI?.last_edited}
            </Typography>
          </AccordionActions>
        )}
      </Accordion>
    </Box>
  );
};
