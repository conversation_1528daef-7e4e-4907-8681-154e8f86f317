import { Box, Divider, Grid, Stack, Typography } from "@mui/material";
import { useAtomValue } from "jotai";
import { currentHPIAtom } from "@cci-monorepo/AmbPatOverview/context/HPIAtoms";
import { describeItemFields, hpiDataKeys } from "./HPIFieldsConfig";

export default function DescribeItemPanel() {
  const currentHPI = useAtomValue(currentHPIAtom);

  return (
    <Box
      component="div"
      sx={{
        width: "100%",
        height: "100%",
        backgroundColor: "white",
        paddingRight: "16px",
      }}
    >
      <Typography
        gutterBottom
        sx={{ color: "rgba(0, 0, 0, 0.70)", font: "normal 700 28px Roboto" }}
      >
        History of Present Illness
      </Typography>
      <Divider sx={{ marginTop: "15px" }} />
      <Stack component="div" sx={{ height: "620px", overflow: "auto" }}>
        <Stack sx={{ width: "95%", marginTop: "10px" }}>
          <Grid
            container
            direction="row"
            justifyContent="center"
            alignItems="center"
            sx={{
              margin: "10px 0 0 40px",
            }}
          >
            {describeItemFields.map((key) => {
              return (
                <>
                  <Grid
                    item
                    xs={3}
                    sx={{ color: "black", font: "normal 700 14px Roboto" }}
                  >
                    {key}
                  </Grid>
                  <Grid
                    item
                    xs={9}
                    sx={{ color: "black", font: "normal 400 14px Roboto" }}
                  >
                    {currentHPI[hpiDataKeys[key]]}
                  </Grid>
                </>
              );
            })}
          </Grid>
        </Stack>
        <Divider sx={{ marginTop: "16px" }} />
      </Stack>
    </Box>
  );
}
