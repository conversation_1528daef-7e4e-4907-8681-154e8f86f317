/**
 * ReasonToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for HPI
 */

import { useState } from "react";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { EditHistoryIcon } from "../../../common/assets";
import { EditHistoryDialog } from "../common/EditHistoryDialog";
import HPIEditHistoryPanel from "./HPIEditHistoryPanel";
import HPIDescribeItemPanel from "./HPIDescribeItemPanel";

export const HPIToolbar = ({ dispText, readonly }: any) => {
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("1");

  const handleShowHistory = () => {
    setActiveTab("1");
    setShowHistoryDialog(true);
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "Edit History",
      icon: <EditHistoryIcon />,
      handler: handleShowHistory,
      disabled: readonly,
    },
  ];

  const tabs = [
    {
      label: "Describe Item",
      value: "0",
      tabDetail: <HPIDescribeItemPanel />,
    },
    {
      label: "Edit History",
      value: "1",
      tabDetail: <HPIEditHistoryPanel />,
    },
  ];

  return (
    <>
      <CommonToolbar
        name={dispText}
        isPldWidget={false}
        menuItems={menuItems}
      />
      <EditHistoryDialog
        tabs={tabs}
        value={activeTab}
        open={showHistoryDialog}
        setOpen={setShowHistoryDialog}
      />
    </>
  );
};
