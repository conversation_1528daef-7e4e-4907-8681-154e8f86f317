import {
  Box,
  Divider,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { useAtomValue } from "jotai";
import { historyHPIAtom } from "../../context/HPIAtoms";
import { hpiDataKeys, hpiDispFields } from "./HPIFieldsConfig";

const StyledTableCell = styled(TableCell)({
  width: "265px",
  font: "normal 400 14px Roboto",
  color: "#000",
  borderRight: "solid 1px rgb(225 230 238)",
});

export default function HPIEditHistoryPanel() {
  const hpiData = useAtomValue(historyHPIAtom);
  const hasData = hpiData.length > 0 ? true : false;

  return (
    <Box component="div" sx={{ backgroundColor: "white" }}>
      <Typography
        sx={{ color: "rgba(0, 0, 0, 0.7)", font: "normal 700 28px Roboto" }}
      >
        History of Present Illness
      </Typography>
      <Divider sx={{ marginTop: "15px" }} />
      <Box sx={{ marginTop: "10px", overflow: "auto" }}>
        <TableContainer
          component={Paper}
          sx={{
            width: "100%",
            maxWidth: "100%",
            height: "552px",
            overflow: "auto",
          }}
        >
          <Table
            size="small"
            aria-label="a dense table"
            sx={{ tableLayout: "fixed", width: "100%" }}
          >
            {hasData ? (
              <>
                <TableHead>
                  <TableRow sx={{ height: "25px" }}>
                    <TableCell
                      sx={{
                        width: "249px",
                        backgroundColor: "#2E4B75",
                        borderRight: "solid 2px black",
                      }}
                    ></TableCell>
                    {hpiData.map((history: any) => (
                      <TableCell
                        component="th"
                        scope="row"
                        sx={{
                          width: "265px",
                          backgroundColor: "#2E4B75",
                          borderRight: "solid 1px #2E4B75",
                        }}
                      ></TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell
                      component="th"
                      scope="row"
                      sx={{
                        font: "normal 700 14px Roboto",
                        borderRight: "solid 2px black",
                        backgroundColor: "#EDEDED",
                      }}
                    >
                      Action
                    </TableCell>
                    {hpiData.map((item: any, index: number) => {
                      return (
                        <StyledTableCell
                          component="th"
                          scope="row"
                          sx={{
                            backgroundColor: index === 0 ? "white" : "#C7E5D8",
                            font: "normal 700 14px Roboto",
                            borderRight: "solid 1px rgb(225 230 238)",
                          }}
                        >
                          {index === 0 ? "Added" : "Modified"}
                        </StyledTableCell>
                      );
                    })}
                  </TableRow>
                  {hpiDispFields.map((field: string, fieldIndex: any) =>
                    field === "Divider_line" ? (
                      <TableRow sx={{ height: "25px" }}>
                        <TableCell
                          sx={{
                            width: "249px",
                            backgroundColor: "#2E4B75",
                            borderRight: "solid 2px black",
                          }}
                        ></TableCell>
                        {hpiData.map((history: any) => (
                          <TableCell
                            component="th"
                            scope="row"
                            sx={{
                              width: "265px",
                              backgroundColor: "#2E4B75",
                              borderRight: "solid 1px #2E4B75",
                            }}
                          ></TableCell>
                        ))}
                      </TableRow>
                    ) : (
                      <TableRow>
                        <TableCell
                          sx={{
                            font: "normal 700 14px Roboto",
                            borderRight: "solid 2px black",
                            backgroundColor: "#EDEDED",
                            width: "150px",
                            minWidth: "150px",
                          }}
                        >
                          {field}
                        </TableCell>
                        {hpiData.map((data: any, index: number) => {
                          let backgroundColor: string = "white";
                          if (!data || data === undefined)
                            return (
                              <TableCell
                                sx={{
                                  borderRight: "solid 1px rgb(225 230 238)",
                                }}
                              ></TableCell>
                            );
                          if (
                            fieldIndex === hpiDispFields.length - 1 &&
                            index !== 0 &&
                            hpiData[index] &&
                            hpiData[index - 1] &&
                            hpiData[index][hpiDataKeys[field]] !==
                              hpiData[index - 1][hpiDataKeys[field]]
                          ) {
                            backgroundColor = "#C7E5D8";
                          }
                          return (
                            <TableCell
                              sx={{
                                backgroundColor: backgroundColor,
                                borderRight: "solid 1px rgb(225 230 238)",
                                wordWrap: "break-word",
                              }}
                            >
                              {hpiData[index][hpiDataKeys[field]]}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    )
                  )}
                </TableBody>
              </>
            ) : null}
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
}
