/**
 * SubjectiveToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for NS
 */

import { useState } from "react";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { EditHistoryIcon } from "../../../common/assets";
import { SubjectiveEditHistoryDialog } from "./SubjectiveEditHistoryDialog";

export const SubjectiveToolbar = ({ dispText, readonly }: any) => {
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  const handleShowHistory = () => {
    setShowHistoryDialog(true);
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "Edit History",
      icon: <EditHistoryIcon />,
      handler: handleShowHistory,
      disabled: readonly,
      hidden: false,
    },
  ];

  return (
    <>
      <CommonToolbar
        name={dispText}
        isPldWidget={false}
        menuItems={menuItems}
      />
      <SubjectiveEditHistoryDialog
        value="1"
        open={showHistoryDialog}
        setOpen={setShowHistoryDialog}
      />
    </>
  );
};
