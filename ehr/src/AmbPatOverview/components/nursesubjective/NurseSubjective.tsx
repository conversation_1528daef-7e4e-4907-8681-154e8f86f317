import { useEffect } from "react";
import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { MultilineInputField } from "@cci/mui-components";
import { useSetErrorDialog, useSetToastFamily } from "@cci-monorepo/common";
import Typography from "@mui/material/Typography";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionActions from "@mui/material/AccordionActions";
import { ModuleHeaderHeight } from "../common/Constants";
import {
  savedDataAtom,
  setParseDataAtom,
  editedMessageDataAtom,
} from "../../context/NurseSubjectAtom";
import { serverRequest } from "@cci-monorepo/Pld/util/DataRequests";
import { SubjectiveToolbar } from "./SubjectiveToolbar";
import {
  formData<PERSON>tom,
  saveUndoAtom,
  undoRedoFlagAtom,
} from "@cci-monorepo/AmbPatOverview/context/UndoRedoAtoms";

export const NurseSubjective = (props: any) => {
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setOpenErrorDialog = useSetErrorDialog();
  const [savedData, setSavedData] = useAtom(savedDataAtom);
  const editedMessageData = useAtomValue(editedMessageDataAtom);
  const parseData = useSetAtom(setParseDataAtom);
  const [formData, setFormData] = useAtom(formDataAtom);
  const [undoRedoFlag, setUndoRedoFlag] = useAtom(undoRedoFlagAtom);
  const saveUndo = useSetAtom(saveUndoAtom);

  const dispText = "Nurse Subjective";

  // Handle saving data
  const handleSave = (description: string) => {
    const onSuccess = async () => {
      loadData((parseData: any) => {
        setShowToast({
          type: "success",
          text: `${dispText} ${editedMessageData ? "updated" : "added"}!`,
          open: true,
        });
        saveUndo({
          ...formData,
          nurseSubjective: parseData.description,
        });
      });
    };
    const onFailure = (error: string) => {
      console.error(`Failed to save ${dispText}.`, error);
      setOpenErrorDialog({
        text: `Failed to save: ${error}`,
        open: true,
      });
    };
    var data = [];
    data.push({
      jit: "22247",
      nit: 0,
      ks: "admitkey",
      data: description,
    });
    var params = { appname: "", dbpath: "", fsdata: "", perm: "", type: "" };
    params.appname = "UNLOCK";
    params.dbpath = Cci.Patient.getDbpath();
    params.fsdata = Ext.encode(data);
    params.perm = "E";
    params.type = "DEMO";
    serverRequest("DBI/savedata", params, onSuccess, onFailure);
  };

  const loadData = (successCallback?: any) => {
    let params = {
      dbpath: Cci.util.Patient.getDbpath(),
    };
    const onSuccess = (result: any) => {
      if (result && result.history) {
        const currentData = parseData(result.history);
        successCallback(currentData);
      }
    };

    const onFailure = (error: string) => {
      console.error(`Failed to get ${dispText}.`, error);
    };

    serverRequest(
      "ambPatOverview/nursesubjective",
      params,
      onSuccess,
      onFailure
    );
  };

  useEffect(() => {
    loadData((parseData: any) => {
      setFormData((prev: any) => ({
        ...prev,
        nurseSubjective: parseData.description,
      }));
    });
  }, []);

  useEffect(() => {
    if (
      formData.nurseSubjective &&
      undoRedoFlag &&
      formData.nurseSubjective !== savedData
    ) {
      handleSave(formData.nurseSubjective);
      setUndoRedoFlag(false);
    }
  }, [formData.nurseSubjective, undoRedoFlag]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={true}
      id="reason"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        aria-controls="reason-content"
        id="reason-header"
      >
        <SubjectiveToolbar
          dispText={dispText}
          readonly={false}
          onSave={handleSave}
        />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: "0px 8px 10px 8px",
          margin: 0,
          background: "#F2F2F2",
        }}
      >
        <MultilineInputField
          rows={undefined}
          data={savedData}
          maxRows={3}
          setData={setSavedData}
          placeholder={"Enter a Nurse Subjective"}
          onBlur={() => {
            if (
              savedData.length > 0 &&
              formData.nurseSubjective !== savedData
            ) {
              handleSave(savedData);
            }
          }}
          sx={{
            "& .MuiInputBase-input": {
              font: "15px 400 Roboto",
              overflowY: "scroll",
            },
          }}
        />
      </AccordionDetails>
      {editedMessageData && (
        <AccordionActions
          sx={{
            borderRadius: "0px 0px 8px 8px",
            backgroundColor: "#F7F7F7",
            justifyContent: "flex-start",
          }}
        >
          <Typography
            sx={{
              font: "normal 400 12px Roboto",
              color: "rgba(0, 0, 0, 0.58)",
            }}
          >
            {editedMessageData.last_edited}
          </Typography>
        </AccordionActions>
      )}
    </Accordion>
  );
};
