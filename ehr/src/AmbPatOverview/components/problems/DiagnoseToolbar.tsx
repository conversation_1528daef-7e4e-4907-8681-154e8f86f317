/**
 * DiagnoseToolbar.tsx
 *
 * @author: RnD
 * @description Toolbar for Diagnose
 */

import { useAtomValue } from "jotai";
import { isShowInActiveProblemsAtom } from "@cci-monorepo/common/context/atoms/problem";
import { Grid, Typography } from "@mui/material";
import { diagnosesPageSize } from "../common/Constants";
import { useMemo } from "react";
import {
  pageDiagAtom,
  rowDataDiagnosisAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";

export const DiagnoseToolbar = () => {
  const rowData = useAtomValue(rowDataDiagnosisAtom);
  const isShowInactive = useAtomValue(isShowInActiveProblemsAtom);
  const page = useAtomValue(pageDiagAtom);

  const rowsCount = useMemo(() => {
    if (isShowInactive) {
      return rowData.length;
    } else {
      return rowData.filter((data: { [key: string]: any }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowData]);

  const countStr = useMemo(() => {
    const count = page * diagnosesPageSize;
    if (count !== undefined && rowsCount !== undefined) {
      if (count >= rowsCount) {
        return `(${rowsCount})`;
      } else {
        return `(${count} of ${rowsCount})`;
      }
    } else {
      return "";
    }
  }, [page, rowsCount]);

  return (
    <>
      <Grid
        container
        spacing={"8px"}
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        sx={{
          marginTop: "-8px",
          opacity: 1,
        }}
      >
        <Grid item sx={{ paddingTop: "6px" }}>
          <Typography
            sx={{
              fontWeight: "bold",
              paddingTop: "0px",
              paddingRight: "10px",
            }}
          >
            Diagnoses Treated This Encounter {countStr}
          </Typography>
        </Grid>
      </Grid>
    </>
  );
};
