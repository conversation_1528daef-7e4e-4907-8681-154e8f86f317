/**
 * ProblemsUtils.tsx
 *
 * @author:
 * @description Data utils for Problems module
 */

import { serverRequest } from "../../utils/DataRequests";
import { diagnosesDBitemsMap, problemsDBitemsMap } from "../common/DataUtils";
import { nullorblank } from "@cci-monorepo/common";

const getProblemAndDiagnoseData = (arr: any, idx: number, module: any) => {
  let icd10 = "",
    snomed = "",
    cicd10: any = 0,
    csnomed: any = 0,
    refine: any = 0,
    namestr: any = "";
  let pb: any = {};
  if (arr.codeSystemResults.length >= 2) {
    pb = {
      id: idx,
      name: arr.match,
      pin: "0",
      frequent: "0",
      sid: Cci.util.Staff.getSid(),
      campus: cci.cfg.campus,
      envid: Cci.Env.getEnvID(),
    };
    arr.codeSystemResults.forEach((item: any) => {
      if (item.codeSystem === "ICD10CM" && item.concepts?.length > 0) {
        cicd10++;
        if (!nullorblank(icd10)) icd10 += ", ";
        icd10 += item.concepts[0].code;
      } else if (item.codeSystem === "SNOMED" && item.concepts?.length > 0) {
        csnomed++;
        if (!nullorblank(snomed)) snomed += ", ";
        snomed += item.concepts[0].code;
        if (module === "problem") namestr = item.concepts[0].description;
      }
    });
    if (cicd10 > 1) refine = 2;
    else if (cicd10 === 1) {
      arr.codeSystemResults.forEach((item: any) => {
        if (item.codeSystem === "ICD10CM" && item.concepts?.length > 0) {
          if (module === "diagnose") {
            pb.name = item.concepts[0].description;
          }
          for (var i = 0; i < item.concepts[0].attributes?.length; i++) {
            let ob = item.concepts[0].attributes[i];
            if (
              ob.type === "Relation Attribute" &&
              ob.name === "ICD10CM Has Clinical Refinement"
            ) {
              refine = 1;
              break;
            }
          }
        }
      });
    }
    if (csnomed === 1 && module === "problem") pb.name = namestr;
    pb = { icd10code: icd10, snomedcode: snomed, refine: refine, ...pb };
  }
  return pb;
};

export const sortProblemsData = (proDatas: any[], module: any) => {
  return proDatas.sort((a: any, b: any) => {
    if (a.pin !== b.pin) {
      return Number(a.pin) - Number(b.pin) > 0 ? -1 : 1;
    } else if (a.frequent !== b.frequent) {
      return Number(a.frequent) - Number(b.frequent) > 0 ? -1 : 1;
    } else if (module === "diagnose" && a.icd10code !== b.icd10code) {
      return Number(a.icd10code) - Number(b.icd10code) > 0 ? 1 : -1;
    } else if (module === "problem" && a.snomedcode !== b.snomedcode) {
      return Number(a.snomed) - Number(b.snomed) > 0 ? 1 : -1;
    }
    return 1;
  });
};

const entryFields: any = [
  "name",
  "status",
  "comments",
  "onsetdate",
  "resolveddate",
  "snomedcode",
  "icd10code",
];

export const searchProsAndDiags = (
  arrs: any[],
  module: any,
  defaultProblems: any[],
  defaultDiagnosis: any[] /*diagnosisArrs:any[]*/
) => {
  let probArrs: any[] = [];
  const arrPros: any[] = [];
  //let refineArrs = cloneDeep(diagnosisArrs);
  let refineArrs: any = {};
  let refineDiagnosisAtts: any = {};
  if (arrs && arrs.length > 0) {
    arrs.forEach((arr: any, idx: number) => {
      if (arr?.codeSystemResults && arr.codeSystemResults.length > 0) {
        let pb: any = {};
        if (module === "problem") {
          pb = getProblemAndDiagnoseData(arr, idx, module);
          arrPros.push(pb);
        } else {
          pb = getProblemAndDiagnoseData(arr, idx, module);
          if (arr.codeSystemResults.length === 2) {
            arr.codeSystemResults.forEach((item: any) => {
              if (item.codeSystem === "ICD10CM" && item.concepts?.length > 0) {
                let rarr: any = {};
                if (!refineArrs.hasOwnProperty(item.concepts[0].code)) {
                  refineArrs[item.concepts[0].code] = {};
                }

                for (var i = 0; i < item.concepts[0].attributes?.length; i++) {
                  let ob = item.concepts[0].attributes[i];
                  if (ob.type === "Knowledge Attribute") {
                    let k = "";
                    if (
                      ob.name.toLowerCase().includes("icd10cm clinical attr")
                    ) {
                      k = ob.name.substring(21).trim();
                    } else if (ob.name.toLowerCase().includes("icd10cm attr")) {
                      k = ob.name.substring(12).trim();
                    }
                    if (!rarr.hasOwnProperty(k)) rarr[k] = [];
                    rarr[k].push(ob.value);
                  }
                }
                Object.keys(rarr).forEach((k: any) => {
                  rarr[k] = Array.from(new Set(rarr[k]));
                });
                refineArrs[item.concepts[0].code] = rarr;
              } else if (
                item.codeSystem === "SNOMED" &&
                item.concepts?.length > 0
              ) {
                pb.snomed = item.concepts[0].code;
              }
            });
          }
          arrPros.push(pb);
        }
      }
    });
    let tempArrs: any[] = [];
    if (module === "problem") tempArrs = defaultProblems;
    else tempArrs = defaultDiagnosis;
    arrPros.forEach((item: any) => {
      tempArrs.forEach((obj: any) => {
        if (
          (module === "problem" && item.snomedcode === obj.snomedcode) ||
          (module === "diagnose" && item.icd10code === obj.icd10code)
        ) {
          item.pin = obj.pin;
        }
      });
      probArrs.push(item);
    });
    probArrs = sortProblemsData(probArrs, module);
    let attsArrays: any = {};
    Object.keys(refineArrs).forEach((key: any) => {
      Object.keys(refineArrs[key]).forEach((k: any) => {
        if (!attsArrays.hasOwnProperty(k)) {
          attsArrays[k] = [];
          attsArrays[k] = refineArrs[key][k];
        }
        if (!refineDiagnosisAtts.hasOwnProperty(k)) {
          refineDiagnosisAtts[k] = [];
          refineDiagnosisAtts[k] = refineArrs[key][k];
        } else {
          attsArrays[k] = [...refineArrs[key][k], ...attsArrays[k]];
          refineDiagnosisAtts[k] = Array.from(
            new Set([...refineArrs[key][k], ...refineDiagnosisAtts[k]])
          );
        }
      });
    });

    Object.keys(refineDiagnosisAtts).forEach((k: any) => {
      if (
        refineDiagnosisAtts[k].length === 1 &&
        attsArrays[k].length === arrs.length
      ) {
        delete refineDiagnosisAtts[k];
      }
    });
  }
  return [probArrs, refineDiagnosisAtts, refineArrs];
};

export const SearchDiagnosisData = (datas: any[], searchText: any) => {
  const filteredPros = datas.filter((pdata: any) => {
    return (
      pdata.name.toLowerCase().includes(searchText.toLowerCase()) ||
      pdata.icd10code.toLowerCase().includes(searchText.toLowerCase())
    );
  });
  return filteredPros;
};

export const formatString = (str: string, val: string, formatVal: string) => {
  let searchVal = val.replace(
    //eslint-disable-next-line
    /([\$|\^|\\|\/|\.|\[|\]|\(|\)|\{|\}|\?|\+|\*])/g,
    "\\$1"
  );
  let findVal = new RegExp("(" + searchVal + ")", "ig");
  let highlightValue = str.replace(findVal, formatVal);
  return <span dangerouslySetInnerHTML={{ __html: highlightValue }}></span>;
};

// Review History
export const getReviewHistory = (data: any) => {
  let ret: any = [];
  let reviewHistory: any = [];
  const field: string = "problems_reviewhistory";
  if (data[field] && data[field].data.length > 0) {
    reviewHistory.push(convertArrayToObject(data[field]));
  }

  ret = reviewHistory
    .reduce((prev: any, cur: any) => (prev ? prev.concat(cur) : cur), [])
    .sort((a: any, b: any) => {
      return a.reviewed_ts < b.reviewed_ts ? 1 : -1;
    })
    .map((item: any, index: number) => {
      return {
        id: index,
        status: item.status,
        review_status:
          item.status === "unable to assess"
            ? item.status + ", Reason:" + item.reason
            : item.status,
        reason: item.reason,
        user: item.user,
        reviewed_ts: item.reviewed_ts,
        fmt_reviewed_tm: item.fmt_reviewed_tm,
        last_reviewed:
          item.status !== "reviewed"
            ? "User was " +
              item.status +
              (!!item.reason ? ", Reason:" + item.reason : "") +
              " - By " +
              item.user +
              " at " +
              item.fmt_reviewed_tm
            : "Last reviewed by " + item.user + " at " + item.fmt_reviewed_tm,
        chksum: item.chksum,
      };
    });
  return ret;
};

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

const getFullData = (fdtype: any, rows: any) => {
  let ret: any = {};
  //const keys = ["Diagnoses", "Problems"];
  const key = fdtype === 0 ? "Diagnoses" : "Problems";
  rows.forEach((row: any) => {
    //let key = row.problem;
    if (!ret[key]) ret[key] = [];
    ret[key].push(row);
  });

  // Sort
  Object.keys(ret).forEach((key: string) => {
    ret[key] = ret[key].sort((a: any, b: any) =>
      a.lgtime > b.lgtime ? 1 : -1
    );
  });
  return ret;
};

// Get rows for the main data grid
const getDisplayRows = (category: any, data: any, index: number) => {
  let rows: any = [];
  let gridRows: any = [];
  let allitems: any = [];
  let activeRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];
  const field: string =
    category === "Diagnoses" ? "diagnosis_data" : "problems_data";
  const eh_field: string =
    category === "Diagnoses" ? "diagnosis_edithistory" : "problems_edithistory";
  if (!data[index][eh_field] || data[index][eh_field].data.length === 0) {
    data.forEach((item: any) => {
      if (item[field] && item[field].data.length > 0) {
        allitems.push(convertArrayToObject(item[field]));
      }
    });
  } else if (data[index][field] && data[index][field].data.length > 0) {
    allitems.push(convertArrayToObject(data[index][field]));
  }
  allitems.forEach((item: any) => {
    item.forEach((element: any) => {
      if (
        !rows.some((row: any) => {
          return JSON.stringify(row) === JSON.stringify(element);
        })
      ) {
        rows.push(element);
      }
    });
  });

  // Add id to each row and group them by status
  rows.forEach((row: any) => {
    if (row.status === "Active") {
      activeRows.push({ ...row, id: row.nit });
    } else if (row.status === "Inactive") {
      inactiveRows.push({ ...row, id: row.nit });
    } else {
      errorRows.push({ ...row, id: row.nit });
    }
  });
  // Returns active, followed by inactive and error
  gridRows = activeRows.concat(inactiveRows, errorRows);
  return gridRows;
};

export const getRealDisplayRows = (category: any, data: any, index: number) => {
  let rows = getDisplayRows(category, data, index);
  const newGridRows: any = [];
  rows.forEach((row: any) => {
    newGridRows.push(row);
  });
  return newGridRows;
};

// Parse server data for Problems
export const parseProblemsData = (data: any, index: number) => {
  let datas: any = {};
  let needAutoSave = false;
  let needReload = { value: false };
  let item = data[index];
  if (
    !item.problems_edithistory ||
    item.problems_edithistory.data.length === 0
  ) {
    for (var i = index + 1; i < data.length; i++) {
      const it = data[i];
      if (
        it.problems_data &&
        it.problems_data.data &&
        it.problems_data.data.length > 0
      ) {
        item = it;
        needAutoSave = true;
        break;
      }
    }
  }
  datas.problems_data = getDisplayRows("Problems", data, index);
  datas.diagnosis_data = getDisplayRows("Diagnoses", data, index);
  const newKey = Cci.RunTime.getEncounterInfo().admitkey;
  if (needAutoSave) {
    datas.problems_data.forEach((item: any) => {
      let datasArr: any[] = [];
      let nit = item.nit * -1;
      Object.keys(problemsDBitemsMap).forEach((dbitem: string) => {
        let da: any = {};
        da.jit = problemsDBitemsMap[dbitem];
        da.nit = nit;
        da.ks = newKey;
        da.data = item[dbitem];
        const existrow = datasArr.find(
          (item: any) => item.jit === da.jit && item.nit === da.nit
        );
        if (!existrow) {
          datasArr.push(da);
        }
      });
      const params = {
        group: "pld_problems",
        appname: "UNLOCK",
        fsdata: datasArr,
        perm: "R",
      };
      serverRequest(
        "DBI/savemgdb",
        params,
        () => {
          needReload = { value: true };
        },
        () => {}
      );
    });
  }
  datas.problems_reviewstatus = convertArrayToObject(
    item.problems_reviewstatus
  );

  const perms = item.staff_problems_perm?.data;
  datas.perm138 = "";
  if (perms.length > 0 && perms[0].length > 0) {
    datas.perm138 = perms[0][0];
  }

  const legacyData = item.legacy_problems?.data;
  datas.legacy_data = [];
  if (legacyData.length > 0 && legacyData[0].length > 0) {
    datas.legacy_data = JSON.parse(legacyData[0][0]);
  }

  datas.problems_data.forEach((item: any) => {
    item.data_source = "";
  });

  return [datas, needReload];
};

const isSameHistory = (a: any, b: any) => {
  let isSame = true;
  Object.keys(a).forEach((key) => {
    if (
      key !== "lgtime" &&
      key !== "fmtlgtime" &&
      entryFields.includes(key) &&
      a[key] !== b[key]
    ) {
      isSame = false;
    }
  });
  return isSame;
};

const parseEditHistoryWityType = (dttype: any, data: any) => {
  let columnFields: any = [];
  let globalFields: any = [];
  let problems: any = [];
  let fullData: any = {};

  // Get all rows and columns
  data.forEach((item: any) => {
    const ehData =
      dttype === 0 ? item.diagnosis_edithistory : item.problems_edithistory;
    if (ehData && ehData.data.length > 0) {
      const lgtimeIdx = ehData.header.indexOf("lgtime");
      const lgnameIdx = ehData.header.indexOf("lgname");
      const fmtlgtimeIdx = ehData.header.indexOf("fmtlgtime");
      let time: any = ehData.data[0][lgtimeIdx];
      let name: any = ehData.data[0][lgnameIdx];
      let formattime: any = ehData.data[0][fmtlgtimeIdx];
      columnFields.push({
        time: time,
        user: name,
      });
      globalFields.push({
        time: time,
        user: name,
        formattime: formattime,
      });
      problems.push(convertArrayToObject(ehData));
    }
  });

  columnFields.sort((a: any, b: any) => (a.time > b.time ? 1 : -1));
  globalFields.sort((a: any, b: any) => (a.time > b.time ? 1 : -1));

  fullData = getFullData(
    1,
    problems.reduce(
      (prev: any, cur: any) => (prev ? prev.concat(cur) : cur),
      []
    )
  );

  Object.keys(fullData).forEach((key: string) => {
    let tempArr: any[] = [];
    fullData[key].forEach((record: any) => {
      let exist = false;
      for (let i = 0; i < tempArr.length; i++) {
        if (isSameHistory(tempArr[i], record)) {
          exist = true;
          break;
        }
      }
      if (!exist) tempArr.push(record);
    });
    fullData[key] = tempArr;
  });

  return { fulldata: fullData, global: globalFields };
};

export const parseEditHistory = (data: any) => {
  const diagEH = parseEditHistoryWityType(0, data);
  const probEH = parseEditHistoryWityType(1, data);
  return {
    diagnoses: diagEH.fulldata,
    diagFields: diagEH.global,
    problems: probEH.fulldata,
    global: probEH.global,
  };
};

export const getSaveData = (category: any, rowData: any) => {
  let saveData: any = [];
  const curEntryFields =
    category === "Diagnoses"
      ? [...entryFields, "probid"]
      : [...entryFields, "diagid"];
  // Copy entry fields before sending to the server
  const newKey = Cci.RunTime.getEncounterInfo().admitkey;
  rowData.forEach((entry: any) => {
    Object.keys(entry).forEach((field) => {
      if (curEntryFields.includes(field)) {
        const curjit =
          category === "Diagnoses"
            ? diagnosesDBitemsMap[field]
            : problemsDBitemsMap[field];
        const curnit = entry["nit"];
        const existrow = saveData.find(
          (item: any) => item.jit === curjit && item.nit === curnit
        );
        if (!existrow && entry.data_source !== "VISTA") {
          saveData.push({
            data: entry[field],
            jit: curjit,
            nit: curnit,
            ks: Number(curnit) === -1 ? newKey : entry["key"],
          });
        }
      }
    });
  });
  return saveData;
};

export const getUndoSaveData = (category: any, rowData: any, undoData: any) => {
  let diffRow: any = {};
  const curEntryFields =
    category === "Diagnoses"
      ? [...entryFields, "probid"]
      : [...entryFields, "diagid"];
  //undo has diffrent row
  const diffUndoRow = undoData.filter(
    (undoRow: any) =>
      !rowData.some(
        (row: any) => JSON.stringify(undoRow) === JSON.stringify(row)
      )
  );
  if (diffUndoRow && diffUndoRow.length > 0) {
    diffRow = diffUndoRow[0];
  } else {
    let rowD = rowData.filter(
      (row: any) =>
        !undoData.some(
          (undoRow: any) => JSON.stringify(undoRow) === JSON.stringify(row)
        )
    );
    if (rowD && rowD.length > 0) {
      diffRow = rowD[0];
      curEntryFields.forEach((field: any) => {
        diffRow[field] = "";
      });
    }
  }
  let saveData: any = [];
  // Copy entry fields before sending to the server
  Object.keys(diffRow).forEach((field) => {
    if (curEntryFields.includes(field)) {
      saveData.push({
        data: diffRow[field],
        jit:
          category === "Diagnoses"
            ? diagnosesDBitemsMap[field]
            : problemsDBitemsMap[field],
        nit: diffRow["nit"],
        ks: diffRow["key"],
      });
    }
  });
  return saveData;
};

export const ymd2dmy = (row: any, field: any) => {
  if (!row[field]) return "";
  return row[field];
};
