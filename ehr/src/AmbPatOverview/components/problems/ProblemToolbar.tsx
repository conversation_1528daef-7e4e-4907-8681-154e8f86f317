/**
 * ProblemToolbar.tsx
 *
 * @author: RnD
 * @description Toolbar for Problem
 */

import { useAtomValue } from "jotai";
import { Grid, Typography } from "@mui/material";

import { isShowInActiveProblemsAtom } from "@cci-monorepo/common/context/atoms/problem";
import { problemsPageSize } from "../common/Constants";
import { useMemo } from "react";
import {
  pageProbAtom,
  rowDataProblemsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";
export const ProblemToolbar = () => {
  const rowData = useAtomValue(rowDataProblemsAtom);
  const isShowInactive = useAtomValue(isShowInActiveProblemsAtom);
  const page = useAtomValue(pageProbAtom);

  const rowsCount = useMemo(() => {
    if (isShowInactive) {
      return rowData.length;
    } else {
      return rowData.filter((data: { [key: string]: any }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowData]);

  const countStr = useMemo(() => {
    const count = page * problemsPageSize;
    if (count !== undefined && rowsCount !== undefined) {
      if (count >= rowsCount) {
        return `(${rowsCount})`;
      } else {
        return `(${count} of ${rowsCount})`;
      }
    } else {
      return "";
    }
  }, [page, rowsCount]);

  return (
    <>
      <Grid
        container
        spacing={"8px"}
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        sx={{
          marginTop: "-8px",
          opacity: 1,
        }}
      >
        <Grid item sx={{ paddingTop: "6px" }}>
          <Typography
            sx={{
              fontWeight: "bold",
              paddingTop: "0px",
              paddingRight: "10px",
            }}
          >
            Problems {countStr}
          </Typography>
        </Grid>
      </Grid>
    </>
  );
};
