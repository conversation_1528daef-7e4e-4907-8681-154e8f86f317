/**
 * ProblemsGrid.tsx
 *
 * @author:
 * @description common component Problems data grid pro
 */
import * as React from "react";
import { Box } from "@mui/material";
import {
  GridActionsCellItem,
  GridEditDateCellProps,
  GridEditInputCellProps,
  GridRenderCellParams,
  GridRowEditStopParams,
  GridRowParams,
  GridCellParams,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { ProblemsRowHeight, problemsPageSize } from "../common/Constants";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { isEditableAtom, showInputErrorAtom } from "../../context/CommonAtoms";

import { randomId } from "@mui/x-data-grid-generator";
import {
  isShowInActiveProblemsAtom,
  selectedRowProblemsAtom,
  rowSelectionModelProblemsAtom,
  needSaveProblems<PERSON>tom,
  needReloadProblemsAtom,
  searchText<PERSON>tom,
  setCommonAtts<PERSON>tom,
  selected<PERSON>ow<PERSON>tom,
  promoteProblemRow<PERSON>tom,
  needSaveDiagnosesAtom,
  selectedRowDiagnosisAtom,
  loading<PERSON>tom,
  staffPerm138<PERSON>tom,
  isLegacyProblemSelectedAtom,
} from "@cci-monorepo/common/context/atoms/problem";
import { DataGridAccordion } from "../common/DataGridAccordion";
import { dateComparator, nullorblank } from "@cci-monorepo/common";
import { serverRequest } from "../../utils/DataRequests";
import { UpArrowIcon, PromoteIcon } from "../../../common/assets";
import { DateField } from "../common/DateField";
import { getSaveData } from "./ProblemsUtils";
import { NameFieldCell } from "./NameFieldCell";
import { StatusFieldCell } from "./StatusFieldCell";
import { NoteFieldCell } from "./NoteFieldCell";
import { toPldSelectRow } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { useSetErrorDialog, useSetToastFamily } from "@cci-monorepo/common";
import CommonRenderCell from "../common/CommonRenderCell";
import { StyledNameTooltip, USERCONTEXT } from "../common/Constants";
import { AdditionalFooter } from "../common/AdditionalFooter";
import { searchProblems } from "@cci-monorepo/common/utils/problem/ProblemsDataAPI";
import {
  rowDataDiagnosisAtom,
  rowDataProblemsAtom,
  rowModesModelProblemAtom,
  openProblemDialogAtom,
  pageProbAtom,
  displayCountProbAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";
export const ProblemsGrid = ({ userContext }: any) => {
  const perm138 = useAtomValue(staffPerm138Atom);
  const isEditable = useAtomValue(isEditableAtom) && perm138 === "1";
  const isReadOnly = !isEditable || userContext === USERCONTEXT.NURSE;
  const [rowData, setRowData] = useAtom(rowDataProblemsAtom);
  const [dRowData, setDRowData] = useAtom(rowDataDiagnosisAtom);
  const isShowInActive = useAtomValue(isShowInActiveProblemsAtom);
  const [rowModesModel, setRowModesModel] = useAtom(rowModesModelProblemAtom);
  const [selectedRow, setSelectedRow] = useAtom(selectedRowProblemsAtom);
  const setSelectedRowDiag = useSetAtom(selectedRowDiagnosisAtom);
  const setLegacyProblemSelected = useSetAtom(isLegacyProblemSelectedAtom);
  const [rowSelectionModel, setRowSelectionModel] = useAtom(
    rowSelectionModelProblemsAtom
  );
  const [needSave, setNeedSave] = useAtom(needSaveProblemsAtom);
  const setDiagnosisNeedSave = useSetAtom(needSaveDiagnosesAtom);
  const setNeedReload = useSetAtom(needReloadProblemsAtom);
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setShowInputError = useSetAtom(showInputErrorAtom);
  const setOpenErrorDialog = useSetErrorDialog();
  const setSearchTextAtom = useSetAtom(searchTextAtom);
  const [openProblemDialog, setOpenProblemDialog] = useAtom(
    openProblemDialogAtom
  );
  const setCommonAtts = useSetAtom(setCommonAttsAtom);
  const setSelectedClickRow = useSetAtom(selectedRowAtom);
  const setPromoteProblemRow = useSetAtom(promoteProblemRowAtom);
  const setLoading = useSetAtom(loadingAtom);
  const [pageProb, setPageProb] = useAtom(pageProbAtom);
  const setDisplayCountProb = useSetAtom(displayCountProbAtom);
  const [needLoadMask, setNeedLoadMask] = React.useState(false);
  const apiRef = useGridApiRef();

  const displayRows: Array<{ [key: string]: any }> = React.useMemo(() => {
    if (!isShowInActive) {
      return rowData.filter((row: { [key: string]: any }) => {
        return String(row.status).toUpperCase() === "ACTIVE";
      });
    } else {
      return rowData;
    }
  }, [isShowInActive, rowData]);

  const displayData = React.useMemo(() => {
    return displayRows?.length >= problemsPageSize * pageProb
      ? displayRows?.slice(0, problemsPageSize * pageProb)
      : displayRows;
  }, [displayRows, pageProb]);

  React.useEffect(() => {
    setDisplayCountProb(displayData?.length);
  }, [displayData, setDisplayCountProb]);

  const promoteToDiagnosis = React.useCallback(
    (params: any) => {
      setNeedLoadMask(true);
      let codestr = params.row.icd10code;
      let codeArrs = codestr.split(",");
      let code = codestr.replace(/ +/g, "");
      let searchField = code;
      const param = {
        searchField: searchField,
      };
      searchProblems(param)
        .then((resp: any) => {
          setNeedLoadMask(false);
          if (codeArrs.length > 1) {
            params.row.refine = 2;
            setSelectedClickRow(params);
            setPromoteProblemRow(params.row);
            setCommonAtts(resp.matches);
            setOpenProblemDialog({
              open: true,
              module: "diagnose",
              clickNameCell: false,
              clickCancel: false,
            });
          } else {
            if (resp?.matches?.length > 0) {
              let obj =
                resp?.matches[0]?.codeSystemResults[0]?.concepts[0]?.attributes;
              let codes = "",
                num = 0;
              obj.forEach((att: any) => {
                if (
                  att.type === "Relation Attribute" &&
                  att.name === "ICD10CM Has Clinical Refinement"
                ) {
                  if (num > 0) codes += " ";
                  codes += att.properties[0]?.value;
                  num++;
                }
              });
              if (!nullorblank(codes)) {
                params.row.refine = 1;
                setSelectedClickRow(params);
                setPromoteProblemRow(params.row);
                searchField = codes;
                const param = {
                  searchField: searchField,
                };
                setNeedLoadMask(true);
                searchProblems(param)
                  .then((res: any) => {
                    setNeedLoadMask(false);
                    setCommonAtts(res.matches);
                    setOpenProblemDialog({
                      open: true,
                      module: "diagnose",
                      clickNameCell: false,
                      clickCancel: false,
                    });
                  })
                  .catch(() => {
                    setNeedLoadMask(false);
                  });
              } else {
                //add directly
                params.row.refine = 1;
                setSelectedClickRow(params);
                setPromoteProblemRow(params.row);
                setLoading(false);
                const id = `new_${randomId()}`;
                const emptyRow = {
                  id: id,
                  key: "0",
                  name: params.row.name,
                  nit: -1,
                  status: params.row.status,
                  statusval: params.row.statusval,
                  onsetdate: params.row.onsetdate,
                  resolveddate: params.row.resolveddate,
                  snomedcode: params.row.snomedcode,
                  icd10code: params.row.icd10code,
                  refine: 0,
                  comments: params.row.comments,
                  probid: params.row.nit,
                  // prob_nit: params.row.probid,(add problem nit)
                };
                const updatedRowData: any = [emptyRow, ...dRowData];
                setDRowData(updatedRowData);
                setDiagnosisNeedSave({
                  value: true,
                  action: "copy",
                  rowId: -1,
                  data: [],
                });
              }
            } else {
              setNeedLoadMask(false);
              setOpenErrorDialog({
                text: "Unable to search the problem",
                open: true,
              });
            }
          }
        })
        .catch((error: any) => {
          setNeedLoadMask(false);
          setOpenErrorDialog({
            text: "Unknown error occurs while searching problem",
            open: true,
          });
        });
    },
    [
      dRowData,
      setCommonAtts,
      setDRowData,
      setDiagnosisNeedSave,
      setLoading,
      setOpenErrorDialog,
      setOpenProblemDialog,
      setPromoteProblemRow,
      setSelectedClickRow,
    ]
  );

  function renderProblemEditCell(params: GridEditInputCellProps) {
    return <NameFieldCell {...params} disabled={params.row.nit !== "-1"} />;
  }

  function renderResolvedDateEditCell(params: GridEditDateCellProps) {
    return (
      <DateField
        {...params}
        disableFuture={true}
        disabled={params.row.nit !== "-1"}
      />
    );
  }
  const columns = React.useMemo(
    () => [
      {
        field: "pinned",
        headerAlign: "center",
        headerName: "",
        align: "center",
        width: 60,
        type: "actions",
        getActions: (params: any) =>
          params.row.data_source === "VISTA"
            ? []
            : [
                isReadOnly ? (
                  <></>
                ) : params.row.diagid !== "" || params.row.promoted === "1" ? (
                  <GridActionsCellItem
                    sx={{
                      padding: "4px 8px 4px 0px",
                    }}
                    icon={<PromoteIcon />}
                    label="promoted"
                    title="Promoted"
                  />
                ) : (
                  <GridActionsCellItem
                    sx={{ padding: "4px 8px 4px 0px" }}
                    icon={
                      <UpArrowIcon
                        style={{
                          visibility:
                            cci.cfg.hideDiagnosis === "1"
                              ? "hidden"
                              : "visible",
                        }}
                      />
                    }
                    disabled={
                      params.row.diagid !== "" || params.row.promoted === "1"
                        ? true
                        : false
                    }
                    label={cci.cfg.hideDiagnosis === "0" ? "promote" : ""}
                    title={
                      cci.cfg.hideDiagnosis === "0"
                        ? "Promote Problem to this Encounter"
                        : ""
                    }
                    onClick={() => {
                      if (cci.cfg.hideDiagnosis === "0") {
                        promoteToDiagnosis(params);
                      }
                    }}
                  />
                ),
              ],
      },
      {
        field: "status",
        headerName: "Status",
        width: 120,
        editable: !isReadOnly,
        renderEditCell: (params: any) => <StatusFieldCell {...params} />,
        type: "singleSelect",
        valueOptions: ["Active", "Inactive", "Error"],
        renderCell: CommonRenderCell,
      },
      {
        field: "name",
        headerName: "Problem",
        minWidth: 150,
        disableColumnMenu: true,
        editable: !isReadOnly,
        renderEditCell: renderProblemEditCell,
        renderCell: (params: GridRenderCellParams<any>) => (
          <div
            style={{
              font: "normal 400 15px Roboto",
              overflow: "hidden",
              textWrap: "nowrap",
              lineHeight: 1,
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              fontStyle: params.row.status === "Inactive" ? "italic" : "normal",
              textDecoration:
                params.row.status === "Error" ? "line-through" : "none",
            }}
          >
            {params.value}
          </div>
        ),
      },
      {
        field: "onsetdate",
        headerName: "Onset Date",
        width: 130,
        disableColumnMenu: true,
        editable: !isReadOnly,
        type: "string",
        sortComparator: dateComparator,
        renderEditCell: (params: any) => (
          <DateField {...params} disableFuture={true} autoPopup={true} />
        ),
        renderCell: CommonRenderCell,
      },
      {
        field: "resolveddate",
        headerName: "Resolution Date",
        width: 160,
        disableColumnMenu: true,
        editable: !isReadOnly,
        sortComparator: dateComparator,
        renderEditCell: renderResolvedDateEditCell,
        type: "string",
        renderCell: CommonRenderCell,
      },
      {
        field: "comments",
        headerName: "Comments",
        disableColumnMenu: true,
        editable: !isReadOnly,
        minWidth: 130,
        renderEditCell: (params: any) => (
          <NoteFieldCell type="Problem" {...params} />
        ),
        renderCell: (params: GridRenderCellParams<any>) => {
          const cmtlines =
            params.row.data_source === "VISTA"
              ? params.row.comments.split(/[;]+/)
              : [params.row.comments];
          return (
            <StyledNameTooltip
              title={cmtlines.map((line: any, index: number) =>
                index === cmtlines.length - 1 ? (
                  <React.Fragment key={index}>{line}</React.Fragment>
                ) : (
                  <React.Fragment key={index}>
                    {line}
                    <br />
                  </React.Fragment>
                )
              )}
            >
              <div
                style={{
                  font: "normal 400 15px Roboto",
                  color: "#000",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  fontStyle:
                    params.row.status === "Inactive" ? "italic" : "normal",
                  textDecoration:
                    params.row.status === "Error" ? "line-through" : "none",
                }}
              >
                {params.value}
              </div>
            </StyledNameTooltip>
          );
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [promoteToDiagnosis]
  );

  const handleRowUpdate = (newRow: any, oldRow: any) => {
    if (!newRow || !oldRow) return;
    // Stop exiting edit mode when one of of these fields are empty
    let isInvalidDate =
      newRow.onsetdate === "Invalid Date" ||
      newRow.resolveddate === "Invalid Date";
    if (!newRow.status || !newRow.name || isInvalidDate) {
      setShowInputError(true);
      throw new Error(
        isInvalidDate ? "Invalid date format." : "Required fields empty."
      );
    } else {
      if (
        JSON.stringify(newRow) !== JSON.stringify(oldRow) ||
        Number(newRow.nit) === -1
      ) {
        // Update rowData with updated data from newRow
        const updatedRowData: any = rowData.map((entry: any) => {
          if (entry.nit === newRow.nit) {
            Object.keys(entry).forEach((field) => {
              entry[field] = newRow[field];
            });
          }
          return entry;
        });
        setRowData(updatedRowData);
        setNeedSave({
          value: true,
          action: newRow.nit.toString() === "-1" ? "added" : "updated",
          rowId: newRow.nit,
          data: [],
        });
      }
    }
    return newRow;
  };
  const handleCellClick = (params: GridCellParams) => {
    if (params.field === "name" && Number(params.row.nit) === -1) {
      setOpenProblemDialog({
        open: true,
        module: "problem",
        clickNameCell: true,
        clickCancel: false,
      });
      setSearchTextAtom(params.row.name);
    }
  };
  const handleRowClick = (params: GridRowParams) => {
    setLegacyProblemSelected(params.row["data_source"] === "VISTA");
    if (params.row["diagid"] !== "") {
      const relatedRow: any = (dRowData as []).find(
        (row: any) => row.nit === params.row["diagid"]
      );
      if (relatedRow) {
        setSelectedRowDiag(relatedRow);
      }
    } else if (params.row["promoted"] === "1") {
      const relatedRow: any = (dRowData as []).find(
        (row: any) => row["probid"] === params.row["nit"]
      );
      if (relatedRow) {
        setSelectedRowDiag(relatedRow);
      }
    }
    setSelectedRow(params.row);
  };

  const autosizeOptions = {
    columns: ["name"],
    includeHeaders: true,
    includeOutliers: true,
  };

  React.useEffect(() => {
    setTimeout(() => {
      apiRef.current?.autosizeColumns(autosizeOptions);
    }, 100);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShowInActive]);

  React.useEffect(() => {
    apiRef.current.autosizeColumns &&
      apiRef.current.autosizeColumns(autosizeOptions);
  });

  React.useEffect(() => {
    if (selectedRow && selectedRow["id"] != null) {
      setRowSelectionModel([selectedRow["id"]]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRow]);

  // Save Problems data to the server
  React.useEffect(() => {
    if (needSave.value) {
      const updatingRow: any = (rowData as []).find(
        (row: any) => row.nit === needSave.rowId
      );
      setNeedSave({ value: false, action: "", data: [] });

      const onSuccess = (result: any) => {
        if (result) {
          if (needSave.action !== "") {
            if (needSave.action === "added") {
              let params = {
                time: Math.round(Date.now() / 1000),
                name: updatingRow.name,
                snomed: updatingRow.snomedcode,
                icd10: updatingRow.icd10code,
                frequent: 1,
                pin: updatingRow.pin,
                sid: Cci.util.Staff.getSid(),
                envid: Cci.Env.getEnvID(),
                campus: cci.cfg.campus,
                isProblems: true,
              };
              serverRequest(
                "pld/problems/savePinFrenquency",
                params,
                () => {},
                () => {}
              );

              const reviewparams = {
                savedata: JSON.stringify({
                  status: "reviewed",
                  chksum:
                    "3c9c7fbafb469b59ad34d9024dd0073bd8e9bb12e900eacdf1e26408a1db2d47",
                }),
              };
              serverRequest(
                "pld/problems/reviewed",
                reviewparams,
                () => {},
                () => {}
              );
            }
            setNeedReload({
              value: true,
              hlRow: updatingRow,
              saveUndo: true,
              category: "Problems",
              dataFromID:
                needSave.action === "copy" ? updatingRow["diagid"] : "",
            });
            let msg: any = `Problem ${needSave.action}!`;
            if (needSave.action === "copy") {
              msg = "Diagnosis added to Problems list!";
            }
            setShowToast({
              type: "success",
              text: msg,
              open: true,
            });
            setRowSelectionModel([]);
          }
        }
      };

      const onFailure = (error: string) => {
        setNeedReload({ value: true, hlRow: updatingRow });
        setOpenErrorDialog({
          text: `System has failed to update problem: ${error}`,
          open: true,
        });
      };

      if (updatingRow && updatingRow["nit"]) {
        let datasArr: any[] = getSaveData("Problems", rowData);
        const params = {
          group: "pld_problems",
          appname: "UNLOCK",
          fsdata: datasArr,
          perm: "R",
        };
        serverRequest("DBI/savemgdb", params, onSuccess, onFailure);
      }
    }
  }, [
    needSave.action,
    needSave.rowId,
    needSave.value,
    rowData,
    setNeedReload,
    setNeedSave,
    setOpenErrorDialog,
    setShowToast,
    setRowSelectionModel,
  ]);

  return (
    <>
      <Box
        style={{
          width: "100%",
        }}
      >
        <DataGridAccordion
          apiRef={apiRef}
          type={"PROBLEMS"}
          rowHeight={ProblemsRowHeight}
          rows={displayData}
          columns={columns}
          displayMsg={"No active problems"}
          enableStatusColFilter={true}
          autosizeOptions={autosizeOptions}
          enableSummary={false}
          loading={needLoadMask}
          onRowUpdate={handleRowUpdate}
          onRowClick={handleRowClick}
          onRowDoubleClick={(params: any) => {
            toPldSelectRow("Problems", {
              gridType: "problems",
              lgtime: params.row.lgtime,
            });
          }}
          onCellClick={handleCellClick}
          sortingOrder={["asc", "desc"]}
          rowModesModel={rowModesModel}
          setRowModesModel={setRowModesModel}
          selectedRow={selectedRow}
          setSelectedRow={setSelectedRow}
          rowSelectionModel={rowSelectionModel}
          setRowSelectionModel={setRowSelectionModel}
          isCellEditable={(params: any) =>
            params.row.data_source !== "VISTA" && isEditable === true
          }
          onRowEditStop={(params: GridRowEditStopParams, event: any) => {
            if (
              openProblemDialog.open ||
              (openProblemDialog.clickCancel && openProblemDialog.clickNameCell)
            ) {
              event.defaultMuiPrevented = true;
            }
          }}
          columnVisibilityModel={{
            status: isShowInActive,
          }}
        />
        {displayRows.length > problemsPageSize * pageProb && (
          <AdditionalFooter
            msg="Load More"
            handleClick={() => setPageProb((preVal) => preVal + 1)}
          />
        )}
      </Box>
    </>
  );
};
