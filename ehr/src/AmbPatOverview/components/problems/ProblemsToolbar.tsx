/**
 * ProblemsToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Problems and Diagnosis
 */
import { useAtom, useAtomValue, useSetAtom } from "jotai";

import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink, IconEye, AddBlueIcon } from "../../../common/assets";
import {
  isShowInActiveProblemsAtom,
  staffPerm138Atom,
} from "@cci-monorepo/common/context/atoms/problem";
import {
  rowSelectionModelProblemsAtom,
  rowSelectionModelDiagnosisAtom,
} from "@cci-monorepo/common/context/atoms/problem";
import { editModuleAtom } from "../../context/CommonAtoms";
import { useMemo } from "react";
import { openProblemDialogAtom } from "@cci-monorepo/Pld/context/ProblemsAtoms"; // editComponent is actually Pld Problems
import {
  rowDataDiagnosisAtom,
  rowDataProblemsAtom,
  displayCountDiagAtom,
  displayCountProbAtom,
  hasReviewedProblemsAtom,
  reviewMessageProblemsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";
export const ProblemsToolbar = (props: any) => {
  const perm138 = useAtomValue(staffPerm138Atom);
  const hasReviewed = useAtomValue(hasReviewedProblemsAtom);
  const [isShowInactive, setIsShowInactive] = useAtom(
    isShowInActiveProblemsAtom
  );
  const rowDataProb = useAtomValue(rowDataProblemsAtom);
  const rowDataDiag = useAtomValue(rowDataDiagnosisAtom);
  const setOpenProblemDialog = useSetAtom(openProblemDialogAtom);
  const setRowSelectionModel = useSetAtom(rowSelectionModelProblemsAtom);
  const setRowSelectionModelDiagnosis = useSetAtom(
    rowSelectionModelDiagnosisAtom
  );
  const reviewMsg = useAtomValue(reviewMessageProblemsAtom);
  const displayCountProb = useAtomValue(displayCountProbAtom);
  const displayCountDiag = useAtomValue(displayCountDiagAtom);
  const setEditModule = useSetAtom(editModuleAtom);

  const rowsCountProb = useMemo(() => {
    if (isShowInactive) {
      return rowDataProb.length;
    } else {
      return rowDataProb.filter((data: { [key: string]: any }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowDataProb]);

  const rowsCountDiag = useMemo(() => {
    if (isShowInactive) {
      return rowDataDiag.length;
    } else {
      return rowDataDiag.filter((data: { [key: string]: any }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowDataDiag]);

  const handleRedirect = () => {
    toPldModule("Problems");
  };

  const handleShowInactiveClick = () => {
    setIsShowInactive(!isShowInactive);
  };

  const handleAddDiag = () => {
    setEditModule("Problems & Diagnosis");
    setOpenProblemDialog({
      open: true,
      module: "diagnose",
      clickNameCell: false,
      clickCancel: false,
    });
    setRowSelectionModelDiagnosis([]);
  };

  const handleAddProb = () => {
    setEditModule("Problems & Diagnosis");
    setOpenProblemDialog({
      open: true,
      module: "problem",
      clickNameCell: false,
      clickCancel: false,
    });
    setRowSelectionModel([]);
  };

  const menuItems: MenuItemProps[] = [
    ...(props.readonly
      ? []
      : [
          {
            label: "View In Health Profile",
            icon: <IconExternalLink />,
            handler: handleRedirect,
          },
        ]),
    {
      label: `${isShowInactive ? "Hide" : "Show"} Inactive Items`,
      icon: <IconEye />,
      handler: handleShowInactiveClick,
    },
    {
      label: "Add Diagnosis",
      icon: <AddBlueIcon />,
      handler: handleAddDiag,
      hidden: props.readonly,
    },
    {
      label: "Add Problem",
      icon: <AddBlueIcon />,
      handler: handleAddProb,
      hidden: props.readonly,
    },
  ];

  return (
    <CommonToolbar
      name="Problems & Diagnosis"
      isPldWidget={true}
      isCritical={false}
      hasReviewed={hasReviewed}
      reviewMsg={
        hasReviewed
          ? reviewMsg[0].last_reviewed
          : "Problems & Diagnosis Not Reviewed"
      }
      count={displayCountProb + displayCountDiag}
      totalCount={rowsCountProb + rowsCountDiag}
      menuItems={perm138 === "1" ? menuItems : menuItems.slice(0, 3)}
    />
  );
};
