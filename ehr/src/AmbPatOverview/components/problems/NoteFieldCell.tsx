/**
 * NoteFieldCell.tsx
 *
 * @author: RnD
 * @description Render comments field
 */

import { TrashIcon } from "@cci/mui-components";
import { IconButton, TextField } from "@mui/material";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import { useAtom } from "jotai";
import {
  rowDataDiagnosisAtom,
  rowDataProblemsAtom,
  rowModesModelProblemAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";
export const NoteFieldCell = (params: GridRenderCellParams<any>) => {
  const { id, api, field, row } = params;
  const oparams = params as any;
  const keys = Object.keys(oparams);
  const type = keys.includes("type") ? oparams["type"] : "";

  const [rowData, setRowData] = useAtom(rowDataProblemsAtom);
  const [rowDataDiag, setRowDataDiag] = useAtom(rowDataDiagnosisAtom);

  const handleDeleteRow = () => {
    if (type === "Problem") {
      const updatedRowData = rowData.filter((row: any) => row.id !== id);
      setRowData(updatedRowData);
    } else if (type === "Diagnosis") {
      const updatedRowData = rowDataDiag.filter((row: any) => row.id !== id);
      setRowDataDiag(updatedRowData);
    }
  };

  return (
    <>
      <TextField
        placeholder="Enter a Comment"
        defaultValue={row.comments}
        onChange={(
          event: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
        ) => {
          api.setEditCellValue({ id, field, value: event.target.value });
        }}
        sx={{
          "& .MuiOutlinedInput-root": {
            height: "100%",
            background: "#FFF",
          },
          width: "calc(100% - 6px)",
          height: "calc(100% - 4px)",
          marginLeft: "3px",
          border: "solid 1px",
          borderRadius: 4,
          borderColor: "lightgray !important",
        }}
        variant="outlined"
      />

      {id.toString().startsWith("new") && (
        <IconButton
          disableFocusRipple
          disableRipple
          color="inherit"
          component="label"
          onClick={() => handleDeleteRow()}
        >
          <TrashIcon />
        </IconButton>
      )}
    </>
  );
};
