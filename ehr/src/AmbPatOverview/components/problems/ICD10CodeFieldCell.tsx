/**
 * ICD10CodeFieldCell.tsx
 *
 * @author: RnD
 * @description Render icd10 code field
 */
import { TextField } from "@mui/material";
import { GridRenderEditCellParams } from "@mui/x-data-grid-pro";

export const ICD10CodeFieldCell = (params: GridRenderEditCellParams<any>) => {
  const { id, api, field, row, disabled } = params;
  return (
    <>
      <TextField
        placeholder=""
        defaultValue={row.icd10code}
        disabled={disabled}
        onChange={(
          event: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
        ) => {
          if (!disabled) {
            api.setEditCellValue({ id, field, value: event.target.value });
          }
        }}
        sx={{
          "& .MuiOutlinedInput-root": {
            height: "100%",
            background: disabled ? "#EBEBEB" : "#FFF",
          },
          width: "calc(100% - 6px)",
          height: "calc(100% - 4px)",
          marginLeft: "3px",
          border: "solid 1px",
          borderRadius: 4,
          borderColor: "lightgray!important",
        }}
        variant="outlined"
      />
    </>
  );
};
