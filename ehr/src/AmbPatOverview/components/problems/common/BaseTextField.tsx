import { TextField } from "@mui/material";
import { styled } from "@mui/material/styles";

export const BaseTextField = styled(TextField, {
  shouldForwardProp: (prop) => prop !== "height",
})((props: any) => ({
  background: "white",
  fontFamily: "Roboto",
  fontSize: props.fontSize ?? "16px",
  height: props.height ?? 32,
  width: props.width ?? 1348,
  border: 0,
  borderRadius: 4,
  "& .MuiInputBase-root": {
    fontSize: "inherit",
    fontFamily: "inherit",
    padding: "0px 6px",
  },
  "& .MuiOutlinedInput-input": {
    color: "black",
    overflow: "hidden",
    textOverflow: "ellipsis",
    font: "inherit",
    padding: "0px 6px 0px 0px",
    height: props.height ?? 40,
    width: props.width ?? 1340,
    border: 0,
    "&::placeholder": {
      color: "#a9a9a9",
      opacity: 1,
    },
  },
  "& .MuiFormLabel-root": {
    color: "#A9A9A9",
  },
  "& .Mui-disabled": {
    background: "#E2E3E4",
    color: "#3D4C51",
  },
  "& .MuiOutlinedInput-notchedOutline": {
    borderRadius: 4,
  },
  "& .MuiOutlinedInput-root": {
    "&.Mui-focused fieldset, &:hover .MuiOutlinedInput-notchedOutline": {
      border: "1px solid #77b2df",
    },
    "&.Mui-error": {
      "& .MuiOutlinedInput-notchedOutline": {
        border: "1px solid #cf4c35",
      },
    },
  },
  "& .MuiOutlinedInput-inputMarginDense": {
    padding: 0,
  },
}));
