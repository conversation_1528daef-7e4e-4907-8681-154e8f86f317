/**
 * DiagnoseGrid.tsx
 *
 * @author:
 * @description common component Diagnoses data grid pro
 */
import * as React from "react";
import { Box } from "@mui/material";
import {
  GridActionsCellItem,
  GridEditInputCellProps,
  GridRenderCellParams,
  GridRowEditStopParams,
  GridRowParams,
  GridCellParams,
  useGridApiRef,
} from "@mui/x-data-grid-pro";

import {
  ProblemsRowHeight,
  diagnosesPageSize,
  USERCONTEXT,
} from "../common/Constants";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { isEditableAtom, showInputErrorAtom } from "../../context/CommonAtoms";
import {
  selectedRowDiagnosisAtom,
  rowSelectionModelDiagnosisAtom,
  needSaveDiagnosesAtom,
  needReloadProblemsAtom,
  promoteProblemRowAtom,
  needSaveProblemsAtom,
  selectedRowProblemsAtom,
  searchTextAtom,
  isShowInActiveProblemsAtom,
} from "@cci-monorepo/common/context/atoms/problem";
import { randomId } from "@mui/x-data-grid-generator";
import { DataGridAccordion } from "../common/DataGridAccordion";
import { serverRequest } from "../../utils/DataRequests";
import { DownArrowIcon } from "../../../common/assets";
import { DateField } from "../common/DateField";
import { getSaveData } from "./ProblemsUtils";
import { NameFieldCell } from "./NameFieldCell";
import { StatusFieldCell } from "./StatusFieldCell";
import { NoteFieldCell } from "./NoteFieldCell";
import { ICD10CodeFieldCell } from "./ICD10CodeFieldCell";
import { toPldSelectRow } from "@cci-monorepo/AmbPatOverview/utils/utils";
import {
  dateComparator,
  useSetErrorDialog,
  useSetToastFamily,
} from "@cci-monorepo/common";
import CommonRenderCell from "../common/CommonRenderCell";
import { AdditionalFooter } from "../common/AdditionalFooter";
import {
  displayCountDiagAtom,
  openProblemDialogAtom,
  pageDiagAtom,
  rowDataDiagnosisAtom,
  rowDataProblemsAtom,
  rowModesModelDiagnosisAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";

export const DiagnoseGrid = ({ userContext }: any) => {
  const isEditable = useAtomValue(isEditableAtom);
  const isReadOnly = !isEditable || userContext === USERCONTEXT.NURSE;
  const [rowData, setRowData] = useAtom(rowDataDiagnosisAtom);
  const [pRowData, setProRowData] = useAtom(rowDataProblemsAtom);
  const isShowInactive = useAtomValue(isShowInActiveProblemsAtom);
  const [rowModesModel, setRowModesModel] = useAtom(rowModesModelDiagnosisAtom);
  const [selectedRow, setSelectedRow] = useAtom(selectedRowDiagnosisAtom);
  const setSelectedRowProb = useSetAtom(selectedRowProblemsAtom);
  const [openProblemDialog, setOpenProblemDialog] = useAtom(
    openProblemDialogAtom
  );
  const [rowSelectionModel, setRowSelectionModel] = useAtom(
    rowSelectionModelDiagnosisAtom
  );
  const [needSave, setNeedSave] = useAtom(needSaveDiagnosesAtom);
  const setProNeedSave = useSetAtom(needSaveProblemsAtom);
  const setNeedReload = useSetAtom(needReloadProblemsAtom);
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setShowInputError = useSetAtom(showInputErrorAtom);
  const setOpenErrorDialog = useSetErrorDialog();
  const setSearchTextAtom = useSetAtom(searchTextAtom);
  const [promoteProblem, setPromoteProblemRow] = useAtom(promoteProblemRowAtom);
  const [pageDiag, setPageDiag] = useAtom(pageDiagAtom);
  const setDisplayCountDiag = useSetAtom(displayCountDiagAtom);
  const apiRef = useGridApiRef();

  const displayRows: Array<{ [key: string]: any }> = React.useMemo(() => {
    if (!isShowInactive) {
      return rowData.filter((row: { [key: string]: any }) => {
        return String(row.status).toUpperCase() === "ACTIVE";
      });
    } else {
      return rowData;
    }
  }, [isShowInactive, rowData]);

  const displayData = React.useMemo(() => {
    return displayRows?.length >= diagnosesPageSize * pageDiag
      ? displayRows?.slice(0, diagnosesPageSize * pageDiag)
      : displayRows;
  }, [displayRows, pageDiag]);

  React.useEffect(() => {
    setDisplayCountDiag(displayData?.length);
  }, [displayData, setDisplayCountDiag]);

  function renderProblemEditCell(params: GridEditInputCellProps) {
    return <NameFieldCell {...params} disabled={params.row.nit !== -1} />;
  }

  const handleCellClick = (params: GridCellParams) => {
    if (params.field === "name" && Number(params.row.nit) === -1) {
      setOpenProblemDialog({
        open: true,
        module: "diagnose",
        clickNameCell: true,
        clickCancel: false,
      });
      setSearchTextAtom(params.row.name);
    }
  };
  const addToProblemsList = React.useCallback(
    (row: any) => {
      const id = `new_${randomId()}`;
      const emptyRow = {
        id: id,
        key: "0",
        name: row.name,
        nit: -1,
        status: row.status,
        statusval: row.statusval,
        onsetdate: row.onsetdate,
        resolveddate: row.resolvedate,
        snomedcode: row.snomedcode,
        icd10code: row.icd10code,
        comments: row.comments,
        diagid: row.nit,
      };
      const updatedRowData: any = [emptyRow, ...pRowData];
      setProRowData(updatedRowData);
      setProNeedSave({
        value: true,
        action: "copy",
        rowId: -1,
        data: [],
      });
    },
    [pRowData, setProNeedSave, setProRowData]
  );

  const columns = React.useMemo(
    () => [
      {
        field: "nit",
        headerAlign: "center",
        headerName: "",
        align: "center",
        width: 60,
        type: "actions",
        getActions: (params: any) => [
          <GridActionsCellItem
            sx={{ padding: "4px 8px 4px 0px" }}
            icon={
              <DownArrowIcon
                style={{
                  visibility:
                    params.row.probid !== "" ||
                    params.row.added === "1" ||
                    isReadOnly
                      ? "hidden"
                      : "visible",
                }}
              />
            }
            disabled={
              params.row.probid !== "" || params.row.added === "1" || isReadOnly
                ? true
                : false
            }
            label="add"
            title={
              params.row.probid !== "" || params.row.added === "1" || isReadOnly
                ? ""
                : "Add Diagnosis to Problems List"
            }
            onClick={() => {
              addToProblemsList(params.row);
            }}
          />,
        ],
      },
      {
        field: "status",
        headerName: "Status",
        width: 120,
        editable: !isReadOnly,
        renderEditCell: (params: any) => <StatusFieldCell {...params} />,
        type: "singleSelect",
        valueOptions: ["Active", "Error"],
        renderCell: CommonRenderCell,
      },
      {
        field: "name",
        headerName: "Diagnosis",
        minWidth: 150,
        disableColumnMenu: true,
        editable: !isReadOnly,
        renderEditCell: renderProblemEditCell,
        renderCell: (params: GridRenderCellParams<any>) => (
          <div
            style={{
              font: "normal 400 15px Roboto",
              overflow: "hidden",
              textWrap: "nowrap",
              lineHeight: 1,
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              fontStyle: params.row.status === "Inactive" ? "italic" : "normal",
              textDecoration:
                params.row.status === "Error" ? "line-through" : "none",
            }}
          >
            {params.value}
          </div>
        ),
      },
      {
        field: "icd10code",
        headerName: "ICD10 Code",
        width: 130,
        disableColumnMenu: true,
        editable: !isReadOnly,
        renderEditCell: (params: any) => (
          <ICD10CodeFieldCell {...params} disabled={true} />
        ),
        renderCell: CommonRenderCell,
      },
      {
        field: "onsetdate",
        headerName: "Onset Date",
        width: 130,
        disableColumnMenu: true,
        editable: !isReadOnly,
        type: "string",
        sortComparator: dateComparator,
        renderEditCell: (params: any) => (
          <DateField {...params} disableFuture={true} autoPopup={true} />
        ),
        renderCell: CommonRenderCell,
      },
      {
        field: "comments",
        headerName: "Comments",
        disableColumnMenu: true,
        editable: !isReadOnly,
        minWidth: 130,
        renderEditCell: (params: any) => (
          <NoteFieldCell type="Diagnosis" {...params} />
        ),
        renderCell: CommonRenderCell,
      },
    ],
    [addToProblemsList, isReadOnly]
  );

  const handleRowUpdate = (newRow: any, oldRow: any) => {
    if (!newRow || !oldRow) return;
    // Stop exiting edit mode when one of of these fields are empty
    let isInvalidDate = newRow.onsetdate === "Invalid Date";
    if (!newRow.status || !newRow.name || isInvalidDate) {
      setShowInputError(true);
      throw new Error(
        isInvalidDate ? "Invalid date format." : "Required fields empty."
      );
    } else {
      if (
        JSON.stringify(newRow) !== JSON.stringify(oldRow) ||
        Number(newRow.nit) === -1
      ) {
        // Update rowData with updated data from newRow
        const updatedRowData: any = rowData.map((entry: any) => {
          if (entry.id === newRow.id) {
            Object.keys(entry).forEach((field) => {
              entry[field] = newRow[field];
            });
          }
          return entry;
        });
        setRowData(updatedRowData);
        setNeedSave({
          value: true,
          action: newRow.nit.toString() === "-1" ? "added" : "updated",
          rowId: newRow.nit,
          data: [],
        });
      }
    }
    return newRow;
  };

  const handleRowClick = (params: GridRowParams) => {
    if (params.row["probid"] !== "") {
      const relatedRow: any = (pRowData as []).find(
        (row: any) => row.nit === params.row["probid"]
      );
      if (relatedRow) {
        setSelectedRowProb(relatedRow);
      }
    } else if (params.row["added"] === "1") {
      const relatedRow: any = (pRowData as []).find(
        (row: any) => row["diagid"] === params.row["nit"]
      );
      if (relatedRow) {
        setSelectedRowProb(relatedRow);
      }
    }
    setSelectedRow(params.row);
  };

  const autosizeOptions = {
    columns: ["name"],
    includeHeaders: true,
    includeOutliers: true,
  };

  React.useEffect(() => {
    setTimeout(() => {
      apiRef.current?.autosizeColumns(autosizeOptions);
    }, 100);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShowInactive]);

  React.useEffect(() => {
    apiRef.current.autosizeColumns &&
      apiRef.current.autosizeColumns(autosizeOptions);
  });

  React.useEffect(() => {
    if (selectedRow && selectedRow["id"] != null) {
      setRowSelectionModel([selectedRow["id"]]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRow]);

  // Save Problems data to the server
  React.useEffect(() => {
    if (needSave.value) {
      const updatingRow: any = (rowData as []).find(
        (row: any) => row.nit === needSave.rowId
      );
      setNeedSave({ value: false, action: "", data: [] });

      const onSuccess = (result: any) => {
        if (result) {
          if (needSave.action !== "") {
            if (needSave.action === "added") {
              let params = {
                time: Math.round(Date.now() / 1000),
                name: updatingRow.name,
                snomed: updatingRow.snomedcode,
                icd10: updatingRow.icd10code,
                frequent: 1,
                pin: updatingRow.pin,
                refine: updatingRow.refine,
                sid: Cci.util.Staff.getSid(),
                envid: Cci.Env.getEnvID(),
                campus: cci.cfg.campus,
                isProblems: false,
              };
              serverRequest(
                "pld/problems/savePinFrenquency",
                params,
                () => {},
                () => {}
              );

              const reviewparams = {
                savedata: JSON.stringify({
                  status: "reviewed",
                  chksum:
                    "3c9c7fbafb469b59ad34d9024dd0073bd8e9bb12e900eacdf1e26408a1db2d47",
                }),
              };
              serverRequest(
                "pld/problems/reviewed",
                reviewparams,
                () => {},
                () => {}
              );
            }
            setNeedReload({
              value: true,
              hlRow: updatingRow,
              category: "Diagnoses",
              dataFromID:
                needSave.action === "copy" ? updatingRow["probid"] : "",
            });
            let msg: any = `Diagnosis ${needSave.action}!`;
            if (Object.keys(promoteProblem).length > 0) {
              msg = "Problem promoted to Diagnosis!";
              setPromoteProblemRow({});
            }
            setShowToast({
              type: "success",
              text: msg,
              open: true,
            });
            setRowSelectionModel([]);
          }
        }
      };

      const onFailure = (error: string) => {
        setNeedReload({ value: true, hlRow: updatingRow });
        setPromoteProblemRow({});
        setOpenErrorDialog({
          text: `System has failed to update Diagnosis: ${error}`,
          open: true,
        });
      };

      if (updatingRow && updatingRow["nit"]) {
        let datasArr: any[] = getSaveData("Diagnoses", rowData);
        const params = {
          group: "pld_diagnoses",
          appname: "UNLOCK",
          fsdata: datasArr,
          perm: "R",
        };
        serverRequest("DBI/savemgdb", params, onSuccess, onFailure);
      }
    }
  }, [
    needSave.action,
    needSave.rowId,
    needSave.value,
    rowData,
    setNeedReload,
    setNeedSave,
    setOpenErrorDialog,
    setShowToast,
    promoteProblem,
    setPromoteProblemRow,
    setRowSelectionModel,
  ]);

  return (
    <>
      <Box
        style={{
          width: "100%",
        }}
      >
        <DataGridAccordion
          apiRef={apiRef}
          type={"DIAGNOSES"}
          rowHeight={ProblemsRowHeight}
          rows={displayData}
          columns={columns}
          displayMsg={"Diagnosis not yet set for this encounter"}
          enableStatusColFilter={true}
          autosizeOptions={autosizeOptions}
          enableSummary={false}
          onRowUpdate={handleRowUpdate}
          onRowClick={handleRowClick}
          onRowDoubleClick={(params: any) => {
            toPldSelectRow("Problems", {
              gridType: "diagnosis",
              lgtime: params.row.lgtime,
            });
          }}
          onCellClick={handleCellClick}
          rowModesModel={rowModesModel}
          setRowModesModel={setRowModesModel}
          selectedRow={selectedRow}
          setSelectedRow={setSelectedRow}
          rowSelectionModel={rowSelectionModel}
          setRowSelectionModel={setRowSelectionModel}
          isCellEditable={(params: any) => isEditable === true}
          onRowEditStop={(params: GridRowEditStopParams, event: any) => {
            if (
              openProblemDialog.open ||
              (openProblemDialog.clickCancel && openProblemDialog.clickNameCell)
            ) {
              event.defaultMuiPrevented = true;
            }
          }}
          columnVisibilityModel={{
            status: isShowInactive,
          }}
        />
        {displayRows.length > diagnosesPageSize * pageDiag && (
          <AdditionalFooter
            msg="Load More"
            handleClick={() => setPageDiag((preVal) => preVal + 1)}
          />
        )}
      </Box>
    </>
  );
};
