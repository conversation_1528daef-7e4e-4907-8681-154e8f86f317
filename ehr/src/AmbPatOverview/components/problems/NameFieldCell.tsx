import * as React from "react";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import { useAtom } from "jotai";
import {
  GridRenderEditCellParams,
  useGridApiContext,
} from "@mui/x-data-grid-pro";
import { PopupIcon } from "../../../common/assets";
import { openProblemDialogAtom } from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";

export const NameFieldCell = (params: GridRenderEditCellParams<any>) => {
  const [openProblemDialog, setOpenProblemDialog] = useAtom(
    openProblemDialogAtom
  );

  const { id, field, disabled } = params;
  const [value, setValue] = React.useState<any>(
    params.value && params.value !== "" ? params.value : ""
  );
  const [error] = React.useState<boolean>(false);

  const apiRef = useGridApiContext();
  React.useLayoutEffect(() => {
    apiRef.current.setEditCellValue({
      id,
      field,
      value: error ? null : value,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  React.useEffect(() => {
    if (
      !openProblemDialog.open &&
      openProblemDialog.clickCancel &&
      openProblemDialog.clickNameCell
    ) {
      apiRef.current.setCellFocus(id, "name");
      const input = document.querySelector(
        `div.MuiDataGrid-row[data-id="${id}"] div[data-field="name"] input`
      ) as HTMLElement;
      input.focus();
      setTimeout(
        () =>
          setOpenProblemDialog({
            ...openProblemDialog,
            clickNameCell: false,
            clickCancel: false,
            open: false,
          }),
        500
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openProblemDialog]);

  const handleClickShowProblemWin = () => {
    setOpenProblemDialog({
      open: true,
      module: "diagnose",
      clickNameCell: false,
      clickCancel: false,
    });
  };

  return (
    <TextField
      sx={{
        "& .MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputAdornedEnd":
          {
            padding: "6px 0px 6px 14px",
          },
        width: "calc(100% - 6px)",
        height: "calc(100% - 6px)",
        marginLeft: "3px",
        background: disabled ? "#EBEBEB" : "#FFF",
        borderRadius: "4px",
      }}
      value={value}
      disabled={disabled}
      onChange={(event: any) => {
        setValue(event.target.value);
      }}
      InputProps={{
        endAdornment: disabled ? (
          <></>
        ) : (
          <InputAdornment position="end">
            <IconButton edge="end" onClick={handleClickShowProblemWin}>
              <PopupIcon />
            </IconButton>
          </InputAdornment>
        ),
      }}
    />
  );
};
