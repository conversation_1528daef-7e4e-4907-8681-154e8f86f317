/**
 * Problems.tsx
 *
 * @author: jfsys
 * @description Problems component
 */

import { useEffect, useState } from "react";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import Box from "@mui/material/Box";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";

import { ModuleHeaderHeight } from "../common/Constants";
import {
  needReloadProblemsAtom,
  selectedRowDiagnosisAtom,
  selectedRowProblemsAtom,
} from "@cci-monorepo/common/context/atoms/problem";
import { userContextAtom } from "../../context/CommonAtoms";
import { getReviewHistory } from "./ProblemsUtils";
import { serverRequest } from "../../utils/DataRequests";
import { ProblemsToolbar } from "./ProblemsToolbar";
import { DiagnoseToolbar } from "./DiagnoseToolbar";
import { DiagnoseGrid } from "./DiagnoseGrid";
import { ProblemToolbar } from "./ProblemToolbar";
import { ProblemsGrid } from "./ProblemGrid";
import { useSetErrorDialog } from "@cci-monorepo/common";
import { getProblems } from "@cci-monorepo/common/utils/problem/ProblemsDataAPI";
import {
  GridCallbackDetails,
  GridRowModes,
  GridRowParams,
  MuiEvent,
} from "@mui/x-data-grid-pro";
import {
  rowDataDiagnosisAtom,
  rowDataProblemsAtom,
  rowModesModelProblemAtom,
  rowModesModelDiagnosisAtom,
  setParseDataProblemsAtom,
  openProblemDialogAtom,
  setReviewMessageProblemsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";
import { randomId } from "@mui/x-data-grid-generator";
import ProblemDialog from "@cci-monorepo/common/components/problem/ProblemDialog";
export const Problems = (props: any) => {
  const rowDataProblems = useAtomValue(rowDataProblemsAtom);
  const [rowDataDiagnosis, setRowDataDiagnosis] = useAtom(rowDataDiagnosisAtom);
  const setRowModesModelDiagnosis = useSetAtom(rowModesModelDiagnosisAtom);

  const parseProblemsData = useSetAtom(setParseDataProblemsAtom);
  const [openProblemDialog, setOpenProblemDialog] = useAtom(
    openProblemDialogAtom
  );
  const setOpenErrorDialog = useSetErrorDialog();
  const setReviewMessageProblems = useSetAtom(setReviewMessageProblemsAtom);
  const [needReload, setNeedReload] = useAtom(needReloadProblemsAtom);
  const setSelectedRowProb = useSetAtom(selectedRowProblemsAtom);
  const setSelectedRowDiag = useSetAtom(selectedRowDiagnosisAtom);
  const userContext = useAtomValue(userContextAtom);

  const [hasData, setHasData] = useState<boolean>(false);
  const [expanded, setExpanded] = useState<boolean>(true);
  const [rowData, setRowData] = useAtom(rowDataProblemsAtom);
  const setRowModesModel = useSetAtom(rowModesModelProblemAtom);

  useEffect(() => {
    if (!hasData || needReload.value) {
      const curCategory = needReload.category;
      const curRow = needReload.hlRow;
      const dataFromID = needReload.dataFromID;
      if (needReload.value) {
        setNeedReload({
          ...needReload,
          value: false,
          hlRow: undefined,
          saveUndo: false,
        });
      }

      const selectRowsInRelated = (category: any, relatedID: any, row: any) => {
        if (category === "Diagnoses") {
          const curRow: any = (rowDataProblems as []).find(
            (row: any) => row.id === row["id"]
          );
          const relatedRow: any = (rowDataDiagnosis as []).find(
            (row: any) => row.nit === relatedID
          );
          if (curRow) {
            setSelectedRowProb(relatedRow);
            setSelectedRowDiag(curRow);
          }
        } else {
          const curRow: any = (rowDataProblems as []).find(
            (row: any) => row.id === row["id"]
          );
          const relatedRow: any = (rowDataDiagnosis as []).find(
            (row: any) => row.nit === relatedID
          );
          if (curRow) {
            setSelectedRowProb(curRow);
            setSelectedRowDiag(relatedRow);
          }
        }
      };

      const onSuccess = (result: any) => {
        if (result && result.length > 0) {
          parseProblemsData(result, 0, !hasData);
          setHasData(true);
          if (dataFromID !== "") {
            selectRowsInRelated(curCategory, dataFromID, curRow);
          }
        }
      };

      const onFailure = (error: string) => {
        setOpenErrorDialog({
          text: `System has failed to load problems data: ${error}`,
          open: true,
        });
      };

      const onHistorySuccess = (data: any) => {
        setReviewMessageProblems(getReviewHistory(data));
      };

      const onHistoryFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load review history. " + error,
          open: true,
        });
      };

      const dbpaths = [
        { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
      ];
      let promises = dbpaths.map((item: any, index) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        return getProblems(params, Cci.util.Patient.getDbpath());
      });
      Promise.all(promises)
        .then((ret: any) => {
          onSuccess(ret);
          const params = {
            campus: cci.cfg.campus,
            dbs: Cci.util.Patient.getDbpath(),
          };
          return serverRequest(
            "pld/problems/review_history",
            params,
            onHistorySuccess,
            onHistoryFailure
          );
        })
        .catch(onFailure);
    }
  }, [
    hasData,
    needReload,
    setNeedReload,
    setOpenErrorDialog,
    parseProblemsData,
    setReviewMessageProblems,
    rowDataProblems,
    rowDataDiagnosis,
    setSelectedRowDiag,
    setSelectedRowProb,
  ]);

  useEffect(() => {
    setExpanded(
      rowDataDiagnosis?.length > 0 || rowDataProblems?.length > 0 ? true : false
    );
  }, [rowDataDiagnosis, rowDataProblems, setExpanded]);
  const onRowDoubleClick = (
    params: GridRowParams,
    event: MuiEvent,
    details: GridCallbackDetails
  ) => {
    const id = `new_${randomId()}`;
    const emptyRow = {
      id: id,
      key: "0",
      name: params.row.name,
      nit: "-1",
      status: "Active",
      statusval: "0",
      onsetdate: "",
      resolveddate: "",
      snomedcode: params.row.snomedcode,
      icd10code: params.row.icd10code,
      pin: params.row.pin,
      comments: "",
      diagid: "",
    };
    const rowDatas: any[] = rowData.filter((a: any) => Number(a.nit) !== -1);
    const updatedRowData: any = [emptyRow, ...rowDatas];
    setRowData(updatedRowData);
    setRowModesModel((oldModel) => ({
      ...oldModel,
      [id]: { mode: GridRowModes.Edit, fieldToFocus: "onsetdate" },
    }));
  };
  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="problems"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="problems-content"
        id="problems-header"
      >
        <ProblemsToolbar readonly={props.readonly} />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <Box
          sx={{
            overflowY: "auto",
          }}
        >
          <Box
            sx={{
              backgroundColor: "#F7F7F7",
              padding: "8px 16px 8px 64px",
            }}
          >
            <DiagnoseToolbar />
          </Box>
          {expanded && (
            <Box
              sx={{
                padding: "0px 0px 16px 0px",
              }}
            >
              <DiagnoseGrid userContext={userContext} />
            </Box>
          )}
          <Box
            sx={{
              backgroundColor: "#F7F7F7",
              padding: "8px 16px 8px 64px",
            }}
          >
            <ProblemToolbar />
            <ProblemDialog
              openProblemDialog={openProblemDialog}
              setProblemDialog={setOpenProblemDialog}
              module={openProblemDialog.module}
              onRowDoubleClick={onRowDoubleClick}
              rowData={rowDataDiagnosis}
              setRowData={setRowDataDiagnosis}
              setRowModesModel={setRowModesModelDiagnosis}
            />
          </Box>
          {expanded ? <ProblemsGrid userContext={userContext} /> : <></>}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};
