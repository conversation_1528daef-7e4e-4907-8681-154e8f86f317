import {
  GridEditSingleSelectCell,
  GridEditSingleSelectCellProps,
} from "@mui/x-data-grid-pro";

export const StatusFieldCell = (props: GridEditSingleSelectCellProps) => {
  const handleValueChange = async (event: any, newValue: any) => {
    const divs = document.getElementsByTagName("DIV");
    let rowDiv = undefined;
    for (let i: number = 0; i < divs.length; i++) {
      const item = divs[i];
      if (item.getAttribute("data-id") === props.id) {
        rowDiv = item;
        break;
      }
    }
    if (rowDiv) {
      const colDivs = rowDiv.childNodes;
      const textDecoration = newValue === "Error" ? "line-through" : "none";
      const fontStyle = newValue === "Inactive" ? "italic" : "normal";
      for (let j: number = 1; j < colDivs.length; j++) {
        const colDiv: any = colDivs[j];
        if (colDiv["tagName"] === "DIV") {
          colDiv.style["fontStyle"] = fontStyle;
          colDiv.style["textDecoration"] = textDecoration;
        }
      }
    }
  };

  return (
    <GridEditSingleSelectCell
      sx={{
        width: "calc(100% - 6px)",
        height: "calc(100% - 6px)",
        marginLeft: "3px",
        backgroundColor: "white",
        borderRadius: "4px",
      }}
      onValueChange={handleValueChange}
      {...props}
    />
  );
};
