/**
 * HMSection.tsx
 *
 * @author: jfsys
 * @description Data grid accordion that contains health maintenance data
 */

import { useEffect, useMemo, useState } from "react";
import dayjs from "dayjs";
import { useAtom, useAtomValue } from "jotai";
import Box from "@mui/material/Box";
import TabContext from "@mui/lab/TabContext";
import {
  rowDataImmuAtom,
  pageImmuAtom,
  rowDataPreventiveAtom,
  pagePreventiveAtom,
  rowDataHealthAtom,
  pageHealthAtom,
} from "../../context/HealthMaintenanceAtoms";
import {
  StyledTab,
  StyledTabs,
  StyledTabPanel,
  HMPageSize,
} from "../common/Constants";
import ImmunizationForecast from "./immunizationForecast";
import PreventiveMeasures from "./PreventiveMeasures";
import HealthScreenings from "./HealthScreenings";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import { AdditionalFooter } from "../common/AdditionalFooter";

export const HMSection = ({ readonly }: any) => {
  const [rowDataImmu, setRowDataImmu] = useAtom(rowDataImmuAtom);
  const rowDataPreventive = useAtomValue(rowDataPreventiveAtom);
  const rowDataHealth = useAtomValue(rowDataHealthAtom);
  const [pageImmu, setPageImmu] = useAtom(pageImmuAtom);
  const [pagePreventive, setPagePreventive] = useAtom(pagePreventiveAtom);
  const [pageHealth, setPageHealth] = useAtom(pageHealthAtom);

  const [hl7Data, setHl7Data] = useState<any>({});
  const [selectedTab, setSelectedTab] = useState("1");

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
  };

  const forecastCountStr = useMemo(() => {
    const count = pageImmu * HMPageSize;
    if (count !== undefined && rowDataImmu!.length) {
      if (count >= rowDataImmu!.length) {
        return `(${rowDataImmu!.length})`;
      } else {
        return `(${count} of ${rowDataImmu!.length})`;
      }
    } else {
      return "";
    }
  }, [pageImmu, rowDataImmu]);

  const measureCountStr = useMemo(() => {
    const count = pagePreventive * HMPageSize;
    if (count !== undefined && rowDataPreventive!.length) {
      if (count >= rowDataPreventive!.length) {
        return `(${rowDataPreventive!.length})`;
      } else {
        return `(${count} of ${rowDataPreventive!.length})`;
      }
    } else {
      return "";
    }
  }, [pagePreventive, rowDataPreventive]);

  const screenCountStr = useMemo(() => {
    const count = pageHealth * HMPageSize;
    if (count !== undefined && rowDataHealth!.length) {
      if (count >= rowDataHealth!.length) {
        return `(${rowDataHealth!.length})`;
      } else {
        return `(${count} of ${rowDataHealth!.length})`;
      }
    } else {
      return "";
    }
  }, [pageHealth, rowDataHealth]);

  const handleForeCast = () => {
    const onIISSuccess = (result: any) => {
      if (
        result &&
        result.data &&
        result.data.data &&
        result.data.data[0] &&
        result.data.data[0][0]
      ) {
        let jObj = JSON.parse(result.data.data[0]);
        switch (jObj["type"]) {
          case "Z31":
            let pats = jObj["pid"];
            let availPats: any = [];
            if (pats && pats.length > 0) {
              pats.forEach((p: any) => {
                formatPat(p);
                availPats.push(p);
              });
              let idx = availPats.findIndex(
                (i: any) => i.SR === Cci.util.Patient.getMrn()
              );
              getForecastInfo(availPats[idx]);
            }
            break;
          case "Z32":
            const params = {
              hl7type: "Z44",
              dbp: Cci.util.Patient.getDbpath(),
            };
            serverRequest(
              "pld/immunizations/forecast",
              params,
              onIISSuccess,
              onIISFailed
            );
            break;
          case "Z42":
            setHl7Data(jObj);
            break;
        }
      }
    };
    const onIISFailed = (error: string) => {
      console.error(error);
    };
    const params = {
      hl7type: "Z34",
      dbp: Cci.util.Patient.getDbpath(),
    };

    serverRequest(
      "pld/immunizations/forecast",
      params,
      onIISSuccess,
      onIISFailed
    );
  };

  const formatPat = (p: any) => {
    p["pat_addr"] =
      (p["streetAddr"] ? p["streetAddr"] + "," : "") +
      (p["cityAddr"] ? p["cityAddr"] + "," : "") +
      (p["stateAddr"] ? p["stateAddr"] + "," : "") +
      (p["zipCode"] ? p["zipCode"] + "," : "") +
      (p["country"] ? p["country"] : "");
    let pids: any = [];
    if (p["SR"]) {
      pids.push("SR:" + p["SR"]);
      p["pid"] = p["SR"];
    }
    if (p["MR"]) {
      pids.push("MR:" + p["MR"]);
      if (!p["pid"]) p["pid"] = p["MR"];
    }
    p["pids"] = pids.join(";");
    p["age"] = p["pat_bod"]
      ? dayjs().diff(dayjs(p["pat_bod"], "YYYYMMDD"), "year")
      : "";
    p["pat_bod"] = p["pat_bod"]
      ? dayjs(p["pat_bod"], "YYYYMMDD").format("DD MMM YYYY")
      : "";
  };

  const getForecastInfo = (pat: any) => {
    const onIISSuccess = (result: any) => {
      if (
        result &&
        result.data &&
        result.data.data &&
        result.data.data[0] &&
        result.data.data[0][0]
      ) {
        let jObj = JSON.parse(result.data.data[0]);
        setHl7Data(jObj);
      }
    };

    const onIISFailed = (error: string) => {
      console.error(error);
    };
    const params = {
      hl7type: "Z44",
      pat: JSON.stringify(pat),
    };
    serverRequest(
      "pld/immunizations/forecast",
      params,
      onIISSuccess,
      onIISFailed
    );
  };

  const formatDate = (d: any) => {
    return d ? dayjs(d, "YYYYMMDD").format("MM/DD/YYYY") : "";
  };
  const parseHL7Data = (d: any) => {
    let fcs = d["forecast"];
    let fc_rows: any = [];
    if (fcs && fcs.length > 0) {
      fcs.forEach((fc: any) => {
        fc["minDoseDate"] = formatDate(fc["minDoseDate"]);
        fc["nextDoseDate"] = formatDate(fc["nextDoseDate"]);
        fc["LatestDoseDate"] = formatDate(fc["LatestDoseDate"]);
        fc_rows.push(fc);
      });
    }

    return fc_rows;
  };

  useEffect(() => {
    handleForeCast();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setRowDataImmu(parseHL7Data(hl7Data));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hl7Data]);

  return (
    <Box sx={{ width: "100%" }}>
      <TabContext value={selectedTab}>
        <StyledTabs
          value={selectedTab}
          onChange={handleTabChange}
          aria-label="hm-panel-tabs"
        >
          <StyledTab
            label={`Immunization Forecast ${forecastCountStr}`}
            value="1"
            disableRipple
          />
          <StyledTab
            label={`Preventive Measures ${measureCountStr}`}
            value="2"
            disableRipple
          />
          <StyledTab
            label={`Health Screenings ${screenCountStr}`}
            value="3"
            disableRipple
          />
        </StyledTabs>
        <StyledTabPanel value="1">
          <ImmunizationForecast loadData={handleForeCast} />
          {rowDataImmu.length > HMPageSize * pageImmu && (
            <AdditionalFooter
              msg="Load More"
              handleClick={() => setPageImmu((preVal) => preVal + 1)}
            />
          )}
        </StyledTabPanel>
        <StyledTabPanel value="2">
          <PreventiveMeasures readonly={readonly} />
          {rowDataPreventive.length > HMPageSize * pagePreventive && (
            <AdditionalFooter
              msg="Load More"
              handleClick={() => setPagePreventive((preVal) => preVal + 1)}
            />
          )}
        </StyledTabPanel>
        <StyledTabPanel value="3">
          <HealthScreenings readonly={readonly} />
          {rowDataHealth.length > HMPageSize * pageHealth && (
            <AdditionalFooter
              msg="Load More"
              handleClick={() => setPageHealth((preVal) => preVal + 1)}
            />
          )}
        </StyledTabPanel>
      </TabContext>
    </Box>
  );
};
