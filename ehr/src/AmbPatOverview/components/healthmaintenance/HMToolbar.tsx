/**
 * HMToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Health Maintenance
 */

import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink } from "../../../common/assets";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import {
  hasReviewedImmunizationAtom,
  reviewedImmunizationAtom,
  displayImmunizationsChecksumAtom,
  timeRefreshAtom,
} from "../../context/HealthMaintenanceAtoms";
import { useAtomValue, useAtom } from "jotai";
import { useSetToastFamily, useSetErrorDialog } from "@cci-monorepo/common";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";

type ReviewType = {
  status: string;
  reason?: string;
  chksum?: string;
};

export const HMToolbar = (props: any) => {
  const hasReviewed = useAtomValue(hasReviewedImmunizationAtom);
  const reviewMessageReason = useAtomValue(reviewedImmunizationAtom);
  const [, setTimeRefreshAtom] = useAtom(timeRefreshAtom);
  const displayImmunizationsChecksum = useAtomValue(
    displayImmunizationsChecksumAtom
  );
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setOpenErrorDialog = useSetErrorDialog();
  const reviewCommit = () => {
    let savedata: ReviewType = {
      status: "reviewed",
      chksum: displayImmunizationsChecksum,
    };
    const params = {
      savedata: JSON.stringify(savedata),
    };
    const onSuccess = (result: any) => {
      if (result && result.data && result.data.data) {
        setTimeRefreshAtom(new Date().valueOf());
        setShowToast({
          type: "success",
          text: "Health Maintenance reviewed",
          open: true,
        });
      }
    };

    const onFailure = (error: string) => {
      setOpenErrorDialog({
        text: "System has failed to review Health Maintenance." + error,
        open: true,
      });
    };
    serverRequest("pld/immunizations/reviewed", params, onSuccess, onFailure);
  };

  const menuItems: MenuItemProps[] = props.readonly
    ? []
    : [
        {
          label: "View In Health Profile",
          icon: <IconExternalLink />,
          handler: () => {
            toPldModule("Immunizations");
          },
        },
      ];

  return (
    <CommonToolbar
      name="Health Maintenance"
      isPldWidget={false}
      menuItems={menuItems}
      hasReviewed={hasReviewed}
      reviewMsg={hasReviewed ? reviewMessageReason?.reviewed_text : ""}
      reviewCommit={reviewCommit}
    />
  );
};
