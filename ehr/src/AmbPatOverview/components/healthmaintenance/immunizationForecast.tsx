import React, { useEffect, useMemo } from "react";
import { Typography } from "@mui/material";
import { TooltipCommonDataGrid } from "../common/TooltipCommonDataGrid";
import { useAtomValue } from "jotai";
import {
  rowDataImmuAtom,
  pageImmuAtom,
} from "@cci-monorepo/AmbPatOverview/context/HealthMaintenanceAtoms";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { dateComparator } from "@cci-monorepo/common";
import { HMPageSize } from "../common/Constants";

interface TextFieldRenderProps {
  value: string;
}

const TextFieldRender = (props: TextFieldRenderProps) => {
  const getStyles = (value: string) => {
    if (value === "Past Due" || value === "Due Now")
      return {
        color: "#ED1F24",
        fontWeight: "700",
      };
    else
      return {
        color: "#000",
        fontWeight: "400",
      };
  };
  return (
    <Typography sx={getStyles(props.value)}>
      {props.value ? props.value : "n/a"}
    </Typography>
  );
};

const ImmunizationForecast = (props: any) => {
  const { loadData } = props;
  const rowDataImmu = useAtomValue(rowDataImmuAtom);
  const page = useAtomValue(pageImmuAtom);

  const columnDefs: any[] = React.useMemo(
    () => [
      {
        field: "vaccineType",
        headerName: "Vaccine Family",
        minWidth: 150,
        flex: 1,
        renderCell: ({ value }: TextFieldRenderProps) => (
          <TextFieldRender value={value} />
        ),
      },
      {
        field: "doseNumber",
        headerName: "Forecasted Dose",
        width: 142,
        renderCell: ({ value }: TextFieldRenderProps) => (
          <TextFieldRender value={value} />
        ),
      },
      {
        field: "nextDoseDate",
        headerName: "Recomm. Date",
        width: 125,
        sortComparator: dateComparator,
        renderCell: ({ value }: TextFieldRenderProps) => (
          <TextFieldRender value={value} />
        ),
      },
      {
        field: "LatestDoseDate",
        headerName: "Overdue Date",
        width: 125,
        sortComparator: dateComparator,
        renderCell: ({ value }: TextFieldRenderProps) => (
          <TextFieldRender value={value} />
        ),
      },
      {
        field: "seriesStatus",
        headerName: "Status",
        width: 105,
        renderCell: ({ value }: TextFieldRenderProps) => (
          <TextFieldRender value={value} />
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [props]
  );

  useEffect(() => {
    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const displayData = useMemo(() => {
    return rowDataImmu?.length >= HMPageSize * page
      ? rowDataImmu?.slice(0, HMPageSize * page)
      : rowDataImmu;
  }, [rowDataImmu, page]);

  return (
    <TooltipCommonDataGrid
      type="immunizationForecast"
      rows={displayData}
      getRowId={(row: any) => row.idx}
      columns={columnDefs}
      noDataMsg="No immunization forecast"
      onRowDoubleClick={() => {
        toPldModule("Immunizations");
      }}
      striped={false}
    />
  );
};

export default ImmunizationForecast;
