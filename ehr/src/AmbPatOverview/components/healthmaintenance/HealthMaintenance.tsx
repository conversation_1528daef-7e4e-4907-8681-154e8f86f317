/**
 * HealthMaintenance.tsx
 *
 * @author: jfsys
 * @description Health Maintenance component
 */

import { useState, useEffect } from "react";
import {
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { HMToolbar } from "./HMToolbar";
import { ModuleHeaderHeight, USERCONTEXT } from "../common/Constants";
import { HMSection } from "./HMSection";
import { useAtomValue, useSetAtom, useAtom } from "jotai";
import { userContextAtom, pldDbpathsAtom } from "../../context/CommonAtoms";
import {
  setImmunizationHistoryAtom,
  setParseDataImmunizationAtom,
  timeRefreshAtom,
} from "../../context/HealthMaintenanceAtoms";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import { useSetErrorDialog } from "@cci-monorepo/common";
import { getReviewHistory } from "@cci-monorepo/Pld/components/immunization/ImmunizationUtil";
import { getImmunizations } from "@cci-monorepo/Pld/util/ImmunizationDataAPI";

export const HealthMaintenance = (props: any) => {
  const userContext = useAtomValue(userContextAtom);
  const setImmunizationHistory = useSetAtom(setImmunizationHistoryAtom);
  const parseDataAtom = useSetAtom(setParseDataImmunizationAtom);
  const [timeRefresh] = useAtom(timeRefreshAtom);
  const pldDbpaths = useAtomValue(pldDbpathsAtom);
  const [expanded, setExpanded] = useState<boolean>(true);
  const setOpenErrorDialog = useSetErrorDialog();

  useEffect(() => {
    if (pldDbpaths?.length) {
      getData();
    }
  }, [pldDbpaths, timeRefresh]);

  const getData = () => {
    let currentIndex = 0;
    const onSuccess = (data: any) => {
      parseDataAtom(data, currentIndex);
    };
    // Callback to process data from server when failed
    const onFailure = (error: any) => {
      setOpenErrorDialog({
        text: "System has failed to load immunization data. " + error,
        open: true,
      });
    };

    let promises = pldDbpaths.map((item: any, index) => {
      const params = { campus: item.campus, dbs: item.dbpath };
      if (item.dbpath === Cci.util.Patient.getDbpath()) {
        currentIndex = index;
      }
      return getImmunizations(params, () => {}, onFailure);
    });
    Promise.all(promises).then((ret: any) => {
      onSuccess(ret);
      getHistory();
    });
  };

  const getHistory = () => {
    const onHistorySuccess = (data: any) => {
      setImmunizationHistory(getReviewHistory(data));
    };

    const onHistoryFailure = (error: any) => {
      setOpenErrorDialog({
        text: "System has failed to load review history. " + error,
        open: true,
      });
    };

    const params = {
      campus: cci.cfg.campus,
      dbs: Cci.util.Patient.getDbpath(),
    };

    return serverRequest(
      "pld/immunizations/review_history",
      params,
      onHistorySuccess,
      onHistoryFailure
    );
  };

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="health-maintenance"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="health-maintenance-content"
        id="health-maintenance-header"
      >
        <HMToolbar readonly={props.readonly} />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <Box>
          {expanded ? (
            <HMSection
              readonly={props.readonly || userContext !== USERCONTEXT.NURSE}
            />
          ) : (
            <></>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};
