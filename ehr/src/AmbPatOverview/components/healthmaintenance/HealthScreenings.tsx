import React, { useCallback, useEffect, useMemo, useState } from "react";
import Box from "@mui/material/Box";
import { GridRowModel } from "@mui/x-data-grid-pro";
import { CommonDataGrid } from "../common/CommonDataGrid";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  rowDataHealthAtom,
  pageHealthAtom,
  hasReviewedHealthAtom,
} from "@cci-monorepo/AmbPatOverview/context/HealthMaintenanceAtoms";
import { HMPageSize, StyledNameTooltip } from "../common/Constants";
import { DataGridSelect } from "@cci-monorepo/Pld/components/common/DataGridSelect";
import { serverNow, useSetToastFamily } from "@cci-monorepo/common";
import { ClickAwayListener, Fade, Grid } from "@mui/material";
import TooltipCommonRow from "../common/TooltipCommonRow";

const HealthScreenings = (props: any) => {
  const { readonly } = props;
  const [rowDataHealth, setRowDataHealth] = useAtom(rowDataHealthAtom);
  const page = useAtomValue(pageHealthAtom);
  const setReviewedHealth = useSetAtom(hasReviewedHealthAtom);
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const [rowSelectionModel, setRowSelectionModel] = useState([]);

  const columnDefs: any[] = React.useMemo(
    () => [
      {
        field: "name",
        headerName: "Health Measure",
        minWidth: 150,
        flex: 1,
      },
      {
        field: "frequency",
        headerName: "Frequency",
        width: 178,
      },
      {
        field: "dueDate",
        headerName: "Due Date",
        width: 125,
      },
      {
        field: "status",
        headerName: "Status",
        width: 178,
        editable: !readonly,
        renderEditCell: (params: any) => {
          const options = [
            { label: "Outstanding Order", value: "Outstanding Order" },
            { label: "Complete", value: "Complete" },
            { label: "Not Applicable", value: "Not Applicable" },
          ];
          return <DataGridSelect params={params} options={options} />;
        },
        renderCell: (params: any) => {
          return params.row.storedAt ? (
            <StyledNameTooltip
              enterDelay={800}
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              sx={{ maxWidth: 220 }}
              title={
                <>
                  <Grid container>
                    <TooltipCommonRow
                      key={1}
                      label="Stored At"
                      value={params.row.storedAt}
                    />
                    <TooltipCommonRow
                      key={2}
                      label="Stored By"
                      value={params.row.storedBy}
                    />
                  </Grid>
                </>
              }
            >
              {params.value}
            </StyledNameTooltip>
          ) : (
            <Box>{params.value}</Box>
          );
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [props]
  );

  const processRowUpdate = (newRow: GridRowModel, oldRow: GridRowModel) => {
    let updatedRow: any = {
      ...newRow,
    };
    if (JSON.stringify(newRow) !== JSON.stringify(oldRow)) {
      updatedRow.storedBy = Cci.util.Staff.getLoginName();
      updatedRow.storedAt = Cci.util.DateTime.serverSecsToTimeStr(
        Cci.util.DateTime.dateToServerUnixtime(serverNow()),
        "HHmm DD MMM YYYY"
      );
      // Update rowData with updated data from newRow
      const updatedRowData: any = rowDataHealth.map((entry: any) => {
        if (entry.name === updatedRow.name) {
          Object.keys(entry).forEach((field) => {
            entry[field] = updatedRow[field];
          });
        }
        return entry;
      });
      setRowDataHealth(updatedRowData);
      setShowToast({
        type: "success",
        text: "Health Maintenance updated!",
        open: true,
      });
    }
    return updatedRow;
  };

  useEffect(() => {
    let notReviewed = rowDataHealth.some(
      (item: any) => item.status !== "Complete"
    );
    setReviewedHealth(!notReviewed);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowDataHealth]);

  const displayData = useMemo(() => {
    return rowDataHealth?.length >= HMPageSize * page
      ? rowDataHealth?.slice(0, HMPageSize * page)
      : rowDataHealth;
  }, [rowDataHealth, page]);

  const getRowId = useCallback((row: any) => row.name, []);

  return (
    <ClickAwayListener
      onClickAway={(envent: any) => {
        if (rowSelectionModel && rowSelectionModel.length > 0) {
          setRowSelectionModel([]);
        }
      }}
    >
      <CommonDataGrid
        type="healthScreenings"
        editMode="row"
        rows={displayData}
        getRowId={getRowId}
        columns={columnDefs}
        noDataMsg="No health screenings"
        striped={false}
        rowSelectionModel={rowSelectionModel}
        setRowSelectionModel={setRowSelectionModel}
        processRowUpdate={processRowUpdate}
      />
    </ClickAwayListener>
  );
};

export default HealthScreenings;
