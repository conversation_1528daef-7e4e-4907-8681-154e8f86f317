import React, { useCallback, useEffect, useMemo, useState } from "react";
import Box from "@mui/material/Box";
import { GridRowModel } from "@mui/x-data-grid-pro";
import { CommonDataGrid } from "../common/CommonDataGrid";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  rowDataPreventiveAtom,
  pagePreventiveAtom,
  hasReviewedPrevAtom,
} from "@cci-monorepo/AmbPatOverview/context/HealthMaintenanceAtoms";
import { HMPageSize, StyledNameTooltip } from "../common/Constants";
import { DataGridSelect } from "@cci-monorepo/Pld/components/common/DataGridSelect";
import { serverNow, useSetToastFamily } from "@cci-monorepo/common";
import { ClickAwayListener, Fade, Grid } from "@mui/material";
import TooltipCommonRow from "../common/TooltipCommonRow";

const PreventiveMeasures = (props: any) => {
  const { readonly } = props;
  const [rowDataPreventive, setRowDataPreventive] = useAtom(
    rowDataPreventiveAtom
  );
  const page = useAtomValue(pagePreventiveAtom);
  const setReviewedPrev = useSetAtom(hasReviewedPrevAtom);
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const [rowSelectionModel, setRowSelectionModel] = useState([]);

  const columnDefs: any[] = React.useMemo(
    () => [
      {
        field: "name",
        headerName: "Preventive Measure",
        minWidth: 150,
        flex: 1,
      },
      {
        field: "frequency",
        headerName: "Frequency",
        width: 300,
      },
      {
        field: "status",
        headerName: "Status",
        width: 128,
        editable: !readonly,
        renderEditCell: (params: any) => {
          const options = [
            { label: "Counseled", value: "Counseled" },
            { label: "Pending", value: "Pending" },
            { label: "Completed", value: "Completed" },
            { label: "Not Applicable", value: "Not Applicable" },
          ];
          return <DataGridSelect params={params} options={options} />;
        },
        renderCell: (params: any) => {
          return params.row.storedAt ? (
            <StyledNameTooltip
              enterDelay={800}
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              sx={{ maxWidth: 220 }}
              title={
                <>
                  <Grid container>
                    <TooltipCommonRow
                      key={1}
                      label="Stored At"
                      value={params.row.storedAt}
                    />
                    <TooltipCommonRow
                      key={2}
                      label="Stored By"
                      value={params.row.storedBy}
                    />
                  </Grid>
                </>
              }
            >
              {params.value}
            </StyledNameTooltip>
          ) : (
            <Box>{params.value}</Box>
          );
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [props]
  );

  const processRowUpdate = (newRow: GridRowModel, oldRow: GridRowModel) => {
    let updatedRow: any = {
      ...newRow,
    };
    if (JSON.stringify(newRow) !== JSON.stringify(oldRow)) {
      updatedRow.storedBy = Cci.util.Staff.getLoginName();
      updatedRow.storedAt = Cci.util.DateTime.serverSecsToTimeStr(
        Cci.util.DateTime.dateToServerUnixtime(serverNow()),
        "HHmm DD MMM YYYY"
      );
      // Update rowData with updated data from newRow
      const updatedRowData: any = rowDataPreventive.map((entry: any) => {
        if (entry.name === updatedRow.name) {
          Object.keys(entry).forEach((field) => {
            entry[field] = updatedRow[field];
          });
        }
        return entry;
      });
      setRowDataPreventive(updatedRowData);
      setShowToast({
        type: "success",
        text: "Health Maintenance updated!",
        open: true,
      });
    }
    return updatedRow;
  };

  useEffect(() => {
    let notReviewed = rowDataPreventive.some(
      (item: any) => item.status !== "Completed"
    );
    setReviewedPrev(!notReviewed);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowDataPreventive]);

  const displayData = useMemo(() => {
    return rowDataPreventive?.length >= HMPageSize * page
      ? rowDataPreventive?.slice(0, HMPageSize * page)
      : rowDataPreventive;
  }, [rowDataPreventive, page]);

  const getRowId = useCallback((row: any) => row.name, []);

  return (
    <ClickAwayListener
      onClickAway={() => {
        if (rowSelectionModel && rowSelectionModel.length > 0) {
          setRowSelectionModel([]);
        }
      }}
    >
      <CommonDataGrid
        type="preventiveMeasures"
        editMode="row"
        rows={displayData}
        getRowId={getRowId}
        columns={columnDefs}
        noDataMsg="No preventive measures"
        striped={false}
        rowSelectionModel={rowSelectionModel}
        setRowSelectionModel={setRowSelectionModel}
        processRowUpdate={processRowUpdate}
      />
    </ClickAwayListener>
  );
};

export default PreventiveMeasures;
