/**
 * Todos.tsx
 *
 * @author: jfsys
 * @description Todos component
 */

import { useState } from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";

import { TodosToolbar } from "./TodosToolbar";
import { ModuleHeaderHeight } from "../common/Constants";
import { TodosSection } from "./TodosSection";
import { userContextAtom } from "../../context/CommonAtoms";
import { useAtomValue } from "jotai";

export const Todos = (props: any) => {
  const [expanded, setExpanded] = useState<boolean>(true);
  const userContext = useAtomValue(userContextAtom);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="todos"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="todos-content"
        id="todos-header"
      >
        <TodosToolbar />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
          background: "#f2f2f2",
        }}
      >
        {userContext && (
          <TodosSection
            userContext={userContext}
            hasSignButton={props.hasSignButton}
          />
        )}
      </AccordionDetails>
    </Accordion>
  );
};
