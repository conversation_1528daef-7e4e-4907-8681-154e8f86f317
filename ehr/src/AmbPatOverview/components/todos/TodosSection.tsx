/**
 * TodosSection.tsx
 *
 * @author: jfsys
 * @description Todos section that contains todos data
 */

import { useEffect, useState } from "react";
import { todoItemsAtom } from "../../context/TodosAtoms";
import { toPldModule } from "../../utils/utils";
import {
  getRegId,
  saveEncounter,
  serverRequest,
} from "../../utils/DataRequests";
import {
  NoteIcon,
  OrderEntryIcon,
  AddIcon,
  ChargesIcon,
  IconExternalLink,
  ChiefComplaintIcon,
  HealthProfileIcon,
  HealthMaintenanceIcon,
  VitalsIcon,
} from "@cci-monorepo/common";
import { Box, Stack, Typography } from "@mui/material";
import { useAtom, useAtomValue } from "jotai";
import { hasReviewedReasonAtom } from "../../context/ReasonAtoms";
import { hasReviewedAllergyAtom } from "../../context/AllergyAtoms";
import { hasReviewedFamilyHxAtom } from "../../context/FamilyAtoms";
import {
  hasReviewedSubstanceUseAtom,
  substanceUseNoneAtom,
  hasReviewedTobaccoAtom,
  tobaccoNoneAtom,
} from "../../context/SocialHistoryAtoms";
import {
  hasReviewedAlcoholAtom,
  noneAlcoholAtom,
} from "../../context/AlcoholAtoms";
import {
  hasReviewedProcedureAtom,
  noneAtom,
} from "../../context/ProceduresAtoms";
import {
  hasReviewedImplantsAtom,
  implantsNoneAtom,
} from "../../context/ImplantsAtoms";
import { hasReviewedPregnancyAtom } from "../../context/PregnancyAtoms";
import {
  hasReviewedConcernsAtom,
  concernsNoneAtom,
} from "../../context/ConcernAtoms";
import {
  hasReviewedPrevAtom,
  hasReviewedHealthAtom,
  hasReviewedImmunizationAtom,
} from "../../context/HealthMaintenanceAtoms";
import { hasReviewedVitalsAtom } from "../../context/VitalSignsAtoms";
import { isHiddenPregnancyAtom } from "../../context/HistoryAtoms";
import { hasReviewedProblemsAtom } from "@cci-monorepo/AmbPatOverview/context/ProblemsAtoms";
import { convertArrayToObject } from "../reason/ReasonUtils";
import { USERCONTEXT } from "../common/Constants";
import ClinicalChargeToolDrawer from "@cci-monorepo/ClinicalChargeTool/components/ClinicalChargeToolDrawer";
import { INBOX_CONFIG } from "@cci-monorepo/GlobalInbox/constants/Constants";
import { MAIN_TABS } from "@oe-src/config/Constants";
import { Button } from "@cci/mui-components";
import { IconLock } from "@cci-monorepo/DischargeSummary/icons";

const NoRowsOverlay = () => {
  return (
    <Stack
      display="flex"
      alignItems="center"
      justifyContent="center"
      sx={{ padding: "0x", height: "100%" }}
    >
      <Typography
        sx={{
          padding: "14px 0px",
          font: "400 24px Raleway",
          color: "rgba(0, 0, 0, 0.44)",
          textAlign: "center",
        }}
      >
        All items completed!
      </Typography>
    </Stack>
  );
};

export const TodosSection = (props: any) => {
  const { userContext, hasSignButton = false } = props;
  const [todoItems, setTodoItems] = useAtom(todoItemsAtom);
  const hasReviewedReason = useAtomValue(hasReviewedReasonAtom);
  const hasReviewedProblems = useAtomValue(hasReviewedProblemsAtom);
  const hasReviewedAllergy = useAtomValue(hasReviewedAllergyAtom);
  const hasReviewedPreg = useAtomValue(hasReviewedPregnancyAtom);
  const hasReviewedAlcohol = useAtomValue(hasReviewedAlcoholAtom);
  const hasReviewedSU = useAtomValue(hasReviewedSubstanceUseAtom);
  const hasReviewedTobacco = useAtomValue(hasReviewedTobaccoAtom);
  const hasReviewedFamily = useAtomValue(hasReviewedFamilyHxAtom);
  const hasReviewedConcern = useAtomValue(hasReviewedConcernsAtom);
  const hasReviewedProc = useAtomValue(hasReviewedProcedureAtom);
  const hasReviewedImpl = useAtomValue(hasReviewedImplantsAtom);
  const hasReviewedMeasures = useAtomValue(hasReviewedPrevAtom);
  const hasReviewedHealth = useAtomValue(hasReviewedHealthAtom);
  const hasReviewedImmunization = useAtomValue(hasReviewedImmunizationAtom);
  const hasReviewedVitals = useAtomValue(hasReviewedVitalsAtom);
  const noneAlcohol = useAtomValue(noneAlcoholAtom);
  const socialHistoryNone = useAtomValue(substanceUseNoneAtom);
  const tobaccoNone = useAtomValue(tobaccoNoneAtom);
  const proceduresNone = useAtomValue(noneAtom);
  const implantsNone = useAtomValue(implantsNoneAtom);
  const concernsNone = useAtomValue(concernsNoneAtom);
  const isHiddenPregnancy = useAtomValue(isHiddenPregnancyAtom);

  const [defaultTodoItems, setDefaultTodoItems] = useState<any[]>([]);
  const [allModulesReviewed, setAllModulesReviewed] = useState<boolean>(false);
  const [chargeDrawerOpen, setChargeDrawerOpen] = useState<boolean>(false);
  const [signDisabled, setSignDisabled] = useState<boolean>(true);
  const [regid, setRegid] = useState<number>(0);

  const handleRedirectVitals = () => {
    Cci.RunTime.onLaunchApp2({
      list: "fs",
      app: "Vitals",
    });
  };

  useEffect(() => {
    if (hasSignButton) {
      getPatInfo();
      getRegId(Cci.util.Patient.getMrn(), (data) => {
        setRegid(data);
      });
    } else {
      getData("");
    }
    // eslint-disable-next-line
  }, []);

  const getData = async (encStatus: string) => {
    const onSuccess = (rsp: any) => {
      let todoList: string[] = [];
      if (userContext === USERCONTEXT.NURSE) {
        let chc = rsp.choicelist ? rsp.choicelist : rsp;
        let data = chc.data as any[];
        data.forEach((c: any) => {
          if (c && c.length > 0) {
            todoList.push(c[0]);
          }
        });
      } else {
        const data = convertArrayToObject(rsp.data);

        const todos = JSON.parse(data[0].todos);
        todos.forEach((c: any) => {
          if (c?.[1] === 1) {
            todoList.push(c[0]);
          }
        });
        setSignDisabled(todoList.length > 0 || encStatus === "Signed");
      }
      const allTodoModules: any = [
        {
          title: "Note Incomplete",
          name: "notes",
          icon: <NoteIcon />,
          handleRedirect: () =>
            Cci.RunTime.onLaunchApp2({
              list: "notes",
              app: "Notes Menu",
            }),
          isShow: todoList.includes("Note"),
        },
        {
          title: "Charge Incomplete",
          name: "charge",
          icon: <ChargesIcon />,
          handleRedirect: () => setChargeDrawerOpen(true),
          isShow: todoList.includes("Charge"),
        },
        {
          title: "Add Diagnosis",
          name: "diagnosis",
          icon: <AddIcon />,
          handleRedirect: () => {
            toPldModule("Problems");
            cci.AmbPatOpenPldDialog = true;
          },
          isShow: todoList.includes("Diagnosis"),
        },
        {
          title: "Med Rec Incomplete",
          name: "medRec",
          icon: <OrderEntryIcon />,
          handleRedirect: () => {
            Cci.RunTime?.setCache(INBOX_CONFIG, {
              params: {
                entry: "OE",
                targetTab: {
                  tabValue: MAIN_TABS.MED_RECON,
                },
              },
            });
            Cci.RunTime.onLaunchApp2({
              list: "cpoe_psat",
            });
          },
          isShow: todoList.includes("MedRec"),
        },
        {
          title: "Review Chief Complaint",
          name: "chiefComplaint",
          icon: <ChiefComplaintIcon />,
          handleRedirect: () => {},
          isHideLink: true,
          isShow: todoList.includes("ChiefComplaint"),
        },
        {
          title: "Review Health Profile",
          name: "healthProfile",
          icon: <HealthProfileIcon />,
          handleRedirect: () => toPldModule("Allergies"),
          isShow: todoList.includes("HealthProfile"),
        },
        {
          title: "Review Health Maintenance",
          name: "healthMaintenance",
          icon: <HealthMaintenanceIcon />,
          handleRedirect: () => {},
          isHideLink: true,
          isShow: todoList.includes("HealthMaintenance"),
        },
        {
          title: "Record Vital Signs",
          name: "vital",
          icon: <VitalsIcon />,
          handleRedirect: handleRedirectVitals,
          isHideLink: true,
          isShow: todoList.includes("Vital"),
        },
      ];
      const showTodoModules = allTodoModules.filter((item: any) => item.isShow);
      setDefaultTodoItems(showTodoModules);
    };

    const onFailure = (error: string) => {
      setDefaultTodoItems([]);
    };

    if (userContext === USERCONTEXT.NURSE) {
      let params = {
        chc: "todos_" + userContext,
      };
      serverRequest("getchoice2", params, onSuccess, onFailure);
    } else {
      let params = {
        dbpath: Cci.util.Patient.getDbpath(),
      };
      serverRequest("ambPatOverview/todos", params, onSuccess, onFailure);
    }
  };

  const getPatInfo = async () => {
    const onSuccess = (rsp: any) => {
      let todoList: string[] = [];
      const data = convertArrayToObject(rsp.data);
      if (data && data.length > 0) {
        getData(data[0].encStatus);
      }
    };
    const onFailure = (error: string) => {
      setDefaultTodoItems([]);
    };
    serverRequest(
      "ess/getpatinfo",
      { dbp: Cci.util.Patient.getDbpath() },
      onSuccess,
      onFailure
    );
  };

  const signEncounter = () => {
    setSignDisabled(true);
    let data = {
      encounterStatus: "Signed",
      patientRegId: Number(regid),
      encounterId: Number(Cci.util.Patient.getPatId()),
      regEncLink: Cci.util.Patient.getDbpath(),
    };
    saveEncounter(
      "EENC",
      data,
      data.regEncLink,
      data.encounterStatus,
      Cci.util.Staff.getSid()
    );
  };

  useEffect(() => {
    if (defaultTodoItems.length === 0) {
      return;
    }
    let showTodoModules = defaultTodoItems.filter((item: any) => {
      if (
        item.name === "notes" ||
        item.name === "charge" ||
        item.name === "diagnosis" ||
        item.name === "medRec" ||
        (item.name === "chiefComplaint" && !hasReviewedReason) ||
        (item.name === "healthProfile" && !allModulesReviewed) ||
        (item.name === "healthMaintenance" &&
          !hasReviewedImmunization &&
          !(hasReviewedMeasures && hasReviewedHealth)) ||
        (item.name === "vital" && !hasReviewedVitals)
      ) {
        return true;
      } else {
        return false;
      }
    });
    setTodoItems(showTodoModules);
  }, [
    defaultTodoItems,
    hasReviewedReason,
    allModulesReviewed,
    hasReviewedMeasures,
    hasReviewedHealth,
    hasReviewedVitals,
    hasReviewedImmunization,
  ]);

  useEffect(() => {
    if (
      hasReviewedProblems &&
      hasReviewedAllergy &&
      (isHiddenPregnancy || hasReviewedPreg) &&
      (hasReviewedAlcohol || noneAlcohol) &&
      (hasReviewedSU || socialHistoryNone) &&
      (hasReviewedTobacco || tobaccoNone) &&
      hasReviewedFamily &&
      (hasReviewedConcern || concernsNone) &&
      (hasReviewedProc || proceduresNone) &&
      (hasReviewedImpl || implantsNone)
    ) {
      setAllModulesReviewed(true);
    } else {
      setAllModulesReviewed(false);
    }
  }, [
    hasReviewedProblems,
    hasReviewedAllergy,
    hasReviewedPreg,
    hasReviewedAlcohol,
    hasReviewedSU,
    hasReviewedTobacco,
    hasReviewedFamily,
    hasReviewedConcern,
    hasReviewedProc,
    hasReviewedImpl,
    isHiddenPregnancy,
    noneAlcohol,
    socialHistoryNone,
    tobaccoNone,
    proceduresNone,
    implantsNone,
    concernsNone,
    setAllModulesReviewed,
  ]);

  return (
    <>
      <Box>
        {hasSignButton && todoItems.length > 0 && (
          <Box
            sx={{
              padding: "8px",
            }}
          >
            <Typography
              noWrap
              sx={{
                font: "400 15px Roboto",
                color: "rgba(0, 0, 0, 0.70)",
              }}
            >
              Complete all To-Dos before signing the encounter.
            </Typography>
          </Box>
        )}
        {todoItems.length > 0 ? (
          <Box
            display="grid"
            gridTemplateColumns="repeat(auto-fit, minmax(242px, 1fr))"
            gap="8px"
            padding="8px"
          >
            {todoItems.map((item: any, index: number) => (
              <Stack
                key={index}
                direction="row"
                justifyContent="space-between"
                sx={{
                  padding: "8px",
                  background: "#fff",
                  borderRadius: "10px",
                  boxShadow: "0px 0px 4px 0px rgba(0, 0, 0, 0.25)",
                  minWidth: "242px",
                }}
              >
                <Stack direction="row">
                  <Box
                    height="24px"
                    width="8px"
                    sx={{
                      background: "#652F8F",
                      borderRadius: "16px",
                      marginRight: "8px",
                    }}
                  ></Box>
                  {item.icon}
                  <Typography
                    noWrap
                    sx={{
                      font: "500 16px Roboto",
                      lineHeight: "24px",
                      marginLeft: "2px",
                    }}
                  >
                    {item.title}
                  </Typography>
                </Stack>
                {!item.isHideLink && (
                  <IconExternalLink
                    onClick={item.handleRedirect}
                    style={{ cursor: "pointer" }}
                  />
                )}
              </Stack>
            ))}
          </Box>
        ) : (
          <NoRowsOverlay />
        )}
        {hasSignButton && (
          <Button
            disabled={signDisabled}
            sx={{
              m: "8px",
              width: "200px",
              padding: "4px 8px 4px 6px",
              background: "#498FD8",
              border: "none",
              "&:focus": {
                outline: "none",
              },
              "&:disabled": {
                background: "#C0C0C0",
              },
              "&:hover": {
                background: "#498FD8",
              },
            }}
            data-testid="expand-pdf-btn"
            onClick={signEncounter}
          >
            <IconLock />
            <Typography
              noWrap
              sx={{
                font: "700 14px Roboto",
                lineHeight: "24px",
                marginLeft: "12px",
                color: "#FFF",
              }}
            >
              {"Sign & Lock Encounter"}
            </Typography>
          </Button>
        )}
      </Box>
      <ClinicalChargeToolDrawer
        open={chargeDrawerOpen}
        setOpen={setChargeDrawerOpen}
      />
    </>
  );
};
