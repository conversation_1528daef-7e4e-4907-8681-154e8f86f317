/**
 * TodosToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Todos
 */

import { useAtomValue } from "jotai";
import { Grid, Typography } from "@mui/material";

import { ModuleHeaderSx } from "../common/Constants";
import { todoItemsAtom } from "../../context/TodosAtoms";

export const TodosToolbar = () => {
  const todoItems = useAtomValue(todoItemsAtom);

  return (
    <>
      <Grid
        container
        spacing={"8px"}
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        sx={{
          marginTop: "-8px",
          opacity: 1,
        }}
      >
        <Grid item sx={{ paddingTop: "6px" }}>
          <Typography sx={ModuleHeaderSx}>
            To-Dos ({todoItems!.length})
          </Typography>
        </Grid>
      </Grid>
    </>
  );
};
