/**
 * Notes.tsx
 *
 * @author: jfsys
 * @description Notes component
 */

import { useEffect, useState } from "react";
import { useAtomValue, useAtom } from "jotai";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { NotesToolbar } from "./NotesToolbar";
import { ModuleHeaderHeight, notePageSize } from "../common/Constants";
import { NotesSection } from "./NotesSection";
import { rowDataNotesAtom, pageAtom } from "../../context/NoteAtoms";
import { AdditionalFooter } from "../common/AdditionalFooter";

export const Notes = () => {
  const rowData = useAtomValue(rowDataNotesAtom);
  const [page, setPage] = useAtom(pageAtom);
  const [expanded, setExpanded] = useState<boolean>(true);

  useEffect(() => {
    setExpanded(rowData.totalNum > 0 ? true : false);
  }, [rowData]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="notes"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="notes-content"
        id="notes-header"
      >
        <NotesToolbar />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <NotesSection expanded={expanded} />
        {rowData.totalNum > notePageSize * page && (
          <AdditionalFooter
            msg="Load More"
            handleClick={() => setPage((preVal) => preVal + 1)}
          />
        )}
      </AccordionDetails>
    </Accordion>
  );
};
