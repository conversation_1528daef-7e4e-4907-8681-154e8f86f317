/**
 * NotesToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Notes
 */

import { PopUpIcon } from "@cci-monorepo/common";
import { Button } from "@cci/mui-components";
import { Box, Grid, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";

interface NoteDialogContentProps {
  rowValue: any;
  handleEdit: () => void;
  onClose: () => void;
}
export const NoteDialogContent = (props: NoteDialogContentProps) => {
  const { rowValue, onClose, handleEdit } = props;

  const data = [
    {
      title: "Reason for Admission",
      msg: "This G1 P1 patient at 39 W 5 Day gestation presented following a motor vehicle collision.",
    },
    {
      title: "History of Present Illness",
      msg: "The patient was involved in a motor vehicle collision on Tuesday, 5/9.  Details regarding the accident are limited.  She was brought to the Emergency Department by EMS, complaining of shortness of breath and anxiety about the baby.",
    },
    {
      title: "Hospital Course",
      msg: "The patient underwent a comprehensive evaluation in the ED, including an X-ray, CT scan, and prenatal US.  Fetal non-stress testing was reassuring throughout her admission. She was continued on fetal monitoring while in the ICU and on the med/surge unit.",
    },
    {
      title: "Discharge Diagnosis",
      msg: "Motor vehicle collision with aggravated tendonitis related to the accident.",
    },
  ];

  const StyledButton = styled(Button)({
    backgroundColor: "#fff",
    border: "1px solid #4B6EAF",
    color: "#4B6EAF",
    fontWeight: "700",
    width: "70px",
    "&:hover": {
      backgroundColor: "#fff",
    },
    "&:focus": {
      outline: "none",
      boxShadow: "none",
    },
  });

  return (
    <Box
      sx={{
        background: "#fff",
        minHeight: "calc(100% - 48px)",
        borderRadius: "8px",
        margin: "16px",
        padding: "8px",
      }}
    >
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item>
          <Typography
            sx={{
              font: "normal 400 15px Roboto",
              color: "rgba(0, 0, 0, 0.58)",
              lineHeight: "24px",
            }}
          >
            Last stored by {rowValue?.storeby} at {rowValue?.storeat}
          </Typography>
        </Grid>
        <Grid item>
          <StyledButton onClick={onClose}>Close</StyledButton>
          <StyledButton onClick={handleEdit} sx={{ marginLeft: "10px" }}>
            <PopUpIcon /> Edit
          </StyledButton>
        </Grid>
      </Grid>

      {data.map((item: any, index: number) => (
        <Box key={index} sx={{ padding: "8px", marginTop: "4px" }}>
          <Typography sx={{ font: "normal 500 16px Roboto" }}>
            {item.title}:
          </Typography>
          <Box
            sx={{
              padding: "10px 12px",
              boxShadow: "0px 0px 6px 0px rgba(0, 0, 0, 0.15)",
              margin: "4px 0",
            }}
          >
            <Typography
              sx={{ font: "normal 400 15px Roboto", lineHeight: "24px" }}
            >
              {item.msg}
            </Typography>
          </Box>
        </Box>
      ))}
    </Box>
  );
};
