/**
 * NotesSection.tsx
 *
 * @author: jfsys
 * @description Data grid accordion that contains notes data
 */

import { useAtom, useAtomValue } from "jotai";
import { rowDataNotesAtom, pageAtom } from "../../context/NoteAtoms";
import { TooltipCommonDataGrid } from "../common/TooltipCommonDataGrid";
import { useEffect, useState, useMemo } from "react";
import { CciDialog } from "@cci/mui-components";
import { Box } from "@mui/material";
import { NoteDialogContent } from "./NoteDialogContent";
import { dateComparator } from "@cci-monorepo/common";
import { notePageSize } from "../common/Constants";

interface NotesSectionProps {
  expanded: boolean;
}

export const NotesSection = (props: NotesSectionProps) => {
  const [rowDataNotes, setRowDataNotes] = useAtom(rowDataNotesAtom);
  const page = useAtomValue(pageAtom);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [rowValue, setRowValue] = useState<any>();
  const columnDataNotes = [
    { field: "name", headerName: "Note", minWidth: 150, flex: 1 },
    { field: "storeby", headerName: "Stored By", width: 162 },
    {
      field: "displayStoredAt",
      headerName: "Stored At",
      sortComparator: dateComparator,
      width: 133,
    },
  ];

  useEffect(() => {
    getData();
    // eslint-disable-next-line
  }, []);

  const getData = async () => {
    const response = await Cci.util.Hobj.requestUnorderedRecords({
      hobj: "ambPatOverview/notes",
      dbs: [Cci.util.Patient.getDbpath()],
      params: { staffid: Cci.util.Staff.getSid() },
    });

    const rowData = {
      notes: response.notes?.map((note: any, index: number) => {
        let status = "";
        if (note.status?.length > 0) {
          let statusList = JSON.parse(note.status);
          statusList.sort((a: any, b: any) => b.time - a.time);
          status = statusList[0].msg;
        }
        return {
          ...note,
          displayStoredAt: note?.storeat?.split(" ")[2],
          index,
          status,
        };
      }),
      totalNum: response?.total?.[0]?.total,
    };

    setRowDataNotes(rowData);
  };

  const handleRowClick = (data: any) => {
    setDialogOpen(true);
    setRowValue(data.row);
  };

  const handleEdit = () => {
    const headerInfo = {
      majorIT: "2305",
      minorIT: rowValue.nit,
      notetime: rowValue.notetime,
      type: rowValue.name,
      notefile: rowValue.tplfile,
      exStatus: [{ edflag: rowValue.editflag }],
    };
    Cci.RunTime.onLaunchApp(
      Cci.RunTime.mainController.getController("Cps.controller.CpsController"),
      { srvconf: "notes" }
    );
    setTimeout(function () {
      Cci.RunTime.mainController
        .getController("Notes.controller.NotesController")
        .doNote(headerInfo, true);
    }, 200);
  };

  const displayData = useMemo(() => {
    return rowDataNotes?.totalNum >= notePageSize * page
      ? rowDataNotes?.notes.slice(0, notePageSize * page)
      : rowDataNotes?.notes;
  }, [rowDataNotes, page]);

  return (
    <Box>
      {props.expanded ? (
        <TooltipCommonDataGrid
          type="notes"
          getRowId={(row: any) => row.index}
          rows={displayData}
          columns={columnDataNotes}
          noDataMsg="No notes documented"
          onRowDoubleClick={handleRowClick}
          disableColumnMenu
          disableColumnResize
          autosizeOnMount={false}
        />
      ) : null}

      {dialogOpen && (
        <CciDialog
          open={dialogOpen}
          setOpen={setDialogOpen}
          title={rowValue.name}
          content={
            <NoteDialogContent
              rowValue={rowValue}
              onClose={() => setDialogOpen(false)}
              handleEdit={handleEdit}
            />
          }
          sx={{
            "& .MuiDialogContent-root": {
              width: "808px",
              height: "800px",

              padding: "0",
            },
            "& .MuiDialogTitle-root": {
              padding: "16px 24px",
            },
            "& hr": {
              display: "none",
            },
            "& .MuiDialogActions-root.MuiDialogActions-spacing": {
              display: "none",
            },
          }}
        />
      )}
    </Box>
  );
};
