/**
 * NotesToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Notes
 */

import { useAtomValue } from "jotai";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink } from "../../../common/assets";
import { pageAtom, rowDataNotesAtom } from "../../context/NoteAtoms";
import { notePageSize, USERCONTEXT } from "../common/Constants";
import { userContextAtom } from "../../context/CommonAtoms";
import { AddBlueIcon } from "@cci-monorepo/common";

export const NotesToolbar = () => {
  const rowData = useAtomValue(rowDataNotesAtom);
  const page = useAtomValue(pageAtom);
  const userContext = useAtomValue(userContextAtom);

  const handleRedirect = () => {
    Cci.RunTime.onLaunchApp2({
      list: "notes",
      app: "Notes Menu",
    });
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "View In Notes Application",
      icon: <IconExternalLink />,
      handler: handleRedirect,
    },
    {
      label: "Create Nursing Note",
      icon: <AddBlueIcon />,
      handler: handleRedirect,
      hidden: userContext == USERCONTEXT.NURSE ? false : true,
    },
  ];

  return (
    <CommonToolbar
      name="Clinical Notes"
      isPldWidget={false}
      count={page * notePageSize}
      totalCount={rowData.totalNum}
      menuItems={menuItems}
    />
  );
};
