import React from "react";
import { TooltipCommonDataGrid } from "../../common/TooltipCommonDataGrid";
import { useAtomValue, useSetAtom } from "jotai";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import {
  rowDataImplantsAtom,
  setParseDataImplantsAtom,
  setReviewMessageImplantsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ImplantsAtoms";
import { isShowInactiveImplAtom } from "@cci-monorepo/AmbPatOverview/context/HistoryAtoms";
import { dateComparator, useSetErrorDialog } from "@cci-monorepo/common";
import { getReviewHistory } from "./ImplantsUtils";
import { toPldWithPayLoad } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import {
  ImplantCheckedNoneText,
  ImplantUncheckedNoneText,
} from "@cci-monorepo/Pld/components/common/Constants";
import CommonRenderCell from "../../common/CommonRenderCell";
import {
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../../common/Constants";
import { HistoryToolbar } from "../HistoryToolbar";

export const Implants = (props: any) => {
  const [hasData, setHasData] = React.useState(false);
  const isShowInActive = useAtomValue(isShowInactiveImplAtom);
  const rowData = useAtomValue(rowDataImplantsAtom);
  const setOpenErrorDialog = useSetErrorDialog();

  const parseImplantsData = useSetAtom(setParseDataImplantsAtom);
  const setReviewMessageImplants = useSetAtom(setReviewMessageImplantsAtom);
  const [expanded, setExpanded] = React.useState(false);

  const activeRows = React.useMemo(() => {
    return rowData.filter((row: { [key: string]: string | number }) => {
      return String(row.status).toUpperCase() === "ACTIVE";
    });
  }, [rowData]);

  const displayRows = React.useMemo(() => {
    if (isShowInActive) {
      return rowData;
    } else {
      return activeRows;
    }
  }, [isShowInActive, rowData, activeRows]);

  const columnDefs = [
    {
      field: "status",
      headerName: "Status",
      width: 140,
      disableColumnMenu: false,
      valueOptions: ["Active", "Inactive", "Error"],
      renderCell: CommonRenderCell,
    },
    {
      field: "device",
      headerName: "Implanted Device",
      minWidth: 150,
      flex: 1,
      disableColumnMenu: true,
      renderCell: (params: GridRenderCellParams<any>) => (
        <div
          style={{
            font: "normal 400 15px Roboto",
            color: "#000",
            overflow: "hidden",
            textOverflow: "ellipsis",
            fontStyle: params.row.status === "Inactive" ? "italic" : "normal",
            textDecoration:
              params.row.status === "Error" ? "line-through" : "none",
          }}
        >
          {params.row.udi !== ImplantCheckedNoneText &&
          params.row.udi !== ImplantUncheckedNoneText
            ? params.value
            : params.value + " " + params.row.fmtlgtime}
        </div>
      ),
    },
    {
      field: "laterality",
      headerName: "Laterality",
      width: 140,
      disableColumnMenu: true,
      renderCell: CommonRenderCell,
    },
    {
      field: "location",
      headerName: "Location",
      width: 140,
      disableColumnMenu: true,
      renderCell: CommonRenderCell,
    },
    {
      field: "implantDate",
      headerName: "Date of Implant",
      width: 190,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: CommonRenderCell,
    },
  ];

  React.useEffect(() => {
    setExpanded(activeRows.length > 0);
  }, [setExpanded, activeRows]);

  React.useEffect(() => {
    if (!hasData) {
      const onSuccess = (result: any) => {
        if (result && result.length > 0) {
          parseImplantsData(result, 0);
          setHasData(true);
        }
      };

      const onFailure = (error: string) => {
        setOpenErrorDialog({
          text: `System has failed to load implants data: ${error}`,
          open: true,
        });
      };

      const onHistorySuccess = (data: any) => {
        setReviewMessageImplants(getReviewHistory(data));
      };

      const onHistoryFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load review history. " + error,
          open: true,
        });
      };

      const dbpaths = [
        { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
      ];
      let promises = dbpaths.map((item: any, index) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        return serverRequest("pld/implants", params, () => {}, onFailure);
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
        const params = {
          campus: cci.cfg.campus,
          dbs: Cci.util.Patient.getDbpath(),
        };
        return serverRequest(
          "pld/implants/review_history",
          params,
          onHistorySuccess,
          onHistoryFailure
        );
      });
    }
  }, [
    hasData,
    rowData,
    parseImplantsData,
    setOpenErrorDialog,
    setReviewMessageImplants,
  ]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <HistoryToolbar
          type="implant"
          count={displayRows.length}
          readonly={props.readonly}
        />
      </AccordionSummary>

      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        {expanded && (
          <TooltipCommonDataGrid
            type="implant"
            rows={displayRows}
            columns={columnDefs}
            enableStatusColFilter={true}
            noDataMsg="No active implants"
            onRowDoubleClick={(row: any) => {
              toPldWithPayLoad("Implants", {
                key: "lgtime",
                value: row.row.lgtime,
              });
            }}
            columnVisibilityModel={{
              status: isShowInActive,
            }}
          />
        )}
      </AccordionDetails>
    </Accordion>
  );
};
