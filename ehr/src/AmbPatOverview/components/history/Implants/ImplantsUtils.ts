import { randomId } from "@mui/x-data-grid-generator";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import { implantsDBitemsMap } from "@cci-monorepo/Pld/components/common/DataUtils";
import { ImplantCheckedNoneText } from "@cci-monorepo/Pld/components/common/Constants";

// Review History
export const getReviewHistory = (data: any) => {
  let ret: any = [];
  let reviewHistory: any = [];
  const field: string = "implants_reviewhistory";
  if (data[field] && data[field].data.length > 0) {
    reviewHistory.push(convertArrayToObject(data[field]));
  }

  ret = reviewHistory
    .reduce((prev: any, cur: any) => (prev ? prev.concat(cur) : cur), [])
    .sort((a: any, b: any) => {
      return a.reviewed_ts < b.reviewed_ts ? 1 : -1;
    })
    .map((item: any, index: number) => {
      return {
        id: index,
        status: item.status,
        review_status:
          item.status === "unable to assess"
            ? item.status + ", Reason:" + item.reason
            : item.status,
        reason: item.reason,
        user: item.user,
        reviewed_ts: item.reviewed_ts,
        fmt_reviewed_tm: item.fmt_reviewed_tm,
        last_reviewed:
          item.status !== "reviewed"
            ? "User was " +
              item.status +
              (!!item.reason ? ", Reason:" + item.reason : "") +
              " - By " +
              item.user +
              " at " +
              item.fmt_reviewed_tm
            : "Last reviewed by " + item.user + " at " + item.fmt_reviewed_tm,
        chksum: item.chksum,
      };
    });
  return ret;
};

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

// Get rows for the main data grid
const getDisplayRows = (data: any, index: number) => {
  let rows: any = [];
  let gridRows: any = [];
  let implants: any = [];
  let activeRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];
  const field: string = "implants_data";
  if (
    !data[index]["implants_edithistory"] ||
    data[index]["implants_edithistory"].data.length === 0
  ) {
    data.forEach((item: any) => {
      if (item[field] && item[field].data.length > 0) {
        implants.push(convertArrayToObject(item[field]));
      }
    });
  } else if (data[index][field] && data[index][field].data.length > 0) {
    implants.push(convertArrayToObject(data[index][field]));
  }
  implants.forEach((implant: any) => {
    implant.forEach((element: any) => {
      if (
        !rows.some((row: any) => {
          return JSON.stringify(row) === JSON.stringify(element);
        })
      ) {
        rows.push(element);
      }
    });
  });

  // Add id to each row and group them by status
  rows.forEach((row: any) => {
    if (row.status === "Active") {
      activeRows.push({ ...row, id: randomId() });
    } else if (row.status === "Inactive") {
      inactiveRows.push({ ...row, id: randomId() });
    } else {
      errorRows.push({ ...row, id: randomId() });
    }
  });
  // Returns active, followed by inactive and error
  gridRows = activeRows.concat(inactiveRows, errorRows);
  return gridRows;
};

// Parse server data for Implants
export const parseImplantsData = (data: any, index: number) => {
  let datas: any = {};
  let needAutoSave = false;
  let needReload = { value: false };
  let item = data[index];
  if (
    !item.implants_edithistory ||
    item.implants_edithistory.data.length === 0
  ) {
    for (var i = index + 1; i < data.length; i++) {
      const it = data[i];
      if (it.implants_data && it.implants_data.data.length > 0) {
        item = it;
        needAutoSave = true;
        break;
      }
    }
  }
  datas.implants_data = getDisplayRows(data, index);
  if (needAutoSave) {
    const curencKey = Cci.RunTime.getEncounterInfo().admitkey;
    datas.implants_data.forEach((item: any) => {
      let datasArr: any[] = [];
      let nit = item.nit * -1;
      Object.keys(implantsDBitemsMap).forEach((dbitem: string) => {
        let da: any = {};
        da.jit = implantsDBitemsMap[dbitem];
        da.nit = nit;
        da.ks = curencKey;
        da.data = item[dbitem];
        const existrow = datasArr.find(
          (item: any) => item.jit === da.jit && item.nit === da.nit
        );
        if (!existrow) {
          datasArr.push(da);
        }
      });
      const params = {
        group: "pld_implants",
        appname: "UNLOCK",
        fsdata: datasArr,
        perm: "R",
      };
      serverRequest(
        "DBI/savemgdb",
        params,
        () => {
          needReload = { value: true };
        },
        () => {}
      );
    });
  }
  datas.implants_reviewstatus = convertArrayToObject(
    item.implants_reviewstatus
  );
  let hasNone = false;
  datas.implants_data.forEach((row: any) => {
    if (row.status === "Inactive" && row.udi === ImplantCheckedNoneText) {
      hasNone = true;
    }
  });
  return [datas, hasNone, needReload];
};
