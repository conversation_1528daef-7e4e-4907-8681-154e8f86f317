import { randomId } from "@mui/x-data-grid-generator";

// Parse server data for family history
export const parseFamilyHxData = (data: any, index: number) => {
  let mainGridRows: any = [];

  if (data && data.length > 0) {
    mainGridRows = getDisplayRows(data, index);
  }
  return [mainGridRows];
};

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

// Get rows for the main data grid
const getDisplayRows = (data: any, index: number) => {
  let rows: any = [];
  let gridRows: any = [];
  let arr: any = [];
  let arr2: any = [];
  let arrOrder: any = [];
  const field: string = "data";
  if (
    !data[index]["familyhx_history"] ||
    data[index]["familyhx_history"].data.length === 0
  ) {
    data.forEach((item: any, i: number) => {
      if (item[field] && item[field].data.length > 0) {
        arrOrder.push({ k: item["familyhx_adopted"]["data"][0][1], v: i });
        arr.push(convertArrayToObject(item[field]));
      }
    });
  } else if (data[index][field] && data[index][field].data.length > 0) {
    arr2.push(convertArrayToObject(data[index][field]));
  }
  // Ensure the latest records with same uuid be present
  arrOrder.sort((a: any, b: any) => (a.k < b.k ? 1 : a.k > b.k ? -1 : 0));
  arrOrder.forEach((e: any) => {
    arr2.push(arr[e.v - 1]);
  });

  arr2.forEach((fhx: any) => {
    fhx.forEach((element: any, index: number) => {
      if (
        !rows.some((row: any) => {
          return row.uuid === element.uuid;
        })
      ) {
        rows.push(element);
      }
    });
  });
  rows.sort((a: any, b: any) => {
    return a.problem > b.problem ? 1 : -1;
  });
  let activeRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];

  // Add id to each row and group them by status
  rows.forEach((row: any) => {
    if (row.status === "Active") {
      activeRows.push({ ...row, id: randomId() });
    } else if (row.status === "Inactive") {
      inactiveRows.push({ ...row, id: randomId() });
    } else {
      errorRows.push({ ...row, id: randomId() });
    }
  });
  // Returns active, followed by inactive and error
  gridRows = activeRows.concat(inactiveRows, errorRows);
  return gridRows;
};

// Review History
export const getReviewHistory = (data: any, field: string) => {
  let ret: any = [];
  let reviewHistory: any = [];
  if (data[field] && data[field].data.length > 0) {
    reviewHistory.push(convertArrayToObject(data[field]));
  }

  ret = reviewHistory
    .reduce((prev: any, cur: any) => (prev ? prev.concat(cur) : cur), [])
    .sort((a: any, b: any) => {
      return a.reviewed_ts < b.reviewed_ts ? 1 : -1;
    })
    .map((item: any, index: number) => {
      return {
        id: index,
        status: item.status,
        review_status:
          item.status === "unable to assess"
            ? item.status + ", Reason:" + item.reason
            : item.status,
        reason: item.reason,
        user: item.user,
        reviewed_ts: item.reviewed_ts,
        fmt_reviewed_tm: item.fmt_reviewed_tm,
        last_reviewed:
          item.status !== "reviewed"
            ? "User was " +
              item.status +
              (!!item.reason ? ", Reason:" + item.reason : "") +
              " - By " +
              item.user +
              " at " +
              item.fmt_reviewed_tm
            : "Last reviewed by " + item.user + " at " + item.fmt_reviewed_tm,
        chksum: item.chksum,
      };
    });
  return ret;
};
