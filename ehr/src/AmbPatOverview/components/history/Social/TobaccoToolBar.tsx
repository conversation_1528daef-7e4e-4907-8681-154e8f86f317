import { Box } from "@mui/material";
import { SocialSubToolBar } from "./SocialSubToolBar";
import { useAtomValue } from "jotai";
import {
  hasReviewedTobaccoAtom,
  tobaccoNoneAtom,
  reviewMessageTobaccoAtom,
  tobaccoCountAtom,
} from "@cci-monorepo/AmbPatOverview/context/SocialHistoryAtoms";

export const TobaccoToolBar = () => {
  const hasReviewedTobacco = useAtomValue(hasReviewedTobaccoAtom);
  const tobaccoNone = useAtomValue(tobaccoNoneAtom);
  const reviewMessageTobacco = useAtomValue(reviewMessageTobaccoAtom);
  const tobaccoCount = useAtomValue(tobaccoCountAtom);

  const formatTooltip = (
    moduleName: string,
    hasReviewed: Boolean,
    reviewMessage: any[]
  ) => {
    if (hasReviewed) {
      return `${reviewMessage.length > 0 && reviewMessage[0].last_reviewed}`;
    } else {
      return `${moduleName} Not Reviewed`;
    }
  };

  return (
    <Box>
      <SocialSubToolBar
        name="Tobacco"
        count={tobaccoCount}
        hasReviewed={hasReviewedTobacco || tobaccoNone}
        iconTooltip={formatTooltip(
          "Tobacco",
          hasReviewedTobacco,
          reviewMessageTobacco
        )}
      />
    </Box>
  );
};
