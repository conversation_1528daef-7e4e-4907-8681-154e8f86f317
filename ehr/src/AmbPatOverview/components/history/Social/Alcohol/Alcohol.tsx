import React from "react";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Grid, Box } from "@mui/material";
import { pldDbpathsAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";
import {
  allDataAlcoholAtom,
  currentAlcoholDataAtom,
  socialHistoryALModuleStatusAtom,
  emptyAlcoholData,
  setParseDataAlcoholAtom,
} from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import AlcoholHistoryScore from "./AlcoholHistoryScore";
import AlcoholHistoryScoreNoData from "./AlcoholHistoryScoreNoData";
import AlcoholQuestionDetails from "./AlcoholQuestionDetails";
import { InitEncounterData } from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";
import { cloneDeep } from "lodash";
import { getAlcohol } from "@cci-monorepo/Pld/util/SocialHistoryAPI";
import { AlcoholTooltip } from "./AlcoholTooltip";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { NoRowsOverlay } from "@cci-monorepo/AmbPatOverview/components/common/CommonDataGrid";

export const Alcohol = ({ loadReviewHistory }: any) => {
  const pldDbpaths = useAtomValue(pldDbpathsAtom);
  const moduleStatus = useAtomValue(socialHistoryALModuleStatusAtom);
  const [hasAlcoholData, setHasAlcoholData] = React.useState(false);

  const initSavedKV: any[] = [];
  const [savedValue] = React.useState(initSavedKV);
  const initEncData: any[] = [];
  const [encData, setEncData] = React.useState(initEncData);

  const [alcoholData, setAlcoholData] = useAtom(currentAlcoholDataAtom);
  const [allAlcoholData, setAllAlcoholData] = useAtom(allDataAlcoholAtom);

  const parseAlcoholData = useSetAtom(setParseDataAlcoholAtom);

  React.useEffect(() => {
    if (!hasAlcoholData && pldDbpaths.length) {
      setAlcoholData(emptyAlcoholData);
      const iniAllData: any[] = [];
      setAllAlcoholData(iniAllData);

      const onSuccess = (data: any) => {
        setHasAlcoholData(true);
        let isCurAlcoholDataEmpty = true;
        Object.keys(emptyAlcoholData).forEach((key: any) => {
          if (emptyAlcoholData[key] !== alcoholData[key]) {
            isCurAlcoholDataEmpty = false;
          }
        });
        if (isCurAlcoholDataEmpty && allAlcoholData.length > 1) {
          for (const [, v] of Object.entries(allAlcoholData[1])) {
            const encAlcoholData: any = cloneDeep(v);
            setAlcoholData(encAlcoholData);
          }
        }
        loadReviewHistory();
      };

      const onFailure = (error: string) => {
        console.error(
          `System has failed to load social history[alcohol] data: ${error}`
        );
      };

      let promises = pldDbpaths.map((item: any, index: number) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        return getAlcohol(
          params,
          (data: any) => {
            parseAlcoholData(data, index);
          },
          onFailure
        );
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pldDbpaths]);

  React.useEffect(() => {
    setEncData(
      InitEncounterData(
        allAlcoholData,
        moduleStatus.reviewed ? alcoholData : null
      )
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allAlcoholData, alcoholData, moduleStatus, savedValue, setAlcoholData]);

  return (
    <>
      {encData && encData.length > 0 ? (
        <Box
          onDoubleClick={() => {
            toPldModule("SocialHistoryAlcohol");
          }}
        >
          <Grid
            container
            style={{
              backgroundColor: "white",
              width: "100%",
            }}
            direction="column"
          >
            <Grid
              item
              padding="16px"
              style={{
                backgroundColor: "white",
                width: "100%",
              }}
            >
              <Grid
                container
                spacing={0}
                wrap="nowrap"
                sx={{ overflow: "auto" }}
                padding="0"
                alignItems="center"
              >
                <Grid item>
                  {encData && encData.length > 1 ? (
                    <AlcoholHistoryScore />
                  ) : (
                    <AlcoholHistoryScoreNoData />
                  )}
                </Grid>
                <Grid item>
                  <AlcoholQuestionDetails />
                </Grid>
              </Grid>
            </Grid>
            <Grid item>
              <AlcoholTooltip>
                <Box
                  sx={{
                    padding: "20px",
                    color: "#626262",
                    textAlign: "left",
                    fontFamily: "Roboto",
                    fontSize: "13px",
                    fontStyle: "normal",
                    fontWeight: "400",
                    lineHeight: "normal",
                  }}
                >
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  Vivamus gravida orci non libero dapibus, ac cursus nisl
                  interdum. Nullam ac metus id turpis facilisis gravida.
                  Praesent scelerisque ligula sit amet orci consectetur, nec
                  tincidunt felis pharetra. In id justo ligula. Integer sit amet
                  suscipit felis. Sed quis ligula sit amet sapien pretium
                  aliquet.
                </Box>
              </AlcoholTooltip>
            </Grid>
          </Grid>
        </Box>
      ) : (
        <Box sx={{ padding: "40px 0", backgroundColor: "#f0f0f0" }}>
          <NoRowsOverlay msg="No Alcohol history" />
        </Box>
      )}
    </>
  );
};
