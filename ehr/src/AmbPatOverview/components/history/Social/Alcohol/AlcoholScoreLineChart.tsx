import { LineChart } from "@mui/x-charts";
import { interpolateRainbow } from "d3-scale-chromatic";

interface IProps {
  data: any[];
}

const colorByScore = (score: any) => {
  const colors = ["#ED1F24", "#FFAA22", "#FFCC33", "#00FF00"];
  if (isNaN(score)) return colors[colors.length - 1];
  const sval = parseFloat(score);
  if (sval > 10) return colors[0];
  if (sval > 7) return colors[1];
  if (sval > 4) return colors[2];
  return colors[3];
};

const getXVal = (d: any) => {
  const monthAbbrs = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sept",
    "Oct",
    "Nov",
    "Dec",
  ];
  const day = d.getDate();
  const mon = d.getMonth();
  return monthAbbrs[mon] + " " + day + ", " + d.getFullYear();
};

export const AlcoholScoreLineChart = (props: IProps) => {
  const data = props.data;
  let pointnum = data && data.length > 0 ? data.length : 0;
  pointnum = pointnum > 6 ? 6 : pointnum;
  let markers: any[] = [];
  const seriesdata: any[] = [];
  const xAxisdata: any[] = [];
  if (pointnum > 1) {
    for (let i = pointnum - 1; i >= 0; i--) {
      if (isNaN(data[i]["score"])) continue;
      const mkdata: any = {};
      mkdata["val"] = data[i]["score"];
      mkdata["xval"] = data[i]["md"] + ", " + data[i]["yyyy"];
      mkdata["xaxis"] = data[i]["lgtime"] + "000";
      mkdata["color"] = colorByScore(data[i]["score"]);
      mkdata["dataindex"] = i;
      markers.push(mkdata);
    }
    for (let i = 0; i < markers.length; i++) {
      seriesdata.push(markers[i]["val"]);
      if (markers[i]["xaxis"].length === 13) {
        xAxisdata.push(new Date(parseInt(markers[i]["xaxis"])));
      } else {
        xAxisdata.push(new Date());
      }
    }
  }

  const transformAndInterpolate = (value: number) => {
    // Linear transformation from [0, 1] to [0.6, 0.2] which is the range of the rainbow color [green, red]
    const transformedValue = 0.6 - value * 0.4;

    // Call interpolateRainbow with the transformed value
    return interpolateRainbow(transformedValue);
  };

  return (
    <LineChart
      margin={{ top: 25, bottom: 35 }}
      series={[
        {
          data: seriesdata,
          area: false,
          showMark: ({ index }) => index % (seriesdata.length - 1) === 0,
        },
      ]}
      grid={{ horizontal: false }}
      yAxis={[
        {
          disableTicks: true,
          colorMap: {
            type: "continuous",
            min: 0,
            max: 12,
            color: transformAndInterpolate,
          },
          position: "none",
        },
      ]}
      xAxis={[
        {
          disableTicks: true,
          scaleType: "time",
          data: xAxisdata,
          valueFormatter: (value) => getXVal(value),
          position: "none",
        },
      ]}
    />
  );
};
