import * as React from "react";
import Box from "@mui/material/Box";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Stack from "@mui/material/Stack";
import Paper from "@mui/material/Paper";
import { Divider } from "@mui/material";
import {
  DownIcon,
  NeutralIcon,
  UnableToAssessIcon,
  UpIcon,
} from "@cci-monorepo/Pld/components/socialHistory/alcohol/assets";
import { useAtomValue } from "jotai";
import {
  allDataAlcoholAtom,
  currentAlcoholDataAtom,
  socialHistoryALModuleStatusAtom,
} from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import {
  CalcScore,
  ColorForScore,
  GetDatePart,
  InitEncounterData,
} from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";
import { AlcoholScoreLineChart } from "./AlcoholScoreLineChart";

const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(0),
  textAlign: "center",
  color: theme.palette.text.secondary,
  flexGrow: 1,
  border: "0",
  boxShadow: "none",
}));

export default function AlcoholHistoryScore(props: any) {
  const moduleStatus = useAtomValue(socialHistoryALModuleStatusAtom);
  const curData = useAtomValue(currentAlcoholDataAtom);
  const allData = useAtomValue(allDataAlcoholAtom);
  const [score, setScore] = React.useState("");
  const initEncData: any[] = [];
  const [encData, setEncData] = React.useState(initEncData);
  const [validScoreNum, setValidScoreNum] = React.useState(0);

  React.useEffect(() => {
    if (moduleStatus.reviewed) {
      setEncData(InitEncounterData(allData, curData));
    } else {
      setEncData(InitEncounterData(allData, null));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [curData, moduleStatus]);

  React.useEffect(() => {
    if (encData && encData.length > 0) {
      setScore(CalcScore(encData[0]));
      let validSNum = 0;
      for (let i = 0; i < encData.length; i++) {
        if (isNaN(encData[i]["score"])) continue;
        validSNum = validSNum + 1;
      }
      setValidScoreNum(validSNum);
    }
  }, [encData]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        width: "auto",
        height: "auto",
        backgroundColor: ColorForScore(score, "0"),
        borderColor: ColorForScore(score, "1"),
        borderStyle: "solid",
        borderWidth: "1px",
        borderRadius: "8px",
      }}
    >
      <Stack direction="row">
        <Item
          sx={{
            padding: "32px 0 32px 16px",
            borderRadius: "8px",
            backgroundColor: ColorForScore(score, "0"),
          }}
        >
          <Stack
            direction="column"
            divider={
              <Divider
                orientation="horizontal"
                sx={{
                  backgroundColor: "#CCC",
                  height: "1px",
                  marginTop: "8px",
                }}
                flexItem
              />
            }
          >
            <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
              <Stack direction="column" alignItems="center" flexWrap="wrap">
                <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "36px",
                      fontStyle: "normal",
                      fontWeight: "700",
                      lineHeight: "30px" /* 83.333% */,
                    }}
                  >
                    {score}
                  </Typography>
                </Item>
                <Item
                  sx={{
                    backgroundColor: ColorForScore(score, "0"),
                    paddingTop: "8px",
                  }}
                >
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: "400",
                      lineHeight: "normal",
                    }}
                  >
                    {moduleStatus.reviewed
                      ? GetDatePart(curData, "md")
                      : GetDatePart(
                          encData.length > 0 ? encData[0] : null,
                          "md"
                        )}
                  </Typography>
                </Item>
                <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: "400",
                      lineHeight: "normal",
                    }}
                  >
                    {moduleStatus.reviewed
                      ? GetDatePart(curData, "yyyy")
                      : GetDatePart(
                          encData.length > 0 ? encData[0] : null,
                          "yyyy"
                        )}
                  </Typography>
                </Item>
              </Stack>
            </Item>
            <Item
              sx={{
                backgroundColor: ColorForScore(score, "0"),
                padding: "8px 0 0 0",
              }}
            >
              <Typography
                sx={{
                  width: "82px",
                  color: "#626262",
                  textAlign: "center",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontStyle: "normal",
                  fontWeight: "400",
                  lineHeight: "normal",
                }}
              >
                {"Last Encounter"}
              </Typography>
            </Item>
          </Stack>
        </Item>
        {validScoreNum > 1 ? (
          <Item
            sx={{
              width: "168px",
              backgroundColor: ColorForScore(score, "0"),
              borderRadius: "0",
            }}
          >
            <AlcoholScoreLineChart data={encData} />
          </Item>
        ) : (
          <></>
        )}

        <Item
          sx={{
            width: "185px",
            padding: "11px 14px 11px 0px",
            backgroundColor: ColorForScore(score, "0"),
            borderRadius: "8px",
          }}
        >
          <Stack direction="column" width="100%" sx={{ gap: "0" }}>
            <Item
              sx={{
                backgroundColor: ColorForScore(score, "0"),
                marginLeft: "12px",
              }}
            >
              <Stack direction="row" sx={{ gap: "0" }}>
                <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: "700",
                      lineHeight: "30px",
                    }}
                  >
                    Score
                  </Typography>
                </Item>
                <Item
                  sx={{
                    width: "147px",
                    backgroundColor: ColorForScore(score, "0"),
                  }}
                >
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: "700",
                      lineHeight: "30px",
                      width: "136px",
                    }}
                  >
                    Encounter Date
                  </Typography>
                </Item>
              </Stack>
            </Item>

            {encData &&
              encData.length > 1 &&
              encData.map((item: any, index: any) => {
                return index === 0 ? (
                  <></>
                ) : (
                  <Item sx={{ width: "47px" }}>
                    <Stack direction="row" sx={{ padding: "0", gap: "0" }}>
                      <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
                        <Stack direction="row" sx={{ padding: "0" }}>
                          <Item
                            sx={{
                              padding: "0",
                              backgroundColor: ColorForScore(score, "0"),
                            }}
                          >
                            <Typography
                              sx={{
                                color: "#626262",
                                textAlign: "right",
                                fontFamily: "Roboto",
                                fontSize: "14px",
                                fontStyle: "normal",
                                fontWeight: "bold",
                                lineHeight: "20px",
                                padding: "0",
                                width: "32px",
                              }}
                            >
                              {item["score"]}
                            </Typography>
                          </Item>
                          <Item
                            sx={{
                              width: "17px",
                              height: "16px",
                              backgroundColor: ColorForScore(score, "0"),
                            }}
                          >
                            {item["icon"] === "GT" ? (
                              <UpIcon
                                style={{
                                  width: "17px",
                                  height: "16px",
                                  verticalAlign: "middle",
                                }}
                              />
                            ) : item["icon"] === "LT" ? (
                              <DownIcon
                                style={{
                                  width: "17px",
                                  height: "16px",
                                  verticalAlign: "middle",
                                }}
                              />
                            ) : item["icon"] === "EQ" ? (
                              <NeutralIcon
                                style={{
                                  width: "17px",
                                  height: "16px",
                                  verticalAlign: "middle",
                                }}
                              />
                            ) : item["icon"] === "UA" ? (
                              <UnableToAssessIcon
                                style={{
                                  width: "17px",
                                  height: "16px",
                                  verticalAlign: "middle",
                                }}
                              />
                            ) : (
                              <></>
                            )}
                          </Item>
                        </Stack>
                      </Item>
                      <Item
                        sx={{
                          width: "136px",
                          backgroundColor: ColorForScore(score, "0"),
                        }}
                      >
                        <Typography
                          sx={{
                            color: "#626262",
                            textAlign: "right",
                            fontFamily: "Roboto",
                            fontSize: "14px",
                            fontStyle: "normal",
                            fontWeight: "400",
                            lineHeight: "20px",
                            width: "120px",
                          }}
                        >
                          {item["md"] + ", " + item["yyyy"]}
                        </Typography>
                      </Item>
                    </Stack>
                  </Item>
                );
              })}
          </Stack>
        </Item>
      </Stack>
    </Box>
  );
}
