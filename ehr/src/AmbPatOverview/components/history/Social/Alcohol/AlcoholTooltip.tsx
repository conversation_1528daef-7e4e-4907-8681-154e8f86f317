import { StyledNameTooltip } from "@cci-monorepo/AmbPatOverview/components/common/Constants";
import { Fade, Grid, Typography, styled } from "@mui/material";
import { currentAlcoholDataAtom } from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import { useAtomValue } from "jotai";
import { CalcScore } from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";

const StyledTitle = styled(Typography)({
  fontStyle: "bold",
  fontSize: "13px",
  fontWeight: "700",
  textAlign: "left",
});

const StyledValue = styled(Typography)({
  fontSize: "13px",
  textAlign: "left",
});

const QNR_freq_Map: any = {
  "0": "Never",
  "1": "Monthly or less",
  "2": "Two to four times a month",
  "3": "Two to three times a week",
  "4": "Four or more times a week",
};

const QNR_qty_per_day_Map: any = {
  "0": "None, I do not drink",
  "0.5": "1 or 2",
  "1": "3 or 4",
  "2": "5 or 6",
  "3": "7 to 9",
  "4": "10 or more",
};

const QNR_gte_6_freq_Map: any = {
  "0": "Never",
  "1": "Less than monthly",
  "2": "Monthly",
  "3": "Weekly",
  "4": "Daily or almost daily",
};

export const AlcoholTooltip = (props: any) => {
  const alcoholData = useAtomValue(currentAlcoholDataAtom);

  return (
    <StyledNameTooltip
      enterDelay={800}
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 200 }}
      sx={{ maxWidth: 400 }}
      title={
        <>
          <Grid container>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Frequency:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>
                  {alcoholData.QNR_freq && QNR_freq_Map[alcoholData.QNR_freq]}
                </StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Per Day:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>
                  {alcoholData.QNR_qty_per_day &&
                    QNR_qty_per_day_Map[alcoholData.QNR_qty_per_day]}
                </StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>6 or more in 1 occasion:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>
                  {alcoholData.QNR_gte_6_freq &&
                    QNR_gte_6_freq_Map[alcoholData.QNR_gte_6_freq]}
                </StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Age Started:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{alcoholData?.agestarted}</StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Start Date:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{alcoholData?.startdate}</StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Quit Date:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{alcoholData?.quitdate}</StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Restart Date:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{alcoholData?.restartdate}</StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Date Last Consumed:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{alcoholData?.lastused}</StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Comments:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{alcoholData?.comments}</StyledValue>
              </Grid>
            </Grid>
            <Grid container>
              <Grid item xs={6}>
                <StyledTitle>Alcohol Use Disorder Score:</StyledTitle>
              </Grid>
              <Grid item xs={6}>
                <StyledValue>{CalcScore(alcoholData)}</StyledValue>
              </Grid>
            </Grid>
          </Grid>
        </>
      }
    >
      {props.children}
    </StyledNameTooltip>
  );
};
