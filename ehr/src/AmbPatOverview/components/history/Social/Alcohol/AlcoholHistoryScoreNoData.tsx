import * as React from "react";
import Box from "@mui/material/Box";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Stack from "@mui/material/Stack";
import Paper from "@mui/material/Paper";
import { Divider } from "@mui/material";
import { useAtomValue } from "jotai";
import {
  allDataAlcoholAtom,
  currentAlcoholDataAtom,
  socialHistoryALModuleStatusAtom,
} from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import {
  CalcScore,
  ColorForScore,
  GetDatePart,
  InitEncounterData,
} from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";

const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(0),
  textAlign: "center",
  color: theme.palette.text.secondary,
  flexGrow: 1,
  border: "0",
  boxShadow: "none",
}));

export default function AlcoholHistoryScoreNoData(props: any) {
  const moduleStatus = useAtomValue(socialHistoryALModuleStatusAtom);
  const curData = useAtomValue(currentAlcoholDataAtom);
  const allData = useAtomValue(allDataAlcoholAtom);
  const [score, setScore] = React.useState("");
  const initEncData: any[] = [];
  const [encData, setEncData] = React.useState(initEncData);

  React.useEffect(() => {
    if (allData && allData.length > 0) {
      setEncData(
        InitEncounterData(allData, moduleStatus.reviewed ? curData : null)
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allData, curData, moduleStatus]);

  React.useEffect(() => {
    if (encData && encData.length > 0) {
      setScore(CalcScore(encData[0]));
    }
  }, [encData]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        width: "auto",
        height: "auto",
        backgroundColor: ColorForScore(score, "0"),
        borderColor: ColorForScore(score, "1"),
        borderStyle: "solid",
        borderWidth: "1px",
        borderRadius: "8px",
      }}
    >
      <Stack direction="row">
        <Item
          sx={{
            borderRadius: "8px",
            backgroundColor: ColorForScore(score, "0"),
            padding:
              encData.length < 1
                ? "62px 16px 62px 16px"
                : "32px 16px 32px 16px",
          }}
        >
          <Stack
            direction="column"
            divider={
              <Divider
                orientation="horizontal"
                sx={{
                  backgroundColor: "#CCC",
                  height: "1px",
                  marginTop: encData.length < 1 ? "0" : "8px",
                }}
                flexItem
              />
            }
          >
            <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
              <Stack direction="column" alignItems="center" flexWrap="wrap">
                <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "36px",
                      fontStyle: "normal",
                      fontWeight: "700",
                      lineHeight: "30px" /* 83.333% */,
                    }}
                  >
                    {encData.length < 1 ? "NA" : score}
                  </Typography>
                </Item>
                <Item
                  sx={{
                    paddingTop: "8px",
                    backgroundColor: ColorForScore(score, "0"),
                  }}
                >
                  <Typography
                    sx={{
                      color: "#626262",
                      textAlign: "center",
                      fontFamily: "Roboto",
                      fontSize: "14px",
                      fontStyle: "normal",
                      fontWeight: "400",
                      lineHeight: "normal",
                    }}
                  >
                    {encData.length < 1 ? "" : GetDatePart(encData[0], "md")}
                  </Typography>
                </Item>
                {encData.length < 1 ? (
                  <></>
                ) : (
                  <Item sx={{ backgroundColor: ColorForScore(score, "0") }}>
                    <Typography
                      sx={{
                        color: "#626262",
                        textAlign: "center",
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: "400",
                        lineHeight: "normal",
                      }}
                    >
                      {GetDatePart(encData[0], "yyyy")}
                    </Typography>
                  </Item>
                )}
              </Stack>
            </Item>
            <Item
              sx={{
                padding: "8px 0 0 0",
                backgroundColor: ColorForScore(score, "0"),
              }}
            >
              <Typography
                sx={{
                  width: "82px",
                  color: "#626262",
                  textAlign: "center",
                  fontFamily: "Roboto",
                  fontSize: "12px",
                  fontStyle: "normal",
                  fontWeight: "400",
                  lineHeight: "normal",
                }}
              >
                {encData.length === 0 ? "No Data Yet" : "Last Encounter"}
              </Typography>
            </Item>
          </Stack>
        </Item>
      </Stack>
    </Box>
  );
}
