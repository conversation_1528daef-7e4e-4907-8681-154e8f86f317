import * as React from "react";

import Box from "@mui/material/Box";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Stack from "@mui/material/Stack";
import Paper from "@mui/material/Paper";
import { Grid } from "@mui/material";
import { useAtomValue } from "jotai";
import {
  allDataAlcoholAtom,
  currentAlcoholDataAtom,
  socialHistoryALModuleStatusAtom,
} from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import {
  GetPrevEncData,
  InitEncounterData,
} from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";
import { AlcoholTooltip } from "./AlcoholTooltip";

const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(0),
  textAlign: "center",
  color: theme.palette.text.secondary,
  flexGrow: 1,
  border: "0",
  boxShadow: "none",
}));

export default function AlcoholQuestionDetails(props: any) {
  const moduleStatus = useAtomValue(socialHistoryALModuleStatusAtom);
  const curData = useAtomValue(currentAlcoholDataAtom);
  const allData = useAtomValue(allDataAlcoholAtom);
  const initEncData: any[] = [];
  const [encData, setEncData] = React.useState(initEncData);

  React.useEffect(() => {
    setEncData(
      InitEncounterData(allData, moduleStatus.reviewed ? curData : null)
    );
  }, [allData, curData, moduleStatus]);

  return (
    <AlcoholTooltip>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          width: "100%",
          height: "auto",
          backgroundColor: "white",
        }}
      >
        <Stack direction="row" alignItems="center">
          <Item sx={{ verticalAlign: "middle" }}>
            <Stack
              sx={{ height: "56px", padding: "0px 32px" }}
              direction="column"
              alignItems="center"
            >
              <Item>
                <Typography
                  sx={{
                    color: "#000",
                    textAlign: "center",
                    fontFamily: "Roboto",
                    fontSize: "20px",
                    fontStyle: "normal",
                    fontWeight: "700",
                    lineHeight: "16px",
                    width: "24px",
                    height: "16px",
                  }}
                >
                  {GetPrevEncData(encData, "agestarted")}
                </Typography>
              </Item>
              <Item>
                <Typography
                  sx={{
                    color: "#626262",
                    textAlign: "center",
                    fontFamily: "Roboto",
                    fontSize: "14px",
                    fontStyle: "normal",
                    fontWeight: "400",
                    lineHeight: "normal",
                    width: "75px",
                    marginTop: "4px",
                  }}
                >
                  Age Started
                </Typography>
              </Item>
            </Stack>
          </Item>

          <Item>
            <Grid
              container
              sx={{ padding: "0px 16px 0px 32px", minWidth: "400px" }}
            >
              <Grid item xs={6}>
                <Grid
                  container
                  sx={{ width: "152px", height: "28px", padding: "0" }}
                  justifyContent="space-evenly"
                  alignItems="flex-start"
                >
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#626262",
                        textAlign: "left",
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: "400",
                        lineHeight: "normal",
                        width: "48px",
                      }}
                    >
                      Start
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#000",
                        textAlign: "center",
                        fontFamily: "Roboto",
                        fontSize: "16px",
                        fontStyle: "normal",
                        fontWeight: "700",
                        lineHeight: "18px",
                        width: "86px",
                      }}
                    >
                      {GetPrevEncData(encData, "startdate")}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={6}>
                <Grid
                  container
                  sx={{ width: "152px", height: "28px", padding: "0" }}
                  justifyContent="space-evenly"
                  alignItems="flex-start"
                >
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#626262",
                        textAlign: "left",
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: "400",
                        lineHeight: "normal",
                        width: "48px",
                      }}
                    >
                      Quit
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#000",
                        textAlign: "center",
                        fontFamily: "Roboto",
                        fontSize: "16px",
                        fontStyle: "normal",
                        fontWeight: "700",
                        lineHeight: "18px",
                        width: "86px",
                      }}
                    >
                      {GetPrevEncData(encData, "quitdate")}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={6}>
                <Grid
                  container
                  sx={{ width: "152px", height: "28px", padding: "0" }}
                  justifyContent="space-evenly"
                  alignItems="flex-start"
                >
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#626262",
                        textAlign: "left",
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: "400",
                        lineHeight: "normal",
                        width: "48px",
                      }}
                    >
                      Restart
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#000",
                        textAlign: "center",
                        fontFamily: "Roboto",
                        fontSize: "16px",
                        fontStyle: "normal",
                        fontWeight: "700",
                        lineHeight: "18px",
                        width: "86px",
                      }}
                    >
                      {GetPrevEncData(encData, "restartdate")}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={6}>
                <Grid
                  container
                  sx={{ width: "152px", height: "28px", padding: "0" }}
                  justifyContent="space-evenly"
                  alignItems="flex-start"
                >
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#626262",
                        textAlign: "left",
                        fontFamily: "Roboto",
                        fontSize: "14px",
                        fontStyle: "normal",
                        fontWeight: "400",
                        lineHeight: "normal",
                        width: "48px",
                      }}
                    >
                      Last
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography
                      sx={{
                        color: "#000",
                        textAlign: "center",
                        fontFamily: "Roboto",
                        fontSize: "16px",
                        fontStyle: "normal",
                        fontWeight: "700",
                        lineHeight: "18px",
                        width: "86px",
                      }}
                    >
                      {GetPrevEncData(encData, "lastused")}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Item>
        </Stack>
      </Box>
    </AlcoholTooltip>
  );
}
