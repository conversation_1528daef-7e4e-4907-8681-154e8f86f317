import React from "react";
import { TooltipCommonDataGrid } from "../../common/TooltipCommonDataGrid";
import { isShowInactiveSocialAtom } from "@cci-monorepo/AmbPatOverview/context/HistoryAtoms";
import { useAtomValue, useSetAtom } from "jotai";
import {
  setParseDataTobaccoAtom,
  rowDataTobaccoAtom,
  tobaccoCountAtom,
  isExpandedAtom,
  smokingPackYearsAtom,
} from "@cci-monorepo/AmbPatOverview/context/SocialHistoryAtoms";
import { dateComparator, useSetErrorDialog } from "@cci-monorepo/common";
import { toPldWithPayLoad } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import dayjs from "dayjs";
import { renderGridDataCell } from "@cci-monorepo/Pld/components/socialHistory/SocialHistoryUtil";
import { Box, Stack } from "@mui/material";
import { getSocialHistory } from "@cci-monorepo/Pld/util/SocialHistoryAPI";
import { pldDbpathsAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";

export const TobaccoSection = ({ loadReviewHistory }: any) => {
  const [hasData, setHasData] = React.useState(false);
  const rowData = useAtomValue(rowDataTobaccoAtom);
  const isShowInActive = useAtomValue(isShowInactiveSocialAtom);
  const smokingPackYears = useAtomValue(smokingPackYearsAtom);
  const setOpenErrorDialog = useSetErrorDialog();

  const parseData = useSetAtom(setParseDataTobaccoAtom);
  const setExpanded = useSetAtom(isExpandedAtom);
  const setDataCount = useSetAtom(tobaccoCountAtom);
  const pldDbpaths = useAtomValue(pldDbpathsAtom);

  const activeRows = React.useMemo(() => {
    return rowData.filter((row: { [key: string]: string | number }) => {
      return String(row.status).toUpperCase() === "ACTIVE";
    });
  }, [rowData]);

  const displayRows = React.useMemo(() => {
    return isShowInActive ? rowData : activeRows;
  }, [isShowInActive, rowData, activeRows]);

  const columnDefs = [
    {
      field: "status",
      headerName: "Status",
      width: 100,
      disableColumnMenu: false,
      valueOptions: ["Active", "Inactive", "Error"],
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
    {
      field: "type",
      headerName: "Type",
      minWidth: 150,
      flex: 1,
      disableColumnMenu: true,
      renderCell: (params: GridRenderCellParams<any>) => (
        <div
          style={{
            font: "normal 400 15px Roboto",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            color: params.row.status === "Inactive" ? "#7C7C7C" : "#000",
            fontStyle: params.row.status === "Inactive" ? "italic" : "normal",
            textDecoration:
              params.row.status === "Error" ? "line-through" : "none",
          }}
        >
          {params.row.status === "Inactive" &&
          (params.row.type === "None" || params.row.type === "")
            ? "Patient Reported None " +
              (params.row.key === ""
                ? ""
                : dayjs.unix(params.row.key).format("YYYY/MM/DD HH:MM"))
            : params.value}
        </div>
      ),
    },
    {
      field: "frequency",
      headerName: "Frequency",
      width: 140,
      disableColumnMenu: true,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
    {
      field: "quitdate",
      headerName: "Quit Date",
      width: 140,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
    {
      field: "restartDate",
      headerName: "Restart Date",
      width: 140,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
  ];

  React.useEffect(() => {
    setExpanded((expanded: boolean) => {
      return expanded || activeRows.length > 0;
    });
  }, [setExpanded, activeRows]);

  React.useEffect(() => {
    setDataCount(displayRows.length);
  }, [setDataCount, displayRows]);

  React.useEffect(() => {
    if (!hasData && pldDbpaths.length) {
      let currentIndex = 0;
      const onSuccess = (data: any) => {
        parseData(data, null, false, currentIndex);
        setHasData(true);
      };

      const onFailure = (error: string) => {
        setOpenErrorDialog({
          text: `System has failed to load social history data: ${error}`,
          open: true,
        });
      };

      let promises = pldDbpaths.map((item: any, index) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        if (item.dbpath === Cci.util.Patient.getDbpath()) {
          currentIndex = index;
        }
        return getSocialHistory(params, () => {}, onFailure);
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
        loadReviewHistory();
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData, pldDbpaths, setOpenErrorDialog]);

  const SmokeStausSx = {
    font: "normal 400 15px Roboto",
    color: "#999",
    textWrap: "no-wrap",
  };

  return (
    <Box>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ padding: "16px 32px" }}
      >
        <Box sx={SmokeStausSx}>
          {displayRows?.[0]?.smokingStatus ?? "Smoking Status"}
        </Box>
        <Box sx={SmokeStausSx}>
          {displayRows?.[0]?.smokelessStatus ?? "Smokeless Status"}
        </Box>
        <Box sx={{ ...SmokeStausSx, color: "rgba(0, 0, 0, 0.58)" }}>
          Smoking Pack-Years:
          <span style={{ fontWeight: 700, paddingLeft: "10px" }}>
            {smokingPackYears}
          </span>
        </Box>
      </Stack>
      <TooltipCommonDataGrid
        type="tobacco"
        getRowId={(row: any) => row.tobaccoid}
        rows={displayRows}
        columns={columnDefs}
        enableStatusColFilter={true}
        noDataMsg="No Tobacco History"
        onRowDoubleClick={(row: any) => {
          toPldWithPayLoad("SocialHistoryTobacco", {
            key: "tobaccoid",
            value: row.row.tobaccoid,
          });
        }}
        columnVisibilityModel={{
          status: isShowInActive,
        }}
      />
    </Box>
  );
};
