import { isJson } from "@cci-monorepo/common";
import { calcPackYears } from "@cci-monorepo/Pld/components/socialHistory/SocialHistoryUtil";
import { randomId } from "@mui/x-data-grid-generator";

// Parse server data for Social Hisotry
export const parseTobaccoData = (
  data: any,
  choiceLists: any,
  editable: boolean,
  index: number
) => {
  let tobaccoGridRows: any = [];
  let entryFields: any = [];
  let reviewMessage: any = [];

  if (data && data.length > 0) {
    entryFields = getEntryFields(data[0].tobacco_coldefs);
    tobaccoGridRows = getDisplayRows(data, index);
    reviewMessage = getReviewHistory(data);
  }
  return [tobaccoGridRows, entryFields, reviewMessage];
};

// Get rows for the main data grid
const getDisplayRows = (data: any, index: number) => {
  let gridRows: any = [];
  let tobacco: any = [];
  let rows: any = [];
  let smokeStatus = {};
  const field: string = "tobacco_data";
  if (
    !data[index]["tobacco_hist"] ||
    data[index]["tobacco_hist"].data.length === 0
  ) {
    data.forEach((item: any, idx: number) => {
      if (item[field] && item[field].data.length > 0) {
        let rows = convertArrayToObject(item[field]);
        tobacco.push(rows);

        if (
          isJson(item?.smoke_status.data[0][0]) &&
          !Object.keys(smokeStatus).length
        ) {
          const oriSmokeStatus = JSON.parse(item?.smoke_status.data[0][0]);
          smokeStatus = {
            smokingStatus: oriSmokeStatus.smokeStatus?.split(" - ")[0],
            smokelessStatus: oriSmokeStatus.smokinglessStatus?.split(" - ")[0],
          };
        }
      }
    });
  } else if (data[index][field] && data[index][field].data.length > 0) {
    tobacco.push(convertArrayToObject(data[index][field]));

    if (isJson(data[index]?.smoke_status.data[0][0])) {
      const oriSmokeStatus = JSON.parse(data[index]?.smoke_status.data[0][0]);
      smokeStatus = {
        smokingStatus: oriSmokeStatus.smokeStatus?.split(" - ")[0],
        smokelessStatus: oriSmokeStatus.smokinglessStatus?.split(" - ")[0],
      };
    }
  }

  tobacco.forEach((item: any) => {
    item.forEach((element: any) => {
      if (
        !rows.some((row: any) => {
          return JSON.stringify(row) === JSON.stringify(element);
        })
      ) {
        rows.push(element);
      }
    });
  });
  let activeRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];
  // Add id to each row and group them by status
  rows.forEach((row: any) => {
    if (row.status === "Active") {
      if (row.type === "Cigarette") {
        smokeStatus = {
          ...smokeStatus,
          smokingPackYears: calcPackYears(row),
        };
      }
      activeRows.push({ ...row, id: randomId(), ...smokeStatus });
    } else if (row.status === "Inactive") {
      inactiveRows.push({ ...row, id: randomId(), ...smokeStatus });
    } else {
      errorRows.push({ ...row, id: randomId(), ...smokeStatus });
    }
  });
  inactiveRows.sort((a: any, b: any) =>
    Cci.util.DateTime.timeStrToServerSecs(a.quitdate) >
    Cci.util.DateTime.timeStrToServerSecs(b.quitdate)
      ? -1
      : 1
  );
  // Returns active, followed by inactive
  gridRows = activeRows.concat(inactiveRows, errorRows);
  return gridRows;
};

// Get entry map from the server data
const getEntryFields = (colDefs: any) => {
  const entryFields: string[] = [];

  if (colDefs && colDefs.header && colDefs.data) {
    const nameIdx = colDefs.header.indexOf("colname");
    const isentryIdx = colDefs.header.indexOf("isentry");

    // Build entry fields
    colDefs.data.forEach((item: any, idx: number) => {
      if (item[isentryIdx] === "1") {
        entryFields.push(item[nameIdx]);
      }
    });
  }

  return entryFields;
};

export const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

// Review History
export const getReviewHistory = (data: any) => {
  let ret: any = [];
  let reviewHistory: any = [];
  const field: string = "tobacco_reviewhistory";
  if (data[field] && data[field].data.length > 0) {
    reviewHistory.push(convertArrayToObject(data[field]));
  }

  ret = reviewHistory
    .reduce((prev: any, cur: any) => (prev ? prev.concat(cur) : cur), [])
    .sort((a: any, b: any) => {
      return a.reviewed_ts < b.reviewed_ts ? 1 : -1;
    })
    .map((item: any, index: number) => {
      return {
        id: index,
        status: item.status,
        review_status:
          item.status === "unable to assess"
            ? item.status + ", Reason:" + item.reason
            : item.status,
        reason: item.reason,
        user: item.user,
        reviewed_ts: item.reviewed_ts,
        fmt_reviewed_tm: item.fmt_reviewed_tm,
        last_reviewed:
          item.status !== "reviewed"
            ? "User was " +
              item.status +
              (!!item.reason ? ", Reason:" + item.reason : "") +
              " - By " +
              item.user +
              " at " +
              item.fmt_reviewed_tm
            : "Last reviewed by " + item.user + " at " + item.fmt_reviewed_tm,
        chksum: item.chksum,
      };
    });
  return ret;
};
