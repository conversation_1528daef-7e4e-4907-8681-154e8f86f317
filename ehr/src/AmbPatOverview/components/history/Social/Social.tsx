import { useEffect } from "react";
import {
  Box,
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import { useSetErrorDialog } from "@cci-monorepo/common";
import { SubStanceUse } from "./SubStanceUse";
import { SocialSubToolBar } from "./SocialSubToolBar";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  setReviewMessageSubstanceUseAtom,
  setReviewMessageTobaccoAtom,
  hasReviewedSubstanceUseAtom,
  substanceUseNoneAtom,
  reviewMessageSUAtom,
  isExpandedAtom,
  dataCountAtom,
} from "@cci-monorepo/AmbPatOverview/context/SocialHistoryAtoms";
import {
  setReviewMessageAlcoholAtom,
  hasReviewedAlcoholAtom,
  noneAlcoholAtom,
  reviewMessageALAtom,
} from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import { Alcohol } from "./Alcohol/Alcohol";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../../common/Constants";
import { HistoryToolbar } from "../HistoryToolbar";
import { TobaccoToolBar } from "./TobaccoToolBar";
import { TobaccoSection } from "./TobaccoSection";
import { getReviewHistoryAlcohol } from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";
import { getReviewHistory as getReviewHistorySubus } from "./SocialHistoryUtil";
import { getReviewHistory as getReviewHistoryTobacco } from "./TobaccoUtil";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";

export const Social = (props: any) => {
  const currentDBPath = Cci.util.Patient.getDbpath();
  const setReviewMessageSubstanceUse = useSetAtom(
    setReviewMessageSubstanceUseAtom
  );
  const hasReviewedSubstanceUse = useAtomValue(hasReviewedSubstanceUseAtom);
  const substanceUseNone = useAtomValue(substanceUseNoneAtom);
  const reviewMessageSU = useAtomValue(reviewMessageSUAtom);
  const dataCount = useAtomValue(dataCountAtom);
  const [expanded, setExpanded] = useAtom(isExpandedAtom);
  const setReviewMessageTobacco = useSetAtom(setReviewMessageTobaccoAtom);
  // Alcohol
  const setReviewMessageAlcohol = useSetAtom(setReviewMessageAlcoholAtom);
  const hasReviewedAlcohol = useAtomValue(hasReviewedAlcoholAtom);
  const noneAlcohol = useAtomValue(noneAlcoholAtom);
  const reviewMessageAL = useAtomValue(reviewMessageALAtom);

  const setOpenErrorDialog = useSetErrorDialog();

  const formatTooltip = (
    moduleName: string,
    hasReviewed: Boolean,
    reviewMessage: any[]
  ) => {
    if (hasReviewed) {
      return `${reviewMessage.length > 0 && reviewMessage[0].last_reviewed}`;
    } else {
      return `${moduleName} Not Reviewed`;
    }
  };

  const loadAlcoholReviewHistory = () => {
    const onHistorySuccess = (data: any) => {
      setReviewMessageAlcohol(getReviewHistoryAlcohol(data));
    };

    const onHistoryFailure = (error: any) => {
      console.error("System has failed to load review history. " + error);
    };

    const params = {
      campus: cci.cfg.campus,
      dbs: currentDBPath,
    };
    return serverRequest(
      "pld/socialhistory/alcohol/review_history",
      params,
      onHistorySuccess,
      onHistoryFailure
    );
  };

  const loadSubusReviewHistory = () => {
    const onHistorySuccess = (data: any) => {
      setReviewMessageSubstanceUse(getReviewHistorySubus(data));
    };

    const onHistoryFailure = (error: any) => {
      console.error("System has failed to load review history. " + error);
    };

    const params = {
      campus: cci.cfg.campus,
      dbs: currentDBPath,
    };
    return serverRequest(
      "pld/socialhistory/subusreview_history",
      params,
      onHistorySuccess,
      onHistoryFailure
    );
  };

  const loadTobaccoReviewHistory = () => {
    const onHistorySuccess = (data: any) => {
      setReviewMessageTobacco(getReviewHistoryTobacco(data));
    };

    const onHistoryFailure = (error: any) => {
      setOpenErrorDialog({
        text: "System has failed to load review history. " + error,
        open: true,
      });
    };

    const params = {
      campus: cci.cfg.campus,
      dbs: currentDBPath,
    };
    return serverRequest(
      "pld/socialhistory/tobaccoreview_history",
      params,
      onHistorySuccess,
      onHistoryFailure
    );
  };

  useEffect(() => {
    loadAlcoholReviewHistory();
    loadSubusReviewHistory();
    loadTobaccoReviewHistory();
  }, []);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <HistoryToolbar type="social" readonly={props.readonly} />
      </AccordionSummary>

      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        {expanded && (
          <Box>
            <Box>
              <SocialSubToolBar
                name="Alcohol"
                hasReviewed={hasReviewedAlcohol || noneAlcohol}
                iconTooltip={formatTooltip(
                  "Alcohol",
                  hasReviewedAlcohol,
                  reviewMessageAL
                )}
              />
              <Alcohol loadReviewHistory={loadAlcoholReviewHistory} />
            </Box>
            <Box>
              <SocialSubToolBar
                name="Substance Use"
                count={dataCount}
                hasReviewed={hasReviewedSubstanceUse || substanceUseNone}
                iconTooltip={formatTooltip(
                  "Substance Use",
                  hasReviewedSubstanceUse,
                  reviewMessageSU
                )}
              />
              <SubStanceUse loadReviewHistory={loadSubusReviewHistory} />
            </Box>
            <Box>
              <TobaccoToolBar />
              <TobaccoSection loadReviewHistory={loadTobaccoReviewHistory} />
            </Box>
          </Box>
        )}
      </AccordionDetails>
    </Accordion>
  );
};
