/**
 * SocialSubToolBar.tsx
 *
 * @author: jfsys
 * @description  Toolbar for social
 */

import { Box, Typography } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import Tooltip from "@mui/material/Tooltip";
import { WarningAmberIcon } from "@cci-monorepo/common/assets";

const ICON_SIZE = 18;

export type MenuItemProps = {
  label: string;
  icon: HTMLElement;
  handler: () => void;
};

export type ToolbarProps = {
  name: string;
  isCritical?: boolean;
  hasReviewed?: boolean;
  count?: number;
  iconTooltip?: string;
};

export const SocialSubToolBar = (props: ToolbarProps) => {
  const { name, isCritical, hasReviewed, count, iconTooltip } = props;

  return (
    <>
      <Box
        sx={{
          padding: "7px 0 7px 40px",
          backgroundColor: "#E0E3E6",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Tooltip
          title={iconTooltip}
          placement="bottom-start"
          componentsProps={{
            tooltip: {
              sx: {
                backgroundColor: "white",
                color: "black",
                boxShadow: "0px 0px 15px 0px #00000033",
                fontSize: "12px",
                fontFamily: "helvetica,arial,verdana,sans-serif",
                borderRadius: "4px",
                display: "inline-block",
                border: "1px solid #B9B9B9",
                padding: "8px",
                maxWidth: "1000px",
                minWidth: "100px",
              },
            },
          }}
        >
          {hasReviewed ? (
            <CheckCircleIcon htmlColor="#68BA6A" sx={{ fontSize: ICON_SIZE }} />
          ) : isCritical ? (
            <ErrorIcon color="error" sx={{ fontSize: ICON_SIZE }} />
          ) : (
            <WarningAmberIcon style={{ width: ICON_SIZE, height: ICON_SIZE }} />
          )}
        </Tooltip>
        <Box sx={{ marginLeft: "4px" }}>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontSize: "16px",
              fontWeight: "500",
              color: "#11181F",
            }}
          >
            {name}
            {count !== undefined && ` (${count})`}
          </Typography>
        </Box>
      </Box>
    </>
  );
};
