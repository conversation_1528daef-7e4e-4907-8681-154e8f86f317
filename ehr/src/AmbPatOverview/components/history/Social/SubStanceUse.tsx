import React from "react";
import { TooltipCommonDataGrid } from "../../common/TooltipCommonDataGrid";
import { isShowInactiveSocialAtom } from "@cci-monorepo/AmbPatOverview/context/HistoryAtoms";
import { useAtomValue, useSetAtom } from "jotai";
import {
  setParseDataSocialHistoryAtom,
  rowDataSubstanceUseAtom,
  isExpandedAtom,
  dataCountAtom,
} from "@cci-monorepo/AmbPatOverview/context/SocialHistoryAtoms";
import { dateComparator, useSetErrorDialog } from "@cci-monorepo/common";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import { toPldWithPayLoad } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { GridRenderCellParams } from "@mui/x-data-grid-pro";
import dayjs from "dayjs";
import { renderGridDataCell } from "@cci-monorepo/Pld/components/socialHistory/SocialHistoryUtil";

export const SubStanceUse = ({ loadReviewHistory }: any) => {
  const [hasData, setHasData] = React.useState(false);
  const rowData = useAtomValue(rowDataSubstanceUseAtom);
  const isShowInActive = useAtomValue(isShowInactiveSocialAtom);
  const setOpenErrorDialog = useSetErrorDialog();

  const parseData = useSetAtom(setParseDataSocialHistoryAtom);
  const setExpanded = useSetAtom(isExpandedAtom);
  const setDataCount = useSetAtom(dataCountAtom);

  const activeRows = React.useMemo(() => {
    return rowData.filter((row: { [key: string]: string | number }) => {
      return String(row.status).toUpperCase() === "ACTIVE";
    });
  }, [rowData]);

  const displayRows = React.useMemo(() => {
    if (isShowInActive) {
      return rowData;
    } else {
      return activeRows;
    }
  }, [isShowInActive, rowData, activeRows]);

  const columnDefs = [
    {
      field: "status",
      headerName: "Status",
      width: 100,
      disableColumnMenu: false,
      valueOptions: ["Active", "Inactive"],
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
    {
      field: "type",
      headerName: "Type",
      minWidth: 150,
      flex: 1,
      disableColumnMenu: true,
      renderCell: (params: GridRenderCellParams<any>) => (
        <div
          style={{
            font: "normal 400 15px Roboto",
            textWrap: "wrap",
            color: params.row.status === "Inactive" ? "#7C7C7C" : "#000",
            fontStyle: params.row.status === "Inactive" ? "italic" : "normal",
          }}
        >
          {params.row.status === "Inactive" &&
          (params.row.type === "None" || params.row.type === "")
            ? "Patient Reported None " +
              (params.row.key === ""
                ? ""
                : dayjs.unix(params.row.key).format("YYYY/MM/DD HH:MM"))
            : params.value}
        </div>
      ),
    },
    {
      field: "frequency",
      headerName: "Frequency",
      width: 140,
      disableColumnMenu: true,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
    {
      field: "lastused",
      headerName: "Last Used",
      width: 140,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
    {
      field: "quitdate",
      headerName: "Quit Date",
      width: 140,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: (params: GridRenderCellParams<any>) => {
        return renderGridDataCell(params);
      },
    },
  ];

  React.useEffect(() => {
    setExpanded((expanded: boolean) => {
      return expanded || activeRows.length > 0;
    });
  }, [setExpanded, activeRows]);

  React.useEffect(() => {
    setDataCount(displayRows.length);
  }, [setDataCount, displayRows]);

  React.useEffect(() => {
    if (!hasData) {
      const onSuccess = (data: any) => {
        parseData(data, null, false, 0);
        setHasData(true);
      };

      const onFailure = (error: string) => {
        setOpenErrorDialog({
          text: `System has failed to load social history data: ${error}`,
          open: true,
        });
      };

      const dbpaths = [
        { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
      ];
      let promises = dbpaths.map((item: any, index) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        return serverRequest("pld/socialhistory", params, () => {}, onFailure);
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
        loadReviewHistory();
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData, setOpenErrorDialog]);

  return (
    <TooltipCommonDataGrid
      type="socialSU"
      rows={displayRows}
      columns={columnDefs}
      enableStatusColFilter={true}
      noDataMsg="No history of Substance Use"
      onRowDoubleClick={(row: any) => {
        toPldWithPayLoad("SocialHistorySU", {
          key: "subuseid",
          value: row.row.subuseid,
        });
      }}
      columnVisibilityModel={{
        status: isShowInActive,
      }}
    />
  );
};
