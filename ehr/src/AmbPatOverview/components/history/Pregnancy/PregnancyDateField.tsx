/**
 * DateField.tsx
 *
 * @author: jfsys
 * @description Render allergy's Onset Date field
 */
import * as React from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import Divider from "@mui/material/Divider";
import Popover from "@mui/material/Popover";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { YearCalendar } from "@mui/x-date-pickers/YearCalendar";
import { MonthCalendar } from "@mui/x-date-pickers/MonthCalendar";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import dayjs from "dayjs";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import Time from "../../common/icons/Time";
import DisableTime from "../../common/icons/DisableTime";
import {
  pregStatusAtom,
  statedDueDateAtom,
  editedPregnancyAtom,
  openDateAtom,
  statedDueDateInvalidAtom,
} from "@cci-monorepo/AmbPatOverview/context/PregnancyAtoms";
import { nullorblank, checkDate } from "@cci-monorepo/common";

export const DateField = ({
  defaultValue,
  disabled,
}: {
  defaultValue?: string;
  disabled: boolean;
}) => {
  const pregStatus = useAtomValue(pregStatusAtom);
  const setStatedDueDate = useSetAtom(statedDueDateAtom);
  const [value, setValue] = React.useState(defaultValue);
  const [openDate, setOpenDate] = useAtom(openDateAtom);
  const setEditedPregnancy = useSetAtom(editedPregnancyAtom);
  const setStatedDueDateInvalid = useSetAtom(statedDueDateInvalidAtom);

  const [page, setPage] = React.useState<any>("year");
  const [error, setError] = React.useState<boolean>(false);
  const [anchor, setAnchor] = React.useState<null | HTMLElement>(null);
  const textFieldRef = React.useRef<null | HTMLElement>(null);

  const open = Boolean(anchor);
  const isDisabled = Boolean(disabled ? true : pregStatus !== "Yes");
  const popupId = open ? "pregnancy-popup" : undefined;

  React.useEffect(() => {
    if (openDate) {
      const target = textFieldRef.current;
      if (target) {
        setPage("year");
        setError(false);
        setAnchor(target);
      } else {
        setAnchor(null);
      }
    } else {
      setAnchor(null);
    }
  }, [openDate, setPage, setValue, setError, setAnchor]);

  React.useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue, setValue]);

  React.useEffect(() => {
    setStatedDueDateInvalid(error);
  }, [error, setStatedDueDateInvalid]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (isDisabled) return;
    if (!anchor) {
      setPage("year");
      setError(false);
    }
    setAnchor(anchor ? null : event.currentTarget);
  };

  const handleClose = () => {
    const target = textFieldRef.current as HTMLInputElement;
    if (target) {
      if (checkDate(target.value)) {
        setStatedDueDate(target.value);
        setError(false);
      } else {
        setError(true);
      }
    }
    setEditedPregnancy(true);
    setAnchor(null);
    setOpenDate(false);
  };

  const handleToday = () => {
    const formattime = dayjs().format("MM/DD/YYYY");
    setValue(formattime);
    setTimeout(() => {
      handleClose();
    }, 200);
  };

  const handleOK = () => {
    handleClose();
  };

  const handleYear = (v: any) => {
    const formattime = dayjs(v).format("YYYY");
    setPage("month");
    setValue(formattime);
  };

  const handleMonth = (v: any) => {
    const formattime = dayjs(v).format("MM") + "/" + value;
    setPage("day");
    setValue(formattime);
  };

  const handleDay = (v: any) => {
    const formattime = dayjs(v).format("MM/DD/YYYY");
    setValue(formattime);
  };

  return (
    <>
      <TextField
        className="Pregnancy-DatePicker"
        inputRef={textFieldRef}
        sx={{
          width: "100%",
          "& .MuiInputBase-root": {
            height: "32px",
            width: "154px",
          },
        }}
        disabled={isDisabled}
        placeholder={isDisabled ? "--/--/----" : "MM/DD/YYYY"}
        error={error}
        value={value}
        aria-describedby={popupId}
        onClick={handleClick}
        onChange={(event: any) => {
          setValue(event.target.value);
          if (event.target.value) {
            if (checkDate(event.target.value)) {
              setStatedDueDate(event.target.value);
              setError(false);
            } else {
              setError(true);
            }
          } else {
            setError(false);
          }
        }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton edge="end" disabled={isDisabled}>
                {isDisabled ? <DisableTime /> : <Time />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
      <Popover
        className="Pregnancy-popover"
        id={popupId}
        open={open}
        anchorEl={anchor}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <Box
          sx={{
            backgroundColor: "white",
            border: "1px solid lightgray",
            boxShadow: "5px 10px 10px lightgray",
          }}
        >
          {page !== "year" ? (
            <Stack
              direction="row"
              sx={{ margin: "10px 20px" }}
              justifyContent="flex-start"
              alignItems="center"
            >
              <Typography
                variant="h6"
                sx={{ fontWeight: "bold", lineHeight: "40px" }}
              >
                {value}
              </Typography>
              <ArrowDropDownIcon />
            </Stack>
          ) : (
            ""
          )}
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            {page === "year" ? (
              <YearCalendar
                sx={{ height: "200px" }}
                disableHighlightToday
                onChange={handleYear}
                defaultValue={
                  !nullorblank(value) && checkDate(value)
                    ? dayjs(value, "YYYY")
                    : null
                }
              />
            ) : page === "month" ? (
              <MonthCalendar
                sx={{ height: "200px" }}
                disableHighlightToday
                onChange={handleMonth}
              />
            ) : (
              <DateCalendar
                autoFocus={false}
                sx={{ height: "270px" }}
                disableHighlightToday
                defaultCalendarMonth={dayjs(value, "MM/YYYY")}
                onChange={handleDay}
                views={["day"]}
                slots={{
                  calendarHeader: () => <></>,
                }}
              />
            )}
          </LocalizationProvider>
          <Divider />
          <Stack
            direction="row"
            justifyContent="space-between"
            sx={{ height: "30px", margin: "10px 20px" }}
          >
            <Button variant="outlined" onClick={handleToday} size="small">
              Today
            </Button>
            {page === "day" ? (
              <Button variant="outlined" onClick={handleOK} size="small">
                Done
              </Button>
            ) : null}
          </Stack>
        </Box>
      </Popover>
    </>
  );
};
