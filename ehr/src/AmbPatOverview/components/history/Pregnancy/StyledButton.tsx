import { Button, styled } from "@mui/material";

const StyledButton = styled(Button)({
  height: "32px",
  padding: "0px 12px",
  borderRadius: "18px",
  fontWeight: "500",
  textTransform: "none",
  "&.<PERSON><PERSON><PERSON><PERSON>on-outlined": {
    border: "none",
    color: "#000",
    "&:disabled": {
      color: "#7C7C7C",
      backgroundColor: "#D6D6D6",
    },
  },
  "&.MuiButton-contained": {
    border: "none",
    color: "#fff",
    background: "#319436",
  },
});

export { StyledButton };
