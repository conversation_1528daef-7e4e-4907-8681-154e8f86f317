import { SHA256 } from "crypto-js";

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

// Parse server data for pregnancy
export const parsePregnancyData = (
  data: any,
  pldDbpaths: any,
  latestDbpath: any
) => {
  let pregnancyData: any = null;
  let reviewMessage: any = [];
  let checksum: string = "";

  if (data && data.length > 0) {
    pregnancyData = getLatestData(data, pldDbpaths, latestDbpath);
    reviewMessage = getReviewHistory(data);
    checksum = getDisplayPregnancyChecksum(pregnancyData);
  }
  return [pregnancyData, reviewMessage, checksum];
};

const getLatestData = (data: any, pldDbpaths: any, latestDbpath: any) => {
  let pregnancyData: any = {};
  if (data.length > 0) {
    const curDbpath = data[0];
    const pregStatusIdx = curDbpath.preg_status.header.indexOf("pregStatus");
    const lactStatusIdx = curDbpath.preg_status.header.indexOf("lactStatus");
    const statedDueDateIdx =
      curDbpath.preg_status.header.indexOf("pregDuedate");
    if (
      curDbpath.preg_status.data.length &&
      (!isNull(curDbpath.preg_status.data[0][pregStatusIdx]) ||
        !isNull(curDbpath.preg_status.data[0][lactStatusIdx]))
    ) {
      pregnancyData.pregStatus = curDbpath.preg_status.data[0][pregStatusIdx];
      pregnancyData.statedDueDate =
        curDbpath.preg_status.data[0][statedDueDateIdx];
      pregnancyData.lactStatus = curDbpath.preg_status.data[0][lactStatusIdx];
    } else {
      for (let index = 1; index < data.length; index++) {
        const item = data[index];
        const pregStatus: any =
          item.preg_status.data.length > 0
            ? item.preg_status.data[0][pregStatusIdx]
            : null;
        const lactStatus: any =
          item.preg_status.data.length > 0
            ? item.preg_status.data[0][lactStatusIdx]
            : null;
        if (!isNull(pregStatus) || !isNull(lactStatus)) {
          if (pregStatus === "Yes" || lactStatus === "Yes") {
            if (pregStatus === "Yes") {
              const pregExpireTimeIdx =
                item.preg_status.header.indexOf("pregExpireTime");
              const pregExpireTime: any =
                item.preg_status.data[0][pregExpireTimeIdx];
              const statedDueDate = item.preg_status.data[0][statedDueDateIdx];
              let fromTime: any = "";
              if (latestDbpath === pldDbpaths[0].dbpath) {
                fromTime =
                  Math.floor(Date.now() / 1000) + Cci.util.DateTime.timedelta;
              } else {
                fromTime = pldDbpaths[index].fromTime;
              }

              if (fromTime < pregExpireTime) {
                pregnancyData.pregStatus = pregStatus;
                pregnancyData.statedDueDate = statedDueDate;
              } else {
                pregnancyData.pregStatus = "";
                pregnancyData.statedDueDate = "";
              }
            }
            if (lactStatus === "Yes") {
              pregnancyData.lactStatus = lactStatus;
            } else {
              pregnancyData.lactStatus = "";
            }
          }
          pregnancyData.needAutoSave = true;
          break;
        }
      }
    }
  }
  return pregnancyData;
};

const getDisplayPregnancyChecksum = (data: any) => {
  const dataString = JSON.stringify(data);
  let checksum = SHA256(dataString).toString();
  return checksum;
};

// Review History
const getReviewHistory = (data: any) => {
  let ret: any = [];
  let reviewHistory: any = [];
  const field: string = "preg_reviewstatus";
  if (data.length > 0) {
    const item = data[0];
    if (item[field] && item[field].data.length > 0) {
      reviewHistory.push(convertArrayToObject(item[field]));
    }
  }

  ret = reviewHistory
    .reduce((prev: any, cur: any) => (prev ? prev.concat(cur) : cur), [])
    .sort((a: any, b: any) => {
      return a.reviewed_ts < b.reviewed_ts ? 1 : -1;
    })
    .map((item: any, index: number) => {
      return {
        id: index,
        status: item.status,
        review_status:
          item.status === "unable to assess"
            ? item.status + ", Reason:" + item.reason
            : item.status,
        reason: item.reason,
        user: item.user,
        reviewed_ts: item.reviewed_ts,
        fmt_reviewed_tm: item.fmt_reviewed_tm,
        last_reviewed:
          item.status !== "reviewed"
            ? "User was " +
              item.status +
              (!!item.reason ? ", Reason:" + item.reason : "") +
              " - By " +
              item.user +
              " at " +
              item.fmt_reviewed_tm
            : "Last reviewed by " + item.user + " at " + item.fmt_reviewed_tm,
        chksum: item.chksum,
      };
    });
  return ret;
};

export const isNull = (key: any) => {
  return !key || key === "" || key === undefined;
};
