import { useAtomValue, useSet<PERSON>tom, useAtom } from "jotai";
import SectionTitle from "../../common/SectionTitle";
import {
  Grid,
  Box,
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import { useEffect, useState } from "react";
import {
  pregStatusAtom,
  statedDueDateAtom,
  lactStatusAtom,
  setParseDataPregnancyAtom,
} from "@cci-monorepo/AmbPatOverview/context/PregnancyAtoms";
import { useSetErrorDialog, nullorblank } from "@cci-monorepo/common";
import { DateField } from "./PregnancyDateField";
import { StyledButton } from "./StyledButton";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { isHiddenPregnancyAtom } from "@cci-monorepo/AmbPatOverview/context/HistoryAtoms";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../../common/Constants";
import { HistoryToolbar } from "../HistoryToolbar";

export const Pregnancy = (props: any) => {
  const pregStatus = useAtomValue(pregStatusAtom);
  const statedDueDate = useAtomValue(statedDueDateAtom);
  const lactStatus = useAtomValue(lactStatusAtom);
  const [expanded, setExpanded] = useState<boolean>(false);
  const [isHiddenPregnancy, setIsHiddenPregnancy] = useAtom(
    isHiddenPregnancyAtom
  );

  const setOpenErrorDialog = useSetErrorDialog();
  const parseDataAtom = useSetAtom(setParseDataPregnancyAtom);

  const isEditable = false;

  const pregnancyOptions: string[] = ["Yes", "No", "Possibly Pregnant"];
  const lactationOptions: string[] = ["Yes", "No", "Unknown"];

  const pregnancyBtns = pregnancyOptions.map((option, idx) => (
    <Grid item key={"grid_" + idx}>
      <StyledButton
        disabled={!isEditable}
        className="Pregnancy-Button"
        variant={pregStatus === option ? "contained" : "outlined"}
      >
        {option}
      </StyledButton>
    </Grid>
  ));

  const lactationBtns = lactationOptions.map((option, idx) => (
    <Grid item key={"grid_" + idx}>
      <StyledButton
        disabled={!isEditable}
        className="Pregnancy-Button"
        variant={lactStatus === option ? "contained" : "outlined"}
      >
        {option}
      </StyledButton>
    </Grid>
  ));

  useEffect(() => {
    const onSuccess = (result: any) => {
      const isFemaleIdx = result.data.header.indexOf("isFemale");
      const isAgeOutIdx = result.data.header.indexOf("isAgeOut");
      const isFemale = result.data.data[0][isFemaleIdx];
      const isAgeOut = result.data.data[0][isAgeOutIdx];

      if (isFemale === "1" && isAgeOut === "0") {
        setIsHiddenPregnancy(false);
      } else {
        setIsHiddenPregnancy(true);
      }
    };

    const onFailure = (error: string) => {
      console.error("Error in getting pregnancy status", error);
    };

    const params: any = {};
    params.campus = Cci.RunTime.getEncounterInfo().campus || cci.cfg.campus;

    serverRequest("pld/pregnancy/check", params, onSuccess, onFailure);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setExpanded(
      !(
        nullorblank(pregStatus) &&
        nullorblank(statedDueDate) &&
        nullorblank(lactStatus)
      )
    );
  }, [pregStatus, statedDueDate, lactStatus, setExpanded]);

  useEffect(() => {
    if (isHiddenPregnancy) return;
    const onSuccess = (data: any) => {
      parseDataAtom(data);
    };

    // Callback to process data from server when failed
    const onFailure = (error: any) => {
      setOpenErrorDialog({
        text: "System has failed to load pregnancy data. " + error,
        open: true,
      });
    };

    const dbpaths = [
      { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
    ];
    let promises = dbpaths.map((item: any) => {
      const params = { campus: item.campus, dbs: item.dbpath };
      return serverRequest("pld/pregnancy", params, () => {}, onFailure);
    });
    Promise.all(promises).then((ret: any) => {
      onSuccess(ret);
    });
  }, [parseDataAtom, setOpenErrorDialog, isHiddenPregnancy]);

  if (isHiddenPregnancy) {
    return <></>;
  } else {
    return (
      <Accordion
        square={true}
        sx={{
          borderRadius: "8px",
          boxShadow:
            "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
        }}
        disableGutters
        expanded={expanded}
        id="history"
      >
        <AccordionSummary
          sx={{
            height: ModuleHeaderHeight,
            flexDirection: "row-reverse",
            backgroundColor: "#F2F2F2",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
            [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
              {
                transform: "rotate(90deg)",
              },
          }}
          expandIcon={
            <ArrowRightIcon
              color="primary"
              onClick={() => setExpanded(!expanded)}
            />
          }
          aria-controls="history-content"
          id="history-header"
        >
          <HistoryToolbar type="pregnancy" readonly={props.readonly} />
        </AccordionSummary>

        <AccordionDetails
          sx={{
            padding: 0,
            margin: 0,
            borderBottomLeftRadius: "8px",
            borderBottomRightRadius: "8px",
          }}
        >
          <Box
            sx={{
              padding: "20px",
            }}
            onDoubleClick={(e: React.MouseEvent<HTMLDivElement>) => {
              if (
                !(e.target as Element).className
                  .split(" ")
                  .some((i: string) =>
                    ["MuiBox-root", "MuiGrid-container"].includes(i)
                  )
              ) {
                toPldModule("Pregnancy");
              }
            }}
          >
            <SectionTitle title="Pregnant?" required />
            <Grid
              container
              spacing={"8px"}
              sx={{
                marginBottom: "16px",
              }}
            >
              {pregnancyBtns}
              <Grid
                item
                sx={{
                  marginLeft: "16px",
                }}
              >
                <SectionTitle
                  title="Stated Due Date"
                  required={pregStatus === "Yes"}
                />
              </Grid>
              <Grid item>
                <DateField
                  defaultValue={statedDueDate}
                  disabled={!isEditable}
                />
              </Grid>
            </Grid>
            <SectionTitle title="Lactating?" required />
            <Grid container spacing={"8px"}>
              {lactationBtns}
            </Grid>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  }
};
