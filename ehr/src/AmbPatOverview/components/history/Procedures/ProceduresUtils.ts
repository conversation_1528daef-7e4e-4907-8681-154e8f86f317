import { GridColDef } from "@mui/x-data-grid-pro";
import { randomId } from "@mui/x-data-grid-generator";

// Parse server data for procedures
export const parseProcedureData = (
  data: any,
  isEditable: boolean,
  index: number
) => {
  let mainGridRows: any = [];
  let mainGridColumns: GridColDef[] = [];
  let noneNit: number = -1;

  if (data && data.length > 0) {
    mainGridRows = getDisplayRows(data, index);
    noneNit = data[0]["nonenit"].data[0][0];
  }
  return [mainGridRows, mainGridColumns, noneNit];
};

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

// Get rows for the main data grid
const getDisplayRows = (data: any, index: number) => {
  let rows: any = [];
  let gridRows: any = [];
  let procedures: any = [];
  const field: string = "data";
  if (
    !data[index]["procedure_history"] ||
    data[index]["procedure_history"].data.length === 0
  ) {
    data.forEach((item: any) => {
      if (item[field] && item[field].data.length > 0) {
        procedures.push(convertArrayToObject(item[field]));
      }
    });
  } else if (data[index][field] && data[index][field].data.length > 0) {
    procedures.push(convertArrayToObject(data[index][field]));
  }
  procedures.forEach((procedure: any) => {
    procedure.forEach((element: any) => {
      if (
        !rows.some((row: any) => {
          return JSON.stringify(row) === JSON.stringify(element);
        })
      ) {
        rows.push(element);
      }
    });
  });
  rows.sort((a: any, b: any) => {
    return a.procedure > b.procedure ? 1 : -1;
  });
  let activeRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];

  // Add id to each row and group them by status
  rows.forEach((row: any) => {
    if (row.status === "Active") {
      activeRows.push({ ...row, id: randomId() });
    } else if (row.status === "Inactive") {
      inactiveRows.push({ ...row, id: randomId() });
    } else {
      errorRows.push({ ...row, id: randomId() });
    }
  });
  // Returns active, followed by inactive and error
  gridRows = activeRows.concat(inactiveRows, errorRows);
  return gridRows;
};
