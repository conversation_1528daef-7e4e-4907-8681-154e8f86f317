import React, { useEffect } from "react";
import { TooltipCommonDataGrid } from "../../common/TooltipCommonDataGrid";
import { useAtomValue, useSetAtom } from "jotai";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import {
  rowDataProcedureAtom,
  setParseDataProcedureAtom,
  setReviewMessageProcedureAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProceduresAtoms";
import { isShowInactiveProcAtom } from "@cci-monorepo/AmbPatOverview/context/HistoryAtoms";
import { dateComparator, useSetErrorDialog } from "@cci-monorepo/common";
import { getReviewHistory } from "../Family/FamilyUtils";
import { toPldWithPayLoad } from "@cci-monorepo/AmbPatOverview/utils/utils";
import CommonRenderCell from "../../common/CommonRenderCell";
import {
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../../common/Constants";
import { HistoryToolbar } from "../HistoryToolbar";

export const Procedures = (props: any) => {
  const [hasData, setHasData] = React.useState(false);
  const isShowInActive = useAtomValue(isShowInactiveProcAtom);
  const rowData = useAtomValue(rowDataProcedureAtom);
  const setOpenErrorDialog = useSetErrorDialog();

  const parseDataAtom = useSetAtom(setParseDataProcedureAtom);
  const setReviewMessageProcedure = useSetAtom(setReviewMessageProcedureAtom);
  const [expanded, setExpanded] = React.useState(false);

  const activeRows = React.useMemo(() => {
    return rowData.filter((row: { [key: string]: string | number }) => {
      return String(row.status).toUpperCase() === "ACTIVE";
    });
  }, [rowData]);

  const displayRows = React.useMemo(() => {
    if (isShowInActive) {
      return rowData;
    } else {
      return activeRows;
    }
  }, [isShowInActive, rowData, activeRows]);

  const columnDefs = [
    {
      field: "status",
      headerName: "Status",
      width: 140,
      disableColumnMenu: false,
      valueOptions: ["Active", "Inactive", "Error"],
      renderCell: CommonRenderCell,
    },
    {
      field: "procedure",
      headerName: "Procedure Name",
      minWidth: 150,
      flex: 1,
      disableColumnMenu: true,
      renderCell: CommonRenderCell,
    },
    {
      field: "dateperformed",
      headerName: "Date of Procedure",
      width: 190,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: CommonRenderCell,
    },
  ];

  useEffect(() => {
    setExpanded(activeRows.length > 0);
  }, [setExpanded, activeRows]);

  useEffect(() => {
    if (!hasData) {
      // Callback to process data from server when succeeded
      const onSuccess = (data: any) => {
        parseDataAtom(data, null, false, 0);
        setHasData(true);
      };

      // Callback to process data from server when failed
      const onFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load procedure data. " + error,
          open: true,
        });
      };

      const onHistorySuccess = (data: any) => {
        setReviewMessageProcedure(
          getReviewHistory(data, "procedure_rvhistory")
        );
      };

      const onHistoryFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load review history. " + error,
          open: true,
        });
      };

      const dbpaths = [
        { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
      ];
      let promises = dbpaths.map((item: any, index) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        return serverRequest("pld/procedure", params, () => {}, onFailure);
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
        const params = {
          campus: cci.cfg.campus,
          dbs: Cci.util.Patient.getDbpath(),
        };
        return serverRequest(
          "pld/procedure/review_history",
          params,
          onHistorySuccess,
          onHistoryFailure
        );
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData, parseDataAtom, setOpenErrorDialog]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <HistoryToolbar
          type="procedure"
          count={displayRows.length}
          readonly={props.readonly}
        />
      </AccordionSummary>

      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        {expanded && (
          <TooltipCommonDataGrid
            type="procedure"
            rows={displayRows}
            columns={columnDefs}
            enableStatusColFilter={true}
            noDataMsg="No procedure history"
            onRowDoubleClick={(row: any) => {
              toPldWithPayLoad("Procedures", {
                key: "snomedct",
                value: row.row.snomedct,
              });
            }}
            columnVisibilityModel={{
              status: isShowInActive,
            }}
          />
        )}
      </AccordionDetails>
    </Accordion>
  );
};
