import { useState, useMemo, useEffect } from "react";
import { TooltipCommonDataGrid } from "../../common/TooltipCommonDataGrid";
import { useAtomValue, useSetAtom } from "jotai";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import {
  rowDataConcernsAtom,
  setParseDataConcernsAtom,
  setReviewMessageConcernsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ConcernAtoms";
import { isShowInactiveConcernsAtom } from "@cci-monorepo/AmbPatOverview/context/HistoryAtoms";
import { dateComparator, useSetErrorDialog } from "@cci-monorepo/common";
import { getReviewHistory } from "./ConcernsUtils";
import { toPldWithPayLoad } from "@cci-monorepo/AmbPatOverview/utils/utils";
import CommonRenderCell from "../../common/CommonRenderCell";
import {
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../../common/Constants";
import { HistoryToolbar } from "../HistoryToolbar";

export const Concerns = () => {
  const [hasData, setHasData] = useState(false);
  const isShowInActive = useAtomValue(isShowInactiveConcernsAtom);
  const rowData = useAtomValue(rowDataConcernsAtom);
  const setOpenErrorDialog = useSetErrorDialog();
  const parseConcernsData = useSetAtom(setParseDataConcernsAtom);
  const setReviewMessageConcerns = useSetAtom(setReviewMessageConcernsAtom);
  const [expanded, setExpanded] = useState(false);

  const activeRows = useMemo(() => {
    return rowData.filter((row: { [key: string]: string | number }) => {
      return String(row.status).toUpperCase() === "ACTIVE";
    });
  }, [rowData]);

  let displayRows: Array<{ [key: string]: string | number }> = rowData;

  if (Object.keys(displayRows).length > 0 && !isShowInActive) {
    displayRows = displayRows.filter((row: any) => {
      return ["ACTIVE", "COMPLETED", "RESOLVED"].includes(
        String(row.status).toUpperCase()
      );
    });
  }

  const columnDefs = [
    {
      field: "status",
      headerName: "Status",
      width: 140,
      disableColumnMenu: false,
      valueOptions: ["Active", "Resolved", "Inactive", "Error"],
      renderCell: CommonRenderCell,
    },
    {
      field: "name",
      headerName: "Concern",
      minWidth: 150,
      flex: 1,
      disableColumnMenu: true,
      renderCell: CommonRenderCell,
    },
    {
      field: "startdate",
      headerName: "Start Date",
      width: 190,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: CommonRenderCell,
    },
    {
      field: "resolvedate",
      headerName: "Resolved Date",
      width: 190,
      disableColumnMenu: true,
      sortComparator: dateComparator,
      renderCell: CommonRenderCell,
    },
  ];

  useEffect(() => {
    setExpanded(activeRows.length > 0);
  }, [setExpanded, activeRows]);

  useEffect(() => {
    if (!hasData) {
      const onSuccess = (result: any) => {
        if (result && result.length > 0) {
          parseConcernsData(result, 0);
          setHasData(true);
        }
      };

      const onFailure = (error: string) => {
        setOpenErrorDialog({
          text: `System has failed to load concerns data: ${error}`,
          open: true,
        });
      };

      const onHistorySuccess = (data: any) => {
        setReviewMessageConcerns(getReviewHistory(data));
      };

      const onHistoryFailure = (error: any) => {
        setOpenErrorDialog({
          text: "System has failed to load review history. " + error,
          open: true,
        });
      };

      const dbpaths = [
        { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
      ];
      let promises = dbpaths.map((item: any, index) => {
        const params = { campus: item.campus, dbs: item.dbpath };
        return serverRequest("pld/concern", params, () => {}, onFailure);
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
        const params = {
          campus: cci.cfg.campus,
          dbs: Cci.util.Patient.getDbpath(),
        };
        return serverRequest(
          "pld/concern/review_history",
          params,
          onHistorySuccess,
          onHistoryFailure
        );
      });
    }
  }, [
    hasData,
    rowData,
    parseConcernsData,
    setOpenErrorDialog,
    setReviewMessageConcerns,
  ]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <HistoryToolbar type="concern" count={activeRows.length} />
      </AccordionSummary>

      <AccordionDetails
        sx={{
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <TooltipCommonDataGrid
          type="concern"
          rows={displayRows}
          columns={columnDefs}
          enableStatusColFilter={true}
          noDataMsg="No active concerns"
          onRowDoubleClick={(row: any) => {
            toPldWithPayLoad("Concerns", {
              key: "lgtime",
              value: row.row.lgtime,
            });
          }}
          columnVisibilityModel={{
            status: isShowInActive,
          }}
        />
      </AccordionDetails>
    </Accordion>
  );
};
