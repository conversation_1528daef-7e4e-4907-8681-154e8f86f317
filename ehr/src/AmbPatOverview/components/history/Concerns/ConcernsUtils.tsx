import { randomId } from "@mui/x-data-grid-generator";

// Review History
export const getReviewHistory = (data: any) => {
  let ret: any = [];
  let reviewHistory: any = [];
  const field: string = "concern_reviewhistory";
  if (data[field] && data[field].data.length > 0) {
    reviewHistory.push(convertArrayToObject(data[field]));
  }

  ret = reviewHistory
    .reduce((prev: any, cur: any) => (prev ? prev.concat(cur) : cur), [])
    .sort((a: any, b: any) => {
      return a.reviewed_ts < b.reviewed_ts ? 1 : -1;
    })
    .map((item: any, index: number) => {
      return {
        id: index,
        status: item.status,
        review_status:
          item.status === "unable to assess"
            ? item.status + ", Reason:" + item.reason
            : item.status,
        reason: item.reason,
        user: item.user,
        reviewed_ts: item.reviewed_ts,
        fmt_reviewed_tm: item.fmt_reviewed_tm,
        last_reviewed:
          item.status !== "reviewed"
            ? "User was " +
              item.status +
              (!!item.reason ? ", Reason:" + item.reason : "") +
              " - By " +
              item.user +
              " at " +
              item.fmt_reviewed_tm
            : "Last reviewed by " + item.user + " at " + item.fmt_reviewed_tm,
        chksum: item.chksum,
      };
    });
  return ret;
};

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

const isValidJson = (jsonString: any) => {
  try {
    JSON.parse(jsonString);
    return true;
  } catch (e) {
    return false;
  }
};

const getFullData = (rows: any) => {
  let ret: any = {};
  rows.forEach((row: any) => {
    let key = row.name;
    if (!ret[key]) ret[key] = [];
    ret[key].push(row);
  });

  // Sort
  Object.keys(ret).forEach((key: string) => {
    ret[key] = ret[key].sort((a: any, b: any) => {
      if (a.lgtime < b.lgtime) return 1;
      if (a.lgtime > b.lgtime) return -1;
      if (a.nit < b.nit) return 1;
      if (a.nit > b.nit) return -1;
      return 0;
    });
  });
  return ret;
};

// Get rows for the main data grid
const getDisplayRows = (data: any, index: number) => {
  let gridRows: any = [];
  let concerns: any = [];
  const field: string = "data";
  let noneNit: any = -1;
  let noneValue: boolean = false;
  if (!data[index]["history"] || data[index]["history"].data.length === 0) {
    data.forEach((item: any) => {
      if (item[field] && item[field].data.length > 0) {
        concerns.push(convertArrayToObject(item[field]));
      }
    });
  } else if (data[index][field] && data[index][field].data.length > 0) {
    concerns = convertArrayToObject(data[index][field]);
  }

  concerns = concerns.flat(1);
  concerns.forEach((concern: any) => {
    if (isValidJson(concern.jsonval)) {
      const json = JSON.parse(concern.jsonval);
      for (const key in json) {
        if (json.hasOwnProperty(key) && !(key in concern)) {
          concern[key] = json[key];
        }
      }

      Object.assign(concern, concern.Concern);

      delete concern.Concern;
      delete concern.jsonval;
    }
  });

  let fullData: any = getFullData(concerns);
  concerns = Object.keys(fullData).map((key: string) => {
    return fullData[key][0];
  });

  // filter deleted concern
  concerns = concerns.filter((entry: any) => entry.name !== "None");

  noneNit =
    concerns.find(
      (entry: any) => entry.jsonval === "NONE" || entry.jsonval === ""
    )?.nit || -1;

  if (noneNit !== -1) {
    noneValue =
      concerns.find((entry: any) => entry.nit === noneNit).jsonval === "NONE";
  } else {
    noneValue = false;
  }

  concerns.sort((a: any, b: any) => {
    return a.name > b.name ? 1 : -1;
  });
  let activeRows: any = [];
  let completedRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];

  // Add id to each row and group them by status
  concerns.forEach((row: any) => {
    if (row.status?.toLowerCase() === "active") {
      activeRows.push({ ...row, id: randomId() });
    } else if (row.status?.toLowerCase() === "inactive") {
      inactiveRows.push({ ...row, id: randomId() });
    } else if (row.status?.toLowerCase() === "resolved") {
      completedRows.push({ ...row, id: randomId() });
    } else if (row.status?.toLowerCase() === "error") {
      completedRows.push({ ...row, id: randomId() });
    }
  });
  // Returns active, followed by inactive and error
  gridRows = activeRows.concat(completedRows, inactiveRows, errorRows);

  return { gridRows, noneNit, noneValue };
};

// Parse server data for Concerns
export const parseConcernsData = (data: any, index: number) => {
  const { gridRows, noneNit, noneValue } = getDisplayRows(data, index);

  return [gridRows, noneValue];
};
