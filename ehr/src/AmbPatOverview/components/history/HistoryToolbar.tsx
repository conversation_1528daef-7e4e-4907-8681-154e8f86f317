/**
 * HistoryToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for History
 */

import { useAtom, useAtomValue } from "jotai";
import { useMemo } from "react";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink, IconEye } from "../../../common/assets";
import {
  isShowInactiveFamilyAtom,
  isShowInactiveSocialAtom,
  isShowInactiveProcAtom,
  isShowInactiveImplAtom,
  isShowInactiveConcernsAtom,
} from "../../context/HistoryAtoms";
import {
  hasReviewedFamilyHxAtom,
  reviewMessageFamilyHxAtom,
} from "@cci-monorepo/AmbPatOverview/context/FamilyAtoms";
import {
  hasReviewedSubstanceUseAtom,
  substanceUseNoneAtom,
  hasReviewedTobaccoAtom,
  tobaccoNoneAtom,
} from "@cci-monorepo/AmbPatOverview/context/SocialHistoryAtoms";
import {
  hasReviewedAlcoholAtom,
  noneAlcoholAtom,
} from "@cci-monorepo/AmbPatOverview/context/AlcoholAtoms";
import {
  hasReviewedProcedureAtom,
  noneAtom,
  reviewMessageProcedureAtom,
} from "@cci-monorepo/AmbPatOverview/context/ProceduresAtoms";
import {
  hasReviewedImplantsAtom,
  implantsNoneAtom,
  reviewMessageImplantsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ImplantsAtoms";
import {
  hasReviewedPregnancyAtom,
  reviewMessagePregnancyAtom,
} from "@cci-monorepo/AmbPatOverview/context/PregnancyAtoms";
import {
  hasReviewedConcernsAtom,
  concernsNoneAtom,
  reviewMessageConcernsAtom,
} from "@cci-monorepo/AmbPatOverview/context/ConcernAtoms";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";

interface HistoryToolbarProps {
  type: "pregnancy" | "social" | "family" | "procedure" | "implant" | "concern";
  count?: number;
  readonly?: boolean;
}

export const HistoryToolbar = (props: HistoryToolbarProps) => {
  const { type, count, readonly } = props;
  const hasReviewedFamily = useAtomValue(hasReviewedFamilyHxAtom);
  const hasReviewedAlcohol = useAtomValue(hasReviewedAlcoholAtom);
  const hasReviewedSU = useAtomValue(hasReviewedSubstanceUseAtom);
  const hasReviewedTobacco = useAtomValue(hasReviewedTobaccoAtom);
  const hasReviewedProc = useAtomValue(hasReviewedProcedureAtom);
  const hasReviewedImpl = useAtomValue(hasReviewedImplantsAtom);
  const hasReviewedPreg = useAtomValue(hasReviewedPregnancyAtom);
  const hasReviewedConcern = useAtomValue(hasReviewedConcernsAtom);

  const noneAlcohol = useAtomValue(noneAlcoholAtom);
  const socialHistoryNone = useAtomValue(substanceUseNoneAtom);
  const tobaccoNone = useAtomValue(tobaccoNoneAtom);
  const proceduresNone = useAtomValue(noneAtom);
  const implantsNone = useAtomValue(implantsNoneAtom);
  const concernsNone = useAtomValue(concernsNoneAtom);

  const [isShowInactiveFamily, setIsShowInactiveFamily] = useAtom(
    isShowInactiveFamilyAtom
  );
  const [isShowInactiveSocial, setIsShowInactiveSocial] = useAtom(
    isShowInactiveSocialAtom
  );
  const [isShowInactiveProc, setIsShowInactiveProc] = useAtom(
    isShowInactiveProcAtom
  );
  const [isShowInactiveImpl, setIsShowInactiveImpl] = useAtom(
    isShowInactiveImplAtom
  );
  const [isShowInactiveConcerns, setIsShowInactiveConcerns] = useAtom(
    isShowInactiveConcernsAtom
  );
  const reviewMessageFamilyHx = useAtomValue(reviewMessageFamilyHxAtom);
  const reviewMessageProcedure = useAtomValue(reviewMessageProcedureAtom);
  const reviewMessageImplants = useAtomValue(reviewMessageImplantsAtom);
  const reviewMessagePregnancy = useAtomValue(reviewMessagePregnancyAtom);
  const reviewMessageConcerns = useAtomValue(reviewMessageConcernsAtom);

  const hasReviewed = useMemo(() => {
    let hasReviewed = false;
    switch (type) {
      case "pregnancy":
        hasReviewed = hasReviewedPreg;
        break;
      case "social":
        hasReviewed =
          (hasReviewedAlcohol || noneAlcohol) &&
          (hasReviewedSU || socialHistoryNone) &&
          (hasReviewedTobacco || tobaccoNone);
        break;
      case "family":
        hasReviewed = hasReviewedFamily;
        break;
      case "procedure":
        hasReviewed = hasReviewedProc || proceduresNone;
        break;
      case "implant":
        hasReviewed = hasReviewedImpl || implantsNone;
        break;
      case "concern":
        hasReviewed = hasReviewedConcern || concernsNone;
        break;
    }
    return hasReviewed;
  }, [
    type,
    hasReviewedFamily,
    hasReviewedAlcohol,
    hasReviewedSU,
    hasReviewedTobacco,
    hasReviewedProc,
    hasReviewedImpl,
    hasReviewedPreg,
    hasReviewedConcern,
    noneAlcohol,
    socialHistoryNone,
    tobaccoNone,
    proceduresNone,
    implantsNone,
    concernsNone,
  ]);

  const reviewMessage = useMemo(() => {
    let reviewMessage = [];
    switch (type) {
      case "pregnancy":
        reviewMessage = reviewMessagePregnancy;
        break;
      case "family":
        reviewMessage = reviewMessageFamilyHx;
        break;
      case "procedure":
        reviewMessage = reviewMessageProcedure;
        break;
      case "implant":
        reviewMessage = reviewMessageImplants;
        break;
      case "concern":
        reviewMessage = reviewMessageConcerns;
        break;
    }
    return reviewMessage;
  }, [
    type,
    reviewMessagePregnancy,
    reviewMessageFamilyHx,
    reviewMessageProcedure,
    reviewMessageImplants,
    reviewMessageConcerns,
  ]);

  const isShowInactive: any = {
    family: isShowInactiveFamily,
    social: isShowInactiveSocial,
    procedure: isShowInactiveProc,
    implant: isShowInactiveImpl,
    concern: isShowInactiveConcerns,
  };

  const setIsShowInactive: any = {
    family: setIsShowInactiveFamily,
    social: setIsShowInactiveSocial,
    procedure: setIsShowInactiveProc,
    implant: setIsShowInactiveImpl,
    concern: setIsShowInactiveConcerns,
  };

  const handleRedirect = () => {
    switch (type) {
      case "pregnancy":
        toPldModule("Pregnancy");
        break;
      case "social":
        toPldModule("SocialHistorySU");
        break;
      case "family":
        toPldModule("FamilyHx");
        break;
      case "procedure":
        toPldModule("Procedures");
        break;
      case "implant":
        toPldModule("Implants");
        break;
      case "concern":
        toPldModule("Concerns");
        break;
    }
  };

  const handleShowInactiveClick = () => {
    setIsShowInactive[type](!isShowInactive[type]);
  };

  const menuItems: MenuItemProps[] = [
    ...(readonly
      ? []
      : [
          {
            label: "View In Health Profile",
            icon: <IconExternalLink />,
            handler: handleRedirect,
          },
        ]),
    {
      label: `${isShowInactive[type] ? "Hide" : "Show"} Inactive Items`,
      icon: <IconEye />,
      handler: handleShowInactiveClick,
    },
  ];

  const menuItems2: MenuItemProps[] = readonly
    ? []
    : [
        {
          label: "View In Health Profile",
          icon: <IconExternalLink />,
          handler: handleRedirect,
        },
      ];

  const formatTooltip = (
    moduleName: string,
    hasReviewed: Boolean,
    reviewMessage: any[]
  ) => {
    if (type === "social") {
      const notReviewed = [];
      if (!hasReviewedAlcohol) notReviewed.push("Alcohol");
      if (!hasReviewedSU) notReviewed.push("Substance Use");
      if (!hasReviewedTobacco) notReviewed.push("Tobacco");

      return notReviewed.length === 0
        ? "All Modules Reviewed"
        : `Social History-${notReviewed.join(", ").replace(/, ([^,]*)$/, ", & $1")} Not Reviewed`;
    }
    if (hasReviewed) {
      return `${reviewMessage.length > 0 && reviewMessage[0].last_reviewed}`;
    } else {
      return `${moduleName} Not Reviewed`;
    }
  };

  const names = {
    pregnancy: "Pregnancy & Lactation",
    social: "Social History",
    family: "Family History",
    procedure: "Procedures",
    implant: "Implants",
    concern: "Concerns",
  };
  const moduleNames = {
    pregnancy: "Pregnancy",
    social: "Substance Use",
    family: "Family",
    procedure: "Procedures",
    implant: "Implants",
    concern: "Concerns",
  };

  return (
    <CommonToolbar
      name={names[type]}
      count={count}
      isPldWidget={true}
      isCritical={false || (type === "pregnancy" && !hasReviewedPreg)}
      hasReviewed={hasReviewed}
      menuItems={type === "pregnancy" ? menuItems2 : menuItems}
      reviewMsg={formatTooltip(moduleNames[type], hasReviewed, reviewMessage)}
    />
  );
};
