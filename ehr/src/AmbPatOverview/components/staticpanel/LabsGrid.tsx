/**
 * LabsGrid.tsx
 *
 * @description Labs Grid on the right
 * <AUTHOR>
 */

import React from "react";
import { useEffect, useState } from "react";
import { useAtom } from "jotai";
import {
  isChangePeriodAtom,
  labsDataAtom,
  selectedPeriodAtom,
  selectedPeriodOptsAtom,
} from "@cci-monorepo/AmbPatOverview/context/LabsAtoms";
import { StickyColumnTable } from "./StickyColumnTable";
import {
  Box,
  Tab,
  Tabs,
  AccordionSummary,
  AccordionDetails,
  accordionSummaryClasses,
} from "@mui/material";
import { LabsH, LabsHH } from "../../../common/assets";
import { NoRowsOverlay } from "../common/CommonDataGrid";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../common/Constants";
import { LabsToolbar } from "./LabsToolbar";

export const LabsGrid = (props: any) => {
  const [tabValue, setTabValue] = useState(0);
  const [hasRequested, setHasRequested] = useState<any[]>([]);
  const [tabDataMap, setTabDataMap] = useState(new Map());
  const [selectedPeriod, setSelectedPeriod] = useAtom(selectedPeriodAtom);
  const [periodOpts, setPeriodOpts] = useAtom(selectedPeriodOptsAtom);
  const [tabsList, setTabsList] = useAtom(labsDataAtom);
  const [isChangePeriod, setIsChangePeriod] = useAtom(isChangePeriodAtom);
  const [expanded, setExpanded] = useState(true);

  useEffect(() => {
    getTimeOpts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (tabsList.length) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabsList, tabValue]);

  useEffect(() => {
    if (selectedPeriod && isChangePeriod) {
      getTabsList();
      setIsChangePeriod(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPeriod, isChangePeriod]);

  const getTabsList = async () => {
    const tabs: any = await getTabsServer(selectedPeriod);
    setTabsList(tabs);
    setHasRequested(new Array(tabs.length).fill(false));
  };

  const getTimeOpts = async () => {
    let tabExists: any = {
      "3": false,
      "6": false,
      "18": false,
    };

    const checkTabExists = (tabData: any) => {
      return tabData && tabData.length > 0 && tabData[0].label !== "None";
    };

    const tab6m: any = await getTabsServer("6");
    tabExists["6"] = checkTabExists(tab6m);
    if (tabExists["6"]) {
      setSelectedPeriod("6");
      setTabsList(tab6m);
      setHasRequested(new Array(tab6m.length).fill(false));
      tabExists["18"] = true;
      const tab3m: any = await getTabsServer("3");
      tabExists["3"] = checkTabExists(tab3m);
    } else {
      tabExists["3"] = false;
      const tab18m: any = await getTabsServer("18");
      tabExists["18"] = checkTabExists(tab18m);
      setSelectedPeriod("18");
      setTabsList(tab18m);
      setHasRequested(new Array(tab18m.length).fill(false));
    }

    const updatedPeriodOpts = periodOpts.map((item) => ({
      ...item,
      disabled: !tabExists[item.value],
    }));

    setPeriodOpts(updatedPeriodOpts);
  };

  const getTabsServer = (selectedPeriod: any) => {
    const params = {
      hobj: "labresults/confgen/panels",
      dbpath: Cci.util.Patient.getDbpath(),
      starttime: getPastNMonths(Number(selectedPeriod)),
      endtime: Math.floor(new Date().getTime() / 1000),
      screenconf: "labpanels.conf",
      odecode: "marray",
    };
    return new Promise((resolve, reject) => {
      Cci.RunTime.getData(params, function (result: any) {
        if (result?.conf?.data?.length) {
          const tabs = result.conf.data.map((item: any[]) => ({
            label: item[1],
            rpara: item[6],
            flag: item[15],
          }));
          setTabValue(0);
          resolve(tabs);
        } else {
          reject(new Error("Result does not contain expected data"));
        }
      });
    });
  };

  const handleTabsChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getPastNMonths = (numMonths: number) => {
    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - numMonths);
    return Math.floor(currentDate.getTime() / 1000);
  };

  const getDataSourceMap = (dataTips: any, values: any, annotations: any) => {
    let datasource: any[] = [];

    dataTips.map((dataTip: any, index: number) => {
      const itemMap = dataTip.map((item: any, itemIdx: number) => {
        let value = values[index][itemIdx],
          anno = annotations[index][itemIdx];
        if (value) {
          return {
            ...item,
            Value: value,
            Annotation: anno,
          };
        } else {
          return { ...item };
        }
      });
      datasource.push(itemMap);
    });
    return datasource;
  };

  const loadData = async () => {
    try {
      if (!hasRequested[tabValue] && !tabsList[tabValue]?.rpar) {
        Cci.RunTime.getDataList(
          [
            {
              hobj: "labresults/getdata/bypanels",
              rpara: tabsList[tabValue].rpara,
              dbpath: Cci.util.Patient.getDbpath(),
              starttime: getPastNMonths(Number(selectedPeriod)),
              endtime: Math.floor(new Date().getTime() / 1000),
            },
          ],
          function (retArray: any) {
            const result = JSON.parse(retArray[0].jresult);
            const columns = result.rl?.map((i: any) => i.data);
            const td = result.rd?.slice(1) || [];
            const tt = result.t?.slice(1) || [];
            const ta = result.a?.slice(1) || [];
            const values = td[0]?.map((_: any, colIndex: any) =>
              td.map((row: any) => row[colIndex])
            );
            const dataTips = tt[0]?.map((_: any, colIndex: any) =>
              tt.map((row: any) => row[colIndex])
            );
            const annotations = ta[0]?.map((_: any, colIndex: any) =>
              ta.map((row: any) => row[colIndex])
            );
            const datasource = getDataSourceMap(dataTips, values, annotations);
            const timeHeader = (result.cl?.slice(1) || []).map((item: any) =>
              Cci.util.DateTime.serverSecsToTimeStr(item)
            );

            const dataFlag = result.fl?.slice(1) || [];
            setHasRequested((prevState) =>
              prevState.map((val, i) => (i === tabValue ? true : val))
            );
            setTabDataMap((prevMap) => {
              return prevMap.set(tabValue, {
                datasource,
                columns,
                timeHeader,
                dataFlag,
              });
            });
          },
          function () {
            setHasRequested((prevState) =>
              prevState.map((val, i) => (i === tabValue ? true : val))
            );
            setTabDataMap((prevMap) => {
              return prevMap.set(tabValue, {
                datasource: [],
                columns: [],
                timeHeader: [],
                dataFlag: [],
              });
            });
          }
        );
      }
    } catch (error) {
      console.log("Error fetching data:", error);
    }
  };

  const redSquare: any = {
    labresult_abnormal: <LabsH style={{ width: 12, height: 12 }} />,
    labresult_critical: <LabsHH style={{ width: 12, height: 12 }} />,
  };

  const tableData = tabDataMap.get(tabValue) || {
    datasource: [],
    columns: [],
    timeHeader: [],
  };

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <LabsToolbar readonly={props.readonly} />
      </AccordionSummary>

      <AccordionDetails
        sx={{
          backgroundColor: "#F2F2F2",
          padding: "0 0 8px 0",
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <Box
          sx={{
            width: "100%",
            backgroundColor: "#F2F2F2",
            textAlign: "center",
          }}
        >
          {tabsList.length && tabsList[0].label !== "None" ? (
            <>
              <Box
                sx={{
                  "& .MuiTabs-root": { minHeight: 40 },
                }}
              >
                <Tabs
                  value={tabValue}
                  onChange={handleTabsChange}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    "& .MuiTab-root.Mui-selected": {
                      color: "#000",
                    },
                    "& .MuiTabs-indicator": { backgroundColor: "#F9D169" },
                    "& .MuiTab-root": {
                      minHeight: 40,
                      textTransform: "none",
                      font: "normal 500 15px Roboto",
                    },
                  }}
                >
                  {tabsList.map((item: any, index: number) => (
                    <Tab
                      icon={<>{redSquare[item.flag]}</>}
                      iconPosition="start"
                      label={item.label}
                      value={index}
                      key={index}
                    />
                  ))}
                </Tabs>
              </Box>
              <StickyColumnTable
                tableData={tableData}
                // tableStyle={{ maxHeight: "calc(100vh - 600px)" }}
                onDoubleClick={() =>
                  Cci.RunTime.onLaunchApp2({
                    list: "labs",
                    app: "Panels",
                  })
                }
              />
            </>
          ) : (
            <NoRowsOverlay
              height="150px"
              msg={
                <>
                  No lab results
                  <br />
                  within the last {selectedPeriod} months
                </>
              }
            />
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};
