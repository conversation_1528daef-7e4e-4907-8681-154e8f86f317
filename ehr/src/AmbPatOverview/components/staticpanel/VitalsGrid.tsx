/**
 * VitalsGrid.tsx
 *
 * @description Vital Signs Grid on the right panel
 * <AUTHOR>
 */

import { useEffect, useState } from "react";
import { serverNow, useSetToastFamily } from "@cci-monorepo/common";
import { StickyColumnTable } from "./StickyColumnTable";
import { VitalsColumnTable } from "./VitalsColumnTable";
import Config from "@cci-monorepo/config/Config";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../common/Constants";
import { VitalsToolbar } from "./VitalsToolbar";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  accordionSummaryClasses,
  Typography,
} from "@mui/material";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  hasReviewedVitalsAtom,
  modifiedVitals<PERSON>tom,
  nowColumnTimeAtom,
  updateData<PERSON><PERSON>,
  vitalResultAtom,
} from "@cci-monorepo/AmbPatOverview/context/VitalSignsAtoms";
import {
  formDataAtom,
  saveUndoAtom,
  undoRedoFlagAtom,
} from "@cci-monorepo/AmbPatOverview/context/UndoRedoAtoms";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";

interface UpdateType {
  name: string;
  time: string;
  timeStr: string;
}

export const VitalsGrid = ({ readonly }: any) => {
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setReviewedVitals = useSetAtom(hasReviewedVitalsAtom);
  const nowColumnTime = useAtomValue(nowColumnTimeAtom);
  const [modifiedVitals, setModifiedVitals] = useAtom(modifiedVitalsAtom);
  const [vitalsResult, setVitalsResult] = useAtom(vitalResultAtom);
  const [vitalsData, setVitalsData] = useState<any>({
    datasource: [],
    columns: [],
    timeHeader: [],
    dataFlag: [],
  });
  const [expanded, setExpanded] = useState(true);
  const setUpdateData = useSetAtom(updateDataAtom);
  const updateData = useAtomValue(updateDataAtom);
  const [formData, setFormData] = useAtom(formDataAtom);
  const [undoRedoFlag, setUndoRedoFlag] = useAtom(undoRedoFlagAtom);
  const saveUndo = useSetAtom(saveUndoAtom);

  const compareArrays = (arr1: any[], arr2: any[]) => {
    const map1 = new Map(
      arr1.map((item: any) => {
        const str = `${item.jit}-${item.key || ""}-${item.nit || ""}`;
        return [str, item];
      })
    );
    const map2 = new Map(
      arr2.map((item: any) => {
        const str = `${item.jit}-${item.key || ""}-${item.nit || ""}`;
        return [str, item];
      })
    );

    const added = arr2.filter((item) => {
      const str = `${item.jit}-${item.key || ""}-${item.nit || ""}`;
      return !map1.has(str);
    });
    const removed = arr1.filter((item) => {
      const str = `${item.jit}-${item.key || ""}-${item.nit || ""}`;
      return !map2.has(str);
    });

    const changed = arr2.filter((item2) => {
      const str = `${item2.jit}-${item2.key || ""}-${item2.nit || ""}`;
      const item1 = map1.get(str);
      return item1 && JSON.stringify(item1) !== JSON.stringify(item2);
    });

    return { added, removed, changed };
  };

  const getData = (callback?: any) => {
    Cci.RunTime.getDataList(
      [
        {
          hobj: "ambPatOverview/vitalsign",
          dbpath: Cci.util.Patient.getDbpath(),
        },
      ],
      function (retArray: any[]) {
        setVitalsResult(retArray);
        if (callback) {
          callback(retArray);
        }
      }
    );
  };

  const onDoubleClick = (rowData?: any) => {
    const selectedTask = {
      majorIT: rowData?.jit,
      minorIT: rowData?.nit || "0",
      taskTime: rowData?.key,
    };
    Cci.RunTime.observetime = rowData?.key;
    Cci.RunTime.setSelectedTask(selectedTask);
    if (Config.inDevMode) {
      return;
    }
    Cci.RunTime.onLaunchApp2({
      list: "fs",
      app: "Vitals",
    });
  };

  const undoRedoSave = () => {
    const vital = formData.vital;
    const onSuccess = () => {
      setShowToast({
        type: "success",
        text: "Vital Signs updated!",
        open: true,
      });
      getData();
    };
    const onFailure = (error: string) => {
      console.error(error);
    };

    const compareData = compareArrays(vitalsResult, vital);
    let vitalList: any = [];
    if (compareData.changed.length) {
      vitalList = vitalList.concat(compareData.changed);
    }
    if (compareData.removed.length) {
      const list = compareData.removed.map((m) => {
        const item = { ...m };
        item.data = "";
        return item;
      });
      vitalList = vitalList.concat(list);
    }
    if (!vitalList.length) {
      return;
    }
    const modifiedDatas = vitalList.map((item: any) => {
      return {
        jit: item.jit,
        nit: item.nit ?? 0,
        ks: item.key,
        data: item.data || "",
        type: "Vital Signs",
        disrowcpoe2: 1,
      };
    });
    let params = {
      fsdata: modifiedDatas,
      staffid: Cci.util.Staff.getSid(),
      dbpath: Cci.util.Patient.getDbpath(),
      appname: "FLOWSHEET",
      perm: "E",
    };

    serverRequest("FSdata/savedata", params, onSuccess, onFailure);
  };

  const onSaveVitals = () => {
    if (modifiedVitals.length === 0) {
      getData();
      return;
    }

    const onSuccess = (ret: any) => {
      setShowToast({
        type: "success",
        text: "Vital Signs updated!",
        open: true,
      });
      setModifiedVitals([]);
      getData((data: any) => {
        saveUndo({
          ...formData,
          vital: data,
        });
      });
    };
    const onFailure = (error: string) => {
      console.error(error);
    };

    let params = {
      fsdata: modifiedVitals,
      staffid: Cci.util.Staff.getSid(),
      dbpath: Cci.util.Patient.getDbpath(),
      appname: "FLOWSHEET",
      perm: "E",
    };

    serverRequest("FSdata/savedata", params, onSuccess, onFailure);
  };

  const getTrueTime = (sec: string | number) => {
    let time = Cci.util.DateTime.serverSecsToTimeStr(sec, "hh:mm a MM/DD/YYYY");
    let hour = time.substring(0, 2);
    if (Number(hour) >= 12) {
      hour = "0" + (Number(hour) - 12);
    }
    return time.replace(time.substring(0, 2), hour);
  };

  useEffect(() => {
    getData((data: any) => {
      setFormData((prev: any) => ({ ...prev, vital: data }));
    });
  }, []);

  useEffect(() => {
    if (Array.isArray(formData.vital) && undoRedoFlag) {
      undoRedoSave();
      let timer = setTimeout(() => {
        setUndoRedoFlag(false);
      }, 0);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [formData.vital, undoRedoFlag]);

  useEffect(() => {
    const displaynameDataMap: Record<string, Record<string, any>> = {};
    const itemObject: Record<string, { jit: string; nit: string }> = {};
    const timestampsSet = new Set<number>();
    const updateList: UpdateType[] = [];
    vitalsResult.forEach((item: any) => {
      const { displayname, key, jit, nit } = item;
      if (item.lgname && item.lgtime) {
        updateList.push({
          name: item.lgname,
          time: item.lgtime,
          timeStr: item.lgtimestr,
        });
      }
      if (displayname && jit) {
        itemObject[item.displayname] = { jit, nit };
      }
      if (!displaynameDataMap[displayname]) {
        displaynameDataMap[displayname] = {};
      }
      displaynameDataMap[displayname][key] = item;
      if (key && !isNaN(Number(key))) {
        timestampsSet.add(Number(key));
      }
    });
    let timestamps = Array.from(timestampsSet).sort((a, b) => a - b);
    let datasource: any[] = [];
    let columns: any[] = [];
    if (nowColumnTime !== -1 && !timestamps.includes(nowColumnTime)) {
      timestamps.push(nowColumnTime);
    }

    Object.entries(displaynameDataMap).forEach(
      ([displayname, timestampData]) => {
        const dataRow = timestamps.map(
          (timestamp) =>
            timestampData[timestamp] || {
              key: String(timestamp),
              jit: itemObject[displayname].jit,
              nit: itemObject[displayname].nit,
            }
        );
        datasource.push(dataRow);
        columns.push(displayname);
      }
    );

    const timeHeader = timestamps.map((item: number) =>
      Cci.util.DateTime.serverSecsToTimeStr(item)
    );

    setVitalsData({
      datasource,
      columns,
      timeHeader,
    });

    updateList.sort((a, b) => Number(a.time) - Number(b.time));
    const len = updateList.length;
    if (len) {
      const updater = {
        name: updateList[len - 1].name,
        time: getTrueTime(updateList[len - 1].time),
      };
      setUpdateData(updater);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [vitalsResult, nowColumnTime]);

  useEffect(() => {
    if (vitalsData.datasource.length > 0) {
      let reviewed = true,
        now = Cci.util.DateTime.dateToServerUnixtime(serverNow());
      for (let i = 0; i < vitalsData.datasource.length; i++) {
        let vitalItemArray = vitalsData.datasource[i];
        let vitalItem = vitalItemArray.findLast((item: any) => item?.data);
        if (!vitalItem || vitalItem.key <= now - 86400) {
          reviewed = false;
          break;
        }
      }
      setReviewedVitals(reviewed);
    } else {
      setReviewedVitals(false);
    }
  }, [vitalsData]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <VitalsToolbar readonly={readonly} />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          backgroundColor: "#F2F2F2",
          padding: 0,
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <Box
          sx={{
            paddingBottom: "8px",
            textAlign: "center",
          }}
        >
          {readonly ? (
            <StickyColumnTable
              type="vitals"
              tableData={vitalsData}
              onDoubleClick={onDoubleClick}
              onBlur={onSaveVitals}
              editable={!readonly}
            />
          ) : (
            <VitalsColumnTable
              type="vitals"
              tableData={vitalsData}
              onDoubleClick={onDoubleClick}
              onBlur={onSaveVitals}
              editable={!readonly}
            />
          )}
          {!readonly && updateData.name && (
            <Typography
              sx={{
                font: "400 12px Roboto",
                color: "rgba(0, 0, 0, 0.58)",
                textAlign: "left",
                marginTop: "10px",
                paddingLeft: "10px",
              }}
            >
              {`Last edited by ${updateData.name} at ${updateData.time}`}
            </Typography>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};
