import { useEffect, useRef } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Typography from "@mui/material/Typography";
import Paper from "@mui/material/Paper";
import { NoRowsOverlay } from "../common/CommonDataGrid";
import { StyledNameTooltip } from "../common/Constants";
import { Grid, Fade, Divider } from "@mui/material";
import { VitalsCell } from "./VitalsCell";

interface StickyColumnTableProps {
  tableData: {
    datasource: any[];
    columns: any[];
    timeHeader: any[];
    dataFlag?: any[];
  };
  onDoubleClick: (rowData?: any) => void;
  onBlur?: () => void;
  tableStyle?: any;
  type?: "vitals" | "";
  editable?: boolean;
}
export const StickyColumnTable = (props: StickyColumnTableProps) => {
  const { tableData, type, tableStyle, onDoubleClick, onBlur } = props;
  const tableRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (tableRef.current) {
      if (tableRef.current.offsetWidth < tableRef.current.scrollWidth) {
        tableRef.current.scrollLeft = tableRef.current.scrollWidth;
      }
    }
  }, [tableData]);

  const stickyStyle: any = {
    position: "sticky",
    left: 0,
    zIndex: 100,
    background: "#e9e9e9",
    boxShadow: "4px 0 4px rgba(0,0,0,0.15)",
    font: "bold 15px Helvetica",
    minWidth: 82,
    width: 82,
  };

  const highLightStyle = (status: string, type?: string) => {
    if (!status) return;
    if (type === "vitals") {
      return {
        font: "700 14px Helvetica",
        color: status,
      };
    }

    const abnormal = {
      font: "700 14px Helvetica",
      color: "#900000",
    };

    const cellflagcritical = {
      font: "700 14px Helvetica",
      color: "#ff0006",
    };

    if (status === "L" || status === "H") return abnormal;
    else if (status === "LL" || status === "HH") return cellflagcritical;
  };

  const TooltipDetailCell = (item: any) => {
    let titleAndKeys: any;
    if (type === "vitals") {
      titleAndKeys = [
        ["Full Name", "fullname"],
        ["Display Name", "displayname"],
        ["Value", "data"],
        ["Unit of Measure", "unit"],
        ["Time", "strkey"],
        ["Stored at", "lgtimestr"],
        ["Stored By", "lgname"],
      ];
    } else {
      titleAndKeys = [
        ["Panel", "Panel"],
        ["Specimen", "Specimen"],
        ["Name", "Name"],
        ["Value", "Value"],
        ["Flag", "Flag"],
        ["Normal Range", "Normal Range"],
        ["Annotation", "Annotation"],
        ["Stored At", "Stored At"],
        ["Stored By", "Stored By"],
      ];
    }
    return (
      <Grid container>
        {type === "vitals" ? (
          <></>
        ) : (
          <Grid container>
            <Grid item xs={12}>
              <div
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  fontFamily: "Roboto",
                }}
              >
                {item.Name}
              </div>
            </Grid>
            <Grid item xs={12}>
              <div
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  fontFamily: "Roboto",
                }}
              >
                {item["Stored At"]}
              </div>
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ m: "8px 0" }} />
            </Grid>
          </Grid>
        )}
        {titleAndKeys.map((value: any, index: number) => (
          <Grid container key={index}>
            <Grid item xs={5}>
              <Typography
                sx={{
                  fontSize: type === "vitals" ? "13px" : "16px",
                  fontWeight: type === "vitals" ? "700" : "400",
                  fontFamily: "Roboto",
                }}
              >
                {value[0] + ":"}
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <Typography
                sx={{
                  fontSize: type === "vitals" ? "13px" : "16px",
                  fontWeight: type === "vitals" ? "400" : "700",
                  fontFamily: "Roboto",
                }}
              >
                {value[1] && item ? Ext.String.htmlDecode(item[value[1]]) : ""}
              </Typography>
            </Grid>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <TableContainer
      style={{
        borderTop: "1px solid #C2C2C2",
        ...tableStyle,
      }}
      ref={tableRef}
      component={Paper}
    >
      <Table
        stickyHeader
        size="small"
        sx={{
          borderRight: "1px solid #C2C2C2",
          "& .MuiTableCell-root": {
            padding: "3px 6px",
          },
        }}
      >
        <TableHead>
          <TableRow>
            <TableCell sx={{ ...stickyStyle, zIndex: 101 }}>
              <br />
            </TableCell>
            {!!tableData?.timeHeader?.length &&
              tableData.timeHeader.map((item: any, index: number) => (
                <TableCell
                  key={index}
                  sx={{
                    background: "#e9e9e9",
                    textAlign: "center",
                    padding: "1px 2px !important",
                  }}
                  data-qtip={item?.slice(4)}
                >
                  <Typography
                    sx={{ font: "700 16px Helvetica", color: "#000" }}
                  >
                    {type === "vitals"
                      ? item?.substring(0, 2) + ":" + item?.substring(2, 4)
                      : item?.substring(0, 4)}
                  </Typography>
                  <Typography
                    sx={{
                      font: "12px Helvetica",
                      whiteSpace: "nowrap",
                      color: "#3F4853",
                    }}
                  >
                    {type === "vitals"
                      ? item?.substring(5, 11) + " '" + item?.substring(14)
                      : item?.substring(5, 11)}
                  </Typography>
                </TableCell>
              ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {tableData.datasource?.map((row: any[], rowIndex: number) => {
            return (
              <TableRow
                key={rowIndex}
                sx={{
                  "&:hover .MuiTableCell-root": {
                    background: `${tableData.timeHeader?.length ? "#F6E596" : "#e9e9e9"} `,
                  },
                }}
              >
                <TableCell sx={stickyStyle}>
                  {tableData.columns[rowIndex]}
                </TableCell>
                {tableData.timeHeader?.length
                  ? row.map((item: any, index: any) => {
                      if (type === "vitals") {
                        return (
                          <VitalsCell
                            index={index}
                            title={TooltipDetailCell(item)}
                            cellItem={item}
                            editable={props.editable ? true : false}
                            onDoubleClick={onDoubleClick}
                            onBlur={onBlur}
                          />
                        );
                      } else {
                        if (item?.Value) {
                          return (
                            <StyledNameTooltip
                              key={index}
                              enterDelay={800}
                              TransitionComponent={Fade}
                              TransitionProps={{
                                timeout: 200,
                              }}
                              title={TooltipDetailCell(item)}
                              sx={{ maxWidth: 300 }}
                            >
                              <TableCell
                                onDoubleClick={() => onDoubleClick(item)}
                                key={index}
                                sx={{
                                  font: "normal 14px Helvetica",
                                  textAlign: "center",
                                  ...highLightStyle(
                                    tableData?.dataFlag?.[index]?.[rowIndex],
                                    type
                                  ),
                                }}
                              >
                                {item.Value}
                              </TableCell>
                            </StyledNameTooltip>
                          );
                        } else {
                          return (
                            <TableCell
                              onDoubleClick={() => onDoubleClick(item)}
                              key={index}
                              sx={{
                                font: "normal 14px Helvetica",
                                textAlign: "center",
                                ...highLightStyle(
                                  tableData?.dataFlag?.[index]?.[rowIndex],
                                  type
                                ),
                              }}
                            >
                              {item?.Value}
                            </TableCell>
                          );
                        }
                      }
                    })
                  : rowIndex === Math.floor(tableData.columns.length / 2) &&
                    type === "vitals" && (
                      <TableCell
                        sx={{
                          border: "none",
                          padding: "0 !important",
                          "& .MuiStack-root": {
                            paddingTop: 0,
                            background: "#fff",
                          },
                        }}
                      >
                        <NoRowsOverlay msg="No vitals recorded" />
                      </TableCell>
                    )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
