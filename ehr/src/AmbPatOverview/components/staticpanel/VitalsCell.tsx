import React, { useEffect, useState } from "react";
import { Fade, TableCell } from "@mui/material";
import { InputField } from "@cci/mui-components";
import { serverNow } from "@cci-monorepo/common";
import { StyledNameTooltip } from "../common/Constants";
import { useAtom, useAtomValue } from "jotai";
import {
  nowColumnTimeAtom,
  modifiedVitalsAtom,
} from "@cci-monorepo/AmbPatOverview/context/VitalSignsAtoms";

interface VitalsCellProps {
  index: number;
  title: React.ReactNode;
  cellItem: any;
  editable: boolean;
  onDoubleClick: (rowData?: any) => void;
  onBlur?: () => void;
}
export const VitalsCell = ({
  index,
  title,
  cellItem,
  editable,
  onDoubleClick,
  onBlur,
}: VitalsCellProps) => {
  const nowColumnTime = useAtomValue(nowColumnTimeAtom);
  const [modifiedVitals, setModifiedVitals] = useAtom(modifiedVitalsAtom);
  const [editing, setEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(cellItem?.data);

  const highLightStyle = (status: string) => {
    if (!status) return;
    return {
      font: "700 14px Helvetica",
      color: status,
    };
  };

  const handleChange = (newValue: any) => {
    setCurrentValue(newValue);
    updataModifiedData(newValue);
  };

  const updataModifiedData = (newValue: string) => {
    let newModifiedVitals = Array.from(modifiedVitals);
    let findIndex = newModifiedVitals.findIndex(
      (item) => item.jit === cellItem.jit && item.ks === cellItem.key
    );
    if (findIndex > -1) {
      if (newValue !== cellItem?.data) {
        newModifiedVitals[findIndex].data = newValue;
      } else {
        newModifiedVitals.splice(findIndex, 1);
      }
    } else {
      if (newValue !== cellItem?.data) {
        let newItem = {
          jit: cellItem.jit,
          nit: cellItem.nit ?? 0,
          ks: cellItem.key,
          data: newValue,
          type: "Vital Signs",
          disrowcpoe2: 1,
        };
        newModifiedVitals.push(newItem);
      }
    }
    setModifiedVitals(newModifiedVitals);
  };

  const handleEdit = () => {
    if (!editable) {
      return;
    }
    let now = Cci.util.DateTime.dateToServerUnixtime(serverNow());
    if (cellItem?.key > now - 86400) {
      setEditing(true);
    }
  };

  useEffect(() => {
    setCurrentValue(cellItem.data);
    if (editable && cellItem.key == nowColumnTime && !cellItem.data) {
      setEditing(true);
    } else {
      setEditing(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cellItem, nowColumnTime]);

  return (
    <>
      {editing ? (
        <TableCell
          key={index}
          sx={{
            font: "normal 14px Helvetica",
            textAlign: "center",
            width: "72px",
            minWidth: "72px",
          }}
        >
          <InputField
            dataType="text"
            data={currentValue}
            setData={handleChange}
            onBlur={onBlur}
            sx={{
              "& .MuiInputBase-input": {
                "&.MuiInputBase-inputMultiline": {
                  paddingTop: "2px",
                  verticalAlign: "center",
                  minHeight: "20px",
                },
              },
              "& .MuiInputBase-root": {
                minHeight: "20px",
                height: "20px",
              },
            }}
          />
        </TableCell>
      ) : currentValue ? (
        <StyledNameTooltip
          key={"" + index}
          enterDelay={800}
          TransitionComponent={Fade}
          TransitionProps={{ timeout: 200 }}
          title={title}
          sx={{ maxWidth: 300 }}
        >
          <TableCell
            onDoubleClick={() => onDoubleClick(cellItem)}
            key={index}
            sx={{
              font: "normal 14px Helvetica",
              textAlign: "center",
              height: "20px",
              width: "72px",
              minWidth: "72px",
              ...highLightStyle(cellItem?.abnormalFlag),
            }}
            onClick={handleEdit}
          >
            {currentValue}
          </TableCell>
        </StyledNameTooltip>
      ) : (
        <TableCell
          onClick={handleEdit}
          onDoubleClick={() => onDoubleClick(cellItem)}
          key={index}
          sx={{
            font: "normal 14px Helvetica",
            textAlign: "center",
            height: "20px",
            width: "72px",
            minWidth: "72px",
            ...highLightStyle(cellItem?.abnormalFlag),
          }}
        >
          {currentValue}
        </TableCell>
      )}
    </>
  );
};
