/**
 * DiagnosticsToolbar.tsx
 *
 * @author: jfsys
 * @description Diagnostics Toolbar
 */

import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink } from "../../../common/assets";

export const DiagnosticsToolbar = (props: any) => {
  const handleRedirect = () => {
    Cci.RunTime.onLaunchApp2({
      list: "labs",
    });
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "View In Lab Results Viewer",
      icon: <IconExternalLink />,
      handler: handleRedirect,
    },
  ];

  return (
    <CommonToolbar
      name="Diagnostic Results"
      count={props.count}
      totalCount={props.totalCount}
      menuItems={menuItems}
    />
  );
};
