/**
 * LabsToolbar.tsx
 *
 * @author: jfsys
 * @description Labs Toolbar
 */

import React, { useState } from "react";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Grid, Select, Typography, IconButton } from "@mui/material";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { IconExternalLink, POCTIcon } from "../../../common/assets";
import { ModuleHeaderSx, USERCONTEXT } from "../common/Constants";
import { MenuItemProps } from "../common/CommonToolbar";
import {
  isChangePeriodAtom,
  selectedPeriodAtom,
  selectedPeriodOptsAtom,
} from "../../context/LabsAtoms";
import { userContextAtom } from "../../context/CommonAtoms";

export const LabsToolbar = (props: any) => {
  const [value, setValue] = useAtom(selectedPeriodAtom);
  const periodOpts = useAtomValue(selectedPeriodOptsAtom);
  const setIsChangePeriod = useSetAtom(isChangePeriodAtom);
  const userContext = useAtomValue(userContextAtom);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMoreClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRedirect = () => {
    Cci.RunTime.onLaunchApp2({
      list: "labs",
      app: "Panels",
    });
  };

  const handleOpenPOCT = () => {
    Cci.RunTime.onLaunchWindow(undefined, {
      cltconf: {
        closable: true,
        dbpath: Cci.util.Patient.getDbpath(),
        title: "POCT Entry",
        wParams: {
          args: {
            hobj: "labresults/poc/savedata",
            group: "poc",
            wparam: "group",
            appname: "OB_CLINIC",
            perm: "E",
            username: Cci.util.Staff.getLoginName(),
            dbpath: Cci.util.Patient.getDbpath(),
          },
        },
        isPatOverview: true,
        updateLabData: updateLabData,
      },
      srvconf: "amb_edit_poc.conf",
    });
  };

  const updateLabData = () => {
    // Set isChangePeriod to true to force update data of LabsGrid
    setIsChangePeriod(true);
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "View In Lab Results Viewer",
      icon: <IconExternalLink />,
      handler: handleRedirect,
    },
    {
      label: "POCT Entry",
      icon: <POCTIcon />,
      handler: handleOpenPOCT,
      hidden: userContext == USERCONTEXT.NURSE ? false : true,
    },
  ];

  return (
    <>
      <Grid
        container
        spacing={"8px"}
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        sx={{
          marginTop: "-8px",
          opacity: 1,
        }}
      >
        <Grid item xs sx={{ paddingTop: "6px" }}>
          <Typography sx={ModuleHeaderSx}>Labs</Typography>
        </Grid>
        <Grid
          item
          xs="auto"
          container
          direction="row"
          alignItems="center"
          justifyContent="flex-end"
        >
          <Grid item>
            <Select
              value={value}
              onChange={(e: any) => {
                setIsChangePeriod(true);
                setValue(e.target.value);
              }}
              displayEmpty
              inputProps={{ "aria-label": "Without label" }}
              size="small"
              sx={{ width: 200, height: 30, fontSize: "15px" }}
              renderValue={(value) => `Past Labs - ${value} Months`}
            >
              {periodOpts.map((item: any) => (
                <MenuItem
                  key={item.value}
                  value={item.value}
                  disabled={item.disabled}
                  sx={{
                    "&.Mui-selected": {
                      backgroundColor: "#FEC341!important",
                    },
                    "&:hover": {
                      backgroundColor: "#F6E596",
                    },
                  }}
                >
                  {item.disabled ? `${item.label} - No Results` : item.label}
                </MenuItem>
              ))}
            </Select>
          </Grid>
          {!props.readonly && (
            <Grid item>
              <IconButton
                aria-label="more"
                id="more-button"
                aria-controls={open ? "more-menu" : undefined}
                aria-expanded={open ? "true" : undefined}
                aria-haspopup="true"
                color="primary"
                onClick={handleMoreClick}
              >
                <MoreVertIcon />
              </IconButton>
              <Menu
                id="more-menu"
                aria-labelledby="more-button"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                sx={{
                  "& .MuiPaper-root": { borderRadius: "4px" },
                }}
              >
                {menuItems?.map(
                  (item: MenuItemProps) =>
                    !item.hidden && (
                      <MenuItem
                        onClick={() => {
                          handleClose();
                          item.handler && item.handler();
                        }}
                      >
                        <ListItemIcon>{item.icon}</ListItemIcon>
                        <Typography
                          sx={{
                            fontSize: "14px",
                            fontWeight: "400",
                            fontFamily: "Rotobo",
                            color: "#000",
                          }}
                        >
                          {item.label}
                        </Typography>
                      </MenuItem>
                    )
                )}
              </Menu>
            </Grid>
          )}
        </Grid>
      </Grid>
    </>
  );
};
