/**
 * LabsTabPanel.tsx
 *
 * @description Labs Tab Panel on the right for Patient Overview module
 * <AUTHOR>
 */

import Grid from "@mui/material/Grid";
import Box from "@mui/material/Box";

import { LabsToolbar } from "./LabsToolbar";
import { LabsGrid } from "./LabsGrid";
import { VitalsToolbar } from "./VitalsToolbar";
import { VitalsGrid } from "./VitalsGrid";

export const LabsTabPanel = () => {
  return (
    <Box sx={{ height: "100%", flexGrow: 1 }}>
      <Grid
        container
        direction="column"
        wrap="nowrap"
        alignItems="center"
        sx={{
          height: "100%",
          padding: "10px 10px 0 10px",
          backgroundColor: "#fff",
        }}
      >
        <Grid item xs>
          <LabsToolbar />
          <LabsGrid />
        </Grid>
        <Grid item sx={{ height: "269px", width: "100%" }}>
          <VitalsToolbar />
          <VitalsGrid />
        </Grid>
      </Grid>
    </Box>
  );
};
