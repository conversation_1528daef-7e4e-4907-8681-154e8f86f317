import { useEffect, useRef, useMemo } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Typography from "@mui/material/Typography";
import Paper from "@mui/material/Paper";
import { NoRowsOverlay } from "../common/CommonDataGrid";
import { Grid } from "@mui/material";
import { VitalsCell } from "./VitalsCell";

interface StickyColumnTableProps {
  tableData: {
    datasource: any[];
    columns: any[];
    timeHeader: any[];
    dataFlag?: any[];
  };
  onDoubleClick: (rowData?: any) => void;
  onBlur?: () => void;
  tableStyle?: any;
  type?: "vitals" | "";
  editable?: boolean;
}
export const VitalsColumnTable = (props: StickyColumnTableProps) => {
  const { tableData, tableStyle, onDoubleClick, onBlur } = props;
  const tableRef = useRef<HTMLDivElement>(null);
  const groupList = useMemo(() => {
    const headers = tableData.timeHeader || [];
    const groupMap = headers.reduce((cur, item) => {
      const str = item.substring(5);
      const count = headers.filter((h) => h.substring(5) === str).length;
      if (!cur[str]) {
        cur[str] = {
          value: str.substring(0, 6) + " '" + str?.substring(9),
          count,
        };
      }
      return cur;
    }, {});
    return Object.values(groupMap);
  }, [tableData.timeHeader]);

  useEffect(() => {
    if (tableRef.current) {
      if (tableRef.current.offsetWidth < tableRef.current.scrollWidth) {
        tableRef.current.scrollLeft = tableRef.current.scrollWidth;
      }
    }
  }, [tableData]);

  const stickyStyle: any = {
    position: "sticky",
    left: 0,
    zIndex: 100,
    background: "#e9e9e9",
    boxShadow: "4px 0 4px -4px rgba(0,0,0,0.15)",
    font: "bold 15px Helvetica",
    width: "90px",
    minWidth: "90px",
    maxWidth: "90px",
  };

  const commonStickyStyle: any = {
    position: "sticky",
    left: 0,
    zIndex: 100,
    background: "#e9e9e9",
    boxShadow: "4px 0 4px rgba(0,0,0,0.15)",
    font: "bold 15px Helvetica",
    width: "90px",
    minWidth: "90px",
    maxWidth: "90px",
  };

  const TooltipDetailCell = (item: any) => {
    let titleAndKeys: any = [
      ["Full Name", "fullname"],
      ["Display Name", "displayname"],
      ["Value", "data"],
      ["Unit of Measure", "unit"],
      ["Time", "strkey"],
      ["Stored at", "lgtimestr"],
      ["Stored By", "lgname"],
    ];
    return (
      <Grid container>
        {titleAndKeys.map((value: any, index: number) => (
          <Grid container key={index}>
            <Grid item xs={5}>
              <Typography
                sx={{
                  fontSize: "13px",
                  fontWeight: "700",
                  fontFamily: "Roboto",
                }}
              >
                {value[0] + ":"}
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <Typography
                sx={{
                  fontSize: "13px",
                  fontWeight: "400",
                  fontFamily: "Roboto",
                }}
              >
                {value[1] && item ? Ext.String.htmlDecode(item[value[1]]) : ""}
              </Typography>
            </Grid>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <TableContainer
      style={{
        borderTop: "1px solid #C2C2C2",
        ...tableStyle,
      }}
      ref={tableRef}
      component={Paper}
    >
      <Table
        stickyHeader
        size="small"
        sx={{
          borderRight: "1px solid #C2C2C2",
          "& .MuiTableCell-root": {
            padding: "2px 4px",
          },
        }}
      >
        <TableHead>
          {groupList.length && (
            <TableRow>
              <TableCell
                sx={{ ...stickyStyle, zIndex: 101, borderBottom: "none" }}
              >
                <br />
              </TableCell>
              {groupList.map((groupItem: any, i: number) => (
                <TableCell
                  key={`vitalsGroup${i}`}
                  sx={{
                    background: "#e9e9e9",
                    textAlign: "center",
                    padding: "1px 2px !important",
                    borderBottom: "none",
                    minWidth: "72px",
                  }}
                  align="center"
                  colSpan={groupItem.count}
                >
                  <Typography
                    sx={{ font: "400 12px Roboto", color: "#2A363C" }}
                  >
                    {groupItem.value}
                  </Typography>
                </TableCell>
              ))}
              {tableData?.timeHeader.length &&
                tableData?.timeHeader.length < 5 && (
                  <TableCell
                    sx={{
                      background: "#e9e9e9",
                      borderBottom: "none",
                    }}
                  />
                )}
            </TableRow>
          )}
          <TableRow>
            <TableCell sx={{ ...stickyStyle, zIndex: 101 }}>
              <br />
            </TableCell>
            {!!tableData?.timeHeader?.length &&
              tableData.timeHeader.map((item: any, index: number) => (
                <TableCell
                  key={index}
                  sx={{
                    background: "#e9e9e9",
                    textAlign: "center",
                    padding: "1px 2px !important",
                  }}
                  data-qtip={item?.slice(4)}
                >
                  <Typography
                    sx={{ font: "700 16px Helvetica", color: "#000" }}
                  >
                    {typeof item === "string" && item.length >= 4
                      ? `${item.substring(0, 2)}:${item.substring(2, 4)}`
                      : ""}
                  </Typography>
                </TableCell>
              ))}
            {tableData?.timeHeader.length &&
              tableData?.timeHeader.length < 5 && (
                <TableCell
                  sx={{
                    background: "#e9e9e9",
                  }}
                />
              )}
          </TableRow>
        </TableHead>
        <TableBody>
          {tableData.datasource?.map((row: any[], rowIndex: number) => {
            return (
              <TableRow
                key={rowIndex}
                sx={{
                  "&:hover .MuiTableCell-root": {
                    background: `${tableData.timeHeader?.length ? "#F6E596" : "#e9e9e9"} `,
                  },
                }}
              >
                <TableCell sx={commonStickyStyle}>
                  {tableData.columns[rowIndex]}
                </TableCell>
                {tableData.timeHeader?.length ? (
                  <>
                    {row.map((item: any, index: any) => {
                      return (
                        <VitalsCell
                          key={index}
                          index={index}
                          title={TooltipDetailCell(item)}
                          cellItem={item}
                          editable={props.editable ? true : false}
                          onDoubleClick={onDoubleClick}
                          onBlur={onBlur}
                        />
                      );
                    })}
                    {tableData?.timeHeader.length &&
                      tableData?.timeHeader.length < 5 && <TableCell />}
                  </>
                ) : (
                  rowIndex === Math.floor(tableData.columns.length / 2) && (
                    <TableCell
                      sx={{
                        border: "none",
                        padding: "0 !important",
                        "& .MuiStack-root": {
                          paddingTop: 0,
                          background: "#fff",
                        },
                      }}
                    >
                      <NoRowsOverlay msg="No vitals recorded" />
                    </TableCell>
                  )
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
