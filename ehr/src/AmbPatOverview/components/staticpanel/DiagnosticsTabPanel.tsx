/**
 * DiagnosticsTabPanel.tsx
 *
 * @description Diagnostics Tab Panel on the right for Patient Overview module
 * <AUTHOR>
 */

import { useAtom } from "jotai";
import { totalResultsAtom, pageAtom } from "../../context/LabsAtoms";
import { NoRowsOverlay } from "../common/CommonDataGrid";
import { useState } from "react";
import {
  Button,
  IconButton,
  Stack,
  styled,
  Grid,
  Fade,
  accordionSummaryClasses,
} from "@mui/material";
import Config from "@cci-monorepo/config/Config";
import React from "react";
import {
  ExternalLinkIcon,
  Microbioloy,
  Radiology,
} from "@cci-monorepo/Provider/icons";
import { StyledNameTooltip, diagnosticsPageSize } from "../common/Constants";
import { serverRequest } from "@cci-monorepo/AmbPatOverview/utils/DataRequests";
import Accordion from "@mui/material/Accordion";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { ModuleHeaderHeight } from "../common/Constants";
import { DiagnosticsToolbar } from "./DiagnosticsToolbar";
import { AccordionSummary, AccordionDetails } from "@mui/material";

export const DiagnosticsTabPanel = () => {
  const [formatedDiagnosticResults, setformatedDiagnosticResults] = useState<
    any[]
  >([]);
  const [gotoPatientPageConfig, setGotoPatientPageConfig] = useState<any>({});
  const [totalResults, setTotalResults] = useAtom(totalResultsAtom);
  const [expanded, setExpanded] = useState(false);
  const [page, setPage] = useAtom(pageAtom);

  const ClickButton = styled(Button)({
    border: "none",
    height: "36px",
    backgroundColor: "transparent",
    "&:hover": {
      backgroundColor: "transparent",
    },
    "&.MuiButton-text": {
      color: "#3C6CBB",
    },
  });

  const onGotoDiagnosticBtnClick = (type: string) => {
    setGotoPatientPageConfig({
      list: "labs",
      app: type,
    });
  };

  React.useEffect(() => {
    if (gotoPatientPageConfig && gotoPatientPageConfig.app) {
      Cci.Patient.loadPatientInfo(Cci.util.Patient.getDbpath());
      Config.gotoPatientPage(
        {
          dbpath: Cci.util.Patient.getDbpath(),
        },
        {
          screen: gotoPatientPageConfig.screen,
          list: gotoPatientPageConfig.list,
          app: gotoPatientPageConfig.app,
          appname: gotoPatientPageConfig.appname,
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gotoPatientPageConfig]);

  React.useEffect(() => {
    const params: any = {};
    params.dbpath = Cci.util.Patient.getDbpath();
    const onSuccess = (result: any) => {
      if (
        result &&
        result.data &&
        result.data.data &&
        result.data.data.length > 0
      ) {
        let resultJson = JSON.parse(result.data.data[0]);
        formatResult(resultJson.diagnosticResults);
        let allResults: any = [].concat(
          resultJson.diagnosticResults.Microbiology,
          resultJson.diagnosticResults.Pathology,
          resultJson.diagnosticResults.Radiology
        );
        setTotalResults(allResults.length);
      }
    };

    const onFailure = (error: string) => {
      console.error("Failed to get diagnostic results.", error);
    };

    serverRequest(
      "ambPatOverview/getdiagnosticresult",
      params,
      onSuccess,
      onFailure
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const MICRO_STATUS: any = {
    P: "POSITIVE",
    A: "ABNORMAL",
    N: "NEGATIVE",
  };

  const REPORT_STATUS: any = {
    P: "PRELIMINARY",
    F: "FINAL",
    S: "SUPPLEMENTAL",
    C: "CORRECTED",
  };

  const formatResult = (diagnosticResults: any) => {
    let formatedMicrobiologyResults: any = [];
    let formatedPathologyResults: any = [];
    let formatedRadiologyResults: any = [];
    let formatedDiagnosticResults: any = [];
    const microbiologyResults = diagnosticResults.Microbiology || [];
    microbiologyResults?.forEach((item: any) => {
      let status = "";
      if (item.cultures?.length > 0) {
        item.cultures.sort((a: any, b: any) => b.resultTime - a.resultTime);
        status = REPORT_STATUS[item.cultures[0].status];
      }
      formatedMicrobiologyResults.push({
        type: "Microbiology",
        name: item.name,
        orderId: item.order_id,
        flag: MICRO_STATUS[item.status],
        time: item.time,
        key: item.key,
        specimenSource: item.specimen_source,
        specimenType: item.specimen_type,
        provider: item.provider,
        spm_collect_time: item.spm_collect_time,
        starttime: item.starttime,
        accession: item.accession,
        status,
        lgname: item.lgname,
        lgtime: item.lgtime,
        result:
          item.results?.length > 0
            ? item.results?.map((item: any) => item.comment).join(";")
            : "",
      });
    });
    formatedDiagnosticResults = formatedDiagnosticResults.concat(
      formatedMicrobiologyResults
    );
    const pathologyResults = diagnosticResults.Pathology || [];
    pathologyResults?.forEach((item: any) => {
      let status = "";
      if (
        (!item.status &&
          item.results &&
          (item.results[0]?.Data?.toLowerCase().includes("corrected report") ||
            item.results[0]?.Data.toLowerCase().includes("report modified"))) ||
        (item.status && item.status === "C")
      ) {
        status = "Corrected";
      }
      if (
        (!item.status &&
          item.results &&
          item.results[0]?.Data?.toLowerCase().includes("addendum")) ||
        (item.status && item.status === "S")
      ) {
        status = status === "Corrected" ? "Corrected, Addended" : "Addended";
      } else if (item.status && item.status === "P") {
        status = "Preliminary";
      } else if (item.status && item.status === "F") {
        status = "Final";
      }
      formatedPathologyResults.push({
        type: "Pathology",
        name: item.ProName,
        orderId: item.order_id,
        status: status,
        time: item.time,
        key: item.key,
        Provider: item.Provider,
        Ctime: item.Ctime,
        specimenSource: item.SpecimenSource,
        caseNum: item.CaseNum,
        lgname: item.lgname,
        lgtime: item.lgtime,
        result:
          item.results?.length > 0
            ? item.results?.map((item: any) => item.Data).join(";")
            : "",
      });
    });
    formatedDiagnosticResults = formatedDiagnosticResults.concat(
      formatedPathologyResults
    );
    const radiologyResults = diagnosticResults.Radiology || [];
    radiologyResults?.forEach((item: any) => {
      formatedRadiologyResults.push({
        type: "Radiology",
        name: item.Report.Title,
        orderId: item.order_id,
        status: item.Report.Status,
        provider: item.Report.RequestedPhy,
        accessionNumber: item.Report.AccessionNumber,
        time: Cci.util.DateTime.serverSecsToTimeStr(
          item.TimeStamp,
          "HH:mm MM/DD/YYYY"
        ),
        key: item.key,
        result: item.Report.Findings,
      });
    });
    formatedDiagnosticResults = formatedDiagnosticResults.concat(
      formatedRadiologyResults
    );
    setformatedDiagnosticResults(formatedDiagnosticResults);
  };

  const displayData = React.useMemo(() => {
    return formatedDiagnosticResults?.length >= diagnosticsPageSize * page
      ? formatedDiagnosticResults?.slice(0, diagnosticsPageSize * page)
      : formatedDiagnosticResults;
  }, [formatedDiagnosticResults, page]);

  React.useEffect(() => {
    setExpanded(totalResults > 0);
  }, [setExpanded, totalResults]);

  const TooltipDetailCard = React.useCallback((data: any) => {
    let titleAndKeys: any[] = [];
    if (data.type === "Microbiology") {
      titleAndKeys = [
        ["Order", "name"],
        ["Order ID", "orderId"],
        ["Specimen Type", "specimenSource"],
        ["Specimen Source", "specimenType"],
        ["Status", "status"],
        ["Flag", "flag"],
        ["Ordering Doctor", "provider"],
        ["Collection Date/Time", "spm_collect_time"],
        ["Collected By", ""],
        ["Start Time", "starttime"],
        ["Accession Number", "accession"],
        ["Stored At", "lgtime"],
        ["Stored By", "lgname"],
      ];
    } else if (data.type === "Pathology") {
      titleAndKeys = [
        ["Order", "name"],
        ["Order ID", "orderId"],
        ["Specimen Type", "specimenSource"],
        ["Status", "status"],
        ["Ordering Doctor", "Provider"],
        ["Collection Date/Time", "Ctime"],
        ["Collected By", ""],
        ["Start Time", "time"],
        ["Case Number", "caseNum"],
        ["Stored At", "lgtime"],
        ["Stored By", "lgname"],
      ];
    } else if (data.type === "Radiology") {
      titleAndKeys = [
        ["Order", "name"],
        ["Order ID", "orderId"],
        ["Status", "status"],
        ["Ordering Doctor", "provider"],
        ["Start Time", "time"],
        ["Accession Number", "accessionNumber"],
      ];
    }
    return (
      <>
        <Grid container>
          {titleAndKeys.map((item, index) => (
            <Grid container key={index}>
              <Grid item xs={5}>
                <div
                  style={{
                    fontStyle: "bold",
                    fontSize: "13px",
                    fontWeight: "700",
                    textAlign: "left",
                  }}
                >
                  {item[0] + ":"}
                </div>
              </Grid>
              <Grid item xs={7}>
                <div style={{ fontSize: "13px", textAlign: "left" }}>
                  {item[1] && data ? data[item[1]] : ""}
                </div>
              </Grid>
            </Grid>
          ))}
        </Grid>
      </>
    );
  }, []);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={expanded}
      id="history"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(90deg)",
            },
        }}
        expandIcon={
          <ArrowRightIcon
            color="primary"
            onClick={() => setExpanded(!expanded)}
          />
        }
        aria-controls="history-content"
        id="history-header"
      >
        <DiagnosticsToolbar
          count={displayData.length}
          totalCount={totalResults}
        />
      </AccordionSummary>

      <AccordionDetails
        sx={{
          backgroundColor: "#F2F2F2",
          padding: "0 0 8px 0",
          margin: 0,
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        }}
      >
        <Stack
          sx={{
            borderRadius: "0px 8px 8px 8px",
            width: "100%",
            height: "100%",
            overflow: "auto",
          }}
        >
          {totalResults > 0 && displayData.length > 0 ? (
            <Stack
              spacing={1}
              direction="column"
              alignItems="center"
              sx={{ marginTop: "16px", marginBottom: "16px" }}
            >
              {displayData.map((report: any, idx: number) => {
                return (
                  <StyledNameTooltip
                    key={idx}
                    enterDelay={800}
                    TransitionComponent={Fade}
                    TransitionProps={{ timeout: 200 }}
                    title={TooltipDetailCard(report)}
                    sx={{ maxWidth: 360 }}
                  >
                    <Stack
                      direction="row"
                      key={
                        report.orderId ||
                        report.type + report.name + report.time + idx
                      }
                      sx={{
                        padding: "8px",
                        background: "white",
                        borderRadius: "8px",
                        boxShadow: "0px 0px 4px 0px rgba(0, 0, 0, 0.251)",
                        height: "94px",
                        width: "420px",
                      }}
                    >
                      <label
                        style={{
                          width: "8px",
                          minWidth: "8px",
                          maxWidth: "8px",
                          borderRadius: "4px",
                          minHeight: "56px",
                          backgroundColor: "#E0E3E6",
                        }}
                      />
                      <Stack
                        sx={{
                          paddingLeft: "7px",
                          flex: 1,
                        }}
                      >
                        <Stack
                          direction="row"
                          sx={{
                            height: "24px",
                            alignItems: "center",
                          }}
                        >
                          {report.type === "Radiology" ? (
                            <Radiology />
                          ) : (
                            <Microbioloy />
                          )}
                          <span
                            style={{
                              lineHeight: "19.36px",
                              fontFamily: "Inter",
                              fontSize: "16px",
                              fontWeight: 600,
                              marginLeft: "8px",
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              maxWidth: "270px",
                              color: "rgba(0, 0, 0, 1)",
                            }}
                          >
                            {report.name}
                          </span>
                          <IconButton
                            sx={{
                              marginLeft: "auto",
                            }}
                            onClick={() => {
                              onGotoDiagnosticBtnClick(report.type);
                            }}
                          >
                            <ExternalLinkIcon />
                          </IconButton>
                        </Stack>
                        <span
                          style={{
                            paddingTop: "10px",
                            paddingBottom: "10px",
                            height: "44px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "340px",
                            fontSize: "16px",
                            lineHeight: "24px",
                            color: "rgba(0, 0, 0, 0.7)",
                          }}
                        >
                          <span
                            style={{
                              fontWeight: 600,
                              paddingRight: "8px",
                            }}
                          >
                            Results -
                          </span>
                          <span
                            style={{
                              fontWeight: 400,
                            }}
                          >
                            {report.result}
                          </span>
                        </span>
                        <div
                          style={{
                            textAlign: "right",
                            height: "18px",
                            color: "rgba(0, 0, 0, 0.6)",
                            fontSize: "16px",
                          }}
                        >
                          {report.time}
                        </div>
                      </Stack>
                    </Stack>
                  </StyledNameTooltip>
                );
              })}
              {totalResults > diagnosticsPageSize * page && (
                <ClickButton
                  onClick={() => setPage((preVal) => preVal + 1)}
                  variant="text"
                >
                  Load More
                </ClickButton>
              )}
            </Stack>
          ) : (
            totalResults === 0 && (
              <NoRowsOverlay height="100px" msg="No diagnostic results" />
            )
          )}
        </Stack>
      </AccordionDetails>
    </Accordion>
  );
};
