/**
 * ReasonEditorDialog.tsx
 *
 * @author: RnD
 * @description Renders ReasonEditorDialog
 */

import { useEffect, useState } from "react";
import { useAtomValue } from "jotai";
import { Box, Grid, Typography, TextField } from "@mui/material";
import { savedReasonAtom } from "../../context/ReasonAtoms";
import { Button, CciDialog } from "@cci/mui-components";

interface ReasonEditorDialogProps {
  dispText: string;
  open: boolean;
  setOpen: (status: boolean) => void;
  onSave: (reason: string) => void;
}

export const ReasonEditorDialog = (props: ReasonEditorDialogProps) => {
  const { dispText, open, setOpen, onSave } = props;
  const [reason, setReason] = useState("");
  const savedReason = useAtomValue(savedReasonAtom);

  useEffect(() => {
    setReason(savedReason);
  }, [savedReason]);

  // <PERSON>le closing the dialog
  const handleClose = () => {
    setReason(savedReason);
    setOpen(false);
  };

  // Handle saving data
  const handleSave = () => {
    setOpen(false);
    onSave(reason);
  };

  const hasData = () => {
    return (reason ?? "").length > 0;
  };

  const content = (
    <Box sx={{ position: "relative", padding: "20px" }}>
      <Typography
        sx={{
          marginTop: "15px",
          fontFamily: "Roboto",
          fontSize: "14px",
          fontWeight: 500,
          color: "#000000",
        }}
      >
        {dispText}
      </Typography>
      <TextField
        autoFocus
        size="small"
        variant="outlined"
        multiline
        rows={2}
        value={reason}
        onChange={(event) => {
          setReason(event.target.value);
        }}
        error={!hasData()}
        sx={{
          // Root class for the input field
          "& .MuiOutlinedInput-root": {
            color: "#000000",
            fontSize: "14px",
            fontFamily: "Roboto",
            fontWeight: 400,
            width: "386px",
            height: "48px",
            backgroundColor: "#FFFFFF",
            padding: "4px 6px 4px 6px",
          },
        }}
      />
    </Box>
  );

  const cancelButton = (
    <Button
      data-testid="visit-reason-cancel-button"
      color="secondary"
      variant="contained"
      onClick={handleClose}
      sx={{ marginRight: "10px" }}
    >
      Cancel
    </Button>
  );

  const saveButton = (
    <Button
      data-testid="visit-reason-save-button"
      color="primary"
      variant="contained"
      onClick={handleSave}
      disabled={!hasData()}
    >
      Save
    </Button>
  );

  const buttons = (
    <Grid
      container
      style={{ margin: "16px 16px 0px 16px" }}
      display="flex"
      spacing={2}
      justifyContent="flex-end"
    >
      {cancelButton}
      {saveButton}
    </Grid>
  );
  return (
    <CciDialog
      open={open}
      setOpen={setOpen}
      handleClose={handleClose}
      title={((savedReason ?? "").length === 0 ? "Add " : "Edit ") + dispText}
      content={content}
      buttons={buttons}
      sx={{
        "& .MuiDialogTitle-root": {
          padding: "16px 24px",
        },
      }}
    />
  );
};
