/**
 * ReasonForVisit.tsx
 *
 * @author: jfsys
 * @description Reason for Visit component
 */

import { useEffect } from "react";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { MultilineInputField } from "@cci/mui-components";
import { useSetErrorDialog, useSetToastFamily } from "@cci-monorepo/common";
import Typography from "@mui/material/Typography";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionActions from "@mui/material/AccordionActions";
import { ReasonToolbar } from "./ReasonToolbar";
import { ModuleHeaderHeight, USERCONTEXT } from "../common/Constants";
import {
  savedReasonAtom,
  setParseDataReasonAtom,
  editedMessageReasonAtom,
} from "../../context/ReasonAtoms";
import { serverRequest } from "@cci-monorepo/Pld/util/DataRequests";
import {
  formDataAtom,
  saveUndoAtom,
  undoRedoFlagAtom,
} from "@cci-monorepo/AmbPatOverview/context/UndoRedoAtoms";
import { ChiefComplaintIcon } from "../common/icons";
import { userContextAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";

export const ReasonForVisit = (props: any) => {
  const [savedReason, setSavedReason] = useAtom(savedReasonAtom);
  const parseReasonData = useSetAtom(setParseDataReasonAtom);
  const editedMessageReason = useAtomValue(editedMessageReasonAtom);
  const [formData, setFormData] = useAtom(formDataAtom);
  const [undoRedoFlag, setUndoRedoFlag] = useAtom(undoRedoFlagAtom);
  const saveUndo = useSetAtom(saveUndoAtom);
  const setShowToast = useSetToastFamily("AmbPatOverview");
  const setOpenErrorDialog = useSetErrorDialog();
  const userContext = useAtomValue(userContextAtom);
  const dispText = "Chief Complaint";
  let timer: any;

  // Handle saving data
  const handleSave = (newReason: string, isReview?: boolean) => {
    const onSuccess = () => {
      const successCallback = (parseData: any) => {
        if (!isReview) {
          saveUndo({
            ...formData,
            reason: parseData.reason,
          });
        }
        setShowToast({
          type: "success",
          text: `${dispText} ${isReview ? "reviewed" : "updated"}!`,
          open: true,
        });
      };

      loadData(successCallback);
    };

    const onFailure = (error: string) => {
      console.error(`Failed to save ${dispText}.`, error);
      setOpenErrorDialog({
        text: `Failed to save: ${error}`,
        open: true,
      });
    };

    const data = [
      {
        jit: "2713",
        nit: 0,
        ks: "admitkey",
        data: {
          source:
            userContext === USERCONTEXT.NURSE
              ? "Nurse Intake"
              : "Patient Overview",
          data: newReason,
        },
      },
    ];
    let params = { appname: "", dbpath: "", fsdata: "", perm: "", type: "" };
    params.appname = "UNLOCK";
    params.dbpath = Cci.Patient.getDbpath();
    params.fsdata = Ext.encode(data);
    params.perm = "E";
    params.type = "DEMO";
    serverRequest("DBI/savedata", params, onSuccess, onFailure);
  };

  const loadData = (successCallback?: any) => {
    let params = {
      dbpath: Cci.util.Patient.getDbpath(),
    };
    const onSuccess = (result: any) => {
      if (result && result.history) {
        const parseData = parseReasonData(result.history);
        successCallback(parseData);
      }
    };

    const onFailure = (error: string) => {
      console.error(`Failed to get ${dispText}.`, error);
    };

    serverRequest("ambPatOverview/visitreason", params, onSuccess, onFailure);
  };

  useEffect(() => {
    loadData((parseData: any) => {
      setFormData((prev: any) => ({ ...prev, reason: parseData.reason }));
    });
  }, []);

  const reviewCommit = () => {
    handleSave(savedReason, true);
  };

  useEffect(() => {
    if (formData.reason && undoRedoFlag && formData.reason !== savedReason) {
      handleSave(formData.reason);
      setTimeout(() => {
        setUndoRedoFlag(false);
      }, 0);
    }
  }, [formData.reason, undoRedoFlag]);

  return (
    <Accordion
      square={true}
      sx={{
        borderRadius: "8px",
        boxShadow:
          "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
      }}
      disableGutters
      expanded={true}
      id="reason"
    >
      <AccordionSummary
        sx={{
          height: ModuleHeaderHeight,
          flexDirection: "row-reverse",
          backgroundColor: "#F2F2F2",
          borderTopLeftRadius: "8px",
          borderTopRightRadius: "8px",
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            {
              transform: "rotate(0deg)",
              marginRight: "8px",
            },
        }}
        aria-controls="reason-content"
        id="reason-header"
        expandIcon={<ChiefComplaintIcon />}
      >
        <ReasonToolbar
          dispText={dispText}
          readonly={props.readonly}
          onSave={handleSave}
          reviewCommit={reviewCommit}
        />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          padding: "0 8px",
          background: "#f2f2f2",
        }}
      >
        <MultilineInputField
          rows={undefined}
          maxRows={3}
          data={savedReason}
          setData={(data) => {
            setSavedReason(data);
          }}
          onBlur={() => {
            if (savedReason) {
              if (savedReason !== formData.reason) handleSave(savedReason);
            } else {
              setSavedReason(formData.reason);
            }
          }}
          sx={{
            "& .MuiInputBase-input": {
              font: "15px 400 Roboto",
              overflowY: "scroll",
            },
            "& .MuiOutlinedInput-root.Mui-error fieldset": {
              borderColor: "#d32f2f",
            },
          }}
        />
      </AccordionDetails>
      {editedMessageReason && (
        <AccordionActions
          sx={{
            borderRadius: "0px 0px 8px 8px",
            backgroundColor: "#f2f2f2",
            justifyContent: "flex-start",
          }}
        >
          <Typography
            sx={{
              font: "normal 400 12px Roboto",
              color: "rgba(0, 0, 0, 0.58)",
            }}
          >
            {editedMessageReason.last_edited}
          </Typography>
        </AccordionActions>
      )}
    </Accordion>
  );
};
