/**
 * ReasonToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for ReasonForVisit
 */

import { useAtomValue } from "jotai";
import { useState } from "react";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink, EditHistoryIcon } from "../../../common/assets";
import {
  savedReasonAtom,
  hasReviewedReasonAtom,
  reviewMessageReasonAtom,
} from "../../context/ReasonAtoms";
import { regIdAtom } from "../../context/CommonAtoms";
import { toRegistion } from "../../utils/utils";
import { EditHistoryDialog } from "../common/EditHistoryDialog";
import ReasonEditHistoryPanel from "./ReasonEditHistoryPanel";

export const ReasonToolbar = ({ dispText, readonly, reviewCommit }: any) => {
  const savedReason = useAtomValue(savedReasonAtom);
  const regid = useAtomValue(regIdAtom);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);
  const hasReviewed = useAtomValue(hasReviewedReasonAtom);
  const reviewMessageReason = useAtomValue(reviewMessageReasonAtom);

  const handleRedirect = () => {
    toRegistion(regid);
  };

  const handleShowHistory = () => {
    setShowHistoryDialog(true);
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "View In Registration",
      icon: <IconExternalLink />,
      handler: handleRedirect,
      hidden: readonly ? true : false,
    },
    {
      label: "Edit History",
      icon: <EditHistoryIcon />,
      handler: handleShowHistory,
      disabled: readonly,
      hidden: `${savedReason ?? ""}`.length === 0 ? true : false,
    },
  ];

  const tabs = [
    {
      label: "Edit History",
      value: "1",
      tabDetail: <ReasonEditHistoryPanel />,
    },
  ];
  return (
    <>
      <CommonToolbar
        name={dispText}
        isPldWidget={false}
        menuItems={menuItems}
        hasReviewed={hasReviewed}
        reviewMsg={hasReviewed ? reviewMessageReason?.last_reviewed : ""}
        reviewCommit={reviewCommit}
      />
      <EditHistoryDialog
        tabs={tabs}
        value={"1"}
        open={showHistoryDialog}
        setOpen={setShowHistoryDialog}
      />
    </>
  );
};
