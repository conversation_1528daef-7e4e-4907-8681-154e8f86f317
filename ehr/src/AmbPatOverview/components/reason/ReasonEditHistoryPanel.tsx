import {
  Box,
  Divider,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { reasonDispFields, reasonDataKeys } from "./ReasonUtils";
import { useAtomValue } from "jotai";
import { historyReasonAtom } from "../../context/ReasonAtoms";

const StyledTableCell = styled(TableCell)({
  width: "265px",
  font: "normal 400 14px Roboto",
  color: "#000",
  borderRight: "solid 1px rgb(225 230 238)",
});

export default function ReasonEditHistoryPanel() {
  const reasonData = useAtomValue(historyReasonAtom);
  var hasData = reasonData.length > 0 ? true : false;

  return (
    <Box component="div" sx={{ backgroundColor: "white" }}>
      <Typography
        sx={{ color: "rgba(0, 0, 0, 0.7)", font: "normal 700 28px Roboto" }}
      >
        Chief <PERSON><PERSON><PERSON>t
      </Typography>
      <Divider sx={{ marginTop: "15px" }} />
      <Box sx={{ marginTop: "10px", overflow: "auto" }}>
        <TableContainer
          component={Paper}
          sx={{ minWidth: 500, overflow: "auto", height: "552px" }}
        >
          <Table
            size="small"
            aria-label="a dense table"
            sx={{ tableLayout: "fixed" }}
          >
            {hasData ? (
              <>
                <TableHead>
                  <TableRow sx={{ height: "25px" }}>
                    <TableCell
                      sx={{
                        width: "249px",
                        backgroundColor: "#2E4B75",
                        borderRight: "solid 2px black",
                      }}
                    ></TableCell>
                    {reasonData.map((history: any) => (
                      <TableCell
                        component="th"
                        scope="row"
                        sx={{
                          width: "265px",
                          backgroundColor: "#2E4B75",
                          borderRight: "solid 1px #2E4B75",
                        }}
                      ></TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell
                      component="th"
                      scope="row"
                      sx={{
                        font: "normal 700 14px Roboto",
                        borderRight: "solid 2px black",
                        backgroundColor: "#EDEDED",
                      }}
                    >
                      Action
                    </TableCell>
                    {reasonData.map((item: any, index: number) => {
                      return (
                        <StyledTableCell
                          component="th"
                          scope="row"
                          sx={{
                            backgroundColor: index === 0 ? "white" : "#C7E5D8",
                            font: "normal 700 14px Roboto",
                            borderRight: "solid 1px rgb(225 230 238)",
                          }}
                        >
                          {index === 0 ? "Added" : "Modified"}
                        </StyledTableCell>
                      );
                    })}
                  </TableRow>
                  {reasonDispFields.map((field: string, fieldIndex: any) =>
                    field === "Divider_line" ? (
                      <TableRow sx={{ height: "25px" }}>
                        <TableCell
                          sx={{
                            width: "249px",
                            backgroundColor: "#2E4B75",
                            borderRight: "solid 2px black",
                          }}
                        ></TableCell>
                        {reasonData.map((history: any) => (
                          <TableCell
                            component="th"
                            scope="row"
                            sx={{
                              width: "265px",
                              backgroundColor: "#2E4B75",
                              borderRight: "solid 1px #2E4B75",
                            }}
                          ></TableCell>
                        ))}
                      </TableRow>
                    ) : (
                      <TableRow>
                        <TableCell
                          sx={{
                            font: "normal 700 14px Roboto",
                            borderRight: "solid 2px black",
                            backgroundColor: "#EDEDED",
                          }}
                        >
                          {field}
                        </TableCell>
                        {reasonData.map((data: any, index: number) => {
                          let backgroundColor: string = "white";
                          if (!data || data === undefined)
                            return (
                              <TableCell
                                sx={{
                                  borderRight: "solid 1px rgb(225 230 238)",
                                }}
                              ></TableCell>
                            );
                          if (
                            fieldIndex === reasonDispFields.length - 1 &&
                            index !== 0 &&
                            reasonData[index] &&
                            reasonData[index - 1] &&
                            reasonData[index][reasonDataKeys[field]] !==
                              reasonData[index - 1][reasonDataKeys[field]]
                          ) {
                            backgroundColor = "#C7E5D8";
                          }
                          return (
                            <TableCell
                              sx={{
                                backgroundColor: backgroundColor,
                                borderRight: "solid 1px rgb(225 230 238)",
                                wordWrap: "break-word",
                              }}
                            >
                              {reasonData[index][reasonDataKeys[field]]}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    )
                  )}
                </TableBody>
              </>
            ) : null}
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
}
