export const reasonDispFields: string[] = [
  "Source",
  "Action User",
  "Action Date/Time",
  "Divider_line",
  "Chief Complaint",
];

export const reasonDataKeys: any = {
  Source: "source",
  "Action User": "lgname",
  "Action Date/Time": "lgtime",
  "Chief Complaint": "reason",
};

export const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

export const parseReasonData = (data: any) => {
  return convertArrayToObject(data);
};
