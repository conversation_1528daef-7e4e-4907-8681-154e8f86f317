/**
 * OrdersToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for outstanding orders
 */

import { useAtomValue } from "jotai";

import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink } from "../../../common/assets";
import { rowDataOrdersAtom } from "../../context/OrdersAtoms";

export const OrdersToolbar = (props: any) => {
  const rowData = useAtomValue(rowDataOrdersAtom);
  const count = rowData?.length;

  const handleRedirect = () => {
    // Todo
  };

  const menuItems: MenuItemProps[] = props.readonly
    ? []
    : [
        {
          label: "View In Order Entry",
          icon: <IconExternalLink />,
          handler: handleRedirect,
        },
      ];

  return (
    <CommonToolbar
      name="Outstanding Orders"
      isPldWidget={false}
      count={count}
      menuItems={menuItems}
    />
  );
};
