/**
 * OutstandingOrders.tsx
 *
 * @author: jfsys
 * @description Outstanding Orders component
 */

import { useEffect, useState } from "react";
import { useAtom, useSetAtom } from "jotai";
import Box from "@mui/material/Box";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { v4 as uuid } from "uuid";
import { OrdersToolbar } from "./OrdersToolbar";
import { ModuleHeaderHeight } from "../common/Constants";
import { OrdersSection } from "./OrdersSection";
import {
  rowDataOrdersAtom,
  rowDataSelectedCategoryAtom,
} from "../../context/OrdersAtoms";
import { Tab, Tabs } from "@mui/material";

export const OutstandingOrders = (props: any) => {
  const [rowData, setRowData] = useAtom(rowDataOrdersAtom);
  const setSelectedCategory = useSetAtom(rowDataSelectedCategoryAtom);
  const [categoryTabList, setCategoryTabList] = useState<any[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [hasData, setHasData] = useState<boolean>(false);
  const [expanded, setExpanded] = useState<boolean>(true);

  const groupByCategoryAndCount = (arr: any[]) => {
    return arr.reduce((acc, obj) => {
      const category = obj.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});
  };

  const handleCategoryTabChange = (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    setTabValue(newValue);
    categoryTabList.forEach((catItem: any, index: number) => {
      if (index === newValue) {
        setSelectedCategory(catItem.label);
      }
    });
  };

  const getOOrders = async () => {
    const params = {
      dbpath: Cci.Patient.getDbpath(),
    };
    const jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
      hobj: "cpoe2/getoutstandingorders",
      noBatch: true,
      params,
    });
    if (jsonData && jsonData.osorders) {
      const osdataarr: any[] = [];
      const osdata = jsonData.osorders.data;
      const osdataheader = jsonData.osorders.header;
      osdata.forEach((ele: any) => {
        let new_ele: any = {};
        new_ele["id"] = uuid();
        new_ele["status"] = "";
        osdataheader.forEach((header: any, idx: any) => {
          new_ele[header] = ele[idx];
        });
        try {
          if (new_ele.data) {
            const initdata = JSON.parse(new_ele.data);
            for (const initdataKey of Object.keys(initdata)) {
              if (!Object.keys(new_ele).includes(initdataKey)) {
                new_ele[initdataKey] = initdata[initdataKey];
              }
            }
          }
        } catch (e) {}
        osdataarr.push(new_ele);
      });
      setRowData(osdataarr);
      const grpByCats = groupByCategoryAndCount(osdataarr);
      let tabArr: any[] = [];
      Object.keys(grpByCats).forEach((key: any, idx: any) => {
        tabArr.push({ label: key, count: grpByCats[key] });
      });
      tabArr = [{ label: "All", count: osdataarr.length }, ...tabArr];
      setCategoryTabList(tabArr);
    }
  };

  useEffect(() => {
    if (!hasData) {
      getOOrders();
      setHasData(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData]);

  useEffect(() => {
    setExpanded(rowData.length > 0 ? true : false);
  }, [rowData, setExpanded]);

  if (hasData) {
    return (
      <Accordion
        square={true}
        sx={{
          borderRadius: "8px",
          boxShadow:
            "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
        }}
        disableGutters
        expanded={expanded}
        id="orders"
      >
        <AccordionSummary
          sx={{
            height: ModuleHeaderHeight,
            flexDirection: "row-reverse",
            backgroundColor: "#F2F2F2",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
            [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
              {
                transform: "rotate(90deg)",
              },
          }}
          expandIcon={
            <ArrowRightIcon
              color="primary"
              onClick={() => setExpanded(!expanded)}
            />
          }
          aria-controls="orders-content"
          id="orders-header"
        >
          <OrdersToolbar readonly={props.readonly} />
        </AccordionSummary>
        <AccordionDetails
          sx={{
            padding: 0,
            margin: 0,
            borderBottomLeftRadius: "8px",
            borderBottomRightRadius: "8px",
          }}
        >
          {categoryTabList.length ? (
            <>
              <Box
                sx={{
                  "& .MuiTabs-root": { minHeight: 40 },
                }}
              >
                <Tabs
                  value={tabValue}
                  onChange={handleCategoryTabChange}
                  scrollButtons="auto"
                  sx={{
                    "& .MuiTab-root.Mui-selected": {
                      color: "#000",
                    },
                    "& .MuiTabs-indicator": { backgroundColor: "#F9D169" },
                    "& .MuiTab-root": {
                      minHeight: 40,
                      textTransform: "none",
                      font: "normal 500 15px Roboto",
                    },
                  }}
                >
                  {categoryTabList.map((item: any, index: number) => (
                    <Tab
                      label={item.label + "(" + item.count + ")"}
                      value={index}
                      key={index}
                    />
                  ))}
                </Tabs>
              </Box>
            </>
          ) : (
            <></>
          )}
          <Box>{expanded ? <OrdersSection /> : <></>}</Box>
        </AccordionDetails>
      </Accordion>
    );
  } else {
    return <></>;
  }
};
