/**
 * OrdersSection.tsx
 *
 * @author: jfsys
 * @description Data grid accordion that contains orders data
 */

import { useAtomValue } from "jotai";
import {
  rowDataOrdersAtom,
  columnDataOrdersAtom,
  rowDataSelectedCategoryAtom,
} from "../../context/OrdersAtoms";
import { TooltipCommonDataGrid } from "../common/TooltipCommonDataGrid";

export const OrdersSection = () => {
  const rowData = useAtomValue(rowDataOrdersAtom);
  const columnData = useAtomValue(columnDataOrdersAtom);
  const selectedCategory = useAtomValue(rowDataSelectedCategoryAtom);
  const filteredRowData =
    selectedCategory === "All"
      ? rowData
      : rowData.filter((item) => item.category === selectedCategory);

  return (
    <TooltipCommonDataGrid
      type="outstandingOrders"
      rows={filteredRowData}
      columns={columnData}
      noDataMsg="No outstanding orders"
    />
  );
};
