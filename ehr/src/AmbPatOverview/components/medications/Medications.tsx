/**
 * Medications.tsx
 *
 * @author: jfsys
 * @description Medications component
 */

import { useEffect, useState, useMemo } from "react";
import { useAtomValue, useSetAtom, useAtom } from "jotai";
import Box from "@mui/material/Box";
import Accordion from "@mui/material/Accordion";
import AccordionSummary, {
  accordionSummaryClasses,
} from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { AdditionalFooter } from "../common/AdditionalFooter";
import {
  medsRowDataAtom,
  setParseDataAtom,
  pageAtom,
  isShowInactiveMedAtom,
} from "@cci-monorepo/AmbPatOverview/context/MedAtoms";
import { MedToolbar } from "./MedToolbar";
import { ModuleHeaderHeight, medPageSize } from "../common/Constants";
import { MedSection } from "./MedSection";
import { getHomeMedList } from "@cci-monorepo/Pld/util/MedsDataAPI";
import { useSetErrorDialog } from "@cci-monorepo/common";

export const Medications = (props: any) => {
  const rowData = useAtomValue(medsRowDataAtom);
  const [hasData, setHasData] = useState<boolean>(false);
  const [expanded, setExpanded] = useState<boolean>(true);
  const parseDataAtom = useSetAtom(setParseDataAtom);
  const setOpenErrorDialog = useSetErrorDialog();
  const [page, setPage] = useAtom(pageAtom);
  const isShowInactive = useAtomValue(isShowInactiveMedAtom);

  const totalMeds = useMemo(() => {
    if (isShowInactive) {
      return rowData.length;
    } else {
      return rowData.filter((data: { [key: string]: string | number }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowData]);

  useEffect(() => {
    if (!hasData) {
      const params = {
        start: 0,
        pagesize: 100,
        showinactive: 0,
        dbpath: Cci.util.Patient.getDbpath(),
      };
      getHomeMedList(params)
        .then((data) => {
          if (data) {
            setHasData(true);
            parseDataAtom(data);
          }
        })
        .catch(() => {
          setOpenErrorDialog({
            text: "System has failed to load medication data",
            open: true,
          });
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData]);

  useEffect(() => {
    setExpanded(rowData.length > 0 ? true : false);
  }, [rowData, setExpanded]);

  if (hasData) {
    return (
      <Accordion
        square={true}
        sx={{
          borderRadius: "8px",
          boxShadow:
            "0px 0px 2px 0px rgb(0,0,0,0.10), 0px 2px 10px 1px rgb(0,0,0,0.15)",
        }}
        disableGutters
        expanded={expanded}
        id="medications"
      >
        <AccordionSummary
          sx={{
            height: ModuleHeaderHeight,
            flexDirection: "row-reverse",
            backgroundColor: "#F2F2F2",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
            [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
              {
                transform: "rotate(90deg)",
              },
          }}
          expandIcon={
            <ArrowRightIcon
              color="primary"
              onClick={() => setExpanded(!expanded)}
            />
          }
          aria-controls="medications-content"
          id="medications-header"
        >
          <MedToolbar readonly={props.readonly} />
        </AccordionSummary>
        <AccordionDetails
          sx={{
            padding: 0,
            margin: 0,
            borderBottomLeftRadius: "8px",
            borderBottomRightRadius: "8px",
          }}
        >
          <Box>{expanded ? <MedSection /> : <></>}</Box>
        </AccordionDetails>
        {totalMeds > medPageSize * page && (
          <AdditionalFooter
            msg="Load More"
            handleClick={() => setPage((preVal) => preVal + 1)}
          />
        )}
      </Accordion>
    );
  } else {
    return <></>;
  }
};
