/**
 * MedSection.tsx
 *
 * @author: jfsys
 * @description Data grid accordion that contains medications data
 */

import clsx from "clsx";
import { useState, useEffect, useMemo } from "react";
import { useAtomValue } from "jotai";
import { Box, Tooltip, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import {
  isShowInactiveMedAtom,
  medsRowDataAtom,
  pageAtom,
} from "../../context/MedAtoms";
import { TooltipCommonDataGrid } from "../common/TooltipCommonDataGrid";
import ERxIcon from "@cci-monorepo/Pld/hook/useMeds/asset/Icn_ERx.svg";
import FreeTextIcon from "@cci-monorepo/Pld/hook/useMeds/asset/Icn_FreeText.svg";
import SureScriptIcon from "@cci-monorepo/Pld/hook/useMeds/asset/Script_Icon.svg";
import ComplianceIcon from "@cci-monorepo/Pld/hook/useMeds/asset/Not_Taking_As_Prescribe.svg";
import { toPldSelectRow } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid-pro";
import CommonRenderCell from "../common/CommonRenderCell";
import { dateComparator } from "@cci-monorepo/common";
import { medPageSize } from "../common/Constants";

const Wrapper = styled("span")({
  display: "flex",
  alignItems: "center",
  gap: 4,
});

const calculateWidth = (width: number) => {
  return width > 530 ? width - 410 : 120;
};

export const MedSection = () => {
  const rowData: any[] = useAtomValue(medsRowDataAtom);
  const isShowInactive = useAtomValue(isShowInactiveMedAtom);
  const page = useAtomValue(pageAtom);
  const [width, setWidth] = useState(0);
  const [columns, setColumns] = useState<GridColDef[]>([]);
  useEffect(() => {
    setColumns([
      {
        field: "flag",
        headerName: "",
        width: 40,
        valueGetter: (v) => {
          if (parseInt(v.row.isftxt) === 1) {
            return v.row.source ?? "Free-text";
          } else if (v.row.source === "Surescripts") {
            return "surescripts";
          } else if (v.row.source === "E-Prescribe") {
            return "eprescribe";
          }
        },
        renderCell: (params: GridRenderCellParams<any, string>) => (
          <Wrapper>
            {params.value === "surescripts" && (
              <Tooltip title="Surescripts">
                <img src={SureScriptIcon} alt="surescript" />
              </Tooltip>
            )}
            {params.value === "eprescribe" && (
              <Tooltip title="E-Prescribe">
                <img src={ERxIcon} alt="eRx" />
              </Tooltip>
            )}
            {params.value &&
              params.value !== "surescripts" &&
              params.value !== "eprescribe" &&
              params.value.length > 0 && (
                <Tooltip title={params.value}>
                  <img src={FreeTextIcon} alt="freetxt" />
                </Tooltip>
              )}
          </Wrapper>
        ),
      },
      {
        field: "status",
        headerName: "Status",
        width: 80,
        valueOptions: ["Active", "Inactive", "In Error"],
        cellClassName: () => clsx("apo-med-cell"),
        renderCell: CommonRenderCell,
      },
      {
        field: "name",
        headerName: "Drug Name",
        flex: 1,
        minWidth: 150,
        renderCell: (params: GridRenderCellParams<any, string>) => (
          <Wrapper>
            <Typography
              sx={{
                width: calculateWidth(width),
                font: "normal 700 16px Roboto",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                fontStyle:
                  params.row.status === "Inactive" ? "italic" : "normal",
                textDecoration:
                  params.row.status === "In Error" ? "line-through" : "none",
              }}
            >
              {params.row.compliance === "Not Taking as Prescribed" ? (
                <Tooltip title={params.row.compliance}>
                  <img src={ComplianceIcon} alt="compliance" />
                </Tooltip>
              ) : null}{" "}
              {params.value}
            </Typography>
          </Wrapper>
        ),
      },
      {
        field: "indication",
        headerName: "Indication",
        width: 160,
        cellClassName: () => clsx("apo-med-cell"),
        renderCell: CommonRenderCell,
      },
      {
        field: "starttime",
        headerName: "Start Date",
        width: 100,
        sortComparator: dateComparator,
        cellClassName: () => clsx("apo-med-cell"),
        renderCell: CommonRenderCell,
      },
    ]);
  }, [width]);

  const displayRows = useMemo(() => {
    if (isShowInactive) {
      return rowData;
    } else {
      return rowData.filter((row: { [key: string]: string | number }) => {
        return String(row.status).toUpperCase() === "ACTIVE";
      });
    }
  }, [isShowInactive, rowData]);

  const displayData = useMemo(() => {
    return displayRows?.length >= medPageSize * page
      ? displayRows?.slice(0, medPageSize * page)
      : displayRows;
  }, [displayRows, page]);

  return (
    <Box
      sx={{
        "& .apo-med-cell": {
          fontFamily: "Roboto !important",
          fontWeight: "400 !important",
          fontSize: "15px !important",
        },
      }}
    >
      <div
        ref={(el) => {
          if (!el) return;
          setWidth(el.getBoundingClientRect().width);
        }}
      >
        <TooltipCommonDataGrid
          type="medication"
          rows={displayData}
          columns={columns}
          enableStatusColFilter={true}
          noDataMsg="No active medications"
          onRowDoubleClick={(row: any) => {
            toPldSelectRow("Meds", "full_order_id" + row.row.full_order_id);
          }}
          columnVisibilityModel={{
            status: isShowInactive,
          }}
        />
      </div>
    </Box>
  );
};
