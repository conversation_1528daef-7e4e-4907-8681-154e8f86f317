/**
 * MedToolbar.tsx
 *
 * @author: jfsys
 * @description Toolbar for Medications
 */

import { useAtom, useAtomValue } from "jotai";
import { useMemo } from "react";
import { CommonToolbar, MenuItemProps } from "../common/CommonToolbar";
import { IconExternalLink, IconEye } from "../../../common/assets";
import {
  medsRowDataAtom,
  isShowInactiveMedAtom,
  reviewStatusAtom,
  pageAtom,
} from "../../context/MedAtoms";
import { toPldModule } from "@cci-monorepo/AmbPatOverview/utils/utils";
import { getTimeStr } from "@cci-monorepo/Pld/components/meds/MedsUtils";
import { medPageSize } from "../common/Constants";

export const MedToolbar = (props: any) => {
  const rowData = useAtomValue(medsRowDataAtom);
  const reviewStatus = useAtomValue(reviewStatusAtom);
  const page = useAtomValue(pageAtom);
  const [isShowInactive, setIsShowInactive] = useAtom(isShowInactiveMedAtom);

  const totalMeds = useMemo(() => {
    if (isShowInactive) {
      return rowData.length;
    } else {
      return rowData.filter((data: { [key: string]: string | number }) => {
        return String(data.status).toUpperCase() === "ACTIVE";
      }).length;
    }
  }, [isShowInactive, rowData]);

  const reviewComponent = useMemo(() => {
    if (reviewStatus.status === "reviewed") {
      return `Last reviewed by ${reviewStatus.user} at ${getTimeStr(
        reviewStatus.time
      )}`;
    }
    return "Medications Not Reviewed";
  }, [reviewStatus]);

  const handleRedirect = () => {
    toPldModule("Meds");
  };

  const handleRedirectCPOE = () => {
    Cci.RunTime.onLaunchApp2({
      list: "cpoe_psat",
    });
  };

  const handleShowInactiveClick = () => {
    setIsShowInactive(!isShowInactive);
  };

  const menuItems: MenuItemProps[] = [
    {
      label: "View In Health Profile",
      icon: <IconExternalLink />,
      handler: handleRedirect,
      hidden: props.readonly ? true : false,
    },

    {
      label: "Place Order in CPOE",
      icon: <IconExternalLink />,
      handler: handleRedirectCPOE,
      hidden: props.readonly ? true : false,
    },
    {
      label: `${isShowInactive ? "Hide" : "Show"} Inactive Items`,
      icon: <IconEye />,
      handler: handleShowInactiveClick,
    },
  ];

  return (
    <CommonToolbar
      name="Medication History"
      isPldWidget={true}
      isCritical={true}
      hasReviewed={reviewStatus.status === "reviewed"}
      count={page * medPageSize}
      totalCount={totalMeds}
      menuItems={menuItems}
      reviewMsg={reviewComponent}
    />
  );
};
