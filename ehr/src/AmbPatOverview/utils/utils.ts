import Config from "@cci-monorepo/config/Config";

export type pldModuleName =
  | "Allergies"
  | "Meds"
  | "Pregnancy"
  | "Problems"
  | "Procedures"
  | "FamilyHx"
  | "AdvanceDirectives"
  | "Immunizations"
  | "SocialHistoryAlcohol"
  | "SocialHistorySU"
  | "SocialHistoryTobacco"
  | "Implants"
  | "Demograph"
  | "Concerns";

export const toPldModule = (moduleName: pldModuleName) => {
  if (Config.inDevMode) {
    return;
  }
  cci.navigatePldModule = moduleName.includes("SocialHistory")
    ? "SocialHistory"
    : moduleName;
  Cci.RunTime.onLaunchApp2({
    apptext: "PLD",
    click: { conf: "pld", link: "launchapp", perms: "154:E" },
    leavehook: [],
    list: "pld",
  });
};

export const toPldWithPayLoad = (
  moduleName: pldModuleName,
  payLoad: {
    key: string;
    value: string;
  }
) => {
  if (Config.inDevMode) {
    console.log(payLoad);
    return;
  }
  cci.AmbPatGridType = moduleName;
  cci.AmbPatPayLoad = payLoad;
  toPldModule(moduleName);
};

export const toPldSelectRow = (moduleName: pldModuleName, rowName: any) => {
  if (Config.inDevMode) {
    return;
  }
  cci.selectRowName = rowName;
  toPldModule(moduleName);
};

export const toRegistion = (regid: string) => {
  if (Config.inDevMode) {
    return;
  }
  if (Cci && Cci.RunTime && Cci.RunTime.onLaunchApp2) {
    const patName = Cci.Patient.getName();
    let jump_params: any = [];
    jump_params.screen = "homepage.conf";
    jump_params.apptext = "REGISTRATION";
    jump_params.click = [];
    jump_params.click.conf = "registration";
    jump_params.click.link = "launchapp";
    jump_params.click.perms = "147:E";
    jump_params.leavehook = [];
    jump_params.list = "registration";
    cci.regid = regid;
    cci.regname = patName;
    cci.navigatePatMrn = Cci.Patient.getMrn();
    cci.openTo = {
      app: "registration",
      field: "subscriberId",
      tab: "ENCOUNTERS",
    };
    cci.navigatePatRegid = regid;
    cci.navigateTab = "Encounters";
    Cci.RunTime.navigateTo({ screen: "homepage.conf" });
    setTimeout(function () {
      Cci.RunTime.onLaunchApp2(jump_params);
    }, 200);
  }
};

export const toRegistionSelectRow = (regid: string, encounterId: any) => {
  if (Config.inDevMode) {
    return;
  }
  if (Cci && Cci.RunTime && Cci.RunTime.onLaunchApp2) {
    cci.encounterId = encounterId;
    toRegistion(regid);
  }
};
