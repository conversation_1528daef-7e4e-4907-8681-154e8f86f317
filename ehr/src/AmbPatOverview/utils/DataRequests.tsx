/**
 * DataRequests.tsx
 *
 * @author: RnD
 * @description Base data request API to the server
 */

import axios from "axios";
import Config from "@cci-monorepo/config/Config";
import { APP_NAMES } from "@cci-monorepo/common";

const baseUrl: string = cci.cfg.baseUrl;

export const postData = (
  url: string,
  formData?: object,
  options?: object,
  onSuccess?: () => void
) => {
  let queryUrl = baseUrl + "/index.php/" + url + "?webtoken=" + Config.webtoken;
  return axios
    .post(queryUrl, formData, options)
    .then((response) => {
      if (onSuccess) onSuccess();
      return response.data;
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * Generic YCQL request
 * @params {string} hobj
 * @params {object} params
 * @params {callback} onSuccess
 * @params {callback} onFailure
 * @returns nothing
 */
export const serverRequest = async (
  hobj: string,
  params: any,
  onSuccess: (data: any) => void,
  onFailure: (error: any) => void
) => {
  let jsonData: any = {};
  const encounterInfo = Cci.RunTime.getEncounterInfo();
  try {
    if (
      hobj === "pld/allergies" ||
      hobj === "pld/pregnancy" ||
      hobj === "pld/demograph" ||
      hobj === "pld/demograph/edithistory" ||
      hobj === "pld/immunizations" ||
      hobj === "pld/implants" ||
      hobj === "pld/problems" ||
      hobj === "pld/familyhx" ||
      hobj === "pld/advancedirective" ||
      hobj === "pld/concern"
    ) {
      let parsedMrn = Cci.util.Patient.getMrn().replace(/-/g, ""),
        lastChar = parsedMrn.charAt(parsedMrn.length - 1),
        patpath = "vol" + lastChar + "/" + parsedMrn;
      jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: hobj,
        noBatch: true,
        visitkey: encounterInfo.visitkey,
        staffid: Cci.util.Staff.getSid(),
        params:
          hobj === "pld/immunizations"
            ? {
                ccitoken: Config.webtoken,
                endtime: encounterInfo.DischTime
                  ? encounterInfo.DischTime
                  : "-1",
                dbp: params.dbs,
              }
            : {
                ccitoken: Config.webtoken,
                endtime: encounterInfo.DischTime
                  ? encounterInfo.DischTime
                  : "-1",
                cursid: Cci.util.Staff.getSid(),
                curpatid: Cci.RunTime.getEncounterInfo().patid,
                patpath: patpath,
                ssn: params.ssn ? params.ssn : null,
                isreg: params.isreg ? params.isreg : null,
              },
        campus: params.campus,
        dbs: params.dbs,
      });
    } else if (hobj === "visit/type") {
      params.ccitoken = Config.webtoken;
      params.ccdb = Cci.util.Patient.getDbpath();
      params.sid = Cci.util.Staff.getSid();
      jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: hobj,
        noBatch: true,
        visitkey: encounterInfo.visitkey,
        params: params,
        campus: params.campus,
      });
    } else {
      params.ccitoken = Config.webtoken;
      jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: hobj,
        noBatch: true,
        dbs: [Cci.util.Patient.getDbpath()],
        visitkey: encounterInfo.visitkey,
        staffid: Cci.util.Staff.getSid(),
        params: params,
      });
    }
    if (!jsonData.errorMsg) {
      if (onSuccess) {
        onSuccess(jsonData);
      }
    } else {
      if (onFailure) {
        onFailure(jsonData.errorMsg);
      }
    }
  } catch (error) {
    if (onFailure) {
      onFailure(error);
    } else {
      console.error(error);
    }
  }
  return jsonData;
};

export const getRegId = (mrn: string, onSuccess: (data: any) => void) => {
  return postData("charge/getRegId", {
    mrn,
  }).then((resp) => {
    return onSuccess(resp.regid);
  });
};

export const saveEncounter = (
  action: string,
  argument: object,
  dbpath: string,
  encStatus: string,
  sid: string
) => {
  const data = {
    data: { [action]: argument },
    appname: APP_NAMES.REGISTRATION,
    dbpath: dbpath,
    encStatus: encStatus,
    sid: sid,
  };
  return postData("globalInbox/updateEncounterStatus", data);
};
