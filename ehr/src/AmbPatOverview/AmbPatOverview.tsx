/**
 * AmbPatOverview.tsx
 *
 * @description Main component for Patient Overview module
 * <AUTHOR>
 */

import { ThemeProvider, styled } from "@mui/material";
import { Provider } from "jotai";

import EhrTheme from "../theme/theme";
import MainContent from "./MainContent";
import { CommonErrorDialog, CommonToast } from "@cci-monorepo/common";
import EditPldHeader from "./EditPldHeader";

const Root = styled("main")(({ theme }) => ({
  height: "100%",
  width: "100%",
  display: "flex",
  flexDirection: "column",
}));

const MainBox = styled("main")(({ theme }) => ({
  position: "relative", // Make the module fit in webframe Tab
  width: "100%",
  display: "flex",
  flexDirection: "row",
  flexGrow: 1,
  height: 0,
}));

export default function AmbPatOverview() {
  return (
    <ThemeProvider theme={EhrTheme}>
      <Provider>
        <Root>
          <EditPldHeader />
          <MainBox>
            <MainContent />
            <CommonErrorDialog
              sx={{
                "& .MuiDialogTitle-root": {
                  padding: "16px 24px",
                },
                "& .MuiDialogActions-root": {
                  padding: "10px 24px 20px",
                },
              }}
            />
            <CommonToast
              tag="AmbPatOverview"
              anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
              autoHideDuration={3000}
              transitionDuration={250}
              alertSx={{
                minWidth: "300px",
                width: "auto",
                whiteSpace: "nowrap",
              }}
            />
          </MainBox>
        </Root>
      </Provider>
    </ThemeProvider>
  );
}
