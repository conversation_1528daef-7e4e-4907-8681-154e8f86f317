import { atom } from "jotai";
import { pldDbpathsAtom } from "./CommonAtoms";
import { isExpandedAtom } from "./SocialHistoryAtoms";
import { parseAlcoholData } from "@cci-monorepo/Pld/components/socialHistory/alcohol/AlcoholUtil";

export const socialHistoryALModuleStatusAtom = atom<any>({
  reviewed: false, //the review status is exactly "reviewed"
  uta: false, //unable to assess
  utaReason: "",
  needSaveReview: false,
  action: "", //"review", "uta", "ableToAssess"
  from: "",
});

export const currentAlcoholStatusAtom = atom("");
export const noneAlcoholAtom = atom<boolean>(false);
//export const noneDisappearAtom = atom<boolean>(false);
export const emptyAlcoholData: any = {
  QNR_freq: "",
  QNR_qty_per_day: "",
  QNR_gte_6_freq: "",
  agestarted: "",
  startdate: "",
  quitdate: "",
  restartdate: "",
  lastused: "",
  comments: "",
  alcoholid: "",
  status: "",
  type: "",
};
export const currentAlcoholDataAtom = atom(emptyAlcoholData);

export const hasReviewedAlcoholAtom = atom<boolean>(false);
export const entryFieldsAlcoholAtom = atom<string[]>([]);
export const alcoholNoneAtom = atom<boolean>(false);
export const allDataAlcoholAtom = atom<any>([]);
export const reviewMessageALAtom = atom<any>([]);

export const setReviewMessageAlcoholAtom = atom(null, (get, set, data: any) => {
  console.log("review Message", data);
  set(reviewMessageALAtom, data);
  let alcoholStatus = get(socialHistoryALModuleStatusAtom);
  if (data.length > 0 && data[0].status === "unable to assess") {
    alcoholStatus.uta = true;
    alcoholStatus.utaReason = data[0].reason;
  } else {
    alcoholStatus.uta = false;
  }
  if (data.length > 0) {
    const reviewed = data.find((row: any) => row.status === "reviewed");
    alcoholStatus.reviewed = !(reviewed === undefined);
  } else {
    alcoholStatus.reviewed = false;
  }
  set(
    hasReviewedAlcoholAtom,
    data.some((row: any) => row.status === "reviewed")
  );
  set(
    socialHistoryALModuleStatusAtom,
    JSON.parse(JSON.stringify(alcoholStatus))
  );
});

export const setParseDataAlcoholAtom = atom(
  null,
  (get, set, data: any, index: number) => {
    set(currentAlcoholStatusAtom, "");
    const pldDbpaths = get(pldDbpathsAtom);
    const curDbPath = Cci.util.Patient.getDbpath();
    let allData = get(allDataAlcoholAtom);
    const [alRows, alEntryFields, alReviewMessage] = parseAlcoholData(
      data,
      index
    );

    if (alRows && alRows.length > 0) {
      set(isExpandedAtom, true);
    }

    const alDbpath = pldDbpaths[index]["dbpath"];
    if (alDbpath === curDbPath) {
      if (alRows && alRows.length > 0) {
        //console.log("current data: ", alRows);
        set(currentAlcoholDataAtom, alRows[0]);
        const isNone =
          alRows[0]["QNR_freq"] === "0" &&
          alRows[0]["QNR_qty_per_day"] === "0" &&
          alRows[0]["QNR_gte_6_freq"] === "0";
        set(alcoholNoneAtom, isNone);
      } else {
        set(currentAlcoholDataAtom, emptyAlcoholData);
        const ditem: any = {};
        ditem[alDbpath] = emptyAlcoholData;
        if (alReviewMessage && alReviewMessage.length > 0) {
          ditem[alDbpath]["status"] = alReviewMessage[0].status;
        }
        allData.push(ditem);
      }
      if (alReviewMessage && alReviewMessage.length > 0) {
        if (
          alReviewMessage[0].status === "unable to assess" ||
          alReviewMessage[0].status === "reviewed"
        ) {
          set(currentAlcoholStatusAtom, alReviewMessage[0].status);
        }
      }
    }

    if (alRows && alRows.length > 0) {
      const ditem: any = {};
      ditem[alDbpath] = alRows[0];
      if (alReviewMessage && alReviewMessage.length > 0) {
        ditem[alDbpath]["status"] = alReviewMessage[0].status;
      }
      allData.push(ditem);
    }
    allData.sort((a: any, b: any) => {
      //sort by lgtime desc
      const av: any = Object.values(a)[0];
      const bv: any = Object.values(b)[0];
      const avv = parseInt(av.lgtime);
      const bvv = parseInt(bv.lgtime);
      if (isNaN(avv) || isNaN(bvv)) return 0;
      return avv === bvv ? 0 : avv > bvv ? -1 : 1;
    });
    set(allDataAlcoholAtom, allData);
    set(entryFieldsAlcoholAtom, alEntryFields);
    set(reviewMessageALAtom, alReviewMessage);

    if (
      alReviewMessage.length > 0 &&
      alReviewMessage[0].status === "unable to assess"
    ) {
      alReviewMessage.uta = true;
      alReviewMessage.utaReason = alReviewMessage[0].reason;
    }
  }
);
