import { atom } from "jotai";
import { parseData } from "../components/nursesubjective/SubjectiveUtils";

export const savedDataAtom = atom<string>("");
export const editedMessageDataAtom = atom<any>(null);
export const historyDataAtom = atom<any>([]);

export const setParseDataAtom = atom(null, (get, set, data: any) => {
  const historyResDatas = parseData(data);

  if (historyResDatas.length > 0) {
    const historyDatas = historyResDatas.map((item: any) => {
      return {
        ...item,
        source: "Nurse Intake",
        lgtime: Cci.util.DateTime.serverSecsToTimeStr(
          item.lgtime,
          "MM/DD/YYYY HH:mm"
        ),
      };
    });
    set(historyDataAtom, historyDatas);

    const currentData = historyDatas[historyDatas.length - 1];
    set(savedDataAtom, currentData.description ?? "");

    if (historyDatas.length > 1 || currentData.key !== currentData.lgtime) {
      let last_edited =
        "Last edited by " + currentData.lgname + " at " + currentData.fmt_time;
      set(editedMessageDataAtom, {
        storedBy: currentData.lgname,
        storedAt: Cci.util.DateTime.serverSecsToTimeStr(
          currentData.lgtime,
          "HHmm DD MMM YYYY"
        ),
        last_edited: last_edited,
      });
    }
    return currentData;
  } else {
    set(historyDataAtom, []);
    set(savedDataAtom, "");
    set(editedMessageDataAtom, null);
    return {};
  }
});
