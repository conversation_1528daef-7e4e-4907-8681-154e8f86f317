import {
  convertArrayToObject,
  getFullData,
} from "@cci-monorepo/Pld/components/allergies/AllergyUtils";
import { atom } from "jotai";
import { randomId } from "@mui/x-data-grid-generator";

export const rowDataAllergyAtom = atom<any[]>([]);
export const isShowInactiveAllergyAtom = atom<boolean>(false);
export const noKnownAllergyAtom = atom<boolean>(false);

export const hasReviewedAllergyAtom = atom<boolean>(false);
export const pageAtom = atom<number>(1);

export const reviewMessageAllergyAtom = atom<any>([]);
export const setReviewMessageAllergyAtom = atom(null, (get, set, data: any) => {
  set(reviewMessageAllergyAtom, data);
  if (data.length > 0) {
    const reviewed = data.find((row: any) => row.status === "reviewed");
    set(hasReviewedAllergyAtom, !(reviewed === undefined));
  } else {
    set(hasReviewedAllergyAtom, false);
  }
});

// Parse allergy data
export const setParseDataAllergyAtom = atom(null, (get, set, data: any) => {
  const gridRows = getDisplayRows(data);
  const newGridRows: any = [];
  set(noKnownAllergyAtom, false);
  gridRows.forEach((row: any) => {
    if (row.name === "No Known Allergies") {
      set(noKnownAllergyAtom, true);
    } else {
      newGridRows.push(row);
    }
  });
  set(rowDataAllergyAtom, newGridRows);
});

// Get rows for the main data grid
const getDisplayRows = (data: any) => {
  let gridRows: any = [];
  let allergies: any = [];
  const field = "allergies_data";
  allergies.push(convertArrayToObject(data[field]));
  let fullData: any = getFullData(
    allergies.reduce(
      (prev: any, cur: any) => (prev ? prev.concat(cur) : cur),
      []
    )
  );
  let rows = Object.keys(fullData).map((key: string, index: number) => {
    return fullData[key][0];
  });
  let activeRows: any = [];
  let inactiveRows: any = [];
  let errorRows: any = [];

  // Add id to each row and group them by status
  rows.forEach((row: any) => {
    if (row.status === "Active") {
      activeRows.push({ ...row, id: randomId() });
    } else if (row.status === "Inactive") {
      inactiveRows.push({ ...row, id: randomId() });
    } else {
      errorRows.push({ ...row, id: randomId() });
    }
  });
  // Returns active, followed by inactive and error
  gridRows = activeRows.concat(inactiveRows, errorRows);
  return gridRows;
};
