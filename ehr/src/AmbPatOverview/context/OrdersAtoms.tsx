import { atom } from "jotai";
import CommonRenderCell from "../components/common/CommonRenderCell";
import RecurringFlag from "@oe-src/common/renderer/nameRenderer/RecurringFlag";
import { ReactComponent as ImagingIcon } from "../../oe-shared/src/assets/Icn_Imaging.svg";
import { ReactComponent as ReferralIcon } from "../../oe-shared/src/assets/Icn_Referral.svg";
import { ReactComponent as LabsIcon } from "../../oe-shared/src/assets/Icn_Labs_MR.svg";
import { ReactComponent as ActiveIcon } from "../../oe-shared/src/assets/Icn_active_dot.svg";
import { ReactComponent as FutureIcon } from "../../oe-shared/src/assets/Icn_future_dot.svg";
import { ReactComponent as PendingIcon } from "../../oe-shared/src/assets/Icn_pending_dot.svg";

export const rowDataOrdersAtom = atom<any[]>([]);
export const columnDataOrdersAtom = atom<any[]>([
  {
    field: "name",
    headerName: "Name",
    minWidth: 160,
    renderCell: (params: any) => (
      <div style={{ display: "flex" }}>
        {params.row?.order_timing === "Recurring" ? (
          <RecurringFlag value={""} onClick={() => {}} />
        ) : (
          <></>
        )}
        <span style={{ paddingLeft: "2px" }}>{params.row?.name}</span>
      </div>
    ),
  },
  {
    field: "category",
    headerName: "Category",
    minWidth: 150,
    renderCell: (params: any) => (
      <div style={{ display: "flex" }}>
        {params.row?.category === "Imaging" ? (
          <ImagingIcon
            style={{ width: "20px", height: "20px", paddingRight: "1px" }}
          />
        ) : params.row?.category === "Labs" ||
          params.row?.category === "Lab" ? (
          <LabsIcon
            style={{ width: "20px", height: "20px", paddingRight: "1px" }}
          />
        ) : params.row?.category === "Referral" ? (
          <ReferralIcon
            style={{ width: "20px", height: "20px", paddingRight: "1px" }}
          />
        ) : (
          <></>
        )}
        {params.row?.category}
      </div>
    ),
  },
  {
    field: "status",
    headerName: "Status",
    minWidth: 130,
    renderCell: (params: any) => (
      <div style={{ display: "flex" }}>
        {params.row?.status === "Active" ? (
          <ActiveIcon
            style={{ width: "20px", height: "20px", paddingRight: "1px" }}
          />
        ) : params.row?.status === "Future" ? (
          <FutureIcon
            style={{ width: "20px", height: "20px", paddingRight: "1px" }}
          />
        ) : params.row?.status === "Pending" ? (
          <PendingIcon
            style={{ width: "20px", height: "20px", paddingRight: "1px" }}
          />
        ) : (
          <></>
        )}
        {params.row?.status}
      </div>
    ),
  },
  {
    field: "duedate",
    headerName: "Order Date",
    minWidth: 120,
    renderCell: CommonRenderCell,
  },
]);
export const rowDataSelectedCategoryAtom = atom("All");
