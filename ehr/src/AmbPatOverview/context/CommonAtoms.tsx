/*
 * CommonAtoms.tsx
 *
 * @author: jfsys
 * @description Declaration of all common atoms used by PatientOverview modules
 */

import { atom } from "jotai";

export const isEditableAtom = atom<boolean>(true);
export const showInputErrorAtom = atom<boolean>(false);
export const regIdAtom = atom<string>("");
export const editModuleAtom = atom<string>("");
export const changeEditModuleAtom = atom<boolean>(false);
export const userContextAtom = atom<string>("");
export const pldDbpathsAtom = atom([]);
export const displayAddDiagnosesBannerAtom = atom<boolean>(false);

export type UndoItem = {
  field: string;
  prevValue: any;
};

export const undoStackAtom = atom<UndoItem[]>([]);
export const redoStackAtom = atom<UndoItem[]>([]);
