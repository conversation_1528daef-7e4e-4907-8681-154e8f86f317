import { atom } from "jotai";
import { convertArrayToObject } from "../components/reason/ReasonUtils";

export const savedHPIAtom = atom<string>("");
export const editedMessageHPIAtom = atom<any>(null);
export const historyHPIAtom = atom<any>([]);
export const hasReviewedHPIAtom = atom<boolean>(false);
export const currentHPIAtom = atom<any>({});

// Parse HPI data
export const setParseDataHPIAtom = atom(null, (get, set, data: any) => {
  const historyHPIDatas = convertArrayToObject(data);

  if (historyHPIDatas.length > 0) {
    const historyDatas = historyHPIDatas.map((item: any) => {
      return {
        ...item,
        lgtime: Cci.util.DateTime.serverSecsToTimeStr(
          item.lgtime,
          "MM/DD/YYYY HH:mm"
        ),
        hpi: item.data,
      };
    });
    set(historyHPIAtom, historyDatas);

    const lastHPI = historyHPIDatas[historyHPIDatas.length - 1];

    const currentHPI = {
      ...lastHPI,
      hpi: lastHPI.data,
      editedTime: lastHPI.fmt_time,
    };
    set(savedHPIAtom, currentHPI.hpi ?? "");

    let reviewed = historyHPIDatas.some((item: any) => {
      return (
        item.key !== item.lgtime &&
        item.lgsid === String(Cci.util.Staff.getSid())
      );
    });
    set(hasReviewedHPIAtom, reviewed);

    if (historyHPIDatas.length || currentHPI.key !== currentHPI.lgtime) {
      let last_edited =
        "Last edited by " +
        currentHPI.lgname +
        " at " +
        Cci.util.DateTime.serverSecsToTimeStr(
          currentHPI.lgtime,
          "hh:mm a MM/DD/YYYY"
        );
      set(editedMessageHPIAtom, {
        storedBy: currentHPI.lgname,
        storedAt: Cci.util.DateTime.serverSecsToTimeStr(
          currentHPI.lgtime,
          "HHmm DD MMM YYYY"
        ),
        last_edited: last_edited,
      });
    }
    set(currentHPIAtom, currentHPI);
    return currentHPI;
  } else {
    set(historyHPIAtom, []);
    set(savedHPIAtom, "");
    set(hasReviewedHPIAtom, false);
    set(editedMessageHPIAtom, null);
    set(currentHPIAtom, {});
    return {};
  }
});
