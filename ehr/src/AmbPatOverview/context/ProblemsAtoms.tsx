import {
  dataReloadTimes<PERSON>tom,
  fullDataProblemsAtom,
  needSaveDiagnosesAtom,
  needSaveProblemsAtom,
  staffPerm138Atom,
} from "@cci-monorepo/common/context/atoms/problem";
import { GridRowModel, GridRowModesModel } from "@mui/x-data-grid-pro";

import { parseProblemsData } from "@cci-monorepo/common/utils/problem/ProblemsUtils";
import { atom } from "jotai";
import {
  diagnosesSaveAtom,
  problemsSaveAtom,
} from "@cci-monorepo/Pld/context/CommonAtoms";
import { OpenProblemDialogState } from "@cci-monorepo/common";
export const rowDataProblemsAtom = atom<GridRowModel[]>([]);
export const rowDataDiagnosisAtom = atom<GridRowModel[]>([]);
export const rowModesModelDiagnosisAtom = atom<GridRowModesModel>({});
export const rowModesModelProblemAtom = atom<GridRowModesModel>({});

// Parse Problems data
export const setParseDataProblemsAtom = atom(
  null,
  (get, set, data: any, index: number, isFirstLoad: boolean) => {
    const [ProblemsDatas, ProblemNeedSaveData, DiagnosisNeedSaveData] =
      parseProblemsData(data, index);
    set(fullDataProblemsAtom, data);
    let reloadTimes = 0;
    if (isFirstLoad) {
      if (ProblemNeedSaveData && ProblemNeedSaveData.length > 0) {
        reloadTimes = reloadTimes + 1;
      }
      if (DiagnosisNeedSaveData && DiagnosisNeedSaveData.length > 0) {
        reloadTimes = reloadTimes + 1;
      }
      set(dataReloadTimesAtom, reloadTimes);
      if (ProblemNeedSaveData && ProblemNeedSaveData.length > 0) {
        set(needSaveProblemsAtom, {
          value: true,
          action: "",
          data: ProblemNeedSaveData,
        });
      }
      if (DiagnosisNeedSaveData && DiagnosisNeedSaveData.length > 0) {
        set(needSaveDiagnosesAtom, {
          value: true,
          action: "",
          data: DiagnosisNeedSaveData,
        });
      }
    }
    ProblemsDatas.legacy_data.forEach((item: any) => {
      const newItem = {
        id: String(item.nit),
        nit: String(item.nit),
        key: 0,
        lgtime: 0,
        lgname: "",
        fmtlgtime: "",
        status: item.status,
        statusval: item.statusval,
        name: item.name,
        onsetdate: item.onsetdate,
        resolveddate: item.resolveddate,
        comments: item.comments,
        snomedcode: item.snomedcode,
        icd10code: item.icd10code,
        data_source: "VISTA",
        pinned: "0",
      };
      if (
        ProblemsDatas.problems_data.filter(
          (item: any) => item.id === newItem.id
        ).length === 0
      ) {
        ProblemsDatas.problems_data.push(newItem);
      }
    });
    set(rowDataProblemsAtom, ProblemsDatas.problems_data);
    set(rowDataDiagnosisAtom, ProblemsDatas.diagnosis_data);
    set(staffPerm138Atom, ProblemsDatas.perm138);
    if (!get(problemsSaveAtom)) {
      set(
        problemsSaveAtom,
        JSON.parse(
          JSON.stringify(
            ProblemsDatas.problems_data.filter(
              (item: any) => item.data_source !== "VISTA"
            )
          )
        )
      );
    }
    if (!get(diagnosesSaveAtom)) {
      set(
        diagnosesSaveAtom,
        JSON.parse(JSON.stringify(ProblemsDatas.diagnosis_data))
      );
    }
  }
);

export const openProblemDialogAtom = atom<OpenProblemDialogState>({
  open: false,
  module: "problem",
  clickNameCell: false,
  clickCancel: false,
});

const initModuleStatusAtom = () => {
  return atom<any>({
    reviewed: false, //the review status is exactly "reviewed"
    uta: false, //unable to assess
    utaReason: "",
    needSaveReview: false,
    action: "", //"review", "uta", "ableToAssess"
    from: "",
  });
};

export const reviewMessageProblemsAtom = atom<any>([]);
export const problemsModuleStatusAtom = initModuleStatusAtom();
export const hasReviewedProblemsAtom = atom<boolean>(false);

export const setReviewMessageProblemsAtom = atom(
  null,
  (get, set, data: any) => {
    set(reviewMessageProblemsAtom, data);
    let problemsStatus = get(problemsModuleStatusAtom);
    if (data.length > 0 && data[0].status === "unable to assess") {
      problemsStatus.uta = true;
      problemsStatus.utaReason = data[0].reason;
    } else {
      problemsStatus.uta = false;
    }
    if (data.length > 0) {
      const reviewed = data.find((row: any) => row.status === "reviewed");
      problemsStatus.reviewed = !(reviewed === undefined);
    } else {
      problemsStatus.reviewed = false;
    }
    set(problemsModuleStatusAtom, JSON.parse(JSON.stringify(problemsStatus)));
    set(hasReviewedProblemsAtom, data.length > 0);
  }
);

export const pageProbAtom = atom<number>(1);
export const displayCountProbAtom = atom<number>(0);
export const pageDiagAtom = atom<number>(1);
export const displayCountDiagAtom = atom<number>(0);
