/**
 * ConcernAtoms.tsx
 *
 * @author: CCI
 * @description Declaration of all atoms related to Concerns
 */

import { atom } from "jotai";
import { parseConcernsData } from "../components/history/Concerns/ConcernsUtils";

export const hasReviewedConcernsAtom = atom<boolean>(false);
export const rowDataConcernsAtom = atom([]);

export const concernsNoneAtom = atom<boolean>(false);

export const selectedRowConcernsAtom = atom<any>({});
export const rowSelectionModelConcernsAtom = atom<any>([]);

export const reviewMessageConcernsAtom = atom<any>([]);
export const setReviewMessageConcernsAtom = atom(
  null,
  (get, set, data: any) => {
    set(reviewMessageConcernsAtom, data);
    set(hasReviewedConcernsAtom, data.length > 0);
  }
);

// Parse Concerns data
export const setParseDataConcernsAtom = atom(
  null,
  (get, set, data: any, index: number) => {
    const [ConcernsDatas, hasNone] = parseConcernsData(data, index);

    set(rowDataConcernsAtom, ConcernsDatas);
    set(concernsNoneAtom, hasNone);
  }
);
