import { atom } from "jotai";
import { parseReasonData } from "../components/reason/ReasonUtils";
import _ from "lodash";

export const savedReasonAtom = atom<string>("");
export const editedMessageReasonAtom = atom<any>(null);
export const historyReasonAtom = atom<any>([]);
export const hasReviewedReasonAtom = atom<boolean>(false);
export const reviewMessageReasonAtom = atom<any>(null);

// Parse Reason data
export const setParseDataReasonAtom = atom(null, (get, set, data: any) => {
  const historyReasonDatas = parseReasonData(data);

  if (historyReasonDatas.length > 0) {
    const historyDatas = historyReasonDatas.map((item: any) => {
      return {
        ...item,
        lgtime: Cci.util.DateTime.serverSecsToTimeStr(
          item.lgtime,
          "MM/DD/YYYY HH:mm"
        ),
      };
    });
    set(historyReasonAtom, historyDatas);

    const currentReason = historyReasonDatas[historyReasonDatas.length - 1];
    set(savedReasonAtom, currentReason.reason ?? "");

    let reviewed = historyReasonDatas.some((item: any) => {
      return (
        item.key !== item.lgtime &&
        item.lgsid === String(Cci.util.Staff.getSid())
      );
    });
    set(hasReviewedReasonAtom, reviewed);
    const reviewData: any = _.maxBy(
      historyReasonDatas.filter(
        (item: any) =>
          item.key !== item.lgtime &&
          item.lgsid === String(Cci.util.Staff.getSid())
      ),
      "lgtime"
    );

    if (reviewed) {
      let last_reviewed =
        "Reviewed by " + reviewData?.lgname + " at " + reviewData?.fmt_time;
      set(reviewMessageReasonAtom, {
        last_reviewed,
      });
    }

    if (
      historyReasonDatas.length ||
      currentReason.key !== currentReason.lgtime
    ) {
      let last_edited =
        "Last edited by " +
        currentReason.lgname +
        " at " +
        Cci.util.DateTime.serverSecsToTimeStr(
          currentReason.lgtime,
          "hh:mm a MM/DD/YYYY"
        );
      set(editedMessageReasonAtom, {
        storedBy: currentReason.lgname,
        storedAt: Cci.util.DateTime.serverSecsToTimeStr(
          currentReason.lgtime,
          "HHmm DD MMM YYYY"
        ),
        last_edited: last_edited,
      });
    }
    return currentReason;
  } else {
    set(historyReasonAtom, []);
    set(savedReasonAtom, "");
    set(hasReviewedReasonAtom, false);
    set(editedMessageReasonAtom, null);
    return {};
  }
});
