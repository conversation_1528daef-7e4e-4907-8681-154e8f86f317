import { atom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { ReasonForVisit } from "../components/reason/ReasonForVisit";
import { Todos } from "../components/todos/Todos";
import { Notes } from "../components/notes/Notes";
import { HealthMaintenance } from "../components/healthmaintenance/HealthMaintenance";
import { OutstandingOrders } from "../components/outstandingorders/OutstandingOrders";
import { ClinicVisits } from "../components/clinicvisits/ClinicVisits";
import { Problems } from "../components/problems/Problems";
import { Problems as EditProblems } from "@cci-monorepo/Pld/components/problem/Problems";
import { Allergies } from "../components/allergies/Allergies";
import { Medications } from "../components/medications/Medications";
import { Pregnancy } from "../components/history/Pregnancy/Pregnancy";
import { Social } from "../components/history/Social/Social";
import { Family } from "../components/history/Family/Family";
import { Procedures } from "../components/history/Procedures/Procedures";
import { Implants } from "../components/history/Implants/Implants";
import { LabsGrid } from "../components/staticpanel/LabsGrid";
import { VitalsGrid } from "../components/staticpanel/VitalsGrid";
import { DiagnosticsTabPanel } from "../components/staticpanel/DiagnosticsTabPanel";
import { Concerns } from "../components/history/Concerns/Concerns";
import { HPI } from "../components/historypresentillness/HPI";
import { displayAddDiagnosesBannerAtom } from "./CommonAtoms";
import { NurseSubjective } from "../components/nursesubjective/NurseSubjective";
import _ from "lodash";

// Since EditProblems is in PLD, this is needed to get the banner working in AmbPatOverview.
const EditProblemsWithBanner = () => {
  const setDisplayAddDiagnosesBanner = useSetAtom(
    displayAddDiagnosesBannerAtom
  );
  return <EditProblems setDisplayAddDiagnosis={setDisplayAddDiagnosesBanner} />;
};

const defaultModuleList = [
  {
    id: 1,
    name: "Chief Complaint",
    component: <ReasonForVisit />,
    enabled: true,
  },
  {
    id: 2,
    name: "History of Present Illness",
    component: <HPI />,
    enabled: true,
    isNsWidget: false,
  },
  {
    id: 3,
    name: "NurseSubjective",
    component: <NurseSubjective />,
    enabled: true,
    isNsWidget: true,
  },
  {
    id: 4,
    name: "Todos",
    component: <Todos />,
    enabled: true,
    hidden: false,
  },
  {
    id: 5,
    name: "Notes",
    component: <Notes />,
    enabled: true,
  },
  {
    id: 6,
    name: "HealthMaintenance",
    component: <HealthMaintenance />,
    enabled: true,
  },
  {
    id: 7,
    name: "OutstandingOrders",
    component: <OutstandingOrders />,
    enabled: true,
  },
  {
    id: 8,
    name: "Encounter History",
    component: <ClinicVisits />,
    enabled: true,
  },
  {
    id: 9,
    componentId: "problems",
    title: "Problems & Diagnosis",
    name: "Problems",
    component: <Problems />,
    editComponent: <EditProblemsWithBanner />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 10,
    name: "Allergies",
    component: <Allergies />,
    enabled: true,
    isPldWidget: true,
    isCritical: true,
  },
  {
    id: 11,
    name: "Medications",
    component: <Medications />,
    enabled: true,
    isPldWidget: true,
    isCritical: true,
  },
  {
    id: 12,
    name: "Pregnancy",
    component: <Pregnancy />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 13,
    name: "Social",
    component: <Social />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 14,
    name: "Family",
    component: <Family />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 15,
    name: "Concerns",
    component: <Concerns />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 16,
    name: "Procedures",
    component: <Procedures />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 17,
    name: "Implants",
    component: <Implants />,
    enabled: true,
    isPldWidget: true,
  },
  {
    id: 18,
    name: "LabsGrid",
    component: <LabsGrid />,
    enabled: true,
    isLabWidget: true,
  },
  {
    id: 19,
    name: "VitalsGrid",
    component: <VitalsGrid />,
    enabled: true,
    isLabWidget: true,
  },
  {
    id: 20,
    name: "DiagnosticsTabPanel",
    component: <DiagnosticsTabPanel />,
    enabled: true,
    isLabWidget: true,
  },
];

export const defaultModules = _.map(defaultModuleList, (module, index) => ({
  ...module,
  id: index + 1,
}));

export const patOverviewModulesAtom = atom<any[]>(defaultModules);
