import { formatDate, isValidDate, toDateOrNull } from "@cci-monorepo/common";
import { atom } from "jotai";
import { regIdAtom } from "./CommonAtoms";

export const upcomingRowsAtom = atom<any[]>([]);
export const pastRowsAtom = atom<any[]>([]);
export const pageAtom = atom<number>(1);
export const totalVisitsAtom = atom<number>(0);

// Parse data
export const setParseDataAtom = atom(
  null,
  (
    get,
    set,
    dbData: any,
    keys: any,
    scheduledStatusList: any[],
    historicStatusList: any[]
  ) => {
    let encounterData: any;
    let upcomingRows: any[] = [];
    let pastRows: any[] = [];
    for (let i = 0; i < dbData.length; i++) {
      set(regIdAtom, dbData[i][keys["regid"]]);
      const encounterId = dbData[i][keys["encounterId"]];
      const dateTimeVal = dbData[i][keys["encounterDateTime"]];
      const dateVal = toDateOrNull(dateTimeVal);
      const dateStringVal = isValidDate(dateVal) ? formatDate(dateVal) : "";
      encounterData = {
        encounterId: encounterId,
        encounterDBPath: dbData[i][keys["encounterDBPath"]],
        specialty: dbData[i][keys["providerspecialty"]],
        date: dateStringVal,
        provider: dbData[i][keys["encounterAdmtProvider"]],
        reason: dbData[i][keys["encounterReasonVisit"]],
        status: dbData[i][keys["encounterStatus"]],
        department: dbData[i][keys["encounterDepartment"]],
      };
      if (scheduledStatusList.includes(dbData[i][keys["encounterStatus"]])) {
        upcomingRows.push(encounterData);
      } else if (
        historicStatusList.includes(dbData[i][keys["encounterStatus"]])
      ) {
        pastRows.push(encounterData);
      }
    }
    set(upcomingRowsAtom, upcomingRows);
    set(pastRowsAtom, pastRows);
  }
);
