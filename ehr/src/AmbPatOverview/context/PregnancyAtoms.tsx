/**
 * PregnancyAtoms.tsx
 *
 * @author: LMK
 * @description Declaration of all atoms related to allergies
 */

import { atom } from "jotai";
import { parsePregnancyData } from "../components/history/Pregnancy/PregnancyUtils";

interface PregnancyData {
  pregStatus: string | null;
  statedDueDate: string | null;
  lactStatus: string | null;
  needAutoSave: boolean;
}

const initModuleStatusAtom = () => {
  return atom<any>({
    reviewed: false, //the review status is exactly "reviewed"
    uta: false, //unable to assess
    utaReason: "",
    needSaveReview: false,
    action: "", //"review", "uta", "ableToAssess"
    from: "",
  });
};

export const pregnancyModuleStatusAtom = initModuleStatusAtom();

// General atoms
export const hasReviewedPregnancyAtom = atom<boolean>(false);
export const needAbleToAssessPregnancyAtom = atom<boolean>(false);
export const needSavePregnancyAtom = atom<boolean>(false);
export const editedPregnancyAtom = atom<boolean>(false);
export const needAutoSavePregnancyAtom = atom<boolean>(false);
export const needReloadPregnancyAtom = atom<boolean>(false);

// Pregnancy data
export const fullDataPregnancyAtom = atom([]);
export const oriPregnancyAtom = atom<PregnancyData | null>(null);
export const pregStatusAtom = atom<string>("");
export const statedDueDateAtom = atom<string>("");
export const lactStatusAtom = atom<string>("");
export const pregLgtimeAtom = atom<string>("");
export const displayPregnancyChecksumAtom = atom("");
export const openDateAtom = atom<boolean>(false);
export const statedDueDateInvalidAtom = atom<boolean>(false);

// Datagrid atoms
export const rowModesModelPregnancyAtom = atom({});

// Selected Pregnancy row
export const reviewMessagePregnancyAtom = atom<any>([]);

// Parse Pregnancy data
export const setParseDataPregnancyAtom = atom(null, (get, set, data: any) => {
  const moduleStatus = get(pregnancyModuleStatusAtom);
  const [pregnancyData, reviewMessage, displayPregnancyChecksum] =
    parsePregnancyData(data, [], "");

  set(fullDataPregnancyAtom, data);
  set(oriPregnancyAtom, pregnancyData);
  set(pregStatusAtom, pregnancyData.pregStatus);
  set(statedDueDateAtom, pregnancyData.statedDueDate);
  set(lactStatusAtom, pregnancyData.lactStatus);
  set(needAutoSavePregnancyAtom, pregnancyData.needAutoSave);
  set(reviewMessagePregnancyAtom, reviewMessage);
  set(displayPregnancyChecksumAtom, displayPregnancyChecksum);
  if (
    reviewMessage.length > 0 &&
    reviewMessage[0].status === "unable to assess"
  ) {
    moduleStatus.uta = true;
    moduleStatus.utaReason = reviewMessage[0].reason;
  } else {
    moduleStatus.uta = false;
  }
  if (reviewMessage.length > 0) {
    const reviewed = reviewMessage.find(
      (row: any) => row.status === "reviewed"
    );
    moduleStatus.reviewed = !(reviewed === undefined);
  } else {
    moduleStatus.reviewed = false;
  }
  set(hasReviewedPregnancyAtom, reviewMessage.length > 0);
  set(pregnancyModuleStatusAtom, JSON.parse(JSON.stringify(moduleStatus)));
});
