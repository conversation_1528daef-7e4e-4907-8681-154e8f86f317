import { atom } from "jotai";
import _ from "lodash";
import { parseImmunizationData } from "@cci-monorepo/Pld/components/immunization/ImmunizationUtil";

// In SZ29340, we're fine with hard-coded values, as shown in Figma.
const timeStr = "0900 19 Dec 2024";
export const defaultPreventiveData = [
  {
    name: "Weight Management",
    frequency: "Continuous monitoring",
    status: "Counseled",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Dental Exam",
    frequency: "Every 6-12 months",
    status: "Completed",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Eye Exam",
    frequency: "Every 1-2 years",
    status: "Completed",
    storedBy: "<PERSON> RN",
    storedAt: timeStr,
  },
  {
    name: "Mental Health Screening",
    frequency: "Periodically or as needed",
    status: "Counseled",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
];

export const defaultHealthData = [
  {
    name: "Cholesterol Screening",
    frequency: "Every 5 years or as advised",
    dueDate: "11/25/2024",
    status: "Complete",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Blood Glucose Screening",
    frequency: "Every 3 years or as advised",
    dueDate: "11/25/2024",
    status: "Complete",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Pap Smear (Cervical Cancer)",
    frequency: "Every 3-5 years",
    dueDate: "09/25/2024",
    status: "Outstanding Order",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Mammogram",
    frequency: "Every 3 years or as advised",
    dueDate: "09/25/2024",
    status: "Outstanding Order",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Sexually Transmitted Infections",
    frequency: "As needed, based on risk",
    dueDate: "",
    status: "Not Applicable",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
  {
    name: "Skin Cancer",
    frequency: "Annually or as needed",
    dueDate: "",
    status: "Not Applicable",
    storedBy: "Julie RN",
    storedAt: timeStr,
  },
];

export const rowDataImmuAtom = atom<any[]>([]);
export const pageImmuAtom = atom<number>(1);

export const rowDataPreventiveAtom = atom<any[]>(defaultPreventiveData);
export const pagePreventiveAtom = atom<number>(1);
export const hasReviewedPrevAtom = atom<boolean>(false);

export const rowDataHealthAtom = atom<any[]>(defaultHealthData);
export const pageHealthAtom = atom<number>(1);
export const hasReviewedHealthAtom = atom<boolean>(false);

export const hasReviewedImmunizationAtom = atom<boolean>(false);
export const reviewedImmunizationAtom = atom<any>(null);
export const displayImmunizationsChecksumAtom = atom("");
export const timeRefreshAtom = atom(new Date().valueOf());

export const setImmunizationHistoryAtom = atom(null, (get, set, data: any) => {
  if (!_.isArray(data)) {
    return;
  }

  const reviewData: any = _.maxBy(
    _.filter(
      data,
      (item: any) =>
        item?.review_status === "reviewed" &&
        item.lgsid === String(Cci.util.Staff.getSid())
    ),
    "reviewed_ts"
  );
  if (reviewData) {
    reviewData.reviewed_text =
      "Reviewed by " + reviewData.user + " at " + reviewData.fmt_reviewed_tm;
  }

  set(hasReviewedImmunizationAtom, reviewData ? true : false);
  set(reviewedImmunizationAtom, reviewData);
});

export const setParseDataImmunizationAtom = atom(
  null,
  (get, set, data: any, index: number) => {
    const [
      gridRows,
      gridColumns,
      entryFields,
      reviewMessage,
      marRows,
      marColumns,
      displayImmunizationsChecksum,
    ] = parseImmunizationData(data, [], false, index);
    set(displayImmunizationsChecksumAtom, displayImmunizationsChecksum);
  }
);
