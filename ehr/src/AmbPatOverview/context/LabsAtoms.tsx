import { atom } from "jotai";

export const labsDataAtom = atom<any[]>([]);
export const selectedPeriodAtom = atom<string>("6");
export const selectedPeriodOptsAtom = atom<any[]>([
  { value: "3", label: "3 Months" },
  { value: "6", label: "6 Months" },
  { value: "18", label: "18 Months" },
]);

export const isChangePeriodAtom = atom<boolean>(false);

export const totalResultsAtom = atom<number>(0);
export const pageAtom = atom<number>(1);
