import { atom } from "jotai";
import { parseMedData } from "@cci-monorepo/Pld/components/meds/MedsUtils";
import { toDatetime } from "@cci-monorepo/Pld/context/MedsAtoms";

export const medsRowDataAtom = atom<any[]>([]);
export const isShowInactiveMedAtom = atom<boolean>(false);

export const reviewStatusAtom = atom<any>({});
export const hasReviewedMedAtom = atom<boolean>(false);
export const pageAtom = atom<number>(1);

// Parse home meds and preferred pharmacies
export const setParseDataAtom = atom(null, (get, set, data: any) => {
  let [medGridRowData, , reviewStatus] = parseMedData(data);
  medGridRowData = (medGridRowData ?? []).map((row: any) => {
    return {
      ...row,
      starttime: toDatetime(row.starttime),
      lasttaken: toDatetime(row.lasttaken),
      lastfilldate: toDatetime(row.lastfilldate),
    };
  });
  set(medsRowDataAtom, medGridRowData);
  set(reviewStatusAtom, reviewStatus);
});
