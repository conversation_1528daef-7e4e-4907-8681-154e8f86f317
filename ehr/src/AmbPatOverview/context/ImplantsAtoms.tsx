/**
 * ImplantsAtoms.tsx
 *
 * @author: RnD
 * @description Declaration of all atoms related to Implants
 */

import { atom } from "jotai";
import { parseImplantsData } from "../components/history/Implants/ImplantsUtils";

export const hasReviewedImplantsAtom = atom<boolean>(false);
export const rowDataImplantsAtom = atom([]);

export const implantsNoneAtom = atom<boolean>(false);

export const selectedRowImplantsAtom = atom<any>({});
export const rowSelectionModelImplantsAtom = atom<any>([]);

export const reviewMessageImplantsAtom = atom<any>([]);
export const setReviewMessageImplantsAtom = atom(
  null,
  (get, set, data: any) => {
    set(reviewMessageImplantsAtom, data);
    set(hasReviewedImplantsAtom, data.length > 0);
  }
);

// Parse Implants data
export const setParseDataImplantsAtom = atom(
  null,
  (get, set, data: any, index: number) => {
    const [ImplantsDatas, hasNone] = parseImplantsData(data, index);
    set(rowDataImplantsAtom, ImplantsDatas.implants_data);
    set(implantsNoneAtom, hasNone);
  }
);
