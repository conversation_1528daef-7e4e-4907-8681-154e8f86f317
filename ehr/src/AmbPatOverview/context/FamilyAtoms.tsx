import { atom } from "jotai";
import { parseFamilyHxData } from "../components/history/Family/FamilyUtils";

export const hasReviewedFamilyHxAtom = atom<boolean>(false);
export const rowDataFamilyHxAtom = atom([]);

// Selected fhx row
export const selectedRowFamilyHxAtom = atom<any>({});
export const rowSelectionModelFamilyHxAtom = atom<any>([]);

export const reviewMessageFamilyHxAtom = atom<any>([]);
export const setReviewMessageFamilyHxAtom = atom(
  null,
  (get, set, data: any) => {
    set(reviewMessageFamilyHxAtom, data);
    set(hasReviewedFamilyHxAtom, data.length > 0);
  }
);

export const setParseDataFamilyHxAtom = atom(
  null,
  (get, set, data: any, hlRow: any, index: number) => {
    const [gridRows] = parseFamilyHxData(data, index);

    const newGridRows: any = [];
    gridRows.forEach((row: any) => {
      newGridRows.push(row);
      row.pin = 0;
    });
    set(rowDataFamilyHxAtom, newGridRows);
    // for the highlight row to find the new id
    if (hlRow) {
      const mainHlRow = newGridRows.find((row: any) => row.name === hlRow.name);
      if (mainHlRow) {
        set(rowSelectionModelFamilyHxAtom, [mainHlRow.id]);
      }
    }
  }
);
