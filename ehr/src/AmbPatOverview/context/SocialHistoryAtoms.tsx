import { atom } from "jotai";
import { parseSocialHistoryData } from "../components/history/Social/SocialHistoryUtil";
import { parseTobaccoData } from "../components/history/Social/TobaccoUtil";
import { calcPackYears } from "@cci-monorepo/Pld/components/socialHistory/SocialHistoryUtil";

export const rowDataSubstanceUseAtom = atom<any>([]);
export const hasReviewedSubstanceUseAtom = atom<boolean>(false);

export const rowSelectionModelSubstanceUseAtom = atom<any>([]);
export const selectedRowSubstanceUseAtom = atom<any>({});

export const substanceUseNoneAtom = atom<boolean>(false);
export const reviewMessageSUAtom = atom<any>([]);

export const setReviewMessageSubstanceUseAtom = atom(
  null,
  (get, set, data: any) => {
    set(reviewMessageSUAtom, data);
    set(
      hasReviewedSubstanceUseAtom,
      data.some((row: any) => row.status === "reviewed")
    );
  }
);

export const setParseDataSocialHistoryAtom = atom(
  null,
  (get, set, data: any, hlRow: any, isEditable: boolean, index: number) => {
    const choiceLists: any = {};
    const [suGridRows, suReviewMessage] = parseSocialHistoryData(
      data,
      choiceLists,
      isEditable,
      index
    );

    set(rowDataSubstanceUseAtom, suGridRows);
    set(reviewMessageSUAtom, suReviewMessage);

    if (
      suReviewMessage.length > 0 &&
      suReviewMessage[0].status === "unable to assess"
    ) {
      suReviewMessage.uta = true;
      suReviewMessage.utaReason = suReviewMessage[0].reason;
    }

    let hasNone = false;
    suGridRows.forEach((item: any) => {
      if (
        String(item.status).toUpperCase() === "INACTIVE" &&
        item.type === "None"
      ) {
        hasNone = true;
      }
    });
    set(substanceUseNoneAtom, hasNone);
  }
);

export const isExpandedAtom = atom<boolean>(false);
export const dataCountAtom = atom<number>(0);

//TobaccoAtom Start
export const rowDataTobaccoAtom = atom<any>([]);
export const hasReviewedTobaccoAtom = atom<boolean>(false);

export const rowSelectionModelTobaccoAtom = atom<any>([]);
export const selectedRowTobaccoAtom = atom<any>({});

export const tobaccoNoneAtom = atom<boolean>(false);
export const reviewMessageTobaccoAtom = atom<any>([]);

export const setReviewMessageTobaccoAtom = atom(null, (get, set, data: any) => {
  set(reviewMessageTobaccoAtom, data);
  set(
    hasReviewedTobaccoAtom,
    data.some((row: any) => row.status === "reviewed")
  );
});

export const smokingPackYearsAtom = atom<string>("");

export const setParseDataTobaccoAtom = atom(
  null,
  (get, set, data: any, hlRow: any, isEditable: boolean, index: number) => {
    const choiceLists: any = {};
    const [tobaccoGridRows, tobaccoReviewMessage] = parseTobaccoData(
      data,
      choiceLists,
      isEditable,
      index
    );

    set(rowDataTobaccoAtom, tobaccoGridRows);
    set(reviewMessageTobaccoAtom, tobaccoReviewMessage);

    if (
      tobaccoReviewMessage.length > 0 &&
      tobaccoReviewMessage[0].status === "unable to assess"
    ) {
      tobaccoReviewMessage.uta = true;
      tobaccoReviewMessage.utaReason = tobaccoReviewMessage[0].reason;
    }

    let hasNone = false;
    let smokePackYear = "";
    tobaccoGridRows.forEach((item: any) => {
      if (
        String(item.status).toUpperCase() === "INACTIVE" &&
        item.type === "None"
      ) {
        hasNone = true;
      } else if (
        item.type === "Cigarette" &&
        String(item.status).toUpperCase() === "ACTIVE"
      ) {
        smokePackYear = calcPackYears(item);
      }
    });

    set(smokingPackYearsAtom, smokePackYear);
    set(tobaccoNoneAtom, hasNone);
  }
);

export const tobaccoCountAtom = atom<number>(0);
