import { atom } from "jotai";
import { isEqual } from "lodash";

export const formDataAtom = atom<any>({});

export const undoHistoryAtom = atom<any[]>([]);

export const redoHistoryAtom = atom<any[]>([]);

export const saveUndoAtom = atom(null, (get, set, newData: any) => {
  const current = get(formDataAtom);
  if (!isEqual(current, newData)) {
    const undoStack = get(undoHistoryAtom);
    set(undoHistoryAtom, [current, ...undoStack.slice(0, 9)]);
    set(formDataAtom, newData);
  }
});

export const undoAtom = atom(null, (get, set) => {
  const undoStack = get(undoHistoryAtom);
  if (undoStack.length === 0) return;

  const [prev, ...restUndo] = undoStack;
  const current = get(formDataAtom);
  const redoStack = get(redoHistory<PERSON>tom);

  set(undoHistory<PERSON>tom, restUndo);
  set(redoHistory<PERSON>tom, [current, ...redoStack.slice(0, 9)]);
  set(formData<PERSON>tom, prev);
});

export const redoAtom = atom(null, (get, set) => {
  const redoStack = get(redoHistoryAtom);
  if (redoStack.length === 0) return;

  const [next, ...restRedo] = redoStack;
  const current = get(formDataAtom);
  const undoStack = get(undoHistoryAtom);

  set(redoHistoryAtom, restRedo);
  set(undoHistoryAtom, [current, ...undoStack.slice(0, 9)]);
  set(formDataAtom, next);
});

export const undoRedoFlagAtom = atom(false);
