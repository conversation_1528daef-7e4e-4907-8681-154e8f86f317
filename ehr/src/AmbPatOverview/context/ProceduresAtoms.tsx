import { atom } from "jotai";
import { parseProcedureData } from "../components/history/Procedures/ProceduresUtils";

export const hasReviewedProcedureAtom = atom<boolean>(false);

export const rowDataProcedureAtom = atom([]);

export const noneAtom = atom<boolean>(false);

// Selected procedure row
export const selectedRowProcedureAtom = atom<any>({});
export const rowSelectionModelProcedureAtom = atom<any>([]);

export const reviewMessageProcedureAtom = atom<any>([]);
export const setReviewMessageProcedureAtom = atom(
  null,
  (get, set, data: any) => {
    set(reviewMessageProcedureAtom, data);
    set(hasReviewedProcedureAtom, data.length > 0);
  }
);

// Parse procedures data
export const setParseDataProcedureAtom = atom(
  null,
  (get, set, data: any, hlRow: any, isEditable: boolean, index: number) => {
    const [gridRows] = parseProcedureData(data, isEditable, index);

    set(noneAtom, false);
    const newGridRows: any = [];
    gridRows.forEach((row: any) => {
      if (row.procedure === "None") {
        set(noneAtom, true);
      } else if (row.procedure === "") {
        set(noneAtom, false);
      } else {
        newGridRows.push(row);
      }
    });
    set(rowDataProcedureAtom, newGridRows);
    // for the highlight row to find the new id
    if (hlRow) {
      const mainHlRow = newGridRows.find((row: any) => row.name === hlRow.name);
      if (mainHlRow) {
        set(rowSelectionModelProcedureAtom, [mainHlRow.id]);
      }
    }
  }
);
