import { useEffect, useState } from "react";
import { useAtom, useAtomValue } from "jotai";
import PldHeader from "@cci-monorepo/Pld/components/common/pld/PldHeader";
import {
  displayAddDiagnosesBannerAtom,
  editModuleAtom,
} from "./context/CommonAtoms";
import Box from "@mui/material/Box";
import ClinicalChargeToolButton from "@cci-monorepo/ClinicalChargeTool/components/common/ClinicalChargeToolButton";
import ClinicalChargeToolDrawer from "@cci-monorepo/ClinicalChargeTool/components/ClinicalChargeToolDrawer";
import CctAddDiagnosisBanner from "@cci-monorepo/ClinicalChargeTool/components/CctAddDiagnosisBanner";
import { CommomHeader } from "./components/common/CommonHeader";

export default function EditPldHeader() {
  const editModule = useAtomValue(editModuleAtom);
  const isEditing = editModule === "Problems & Diagnosis";
  const [open, setOpen] = useState<boolean>(false);
  const handleClick = () => {
    setOpen(true);
  };
  const [displayAddDiagnosesBanner, setDisplayAddDiagnosesBanner] = useAtom(
    displayAddDiagnosesBannerAtom
  );
  const handleOpenClinicalChargeTool = (event: any) => {
    setOpen(true);
  };
  const handleOnCloseBanner = (event: any) => {
    setDisplayAddDiagnosesBanner(false);
  };
  useEffect(() => {
    if (open) setDisplayAddDiagnosesBanner(false);
  }, [open]);

  return (
    <>
      <Box
        display="flex"
        alignItems="center"
        sx={(theme) => ({
          top: 0,
          zIndex: theme.zIndex.appBar,
          height: "44px",
          pl: 2,
          boxShadow: displayAddDiagnosesBanner
            ? ""
            : "0px 4px 6px rgba(0,0,0,0.1)",
          backgroundColor: "white",
        })}
      >
        <ClinicalChargeToolButton handleClick={handleClick} />
        <Box sx={{ ml: "auto" }}>
          {isEditing ? (
            <PldHeader disableHeaderGridBoxShadow />
          ) : (
            <CommomHeader />
          )}
        </Box>

        <ClinicalChargeToolDrawer open={open} setOpen={setOpen} />
      </Box>
      {displayAddDiagnosesBanner && (
        <CctAddDiagnosisBanner
          handleOpenClinicalChargeTool={handleOpenClinicalChargeTool}
          handleOnCloseBanner={handleOnCloseBanner}
        />
      )}
    </>
  );
}
