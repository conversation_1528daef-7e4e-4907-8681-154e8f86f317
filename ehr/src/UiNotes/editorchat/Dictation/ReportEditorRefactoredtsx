import React, { useEffect, useCallback } from 'react';
import { ThemeProvider, CssBaseline, Box } from '@mui/material';
import { EditorContent } from '@tiptap/react';
import { darkTheme } from './theme';
import { BACKGROUND_TITLE_COLOR } from './constants';

// Hooks
import { useEditorCore } from './hooks/useEditorCore';
import { useTemplateManagement } from './hooks/useTemplateManagement';
import { useMacroManagement } from './hooks/useMacroManagement';
import { useReportManagement } from './hooks/useReportManagement';
import { useVoiceRecognition } from './hooks/useVoiceRecognition';
import { useAIAssistant } from './hooks/useAIAssistant';

// Components
import { EditorToolbar } from './components/EditorToolbar';
import { SaveTemplateDialog } from './components/SaveTemplateDialog';
import { SaveReportDialog } from './components/SaveReportDialog';
import { VoiceControls } from './components/VoiceControls';
import { AIAssistantPanel } from './components/AIAssistantPanel';
import { MacroConfigDialog } from './components/MacroConfigDialog';

// Services
import { MacroService } from './services/MacroService';
import { ReportService } from './services/ReportService';
import { TemplateService } from './services/TemplateService';
import { AIService } from './services/AIService';

// Utils
import { canUndo, canRedo } from './utils/editorUtils';

interface ReportEditorProps {
  // Add any props that the original component needs
}

export const ReportEditorRefactored: React.FC<ReportEditorProps> = () => {
  // Initialize editor core
  const {
    editor,
    clearContent,
    setContent,
    getJSONContent,
    getTextContent,
    focusEditor,
    isEmpty,
    undo,
    redo,
  } = useEditorCore({
    onTab: (editor) => macroHooks.handleTab(editor),
    onShiftTab: (editor) => macroHooks.handleShiftTab(editor),
    onSelectionUpdate: (editor) => aiHooks.updateSelectedText(),
  });

  // Initialize template management
  const templateHooks = useTemplateManagement(editor);

  // Initialize macro management
  const macroHooks = useMacroManagement(editor);

  // Initialize report management
  const reportHooks = useReportManagement(editor);

  // Initialize voice recognition
  const voiceHooks = useVoiceRecognition({
    editor,
    macroData: macroHooks.macroData,
    onProcessTranscript: (text) => {
      console.log('Processed transcript:', text);
    },
    onCommandProcessed: (command) => {
      console.log('Processed command:', command);
      // Handle specific voice commands
      handleVoiceCommand(command);
    },
    onError: (error) => {
      console.error('Voice recognition error:', error);
    },
  });

  // Initialize AI assistant
  const aiHooks = useAIAssistant(editor);

  // Handle voice commands
  const handleVoiceCommand = useCallback((command: string) => {
    const lowerCommand = command.toLowerCase();
    
    if (lowerCommand.includes('autofix')) {
      const selectedText = aiHooks.selectedText;
      if (selectedText) {
        aiHooks.handleAutoFix(selectedText);
      }
    } else if (lowerCommand.includes('improve')) {
      const selectedText = aiHooks.selectedText;
      if (selectedText) {
        aiHooks.handleImproveText(selectedText);
      }
    } else if (lowerCommand.includes('showaiassistant')) {
      aiHooks.showAIAssistantPanel();
    } else if (lowerCommand.includes('hideaiassistant')) {
      aiHooks.hideAIAssistantPanel();
    }
  }, [aiHooks]);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([
          macroHooks.handleGetJsonMacroFileList(),
          reportHooks.handleGetJsonReportFileList(),
        ]);
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, []);

  // Handle template save
  const handleTemplateSave = useCallback(async (templateData: { name: string; owner: string }) => {
    if (!editor) return;

    try {
      const content = getJSONContent();
      if (content) {
        await TemplateService.createTemplate('current_user', {
          name: templateData.name,
          description: JSON.stringify(content),
          owner: templateData.owner,
          type: 'rad-template',
        });
        
        templateHooks.handleRadTemplateDialogClose();
        // Optionally reload templates or show success message
      }
    } catch (error) {
      console.error('Error saving template:', error);
    }
  }, [editor, getJSONContent, templateHooks]);

  // Handle report save
  const handleReportSave = useCallback(async (reportName: string) => {
    if (!editor) return;

    try {
      const content = getJSONContent();
      if (content) {
        await ReportService.saveReport({
          name: reportName,
          type: 'report',
          description: JSON.stringify(content),
          owner: 'current_user',
          examDate: new Date().toISOString(),
        });
        
        reportHooks.handleSaveAsDialogClose();
        // Optionally reload reports or show success message
      }
    } catch (error) {
      console.error('Error saving report:', error);
    }
  }, [editor, getJSONContent, reportHooks]);

  // Handle macro save
  const handleMacroSave = useCallback(async (macroData: {
    name: string;
    type: 'text' | 'dropdown' | 'template';
    description: string | any[];
  }) => {
    try {
      await MacroService.saveMacro({
        name: macroData.name,
        type: macroData.type,
        description: macroData.description,
        owner: 'current_user',
      });
      
      // Reload macros
      await macroHooks.handleGetJsonMacroFileList();
      
      // Close dialog (this would need to be implemented in the macro hooks)
      console.log('Macro saved successfully');
    } catch (error) {
      console.error('Error saving macro:', error);
    }
  }, [macroHooks]);

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box
        sx={{
          height: "calc(100vh - 40px)",
          minHeight: "600px",
          display: "flex",
          flexDirection: "column",
          backgroundColor: "background.default",
          color: "text.primary",
        }}
      >
        {/* Toolbar */}
        <Box
          sx={{
            pl: 1,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            position: "relative",
            zIndex: 10,
            boxShadow: 1,
            backgroundColor: BACKGROUND_TITLE_COLOR,
            flexShrink: 0,
          }}
        >
          <EditorToolbar
            canUndo={editor ? canUndo(editor) : false}
            canRedo={editor ? canRedo(editor) : false}
            hasContent={editor ? !isEmpty() : false}
            inTemplateEditing={templateHooks.inTemplateEditing}
            selectedItemName={templateHooks.selectedItem?.name}
            onSaveTemplate={templateHooks.handleSaveTemplate}
            onExitTemplateEditing={templateHooks.exitTemplateEditing}
            inNewReport={reportHooks.inNewReport}
            onSaveAsReport={reportHooks.openSaveAsReportDialog}
            onNewReport={reportHooks.startNewReport}
            onUndo={undo}
            onRedo={redo}
            showAIAssistant={aiHooks.showAIAssistant}
            onToggleAIAssistant={aiHooks.toggleAIAssistant}
            listening={voiceHooks.listening}
            onToggleVoice={voiceHooks.toggleListening}
            onOpenMacroConfig={() => macroHooks.openMacroConfig('text')}
            isSaving={templateHooks.isSavingRadTemplate}
          />
        </Box>

        {/* Main Content Area */}
        <Box sx={{ display: "flex", flex: 1, overflow: "hidden" }}>
          {/* Left Sidebar - Voice Controls */}
          <Box
            sx={{
              width: 300,
              borderRight: 1,
              borderColor: "divider",
              p: 2,
              overflow: "auto",
            }}
          >
            <VoiceControls
              listening={voiceHooks.listening}
              voiceStatus={voiceHooks.voiceStatus}
              voiceError={voiceHooks.voiceError}
              isNumberMode={voiceHooks.isNumberMode}
              browserSupportsSpeechRecognition={voiceHooks.browserSupportsSpeechRecognition}
              onToggleListening={voiceHooks.toggleListening}
              onStartListening={voiceHooks.startListening}
              onStopListening={voiceHooks.stopListening}
              transcript={voiceHooks.transcript}
              finalTranscript={voiceHooks.finalTranscript}
            />
          </Box>

          {/* Editor Area */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                flex: 1,
                overflow: "auto",
                p: 2,
                "& .ProseMirror": {
                  outline: "none",
                  minHeight: "100%",
                  fontSize: "14px",
                  lineHeight: 1.6,
                },
              }}
            >
              <EditorContent editor={editor} />
            </Box>
          </Box>
        </Box>

        {/* AI Assistant Panel */}
        <AIAssistantPanel
          open={aiHooks.showAIAssistant}
          onClose={aiHooks.hideAIAssistantPanel}
          selectedText={aiHooks.selectedText}
          onAutoFix={aiHooks.handleAutoFix}
          onImproveText={aiHooks.handleImproveText}
          onRephraseText={aiHooks.handleRephraseText}
          onRestructureText={aiHooks.handleRestructureText}
          onGenerateImpression={aiHooks.generateImpression}
          conversation={aiHooks.aiImpressionConversation}
          onClearConversation={aiHooks.clearAIConversation}
          isLoading={aiHooks.inFetchData}
          chatEndRef={aiHooks.chatEndRef}
        />

        {/* Dialogs */}
        <SaveTemplateDialog
          open={templateHooks.openRadTemplateDialog}
          onClose={templateHooks.handleRadTemplateDialogClose}
          onSave={handleTemplateSave}
          isSaving={templateHooks.isSavingRadTemplate}
        />

        <SaveReportDialog
          open={reportHooks.openSaveAsDialog}
          onClose={reportHooks.handleSaveAsDialogClose}
          onSave={handleReportSave}
        />

        <MacroConfigDialog
          open={false} // This would need to be managed by macro hooks
          onClose={() => {}} // This would need to be implemented
          onSaveMacro={handleMacroSave}
          macroType={macroHooks.macroType}
          onMacroTypeChange={(type) => {}} // This would need to be implemented
        />
      </Box>
    </ThemeProvider>
  );
};
