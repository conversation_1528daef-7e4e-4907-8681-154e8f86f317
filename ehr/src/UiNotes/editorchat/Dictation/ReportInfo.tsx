import React, { useEffect, useState } from "react";
import {
  Box,
  TextField,
  CssBaseline,
  InputAdornment,
  IconButton,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  <PERSON>alogActions,
  Button,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { Clear, Delete } from "@mui/icons-material";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  DeletedItemAtom,
  FormValuesAtom,
  InTemplateEditingAtom,
  MacroDataAtom,
  MacroTypeAtom,
  NewChoiceNameAtom,
  NewCodeDescriptionPairsAtom,
  ReportDataAtom,
  OpenConfigDialogAtom,
  SearchTextAtom,
  SelectedFloatingReportAtom,
  SelectedItemAtom,
  ReloadMacroDataAtom,
  ReloadRadTemplates<PERSON>tom,
  ReloadRadMacros<PERSON><PERSON>,
  RadMac<PERSON><PERSON><PERSON>,
} from "./ReportContext";
import { assignMacro, isJsonString, replaceCurrentNodeContent, convertDateString } from "./TipTapHelper";
import {
  MACRO_DROPDOWN_COLOR,
  FONT_COLOR,
  MACRO_TEXT_COLOR,
  API_URL,
  LIST_JSON_REPORT_FILES,
  DELETE_JSON_TEMPLATE,
  DELETE_JSON_MACRO,
} from "./Constant";
import { GridColDef, GridRowParams } from "@mui/x-data-grid";
import { DataGridPro, GridRenderCellParams } from "@mui/x-data-grid-pro";
import { MdModeEditOutline } from "react-icons/md";
import { MacroType, ReportType } from "./Type";
import SharedDataPanel from "./SharedDataPanel";
import { RadTemplateAPI } from "../Services/RadTemplateAPI";
import { RadMacroAPI } from "../Services/RadMacroAPI";

const darkTheme = createTheme({
  palette: {
    mode: "dark",
    background: {
      default: "#121212",
      paper: "#1e1e1e",
    },
    text: {
      primary: "#ffffff",
      secondary: "#aaaaaa",
    },
  },
});

const ReportInfo = (props: any) => {
  const [searchText, setSearchText] = useAtom(SearchTextAtom);
  const [selectedItem, setSelectedItem] = useAtom(SelectedItemAtom);
  const [macroData, setMacroData] = useAtom(MacroDataAtom);
  const reportData = useAtomValue(ReportDataAtom);
  const [data, setData] = useState<any>();

  // console.log("---> macroData: ", macroData);
  // console.log("---> macroData: ", macroData)
  // console.log("---> reportData: ", reportData);

  const [filteredData, setFilteredData] = useState([]);
  const setDeletedItem = useSetAtom(DeletedItemAtom);
  const [selectedFloatingReport, setSelectedFloatingReport] = useAtom(
    SelectedFloatingReportAtom
  );

  const [selectedOption, setSelectedOption] = useState("template");
  const setOpenConfigDialog = useSetAtom(OpenConfigDialogAtom);
  const setFormValues = useSetAtom(FormValuesAtom);
  const setMacroType = useSetAtom(MacroTypeAtom);
  const setNewChoiceName = useSetAtom(NewChoiceNameAtom);
  const setNewCodeDescriptionPairs = useSetAtom(NewCodeDescriptionPairsAtom);
  const [inTemplateEditing, setInTemplateEditing] = useAtom(InTemplateEditingAtom);

  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [pendingTemplate, setPendingTemplate] = useState<MacroType | null>(null);
  const [radTemplates, setRadTemplates] = useState<any[]>([]);
  const [isLoadingRadTemplates, setIsLoadingRadTemplates] = useState(false);
  const [reloadRadTemplates, setReloadRadTemplates] = useAtom(ReloadRadTemplatesAtom);
  const [radMacros, setRadMacros] = useAtom(RadMacrosAtom);
  const [isLoadingRadMacros, setIsLoadingRadMacros] = useState(false);
  const [reloadRadMacros, setReloadRadMacros] = useAtom(ReloadRadMacrosAtom);

  const macroTemplateColumns: GridColDef[] = [
    { field: "name", headerName: "Name", flex: 1.5, resizable: true },
    {
      field: "type",
      headerName: "Type",
      flex: 0.8,
      resizable: true,
      renderCell: (params: GridRenderCellParams) => (
        <Typography
          sx={{
            color: getTypeColor(params.row),
            font: "16px Roboto",
          }}
        >
          {params.row.type}
        </Typography>
      ),
    },
    { field: "owner", headerName: "Owner", flex: 1.2, resizable: true },
    {
      field: "action",
      headerName: "Action",
      flex: 0.8,
      resizable: true,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", gap: 0 }}>
          <IconButton
            size="small"
            color="primary"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              const item = params.row;
              if (item.type === "text") {
                setMacroType("text");
                setSelectedItem(item); // Set selectedItem for edit detection
                setOpenConfigDialog(true);
                setFormValues({
                  name: item.name,
                  description: item.description,
                });
              } else if (item.type === "dropdown") {
                setMacroType("dropdown");
                setSelectedItem(item); // Set selectedItem for edit detection
                setOpenConfigDialog(true);
                setNewChoiceName(item.name);
                // Ensure description is an array
                const description = Array.isArray(item.description) 
                  ? item.description 
                  : (typeof item.description === 'string' ? JSON.parse(item.description) : []);
                setNewCodeDescriptionPairs(description);
              } else if (item.type === "template") {
                if (inTemplateEditing) {
                  // Only show switch dialog if this is a different template than the current one
                  if (selectedItem?.id !== item.id) {
                    setPendingTemplate(item);
                    setOpenConfirmDialog(true);
                  }
                } else {
                  // Set template editing state and paste content
                  setInTemplateEditing(true);
                  setSelectedItem(item);
                  handleUseTemplate(item);
                }
                // Prevent row selection and panel expansion
                e.preventDefault();
                e.stopPropagation();
                return false;
              }
            }}
            sx={{
              display: ["dropdown", "text", "template"].includes(
                params.row.type
              )
                ? ""
                : "none",
              padding: "2px",
            }}
            disabled={selectedFloatingReport ? true : false}
          >
            <MdModeEditOutline />
          </IconButton>
          <IconButton
            color="primary"
            onClick={(e: { stopPropagation: () => void }) => {
              e.stopPropagation();
              handleDelete(params.row);
            }}
            sx={{ padding: "2px" }}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ];

  const reportColumns: GridColDef[] = [
    { field: "name", headerName: "Name", flex: 2.5, resizable: true },
    { field: "examType", headerName: "Exam Type", flex: 0.8, resizable: true },
    { field: "examDate", headerName: "Exam Date", flex: 0.8, resizable: true },
    { field: "owner", headerName: "Owner", flex: 1.5, resizable: true },
    {
      field: "signedDate",
      headerName: "Signed Date",
      flex: 0.8,
      resizable: true,
    },
    // {
    //   field: "action",
    //   headerName: "Action",
    //   flex: 0.5,
    //   resizable: true,
    //   renderCell: (params: GridRenderCellParams) => (
    //     <Box sx={{ display: "flex", gap: 0 }}>
    //       <IconButton
    //         size="small"
    //         color="primary"
    //         onClick={(e: { stopPropagation: () => void }) => {
    //           e.stopPropagation();
    //           setSelectedFloatingReport(params.row);
    //         }}
    //         sx={{
    //           display: params.row.type === "report" ? "" : "none",
    //           padding: "2px",
    //         }}
    //         disabled={selectedFloatingReport ? true : false}
    //       >
    //         <OpenInNew />
    //       </IconButton>
    //       <IconButton
    //         color="primary"
    //         onClick={(e: { stopPropagation: () => void }) => {
    //           e.stopPropagation();
    //           handleDelete(params.row);
    //         }}
    //         sx={{ padding: "2px" }}
    //       >
    //         <Delete />
    //       </IconButton>
    //     </Box>
    //   ),
    // },
  ];

  const [columns, setColumns] = useState<GridColDef[]>([]);

  const { editor } = props;

  useEffect(() => {
    if (!data) {
      setFilteredData([]);
      return;
    }

    // First, ensure all items have unique IDs
    const dataWithUniqueIds = data.map((item: any, index: number) => {
      // If item already has an id, use it, otherwise generate a unique one
      const uniqueId = item.id || `${item.type}-${item.name}-${Date.now()}-${index}`;
      return {
        ...item,
        id: uniqueId
      };
    });

    // Then filter based on search text
    const filtered = dataWithUniqueIds.filter((item: { name: string; type: string }) => {
      if (!searchText) {
        return true;
      } else {
        return (
          (item.name &&
            item.name.toLowerCase().includes(searchText.toLowerCase())) ||
          (item.type &&
            item.type.toLowerCase().includes(searchText.toLowerCase()))
        );
      }
    });

    setFilteredData(filtered);
  }, [data, searchText]);

  const [filesObj, setFilesObj] = useState<any>({});

  // Function to fetch rad templates
  const fetchRadTemplates = async () => {
    setIsLoadingRadTemplates(true);
    try {
      const staffId = "global";
      console.log('Fetching rad templates for staff:', staffId);
      const templates = await RadTemplateAPI.getRadTemplates(staffId);
      console.log('Received rad templates:', templates);
      setRadTemplates(templates);
    } catch (error) {
      console.error('Error fetching rad templates:', error);
      setRadTemplates([]);
    } finally {
      setIsLoadingRadTemplates(false);
    }
  };

        const fetchRadMacros = async () => {
        setIsLoadingRadMacros(true);
        try {
          const staffId = "global";
          console.log('Fetching rad macros for staff:', staffId);
          const macros = await RadMacroAPI.getRadMacros(staffId);
          console.log('Received rad macros:', macros);
          // Log the types of each macro
          macros.forEach((macro: any) => {
            console.log(`Macro "${macro.name}" has type: "${macro.type}"`);
          });
          setRadMacros(macros);
        } catch (error) {
          console.error('Error fetching rad macros:', error);
          setRadMacros([]);
        } finally {
          setIsLoadingRadMacros(false);
        }
      };

  const handleGetJsonReportFileList = async () => {
    try {
      const response = await fetch(LIST_JSON_REPORT_FILES);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();
      console.log("Fetched file list:", data);
      setFilesObj(data);
    } catch (error) {
      console.error("Error fetching file list:", error);
    }
  };

  useEffect(() => {
    handleGetJsonReportFileList();
  }, []);

  // Fetch rad templates when template option is selected
  useEffect(() => {
    if (selectedOption === "template") {
      fetchRadTemplates();
    }
  }, [selectedOption]);

  // Fetch rad macros when macro option is selected
  useEffect(() => {
    if (selectedOption === "macro") {
      fetchRadMacros();
    }
  }, [selectedOption]);

  // Refresh rad templates when reloadRadTemplates is triggered
  useEffect(() => {
    if (reloadRadTemplates) {
      fetchRadTemplates();
      setReloadRadTemplates(false);
    }
  }, [reloadRadTemplates, setReloadRadTemplates]);

  // Refresh rad macros when reloadRadMacros is triggered
  useEffect(() => {
    if (reloadRadMacros) {
      fetchRadMacros();
      setReloadRadMacros(false);
    }
  }, [reloadRadMacros, setReloadRadMacros]);

  // Update selectedItem when rad templates are refreshed
  useEffect(() => {
    if (selectedItem && selectedItem.templateID && radTemplates.length > 0) {
      // Find the updated template in the refreshed data
      const updatedTemplate = radTemplates.find((template: any) => 
        template.templateID === selectedItem.templateID
      );
      
      if (updatedTemplate) {
        console.log('Updating selectedItem with fresh data:', {
          old: { name: selectedItem.name, owner: selectedItem.owner },
          new: { name: updatedTemplate.name, owner: updatedTemplate.owner }
        });
        
        // Update the selectedItem with the fresh data from server
        setSelectedItem({
          ...selectedItem,
          name: updatedTemplate.name,
          owner: updatedTemplate.owner,
          description: updatedTemplate.description,
        });
      }
    }
  }, [radTemplates]); // Remove selectedItem from dependencies

  useEffect(() => {
    let currentData = data || [];
    
    if (selectedOption === "template") {
      // Filter existing data to only show non-rad templates (templates without templateID)
      const existingTemplates = currentData.filter((item: any) => 
        item.type === "template" && !item.templateID
      );
      
      // Transform rad templates to match expected format and ensure unique IDs
      const transformedRadTemplates = radTemplates.map((template: any, index: number) => ({
        id: template.templateID || `rad-template-${template.name}-${Date.now()}-${index}`,
        name: template.name,
        type: "template",
        description: template.description,
        owner: template.owner,
        templateID: template.templateID,
        staffID: template.staffID,
        creationTime: template.creationTime,
        modifiedTime: template.modifiedTime
      }));
      
      // Combine templates - rad templates take precedence over existing templates with same ID
      const allTemplates = [...existingTemplates, ...transformedRadTemplates];
      const uniqueTemplates = allTemplates.filter((template, index, self) => {
        const firstIndex = self.findIndex(t => t.id === template.id);
        // Keep the template if it's the first occurrence OR if it's a rad template (has templateID)
        return index === firstIndex || template.templateID;
      });
      
      currentData = uniqueTemplates;
      setData(currentData);
      setColumns(macroTemplateColumns);
    } else if (selectedOption === "macro") {
      // Filter existing data to only show non-rad macros (macros without macroID)
      const existingMacros = macroData
        ? macroData.filter((item: MacroType) => item.type !== "template" && !item.macroID)
        : [];
      
      // Transform rad macros to match expected format and ensure unique IDs
      const transformedRadMacros = radMacros.map((macro: any, index: number) => {
        // Determine the correct type based on description content
        let correctType = "text";
        if (macro.type === "text" || macro.type === "dropdown") {
          correctType = macro.type;
        } else if (macro.type === "rad") {
          // If type is "rad", try to determine from description
          try {
            const description = macro.description;
            if (typeof description === 'string') {
              // Check if it's JSON (dropdown) or plain text
              const parsed = JSON.parse(description);
              if (Array.isArray(parsed)) {
                correctType = "dropdown";
              } else {
                correctType = "text";
              }
            } else {
              correctType = "text";
            }
          } catch (e) {
            // If parsing fails, it's text
            correctType = "text";
          }
        }
        
        return {
          id: macro.macroID || `rad-macro-${macro.name}-${Date.now()}-${index}`,
          name: macro.name,
          type: correctType,
          description: macro.description,
          macroID: macro.macroID,
          staffID: macro.staffID,
          creationTime: macro.creationTime,
          modifiedTime: macro.modifiedTime
        };
      });
      
      // Combine macros - rad macros take precedence over existing macros with same ID
      const allMacros = [...existingMacros, ...transformedRadMacros];
      const uniqueMacros = allMacros.filter((macro, index, self) => {
        const firstIndex = self.findIndex(m => m.id === macro.id);
        // Keep the macro if it's the first occurrence OR if it's a rad macro (has macroID)
        return index === firstIndex || macro.macroID;
      });
      
      // Ensure unique IDs for macro data
      const macroDataWithUniqueIds = uniqueMacros.map((item: any, index: number) => ({
        ...item,
        id: item.id || `macro-${item.name}-${Date.now()}-${index}`
      }));
      setData(macroDataWithUniqueIds);
      setColumns(macroTemplateColumns);
    } else if (selectedOption === "report") {
      // Ensure unique IDs for report data
      const reportDataWithUniqueIds = reportData ? reportData.map((item: any, index: number) => ({
        ...item,
        id: item.id || `report-${item.name}-${Date.now()}-${index}`
      })) : [];
      setData(reportDataWithUniqueIds);
      setFilteredData(reportDataWithUniqueIds);
      setColumns(reportColumns);

      // If we have a newly added report, select it
      if (props.newlyAddedReport) {
        setSelectedItem(props.newlyAddedReport);
      }
      // Otherwise, if we have report data and no selected item, find the most recently added report
      else if (reportData && reportData.length > 0 && !selectedItem) {
        const mostRecentReport = reportData.reduce((prev: ReportType, current: ReportType) => {
          return (prev.id > current.id) ? prev : current;
        });
        setSelectedItem(mostRecentReport);
      }
    }
  }, [macroData, reportData, radTemplates, radMacros, selectedOption, setData, setFilteredData, inTemplateEditing, props.newlyAddedReport]);

  // Add a new useEffect to handle the initial state
  useEffect(() => {
    if (inTemplateEditing) {
      // When in template editing mode, ensure we're showing only non-template items in the macro tab
      if (selectedOption === "macro") {
        const filteredData = macroData
          ? macroData.filter((item: MacroType) => item.type !== "template")
          : [];
        // Ensure unique IDs for macro data
        const macroDataWithUniqueIds = filteredData.map((item: any, index: number) => ({
          ...item,
          id: item.id || `macro-${item.name}-${Date.now()}-${index}`
        }));
        setData(macroDataWithUniqueIds);
      }
    }
  }, [inTemplateEditing, macroData, selectedOption]);

  // Put this here for now
  const [patData, setPatData] = useState<any>(null);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(API_URL);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const result = await response.json();
        setPatData(result);
      } catch (error: any) {
        console.log(error.message);
      }
    };

    fetchData();
  }, []);

  // Set template content to editor upon double clicking on a template from the template list
  const handleUseTemplate = (item: MacroType) => {
    if (editor && item && item.description) {
      let templateContent: any;
      if (typeof item.description === 'string') {
        templateContent = isJsonString(item.description)
          ? JSON.parse(item.description)
          : item.description;
      } else {
        templateContent = item.description;
      }
      editor.commands.setContent(templateContent);
      editor.commands.insertContent("<p></p>");
      editor.commands.focus(1);
    }
  };

  const handleApplyMacro = (item: MacroType) => {
    if (editor) {
      assignMacro(editor, item);
    }
  };

  const getTypeColor = (item: MacroType) => {
    if (item) {
      const { type } = item;
      if (type === "text") {
        return MACRO_TEXT_COLOR;
      } else if (type === "dropdown") {
        return MACRO_DROPDOWN_COLOR;
      }
    }
    return FONT_COLOR;
  };

  const setReloadMacroData = useSetAtom(ReloadMacroDataAtom);

  // Handler for deleting a row
  const handleDelete = async (item: MacroType) => {
    try {
      console.log("Delete called for item:", item);
      
      // Type guard for rad template
      const isRadTemplate = (item: any): item is { templateID: string } =>
        item.type === "template" && typeof item.templateID === "string";

      // Type guard for rad macro
      const isRadMacro = (item: any): item is { macroID: string } =>
        (item.type === "text" || item.type === "dropdown") && 
        item.macroID && 
        typeof item.macroID === "string" && 
        item.macroID.length > 0;

      console.log("Is rad template:", isRadTemplate(item));
      console.log("Is rad macro:", isRadMacro(item));
      console.log("Item type:", item.type);
      console.log("Item macroID:", (item as any).macroID);
      console.log("Item has macroID property:", 'macroID' in item);
      console.log("Item keys:", Object.keys(item));

      if (isRadTemplate(item)) {
        const staffId = "global";
        await RadTemplateAPI.deleteRadTemplate(staffId, item.templateID);
        // Optimistically remove from local radTemplates state
        setRadTemplates((prev: any) => prev.filter((t: any) => t.templateID !== item.templateID));
        // Also remove from data state for immediate table update
        setData((prevData: any[]) => prevData.filter((d: any) => d.templateID !== item.templateID));
        // Trigger refresh of rad templates
        setReloadRadTemplates(true);
        // If the deleted item was selected, clear the selection
        if (selectedItem && (selectedItem as any).templateID === item.templateID) {
          setSelectedItem(null);
        }
      } else if (isRadMacro(item)) {
        console.log("Deleting rad macro with ID:", item.macroID);
        const staffId = "global";
        await RadMacroAPI.deleteRadMacro(staffId, item.macroID);
        // Optimistically remove from local radMacros state
        setRadMacros((prev: any) => prev.filter((m: any) => m.macroID !== item.macroID));
        // Also remove from data state for immediate table update
        setData((prevData: any[]) => prevData.filter((d: any) => d.macroID !== item.macroID));
        // Trigger refresh of rad macros
        setReloadRadMacros(true);
        // If the deleted item was selected, clear the selection
        if (selectedItem && (selectedItem as any).macroID === item.macroID) {
          setSelectedItem(null);
        }

      } else {
        // Check if this is actually a rad macro that wasn't caught by the type guard
        const isActuallyRadMacro = radMacros.some((m: any) => m.name === item.name);
        if (isActuallyRadMacro) {
          console.log("Found rad macro by name lookup:", item.name);
          const radMacro = radMacros.find((m: any) => m.name === item.name);
          if (radMacro && radMacro.macroID) {
            console.log("Deleting rad macro by name lookup, ID:", radMacro.macroID);
            const staffId = "global";
            await RadMacroAPI.deleteRadMacro(staffId, radMacro.macroID);
            // Optimistically remove from local radMacros state
            setRadMacros((prev: any) => prev.filter((m: any) => m.macroID !== radMacro.macroID));
            // Also remove from data state for immediate table update
            setData((prevData: any[]) => prevData.filter((d: any) => d.name !== item.name));
            // Trigger refresh of rad macros
            setReloadRadMacros(true);
            // If the deleted item was selected, clear the selection
            if (selectedItem && selectedItem.name === item.name) {
              setSelectedItem(null);
            }
            return; // Exit early
          }
        }
        
        // Handle local templates and macros using existing logic
        if (item.type === "template") {
          const response = await fetch(DELETE_JSON_TEMPLATE, {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ name: item.name, id: item.id }),
          });

          if (response.ok) {
            // Remove the template from local state
            const updatedData = macroData.filter((data: MacroType) => data.name !== item.name);
            setMacroData(updatedData);

            // If the deleted item was selected, clear the selection
            if (selectedItem && selectedItem.name === item.name) {
              setSelectedItem(null);
            }

            // Trigger a reload of the macro data
            setReloadMacroData(true);
          } else {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete template");
          }
        } else {
          // For local macros, use the old fetch endpoint
          const response = await fetch(DELETE_JSON_MACRO, {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ name: item.name }),
          });

          if (response.ok) {
            // Remove the macro from local state
            const updatedData = macroData.filter((data: MacroType) => data.name !== item.name);
            setMacroData(updatedData);

            // If the deleted item was selected, clear the selection
            if (selectedItem && selectedItem.name === item.name) {
              setSelectedItem(null);
            }

            // Trigger a reload of the macro data
            setReloadMacroData(true);
          } else {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete macro");
          }
        }
      }
    } catch (error) {
      console.error("Error deleting item:", error);
    }
  };

  // Handle Row Click
  const handleRowClick = (row: any) => {
    if (row.type === "template") {
      if (inTemplateEditing) {
        // If in template editing mode, open the template for editing
        setInTemplateEditing(true);
        setSelectedItem(row);
        const templateContent = typeof row.description === 'string'
          ? (isJsonString(row.description)
            ? JSON.parse(row.description)
            : row.description)
          : row.description;
        editor.commands.setContent(templateContent);
        editor.commands.focus("start");
        const templateRadio = document.querySelector('input[value="template"]');
        if (templateRadio) {
          (templateRadio as HTMLInputElement).click();
        }
      } else {
        // If in report editing mode, insert the template content at cursor
        const templateContent = typeof row.description === 'string'
          ? (isJsonString(row.description)
            ? JSON.parse(row.description)
            : row.description)
          : row.description;
        editor.chain()
          .focus()
          .insertContent(templateContent)
          .run();
      }
    } else {
      // Handle other types of macros
      setSelectedItem(row);
      const description = typeof row.description === 'string' ? row.description : '';
      if (row.type === "text") {
        replaceCurrentNodeContent(editor, description);
      } else if (row.type === "template") {
        const updatedContent = isJsonString(description)
          ? JSON.parse(description)
          : description;
        editor.commands.insertContent(updatedContent);
        editor.commands.focus("start");
      }
    }
  };

  // Handle Row Double Click
  const handleRowDoubleClick = (params: GridRowParams) => {
    const item = params.row;
    setSelectedItem(item);

    if (item.type === "template") {
      if (!props.inNewReport) {
        // In template editing mode
        setInTemplateEditing(true);
        handleUseTemplate(item);
      } else {
        // In report editing mode - just copy the template content
        handleUseTemplate(item);
      }
    } else if (item.type === "dropdown") {
      handleApplyMacro(item);
    }
  };

  const handleConfirmDialogClose = () => {
    setOpenConfirmDialog(false);
    setPendingTemplate(null);
  };

  const handleConfirmReplace = () => {
    if (pendingTemplate) {
      handleUseTemplate(pendingTemplate);
    }
    handleConfirmDialogClose();
  };

  // Template switching handlers
  const handleConfirmTemplateSwitch = () => {
    setOpenConfirmDialog(false);
    if (pendingTemplate) {
      setSelectedItem(pendingTemplate);
      handleUseTemplate(pendingTemplate);
      setPendingTemplate(null);
    }
  };

  const handleCancelTemplateSwitch = () => {
    setPendingTemplate(null);
    setOpenConfirmDialog(false);
  };

  const handleOptionChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchText("");
    setSelectedOption(e.target.value);
    // Don't clear the selected item when switching to template mode
    if (e.target.value !== "template") {
      setSelectedItem(null);
    }
  };

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box
        sx={{
          overflowY: "hidden",
          height: "100%",
        }}
      >
        <SharedDataPanel
          selectedOption={selectedOption}
          onOptionChange={(value) => {
            setSearchText("");
            setSelectedOption(value);
            if (value !== "template") {
              setSelectedItem(null);
            }
          }}
          showReportOption={true}
        />
        <Box sx={{ height: `calc(100% - 60px)`, overflowY: "auto" }}>
          {selectedOption === "template" && isLoadingRadTemplates ? (
            <Box sx={{ 
              display: "flex", 
              justifyContent: "center", 
              alignItems: "center", 
              height: "100%",
              color: "text.secondary"
            }}>
              <Typography>Loading rad templates...</Typography>
            </Box>
          ) : (
            <DataGridPro
            columnHeaderHeight={30}
            rowHeight={30}
            rows={filteredData}
            columns={columns}
            disableColumnMenu
            isCellEditable={() => false}
            hideFooter
            onRowClick={handleRowClick} // Row Click Event
            onRowDoubleClick={handleRowDoubleClick} // Row Double-Click Event
            sx={{
              fontSize: "16px",
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "#333", // Change header background color
                color: "white", // Change text color
              },
              "& .MuiDataGrid-columnSeparator": {
                color: "white", // Change column separator color
              },
              // Remove selected cell border
              "& .MuiDataGrid-cell:focus, & .MuiDataGrid-cell:focus-within": {
                outline: "none",
              },
            }}
          />
          )}
        </Box>
        <Dialog
          open={openConfirmDialog}
          onClose={handleConfirmDialogClose}
        >
          <DialogTitle>Replace Content?</DialogTitle>
          <DialogContent>
            <Typography>
              The editor already contains content. Do you want to replace it with the template?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleConfirmDialogClose}>Cancel</Button>
            <Button onClick={handleConfirmReplace} color="primary">
              Replace
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={openConfirmDialog}
          onClose={(event, reason) => {
            if (reason === 'backdropClick') {
              return;
            }
            handleCancelTemplateSwitch();
          }}
          disableEscapeKeyDown={false}
        >
          <DialogTitle>Switch Template</DialogTitle>
          <DialogContent>
            <DialogContentText>
              You are currently editing a template. Switching to another template will discard your changes. Do you want to continue?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelTemplateSwitch}>Cancel</Button>
            <Button onClick={handleConfirmTemplateSwitch} color="primary" autoFocus>
              Switch Template
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default ReportInfo;
