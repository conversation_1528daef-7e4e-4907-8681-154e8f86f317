import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
} from '@mui/material';

interface EditTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: { name: string; owner: string }) => void;
  initialName: string;
  initialOwner: string;
  isLoading?: boolean;
}

export const EditTemplateDialog: React.FC<EditTemplateDialogProps> = ({
  open,
  onClose,
  onSave,
  initialName,
  initialOwner,
  isLoading = false,
}) => {
  const [name, setName] = useState(initialName);
  const [owner, setOwner] = useState(initialOwner);

  // Update form when dialog opens with new initial values
  useEffect(() => {
    if (open) {
      setName(initialName);
      setOwner(initialOwner);
    }
  }, [open, initialName, initialOwner]);

  const handleSave = () => {
    if (name.trim() && owner.trim()) {
      onSave({
        name: name.trim(),
        owner: owner.trim(),
      });
    }
  };

  const handleClose = () => {
    // Reset form
    setName(initialName);
    setOwner(initialOwner);
    onClose();
  };

  const isFormValid = name.trim() && owner.trim() && !name.includes('/');

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "500px",
        },
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          Edit Template Properties
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            autoFocus
            label="Template Name"
            type="text"
            fullWidth
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={name.includes('/')}
            helperText={name.includes('/') ? "Template name cannot contain slashes (/)": ""}
            required
          />
          
          <TextField
            label="Owner"
            type="text"
            fullWidth
            value={owner}
            onChange={(e) => setOwner(e.target.value)}
            required
            placeholder="Enter owner name"
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={isLoading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          disabled={!isFormValid || isLoading}
          variant="contained"
        >
          {isLoading ? 'Saving...' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 