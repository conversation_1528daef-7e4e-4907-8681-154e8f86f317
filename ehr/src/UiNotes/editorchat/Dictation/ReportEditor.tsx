import React, { useState, useEffect, useRef, useCallback } from "react";
import useWebSocket from "@cci-monorepo/common/hooks/useWebSocket";

import {
  Box,
  CssBaseline,
  ThemeProvider,
  createTheme,
  Typography,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Tooltip,
  TableCell,
  TableBody,
  TableRow,
  Table,
  TableHead,
  ButtonGroup,
  Divider,
  CircularProgress,
  Popover,
} from "@mui/material";
import {
  SaveAs,
  Refresh,
  SettingsVoice,
  MicNone,
  SpeakerNotes,
  SpeakerNotesOff,
  Close,
  Help,
  Save,
  Description,
} from "@mui/icons-material";
import { TbMacro, TbTemplate } from "react-icons/tb";

import {
  Editor,
  EditorContent,
  Extension,
  JSONContent,
  useEditor,
} from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import "./tiptap.css";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import {
  AIImpression<PERSON>onversation<PERSON>tom,
  DropdownMacroInfoAtom,
  FormValuesAtom,
  InTemplateEditingAtom,
  MacroDataAtom,
  MrnAtom,
  NewChoiceNameAtom,
  NewCodeDescriptionPairsAtom,
  ReportDataAtom,
  OpenConfigDialogAtom,
  SearchTextAtom,
  SelectedItemAtom,
  ShowAIAssistantAtom,
  ReloadMacroDataAtom,
  MacroTypeAtom,
  ReloadRadTemplatesAtom,
} from "./ReportContext";
import {
  MACRO_DROPDOWN_COLOR,
  TITLE_COLOR,
  BACKGROUND_COLOR,
  BORDER_COLOR,
  DELETE_CMD,
  UNDO_CMD,
  MAIN_CMD,
  REDO_CMD,
  GOTO_CMD,
  ACTIVE_COLOR,
  MACRO_CMD,
  STARTNUMBER_CMD,
  STOPNUMBER_CMD,
  BEGIN_CMD,
  END_CMD,
  TODAY_CMD,
  YESTERDAY_CMD,
  BACKSPACE_CMD,
  CAPS_CMD,
  BEGINLINE_CMD,
  ENDLINE_CMD,
  MACRO_TEXT_COLOR,
  PICKMACRO_CMD,
  IMPRESSION_CMD,
  SHOWAIASSISTANT_CMD,
  HIDEAIASSISTANT_CMD,
  AUTOFIX_CMD,
  IMPROVE_CMD,
  REPHRASE_CMD,
  RESTRUCTURE_CMD,
  SIGN_CMD,
  SELECT_CMD,
  UNSELECT_CMD,
  CLEAR_CMD,
  COPY_CMD,
  NO_FINDINGS_MSG,
  GREETING_MSG,
  COPY_MSG,
  PATINFO_CMD,
  RADORDER_CMD,
  PROBLEM_CMD,
  LABRESULTS_CMD,
  RADREPORT_CMD,
  BACKGROUND_TITLE_COLOR,
  COPY_LAST_MSG,
  LIST_JSON_MACRO_FILES,
  LIST_JSON_REPORT_FILES,
  SAVE_JSON_TEMPLATE,
  SAVE_JSON_REPORT,
  NEXT_CMD,
  PREVIOUS_CMD,
  APPLY_CMD,
  REJECT_CMD,
} from "./Constant";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import MacroConfig from "./MacroConfig";
import { getPatInfo, normalQuery } from "@cci-monorepo/UiNotes/util/AIPilot";
import FloatingReport from "./FloatingReport";
import DiffMatchPatch from "diff-match-patch";
import FloatingAIAssistant from "./FloatingAIAssistant";
import { DropdownDescription, MacroType, ReportType } from "./Type";
import { CRITICAL_COMMANDS } from "./Constant";
import ReportInfo from "./ReportInfo";
import { styled } from "@mui/material/styles";
import {
  getMacroInfo,
  isDropdownMacroNode,
  findCurrentBracketField,
  findBracketPairs,
  findCurrentBracketPairIndex,
  findDropdownMacroInBrackets,
  capitalizeWordAtCursor,
  doBackspace,
  formatMrn,
  isJsonString,
  getIndex,
  processCommands,
  goToTheBeginningOfLine,
  goToTheEndOfLine,
  getTextSelection,
  insertContentAtCursor,
  replaceCurrentNodeContent,
  convertDateString,
  deleteFieldContent,
  formatText,
  replaceSelection,
  goToLabel,
} from "./TipTapHelper";
import {
  getCurrentNodeAttributes,
  getFindings,
  handleUpdateImpression,
  renderPacsViewerObject,
} from "./Helper";
import AutoFixPreview from "./AutoFixPreview";
import { iconSize } from "../../reports/components/styles";
import { SaveRadTemplateDialog } from "./SaveRadTemplateDialog";
import { RadTemplateAPI } from "../Services/RadTemplateAPI";

const darkTheme = createTheme({
  palette: {
    mode: "dark",
    background: {
      default: BACKGROUND_COLOR,
      paper: "#1e1e1e",
    },
    text: {
      primary: "#ffffff",
      secondary: "#b3b3b3",
    },
  },
  typography: {
    fontFamily: "Roboto",
    body1: {
      fontSize: "16px",
    },
    h5: {
      fontSize: "32px",
    },
    h6: {
      fontSize: "24px",
    },
  },
  components: {
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          font: "16px Roboto",
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          "&::-webkit-scrollbar": {
            width: "12px",
            height: "12px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "#1e1e1e",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#555555",
            borderRadius: "6px",
            border: "3px solid #1e1e1e",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            backgroundColor: "#888888",
          },
        },
      },
    },
  },
});

const CommandText = styled("strong")({
  color: "#4CAF50",
});

interface MacroInfo {
  type: string;
  description: any;
}

interface AutoFixPreviewPopoverProps {
  anchorEl: HTMLElement | null;
  diffs?: any[];
  isLoading?: boolean;
  open?: boolean;
  onClose?: () => void;
  loadingText?: string;
}

const AutoFixPreviewPopover = ({
  anchorEl,
  diffs = [],
  isLoading = false,
  open = Boolean(anchorEl),
  onClose = () => {},
  loadingText = "Processing...",
}: AutoFixPreviewPopoverProps) => {
  const id = open ? "autofix-preview" : undefined;

  // Use different positioning for loading state vs tooltip
  const popoverProps = isLoading
    ? {
        anchorReference: "anchorPosition" as const,
        anchorPosition: {
          top: window.innerHeight / 2,
          left: window.innerWidth / 2,
        },
        transformOrigin: {
          vertical: "center" as const,
          horizontal: "center" as const,
        },
      }
    : {
        anchorOrigin: {
          vertical: "bottom" as const,
          horizontal: "left" as const,
        },
        transformOrigin: {
          vertical: "top" as const,
          horizontal: "left" as const,
        },
      };

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      {...popoverProps}
      sx={{
        "& .MuiPopover-paper": {
          backgroundColor: "#000000",
          color: "white",
          padding: "8px",
          maxWidth: "400px",
          minWidth: "200px",
          border: "1px solid white",
        },
      }}
    >
      {isLoading ? (
        <Box sx={{ p: 2, display: "flex", alignItems: "center", gap: 1 }}>
          <CircularProgress size={20} />
          <Typography>{loadingText}</Typography>
        </Box>
      ) : (
        <Box sx={{ p: 1 }}>
          {diffs &&
            diffs.map((diff: any, index: number) => (
              <Typography
                key={index}
                component="span"
                sx={{
                  backgroundColor:
                    diff[0] === 1
                      ? "rgba(0, 255, 0, 0.2)"
                      : diff[0] === -1
                        ? "rgba(255, 0, 0, 0.2)"
                        : "transparent",
                  color:
                    diff[0] === 1
                      ? "#00ff00"
                      : diff[0] === -1
                        ? "#ff0000"
                        : "inherit",
                  textDecoration: diff[0] === -1 ? "line-through" : "none",
                }}
              >
                {diff[1]}
              </Typography>
            ))}
        </Box>
      )}
    </Popover>
  );
};

const ReportEditor = () => {
  const [selectedItem, setSelectedItem] = useAtom(SelectedItemAtom);
  const [openSaveAsDialog, setOpenSaveAsDialog] = useState(false);
  const setOpenConfigDialog = useSetAtom(OpenConfigDialogAtom);
  const [newName, setNewName] = useState<string>("");
  const [reloadMacroData, setReloadMacroData] = useAtom(ReloadMacroDataAtom);
  const [reloadReportData, setReloadReportData] = useState<boolean>(true);
  const setReloadRadTemplates = useSetAtom(ReloadRadTemplatesAtom);
  const [macroData, setMacroData] = useAtom(MacroDataAtom);
  const [reportData, setReportData] = useAtom(ReportDataAtom);
  const [newlyAddedReport, setNewlyAddedReport] = useState<ReportType | null>(
    null
  );
  const [voiceStatus, setVoiceStatus] = useState<
    "listening" | "error" | "idle"
  >("idle");
  const [voiceError, setVoiceError] = useState<string>("");
  const [autoFixAnchorEl, setAutoFixAnchorEl] = useState<HTMLElement | null>(
    null
  );
  const [positionMarker, setPositionMarker] = useState<HTMLElement | null>(
    null
  );

  const [enableSaveAs, setEnableSaveAs] = useState(false);

  // keep track of the selected drop down macro to assign to the current selected dropdown field
  const dropdownMacroInfo = useAtomValue(DropdownMacroInfoAtom);

  const setFormValues = useSetAtom(FormValuesAtom);
  const setNewChoiceName = useSetAtom(NewChoiceNameAtom);
  const setNewCodeDescriptionPairs = useSetAtom(NewCodeDescriptionPairsAtom);
  const setSearchText = useSetAtom(SearchTextAtom);

  const [showAITools, setShowAITools] = useState<boolean>(true);
  const [selectedText, setSelectedText] = useState<string>("");
  const [aiSuggestedText, setAISuggestedText] = useState("");
  const [inFetchData, setInFetchData] = useState<boolean>(false);

  const [showAIAssistant, setShowAIAssistant] = useAtom(ShowAIAssistantAtom);

  const [aiImpressionConversation, setAIImpressionConversation] = useAtom(
    AIImpressionConversationAtom
  );

  const chatEndRef = useRef<HTMLDivElement | null>(null);

  const [patInfo, setPatInfo] = useState<string>("");
  const setMrn = useSetAtom(MrnAtom);

  // @ts-ignore
  const { isConnected, lastMessage, sendMessage } = useWebSocket();

  const [inTemplateEditing, setInTemplateEditing] = useAtom(
    InTemplateEditingAtom
  );
  const [inNewReport, setInNewReport] = useState(false);

  const [openHelpDialog, setOpenHelpDialog] = useState(false);

  const [macroType, setMacroType] = useAtom(MacroTypeAtom);

  const [openRefreshDialog, setOpenRefreshDialog] = useState(false);

  const [openDuplicateDialog, setOpenDuplicateDialog] = useState(false);
  const [duplicateMessage, setDuplicateMessage] = useState("");

  const [openCloseConfirmDialog, setOpenCloseConfirmDialog] = useState(false);

  const [impressionLoadingEl, setImpressionLoadingEl] =
    useState<HTMLElement | null>(null);

  // Rad template dialog state
  const [openRadTemplateDialog, setOpenRadTemplateDialog] = useState(false);
  const [isSavingRadTemplate, setIsSavingRadTemplate] = useState(false);

  // Handle fetching macro data: templates, text macros, and choice macros
  const handleGetJsonMacroFileList = async () => {
    try {
      const response = await fetch(LIST_JSON_MACRO_FILES);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();
      console.log("Fetched macro file list:", data);
      const macros: MacroType[] = Object.values(data) || [];
      setMacroData(macros);
      console.log("macros: ", macros, ", macroData: ", macroData);
    } catch (error) {
      console.error("Error fetching macro file list:", error);
    }
  };

  useEffect(() => {
    if (reloadMacroData) {
      handleGetJsonMacroFileList();
      setReloadMacroData(false);
    }
  }, [reloadMacroData]);

  // console.log("macroData: ", macroData);

  // Handle fetching report data: external and internal reports
  const handleGetJsonReportFileList = async () => {
    try {
      const response = await fetch(LIST_JSON_REPORT_FILES);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();
      console.log("Fetched report file list:", data);
      const reports: ReportType[] = Object.values(data) || [];
      setReportData(reports);
    } catch (error) {
      console.error("Error fetching report file list:", error);
    }
  };

  useEffect(() => {
    if (reloadReportData) {
      handleGetJsonReportFileList();
      setReloadReportData(false);
    }
  }, [reloadReportData]);

  const onGetPatientInfo = async (mrn: string) => {
    const patInfo = await getPatInfo(mrn);
    console.log("patInfo: ", patInfo);
    setPatInfo(JSON.stringify(patInfo));
  };

  useEffect(() => {
    if (lastMessage) {
      const msgObj = JSON.parse(lastMessage);
      if (msgObj && msgObj.data && msgObj.data.study) {
        const study = msgObj.data.study;
        if (study) {
          const studyObj = JSON.parse(study);
          const tmpMrn = formatMrn(studyObj.mrn);
          setMrn(tmpMrn);
          onGetPatientInfo(tmpMrn);
          const renderedInfo = renderPacsViewerObject(studyObj);
          editor?.commands.setContent(`${renderedInfo}`);
        }
      }
    }
  }, [lastMessage]);

  // Why can't I use macroData directly??? Shouldn't have to fetch macro data every time!
  const handleTab = async (editor: Editor) => {
    const { from } = editor.state.selection;
    const lineStart = editor.state.doc.resolve(from).start();
    const lineEnd = editor.state.doc.resolve(from).end();

    const bracketPairs = findBracketPairs(editor.state.doc);
    if (bracketPairs.length === 0) return;

    // If cursor is at the start or before first bracket pair, go to first bracket pair
    if (from <= bracketPairs[0].start) {
      handleBracketSelection(editor, bracketPairs[0]);
      return;
    }

    // Find bracket pairs in current line
    const lineBracketPairs = bracketPairs.filter(
      (pair) => pair.start >= lineStart && pair.end <= lineEnd
    );

    if (lineBracketPairs.length > 0) {
      const currentIndex = findCurrentBracketPairIndex(lineBracketPairs, from);
      let targetIndex = 0;

      if (currentIndex === -1) {
        targetIndex = lineBracketPairs.findIndex((pair) => pair.start > from);
        if (targetIndex === -1) {
          const nextLineBracketPairs = bracketPairs.filter(
            (pair) => pair.start > lineEnd
          );
          handleBracketSelection(
            editor,
            nextLineBracketPairs[0] || bracketPairs[0]
          );
          return;
        }
      } else {
        targetIndex = currentIndex + 1;
        if (targetIndex >= lineBracketPairs.length) {
          const nextLineBracketPairs = bracketPairs.filter(
            (pair) => pair.start > lineEnd
          );
          handleBracketSelection(
            editor,
            nextLineBracketPairs[0] || bracketPairs[0]
          );
          return;
        }
      }

      handleBracketSelection(editor, lineBracketPairs[targetIndex]);
    } else {
      const nextLineBracketPairs = bracketPairs.filter(
        (pair) => pair.start > lineEnd
      );
      handleBracketSelection(
        editor,
        nextLineBracketPairs[0] || bracketPairs[0]
      );
    }
  };

  // Helper function to handle bracket selection and macro detection
  const handleBracketSelection = (
    editor: Editor,
    pair: { start: number; end: number }
  ) => {
    console.log("handleBracketSelection called with pair:", pair);
    // Move cursor to the start of the bracket content (after the opening bracket and space)
    const targetPos = pair.start + 2;
    editor.commands.focus();
    editor.commands.setTextSelection(targetPos);

    // Check for dropdown macro in the target bracket pair
    const potentialMacroName = findDropdownMacroInBrackets(
      editor,
      pair.start,
      pair.end
    );
    console.log("Potential macro name found:", potentialMacroName);

    if (potentialMacroName) {
      const dropdownMacro = macroData.find(
        (macro) =>
          macro.type === "dropdown" && macro.name === potentialMacroName
      );
      console.log("Found dropdown macro:", dropdownMacro);
      if (dropdownMacro) {
        setSelectedItem(dropdownMacro);
        console.log("Selected item set to:", dropdownMacro);
      }
    }
  };

  // Why can't I use macroData directly??? Shouldn't have to fetch macro data every time!
  const handleShiftTab = async (editor: Editor) => {
    const { from } = editor.state.selection;
    const lineStart = editor.state.doc.resolve(from).start();
    const lineEnd = editor.state.doc.resolve(from).end();

    const bracketPairs = findBracketPairs(editor.state.doc);
    const lineBracketPairs = bracketPairs.filter(
      (pair) => pair.start >= lineStart && pair.end <= lineEnd
    );

    if (lineBracketPairs.length > 0) {
      const currentIndex = findCurrentBracketPairIndex(lineBracketPairs, from);
      let targetIndex;

      if (currentIndex === -1) {
        targetIndex = lineBracketPairs.findLastIndex((pair) => pair.end < from);
        if (targetIndex === -1) {
          const prevLineBracketPairs = bracketPairs.filter(
            (pair) => pair.end < lineStart
          );
          if (prevLineBracketPairs.length > 0) {
            handleBracketSelection(
              editor,
              prevLineBracketPairs[prevLineBracketPairs.length - 1]
            );
            return;
          }
          const lastBracketPair = bracketPairs[bracketPairs.length - 1];
          if (lastBracketPair.start >= lineEnd) {
            handleBracketSelection(editor, lastBracketPair);
          }
          return;
        }
      } else {
        if (currentIndex === 0) {
          const prevLineBracketPairs = bracketPairs.filter(
            (pair) => pair.end < lineStart
          );
          if (prevLineBracketPairs.length > 0) {
            handleBracketSelection(
              editor,
              prevLineBracketPairs[prevLineBracketPairs.length - 1]
            );
            return;
          }
          const lastBracketPair = bracketPairs[bracketPairs.length - 1];
          if (lastBracketPair.start >= lineEnd) {
            handleBracketSelection(editor, lastBracketPair);
          }
          return;
        }
        targetIndex = currentIndex - 1;
      }

      handleBracketSelection(editor, lineBracketPairs[targetIndex]);
    } else {
      const prevLineBracketPairs = bracketPairs.filter(
        (pair) => pair.end < lineStart
      );
      if (prevLineBracketPairs.length > 0) {
        handleBracketSelection(
          editor,
          prevLineBracketPairs[prevLineBracketPairs.length - 1]
        );
      } else {
        const lastBracketPair = bracketPairs[bracketPairs.length - 1];
        if (lastBracketPair.start >= lineEnd) {
          handleBracketSelection(editor, lastBracketPair);
        }
      }
    }
  };

  const KeyboardShortcuts = Extension.create({
    name: "keyboardShortcuts",
    addKeyboardShortcuts() {
      return {
        Tab: ({ editor }) => {
          console.log("Tab pressed in KeyboardShortcuts");
          return false; // Let the editor handleKeyDown handle it
        },
        "Shift-Tab": ({ editor }) => {
          console.log("Shift-Tab pressed in KeyboardShortcuts");
          return false; // Let the editor handleKeyDown handle it
        },
      };
    },
  });

  // Helper function to find first bracket pair and move cursor inside it
  const moveCursorToFirstBracket = (editor: Editor | null) => {
    if (!editor) return;

    const { doc } = editor.state;
    const text = doc.textContent;
    const firstBracketIndex = text.indexOf("[");

    if (firstBracketIndex === -1) return;

    // Find the actual position in the document structure
    let currentPos = 0;
    let targetPos = 0;

    doc.descendants((node, pos) => {
      if (
        currentPos + node.textContent.length >= firstBracketIndex &&
        targetPos === 0
      ) {
        // Calculate the exact position within the node
        targetPos = pos + (firstBracketIndex - currentPos) + 2; // +2 to place cursor after the bracket
        return false; // Stop iterating
      }
      currentPos += node.textContent.length;
      return true;
    });

    if (targetPos > 0) {
      editor.commands.focus();
      editor.commands.setTextSelection(targetPos);
    }
  };

  // Tiptap editor setup
  const editor = useEditor({
    extensions: [StarterKit, KeyboardShortcuts],
    autofocus: true,
    editorProps: {
      handleKeyDown: (view, event: KeyboardEvent) => {
        if (event.key === "Tab") {
          handleKeyDown(event as unknown as React.KeyboardEvent);
          return true;
        }
        return false;
      },
    },
    onTransaction: ({ transaction, editor }) => {
      const flag = !editor.isEmpty;
      setEnableSaveAs(flag);
    },
    onSelectionUpdate: ({ editor }) => {
      const selection = editor.state.selection;
      const { from, to } = selection;
      setSelectedText(
        from !== to ? editor.state.doc.textBetween(from, to, " ") : ""
      );
    },
    onCreate: ({ editor }) => {
      moveCursorToFirstBracket(editor);
    },
  });

  const roEditor = useEditor({
    extensions: [StarterKit],
    editable: false,
  });

  const handleCommandWithConfirmation = (
    command: string,
    callback: Function
  ) => {
    if (
      CRITICAL_COMMANDS.includes(command as (typeof CRITICAL_COMMANDS)[number])
    ) {
      setPendingCommand({ command, callback });
    } else {
      callback();
    }
  };

  const processCommand = (command: string, callback: Function) => {
    setVoiceStatus("listening");
    handleCommandWithConfirmation(command, callback);
    setTimeout(() => setVoiceStatus("idle"), 1000);
  };

  const commands = [
    {
      command: MACRO_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          const macroDescription = getTextSelection(editor);
          if (macroDescription) {
            setMacroType("text");
            setOpenConfigDialog(true);
            setFormValues({
              name: "",
              description: macroDescription,
            });
          }
          return true;
        }
        return false;
      },
    },
    {
      command: UNDO_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        processCommand(cmd.command, () => {
          !showAIAssistant && editor?.commands.undo();
        });
      },
    },
    {
      command: REDO_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        processCommand(cmd.command, () => {
          !showAIAssistant && editor?.commands.redo();
        });
      },
    },
    {
      command: SELECT_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          const { state } = editor;
          const { selection } = state;
          const { $from } = selection;

          // Get the current node and its text content
          const node = $from.parent;
          const text = node.textContent;
          const pos = $from.pos;
          const textOffset = $from.textOffset;

          // Get the current line boundaries
          const lineStart = state.doc.resolve(pos).start();
          const lineEnd = state.doc.resolve(pos).end();
          const lineContent = state.doc.textBetween(lineStart, lineEnd);

          // Find word boundaries
          let start = textOffset;
          let end = textOffset;

          // If we're at the end of the line
          if (textOffset === text.length) {
            // Find the last word boundary in the current text
            const spaceIndex = text.lastIndexOf(" ");
            const colonIndex = text.lastIndexOf(":");
            const openBracketIndex = text.lastIndexOf("[");
            const closeBracketIndex = text.lastIndexOf("]");

            let lastWordBoundary = Math.max(
              spaceIndex,
              colonIndex,
              openBracketIndex,
              closeBracketIndex
            );

            start = lastWordBoundary + 1;
            if (start === 0) {
              // If no boundary found, we're at the beginning of the line
              start = 0;
            }
            end = text.length;

            // Calculate positions based on the current node
            const from = pos - (text.length - start);
            const to = pos;

            // Set the selection
            editor.chain().setTextSelection({ from, to }).focus().run();
          } else if (textOffset === 0 && pos > lineStart) {
            // If we're at the start of a new node but not at the start of the line
            // Get the previous text within the current line
            const prevText = lineContent.substring(0, pos - lineStart);

            // Check if the last character is a special character
            const lastChar = prevText[prevText.length - 1];
            if ([":", "[", "]", " "].includes(lastChar)) {
              // If it is, select just that character
              const from = pos - 1;
              const to = pos;
              editor.chain().setTextSelection({ from, to }).focus().run();
            } else {
              // Otherwise, find the last boundary
              const lastSpace = prevText.lastIndexOf(" ");
              const lastColon = prevText.lastIndexOf(":");
              const lastOpenBracket = prevText.lastIndexOf("[");
              const lastCloseBracket = prevText.lastIndexOf("]");

              const lastBoundary = Math.max(
                lastSpace,
                lastColon,
                lastOpenBracket,
                lastCloseBracket
              );

              const from = lineStart + lastBoundary + 1;
              const to = pos;
              editor.chain().setTextSelection({ from, to }).focus().run();
            }
          } else {
            // Find the start of the word
            while (start > 0 && /\w/.test(text[start - 1])) {
              start--;
            }

            // Find the end of the word
            while (end < text.length && /\w/.test(text[end])) {
              end++;
            }

            // Calculate the absolute positions
            const from = pos - (textOffset - start);
            const to = pos + (end - textOffset);

            // Set the selection
            editor.chain().setTextSelection({ from, to }).focus().run();
          }
        }
      },
    },
    {
      command: UNSELECT_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          const { to } = editor.state.selection;
          editor.chain().focus().setTextSelection(to).run();
        }
      },
    },
    {
      command: NEXT_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor && handleTab(editor);
      },
    },
    {
      command: PREVIOUS_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor && handleShiftTab(editor);
      },
    },
    {
      command: DELETE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        processCommand(cmd.command, () => {
          !showAIAssistant && editor && deleteFieldContent(editor);
        });
      },
    },
    {
      command: [STARTNUMBER_CMD, STOPNUMBER_CMD],
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!editor) return;

        const isListActive = editor.isActive("orderedList");
        const isStarting = cmd.command === STARTNUMBER_CMD;

        // Only toggle if we're starting a list when none exists, or stopping a list when one exists
        if ((isStarting && !isListActive) || (!isStarting && isListActive)) {
          editor.chain().focus().toggleList("orderedList", "listItem").run();
        }
      },
    },
    {
      command: BEGIN_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor?.commands.focus("start");
      },
    },
    {
      command: END_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor?.commands.focus("end");
      },
    },
    {
      command: BEGINLINE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor && goToTheBeginningOfLine(editor);
      },
    },
    {
      command: ENDLINE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor && goToTheEndOfLine(editor);
      },
    },
    {
      command: TODAY_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          const today = new Date();
          const formattedDate = `${String(today.getMonth() + 1).padStart(2, "0")}/${String(today.getDate()).padStart(2, "0")}/${today.getFullYear()}`;
          const bracketField = findCurrentBracketField(editor);
          if (bracketField) {
            editor
              .chain()
              .focus()
              .setTextSelection({
                from: bracketField.start + 1,
                to: bracketField.end - 1,
              })
              .deleteSelection()
              .insertContent(formattedDate)
              .run();
          } else {
            insertContentAtCursor(editor, formattedDate);
          }
        }
      },
    },
    {
      command: YESTERDAY_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          const formattedDate = `${String(yesterday.getMonth() + 1).padStart(2, "0")}/${String(yesterday.getDate()).padStart(2, "0")}/${yesterday.getFullYear()}`;
          const bracketField = findCurrentBracketField(editor);
          if (bracketField) {
            editor
              .chain()
              .focus()
              .setTextSelection({
                from: bracketField.start + 1,
                to: bracketField.end - 1,
              })
              .deleteSelection()
              .insertContent(formattedDate)
              .run();
          } else {
            insertContentAtCursor(editor, formattedDate);
          }
        }
      },
    },
    {
      command: HIDEAIASSISTANT_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        setShowAIAssistant(false);
      },
    },
    {
      command: AUTOFIX_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor && !inFetchData) {
          const bracketField = findCurrentBracketField(editor);
          if (bracketField) {
            // We're inside brackets
            const macroName = findDropdownMacroInBrackets(
              editor,
              bracketField.start,
              bracketField.end
            );
            const macroInfo = macroData.find(
              (macro) => macro.name === macroName
            );

            if (macroInfo?.type === "dropdown") {
              // For dropdown macros, only fix text after the colon
              const text = editor.state.doc.textBetween(
                bracketField.start,
                bracketField.end
              );
              const colonIndex = text.indexOf(":");
              if (colonIndex > 0) {
                const textToFix = text
                  .substring(colonIndex + 1, text.length - 1)
                  .trim();
                if (textToFix) {
                  // Calculate the exact range for the text after colon
                  const from = bracketField.start + colonIndex + 1;
                  const to = bracketField.end - 1;
                  onAutoFix(AUTOFIX_CMD, textToFix, from, to, false);
                  return true;
                }
              }
            } else {
              // For non-dropdown brackets, fix everything inside
              const textToFix = editor.state.doc
                .textBetween(bracketField.start + 1, bracketField.end - 1)
                .trim();
              if (textToFix) {
                onAutoFix(
                  AUTOFIX_CMD,
                  textToFix,
                  bracketField.start + 1,
                  bracketField.end - 1,
                  false
                );
                return true;
              }
            }
          }

          // If we have selected text, fix it regardless of whether it's inside brackets or not
          const { from, to } = editor.state.selection;
          const selectedText = editor.state.doc.textBetween(from, to);

          // Get the current line boundaries
          const lineStart = editor.state.doc.resolve(from).start();
          const lineEnd = editor.state.doc.resolve(to).end();
          const currentLine = editor.state.doc.textBetween(lineStart, lineEnd);

          console.log("Auto-fix selection details:", {
            from,
            to,
            selectedText,
            lineStart,
            lineEnd,
            currentLine,
          });

          if (selectedText) {
            // Store the exact range for later use
            setAutoFixRange({ from, to });
            onAutoFix(AUTOFIX_CMD, selectedText, from, to, false);
            return true;
          }

          return true;
        }
        return true; // Always return true to prevent default command processing
      },
    },
    {
      command: IMPROVE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && showAITools && selectedText) {
          onRewrite(IMPROVE_CMD, selectedText);
        }
      },
    },
    {
      command: REPHRASE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && showAITools && selectedText) {
          onRewrite(REPHRASE_CMD, selectedText);
        }
      },
    },
    {
      command: RESTRUCTURE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && showAITools && selectedText) {
          onRewrite(RESTRUCTURE_CMD, selectedText);
        }
      },
    },
    {
      command: SIGN_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant &&
          editor
            ?.chain()
            .focus("end")
            .insertContent(
              "<br/><p display=block color=cyan>Report Signed By: Dr. Demo</p>"
            )
            .run();
      },
    },
    {
      command: `${PICKMACRO_CMD} *`,
      callback: (macroName: string) => {
        console.log("command: ", macroName);
        if (!showAIAssistant && macroName && editor) {
          const choice = macroName.toLowerCase();
          const dIndex = getIndex(choice);
          const bracketField = findCurrentBracketField(editor);

          if (bracketField) {
            const macroName = findDropdownMacroInBrackets(
              editor,
              bracketField.start,
              bracketField.end
            );
            const macroInfo = macroData.find(
              (macro) => macro.name === macroName
            );

            if (
              macroInfo?.type === "dropdown" &&
              Array.isArray(macroInfo.description)
            ) {
              let newDescription = "";
              if (dIndex > 0 && dIndex <= macroInfo.description.length) {
                newDescription = macroInfo.description[dIndex - 1].description;
              } else {
                const choiceItem = macroInfo.description.find(
                  (cItem: DropdownDescription) =>
                    cItem.code.toLowerCase() === choice
                );
                if (choiceItem) {
                  newDescription = choiceItem.description;
                }
              }

              const newContent = `[${macroName}: ${newDescription}]`;

              editor
                .chain()
                .focus()
                .setTextSelection({
                  from: bracketField.start,
                  to: bracketField.end,
                })
                .deleteSelection()
                .insertContent(newContent)
                .setTextSelection({
                  from: bracketField.start + newContent.indexOf("]"),
                  to: bracketField.start + newContent.indexOf("]"),
                })
                .run();
            }
          }
        }
      },
    },
    {
      command: `${GOTO_CMD} *`,
      callback: (labelName: string) => {
        console.log("command: ", labelName);
        if (!showAIAssistant && editor) {
          const labelField = goToLabel(editor, labelName);
          if (labelField) {
            let tmpMacroData = [{}];
            const bracketField = findCurrentBracketField(editor);

            if (bracketField) {
              const macroName = findDropdownMacroInBrackets(
                editor,
                bracketField.start,
                bracketField.end
              );
              if (macroName) {
                const isDropdown = isDropdownMacroNode(labelField);
                if (isDropdown) {
                  tmpMacroData = macroData.filter((data: MacroType) => {
                    return data.name === macroName && data.type === "dropdown";
                  });
                } else {
                  tmpMacroData = macroData.filter((data: MacroType) => {
                    return data.name === macroName;
                  });
                }
              }
            }

            setSelectedItem(tmpMacroData[0]);
          }
        }
      },
    },
    {
      command: "*",
      callback: (command: string) => {
        const content = command.toLowerCase();
        if (!content || !editor) {
          return;
        }

        // Check for special commands first to prevent text replacement
        if (
          content === REJECT_CMD.toLowerCase() ||
          content === AUTOFIX_CMD.toLowerCase() ||
          content === APPLY_CMD.toLowerCase() ||
          content === "auto fix" ||
          (content === "okay" && autoFixAnchorEl)
        ) {
          return;
        }

        if (
          MAIN_CMD.includes(content) ||
          content.indexOf(PICKMACRO_CMD) !== -1 ||
          content.indexOf(GOTO_CMD) !== -1
        ) {
          return;
        }

        // Check for template name in report editing mode
        if (inNewReport) {
          const template = macroData.find(
            (macro) =>
              macro.type === "template" && macro.name.toLowerCase() === content
          );
          if (template) {
            handleTemplateCopy(template);
            return;
          }
        }

        const attrs = getCurrentNodeAttributes(editor);
        console.log("Current node attributes:", attrs);
        if (attrs?.date) {
          const updatedContent = convertDateString(command);
          console.log(
            "Converting date string:",
            command,
            "to:",
            updatedContent
          );
          replaceCurrentNodeContent(editor, updatedContent);
          return;
        }

        // Check for text macro
        console.log("Checking for text macro with content:", content);
        const textMacro = macroData.find(
          (macro) =>
            macro.type === "text" && macro.name.toLowerCase() === content
        );
        console.log("Found text macro:", textMacro);

        if (textMacro) {
          const description =
            typeof textMacro.description === "string"
              ? textMacro.description
              : "";
          console.log("Inserting text macro description:", description);
          editor.chain().focus().insertContent(description).run();
          return;
        }

        // Check for template
        console.log("Checking for template with content:", content);
        const template = macroData.find(
          (macro) =>
            macro.type === "template" && macro.name.toLowerCase() === content
        );
        console.log("Found template:", template);

        if (template) {
          console.log("Handling template content");
          handleTemplateContent(template, !inTemplateEditing && !inNewReport);
          return;
        }

        // Check for dropdown macro
        console.log("Checking for dropdown macro with content:", content);
        const dropdownMacro = macroData.find(
          (macro) =>
            macro.type === "dropdown" && macro.name.toLowerCase() === content
        );
        console.log("Found dropdown macro:", dropdownMacro);

        if (dropdownMacro) {
          console.log("Handling dropdown macro");
          // If we're in template editing mode, handle it differently
          if (inTemplateEditing) {
            const handled = handleDropdownMacroInBrackets(editor, content);
            console.log(
              "Dropdown macro handling result in template mode:",
              handled
            );
            if (handled) {
              return;
            }
          } else {
            // In regular mode, just insert the macro name with colon
            console.log("Inserting dropdown macro name with colon");
            editor
              .chain()
              .focus()
              .insertContent(`${dropdownMacro.name}:`)
              .run();
            return;
          }
        }

        // Check for other macro types
        console.log("Checking for other macro types with content:", content);
        const macroInfo = getMacroInfo(editor, content, macroData);
        console.log("Found macro info:", macroInfo);

        if (macroInfo) {
          if (macroInfo.type === "template") {
            console.log("Handling template macro");
            handleTemplateContent(
              macroInfo,
              !inTemplateEditing && !inNewReport
            );
          } else if (macroInfo.type === "text") {
            const description =
              typeof macroInfo.description === "string"
                ? macroInfo.description
                : "";
            console.log(
              "Replacing content with text macro description:",
              description
            );
            replaceCurrentNodeContent(editor, description);
          }
          return;
        }

        // Handle regular text replacement
        console.log("Handling regular text replacement for:", command);
        handleTextReplacement(command, editor);
      },
    },
    {
      command: BACKSPACE_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        !showAIAssistant && editor && doBackspace(editor);
      },
    },
    {
      command: CAPS_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          capitalizeWordAtCursor(editor);
        }
      },
    },
    {
      command: SHOWAIASSISTANT_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        setShowAIAssistant(true);
        editor?.commands.focus();
        let textSelection = "";
        if (editor) {
          textSelection = getTextSelection(editor);
        }
        setSelectedText(textSelection);
        setAISuggestedText("");
        setDiffs("");
      },
    },
    {
      command: APPLY_CMD,
      callback: (cmd: { command: string }) => {
        if (!showAIAssistant && editor && aiSuggestedText && autoFixRange) {
          const { from, to } = autoFixRange;

          // Get the current content at the range before replacement
          const beforeContent = editor.state.doc.textBetween(from, to);

          // Clean up the content before inserting
          let cleanedContent = aiSuggestedText.trim();

          // Remove any trailing text that might be added by the AI
          const unwantedSuffixes = ["cond.", "cond", ".cond", ".cond."];
          for (const suffix of unwantedSuffixes) {
            if (cleanedContent.endsWith(suffix)) {
              cleanedContent = cleanedContent.slice(0, -suffix.length).trim();
            }
          }

          // Ensure proper sentence ending
          if (!cleanedContent.endsWith(".")) {
            cleanedContent += ".";
          }

          // Create a transaction to replace the content
          const tr = editor.state.tr;

          // Delete the old content
          tr.delete(from, to);

          // Insert the new content
          tr.insertText(cleanedContent, from);

          // Apply the transaction
          editor.view.dispatch(tr);

          // Set cursor position to the end of new content
          const newEnd = from + cleanedContent.length;
          window.setTimeout(() => {
            editor.commands.setTextSelection({ from: newEnd, to: newEnd });
            editor.commands.focus();
          }, 0);

          // Clean up UI elements
          setAutoFixAnchorEl(null);
          if (positionMarker && positionMarker.parentNode) {
            positionMarker.parentNode.removeChild(positionMarker);
            setPositionMarker(null);
          }
          return true;
        }
        return false;
      },
    },
    {
      command: REJECT_CMD,
      callback: (cmd: { command: string }) => {
        console.log("REJECT_CMD handler called with command:", cmd.command);
        // Dismiss the tooltip and remove position marker
        setAutoFixAnchorEl(null);
        if (positionMarker && positionMarker.parentNode) {
          positionMarker.parentNode.removeChild(positionMarker);
          setPositionMarker(null);
        }
        // Always return true to prevent any text replacement
        return true;
      },
    },
    {
      command: IMPRESSION_CMD,
      callback: (cmd: { command: string }) => {
        console.log("command: ", cmd.command);
        if (!showAIAssistant && editor) {
          const findings = getFindings(editor);
          if (findings) {
            onGenerateImpression(editor, findings);
          } else {
            setDuplicateMessage(NO_FINDINGS_MSG);
            setOpenDuplicateDialog(true);
            speak(NO_FINDINGS_MSG);
          }
        }
      },
    },
  ];

  const { listening, browserSupportsSpeechRecognition } = useSpeechRecognition({
    commands,
  });

  useEffect(() => {
    if (!browserSupportsSpeechRecognition) {
      handleVoiceError("Speech recognition is not supported in this browser");
    }
  }, [browserSupportsSpeechRecognition]);

  useEffect(() => {
    if (listening) {
      setVoiceStatus("listening");
    } else {
      setVoiceStatus("idle");
    }
  }, [listening]);

  const handleStartStopListening = () => {
    try {
      listening
        ? SpeechRecognition.stopListening()
        : SpeechRecognition.startListening({ continuous: true });
    } catch (error) {
      handleVoiceError(
        error instanceof Error
          ? error.message
          : "Failed to start/stop listening"
      );
    }
  };

  useEffect(() => {
    handleStartStopListening();
    return () => {
      SpeechRecognition.stopListening();
    };
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    if (!selectedItem) return;

    if (selectedItem.type === "template" || selectedItem.type === "report") {
      setNewName(selectedItem.name);

      if (!inNewReport) {
        setTimeout(() => {
          if (inTemplateEditing && selectedItem.type === "template") {
            const content = isJsonString(selectedItem.description)
              ? JSON.parse(selectedItem.description)
              : selectedItem.description;
            editor?.commands.setContent(content);
            moveCursorToFirstBracket(editor);
          } else {
            roEditor?.commands.setContent(
              isJsonString(selectedItem.description)
                ? JSON.parse(selectedItem.description)
                : selectedItem.description
            );
          }
        }, 0);
      }
    } else if (selectedItem.type === "other") {
      roEditor?.commands.setContent(selectedItem.description);
    }

    if (
      !inTemplateEditing &&
      selectedItem.type === "template" &&
      !inNewReport
    ) {
      editor?.commands.setContent("");
    }
  }, [selectedItem, inTemplateEditing, inNewReport, editor, roEditor]);

  useEffect(() => {
    if (editor && dropdownMacroInfo && dropdownMacroInfo.description) {
      console.log("Dropdown macro info changed:", dropdownMacroInfo);
      const { state, commands } = editor;
      const selection = state.selection;
      if (selection) {
        console.log("Current selection:", selection);
        // Replace the content of the current paragraph node with new content
        commands.insertContentAt(selection.from, dropdownMacroInfo.description);
        console.log("Inserted dropdown macro description");
      }
    }
  }, [dropdownMacroInfo]);

  // Save As dialog close handler
  const handleSaveAsDialogClose = () => {
    setOpenSaveAsDialog(false);
  };

  // Helper function to check for duplicate names
  const isDuplicateName = (
    name: string,
    inTemplateEditing: boolean,
    macroData: MacroType[],
    selectedItem: MacroType | null
  ): boolean => {
    return macroData.some(
      (item: MacroType) =>
        item.name === name &&
        ((inTemplateEditing &&
          item.type === "template" &&
          (!selectedItem || selectedItem.name !== name)) ||
          (!inTemplateEditing && item.type !== "template"))
    );
  };

  // Helper function to select radio button
  const selectRadioButton = (value: string) => {
    setTimeout(() => {
      const radio = document.querySelector(`input[value="${value}"]`);
      if (radio) {
        (radio as HTMLInputElement).click();
      }
    }, 0);
  };

  // Helper function to create a new template item
  const createTemplateItem = (
    id: string,
    name: string,
    jsonData: JSONContent
  ): MacroType => ({
    id,
    name,
    type: "template",
    description: jsonData as any,
  });

  // Helper function to create a new report item
  const createReportItem = (
    id: number,
    name: string,
    jsonData: JSONContent
  ): ReportType => ({
    id,
    name,
    type: "report",
    description: JSON.stringify(jsonData),
    owner: "User",
  });

  const handleSaveJson = async (
    name: string,
    inTemplateEditing: boolean,
    jsonData: JSONContent
  ) => {
    // Check for duplicate name
    if (isDuplicateName(name, inTemplateEditing, macroData, selectedItem)) {
      setDuplicateMessage(
        `A ${inTemplateEditing ? "template" : "report"} with the name "${name}" already exists.`
      );
      setOpenDuplicateDialog(true);
      return;
    }

    const urlLink = inTemplateEditing ? SAVE_JSON_TEMPLATE : SAVE_JSON_REPORT;
    const id = inTemplateEditing
      ? selectedItem?.type === "template" && selectedItem.name === name
        ? selectedItem.id
        : Date.now().toString()
      : Date.now();

    try {
      const response = await fetch(
        `${urlLink}?name=${encodeURIComponent(name)}&id=${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(jsonData),
        }
      );

      if (!response.ok && response.status !== 500) {
        throw new Error(
          `Failed to save ${inTemplateEditing ? "template" : "report"}`
        );
      }

      const result = await response.json();
      setReloadMacroData(true);
      setReloadReportData(true);

      if (inTemplateEditing) {
        setInTemplateEditing(false);
        const newItem = createTemplateItem(id.toString(), name, jsonData);

        if (selectedItem?.type === "template" && selectedItem.name === name) {
          setSelectedItem({ ...selectedItem, description: jsonData as any });
        } else {
          setSelectedItem(newItem);
        }

        selectRadioButton("template");
      } else {
        const newItem = createReportItem(id, name, jsonData);
        setNewlyAddedReport(newItem);
        setReloadReportData(true);
        selectRadioButton("report");
        setInNewReport(false);
        setSelectedItem(null);
        editor?.commands.setContent("");
      }

      setOpenSaveAsDialog(false);
    } catch (error) {
      console.error("Error saving JSON:", error);
      setDuplicateMessage(
        `Failed to save ${inTemplateEditing ? "template" : "report"}. Please try again.`
      );
      setOpenDuplicateDialog(true);
    }
  };

  const handleSaveAs = () => {
    const currentEditorContent: JSONContent | undefined = editor?.getJSON();
    if (currentEditorContent) {
      handleSaveJson(newName, inTemplateEditing, currentEditorContent);
    }
  };

  useEffect(() => {
    if (!selectedText) {
      setAISuggestedText("");
    }
  }, [selectedText]);

  // Helper function to get item properties consistently
  const getItemProperties = (item: any) => {
    return {
      type: item?.type || item?.row?.type,
      description: item?.description || item?.row?.description,
      name: item?.name || item?.row?.name,
    };
  };

  // Helper function to set editor content
  const setEditorContent = (content: any) => {
    if (!roEditor) return;

    if (typeof content === "string") {
      try {
        content = JSON.parse(content);
      } catch (e) {
        // If parsing fails, use the string as is
      }
    }
    roEditor.commands.setContent(content);
  };

  // Add this useEffect to handle setting the roEditor content
  useEffect(() => {
    if (!selectedItem) return;

    const { type, description } = getItemProperties(selectedItem);

    if (type === "report" || type === "template" || type === "text") {
      setEditorContent(description);
    }
  }, [selectedItem, roEditor]);

  // Render the bottom right portion
  const displayItem = () => {
    if (!selectedItem) return "";

    const { type, description } = getItemProperties(selectedItem);

    if (type === "dropdown") {
      return (
        <Box sx={{ height: `calc(100% - 60px)`, overflowY: "auto" }}>
          <Table stickyHeader sx={{ width: "100%" }}>
            <TableHead
              sx={{
                position: "sticky",
                backgroundColor: "#121212",
              }}
            >
              <TableRow>
                <TableCell
                  sx={{
                    font: "16px Roboto",
                    padding: "4px",
                    fontWeight: "bold",
                  }}
                >
                  #
                </TableCell>
                <TableCell
                  sx={{
                    font: "16px Roboto",
                    padding: "4px",
                    fontWeight: "bold",
                    minWidth: "150px",
                  }}
                >
                  Choice
                </TableCell>
                <TableCell
                  sx={{
                    font: "16px Roboto",
                    padding: "4px",
                    fontWeight: "bold",
                  }}
                >
                  Description
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {description.map((choice: DropdownDescription, index: number) => (
                <TableRow key={choice.id || index}>
                  <TableCell
                    sx={{
                      font: "16px Roboto",
                      padding: "4px",
                      verticalAlign: "top",
                    }}
                  >
                    {index + 1}
                  </TableCell>
                  <TableCell
                    sx={{
                      font: "16px Roboto",
                      padding: "4px",
                      fontWeight: "bold",
                      color: MACRO_TEXT_COLOR,
                      verticalAlign: "top",
                    }}
                  >
                    {choice.code}
                  </TableCell>
                  <TableCell
                    sx={{
                      font: "16px Roboto",
                      lineHeight: 1.5,
                      padding: "4px",
                      whiteSpace: "normal",
                    }}
                  >
                    {choice.description}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
      );
    }

    if (type === "template" || type === "report" || type === "text") {
      return (
        <Box
          sx={{ height: "100%", overflowY: "auto" }}
          onDoubleClick={() => {
            console.log("Template double-clicked:", {
              type,
              inNewReport,
              inTemplateEditing,
              templateName: selectedItem.name,
            });
            if (type === "template") {
              handleTemplateContent(selectedItem, !inNewReport);
            }
          }}
        >
          <EditorContent editor={roEditor} />
        </Box>
      );
    }

    return "";
  };

  const [diffs, setDiffs] = useState<any>();
  const dmp = new DiffMatchPatch();

  const [aiImpression, setAIImpression] = useState<string>("");

  // This is used in Floating AI Assistant
  const onAIGeneralInfo = async (
    voiceCommand: string,
    extraPrompt: string = ""
  ) => {
    if (inFetchData) {
      return;
    }

    await normalQuery({
      prompt: voiceCommand + "." + extraPrompt + ". Only include the content.",
      content: patInfo,
      setResult: setAIImpression,
      inFetchData,
      setInFetchData,
      endCallback: (content: string) => {
        setAIImpressionConversation([
          ...aiImpressionConversation,
          { isAI: false, content: voiceCommand },
          { isAI: true, content: content },
        ]);
        setAISuggestedText(content);
      },
    });
  };

  // This is used in Floating AI Assistant
  const onAIImpression = async (
    voiceCommand: string,
    inputPromptCommand: string = ""
  ) => {
    if (inFetchData) {
      return;
    }

    let voicePrompt = inputPromptCommand;
    if (!voicePrompt || !voicePrompt.trim()) {
      setInFetchData(false);
      setAIImpression("");
      return;
    }

    await normalQuery({
      prompt:
        voiceCommand +
        " this paragraph. Only return the result without including the findings.",
      content: voicePrompt,
      setResult: setAIImpression,
      inFetchData,
      setInFetchData,
      endCallback: (content: string) => {
        setAIImpressionConversation([
          ...aiImpressionConversation,
          { isAI: false, content: voiceCommand },
          { isAI: true, content: content },
        ]);
        setAISuggestedText(content);
      },
    });
  };

  const onRewrite = async (
    voiceCommand: string,
    inputPromptCommand: string = ""
  ) => {
    if (inFetchData) {
      return;
    }

    let voicePrompt = inputPromptCommand;
    if (!voicePrompt || !voicePrompt.trim()) {
      setInFetchData(false);
      setSelectedText("");
      setAISuggestedText("");
      setDiffs("");
      return;
    }

    await normalQuery({
      prompt: voiceCommand + " this paragraph. Only return the result.",
      content: voicePrompt,
      setResult: setAISuggestedText, //setRewriteContent,
      inFetchData,
      setInFetchData,
      endCallback: (content: string) => {
        console.log("content: ", content);
        // Compute the diff
        const tmpDiffs = dmp.diff_main(voicePrompt, content);
        dmp.diff_cleanupSemantic(tmpDiffs); // Clean up minor differences
        setDiffs(tmpDiffs);
        setInFetchData(false);
      },
    });
  };

  const [originalText, setOriginalText] = useState("");
  const [autoFixRange, setAutoFixRange] = useState<{
    from: number;
    to: number;
  } | null>(null);

  // Add this useEffect to handle cleanup
  useEffect(() => {
    if (!editor) return;

    const handleDocumentChange = () => {
      setAutoFixAnchorEl(null);
      if (positionMarker && positionMarker.parentNode) {
        positionMarker.parentNode.removeChild(positionMarker);
        setPositionMarker(null);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (autoFixAnchorEl) {
        handleDocumentChange();
      }
    };

    const handleMouseLeave = (event: MouseEvent) => {
      if (autoFixAnchorEl) {
        handleDocumentChange();
      }
    };

    // Listen for document changes (undo, redo, etc.)
    editor.on("transaction", ({ transaction }) => {
      if (transaction.docChanged) {
        handleDocumentChange();
      }
    });

    // Listen for keyboard input
    document.addEventListener("keydown", handleKeyDown);
    // Listen for mouse leave
    document.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      editor.off("transaction");
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, [editor, positionMarker, autoFixAnchorEl]);

  const [isAutoFixLoading, setIsAutoFixLoading] = useState(false);

  const onAutoFix = async (
    voiceCommand: string,
    inputPromptCommand: string = "",
    from?: number,
    to?: number,
    applyChanges: boolean = false
  ) => {
    if (inFetchData || !editor) {
      return;
    }

    let voicePrompt = inputPromptCommand;
    if (!voicePrompt || !voicePrompt.trim()) {
      setInFetchData(false);
      setSelectedText("");
      setAISuggestedText("");
      setDiffs([]);
      setOriginalText("");
      setAutoFixRange(null);
      setAutoFixAnchorEl(null);
      if (positionMarker && positionMarker.parentNode) {
        positionMarker.parentNode.removeChild(positionMarker);
        setPositionMarker(null);
      }
      return;
    }

    // Show loading indicator
    setIsAutoFixLoading(true);
    setAutoFixAnchorEl(document.body);

    // Get the current selection range if not provided
    if (from === undefined || to === undefined) {
      const { from: selFrom, to: selTo } = editor.state.selection;
      from = selFrom;
      to = selTo;
    }

    // Get the actual text content from the range
    const selectedText = editor.state.doc.textBetween(from, to);
    if (!selectedText.trim()) {
      setIsAutoFixLoading(false);
      setAutoFixAnchorEl(null);
      return;
    }

    // Check if the selection spans multiple nodes
    const startNode = editor.state.doc.resolve(from).parent;
    const endNode = editor.state.doc.resolve(to).parent;
    if (startNode !== endNode) {
      setIsAutoFixLoading(false);
      setAutoFixAnchorEl(null);
      setDuplicateMessage(
        "Auto-fix is not supported for selections that span multiple paragraphs. Please select text within a single paragraph."
      );
      setOpenDuplicateDialog(true);
      return;
    }

    // Store the range and original text
    setAutoFixRange({ from, to });
    setOriginalText(selectedText);

    await normalQuery({
      prompt:
        voiceCommand +
        " this text. Return ONLY the corrected text without any prefix, explanation, or additional text. Do not include 'Here's the corrected text:' or similar phrases.",
      content: selectedText,
      setResult: setAISuggestedText,
      inFetchData,
      setInFetchData,
      endCallback: (content: string) => {
        // Clean up the AI's response
        let cleanedContent = content.trim();

        // Remove any trailing text that might be added by the AI
        const unwantedSuffixes = ["cond.", "cond", ".cond", ".cond."];
        for (const suffix of unwantedSuffixes) {
          if (cleanedContent.endsWith(suffix)) {
            cleanedContent = cleanedContent.slice(0, -suffix.length).trim();
          }
        }

        // Ensure proper sentence ending
        if (!cleanedContent.endsWith(".")) {
          cleanedContent += ".";
        }

        // Set the cleaned content
        setAISuggestedText(cleanedContent);

        // Compute the diff using the cleaned content
        const tmpDiffs = dmp.diff_main(selectedText, cleanedContent);
        dmp.diff_cleanupSemantic(tmpDiffs);
        setDiffs(tmpDiffs);

        // Show tooltip at cursor position
        const selection = editor.state.selection;
        if (selection) {
          const pos = selection.$anchor.pos;
          const { node, offset } = editor.view.domAtPos(pos);

          // Find the closest text node or paragraph
          let targetNode = node;
          if (targetNode.nodeType === Node.TEXT_NODE) {
            targetNode = targetNode.parentNode as HTMLElement;
          }

          if (targetNode instanceof HTMLElement) {
            // Create a temporary span to get accurate positioning
            const tempSpan = document.createElement("span");
            tempSpan.style.position = "absolute";
            tempSpan.style.visibility = "hidden";
            targetNode.appendChild(tempSpan);

            // Get the position of the text node
            const rect = tempSpan.getBoundingClientRect();
            targetNode.removeChild(tempSpan);

            // Create a new element at the exact position
            const newPositionMarker = document.createElement("div");
            newPositionMarker.style.position = "absolute";
            newPositionMarker.style.left = `${rect.left}px`;
            newPositionMarker.style.top = `${rect.top}px`;
            newPositionMarker.style.width = "1px";
            newPositionMarker.style.height = "1px";
            document.body.appendChild(newPositionMarker);

            // Clean up any existing marker
            if (positionMarker && positionMarker.parentNode) {
              positionMarker.parentNode.removeChild(positionMarker);
            }

            setPositionMarker(newPositionMarker);
            setAutoFixAnchorEl(newPositionMarker);
          }
        }
        setIsAutoFixLoading(false);
        setInFetchData(false);
      },
    });
  };

  const [generating, setGenerating] = useState<string>("");
  const [impression, setImpression] = useState<string>("");
  const onGenerateImpression = async (editor: Editor, findings: string) => {
    console.log(findings);
    if (!findings) {
      return;
    }
    setImpression("");

    if (inFetchData) {
      return;
    }

    setInFetchData(true);
    setGenerating("generating ...");

    // Create a temporary div for the loading state
    const loadingDiv = document.createElement("div");
    loadingDiv.style.position = "fixed";
    loadingDiv.style.top = "50%";
    loadingDiv.style.left = "50%";
    loadingDiv.style.transform = "translate(-50%, -50%)";
    document.body.appendChild(loadingDiv);
    setImpressionLoadingEl(loadingDiv);

    await normalQuery({
      prompt: "Generate a concise radiology impression",
      content: findings + " **Radiology Impression:**",
      setResult: setImpression,
      inFetchData,
      setInFetchData,
      endCallback: (content: string) => {
        // Clean up the AI's response
        let tmpImpression = content
          .replace("**Radiology Impression:**", "")
          .replace("Here is a concise radiology impression:", "")
          .replace(/\[.*?\]/g, "") // Remove any [insert ...] placeholders
          .replace(/\n\s*\n/g, "\n") // Replace multiple newlines with single newline
          .trim();

        setImpression(tmpImpression);
        setGenerating(""); // Clear the progress box
        setImpressionLoadingEl(null); // Remove the progress box

        // Clean up the temporary div
        if (loadingDiv.parentNode) {
          loadingDiv.parentNode.removeChild(loadingDiv);
        }

        if (editor) {
          handleUpdateImpression(editor, tmpImpression);
          chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
        }
      },
    });
  };

  const synth = window.speechSynthesis;
  const synthVoices = window.speechSynthesis.getVoices();
  const speak = (text: string) => {
    let utterance = new SpeechSynthesisUtterance(text);
    utterance.voice = synthVoices[0];
    utterance.pitch = 1.2;
    utterance.rate = 1.1;
    synth.speak(utterance);
  };

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const encodedData = params.get("data");

    if (encodedData) {
      try {
        const jsonData = JSON.parse(decodeURIComponent(encodedData));
        console.log("Received JSON:", jsonData);
      } catch (error) {
        console.error("Error parsing JSON:", error);
      }
    }
  }, []);

  const [bottomPanelHeight, setBottomPanelHeight] = useState(350);
  const [bottomLeftWidth, setBottomLeftWidth] = useState("65%");
  const minHeight = 100;
  const maxHeight = 1000;
  const minWidth = 200;
  const maxWidth = 1200;

  const handleVerticalResize = (e: React.MouseEvent) => {
    const startY = e.clientY;
    const startHeight = bottomPanelHeight;

    const onMouseMove = (e: MouseEvent) => {
      const newHeight = startHeight + (startY - e.clientY);
      setBottomPanelHeight(Math.min(maxHeight, Math.max(minHeight, newHeight)));
    };

    const onMouseUp = () => {
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    };

    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
  };

  const handleHorizontalResize = (e: React.MouseEvent) => {
    const startX = e.clientX;
    const container = e.currentTarget.parentElement;
    if (!container) return;
    const startWidth = container.getBoundingClientRect().width;
    const startLeftWidth =
      container.firstElementChild?.getBoundingClientRect().width || 0;

    const onMouseMove = (e: MouseEvent) => {
      const newLeftWidth = startLeftWidth + (e.clientX - startX);
      const containerWidth = container.getBoundingClientRect().width;
      const percentage = (newLeftWidth / containerWidth) * 100;
      const clampedPercentage = Math.min(80, Math.max(20, percentage));
      setBottomLeftWidth(`${clampedPercentage}%`);
    };

    const onMouseUp = () => {
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    };

    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
  };

  const handleVoiceError = (error: string) => {
    setVoiceStatus("error");
    setVoiceError(error);
    setTimeout(() => {
      setVoiceStatus("idle");
      setVoiceError("");
    }, 3000);
  };

  // Add the pending command state
  const [pendingCommand, setPendingCommand] = useState<{
    command: string;
    callback: Function;
  } | null>(null);

  // Initialize bottomLeftWidth only once when the component mounts
  useEffect(() => {
    setBottomLeftWidth("65%");
  }, []); // Empty dependency array means this only runs once on mount

  const handleRadioButtonChange = (value: string) => {
    // Don't reset the width when changing radio buttons
    const radio = document.querySelector(`input[value="${value}"]`);
    if (radio) {
      (radio as HTMLInputElement).click();
    }
  };

  const handleHelpDialogClose = () => {
    setOpenHelpDialog(false);
  };

  const handleSaveTemplate = async () => {
    if (editor && selectedItem?.type === "template") {
      const currentContent = editor.getJSON();

      // Check if this is a rad template (has templateID property)
      if (selectedItem.templateID) {
        try {
          const description = JSON.stringify(currentContent);
          const staffId = "global"; // Use the same hardcoded staff ID as in other rad template operations

          await RadTemplateAPI.updateRadTemplate(
            staffId,
            selectedItem.templateID,
            {
              type: "rad-template",
              name: selectedItem.name,
              description,
              owner: selectedItem.owner || "global",
            }
          );

          console.log("Rad template updated successfully");

          // Trigger refresh of rad templates in ReportInfo component
          setReloadRadTemplates(true);

          // Update the selected item with the new content
          setSelectedItem({
            ...selectedItem,
            description: currentContent as any,
          });

          // Close the editor after successful save
          setInTemplateEditing(false);
          editor?.commands.setContent("");
        } catch (error) {
          console.error("Error updating rad template:", error);
          alert(
            `Failed to update rad template: ${error instanceof Error ? error.message : "Unknown error"}`
          );
        }
      } else {
        // Fall back to the existing logic for non-rad templates
        handleSaveJson(selectedItem.name, true, currentContent);
      }
    }
  };

  const handleRefresh = () => {
    // Reset all relevant state
    setSelectedItem(null);
    setSearchText("");
    setNewName("");
    setAISuggestedText("");
    setDiffs("");
    setInTemplateEditing(false);
    setInNewReport(false);

    // Clear editor content and reset cursor
    if (editor) {
      editor.commands.clearContent();
      editor.commands.focus();
    }

    // Close the dialog
    setOpenRefreshDialog(false);
  };

  const handleRefreshDialogClose = () => {
    setOpenRefreshDialog(false);
  };

  const handleDuplicateDialogClose = () => {
    setOpenDuplicateDialog(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Tab" && editor) {
      event.preventDefault();
      if (event.shiftKey) {
        handleShiftTab(editor);
      } else {
        handleTab(editor);
      }
    }
  };

  const handlePickMacro = (macroInfo: MacroInfo) => {
    if (!editor) return;

    if (macroInfo.type === "dropdown" && Array.isArray(macroInfo.description)) {
      const choice = macroInfo.description[0];
      if (choice && typeof choice === "object" && "text" in choice) {
        editor.chain().focus().insertContent(choice.text).run();
      }
    } else if (typeof macroInfo.description === "string") {
      editor.chain().focus().insertContent(macroInfo.description).run();
    } else if (
      macroInfo.description &&
      typeof macroInfo.description === "object"
    ) {
      editor.chain().focus().insertContent(macroInfo.description).run();
    }
  };

  const handleCloseConfirmDialogClose = () => {
    setOpenCloseConfirmDialog(false);
  };

  // Rad template handlers
  const handleSaveAsRadTemplate = () => {
    setOpenRadTemplateDialog(true);
  };

  const handleSaveRadTemplate = async (templateData: {
    name: string;
    owner: string;
  }) => {
    if (!editor) return;

    setIsSavingRadTemplate(true);
    try {
      const currentContent = editor.getJSON();
      const description = JSON.stringify(currentContent);

      // Use hardcoded staff ID 'global' as per the pattern in ReportInfo.tsx
      const staffId = "global";

      await RadTemplateAPI.createRadTemplate(staffId, {
        type: "rad-template",
        name: templateData.name,
        description,
        owner: templateData.owner,
      });

      // Show success message or handle success
      console.log("Rad template saved successfully");

      // Trigger refresh of rad templates in ReportInfo component
      setReloadRadTemplates(true);

      // Close dialog
      setOpenRadTemplateDialog(false);
    } catch (error) {
      console.error("Error saving rad template:", error);
      // Show error message to user
      alert(
        `Failed to save rad template: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setIsSavingRadTemplate(false);
    }
  };

  const handleRadTemplateDialogClose = () => {
    setOpenRadTemplateDialog(false);
  };

  const handleClose = () => {
    if (inTemplateEditing || inNewReport) {
      setOpenCloseConfirmDialog(true);
    } else {
      setSelectedItem(null);
      editor?.commands.setContent("");
    }
  };

  const handleConfirmClose = () => {
    if (inTemplateEditing) {
      setInTemplateEditing(false);
    } else {
      setInNewReport(false);
    }
    setSelectedItem(null);
    editor?.commands.setContent("");
    setOpenCloseConfirmDialog(false);
  };

  const commandCallbacks = {
    // ... existing code ...
    [NEXT_CMD]: {
      command: NEXT_CMD,
      callback: () => {
        !showAIAssistant && editor && handleTab(editor);
      },
    },
    [PREVIOUS_CMD]: {
      command: PREVIOUS_CMD,
      callback: () => {
        !showAIAssistant && editor && handleShiftTab(editor);
      },
    },
    // ... existing code ...
  };

  // Helper function to handle template content
  const handleTemplateContent = (
    template: any,
    shouldEnableEditing: boolean
  ) => {
    const templateContent =
      typeof template.description === "string"
        ? isJsonString(template.description)
          ? JSON.parse(template.description)
          : template.description
        : template.description;

    // If we're in report editing mode, just copy the content
    if (inNewReport) {
      editor?.chain().focus().insertContent(templateContent).run();
      return;
    }

    // Only enable template editing if not in report mode
    if (shouldEnableEditing) {
      setInTemplateEditing(true);
      setSelectedItem(template);
      handleRadioButtonChange("template");
    }

    // Ensure editor is ready before setting content
    if (editor) {
      editor.chain().setContent(templateContent).focus().run();
    }
  };

  // Add useEffect to handle cursor position when inTemplateEditing or inNewReport changes
  useEffect(() => {
    // Only handle cursor positioning if we're actually changing modes
    if (
      (inTemplateEditing || inNewReport) &&
      editor &&
      selectedItem &&
      (selectedItem.type === "template" || selectedItem.type === "report")
    ) {
      // First clear any existing content
      editor.commands.clearContent();

      // Then set the new content
      editor.commands.setContent(selectedItem.description);

      // Force cursor to start and prevent any automatic movement
      requestAnimationFrame(() => {
        editor.commands.focus();
        editor.commands.setTextSelection(0);

        // Add a small delay to ensure cursor stays at start
        setTimeout(() => {
          editor.commands.setTextSelection(0);

          // Scroll to the cursor position after content is fully rendered
          setTimeout(() => {
            // Get the editor's DOM element
            const editorElement = editor.view.dom;
            if (editorElement) {
              // Find the first text node or paragraph
              const firstNode = editorElement.querySelector(
                'p, [data-type="paragraph"]'
              );
              if (firstNode) {
                firstNode.scrollIntoView({
                  behavior: "smooth",
                  block: "start",
                });
              } else {
                // Fallback to scrolling the editor itself
                editorElement.scrollTo({
                  top: 0,
                  behavior: "smooth",
                });
              }
            }
          }, 100);
        }, 50);
      });
    }
  }, [inTemplateEditing, inNewReport, editor, selectedItem]);

  // Add logging to the double-click handler
  const handleDoubleClick = () => {
    if (selectedItem?.type === "template") {
      handleTemplateContent(selectedItem, !inNewReport);
    }
  };

  // Add this function to handle template copying
  const handleTemplateCopy = (template: any) => {
    if (!editor || !inNewReport) return;

    const templateContent =
      typeof template.description === "string"
        ? isJsonString(template.description)
          ? JSON.parse(template.description)
          : template.description
        : template.description;

    // Clear any existing content first
    editor.commands.clearContent();

    // Set the new content and focus the editor
    editor.chain().setContent(templateContent).focus().run();

    // Ensure cursor is visible
    requestAnimationFrame(() => {
      // Scroll to the cursor position after content is fully rendered
      setTimeout(() => {
        const editorElement = editor.view.dom;
        if (editorElement) {
          const firstNode = editorElement.querySelector(
            'p, [data-type="paragraph"]'
          );
          if (firstNode) {
            firstNode.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          } else {
            editorElement.scrollTo({
              top: 0,
              behavior: "smooth",
            });
          }
        }
      }, 100);
    });
  };

  // Helper function to handle text replacement
  const handleTextReplacement = (command: string, editor: Editor) => {
    const updatedContent = formatText(processCommands(command));
    const { from, to } = editor.state.selection;

    // Get surrounding text for context
    const textAfter = editor.state.doc.textBetween(to, to + 1);
    const textBefore = editor.state.doc.textBetween(0, from);

    // Define special characters and sentence boundaries
    const SENTENCE_ENDINGS = [".", "!", "?", "\n"];
    const NO_SPACE_AFTER = [":", "[", "]", " "];

    // Determine if we should capitalize based on sentence position
    const lastSentenceEnd = Math.max(
      ...SENTENCE_ENDINGS.map((char) => textBefore.lastIndexOf(char))
    );
    const isStartOfSentence =
      from === 0 ||
      (lastSentenceEnd !== -1 &&
        textBefore.slice(lastSentenceEnd + 1).trim() === "");

    // Apply capitalization if needed
    const finalContent = !isStartOfSentence
      ? updatedContent.charAt(0).toLowerCase() + updatedContent.slice(1)
      : updatedContent;

    // Replace the text
    editor.chain().deleteRange({ from, to }).insertContent(finalContent).run();

    // Add space if needed (not after special characters or colons)
    const needsSpace =
      textAfter &&
      !NO_SPACE_AFTER.includes(textAfter) &&
      !finalContent.endsWith(":");

    if (needsSpace) {
      editor.chain().insertContent(" ").run();
    }
  };

  // Helper function to handle dropdown macro in brackets
  const handleDropdownMacroInBrackets = (editor: Editor, content: string) => {
    console.log("handleDropdownMacroInBrackets called with content:", content);
    const bracketField = findCurrentBracketField(editor);
    console.log("Current bracket field:", bracketField);

    if (bracketField) {
      const dropdownMacro = macroData.find(
        (macro) =>
          macro.type === "dropdown" && macro.name.toLowerCase() === content
      );
      console.log("Found dropdown macro for content:", dropdownMacro);

      if (dropdownMacro) {
        editor.chain().focus().insertContent(`${dropdownMacro.name}:`).run();
        console.log("Inserted dropdown macro name with colon");
      } else {
        editor.chain().focus().insertContent(content).run();
        console.log("Inserted regular content");
      }
      return true;
    }
    return false;
  };

  // Add this useEffect to handle cursor movement when entering report editing mode
  useEffect(() => {
    if (inNewReport && editor) {
      moveCursorToFirstBracket(editor);
    }
  }, [inNewReport, editor]);

  const handleAutoFixReject = useCallback(() => {
    setAutoFixAnchorEl(null);
    // Don't remove position marker to keep the highlight
  }, []);

  const handleAutoFixAccept = useCallback(
    (event?: React.SyntheticEvent) => {
      if (editor && aiSuggestedText && autoFixRange) {
        const { from, to } = autoFixRange;

        // Replace the entire selection with the new text
        editor
          .chain()
          .focus()
          .setTextSelection({ from, to })
          .deleteSelection()
          .insertContent(aiSuggestedText.trim())
          .run();

        // Dismiss the tooltip
        setAutoFixAnchorEl(null);
        if (positionMarker && positionMarker.parentNode) {
          positionMarker.parentNode.removeChild(positionMarker);
          setPositionMarker(null);
        }
      }
    },
    [editor, aiSuggestedText, autoFixRange, positionMarker]
  );

  // Add useEffect to track editor state
  useEffect(() => {
    // No need for console logs here
  }, [editor]);

  // Add useEffect to track template editing state
  useEffect(() => {
    // No need for console logs here
  }, [inTemplateEditing, selectedItem, inNewReport]);

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box
        sx={{
          height: "calc(100vh - 40px)",
          minHeight: "600px",
          display: "flex",
          flexDirection: "column",
          backgroundColor: "background.default",
          color: "text.primary",
        }}
      >
        {/* Top panel: Toolbar */}
        <Box
          sx={{
            pl: 1,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            position: "relative",
            zIndex: 10,
            boxShadow: 1,
            backgroundColor: BACKGROUND_TITLE_COLOR,
            flexShrink: 0,
          }}
        >
          <Box sx={{ display: "flex", gap: 1 }}>
            <ButtonGroup
              variant="contained"
              sx={{
                "& .MuiButton-root": {
                  whiteSpace: "nowrap",
                  minWidth: "120px",
                  height: "36px",
                },
              }}
            >
              {/* Removed Save Report and Sign Report buttons */}
            </ButtonGroup>
          </Box>
          <Box sx={{ display: "flex" }}>
            {!inTemplateEditing && !inNewReport ? (
              <>
                <Tooltip title="New Report" arrow>
                  <span>
                    <IconButton
                      color="primary"
                      onClick={() => {
                        setInNewReport(true);
                        editor?.commands.setContent("");
                        editor?.commands.focus("start");
                        setSelectedItem(null);
                        setNewName("");
                      }}
                      sx={iconSize}
                    >
                      <Description sx={iconSize} />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title="New Template" arrow>
                  <span>
                    <IconButton
                      color="primary"
                      onClick={() => {
                        setInTemplateEditing(!inTemplateEditing);
                        editor?.commands.setContent("");
                        editor?.commands.focus("start");
                        setSelectedItem(null);
                        setNewName("");
                        const templateRadio = document.querySelector(
                          'input[value="template"]'
                        );
                        if (templateRadio) {
                          (templateRadio as HTMLInputElement).click();
                        }
                      }}
                      sx={iconSize}
                    >
                      <TbTemplate />
                    </IconButton>
                  </span>
                </Tooltip>
              </>
            ) : null}
            <Divider
              orientation="vertical"
              flexItem
              sx={{ mx: 0.5, my: 0.5 }}
            />
            <Tooltip title="New Macro" arrow>
              <span>
                <IconButton
                  color="primary"
                  onClick={() => {
                    setFormValues({
                      name: "",
                      description: "",
                    });
                    setNewChoiceName("");
                    setNewCodeDescriptionPairs([{ code: "", description: "" }]);
                    setOpenConfigDialog(true);
                  }}
                  sx={iconSize}
                >
                  <TbMacro />
                </IconButton>
              </span>
            </Tooltip>
            {/* <Divider orientation="vertical" flexItem sx={{ mx: 0.5, my: 0.5 }} /> */}
            <Tooltip
              title={
                showAIAssistant ? "Hide AI Assistant" : "Show AI Assistant"
              }
              arrow
            >
              <span>
                <IconButton
                  color="primary"
                  onClick={() => {
                    setShowAIAssistant(!showAIAssistant);
                    editor?.commands.focus();
                    let textSelection = "";
                    if (editor) {
                      textSelection = getTextSelection(editor);
                    }
                    setSelectedText(textSelection);
                    setAISuggestedText("");
                    setDiffs("");
                  }}
                  sx={{ display: "none" }}
                >
                  {showAIAssistant ? (
                    <SpeakerNotesOff sx={iconSize} />
                  ) : (
                    <SpeakerNotes sx={iconSize} />
                  )}
                </IconButton>
              </span>
            </Tooltip>
            <Divider
              orientation="vertical"
              flexItem
              sx={{ mx: 0.5, my: 0.5 }}
            />
            <Tooltip title="Refresh" arrow>
              <span>
                <IconButton
                  color="primary"
                  onClick={() => setOpenRefreshDialog(true)}
                  disabled={!enableSaveAs}
                >
                  <Refresh sx={iconSize} />
                </IconButton>
              </span>
            </Tooltip>
            <Divider
              orientation="vertical"
              flexItem
              sx={{ mx: 0.5, my: 0.5 }}
            />
            {enableSaveAs || inNewReport ? (
              <>
                <Tooltip title="Save Template">
                  <span>
                    <IconButton
                      color="primary"
                      onClick={handleSaveTemplate}
                      disabled={
                        !inTemplateEditing || selectedItem?.type !== "template"
                      }
                      sx={iconSize}
                    >
                      <Save sx={iconSize} />
                    </IconButton>
                  </span>
                </Tooltip>
                <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
                <Tooltip
                  title={
                    inTemplateEditing ? "Save As Template" : "Save As Report"
                  }
                >
                  <span>
                    <IconButton
                      color="primary"
                      onClick={
                        inTemplateEditing
                          ? handleSaveAsRadTemplate
                          : () => setOpenSaveAsDialog(true)
                      }
                      disabled={!inTemplateEditing && !inNewReport}
                      sx={{ ...iconSize }}
                    >
                      <SaveAs sx={{ ...iconSize }} />
                    </IconButton>
                  </span>
                </Tooltip>
              </>
            ) : (
              <>
                <Tooltip title="Save Template">
                  <span>
                    <IconButton color="primary" disabled sx={iconSize}>
                      <Save sx={iconSize} />
                    </IconButton>
                  </span>
                </Tooltip>
                <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
                <Tooltip title="Save As Template">
                  <span>
                    <IconButton color="primary" disabled sx={iconSize}>
                      <SaveAs sx={iconSize} />
                    </IconButton>
                  </span>
                </Tooltip>
              </>
            )}
            <Divider
              orientation="vertical"
              flexItem
              sx={{ mx: 0.5, my: 0.5 }}
            />
            <Tooltip title="Voice" arrow>
              <span>
                {listening ? (
                  <IconButton
                    onClick={handleStartStopListening}
                    sx={{ color: ACTIVE_COLOR }}
                  >
                    <SettingsVoice sx={iconSize} />
                  </IconButton>
                ) : (
                  <IconButton
                    color="primary"
                    onClick={handleStartStopListening}
                  >
                    <MicNone sx={iconSize} />
                  </IconButton>
                )}
              </span>
            </Tooltip>
            <Divider
              orientation="vertical"
              flexItem
              sx={{ mx: 0.5, my: 0.5 }}
            />
            <Tooltip title="Voice Commands Help" arrow>
              <span>
                <IconButton
                  color="primary"
                  onClick={() => setOpenHelpDialog(true)}
                >
                  <Help sx={iconSize} />
                </IconButton>
              </span>
            </Tooltip>
          </Box>
        </Box>

        {/* Middle panel: Editor */}
        <Box
          sx={{
            flex: 1,
            borderRadius: 1,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              px: 1,
              flexShrink: 0,
              height: 48,
            }}
          >
            {inTemplateEditing || inNewReport ? (
              <>
                <Typography
                  sx={{
                    font: "16px Roboto",
                    fontWeight: "bold",
                    color: TITLE_COLOR,
                  }}
                >
                  {inTemplateEditing
                    ? selectedItem?.type === "template"
                      ? `${selectedItem.name} (edit)`
                      : "New Template"
                    : "New Report"}
                </Typography>
                <Tooltip
                  title={
                    inTemplateEditing
                      ? "Close Template Editing"
                      : "Close New Report"
                  }
                  arrow
                >
                  <IconButton onClick={handleClose}>
                    <Close sx={iconSize} />
                  </IconButton>
                </Tooltip>
              </>
            ) : null}
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              flex: 1,
              overflowY: "auto",
              padding: 1,
              m: 0,
              cursor: "text",
              minHeight: 0,
            }}
            onClick={(e) => {
              e.stopPropagation();
              editor?.commands.focus();
            }}
          >
            <Box sx={{ flex: 1, minHeight: 0 }}>
              <EditorContent editor={editor} />
            </Box>
            <Box ref={chatEndRef}></Box>
          </Box>
          {showAIAssistant ? <FloatingAIAssistant /> : ""}
        </Box>

        {/* Vertical Resizer */}
        <Box
          height={5}
          sx={{ cursor: "row-resize", backgroundColor: BORDER_COLOR }}
          onMouseDown={handleVerticalResize}
        />

        {/* Resizable bottom panel: (template/report on the left and description on the right) */}
        <Box
          sx={{
            display: "flex",
            px: 0.5,
            height: `${bottomPanelHeight}px`,
          }}
        >
          <Box
            sx={{
              overflow: "auto",
              borderRadius: 1,
              p: 0.5,
              pt: 0,
              backgroundColor: BACKGROUND_COLOR,
              position: "relative",
              height: `calc(100%)`,
              width: bottomLeftWidth,
            }}
          >
            <ReportInfo
              editor={editor}
              newlyAddedReport={newlyAddedReport}
              inNewReport={inNewReport}
            />
          </Box>

          <Box
            width={5}
            sx={{ cursor: "col-resize", backgroundColor: BORDER_COLOR }}
            onMouseDown={handleHorizontalResize}
          />

          <Box
            sx={{
              flex: 1,
              minHeight: 0,
              borderLeft: `1px solid ${BORDER_COLOR}`,
              borderRadius: 1,
              p: 1,
              pt: 0,
              overflowY: "auto",
              overflowX: "hidden",
            }}
          >
            {selectedItem || inNewReport ? (
              <>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    sx={{
                      position: "sticky",
                      top: 0,
                      backgroundColor: BACKGROUND_COLOR,
                      zIndex: 1000,
                      lineHeight: 2,
                      mt: 1,
                      mb: 2,
                    }}
                  >
                    <span
                      style={{
                        color: TITLE_COLOR,
                        fontWeight: "bold",
                        fontSize: "18px",
                      }}
                    >
                      {inNewReport
                        ? "New Report"
                        : selectedItem?.row?.name || selectedItem?.name || ""}
                    </span>
                  </Typography>
                </Box>
                {displayItem()}
              </>
            ) : (
              <Box sx={{ p: 1 }}>
                <Typography variant="body1" color="text.secondary">
                  Select an item to view details
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      <SaveRadTemplateDialog
        open={openRadTemplateDialog}
        onClose={handleRadTemplateDialogClose}
        onSave={handleSaveRadTemplate}
        initialName={newName}
        isLoading={isSavingRadTemplate}
      />
      <Dialog
        open={openSaveAsDialog}
        onClose={handleSaveAsDialogClose}
        sx={{
          "& .MuiDialog-paper": {
            width: "33%",
          },
        }}
      >
        <DialogTitle>
          {"Save " + (inTemplateEditing ? "Template" : "Report")}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label={(inTemplateEditing ? "Template" : "Report") + " Name"}
            type="text"
            fullWidth
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            error={newName.includes("/")}
            helperText={
              newName.includes("/")
                ? `${inTemplateEditing ? "Template" : "Report"} name cannot contain slashes (/)`
                : ""
            }
            FormHelperTextProps={{
              sx: {
                fontSize: "0.875rem",
                marginTop: 0.5,
                color: newName.includes("/") ? "error.main" : "text.secondary",
              },
            }}
            onFocus={() => setNewName("")}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleSaveAsDialogClose} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSaveAs}
            color="primary"
            disabled={!newName || newName.includes("/")}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      <MacroConfig />
      <FloatingReport />
      <Dialog
        open={!!pendingCommand}
        onClose={() => setPendingCommand(null)}
        className="command-confirmation-dialog"
      >
        <DialogTitle>Confirm Command</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to execute: "{pendingCommand?.command}"?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPendingCommand(null)}>Cancel</Button>
          <Button
            onClick={() => {
              pendingCommand?.callback();
              setPendingCommand(null);
            }}
            color="primary"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      {/* Voice Commands Help Dialog */}
      <Dialog
        open={openHelpDialog}
        onClose={handleHelpDialogClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Voice Commands</DialogTitle>
        <DialogContent>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            {/* Navigation Commands */}
            <Typography variant="h6" sx={{ color: "#2196F3" }}>
              Navigation Commands
            </Typography>
            <Box
              sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 2 }}
            >
              <Typography>
                <CommandText>{NEXT_CMD}</CommandText> - Move to next field
              </Typography>
              <Typography>
                <CommandText>{PREVIOUS_CMD}</CommandText> - Move to previous
                field
              </Typography>
              <Typography>
                <CommandText>{BEGIN_CMD}</CommandText> - Go to beginning of
                document
              </Typography>
              <Typography>
                <CommandText>{END_CMD}</CommandText> - Go to end of document
              </Typography>
              <Typography>
                <CommandText>{BEGINLINE_CMD}</CommandText> - Go to beginning of
                line
              </Typography>
              <Typography>
                <CommandText>{ENDLINE_CMD}</CommandText> - Go to end of line
              </Typography>
              <Typography>
                <CommandText>go to [label]</CommandText> - Move cursor to after
                the colon of the specified label, or inside brackets if present
              </Typography>
            </Box>

            {/* Macro Management Commands */}
            <Typography variant="h6" sx={{ color: "#2196F3" }}>
              Macro Management Commands
            </Typography>
            <Box
              sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 2 }}
            >
              <Typography>
                <CommandText>{MACRO_CMD}</CommandText> - Create a new macro from
                selected text
              </Typography>
            </Box>

            {/* Editing & Content Commands */}
            <Typography variant="h6" sx={{ color: "#2196F3" }}>
              Editing & Content Commands
            </Typography>
            <Box
              sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 2 }}
            >
              <Typography>
                <CommandText>{UNDO_CMD}</CommandText> - Undo last action
              </Typography>
              <Typography>
                <CommandText>{REDO_CMD}</CommandText> - Redo last action
              </Typography>
              <Typography>
                <CommandText>{SELECT_CMD}</CommandText> - Select word at cursor
                position
              </Typography>
              <Typography>
                <CommandText>{UNSELECT_CMD}</CommandText> - Clear selection
              </Typography>
              <Typography>
                <CommandText>{DELETE_CMD}</CommandText> - Delete selected
                content
              </Typography>
              <Typography>
                <CommandText>{BACKSPACE_CMD}</CommandText> - Delete previous
                character
              </Typography>
              <Typography>
                <CommandText>{STARTNUMBER_CMD}</CommandText> - Start numbered
                list
              </Typography>
              <Typography>
                <CommandText>{STOPNUMBER_CMD}</CommandText> - Stop numbered list
              </Typography>
              <Typography>
                <CommandText>{TODAY_CMD}</CommandText> - Insert today's date
              </Typography>
              <Typography>
                <CommandText>{YESTERDAY_CMD}</CommandText> - Insert yesterday's
                date
              </Typography>
              <Typography>
                <CommandText>{PICKMACRO_CMD}</CommandText> - Insert a text macro
                by name or a choice from a dropdown macro
              </Typography>
              <Typography>
                <CommandText>{CAPS_CMD}</CommandText> - Capitalize selected text
              </Typography>
              <Typography>
                <CommandText>[text macro name]</CommandText> - Insert a text
                macro's content
              </Typography>
              <Typography>
                <CommandText>[template name]</CommandText> - Open and edit a
                template
              </Typography>
              <Typography>
                <CommandText>[dropdown macro name]</CommandText> - In template
                edit mode, add a dropdown macro name with colon inside brackets
              </Typography>
            </Box>

            {/* AI Assistant Commands */}
            <Typography variant="h6" sx={{ color: "#2196F3" }}>
              AI Assistant Commands
            </Typography>
            <Box
              sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 2 }}
            >
              <Typography>
                <CommandText>generate impression</CommandText> - Generate
                impression
              </Typography>
              <Typography>
                <CommandText>make impression</CommandText> - Generate impression
              </Typography>
              <Typography>
                <CommandText>auto fix</CommandText> - Fix grammar and formatting
                of text inside brackets
              </Typography>
              <Typography>
                <CommandText>{APPLY_CMD}</CommandText> - Accept the suggested
                auto-fix changes
              </Typography>
              <Typography>
                <CommandText>{REJECT_CMD}</CommandText> - Reject the suggested
                auto-fix changes
              </Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleHelpDialogClose} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openRefreshDialog}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleRefreshDialogClose();
          }
        }}
      >
        <DialogTitle>Confirm Refresh</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to refresh the editor? This will clear all
            content.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRefreshDialogClose}>Cancel</Button>
          <Button onClick={handleRefresh} color="primary">
            Refresh
          </Button>
        </DialogActions>
      </Dialog>
      {/* Duplicate Name Dialog */}
      <Dialog open={openDuplicateDialog} onClose={handleDuplicateDialogClose}>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Typography>{duplicateMessage}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDuplicateDialogClose} color="primary">
            OK
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openCloseConfirmDialog}
        onClose={handleCloseConfirmDialogClose}
      >
        <DialogTitle>Confirm Close</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to close{" "}
            {inTemplateEditing ? "template editing" : "new report"}? Any unsaved
            changes will be lost.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialogClose}>Cancel</Button>
          <Button onClick={handleConfirmClose} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <AutoFixPreviewPopover
        anchorEl={autoFixAnchorEl}
        open={Boolean(autoFixAnchorEl)}
        onClose={() => setAutoFixAnchorEl(null)}
        diffs={diffs}
        isLoading={isAutoFixLoading || inFetchData}
        loadingText={
          isAutoFixLoading
            ? "Processing auto-fix..."
            : "Generating impression..."
        }
      />
      <AutoFixPreviewPopover
        anchorEl={impressionLoadingEl}
        open={Boolean(impressionLoadingEl)}
        onClose={() => setImpressionLoadingEl(null)}
        isLoading={inFetchData}
        loadingText="Generating impression..."
      />
    </ThemeProvider>
  );
};

export default ReportEditor;
