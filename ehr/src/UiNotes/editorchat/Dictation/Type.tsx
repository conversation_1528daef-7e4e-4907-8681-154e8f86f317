export interface ConfigInfo {
  title: "Configuration" | "Date Configuration" | "Dropdown Configuration";
  content: any;
}

export interface MacroInfo {
  title: string;
  content: any;
}

export interface RadData {
  id: string;
  name: string;
  type: "dropdown" | "text" | "template";
  description: any;
}

export interface AIConversation {
  isAI: boolean;
  content: string;
}

export interface TemplateMacroType {
  id: React.Key | null | undefined;
  name: string;
  type: string;
  description: string;
  owner: string;
}

export type DropdownDescription = {
  id: string | number;
  code: string;
  description: string;
};

// Note that template is also a macro
export type MacroType = {
  type: "text" | "dropdown" | "template";
  description: string | DropdownDescription[]; // string if type is "text" or "template", array of DropdownDescription if type is "dropdown"
  id: string | number;
  owner?: string;
  name: string;
  studyDescription?: string;
  studyDateTime?: string;
  patientAge?: string;
  indication?: string;
  templateID?: string; // For rad templates
  macroID?: string; // For rad macros
  staffID?: string;
  creationTime?: string;
  modifiedTime?: string;
};

export interface ReportType {
  id: string | number;
  name: string;
  type: string;
  description: string;
  owner: string;
  examDate?: string;
  examType?: string;
  signedDate?: string;
}
