import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>ton,
  Dialog,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogTitle,
  TextField,
  IconButton,
  useTheme,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
} from "@mui/material";
import { Close, Edit, Delete } from "@mui/icons-material";
import { useAtom, useSetAtom } from "jotai";
import {
  FormValuesAtom,
  MacroDataAtom,
  MacroTypeAtom,
  NewChoiceNameAtom,
  NewCodeDescriptionPairsAtom,
  OpenConfigDialogAtom,
  SelectedItemAtom,
  ReloadRadMacrosAtom,
  RadMacrosAtom,
} from "./ReportContext";
import { SAVE_JSON_MACRO, DELETE_JSON_MACRO, MACRO_NAME_SLASH_ERROR } from "./Constant";
import { RadData } from "./Type";
import { RadMacroAPI } from "../Services/RadMacroAPI";

export interface CodeDescriptionPair {
  code: string;
  description: string;
  isDefault?: boolean;
}

export interface DropdownChoice {
  id: string;
  name: string;
  type: "dropdown";
  description: CodeDescriptionPair[];
}

const MacroConfig: React.FC = () => {
  const [formValues, setFormValues] = useAtom(FormValuesAtom);
  const [openDialog, setOpenDialog] = useState(false);
  const [newChoiceName, setNewChoiceName] = useAtom(NewChoiceNameAtom);
  const [newCodeDescriptionPairs, setNewCodeDescriptionPairs] = useAtom(
    NewCodeDescriptionPairsAtom
  );
  const [macroData, setMacroData] = useAtom(MacroDataAtom);
  const [openConfigDialog, setOpenConfigDialog] = useAtom(OpenConfigDialogAtom);
  const [selectedItem, setSelectedItem] = useAtom(SelectedItemAtom);
  const setReloadRadMacros = useSetAtom(ReloadRadMacrosAtom);
  const [radMacros, setRadMacros] = useAtom(RadMacrosAtom);

  // Get current theme
  const theme = useTheme();

  const [defaultChoiceIndex, setDefaultChoiceIndex] = useState<number>(0);

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setNewChoiceName("");
    setNewCodeDescriptionPairs([{ code: "", description: "", isDefault: true }]);
    setDefaultChoiceIndex(0);
  };

  const saveData = async (props: any) => {
    let newChoice: RadData;

    // Filtered out the 'new' macro. This can be an existing or new macro
    const filteredRadData = macroData.filter(
      (item: any) => item.name !== props.name
    );

    if (macroType === "text") {
      newChoice = {
        id: Math.random().toString(),
        type: "text" as const,
        name: formValues.name,
        description: formValues.description,
      };
    } else {
      newChoice = {
        id: Math.random().toString(),
        type: "dropdown" as const,
        name: newChoiceName,
        description: newCodeDescriptionPairs,
      };
    }

    if (newChoice) {
      try {
        const result = await handleSaveJsonMacro(newChoice);
        console.log("Macro save result:", result);

        // Trigger refresh of rad macros
        setReloadRadMacros(true);

        // Update the selectedItem if it's the same macro being edited
        if (selectedItem && selectedItem.name === props.name) {
          setSelectedItem(newChoice);
        } else {
          // If it's a new macro, set it as the selected item
          setSelectedItem(newChoice);
        }

        // Select the Macro radio button
        const macroRadio = document.querySelector('input[value="macro"]');
        if (macroRadio) {
          (macroRadio as HTMLInputElement).click();
        }
      } catch (error) {
        console.error("Failed to save macro:", error);
        // Don't update the UI state if save failed
      }
    }
  };

  const handleSaveJsonMacro = async (macroData: RadData) => {
    try {
      if (!macroData.name) {
        throw new Error("Macro name is required");
      }

      const staffId = "global"; // Use hardcoded staff ID as per pattern

      const radMacroData = {
        type: macroType as "text" | "dropdown", // Ensure proper typing
        name: macroData.name,
        description: typeof macroData.description === 'string'
          ? macroData.description
          : JSON.stringify(macroData.description)
      };

      // Check if we're editing an existing rad macro
      // First check if selectedItem has a macroID (indicating it's a rad macro)
      const isEditingRadMacro = selectedItem && 'macroID' in selectedItem && selectedItem.macroID;

      if (isEditingRadMacro) {
        // Update existing rad macro
        console.log("Updating existing rad macro:", selectedItem.macroID);
        const updatedMacro = await RadMacroAPI.updateRadMacro(staffId, selectedItem.macroID, radMacroData);
        console.log("Rad macro updated successfully:", updatedMacro);

        // Update the macro in shared radMacros state
        setRadMacros((prev: any) => prev.map((m: any) =>
          m.macroID === selectedItem.macroID ? updatedMacro : m
        ));

        return { message: "Macro updated successfully", macro: updatedMacro };
      } else {
        // Create new rad macro
        console.log("Creating new rad macro");
        const createdMacro = await RadMacroAPI.createRadMacro(staffId, radMacroData);
        console.log("Rad macro created successfully:", createdMacro);

        // Add the created macro to shared radMacros state immediately
        setRadMacros((prev: any) => [...prev, createdMacro]);

        return { message: "Macro saved successfully", macro: createdMacro };
      }
    } catch (error) {
      console.error("Error saving rad macro:", error);
      throw error;
    }
  };

  const handleChangeCodeDescription = (
    index: number,
    field: "code" | "description" | "isDefault",
    value: string | boolean
  ) => {
    const updatedPairs = [...newCodeDescriptionPairs];
    if (field === "isDefault") {
      // Set all choices to not default first
      updatedPairs.forEach(pair => {
        pair.isDefault = false;
      });
      // Then set only the selected choice as default
      updatedPairs[index].isDefault = true;
      setDefaultChoiceIndex(index);
    } else {
      updatedPairs[index] = {
        ...updatedPairs[index],
        [field]: value,
      };
    }
    setNewCodeDescriptionPairs(updatedPairs);
  };

  const handleAddCodeDescriptionPair = () => {
    setNewCodeDescriptionPairs([
      ...newCodeDescriptionPairs,
      { code: "", description: "", isDefault: false },
    ]);
  };

  const handleRemoveCodeDescriptionPair = (index: number) => {
    const updatedPairs = newCodeDescriptionPairs.filter((_, i) => i !== index);
    // If we removed the default choice, set the first choice as default
    if (updatedPairs.length > 0 && !updatedPairs.some(pair => pair.isDefault)) {
      updatedPairs[0].isDefault = true;
      setDefaultChoiceIndex(0);
    }
    setNewCodeDescriptionPairs(updatedPairs);
  };

  // Add a new effect to ensure first choice is default when editing
  useEffect(() => {
    if (newCodeDescriptionPairs.length > 0 && !newCodeDescriptionPairs.some(pair => pair.isDefault)) {
      const updatedPairs = [...newCodeDescriptionPairs];
      updatedPairs[0].isDefault = true;
      setNewCodeDescriptionPairs(updatedPairs);
      setDefaultChoiceIndex(0);
    }
  }, [newCodeDescriptionPairs]);

  const handleSaveChoice = async () => {
    try {
      await saveData({ name: newChoiceName, description: newCodeDescriptionPairs });
      setOpenConfigDialog(false);
      setFormValues({
        name: "",
        description: "",
      });
      setNewCodeDescriptionPairs([{ code: "", description: "", isDefault: true }]);
      setDefaultChoiceIndex(0);
    } catch (error) {
      console.error("Failed to save choice:", error);
    }
  };

  const handleChangeName = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewChoiceName(event.target.value);
  };

  const handleKeyDown = async (event: {
    key: string;
    preventDefault: () => void;
  }) => {
    if (event.key === "Enter") {
      event.preventDefault();
      await handleSaveChoice();
    }
  };

  const [macroType, setMacroType] = useAtom(MacroTypeAtom);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (value === "text" || value === "dropdown") {
      setMacroType(value);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormValues({ ...formValues, [name]: value });
  };

  // Handle save macro
  const handleSubmit = async () => {
    try {
      console.log("Form submitted:", formValues);
      await saveData(formValues);
      setOpenConfigDialog(false);
      setFormValues({
        name: "",
        description: "",
      });
    } catch (error) {
      console.error("Failed to submit form:", error);
    }
  };

  const handleConfigDialogClose = () => {
    setOpenConfigDialog(false);
  };

  const handleDeleteMacro = async (macroName: string) => {
    try {
      const response = await fetch(DELETE_JSON_MACRO, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: macroName }),
      });

      if (response.ok) {
        // Remove the macro from local state
        const updatedData = macroData.filter((item: any) => item.name !== macroName);
        setMacroData(updatedData);
      }
    } catch (error) {
      console.error("Error deleting macro:", error);
    }
  };

  return (
    <Dialog
      open={openConfigDialog}
      onClose={handleConfigDialogClose}
      sx={{
        "& .MuiDialog-paper": {
          width: "50%",
          maxWidth: "50%",
        },
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#3D6EBF',
        color: 'white',
        padding: '12px 24px'
      }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', fontSize: '18px' }}>
          Macros
        </Typography>
        <IconButton onClick={handleConfigDialogClose} sx={{ color: 'white', padding: '4px' }}>
          <Close />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>Existing Macros ({macroData.length})</Typography>
          <Box sx={{ maxHeight: 200, overflow: 'auto', border: '1px solid #ccc', borderRadius: 1, p: 1 }}>
            {macroData.length === 0 ? (
              <Typography color="text.secondary">No macros available</Typography>
            ) : (
              macroData.map((macro: any) => (
                <Box
                  key={macro.id}
                  sx={{
                    p: 1,
                    border: '1px solid #eee',
                    borderRadius: 1,
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': { backgroundColor: '#f5f5f5' }
                  }}
                  onClick={() => {
                    if (macro.type === 'text') {
                      setFormValues({ name: macro.name, description: macro.description });
                    } else {
                      setNewChoiceName(macro.name);
                      setNewCodeDescriptionPairs(macro.description);
                    }
                    setSelectedItem(macro);
                  }}
                >
                  <Typography variant="subtitle1">{macro.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {macro.type === 'text' ? macro.description : `${macro.description.length} choices`}
                  </Typography>
                </Box>
              ))
            )}
          </Box>
        </Box>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Create New Macro</Typography>
          <Button
            variant="contained"
            size="small"
            onClick={() => {
              setFormValues({ name: "", description: "" });
              setNewChoiceName("");
              setNewCodeDescriptionPairs([{ code: "", description: "", isDefault: true }]);
              setSelectedItem(null);
            }}
          >
            New Macro
          </Button>
        </Box>
        <Box
          sx={{
            height: "auto",
            maxHeight: macroType === "text" ? "300px" : "400px",
            overflow: "auto",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
            <RadioGroup
              row
              value={macroType}
              onChange={handleChange}
              sx={{ display: "flex", flexDirection: "row", gap: 2 }}
            >
              <FormControlLabel value="text" control={<Radio />} label="Text" />
              <FormControlLabel
                value="dropdown"
                control={<Radio />}
                label="Dropdown"
              />
            </RadioGroup>
          </Box>
          {macroType === "text" ? (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                width: "100%",
              }}
            >
              <TextField
                size="small"
                label="Name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                variant="outlined"
                fullWidth
                error={formValues.name.includes('/')}
                helperText={formValues.name.includes('/') ? MACRO_NAME_SLASH_ERROR : ""}
                FormHelperTextProps={{
                  sx: { fontSize: '0.875rem', marginTop: 0.5 }
                }}
              />
              <TextField
                size="small"
                label="Description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                variant="outlined"
                multiline
                rows={3}
                fullWidth
              />
            </Box>
          ) : (
            <Box>
              <TextField
                autoFocus
                label="Name"
                variant="outlined"
                value={newChoiceName}
                onChange={handleChangeName}
                fullWidth
                size="small"
                sx={{ marginBottom: 1, marginTop: 1 }}
                error={newChoiceName.includes('/')}
                helperText={newChoiceName.includes('/') ? MACRO_NAME_SLASH_ERROR : ""}
                FormHelperTextProps={{
                  sx: { fontSize: '0.875rem', marginTop: 0.5 }
                }}
              />
              <Box sx={{ overflow: "auto", maxHeight: "250px" }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Select default choice for dropdown
                </Typography>
                {newCodeDescriptionPairs.map((pair, index) => (
                  <Box key={index} sx={{ marginBottom: 1 }}>
                    <Box
                      sx={{
                        display: "flex",
                        gap: 1,
                        alignItems: "center",
                        mt: 0.7,
                      }}
                    >
                      <Radio
                        checked={pair.isDefault || false}
                        onChange={() => handleChangeCodeDescription(index, "isDefault", true)}
                        value={index}
                        name="default-choice"
                      />
                      <TextField
                        autoFocus={newChoiceName ? true : false}
                        label="Code"
                        variant="outlined"
                        value={pair.code}
                        onChange={(e: { target: { value: string } }) =>
                          handleChangeCodeDescription(
                            index,
                            "code",
                            e.target.value
                          )
                        }
                        size="small"
                        sx={{ flex: 1, top: 0 }}
                      />
                      <TextField
                        label="Description"
                        multiline
                        variant="outlined"
                        fullWidth
                        maxRows={5}
                        sx={{
                          "& .MuiInputBase-root": {
                            overflow: "hidden", // Prevent unnecessary scrollbars
                          },
                          flex: 2,
                        }}
                        value={pair.description}
                        onChange={(e: { target: { value: string } }) =>
                          handleChangeCodeDescription(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        size="small"
                      />
                      <IconButton
                        onClick={() => handleRemoveCodeDescriptionPair(index)}
                        sx={{ marginLeft: 1 }}
                      >
                        <Close />
                      </IconButton>
                    </Box>
                  </Box>
                ))}
              </Box>
              <Button
                sx={{ marginBottom: "10px" }}
                variant="outlined"
                onClick={handleAddCodeDescriptionPair}
                disabled={newCodeDescriptionPairs.length >= 10} // limit to 10 choices for now
              >
                Add New Code/Description
              </Button>
            </Box>
          )}
          <Dialog
            open={openDialog}
            onClose={handleCloseDialog}
            fullWidth
            maxWidth="md"
            onKeyDown={handleKeyDown}
          >
            <DialogTitle>Add Dropdown</DialogTitle>
            <DialogContent>
              <TextField
                label="Dropdown Name"
                variant="outlined"
                value={newChoiceName}
                onChange={handleChangeName}
                fullWidth
                size="small"
                sx={{ marginBottom: 2, marginTop: 1 }}
              />
              {newCodeDescriptionPairs.map((pair, index) => (
                <Box key={index} sx={{ marginBottom: 1 }}>
                  <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
                    <TextField
                      label="Code"
                      variant="outlined"
                      value={pair.code}
                      onChange={(e: { target: { value: string } }) =>
                        handleChangeCodeDescription(
                          index,
                          "code",
                          e.target.value
                        )
                      }
                      size="small"
                      sx={{ flex: 1 }}
                    />
                    <TextField
                      label="Description"
                      variant="outlined"
                      value={pair.description}
                      onChange={(e: { target: { value: string } }) =>
                        handleChangeCodeDescription(
                          index,
                          "description",
                          e.target.value
                        )
                      }
                      size="small"
                      sx={{ flex: 2 }}
                    />
                    <IconButton
                      onClick={() => handleRemoveCodeDescriptionPair(index)}
                      sx={{ marginLeft: 1 }}
                    >
                      <Close />
                    </IconButton>
                  </Box>
                </Box>
              ))}
            </DialogContent>
            <DialogActions sx={{ justifyContent: "space-between", padding: 2 }}>
              <Button
                sx={{ marginBottom: "10px", marginLeft: "10px" }}
                variant="outlined"
                onClick={handleAddCodeDescriptionPair}
              >
                Add Code-Description Pair
              </Button>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Button aria-hidden={true} onClick={handleCloseDialog}>
                  Cancel
                </Button>
                <Button aria-hidden={true} onClick={handleSaveChoice}>
                  Save
                </Button>
              </Box>
            </DialogActions>
          </Dialog>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleConfigDialogClose} color="primary">
          Cancel
        </Button>
        <Button
          disabled={
            macroType === "text"
              ? formValues.name && formValues.description
                ? false
                : true
              : newChoiceName &&
                newCodeDescriptionPairs.length > 0 &&
                newCodeDescriptionPairs[0].code &&
                newCodeDescriptionPairs[0].description
                ? false
                : true
          }
          onClick={macroType === "text" ? handleSubmit : handleSaveChoice}
          color="primary"
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MacroConfig;
