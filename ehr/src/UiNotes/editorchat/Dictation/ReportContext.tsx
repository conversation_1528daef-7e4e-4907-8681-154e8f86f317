import { atom } from "jotai";
import { AIConversation, ConfigInfo, MacroInfo, MacroType } from "./Type";
import { DEFAULT_CONFIG, DEFAULT_MACRO } from "./Constant";

export interface CodeDescriptionPair {
  code: string;
  description: string;
  isDefault?: boolean;
}

export const ConfigDateAttributeAtom = atom<string>("");
export const ConfigDropdownAttributeAtom = atom<string>("");

export const DropdownMacroInfoAtom = atom<CodeDescriptionPair>({
  code: "",
  description: "",
});

// Macros include templates, text macros, and choice macros
export const MacroDataAtom = atom<MacroType[]>([]);
export const ReportDataAtom = atom<any>([]);

export const ReloadMacroDataAtom = atom<boolean>(true);
export const ReloadRadTemplatesAtom = atom<boolean>(false);
export const ReloadRadMacrosAtom = atom<boolean>(false);
export const RadMacrosAtom = atom<any[]>([]);

export const SelectedItemAtom = atom<any>(null);

// config info for selected date or dropdown node
export const ConfigInfoAtom = atom<ConfigInfo>(DEFAULT_CONFIG);

// macro info for selected dropdown node
export const MacroInfoAtom = atom<MacroInfo>(DEFAULT_MACRO);

export const SelectedBracketNodeAtom = atom<any>(null);
export const OpenConfigDialogAtom = atom<boolean>(false);

export const FormValuesAtom = atom<any>({
  name: "",
  description: "",
});

export const SearchTextAtom = atom<string>("");
export const DeletedItemAtom = atom<any>(null);
export const SelectedFloatingReportAtom = atom<any>(null);
export const ShowAIAssistantAtom = atom<boolean>(false);

export const AIImpressionConversationAtom = atom<AIConversation[]>([]);

export const MacroTypeAtom = atom<"text" | "dropdown" | "template">("text");
export const NewChoiceNameAtom = atom<string>("");
export const NewCodeDescriptionPairsAtom = atom<CodeDescriptionPair[]>([
  { code: "", description: "" },
]);

export const InTemplateEditingAtom = atom<boolean>(false);

export const MrnAtom = atom<string>("");
