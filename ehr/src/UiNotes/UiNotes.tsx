import * as React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import ProgressNote from "./ProgressNote";
import { CurrentTabIndexAtom } from "@cci-monorepo/UiNotes/context/NoteContext";
import { useAtom } from "jotai";
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ paddingTop: 0 }}>
          <div>{children}</div>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

export default function UiNotes() {
  const [value, setValue] = React.useState(0);
  const [currentTabIndex, setCurrentTabIndex] = useAtom(CurrentTabIndexAtom);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    setCurrentTabIndex(newValue);
  };

  return (
    <Box
      sx={{
        height: "100vh",
        border: "5px solid #C2D7F7",
        marginTop: 0,
      }}
    >
      <Box
        sx={{
          borderBottom: 1,
          borderColor: "divider",
          backgroundColor: "#C2D7F7",
        }}
      >
        <Tabs
          sx={{
            minHeight: "5px",
            color: "#000000",
            "& .MuiTab-root": {
              color: "#FFFFFF",
              minHeight: "5px",
              height: "5px",
              padding: "15px 20px 15px 10px",
              minWidth: "50px",
              marginBottom: "-1px",
              textTransform: "none",
              background: `linear-gradient(247deg, transparent 10px, #A1A7AF 12px)`,
            },
            "& .Mui-selected": {
              color: "#000000 !important",
              background: `linear-gradient(247deg, transparent 10px, #FFFFFF 12px)`,
            },
          }}
          value={currentTabIndex}
          onChange={handleChange}
          aria-label="basic tabs example"
        >
          <Tab label="Progress Note" {...a11yProps(0)} />
        </Tabs>
      </Box>
      <CustomTabPanel value={currentTabIndex} index={0}>
        <ProgressNote screenParams={{}} />
      </CustomTabPanel>
    </Box>
  );
}
