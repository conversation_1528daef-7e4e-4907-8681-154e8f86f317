import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  IconButton,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Paper,
  CircularProgress,
} from "@mui/material";
import { Close, Add, Delete } from "@mui/icons-material";
import { useAtom } from "jotai";
import {
  FormValuesAtom,
  MacroDataAtom,
  OpenConfigDialogAtom,
  SelectedItemAtom,
  MacroType,
  SelectedTextAtom,
} from "../context/MacroContext";
import { MacroAPI } from "../services/MacroAPI";
import { Macro } from "../types/MacroVersion";

interface MacroConfigProps {
  /** Whether to show selection mode (Ctrl+M) or full management mode (icon click) */
  selectionMode?: boolean;
  /** Callback when a macro is selected in selection mode */
  onMacroSelect?: (macro: Macro) => void;
  /** Callback when the dialog closes */
  onClose?: () => void;
}

/**
 * Macro configuration dialog component
 * Supports two modes:
 * - Selection mode (Ctrl+M): Quick macro selection and insertion
 * - Management mode (icon click): Full macro creation, editing, and deletion
 */
const MacroConfig: React.FC<MacroConfigProps> = ({
  selectionMode = false,
  onMacroSelect,
  onClose: onCloseCallback,
}) => {
  const [formValues, setFormValues] = useAtom(FormValuesAtom);
  const [macroData, setMacroData] = useAtom(MacroDataAtom);
  const [openConfigDialog, setOpenConfigDialog] = useAtom(OpenConfigDialogAtom);
  const [selectedItem, setSelectedItem] = useAtom(SelectedItemAtom);
  const [selectedText, setSelectedText] = useAtom(SelectedTextAtom);
  const [isCreatingNew, setIsCreatingNew] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasFetched, setHasFetched] = useState(false);

  // Fetch macros from the API
  const fetchMacros = async () => {
    if (isLoading || hasFetched) return;

    setIsLoading(true);
    setError(null);
    try {
      const macros = await MacroAPI.getMacros("global");
      // Convert API Macro type to MacroType
      const convertedMacros: MacroType[] = macros.map((macro) => ({
        id: macro.macroID,
        type: "text",
        name: macro.name,
        description: macro.description,
      }));

      setMacroData(convertedMacros);
      setHasFetched(true);
    } catch (err) {
      console.error("Error fetching macros:", err);
      setError("Failed to load macros");
    } finally {
      setIsLoading(false);
    }
  };

  // Close dialog and reset state
  const handleClose = () => {
    setOpenConfigDialog(false);
    setFormValues({ name: "", description: "" });
    setIsCreatingNew(false);
    setSelectedText("");
    setHasFetched(false);
    if (onCloseCallback) {
      onCloseCallback();
    }
  };

  // Save or update macro
  const handleSave = async () => {
    try {
      if (selectedItem) {
        // Update existing macro
        const macroToSave: Macro = {
          macroID: selectedItem.id.toString(),
          type: "text",
          name: formValues.name,
          description: formValues.description,
        };

        // Save to backend
        await MacroAPI.updateMacro("global", macroToSave);

        // Convert to MacroType for local state
        const macroTypeToSave: MacroType = {
          id: macroToSave.macroID,
          type: "text",
          name: macroToSave.name,
          description: macroToSave.description,
        };

        // Update local state
        const updatedData = macroData.map((item) =>
          item.id === selectedItem.id ? macroTypeToSave : item
        );

        setMacroData(updatedData);
        setSelectedItem(macroTypeToSave);
      } else {
        // Create new macro
        const newMacro = {
          macroID: "",
          type: "text",
          name: formValues.name,
          description: formValues.description,
        };

        // Save to backend
        const savedMacro = await MacroAPI.createMacro("global", newMacro);

        // Convert to MacroType for local state
        const macroTypeToSave: MacroType = {
          id: savedMacro.macroID,
          type: "text",
          name: savedMacro.name,
          description: savedMacro.description,
        };

        // Update local state
        setMacroData([...macroData, macroTypeToSave]);
        setSelectedItem(macroTypeToSave);
      }
    } catch (error) {
      console.error("Failed to save macro:", error);
      setError("Failed to save macro");
    }
  };

  // Delete macro
  const handleDeleteMacro = async (macro: MacroType) => {
    try {
      await MacroAPI.deleteMacro("global", macro.id.toString());
      const updatedData = macroData.filter((item) => item.id !== macro.id);
      setMacroData(updatedData);
      if (selectedItem?.id === macro.id) {
        setSelectedItem(null);
      }
    } catch (error) {
      console.error("Failed to delete macro:", error);
      setError("Failed to delete macro");
    }
  };

  // Start creating a new macro
  const handleCreateNew = () => {
    setIsCreatingNew(true);
    setFormValues({ name: "", description: "" });
    setSelectedItem(null);
  };

  // Select a macro to view/edit
  const handleSelectMacro = (macro: MacroType) => {
    // Just select the macro to show its description
    setSelectedItem(macro);
    setFormValues({ name: macro.name, description: macro.description });
  };

  // Use selected macro (only in selection mode)
  const handleUseMacro = () => {
    if (selectedItem && selectionMode && onMacroSelect) {
      const macroForSelection: Macro = {
        macroID: typeof selectedItem.id === 'string' ? selectedItem.id : selectedItem.id.toString(),
        type: selectedItem.type,
        name: selectedItem.name,
        description: selectedItem.description,
      };
      onMacroSelect(macroForSelection);
      handleClose();
    }
  };

  // Initialize dialog state when opened
  useEffect(() => {
    if (openConfigDialog) {
      fetchMacros();
      if (selectedText) {
        setIsCreatingNew(true);
        setFormValues({
          name: "",
          description: selectedText,
        });
      } else if (macroData.length > 0) {
        const firstMacro = macroData[0];
        setSelectedItem(firstMacro);
        setFormValues({
          name: firstMacro.name,
          description: firstMacro.description,
        });
        setIsCreatingNew(true);
      } else {
        setIsCreatingNew(true);
        setFormValues({
          name: "",
          description: "",
        });
      }
    }
  }, [openConfigDialog, selectedText]);

  return (
    <Dialog
      open={openConfigDialog}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          "& *": {
            fontFamily: "Roboto",
            fontSize: "16px",
          },
          boxShadow: 'none'
        },
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        // Blue background for both modes
        backgroundColor: '#3D6EBF',
        color: 'white',
        padding: '12px 24px'
      }}>
        <Typography
          variant="subtitle1"
          sx={{
            fontFamily: "Roboto",
            fontSize: "16px",
            fontWeight: 'bold'
          }}
        >
          {selectionMode ? "Use Macro" : "Macros"}
        </Typography>
        <IconButton onClick={handleClose} size="small" sx={{ color: selectionMode ? 'inherit' : 'white' }}>
          <Close />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{
        p: 2,
        pt: 3,
        '&.MuiDialogContent-root': {
          paddingTop: '15px'
        }
      }}>
        <Box sx={{ display: "flex", gap: 2, height: "400px" }}>
          <Paper sx={{ width: "40%", p: 1, overflow: "auto", boxShadow: 'none' }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mb={1}
            >
              <Typography
                variant="subtitle1"
                sx={{ fontFamily: "Roboto", fontSize: "16px", fontWeight: 500 }}
              >
                Existing Macros
              </Typography>
              {/* Only show New Macro button in management mode */}
              {!selectionMode && (
                <Button
                  startIcon={<Add />}
                  onClick={handleCreateNew}
                  variant="contained"
                  size="small"
                  sx={{ fontFamily: "Roboto", fontSize: "14px" }}
                >
                  New Macro
                </Button>
              )}
            </Box>
            {isLoading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="200px"
              >
                <CircularProgress />
              </Box>
            ) : error ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="200px"
              >
                <Typography
                  color="error"
                  sx={{ fontFamily: "Roboto", fontSize: "16px" }}
                >
                  {error}
                </Typography>
              </Box>
            ) : (
              <List>
                {macroData.map((macro) => (
                  <React.Fragment key={macro.id}>
                    <ListItem
                      button
                      selected={selectedItem?.id === macro.id}
                      onClick={() => handleSelectMacro(macro)}
                      onDoubleClick={handleUseMacro}
                    >
                      <ListItemText
                        primary={macro.name}
                        secondary={macro.description.substring(0, 50) + "..."}
                        primaryTypographyProps={{
                          variant: "body2",
                          sx: { fontFamily: "Roboto", fontSize: "16px" },
                        }}
                        secondaryTypographyProps={{
                          variant: "caption",
                          sx: { fontFamily: "Roboto", fontSize: "16px" },
                        }}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          aria-label="delete"
                          onClick={() => handleDeleteMacro(macro)}
                          size="small"
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>

          {((isCreatingNew && !selectionMode) || (selectionMode && selectedItem)) && (
            <Paper sx={{ width: "60%", p: 1, boxShadow: 'none' }}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 2,
                  height: "100%",
                }}
              >
                {!selectionMode && (
                  <TextField
                    label="Macro Name"
                    value={formValues.name}
                    onChange={(e) =>
                      setFormValues({ ...formValues, name: e.target.value })
                    }
                    fullWidth
                    required
                    size="small"
                    InputProps={{
                      sx: { fontFamily: "Roboto", fontSize: "16px" },
                    }}
                    InputLabelProps={{
                      sx: { fontFamily: "Roboto", fontSize: "16px" },
                    }}
                  />
                )}
                <TextField
                  label="Description"
                  value={formValues.description}
                  onChange={(e) =>
                    setFormValues({
                      ...formValues,
                      description: e.target.value,
                    })
                  }
                  fullWidth
                  multiline
                  rows={selectionMode ? 15 : 12}
                  InputProps={{
                    sx: { fontFamily: "Roboto", fontSize: "16px" },
                    readOnly: selectionMode,
                  }}
                  InputLabelProps={{
                    sx: { fontFamily: "Roboto", fontSize: "16px" },
                  }}
                  sx={{
                    flex: 1,
                    "& .MuiInputBase-root": {
                      height: "100%",
                    },
                    "& .MuiInputBase-input": {
                      height: "100%",
                    },
                  }}
                />
              </Box>
            </Paper>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ px: 2, pt: 0, pb: 2 }}>
        <Button onClick={handleClose} size="small" sx={{ fontSize: "14px" }}>Cancel</Button>
        {/* Save button - only in management mode when creating/editing */}
        {isCreatingNew && !selectionMode && (
          <Button
            onClick={handleSave}
            variant="contained"
            color="primary"
            size="small"
            sx={{ fontSize: "14px" }}
            disabled={Boolean(
              selectedItem &&
              formValues.name === selectedItem.name &&
              formValues.description === selectedItem.description
            )}
          >
            Save Macro
          </Button>
        )}
        {/* Use Macro button - only in selection mode when macro is selected */}
        {selectionMode && selectedItem && (
          <Button
            onClick={handleUseMacro}
            variant="contained"
            color="primary"
            size="small"
            sx={{ fontFamily: "Roboto", fontSize: "14px" }}
          >
            Use Macro
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default MacroConfig; 