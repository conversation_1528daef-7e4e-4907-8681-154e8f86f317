import React, { useState, useEffect } from 'react';
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { API_BASE_URL, DEFAULT_TIME_RANGE_SECONDS } from '../Constants';
import { CompactDataComponent } from './CompactDataComponent';

/**
 * Interface for vitals data
 */
export interface VitalsData {
  key: number;
  heartRate: number;
  respirationRate: number;
  pulseOx: number;
  temperatureF: number;
  temperatureC: number;
  painScore: number;
  systolicBP: number;
  diastolicBP: number;
}

/**
 * Interface for selected vital column
 */
interface SelectedVitalColumn {
  timeKey: number;
}

/**
 * Props for the Vitals component
 */
interface VitalsProps {
  patientId?: string;
  startTime?: number;
  endTime?: number;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
  onCopy?: (vitalsDataWithSelection?: any) => void;
  vitalsData?: VitalsData[];
  isEnabled?: boolean; // New prop to control if component is enabled
  useCompactWrapper?: boolean; // New prop to control if CompactDataComponent should be used
}

// Define normal ranges for vital signs
const normalRanges = {
  temperatureF: { min: 97.8, max: 100.4, unit: '°F' },
  temperatureC: { min: 36.5, max: 38.0, unit: '°C' },
  heartRate: { min: 60, max: 100, unit: 'bpm' },
  respirationRate: { min: 12, max: 18, unit: 'breaths/min' },
  pulseOx: { min: 95, max: 100, unit: '%' },
  painScore: { min: 0, max: 10, unit: '' },
  systolicBP: { min: 90, max: 120, unit: 'mmHg' },
  diastolicBP: { min: 60, max: 80, unit: 'mmHg' }
};

// Helper function to check if a value is abnormal
const isAbnormal = (value: number | null, vitalType: keyof typeof normalRanges): boolean => {
  if (value === null || value === undefined) return false;
  const range = normalRanges[vitalType];
  if (!range) return false;
  return value < range.min || value > range.max;
};

// Styled table for vitals (compact version)
const StyledTable = styled(Table)({
  '& .MuiTableCell-root': {
    padding: '2px 4px',
    fontSize: '15px',
    fontFamily: 'Roboto',
    borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
    '&.MuiTableCell-head': {
      backgroundColor: '#f0f0f0',
      fontWeight: 500,
      color: '#333333',
    },
    '&.MuiTableCell-body': {
      backgroundColor: '#ffffff',
      color: '#333333',
    },
  },
  "& .MuiTableRow-root:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  },
});

/**
 * Vitals component that displays vitals data
 */
export const Vitals: React.FC<VitalsProps> = ({
  patientId,
  startTime,
  endTime,
  editor,
  mode = 'copy',
  onDelete,
  onCopy,
  vitalsData: initialVitalsData,
  isEnabled = true,
  useCompactWrapper = true
}) => {
  const [vitalsData, setVitalsData] = useState<VitalsData[]>(initialVitalsData || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);
  const [selectedColumns, setSelectedColumns] = useState<SelectedVitalColumn[]>([]);
  const [isSelectiveMode, setIsSelectiveMode] = useState(false);

  const fetchVitalsData = async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      const endTimeValue = endTime || Math.floor(Date.now() / 1000);
      const startTimeValue = startTime || (endTimeValue - DEFAULT_TIME_RANGE_SECONDS); // Default to last 3 months if not provided

      const url = `${API_BASE_URL}/getData?object=Vitals&patid=${patientId}&type=UnifiedCCO&starttime=${startTimeValue}&endtime=${endTimeValue}`;

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('webtoken'),
          'X-Forwarded-For': '127.0.0.1',
          'X-Real-IP': '127.0.0.1',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch vitals data: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch vitals data');
      }

      if (!Array.isArray(data.data)) {
        throw new Error('Invalid data format received from API');
      }

      // Filter and transform vitals data
      const validRecords = data.data.filter((item: any) => {
        return item.heart_rate !== null && item.heart_rate !== undefined ||
          item.respiration_rate !== null && item.respiration_rate !== undefined ||
          item.pulse_ox !== null && item.pulse_ox !== undefined ||
          item.temperature_f !== null && item.temperature_f !== undefined ||
          item.temperature_c !== null && item.temperature_c !== undefined ||
          item.pain_score !== null && item.pain_score !== undefined ||
          item.cuff_bp_systolic !== null && item.cuff_bp_systolic !== undefined ||
          item.cuff_bp_diastolic !== null && item.cuff_bp_diastolic !== undefined;
      });

      const transformedData = validRecords.map((item: any) => ({
        key: Number(item.key),
        heartRate: item.heart_rate !== null && item.heart_rate !== undefined ? Number(item.heart_rate) : null,
        respirationRate: item.respiration_rate !== null && item.respiration_rate !== undefined ? Number(item.respiration_rate) : null,
        pulseOx: item.pulse_ox !== null && item.pulse_ox !== undefined ? Number(item.pulse_ox) : null,
        temperatureF: item.temperature_f !== null && item.temperature_f !== undefined ? Number(item.temperature_f) : null,
        temperatureC: item.temperature_c !== null && item.temperature_c !== undefined ? Number(item.temperature_c) : null,
        painScore: item.pain_score !== null && item.pain_score !== undefined ? Number(item.pain_score) : null,
        systolicBP: item.cuff_bp_systolic !== null && item.cuff_bp_systolic !== undefined ? Number(item.cuff_bp_systolic) : null,
        diastolicBP: item.cuff_bp_diastolic !== null && item.cuff_bp_diastolic !== undefined ? Number(item.cuff_bp_diastolic) : null,
      }));

      setVitalsData(transformedData);
    } catch (err) {
      console.error('Error fetching vitals:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch vitals');
    } finally {
      setLoading(false);
    }
  };

  const handleExpand = () => {
    if (!isExpanded) {
      fetchVitalsData();
    }
  };

  // Fetch vitals on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchVitalsData();
    }
  }, [patientId, startTime, endTime]);

  // Update parent component whenever vitals data changes
  useEffect(() => {
    if (onCopy) {
      const vitalsDataWithSelection = {
        vitals: vitalsData,
        selectedColumns: selectedColumns,
        isSelectiveMode: isSelectiveMode,
        isEnabled: isEnabled
      };
      onCopy(vitalsDataWithSelection);
    }
  }, [vitalsData, selectedColumns, isSelectiveMode, isEnabled]); // Removed onCopy from dependencies

  const handleCopy = () => {
    console.log('handleCopy called, vitalsData length:', vitalsData.length);

    // If onCopy prop is provided, use it (for section-based copying)
    if (onCopy) {
      console.log('onCopy prop exists, calling it');
      // Pass the current vitals data with selected columns to the copy function
      const vitalsDataWithSelection = {
        vitals: vitalsData,
        selectedColumns: selectedColumns,
        isSelectiveMode: isSelectiveMode
      };
      console.log('Vitals copy data:', vitalsDataWithSelection);
      onCopy(vitalsDataWithSelection);
      return;
    } else {
      console.log('onCopy prop does not exist');
    }

    // Only proceed with fallback behavior if there's data and no onCopy prop
    if (vitalsData.length === 0) return;

    // Fallback to original behavior for direct text insertion
    const vitalsText = vitalsData
      .map(vital => {
        const parts = [];
        if (vital.heartRate !== null) parts.push(`HR: ${vital.heartRate}`);
        if (vital.respirationRate !== null) parts.push(`RR: ${vital.respirationRate}`);
        if (vital.pulseOx !== null) parts.push(`O2: ${vital.pulseOx}%`);
        if (vital.temperatureF !== null) parts.push(`Temp: ${vital.temperatureF}°F`);
        if (vital.painScore !== null) parts.push(`Pain: ${vital.painScore}`);
        if (vital.systolicBP !== null && vital.diastolicBP !== null) parts.push(`BP: ${vital.systolicBP}/${vital.diastolicBP}`);
        return parts.join(', ');
      })
      .join('\n');

    const textToCopy = `Vitals:\n${vitalsText}`;

    if (editor) {
      editor.insertText(textToCopy);
    } else {
      navigator.clipboard.writeText(textToCopy);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  // Handle clicking on vital columns for selective mode
  const handleVitalColumnClick = (timeKey: number) => {
    // Don't allow column selection if component is disabled
    if (!isEnabled) {
      return;
    }

    const existingIndex = selectedColumns.findIndex(
      column => column.timeKey === timeKey
    );

    let newSelectedColumns: SelectedVitalColumn[];
    if (existingIndex >= 0) {
      // Remove from selection
      newSelectedColumns = selectedColumns.filter((_, index) => index !== existingIndex);
    } else {
      // Add to selection
      newSelectedColumns = [...selectedColumns, { timeKey }];
    }

    setSelectedColumns(newSelectedColumns);

    // Enable selective mode if any columns are selected
    setIsSelectiveMode(newSelectedColumns.length > 0);

    // Update the parent component with the current selection data
    if (onCopy) {
      const vitalsDataWithSelection = {
        vitals: vitalsData,
        selectedColumns: newSelectedColumns,
        isSelectiveMode: newSelectedColumns.length > 0,
        isEnabled: isEnabled
      };
      onCopy(vitalsDataWithSelection);
    }
  };

  // Check if a vital column is selected
  const isVitalColumnSelected = (timeKey: number): boolean => {
    return selectedColumns.some(column => column.timeKey === timeKey);
  };



  // Reset selective mode when no columns are selected or when component is disabled
  useEffect(() => {
    if (selectedColumns.length === 0 || !isEnabled) {
      setIsSelectiveMode(false);
    }
  }, [selectedColumns, isEnabled]);

  // Clear column selection when component is disabled
  useEffect(() => {
    if (!isEnabled && selectedColumns.length > 0) {
      setSelectedColumns([]);
    }
  }, [isEnabled]);

  const formatTimestamp = (timestamp: string | number): { date: string, time: string } => {
    try {
      const date = new Date(Number(timestamp) * 1000);
      if (isNaN(date.getTime())) {
        return { date: 'Invalid', time: 'Time' };
      }
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return {
        date: `${month}/${day}/${year}`,
        time: `${hours}:${minutes}`
      };
    } catch (error) {
      return { date: 'Invalid', time: 'Time' };
    }
  };

  // Helper function to render vital value with red styling if abnormal
  const renderVitalValue = (value: any, vitalType: string) => {
    if (value === null || value === undefined) {
      return '';
    }

    // Special handling for blood pressure
    if (vitalType === 'bloodPressure') {
      if (
        typeof value === 'object' &&
        typeof value.systolic === 'number' &&
        typeof value.diastolic === 'number'
      ) {
        const systolicAbnormal = isAbnormal(value.systolic, 'systolicBP');
        const diastolicAbnormal = isAbnormal(value.diastolic, 'diastolicBP');

        if (systolicAbnormal || diastolicAbnormal) {
          return (
            <span style={{ color: '#ff0000' }}>
              {value.systolic}/{value.diastolic} mmHg
            </span>
          );
        }
        return `${value.systolic}/${value.diastolic} mmHg`;
      }
      return '';
    }

    // Handle other vital types
    const abnormal = isAbnormal(value, vitalType as keyof typeof normalRanges);
    const range = normalRanges[vitalType as keyof typeof normalRanges];
    const unit = range ? range.unit : '';

    if (abnormal) {
      return (
        <span style={{ color: '#ff0000' }}>
          {value} {unit}
        </span>
      );
    }
    return `${value} ${unit}`;
  };

  // Process vitals data to create the table structure
  const processVitalsData = () => {
    if (!vitalsData || vitalsData.length === 0) {
      return { timeKeys: [], groupedByTime: {}, timeKeyToDisplay: {} };
    }

    // Group vitals by time
    const groupedByTime: { [key: number]: any } = {};
    const timeKeyToDisplay: { [key: number]: { date: string, time: string } } = {};

    vitalsData.forEach((vital) => {
      const timeKey = vital.key;
      const { date, time } = formatTimestamp(timeKey);
      timeKeyToDisplay[timeKey] = { date, time };

      if (!groupedByTime[timeKey]) {
        groupedByTime[timeKey] = {};
      }

      // Always include all vitals data for display
      if (vital.heartRate !== null) {
        groupedByTime[timeKey].heartRate = vital.heartRate;
      }
      if (vital.respirationRate !== null) {
        groupedByTime[timeKey].respirationRate = vital.respirationRate;
      }
      if (vital.pulseOx !== null) {
        groupedByTime[timeKey].pulseOx = vital.pulseOx;
      }
      if (vital.temperatureF !== null) {
        groupedByTime[timeKey].temperatureF = vital.temperatureF;
      }
      if (vital.painScore !== null) {
        groupedByTime[timeKey].painScore = vital.painScore;
      }
      if (vital.systolicBP !== null && vital.diastolicBP !== null) {
        groupedByTime[timeKey].bloodPressure = { systolic: vital.systolicBP, diastolic: vital.diastolicBP };
      }
    });

    const timeKeys = Object.keys(groupedByTime).map(Number).sort((a, b) => a - b);
    return { timeKeys, groupedByTime, timeKeyToDisplay };
  };

  // Get processed data
  const { timeKeys, groupedByTime, timeKeyToDisplay } = processVitalsData();

  // Define vital sign types and their display names
  const vitalTypes = [
    { key: 'heartRate', name: 'Heart Rate' },
    { key: 'respirationRate', name: 'Respiratory Rate' },
    { key: 'pulseOx', name: 'O2 Saturation' },
    { key: 'temperatureF', name: 'Temperature (°F)' },
    { key: 'painScore', name: 'Pain Score' },
    { key: 'bloodPressure', name: 'Blood Pressure' }
  ];

  const vitalsContent = (
    <>
      {vitalsData.length > 0 ? (
        <TableContainer component={Paper} sx={{ boxShadow: 'none', border: 'none' }}>
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Vital Sign</TableCell>
                {timeKeys.map((timeKey) => {
                  const { date, time } = timeKeyToDisplay[timeKey];
                  const isSelected = isVitalColumnSelected(timeKey);
                  return (
                    <TableCell
                      key={timeKey}
                      align="center"
                      onClick={() => handleVitalColumnClick(timeKey)}
                      style={{
                        cursor: isEnabled ? 'pointer' : 'default',
                        backgroundColor: isSelected ? '#ffa500' : '#f0f0f0',
                        transition: 'background-color 0.2s',
                        opacity: isEnabled ? 1 : 0.6
                      }}
                    >
                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '14px' }}>
                        {date}
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '12px', color: '#666' }}>
                        {time}
                      </Typography>
                    </TableCell>
                  );
                })}
              </TableRow>
            </TableHead>
            <TableBody>
              {vitalTypes.map((vitalType) => {
                return (
                  <TableRow key={vitalType.key}>
                    <TableCell sx={{ fontWeight: 'bold' }}>{vitalType.name}</TableCell>
                    {timeKeys.map((timeKey) => {
                      const value = groupedByTime[timeKey]?.[vitalType.key];
                      const isSelected = isVitalColumnSelected(timeKey);
                      return (
                        <TableCell
                          key={timeKey}
                          align="center"
                          style={{
                            backgroundColor: isSelected ? '#ffa500' : 'transparent',
                            transition: 'background-color 0.2s'
                          }}
                        >
                          {renderVitalValue(value, vitalType.key)}
                        </TableCell>
                      )
                    })}
                  </TableRow>
                );
              })}
            </TableBody>
          </StyledTable>
        </TableContainer>
      ) : (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666666', fontSize: '13px' }}>
          No vitals found
        </Box>
      )}
    </>
  );

  // If useCompactWrapper is false, return just the content without the wrapper
  if (!useCompactWrapper) {
    return vitalsContent;
  }

  return (
    <CompactDataComponent
      title="Vitals"
      isLoading={loading}
      error={error}
      initialExpanded={isExpanded}
      data={{
        vitals: vitalsData,
        selectedColumns: selectedColumns,
        isSelectiveMode: isSelectiveMode
      }}
      mode={mode}
      onDelete={handleDelete}
      onCopy={handleCopy}
      onExpand={handleExpand}
    >
      {vitalsContent}
    </CompactDataComponent>
  );
};
