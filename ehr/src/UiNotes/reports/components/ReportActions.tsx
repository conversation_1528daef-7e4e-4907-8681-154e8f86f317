import React from "react";
import { Box, Tooltip, IconButton } from "@mui/material";
import {
  Delete,
  History,
} from "@mui/icons-material";
import { StyledActionButton, actionIconStyles, disabledIconStyles } from "./styles";

interface ReportActionsProps {
  onDelete: () => void;
  onVersionHistory: () => void;
  isReportSelected: boolean;
  isEditorEmpty: boolean;
  selectedReport?: any; // Add selected report to check signature status
}

export const ReportActions: React.FC<ReportActionsProps> = ({
  onDelete,
  onVersionHistory,
  isReportSelected,
  isEditorEmpty,
  selectedReport,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        gap: 0,
        alignItems: "center",
        justifyContent: "flex-end",
        width: "100%",
        position: "sticky",
        top: 0,
        backgroundColor: "#fff",
        padding: "8px 4px",
        zIndex: 1,
        borderBottom: "1px solid #e0e0e0",
      }}
    >
      <Tooltip
        title={isReportSelected ? "Version History" : "No Report Selected"}
      >
        <span>
          <StyledActionButton
            onClick={onVersionHistory}
            disabled={!isReportSelected}
          >
            <History sx={isReportSelected ? actionIconStyles : disabledIconStyles} />
          </StyledActionButton>
        </span>
      </Tooltip>
      {!selectedReport?.signed && (
        <Tooltip
          title={isReportSelected ? "Delete Report" : "No Report Selected"}
        >
          <span>
            <StyledActionButton onClick={onDelete} disabled={!isReportSelected}>
              <Delete sx={isReportSelected ? actionIconStyles : disabledIconStyles} />
            </StyledActionButton>
          </span>
        </Tooltip>
      )}
    </Box>
  );
};
