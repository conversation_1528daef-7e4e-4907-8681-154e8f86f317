import React from "react";
import { Box } from "@mui/material";
import { ReportActions } from "./ReportActions";

interface ReportToolbarProps {
  onSave: () => void;
  onSaveAs: () => void;
  onDelete: () => void;
  onVersionHistory: () => void;
  isReportSelected: boolean;
  isEditorEmpty: boolean;
  selectedReport?: any; // Add selected report to check signature status
}

export const ReportToolbar: React.FC<ReportToolbarProps> = ({
  onSave,
  onSaveAs,
  onDelete,
  onVersionHistory,
  isReportSelected,
  isEditorEmpty,
  selectedReport,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        borderBottom: "1px solid #e0e0e0",
      }}
    >
      <ReportActions
        onDelete={onDelete}
        onVersionHistory={onVersionHistory}
        isReportSelected={isReportSelected}
        isEditorEmpty={isEditorEmpty}
        selectedReport={selectedReport}
      />
    </Box>
  );
};
