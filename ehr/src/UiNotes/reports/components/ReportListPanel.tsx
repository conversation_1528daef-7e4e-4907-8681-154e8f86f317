import React, { useState } from "react";
import { Box, Typography, CircularProgress, Tooltip } from "@mui/material";
import { History as HistoryIcon } from "@mui/icons-material";
import ReportGroup from "./ReportGroup";
import {
  ResizablePanel,
  PanelContent,
  PanelAppBar,
  PanelToolbar,
  ReportListContainer,
  StyledRefreshIcon,
  ReportListRefreshButton,
  ReportListVersionHistoryButton,
} from "./styles";
import { LeftPanelDragHandle } from "./PanelDragHandles";
import ReportFilterBox from "./ReportFilterBox";
import { TimeRange } from "./TimeRangeFilter";
import VersionHistoryDialog from "./VersionHistoryDialog";
import { ReportAPI } from "../services/ReportAPI";
import { NoteReport } from "../types/NoteVersion";

interface ReportListPanelProps {
  panelWidth: number;
  isLoadingReports: boolean;
  isRefreshing: boolean;
  reportList: any[];
  selectedReport: any;
  onDragStart: (e: React.MouseEvent) => void;
  onRefresh: () => void;
  onReportClick: (report: any) => void;
  campus: string;
  patID: string;
}

export const ReportListPanel: React.FC<ReportListPanelProps> = ({
  panelWidth,
  isLoadingReports,
  isRefreshing,
  reportList,
  selectedReport,
  onDragStart,
  onRefresh,
  onReportClick,
  campus,
  patID,
}) => {
  const [filterText, setFilterText] = React.useState("");
  const [timeRange, setTimeRange] = React.useState<TimeRange>("all");
  const [isVersionHistoryOpen, setIsVersionHistoryOpen] = useState(false);

  const handleVersionHistory = async () => {
    if (selectedReport) {
      try {
        // Fetch version history to ensure it's available
        await ReportAPI.getHistory(campus, patID, selectedReport.noteID);
        setIsVersionHistoryOpen(true);
      } catch (error) {
        console.error("Error fetching version history:", error);
      }
    }
  };

  const handleVersionSelect = (version: NoteReport) => {
    if (version) {
      onReportClick(version);
    }
    setIsVersionHistoryOpen(false);
  };

  const filterReportsByTimeRange = (reports: any[]): any[] => {
    if (timeRange === "all") return reports;

    const now = new Date();
    const startDate = new Date();

    switch (timeRange) {
      case "today":
        startDate.setHours(0, 0, 0, 0);
        break;
      case "7days":
        startDate.setDate(now.getDate() - 7);
        break;
      case "14days":
        startDate.setDate(now.getDate() - 14);
        break;
      case "month":
        startDate.setMonth(now.getMonth() - 1);
        break;
      case "6months":
        startDate.setMonth(now.getMonth() - 6);
        break;
      case "year":
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    return reports.filter((report) => {
      const reportDate = new Date(report.creationTime);
      return reportDate >= startDate && reportDate <= now;
    });
  };

  const groupReportsByType = (
    reports: any[]
  ): { type: string; reports: any[] }[] => {
    // First filter by time range
    const timeFilteredReports = filterReportsByTimeRange(reports);

    // Sort reports by creationTime in descending order (newest first)
    const sortedReports = [...timeFilteredReports].sort((a, b) => {
      const timeA = new Date(a.creationTime).getTime();
      const timeB = new Date(b.creationTime).getTime();
      return timeB - timeA;
    });

    // Group the sorted reports by type
    const groupedReports = sortedReports.reduce(
      (groups: { [key: string]: any[] }, report) => {
        const type = report.type || "Other";
        if (!groups[type]) {
          groups[type] = [];
        }
        groups[type].push(report);
        return groups;
      },
      {}
    );

    // Convert to array format and sort groups by type name
    return Object.entries(groupedReports)
      .map(([type, reports]) => ({
        type,
        reports,
      }))
      .sort((a, b) => a.type.localeCompare(b.type));
  };

  return (
    <ResizablePanel
      className="report-list-panel"
      sx={{
        height: "100%",
        boxSizing: "border-box",
      }}
    >
      <LeftPanelDragHandle onMouseDown={onDragStart} />
      <PanelContent sx={{ width: "100%", boxSizing: "border-box" }}>
        <ReportListContainer sx={{ width: "100%", boxSizing: "border-box" }}>
          <PanelAppBar position="static" sx={{ backgroundColor: "#45B766" }}>
            <PanelToolbar>
              <Typography
                variant="h6"
                sx={{
                  fontSize: "15px",
                  fontFamily: "Roboto",
                  fontWeight: 500,
                  color: "#ffffff",
                  paddingLeft: "5px",
                }}
              >
                Reports
              </Typography>
              <Box sx={{ flex: 1 }} />
              <Box sx={{ display: "flex", gap: 1 }}>
                <Tooltip title="Refresh Report List">
                  <span>
                    <ReportListRefreshButton
                      onClick={onRefresh}
                      disabled={isLoadingReports}
                    >
                      <StyledRefreshIcon
                        isrefreshing={isRefreshing.toString()}
                      />
                    </ReportListRefreshButton>
                  </span>
                </Tooltip>
                <Tooltip title="Version History">
                  <span>
                    <ReportListVersionHistoryButton
                      onClick={handleVersionHistory}
                      disabled={!selectedReport}
                    >
                      <HistoryIcon />
                    </ReportListVersionHistoryButton>
                  </span>
                </Tooltip>
              </Box>
            </PanelToolbar>
          </PanelAppBar>
          <Box sx={{ px: 0.625, py: 0.625, pt: 1.25 }}>
            <ReportFilterBox
              filterText={filterText}
              timeRange={timeRange}
              onFilterChange={setFilterText}
              onTimeRangeChange={setTimeRange}
            />
            {isLoadingReports ? (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <CircularProgress />
              </Box>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 1,
                  mt: 1,
                }}
              >
                {groupReportsByType(reportList).map((group) => {
                  const filteredReports =
                    filterText.length >= 3
                      ? group.reports.filter((report) => {
                        const title = (report.title || "").toLowerCase();
                        const searchTerms = filterText
                          .toLowerCase()
                          .split(/\s+/);
                        return searchTerms.every((term) =>
                          title.includes(term)
                        );
                      })
                      : group.reports;
                  if (filteredReports.length === 0) return null;
                  return (
                    <ReportGroup
                      key={group.type}
                      type={group.type}
                      reports={filteredReports}
                      onReportClick={onReportClick}
                      selectedReport={selectedReport}
                      campus={campus}
                      patID={patID}
                      onDelete={onRefresh}
                      onRefresh={onRefresh}
                    />
                  );
                })}
              </Box>
            )}
          </Box>
        </ReportListContainer>
      </PanelContent>

      {selectedReport && (
        <VersionHistoryDialog
          open={isVersionHistoryOpen}
          onClose={() => setIsVersionHistoryOpen(false)}
          campus={campus}
          patID={patID}
          noteID={selectedReport.noteID}
          onVersionSelect={handleVersionSelect}
        />
      )}
    </ResizablePanel>
  );
};
