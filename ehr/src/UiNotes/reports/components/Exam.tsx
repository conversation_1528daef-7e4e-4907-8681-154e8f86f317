import React, { useState, useEffect } from 'react';
import { <PERSON>, Typography, IconButton, Dialog, DialogContent, DialogTitle, Button, Snackbar, Alert } from '@mui/material';
import { MedicalServices, Close, Save } from '@mui/icons-material';
import { getDynamicPatientId } from '../Constants';
import { CompactDataComponent } from './CompactDataComponent';
import ExamComponent from '../../exam/ExamComponent';
import { getCampus, getPatID, getVisitKey } from '../../common/utils/runtimeUtils';
import { ExamAPI } from '../services/ExamAPI';
import { useAtomValue } from 'jotai';
import { selectedDataAtom } from '../context/SelectedDataContext';

interface ExamData {
  exam_data?: string;
  vitals_reviewed?: boolean;
  nursing_note_reviewed?: boolean;
}

interface ExamProps {
  patientId?: string;
  editor?: any;
  mode?: 'copy' | 'delete';
  onDelete?: () => void;
  initialExpanded?: boolean;
}

export const Exam: React.FC<ExamProps> = ({
  patientId = getDynamicPatientId(),
  editor,
  mode = 'copy',
  onDelete,
  initialExpanded = false
}) => {
  const [examData, setExamData] = useState<ExamData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const [isExamDialogOpen, setIsExamDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const examComponentRef = React.useRef<any>(null);

  const selectedData = useAtomValue(selectedDataAtom);
  const selectedExamData = selectedData.exam_data;

  // Load existing exam data when component mounts
  useEffect(() => {
    const loadExamData = async () => {
      try {
        setLoading(true);
        setError(null);

        const campus = getCampus();
        const patID = getPatID();
        const visitkey = getVisitKey();

        const examData = await ExamAPI.getExam(campus, patID, visitkey);

        setExamData(examData);
      } catch (error) {
        // This is expected if no exam data exists yet
        setError(null); // Don't show error for missing data
      } finally {
        setLoading(false);
      }
    };

    loadExamData();
  }, []);

  const handleExpand = () => {
    // No server query needed for Exam
  };

  const getTabDisplayName = (tabName: string): string => {
    const tabNameMap: { [key: string]: string } = {
      'HEENT': 'HEENT',
      'Neck': 'Neck',
      'Cardio': 'Cardiovascular',
      'PULM': 'Pulmonary',
      'Abd': 'Abdominal',
      'GU/AR': 'Genitourinary/Anorectal',
      'Musc': 'Musculoskeletal',
      'UPPER': 'Upper Extremities',
      'LOWER': 'Lower Extremities'
    };

    return tabNameMap[tabName] || tabName;
  };

  const formatExamDataForDisplay = (data: any) => {
    // Handle both new format (examData) and legacy format (exam_data)
    const examData = data.examData || data.exam_data;
    if (!data || !examData) return null;

    const sections: string[] = [];

    // Add vitals and nursing note status
    if (data.vitalsReviewed) {
      sections.push('✓ Vital signs reviewed');
    }
    if (data.nursingNoteReviewed) {
      sections.push('✓ Nursing note reviewed');
    }

    // Process exam data from different tabs - only show selected findings
    Object.keys(examData).forEach(tabName => {
      if (tabName === 'ALL') return; // Skip ALL tab as it's just references

      const tabData = examData[tabName];
      if (Array.isArray(tabData) && tabData.length > 0) {
        const tabFindings: string[] = [];

        tabData.forEach((card: any) => {
          if (card.sections && card.sections.length > 0) {
            card.sections.forEach((section: any) => {
              section.rows.forEach((row: any) => {
                if (row.type === 'checkbox' && row.value === true) {
                  tabFindings.push(row.label);
                } else if (row.type === 'plusminus' && row.value) {
                  const symbol = row.value === '+' ? '+' : row.value === '-' ? '-' : '';
                  if (symbol) {
                    const prefix = symbol === '+' ? 'Positive for ' : 'Negative for ';
                    tabFindings.push(prefix + row.label);
                  }
                } else if (row.type === 'multiCheckbox' && row.checkboxes && Array.isArray(row.checkboxes)) {
                  // Process multiCheckbox rows - find checked checkboxes
                  const checkedCheckboxes = row.checkboxes.filter((checkbox: any) => checkbox.value === true);
                  if (checkedCheckboxes.length > 0) {
                    const checkboxLabels = checkedCheckboxes.map((checkbox: any) => checkbox.label);
                    tabFindings.push(`${row.label}: ${checkboxLabels.join(', ')}`);
                  }
                }
              });
            });
          }

          // Add additional notes if present
          if (card.notes && card.notes.trim()) {
            tabFindings.push(card.notes.trim());
          }
        });

        // Only add the tab if it has findings
        if (tabFindings.length > 0) {
          // Add newline only if there are previous sections
          if (sections.length > 0) {
            sections.push(`\n**${getTabDisplayName(tabName)}:** ${tabFindings.join('. ')}`);
          } else {
            sections.push(`**${getTabDisplayName(tabName)}:** ${tabFindings.join('. ')}`);
          }
        }
      }
    });

    return sections.join('\n');
  };

  const handleCopy = () => {
    const dataToUse = examData || selectedExamData;
    if (!dataToUse) return;

    let textToCopy = 'Physical Exam:\n';

    // Check if we have examData (new format) or exam_data (legacy format)
    const hasExamData = dataToUse.examData || dataToUse.exam_data;

    if (hasExamData) {
      const formattedData = formatExamDataForDisplay(dataToUse);
      if (formattedData) {
        textToCopy += formattedData;
      } else {
        textToCopy += 'No exam findings documented';
      }
    } else {
      textToCopy += 'No exam data found';
    }

    if (editor) {
      editor.insertText(textToCopy);
    } else {
      navigator.clipboard.writeText(textToCopy);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  const handleSaveExam = async () => {
    // Close the dialog immediately
    setIsExamDialogOpen(false);

    // Show saving message
    setSaveMessage({ type: 'success', text: 'Saving exam data...' });

    try {
      setIsSaving(true);

      // Get exam data from the ExamComponent including current checkbox states
      const examDataForSave = examComponentRef.current?.() || examData || {
        vitalsReviewed: false,
        nursingNoteReviewed: false,
        examData: null
      };

      // Get staffid and ccitoken from CCI environment
      const cci = window.cci || window.Cci;
      const staffid = cci?.util?.Staff?.getSid?.() || cci?.Staff?.getSid?.() || 0;
      const ccitoken = localStorage.getItem("webtoken") || window.sessionStorage.getItem("webtoken") || "";

      const campus = getCampus();
      const patID = getPatID();
      const visitkey = getVisitKey();

      // Call the ExamAPI to save the data
      await ExamAPI.updateExam(
        campus,
        patID,
        visitkey,
        examDataForSave,
        staffid,
        ccitoken
      );

      // Update the local exam data state with the saved data
      setExamData(examDataForSave);

      // Show success message
      setSaveMessage({ type: 'success', text: 'Exam data saved successfully!' });

    } catch (error) {
      console.error('Error saving exam:', error);
      setSaveMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'Failed to save exam data'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderExamContent = () => {
    const dataToUse = examData || selectedExamData;

    if (loading) {
      return (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666' }}>
          <Typography variant="body2" sx={{ fontSize: '15px', fontFamily: 'Roboto, sans-serif' }}>
            Loading exam data...
          </Typography>
        </Box>
      );
    }

    if (!dataToUse) {
      return (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666' }}>
          <Typography variant="body2" sx={{ marginBottom: 1, fontSize: '15px', fontFamily: 'Roboto, sans-serif' }}>
            No exam data found
          </Typography>
          <Typography variant="body2" sx={{ fontSize: '15px', fontFamily: 'Roboto, sans-serif', color: '#999' }}>
            Click the exam icon to create and save physical exam data
          </Typography>
        </Box>
      );
    }

    // Check if we have examData (new format) or exam_data (legacy format)
    const hasExamData = dataToUse.examData || dataToUse.exam_data;

    if (!hasExamData) {
      return (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666' }}>
          <Typography variant="body2" sx={{ fontSize: '15px', fontFamily: 'Roboto, sans-serif' }}>
            No exam findings documented
          </Typography>
        </Box>
      );
    }

    const formattedData = formatExamDataForDisplay(dataToUse);
    if (!formattedData) {
      return (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666' }}>
          <Typography variant="body2" sx={{ fontSize: '15px', fontFamily: 'Roboto, sans-serif' }}>
            No exam findings documented
          </Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ padding: '12px' }}>
        <Typography
          variant="body2"
          sx={{
            whiteSpace: 'pre-line',
            fontFamily: 'Roboto, Arial, sans-serif',
            fontSize: '15px',
            lineHeight: 1.1,
            color: '#333',
            '& strong': {
              fontWeight: 'bold'
            }
          }}
          dangerouslySetInnerHTML={{
            __html: formattedData.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          }}
        />
      </Box>
    );
  };

  return (
    <>
      <CompactDataComponent
        title="Exam"
        isLoading={loading}
        error={error}
        initialExpanded={initialExpanded}
        data={examData || selectedExamData || undefined}
        mode={mode}
        onDelete={handleDelete}
        onCopy={handleCopy}
        onExpand={handleExpand}
        disableExamDialog={true}
        onExamIconClick={() => setIsExamDialogOpen(true)}
      >
        {renderExamContent()}
      </CompactDataComponent>

      <Dialog
        open={isExamDialogOpen}
        onClose={() => setIsExamDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            height: '90vh',
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#3D6EBF',
          color: 'white',
          padding: '12px 24px'
        }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', fontSize: '16px' }}>
            Physical Exam
          </Typography>
          <IconButton onClick={() => setIsExamDialogOpen(false)} sx={{ color: 'white', padding: '4px' }}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {(() => {
              const campus = getCampus();
              const patID = getPatID();
              const visitkey = getVisitKey();
              return (
                <ExamComponent
                  campus={campus}
                  patID={patID}
                  visitkey={visitkey}
                  onGetSaveData={(getDataFn) => {
                    examComponentRef.current = getDataFn;
                  }}
                />
              );
            })()}
          </Box>
          <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="outlined"
              onClick={() => setIsExamDialogOpen(false)}
              size="medium"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSaveExam}
              disabled={isSaving}
              size="medium"
            >
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
          </Box>
        </DialogContent>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={!!saveMessage}
        autoHideDuration={3000}
        onClose={() => setSaveMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSaveMessage(null)}
          severity={saveMessage?.type || 'info'}
          sx={{ width: '100%' }}
        >
          {saveMessage?.text}
        </Alert>
      </Snackbar>
    </>
  );
}; 