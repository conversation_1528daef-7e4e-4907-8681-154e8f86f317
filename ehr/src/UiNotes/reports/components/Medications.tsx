import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { getDynamicPatientId, API_BASE_URL, DEFAULT_TIME_RANGE_SECONDS } from '../Constants';
import { CompactDataComponent } from './CompactDataComponent';

/**
 * Interface for medication data
 */
export interface MedicationData {
  fsname: string;
  dose_value: string;
  dose_unit: string;
  key: string;
}

/**
 * Props for the Medications component
 */
interface MedicationsProps {
  patientId?: string;
  startTime?: number;
  endTime?: number;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
}

/**
 * Styled table for medications (compact version)
 */
const StyledTable = styled(Table)({
  '& .MuiTableCell-root': {
    padding: '2px 4px',
    fontSize: '15px',
    fontFamily: 'Roboto',
    borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
    '&.MuiTableCell-head': {
      backgroundColor: '#f0f0f0',
      fontWeight: 500,
      color: '#333333',
    },
    '&.MuiTableCell-body': {
      backgroundColor: '#ffffff',
      color: '#333333',
    },
  },
  "& .MuiTableRow-root:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  },
});

/**
 * Formats a Unix timestamp to the format "0000 27 May 2025"
 */
const formatTimestamp = (timestamp: string | number): string => {
  try {
    const date = new Date(Number(timestamp) * 1000); // Convert Unix timestamp to milliseconds
    if (isNaN(date.getTime())) {
      console.error("Invalid timestamp:", timestamp);
      return "Invalid Time";
    }
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const month = date.toLocaleString("en-US", { month: "short" });
    const year = date.getFullYear();
    return `${hours}${minutes} ${month} ${day} ${year}`;
  } catch (error) {
    console.error("Error formatting time:", error);
    return "Invalid Time";
  }
};

/**
 * Medications component that displays medication data
 */
export const Medications: React.FC<MedicationsProps> = ({
  patientId = getDynamicPatientId(),
  startTime,
  endTime,
  editor,
  mode = 'copy',
  onDelete
}) => {
  const [medications, setMedications] = useState<MedicationData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  const fetchMedications = useCallback(async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      const endTimeValue = endTime || Math.floor(Date.now() / 1000);
      const startTimeValue = startTime || (endTimeValue - DEFAULT_TIME_RANGE_SECONDS); // Default to last 3 months if not provided

      const url = `${API_BASE_URL}/getData?object=MedOrder&patid=${patientId}&type=UnifiedCCO`;

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('webtoken'),
          'X-Forwarded-For': '127.0.0.1',
          'X-Real-IP': '127.0.0.1',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch medications: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch medications data');
      }

      if (!Array.isArray(result.data)) {
        throw new Error('Invalid data format received from API');
      }

      // Use Set for deduplication
      const seenNames = new Set();
      const uniqueMeds = result.data.reduce((acc: MedicationData[], item: any) => {
        const name = item.fsname || item.name || '';
        if (!seenNames.has(name)) {
          seenNames.add(name);
          acc.push({
            fsname: name,
            dose_value: item.dose_value || item.dose || '',
            dose_unit: item.dose_unit || item.unit || '',
            key: item.key || item.id || ''
          });
        }
        return acc;
      }, []);

      setMedications(uniqueMeds);
    } catch (err) {
      console.error('Error fetching medications:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch medications');
    } finally {
      setLoading(false);
    }
  }, [patientId, startTime, endTime]);

  const handleExpand = () => {
    if (!isExpanded) {
      fetchMedications();
    }
  };

  // Fetch medications on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchMedications();
    }
  }, [patientId, startTime, endTime, isExpanded, fetchMedications]);

  const handleCopy = () => {
    if (medications.length === 0) return;

    const medicationsText = medications
      .map(med => `${med.fsname || 'N/A'} - ${med.dose_value || 'N/A'} ${med.dose_unit || ''}`)
      .join('\n');

    const textToCopy = `Medications:\n${medicationsText}`;

    if (editor) {
      editor.insertText(textToCopy);
    } else {
      navigator.clipboard.writeText(textToCopy);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <CompactDataComponent
      title="Medications"
      isLoading={loading}
      error={error}
      initialExpanded={isExpanded}
      data={{ inpatientMedications: medications }}
      mode={mode}
      onDelete={handleDelete}
      onCopy={handleCopy}
      onExpand={handleExpand}
    >
      {medications.length > 0 ? (
        <TableContainer component={Paper} sx={{ boxShadow: 'none', border: 'none' }}>
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Medication</TableCell>
                <TableCell>Dose</TableCell>
                <TableCell>Unit</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {medications.map((medication, index) => (
                <TableRow key={index}>
                  <TableCell>{medication.fsname || ''}</TableCell>
                  <TableCell>{medication.dose_value || ''}</TableCell>
                  <TableCell>{medication.dose_unit || ''}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      ) : (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666666', fontSize: '13px' }}>
          No medications found
        </Box>
      )}
    </CompactDataComponent>
  );
};
