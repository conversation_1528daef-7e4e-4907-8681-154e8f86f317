import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Tooltip,
} from "@mui/material";
import { getDynamicPatientId } from "../Constants";
import { styled } from "@mui/material/styles";
import { CompactDataComponent } from "./CompactDataComponent";

export interface LabResultsData {
  lab_panel_name?: string;
  lab_collection_time?: string;
  labresult_name?: string;
  labresult_value?: string;
  labresult_unit?: string;
  labresult_status?: string;
  labresult_flag?: string;
}

interface GroupedLabResult {
  lab_panel_name: string;
  lab_collection_time: string;
  results: LabResultsData[];
}

interface LabResultsProps {
  patientId?: string;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
  onCopy?: () => void;
  startTime?: number;
  endTime?: number;
}

// Styled table for lab results (compact version)
const StyledTable = styled(Table)({
  "& .MuiTableCell-root": {
    padding: "2px 4px",
    fontSize: "15px",
    fontFamily: "Roboto",
    borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
    "&.MuiTableCell-head": {
      backgroundColor: "#f0f0f0",
      fontWeight: 500,
      color: "#333333",
    },
    "&.MuiTableCell-body": {
      backgroundColor: "#ffffff",
      color: "#333333",
    },
  },
  "& .MuiTableRow-root:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  },
});

export const LabResults: React.FC<LabResultsProps> = ({
  patientId = getDynamicPatientId(),
  editor,
  mode = "copy",
  onDelete,
  onCopy,
  startTime,
  endTime,
}) => {
  const [labResults, setLabResults] = useState<LabResultsData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  const fetchLabResults = async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      let url = `${cci.cfg.baseUrl}/index.php/Api/getData?object=LabResults&patid=${patientId}&type=UnifiedCCO`;

      if (startTime && endTime) {
        url += `&starttime=${startTime}&endtime=${endTime}`;
      }

      const response = await fetch(url, {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch lab results: ${response.status} ${response.statusText}`
        );
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to fetch lab results data");
      }

      if (!Array.isArray(result.data)) {
        throw new Error("Invalid data format received from API");
      }

      setLabResults(result.data);
    } catch (err) {
      console.error("Error fetching lab results:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch lab results"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleExpand = () => {
    if (!isExpanded) {
      fetchLabResults();
    }
  };

  // Group lab results by panel name and collection time to avoid duplicates
  const groupLabResults = (results: LabResultsData[]): GroupedLabResult[] => {
    const grouped: { [key: string]: GroupedLabResult } = {};

    results.forEach((result) => {
      const key = `${result.lab_panel_name || "Unknown"}-${result.lab_collection_time || "Unknown"}`;

      if (!grouped[key]) {
        grouped[key] = {
          lab_panel_name: result.lab_panel_name || "Unknown",
          lab_collection_time: result.lab_collection_time || "Unknown",
          results: [],
        };
      }

      grouped[key].results.push(result);
    });

    return Object.values(grouped);
  };

  // Create tooltip content showing all related lab results
  const createTooltipContent = (results: LabResultsData[]) => {
    return (
      <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
        {results.map((result, index) => {
          const name = result.labresult_name || "N/A";
          const value = result.labresult_value || "N/A";
          const unit = result.labresult_unit || "";
          const flag = result.labresult_flag || "";

          // Check if the value is abnormal (flag is not empty and not just whitespace)
          const isAbnormal = flag && flag.trim() !== "";

          return (
            <Box key={index} sx={{ display: "flex", alignItems: "center" }}>
              <span>• </span>
              <span style={{ color: "#2563eb", fontWeight: 500 }}>{name}</span>
              <span>: </span>
              {isAbnormal ? (
                <>
                  <span style={{ color: "#d32f2f" }}>{value}</span>
                  <span style={{ color: "#333333" }}> {unit}</span>
                  <span style={{ color: "#d32f2f", marginLeft: "4px" }}>
                    ({flag})
                  </span>
                </>
              ) : (
                <span style={{ color: "#333333" }}>
                  {value} {unit}
                </span>
              )}
            </Box>
          );
        })}
      </Box>
    );
  };

  // Fetch lab results on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchLabResults();
    }
  }, [patientId, startTime, endTime]);

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <CompactDataComponent
      title="Lab Results"
      isLoading={loading}
      error={error}
      initialExpanded={isExpanded}
      data={{ labResults }}
      mode={mode}
      onDelete={handleDelete}
      onExpand={handleExpand}
    >
      {labResults.length > 0 ? (
        <TableContainer
          component={Paper}
          sx={{ boxShadow: "none", border: "none" }}
        >
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Collection Time</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groupLabResults(labResults).map((groupedLab, index) => (
                <Tooltip
                  key={index}
                  title={
                    <Box sx={{ maxWidth: 300 }}>
                      {createTooltipContent(groupedLab.results)}
                    </Box>
                  }
                  placement="right"
                  arrow
                  componentsProps={{
                    tooltip: {
                      sx: {
                        backgroundColor: "#f5f5f5 !important",
                        color: "#333333 !important",
                        fontSize: "13px",
                        border: "1px solid #cccccc",
                        boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                      },
                    },
                    arrow: {
                      sx: {
                        color: "#f5f5f5 !important",
                      },
                    },
                  }}
                >
                  <TableRow sx={{ cursor: "help" }}>
                    <TableCell>{groupedLab.lab_panel_name}</TableCell>
                    <TableCell>{groupedLab.lab_collection_time}</TableCell>
                  </TableRow>
                </Tooltip>
              ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      ) : (
        <Box
          sx={{
            padding: "12px",
            textAlign: "center",
            color: "#666666",
            fontSize: "13px",
          }}
        >
          No lab results found
        </Box>
      )}
    </CompactDataComponent>
  );
};
