import React, { useState, useEffect } from 'react';
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';
import { getDynamicPatientId, API_BASE_URL } from '../Constants';
import { styled } from '@mui/material/styles';
import { CompactDataComponent } from './CompactDataComponent';

interface Problem {
  name: string;
  status: string;
  onset_date: string;
  icd10_code: string;
}

interface ProblemsProps {
  patientId?: string;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
  initialExpanded?: boolean;
}

// Styled table for problems (compact version)
const StyledTable = styled(Table)({
  '& .MuiTableCell-root': {
    padding: '2px 4px',
    fontSize: '15px',
    fontFamily: 'Roboto',
    borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
    '&.MuiTableCell-head': {
      backgroundColor: '#f0f0f0',
      fontWeight: 500,
      color: '#333333',
    },
    '&.MuiTableCell-body': {
      backgroundColor: '#ffffff',
      color: '#333333',
    },
  },
  "& .MuiTableRow-root:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  },
});

export const Problems: React.FC<ProblemsProps> = ({
  patientId = getDynamicPatientId(),
  editor,
  mode = "copy",
  onDelete,
  initialExpanded = false,
}) => {
  const [problems, setProblems] = useState<Problem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(initialExpanded);

  const fetchProblems = async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      const url = `${API_BASE_URL}/getData?object=Problems&patid=${patientId}&type=UnifiedCCO`;

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('webtoken'),
          'X-Forwarded-For': '127.0.0.1',
          'X-Real-IP': '127.0.0.1',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch problems data: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch problems data');
      }

      if (!Array.isArray(result.data)) {
        throw new Error('Invalid data format received from API');
      }

      setProblems(result.data);
    } catch (err) {
      console.error('Error fetching problems:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch problems');
    } finally {
      setLoading(false);
    }
  };

  const handleExpand = () => {
    if (!isExpanded) {
      fetchProblems();
    }
  };

  // Fetch problems on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchProblems();
    }
  }, [patientId]);

  const handleCopy = () => {
    if (problems.length === 0) return;

    const problemsText = problems
      .map(problem => `${problem.name} (${problem.status}, ${problem.onset_date})`)
      .join('\n');

    const textToCopy = `Problems:\n${problemsText}`;

    if (editor) {
      editor.insertText(textToCopy);
    } else {
      navigator.clipboard.writeText(textToCopy);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <CompactDataComponent
      title="Problems"
      isLoading={loading}
      error={error}
      initialExpanded={isExpanded}
      data={problems as unknown as Record<string, unknown>}
      mode={mode}
      onDelete={handleDelete}
      onCopy={handleCopy}
      onExpand={handleExpand}
    >
      {problems.length > 0 ? (
        <TableContainer component={Paper} sx={{ boxShadow: 'none', border: 'none' }}>
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Problem</TableCell>
                <TableCell>Onset Date</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {problems.map((problem, index) => (
                <TableRow key={index}>
                  <TableCell>{problem.name || 'N/A'}</TableCell>
                  <TableCell>{problem.onset_date || 'N/A'}</TableCell>
                  <TableCell>{problem.status || 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      ) : (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666666', fontSize: '13px' }}>
          No problems found
        </Box>
      )}
    </CompactDataComponent>
  );
};
