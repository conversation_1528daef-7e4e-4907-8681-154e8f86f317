import React from 'react';
import { Box, Typography, I<PERSON><PERSON>utton, Tooltip } from '@mui/material';
import { Clear as ClearIcon } from '@mui/icons-material';
import { Editor } from '@tiptap/core';
import { ChatMessageType } from './chat/ChatMessage';
import { ResizablePanel, PanelContent, StyledChatBox } from './styles';
import { LeftPanelDragHandle } from './PanelDragHandles';

interface ChatPanelProps {
  panelWidth: number;
  messages: ChatMessageType[];
  inputValue: string;
  isProcessing: boolean;
  editor: Editor | null;
  onDragStart: (e: React.MouseEvent) => void;
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onInputKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onSend: () => void;
  onClearHistory: () => void;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({
  panelWidth,
  messages,
  inputValue,
  isProcessing,
  editor,
  onDragStart,
  onInputChange,
  onInputKeyDown,
  onSend,
  onClearHistory,
}) => {
  return (
    <ResizablePanel
      className="chat-panel"
      sx={{
        width: `${panelWidth}%`,
        border: "1px solid rgba(0, 0, 0, 0.12)",
        padding: "5px",
        margin: "0px 5px 5px 5px",
        borderRadius: "4px",
      }}
    >
      <LeftPanelDragHandle onMouseDown={onDragStart} />
      <PanelContent>
        <StyledChatBox>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              padding: "8px 16px",
              borderBottom: "1px solid #e0e0e0",
              backgroundColor: "#3C4B50",
              color: "white",
            }}
          >
            <Typography variant="h6" sx={{ color: "white" }}>AI Chat Box</Typography>
            <Box>
              <Tooltip title="Clear Chat History">
                <IconButton onClick={onClearHistory} size="small" sx={{ color: "white" }}>
                  <ClearIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              height: "calc(100% - 48px)",
            }}
          >
            <Box
              sx={{
                flex: 1,
                overflowY: "auto",
                padding: "16px",
                display: "flex",
                flexDirection: "column",
                gap: "16px",
              }}
            >
              {messages.map((message) => (
                <Box
                  key={message.id}
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: message.isUser ? "flex-end" : "flex-start",
                  }}
                >
                  <Box
                    sx={{
                      maxWidth: "80%",
                      padding: "8px 12px",
                      borderRadius: "8px",
                      backgroundColor: message.isUser ? "#e3f2fd" : "#f5f5f5",
                      color: message.isUser ? "#1976d2" : "#333",
                    }}
                  >
                    {message.content}
                  </Box>
                  <Typography
                    variant="caption"
                    sx={{
                      marginTop: "4px",
                      color: "#666",
                    }}
                  >
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </Typography>
                </Box>
              ))}
            </Box>
            <Box
              sx={{
                padding: "16px",
                borderTop: "1px solid #e0e0e0",
              }}
            >
              <Box
                sx={{
                  position: "relative",
                  display: "flex",
                  alignItems: "flex-start",
                }}
              >
                <Box
                  component="textarea"
                  value={inputValue}
                  onChange={onInputChange}
                  onKeyDown={onInputKeyDown}
                  placeholder="Type your message..."
                  disabled={isProcessing}
                  sx={{
                    width: "100%",
                    minHeight: "36px",
                    maxHeight: "80px",
                    padding: "8px 40px 8px 8px",
                    borderRadius: "4px",
                    border: "1px solid #e0e0e0",
                    resize: "none",
                    "&:focus": {
                      outline: "none",
                      borderColor: "#1976d2",
                    },
                    "&::-webkit-scrollbar": {
                      display: "none",
                    },
                    scrollbarWidth: "none",
                    msOverflowStyle: "none",
                  }}
                />
                <IconButton
                  onClick={onSend}
                  disabled={!inputValue.trim() || isProcessing}
                  sx={{
                    position: "absolute",
                    right: "4px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    color: inputValue.trim() && !isProcessing ? "#1976d2" : "#ccc",
                    padding: "4px",
                    "&:hover": {
                      backgroundColor: "rgba(25, 118, 210, 0.04)",
                    },
                  }}
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"
                      fill="currentColor"
                    />
                  </svg>
                </IconButton>
              </Box>
            </Box>
          </Box>
        </StyledChatBox>
      </PanelContent>
    </ResizablePanel>
  );
}; 