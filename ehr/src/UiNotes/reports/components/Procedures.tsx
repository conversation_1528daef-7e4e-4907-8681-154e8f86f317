import React, { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Box, Typography } from '@mui/material';
import { getDynamicPatientId, API_BASE_URL } from '../Constants';
import { styled } from '@mui/material/styles';
import { CompactDataComponent } from './CompactDataComponent';

export interface ProcedureData {
  name?: string;
  datePerformed?: string;
  status?: string;
  snomedCode?: string;
  provider?: string;
  summary?: string;
  priority?: string;
  key?: string;
  starttime?: string;
}

interface ProceduresProps {
  patientId?: string;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
  startTime?: number;
  endTime?: number;
}

// Styled table for procedures (compact version)
const StyledTable = styled(Table)({
  '& .MuiTableCell-root': {
    padding: '2px 4px',
    fontSize: '15px',
    fontFamily: 'Roboto',
    borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
    '&.MuiTableCell-head': {
      backgroundColor: '#f0f0f0',
      fontWeight: 500,
      color: '#333333',
    },
    '&.MuiTableCell-body': {
      backgroundColor: '#ffffff',
      color: '#333333',
    },
  },
  "& .MuiTableRow-root:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  },
});

export const Procedures: React.FC<ProceduresProps> = ({
  patientId = getDynamicPatientId(),
  editor,
  mode = 'copy',
  onDelete,
  startTime,
  endTime,
}) => {
  const [procedures, setProcedures] = useState<ProcedureData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  const fetchProcedures = async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      let url = `${API_BASE_URL}/getData?object=ProcedureOrder&patid=${patientId}&type=UnifiedCCO`;

      if (startTime && endTime) {
        url += `&starttime=${startTime}&endtime=${endTime}`;
      }

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('webtoken'),
          'X-Forwarded-For': '127.0.0.1',
          'X-Real-IP': '127.0.0.1',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch procedures: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch procedures data');
      }

      if (!Array.isArray(result.data)) {
        throw new Error('Invalid data format received from API');
      }

      setProcedures(result.data);
    } catch (err) {
      console.error('Error fetching procedures:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch procedures');
    } finally {
      setLoading(false);
    }
  };

  const handleExpand = () => {
    if (!isExpanded) {
      fetchProcedures();
    }
  };

  // Fetch procedures on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchProcedures();
    }
  }, [patientId, startTime, endTime]);

  const handleCopy = () => {
    if (procedures.length === 0) return;

    const proceduresText = procedures
      .map(proc => `${proc.name || 'N/A'} - ${proc.starttime || 'N/A'}`)
      .join('\n');

    const textToCopy = `Procedures:\n${proceduresText}`;

    if (editor) {
      editor.insertText(textToCopy);
    } else {
      navigator.clipboard.writeText(textToCopy);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <CompactDataComponent
      title="Procedures"
      isLoading={loading}
      error={error}
      initialExpanded={isExpanded}
      data={{ procedures }}
      mode={mode}
      onDelete={handleDelete}
      onCopy={handleCopy}
      onExpand={handleExpand}
    >
      {procedures.length > 0 ? (
        <TableContainer component={Paper} sx={{ boxShadow: 'none', border: 'none' }}>
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Date Performed</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {procedures.map((procedure, index) => (
                <TableRow key={index}>
                  <TableCell>{procedure.name || 'N/A'}</TableCell>
                  <TableCell>{procedure.starttime || 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      ) : (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666666', fontSize: '13px' }}>
          No procedures found
        </Box>
      )}
    </CompactDataComponent>
  );
};
