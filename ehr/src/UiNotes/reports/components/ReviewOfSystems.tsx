import React, { useState, useEffect, useCallback } from "react";
import { Box, Typography } from "@mui/material";
import { getDynamicPatientId, API_BASE_URL } from "../Constants";
import { CompactDataComponent } from "./CompactDataComponent";
import { ReviewOfSystemsDialog } from "./ReviewOfSystemsDialog";
import { getPNGridScreenConf, getPNGridScreenData } from "../../../PNEditGridRs/util/PNGridData";

interface ReviewOfSystemsData {
  // Grid data structure from PNEditGridRs
  label: string;
  rows: Array<{
    id: number;
    name: string;
    positive: boolean;
    negative: boolean;
    anno?: string;
    newanno?: string;
  }>;
  isNA: boolean;
  jit?: string | number;
  nit?: string | number;
  key?: string | number;
}

interface ReviewOfSystemsProps {
  patientId?: string;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
  initialExpanded?: boolean;
}

export const ReviewOfSystems: React.FC<ReviewOfSystemsProps> = ({
  patientId = getDynamicPatientId(),
  editor,
  mode = "copy",
  onDelete,
  initialExpanded = false,
}) => {
  const [rosData, setRosData] = useState<ReviewOfSystemsData[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const [isRosDialogOpen, setIsRosDialogOpen] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const fetchReviewOfSystems = useCallback(async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      // Get the dbpath for the current patient
      const dbpath = Cci?.Patient?.getDbpath();
      if (!dbpath) {
        console.error("No patient selected");
        setRosData(null);
        return;
      }

      // Fetch PNEditGridRs configuration and data
      const screenConf = await getPNGridScreenConf();
      if (!screenConf || !screenConf.conf || !screenConf.conf.data || screenConf.conf.data.length === 0) {
        console.error("Failed to get PNEditGridRs configuration");
        setRosData(null);
        return;
      }

      // Get the grid data
      const gridData = await getPNGridScreenData(screenConf, dbpath);
      if (!gridData || !Array.isArray(gridData) || gridData.length === 0) {
        console.log("No Review of Systems data available");
        setRosData(null);
        return;
      }

      setRosData(gridData);
    } catch (error) {
      console.error("Error fetching review of systems data:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch review of systems"
      );
    } finally {
      setLoading(false);
    }
  }, [patientId]);

  const handleExpand = () => {
    if (!isExpanded) {
      fetchReviewOfSystems();
    }
  };

  // Fetch review of systems on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchReviewOfSystems();
    }
  }, [patientId, isExpanded, fetchReviewOfSystems]);

  const handleCopy = () => {
    if (!rosData || !Array.isArray(rosData)) return;

    const sections: string[] = [];

    // Process each grid item (section)
    rosData.forEach((item) => {
      if (!item || !item.label || !item.rows || !Array.isArray(item.rows)) {
        return;
      }

      // Skip if the entire section is marked as N/A
      if (item.isNA) {
        return;
      }

      const sectionFindings: string[] = [];

      // Process each row in the section
      item.rows.forEach((row) => {
        if (!row || !row.name) {
          return;
        }

        if (row.positive) {
          sectionFindings.push(`Positive for ${row.name}`);
        } else if (row.negative) {
          sectionFindings.push(`Negative for ${row.name}`);
        }
      });

      // Only add the section if it has findings
      if (sectionFindings.length > 0) {
        sections.push(`${item.label}: ${sectionFindings.join('. ')}`);
      }
    });

    if (sections.length === 0) {
      const textToCopy = "Review of Systems:\nNo review of systems findings documented.";
      if (editor) {
        editor.insertText(textToCopy);
      } else {
        navigator.clipboard.writeText(textToCopy);
      }
      return;
    }

    const textToCopy = `Review of Systems:\n${sections.join("\n")}`;

    if (editor) {
      editor.insertText(textToCopy);
    } else {
      navigator.clipboard.writeText(textToCopy);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };



  return (
    <>
      <CompactDataComponent
        title="Review of Systems"
        isLoading={loading}
        error={error}
        initialExpanded={isExpanded}
        data={rosData as unknown as Record<string, unknown>}
        mode={mode}
        onDelete={handleDelete}
        onCopy={handleCopy}
        onExpand={handleExpand}
        disableExamDialog={true}
        onExamIconClick={() => setIsRosDialogOpen(true)}
      >
        {rosData && Array.isArray(rosData) && rosData.length > 0 ? (
          <Box sx={{ padding: "8px" }}>
            {rosData.map((item) => {
              if (!item || !item.label || !item.rows || !Array.isArray(item.rows)) {
                return null;
              }

              // Skip if the entire section is marked as N/A
              if (item.isNA) {
                return null;
              }

              const sectionFindings: string[] = [];

              // Process each row in the section
              item.rows.forEach((row) => {
                if (!row || !row.name) {
                  return;
                }

                if (row.positive) {
                  sectionFindings.push(`Positive for ${row.name}`);
                } else if (row.negative) {
                  sectionFindings.push(`Negative for ${row.name}`);
                }
              });

              // Only render the section if it has findings
              if (sectionFindings.length > 0) {
                return (
                  <Box key={item.label} sx={{ marginBottom: "4px" }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#333",
                        fontSize: "15px",
                        fontFamily: "Roboto, sans-serif",
                        lineHeight: 1.4,
                        "& strong": {
                          fontWeight: 700,
                          color: "#000",
                        },
                      }}
                    >
                      <strong>{item.label}:</strong> {sectionFindings.join('. ')}
                    </Typography>
                  </Box>
                );
              }

              return null;
            })}
          </Box>
        ) : (
          <Box
            sx={{
              padding: "12px",
              textAlign: "center",
              color: "#666666",
              fontSize: "15px",
              fontFamily: "Roboto, sans-serif",
            }}
          >
            No review of systems data found
          </Box>
        )}
      </CompactDataComponent>

      <ReviewOfSystemsDialog
        open={isRosDialogOpen}
        onClose={() => setIsRosDialogOpen(false)}
        onSave={(data) => {
          // Update the local data with the saved data
          if (data && Array.isArray(data)) {
            setRosData(data);
          } else {
            // If no data provided, refresh from server
            fetchReviewOfSystems();
          }
        }}
        onSaveMessage={(message) => {
          setSaveMessage(message);
          // Auto-hide the message after 3 seconds
          setTimeout(() => setSaveMessage(null), 3000);
        }}
      />

      {/* Success/Error Message */}
      {saveMessage && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            backgroundColor: saveMessage.type === 'success' ? '#4caf50' : '#f44336',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '4px',
            zIndex: 10000,
            boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
          }}
        >
          {saveMessage.text}
        </Box>
      )}
    </>
  );
};
