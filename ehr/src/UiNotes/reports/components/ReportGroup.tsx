import React, { useState } from "react";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Tooltip,
  Alert,
  Snackbar,
} from "@mui/material";
import { ExpandMore, ExpandLess, Delete, Create } from "@mui/icons-material";
import { ReportAPI } from "../services/ReportAPI";
import { useSignatureConfig } from "../hooks/useSignatureConfig";
import { SignatureDialog } from "./SignatureDialog";

interface ReportGroupProps {
  type: string;
  reports: any[];
  onReportClick?: (report: any) => void;
  selectedReport?: any;
  campus: string;
  patID: string;
  onDelete?: () => void;
  onRefresh?: () => void; // Add refresh callback for after signing
}

const ReportGroup: React.FC<ReportGroupProps> = ({
  type,
  reports,
  onReportClick,
  selectedReport,
  campus,
  patID,
  onDelete,
  onRefresh,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<any>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [signatureDialogOpen, setSignatureDialogOpen] = useState(false);
  const [reportToSign, setReportToSign] = useState<any>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  const { isSignableType } = useSignatureConfig();

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  };

  const handleDeleteClick = (e: React.MouseEvent, report: any) => {
    e.stopPropagation();
    setReportToDelete(report);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!reportToDelete) return;

    try {
      setIsDeleting(true);
      const cci = window.Cci || window.cci;
      const staffid = cci ? cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";
      await ReportAPI.deleteReport(
        campus,
        patID,
        reportToDelete.noteID,
        reportToDelete.type || type, // Pass note type for NIT
        staffid,
        ccitoken
      );
      onDelete?.(); // Refresh the list after successful deletion
      showSnackbar("Report deleted successfully", "success");
    } catch (error) {
      console.error("Failed to delete report:", error);
      showSnackbar("Failed to delete report", "error");
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setReportToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setReportToDelete(null);
  };

  const handleSignClick = (e: React.MouseEvent, report: any) => {
    e.stopPropagation();
    setReportToSign(report);
    setSignatureDialogOpen(true);
  };

  const handleSignConfirm = async (comment: string) => {
    if (!reportToSign) return;

    try {
      const staffid = Cci ? Cci.util.Staff.getSid() : undefined;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";

      await ReportAPI.signReport(
        campus,
        patID,
        reportToSign.noteID,
        reportToSign.type || type, // Use report type or fallback to group type
        comment,
        staffid,
        ccitoken
      );

      showSnackbar("Report signed successfully", "success");
      onRefresh?.(); // Refresh the reports list to show updated signature status
    } catch (error) {
      console.error("Failed to sign report:", error);
      throw error; // Re-throw to let SignatureDialog handle the error display
    } finally {
      setSignatureDialogOpen(false);
      setReportToSign(null);
    }
  };

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          p: 0.5,
          backgroundColor: "#3D6EBF",
          borderRadius: "0",
          cursor: "pointer",
          "&:hover": {
            backgroundColor: "#2d5ba8",
          },
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? (
          <ExpandLess sx={{ color: "#ffffff", mr: 0.5 }} />
        ) : (
          <ExpandMore sx={{ color: "#ffffff", mr: 0.5 }} />
        )}
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: "medium",
            fontSize: "15px",
            fontFamily: "Roboto",
            color: "#ffffff",
          }}
        >
          {type} ({reports.length})
        </Typography>
      </Box>
      {isExpanded && (
        <List sx={{ pl: 1 }}>
          {reports.map((report) => (
            <ListItem
              key={report.noteID}
              sx={{
                borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
                "&:last-child": {
                  borderBottom: "none",
                },
                "&:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                },
                cursor: "pointer",
                py: 0.35,
                px: 0.75,
                backgroundColor:
                  selectedReport?.noteID === report.noteID
                    ? "#F9EEBC"
                    : "transparent",
                position: "relative",
              }}
              onClick={() => onReportClick?.(report)}
            >
              <ListItemText
                primary={
                  <Typography
                    sx={{
                      fontSize: "15px",
                      fontFamily: "Roboto",
                      color: "#000000",
                      lineHeight: 1.3,
                    }}
                  >
                    {report.title || "Untitled Report"}
                  </Typography>
                }
                sx={{ m: 0 }}
              />
              <Box sx={{ display: "flex", gap: 0.5 }}>
                {report.required_signature && !report.signed && (
                  <Tooltip title="Sign Report">
                    <IconButton
                      size="small"
                      onClick={(e) => handleSignClick(e, report)}
                      sx={{
                        opacity: 0,
                        transition: "opacity 0.2s",
                        "&:hover": {
                          backgroundColor: "rgba(0, 0, 0, 0.08)",
                        },
                        ".MuiListItem-root:hover &": {
                          opacity: 1,
                        },
                      }}
                    >
                      <Create fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                {!report.signed && (
                  <Tooltip title="Delete Report">
                    <IconButton
                      data-testid="delete-button"
                      size="small"
                      onClick={(e) => handleDeleteClick(e, report)}
                      sx={{
                        opacity: 0,
                        transition: "opacity 0.2s",
                        "&:hover": {
                          backgroundColor: "rgba(0, 0, 0, 0.08)",
                        },
                        ".MuiListItem-root:hover &": {
                          opacity: 1,
                        },
                      }}
                    >
                      <Delete fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </ListItem>
          ))}
        </List>
      )}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
        <DialogTitle>Delete Report</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "
            {reportToDelete?.title || "Untitled Report"}"? This action cannot be
            undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={isDeleting}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {reportToSign && (
        <SignatureDialog
          open={signatureDialogOpen}
          onClose={() => {
            setSignatureDialogOpen(false);
            setReportToSign(null);
          }}
          onSign={handleSignConfirm}
          reportTitle={reportToSign.title || "Untitled Report"}
          reportType={reportToSign.type || type}
        />
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ReportGroup;
