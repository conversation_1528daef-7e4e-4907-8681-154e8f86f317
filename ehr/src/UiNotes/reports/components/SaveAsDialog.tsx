import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from "@mui/material";

interface SaveAsDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (title: string) => void;
}

export const SaveAsDialog: React.FC<SaveAsDialogProps> = ({
  open,
  onClose,
  onSave,
}) => {
  const [title, setTitle] = useState("");

  const handleSave = () => {
    if (title.trim()) {
      onSave(title.trim());
      setTitle("");
      onClose();
    }
  };

  const handleClose = () => {
    setTitle("");
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Save Report As</DialogTitle>
      <DialogContent sx={{ minWidth: 400, padding: 3 }}>
        <TextField
          autoFocus
          margin="dense"
          label="Report Title"
          type="text"
          fullWidth
          variant="outlined"
          size="medium"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSave();
            }
          }}
          sx={{ marginTop: 1 }}
        />
      </DialogContent>
      <DialogActions sx={{ padding: 2 }}>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSave} color="primary" disabled={!title.trim()}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 