import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Grid,
  Paper,
} from '@mui/material';
import { Close, Save, Refresh, Comment } from '@mui/icons-material';
import { getCampus, getPatID, getVisitKey } from '../../common/utils/runtimeUtils';
import { ThemeProvider, styled } from '@mui/material/styles';
import EhrTheme from '../../../theme/theme';
import { getPNGridScreenConf, getPNGridScreenData } from '../../../PNEditGridRs/util/PNGridData';
import WaterfallLayout from '../../../PNEditGridRs/components/WaterfallLayout';
import { AnnoDiglog } from '../../../PNEditGridRs/components/AnnoDiglog';
import { GirdDialog } from '../../../PNEditGridRs/components/GridDialog';
import ConfirmDlg from '../../../PNEditGridRs/components/ConfirmDlg';
import { savePNGridData } from '../../../PNEditGridRs/util/PNGridData';
import { CommonToast, useSetToastFamily } from '@cci-monorepo/common';
import theme from '@cci-monorepo/Registration/styles/Theme';
import { updateDirtyTab, cancelFn, discardFn, saveFn } from '../../../PNEditGridRs/util/dirty';
import _ from 'lodash';
import '../styles/global.css';

// Styled component for proper layout
const RosContentRoot = styled("div")(({ theme }) => ({
  position: "relative",
  height: "100%",
  width: "100%",
  overflow: "auto",
  fontFamily: "Roboto, Arial, sans-serif",
  "& .MuiMasonry-root": {
    width: "100% !important",
  },
  "& .MuiBox-root": {
    fontFamily: "Roboto, Arial, sans-serif !important",
  },
  "& .MuiTypography-root": {
    fontFamily: "Roboto, Arial, sans-serif !important",
  },
  "& .MuiButton-root": {
    fontFamily: "Roboto, Arial, sans-serif !important",
  },
  "& .MuiIconButton-root": {
    fontFamily: "Roboto, Arial, sans-serif !important",
  },
  "& .MuiCheckbox-root": {
    fontFamily: "Roboto, Arial, sans-serif !important",
  },
  "& .MuiFormControlLabel-root": {
    fontFamily: "Roboto, Arial, sans-serif !important",
  },
}));

interface ReviewOfSystemsDialogProps {
  open: boolean;
  onClose: () => void;
  onSave?: (data: any) => void;
  onSaveMessage?: (message: { type: 'success' | 'error', text: string }) => void;
}

export const ReviewOfSystemsDialog: React.FC<ReviewOfSystemsDialogProps> = ({
  open,
  onClose,
  onSave,
  onSaveMessage
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);

  // Get patient information
  const dbpath = Cci?.Patient?.getDbpath();

  // State for PNEditGridRs functionality
  const [gridData, setGridData] = useState<any[]>([]);
  const [buttonSetting, setButtonSettting] = useState<any[]>([]);
  const [initData, setInitData] = useState<any[]>([]);
  const [isDirty, setIsDirty] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [openList, setOpenList] = useState(false);
  const [openAnno, setOpenAnno] = useState(false);
  const [annoData, setAnnoData] = useState({});
  const [valueTip, setValueTip] = useState<any>("");
  const [isDisable, setIsDisable] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const setToast = useSetToastFamily("ReviewOfSystemsDialog");
  const [refreshId, setRefreshId] = useState<any>(null);
  const [dirtyLeaveCpsCallback, setDirtyLeaveCpsCallback] = useState<any>({
    callback: null,
  });

  const isNoCommit =
    JSON.stringify(_.map(initData, (item) => _.omit(item, "isNA"))) ===
    JSON.stringify(_.map(gridData, (item) => _.omit(item, "isNA")));

  const [contentWidth, setContentWidth] = useState(containerWidth - 20);
  const minWidth = 320; // Adjusted for 3 columns in md dialog
  const columnCount = Math.max(1, Math.floor(contentWidth / minWidth));

  useEffect(() => {
    if (containerRef.current) {
      // Try to get dimensions immediately
      const updateDimensions = () => {
        if (containerRef.current) {
          const { width, height } = containerRef.current.getBoundingClientRect();
          setContainerWidth(width);
          setContainerHeight(height);
        }
      };

      // Try immediately
      updateDimensions();

      // If no width, try again with a small delay
      if (containerRef.current.offsetWidth === 0) {
        const timer = setTimeout(updateDimensions, 50);
        return () => clearTimeout(timer);
      }
    }
  }, [open]);

  useEffect(() => {
    // Use more of the available width, accounting for padding
    setContentWidth(containerWidth - 40);
  }, [containerWidth]);

  // Add resize listener to handle window resizing and container changes
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        const height = containerRef.current.offsetHeight;
        if (width > 0) {
          setContainerWidth(width);
          setContainerHeight(height);
        }
      }
    };

    // Use ResizeObserver if available for more reliable container size detection
    let resizeObserver: ResizeObserver | null = null;
    if (containerRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const width = entry.contentRect.width;
          const height = entry.contentRect.height;
          if (width > 0) {
            setContainerWidth(width);
            setContainerHeight(height);
          }
        }
      });
      resizeObserver.observe(containerRef.current);
    }

    // Also try to get the dialog's full width by checking parent containers
    const getFullDialogWidth = () => {
      if (containerRef.current) {
        // Try to find the dialog container
        let element: HTMLElement | null = containerRef.current;
        while (element && element.parentElement) {
          element = element.parentElement;
          if (element.classList.contains('MuiDialog-paper') ||
            element.classList.contains('MuiDialog-root')) {
            const dialogWidth = element.offsetWidth;
            if (dialogWidth > 0 && dialogWidth > containerRef.current.offsetWidth) {
              setContainerWidth(dialogWidth - 80); // Account for padding
              break;
            }
          }
        }
      }
    };

    // Try to get full width after a short delay
    setTimeout(getFullDialogWidth, 100);

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    if (open && dbpath) {
      // Wait for container width to be available, but don't wait too long
      const checkAndFetch = () => {
        if (containerWidth > 0) {
          fetchData();
        } else {
          // If container width is still 0 after 500ms, proceed anyway
          setTimeout(() => {
            if (containerWidth === 0) {
              setContainerWidth(1200); // Set a larger default width for more columns
            }
            fetchData();
          }, 500);
        }
      };

      // Add a delay to ensure the dialog is fully rendered
      const timer = setTimeout(checkAndFetch, 100);
      return () => clearTimeout(timer);
    } else if (!open) {
      // Reset state when dialog closes
      setGridData([]);
      setInitData([]);
      setIsLoading(false);
      setIsDirty(false);
    }
  }, [dbpath, open, containerWidth]);

  const makeSaveParams = (
    conf: any,
    data: string | null,
    anno: string | null
  ): any => {
    let param: any = {
      type: conf.type,
      hobj: conf.savehobj,
      secname: conf.secname,
      appname: "OB_CLINIC",
      perm: "E",
      dbpath: conf.dbpath,
      visitkey: conf.key,
      admitkey: conf.key,
    };
    let fsdata: any = { jit: conf.jit, nit: conf.nit, ks: conf.key };
    if (data !== null) {
      fsdata.data = data;
    } else {
      fsdata.data = "";
    }
    if (anno !== null) {
      fsdata.anno = anno;
    }
    param.fsdata = JSON.stringify(fsdata);

    return param;
  };

  const fetchData = () => {
    setIsLoading(true);
    getPNGridScreenConf()
      .then((screenConf) => {
        if (
          !screenConf ||
          !screenConf.conf ||
          !screenConf.conf.data ||
          screenConf.conf.data.length === 0
        ) {
          setIsLoading(false);
          return;
        }
        const buttons: any = [];
        _.map(screenConf.conf.data, (conf) => {
          if (conf[2] === "button") {
            buttons.push({
              index: conf[0],
              type: conf[7],
              event: conf[11],
            });
          }
        });
        setButtonSettting(buttons);
        getPNGridScreenData(screenConf, dbpath)
          .then((gridsConfig) => {
            setGridData(gridsConfig);
            setInitData(gridsConfig);
            setIsLoading(false);
          })
          .catch((error) => {
            setIsLoading(false);
          });
      })
      .catch((error) => {
        setIsLoading(false);
      });
  };

  const changeData = (
    id: string | number,
    type: string,
    colId?: string | number
  ) => {
    const res = _.cloneDeep(gridData);
    const list = _.map(res, (element) => {
      if (element.jit === id) {
        if (type === "setNull") {
          element.isNA = !element.isNA;
          element.rows.map((row: any) => {
            row.positive = false;
            row.negative = false;
            return row;
          });
        } else if (type === "add" || type === "sub") {
          element.isNA = false;
          if (type === "add") {
            element.rows.map((row: any) => {
              if (row.id === colId) {
                row.positive = !row.positive;
                row.negative = false;
              }
              return row;
            });
          } else {
            element.rows.map((row: any) => {
              if (row.id === colId) {
                row.negative = !row.negative;
                row.positive = false;
              }
              return row;
            });
          }
        }
      }
      return element;
    });

    setGridData(list);
  };

  const handlerData = (type: string) => {
    if (type === "Refresh") {
      if (!isNoCommit) {
        setRefreshId(null);
        setOpenConfirm(true);
      } else {
        setGridData(initData);
      }
    } else if (type === "Save") {
      saveAllData();
    } else if (type === "Annotation") {
      setValueTip("");
      setOpenList(true);
    }
  };

  const handleData = (item: any) => {
    let data: string[] | null = null;
    let firstRow = item?.rows?.[0];
    let anno: string | null =
      firstRow.newanno === firstRow.anno ? null : firstRow.newanno;
    let cellchanged = false;
    item?.rows.forEach((row: any) => {
      const orgData = row.id % 100;
      const val = (row.positive ? 10 : 0) + (row.negative ? 1 : 0);
      if (val !== orgData) {
        cellchanged = true;
      }
    });
    if (cellchanged) {
      data = [];
      item?.rows.forEach((row: any) => {
        if (row.positive) {
          data && data.push("+" + row.name);
        } else if (row.negative) {
          data && data.push("-" + row.name);
        }
      });
    }
    const param = makeSaveParams(
      item,
      data === null ? null : data.join(","),
      anno
    );
    return param;
  };

  async function saveAllData() {
    var authInputWin = Ext.create("Auth.view.AuthInputWindow");
    authInputWin.action = handleSave;
    var authParams = {
      app: "F19",
      perm: "E",
      func: "",
      msg: "Save review of systems change?",
    };
    authInputWin.doAuthen(authParams);
  }

  const handleSave = () => {
    const diffData: any = [];
    _.map(initData, (element) => {
      _.map(gridData, (gird) => {
        if (
          element.jit === gird.jit &&
          JSON.stringify(element) !== JSON.stringify(gird)
        ) {
          diffData.push(gird);
        }
      });
    });
    if (!diffData || !diffData.length) {
      return;
    }

    const commmits: any = [];

    _.map(diffData, (element) => {
      const data = handleData(element);
      commmits.push(savePNGridData(data));
    });

    setIsDisable(true);
    Promise.all(commmits)
      .then((ret) => {
        if (ret[0] && ret[0]?.ExecOk === true) {
          setToast({
            open: true,
            text: "Save succeeded.",
            type: "success",
          });
          fetchData();
        }
      })
      .catch((error) => {
        // console.log("Save data failed: ", error);
      })
      .finally(() => {
        setIsDisable(false);
      });
  };

  const handleSingleSave = (id: number | string) => {
    const res = _.find(gridData, (item) => String(item.jit) === String(id));
    const commmits: any = [];
    const data = handleData(res);
    commmits.push(savePNGridData(data));
    Promise.all(commmits)
      .then((ret) => {
        if (ret[0] && ret[0]?.ExecOk === true) {
          setToast({
            open: true,
            text: "Save succeeded.",
            type: "success",
          });
          refreshInitData(id);
        }
      })
      .catch((error) => {
        // console.log("Save data failed: ", error);
      });
  };

  const refreshSingleEvent = (id: number | string, isNeedSave: boolean) => {
    if (isNeedSave) {
      setRefreshId(id);
      setOpenConfirm(true);
    } else {
      refreshData(id);
    }
  };

  const refreshInitData = (id: number | string) => {
    const index = _.findIndex(
      initData,
      (item) => String(item.jit) === String(id)
    );
    const res = _.cloneDeep(initData);
    res[index] = gridData[index];
    setInitData(res);
  };

  const refreshData = (id: number | string) => {
    const index = _.findIndex(
      initData,
      (item) => String(item.jit) === String(id)
    );
    const res = _.cloneDeep(gridData);
    res[index] = initData[index];
    setGridData(res);
  };

  const handleText = (id: number | string, text: string) => {
    setOpenAnno(false);
    const res = _.cloneDeep(gridData);
    if (id && (text || text === "")) {
      const list = _.map(res, (item) => {
        if (String(item.jit) === String(id)) {
          _.map(item.rows, (row) => {
            row.newanno = text;
            return row;
          });
        }
        return item;
      });
      setGridData(list);
    }
  };

  const openAnnoData = (id: number | string, isWrite?: boolean) => {
    const res = _.find(gridData, (item) => String(item.jit) === String(id));
    setOpenAnno(true);
    setAnnoData({
      label: res?.label,
      readonly: isWrite ? false : true,
      jit: id,
      anno: res?.rows?.[0]?.newanno,
      handleText,
    });
  };

  const gridProps = {
    initData,
    items: gridData,
    columns: columnCount,
    changeData,
    openAnnoData,
    handleSingleSave,
    refreshSingleEvent,
  };

  const annoProps = {
    open: openAnno,
    handleClose: () => {
      setOpenAnno(false);
    },
    handleText,
    ...annoData,
  };

  const annoListProps = {
    data: gridData,
    setValueTip,
    open: openList,
    handleClose: () => {
      setValueTip("");
      setOpenList(false);
    },
    submit: () => {
      setOpenList(false);
      if (valueTip) {
        openAnnoData(valueTip, true);
      }
    },
  };

  const confirmProps = {
    openWin: openConfirm,
    show: setOpenConfirm,
    onRefresh: () => {
      setOpenConfirm(false);
      if (refreshId) {
        refreshData(refreshId);
      } else {
        setGridData(initData);
        if (isDirty) {
          discardFn();
          dirtyLeaveCpsCallback &&
            dirtyLeaveCpsCallback.callback &&
            dirtyLeaveCpsCallback.callback();
        }
      }
    },
    onOK: () => {
      setOpenConfirm(false);
      if (refreshId) {
        handleSingleSave(refreshId);
      } else {
        saveAllData();
        if (isDirty) {
          saveFn();
        }
      }
    },
    onCancel: () => {
      setOpenConfirm(false);
      if (isDirty) {
        cancelFn();
      }
    },
  };

  const handleSaveClick = async () => {
    try {
      setIsSaving(true);

      // Call the existing save functionality
      await saveAllData();

      // Call the onSave callback with the updated data
      if (onSave) {
        onSave(gridData);
      }

      // Close the dialog immediately after successful save
      onClose();

      // Pass success message to parent component
      if (onSaveMessage) {
        onSaveMessage({ type: 'success', text: 'Review of Systems saved successfully' });
      }
    } catch (error) {
      console.error('Error saving Review of Systems:', error);
      if (onSaveMessage) {
        onSaveMessage({ type: 'error', text: 'Failed to save Review of Systems' });
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    setSaveMessage(null);
    onClose();
  };

  return (
    <>
      <style>
        {`
          .ros-dialog .MuiMasonry-root {
            width: 100% !important;
          }
          .ros-dialog .MuiBox-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
          .ros-dialog .MuiTypography-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
          .ros-dialog .MuiButton-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
          .ros-dialog .MuiIconButton-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
          .ros-dialog .MuiCheckbox-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
          .ros-dialog .MuiFormControlLabel-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
          .ros-dialog .MuiDivider-root {
            margin: 10px 0 !important;
          }
          .ros-dialog .MuiPaper-root {
            font-family: 'Roboto', Arial, sans-serif !important;
          }
        `}
      </style>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth={false}
        className="ros-dialog"
        PaperProps={{
          sx: {
            height: '90vh',
            maxHeight: '90vh',
            width: '90vw',
            maxWidth: '1000px'
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#3D6EBF',
          color: 'white',
          padding: '12px 24px'
        }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', fontSize: '16px' }}>
            Review of Systems
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              onClick={() => handlerData("Annotation")}
              sx={{ color: 'white', padding: '4px' }}
              title="Annotation"
            >
              <Comment />
            </IconButton>
            {_.some(buttonSetting, (item) =>
              _.includes(_.toLower(item?.event), _.toLower("Refresh"))
            ) && (
                <IconButton
                  onClick={() => handlerData("Refresh")}
                  sx={{ color: 'white', padding: '4px' }}
                  title="Refresh"
                >
                  <Refresh />
                </IconButton>
              )}
            {_.some(buttonSetting, (item) =>
              _.includes(_.toLower(item?.event), _.toLower("Save"))
            ) && (
                <IconButton
                  onClick={() => handlerData("Save")}
                  disabled={isDisable || isNoCommit}
                  sx={{ color: 'white', padding: '4px' }}
                  title="Save"
                >
                  <Save />
                </IconButton>
              )}
            <IconButton onClick={handleClose} sx={{ color: 'white', padding: '4px' }}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{
            flex: 1,
            overflow: 'auto',
            minHeight: '400px',
            position: 'relative'
          }} ref={containerRef}>
            {dbpath && dbpath.trim() ? (
              <ThemeProvider theme={EhrTheme}>
                <RosContentRoot>
                  <Box sx={{
                    p: 2,
                    minHeight: '100%',
                    width: '100%',
                    fontFamily: 'Roboto, Arial, sans-serif',
                    '& .MuiMasonry-root': {
                      width: '100% !important'
                    },
                    '& .MuiBox-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    },
                    '& .MuiTypography-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    },
                    '& .MuiButton-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    },
                    '& .MuiIconButton-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    },
                    '& .MuiCheckbox-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    },
                    '& .MuiFormControlLabel-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    },
                    '& .MuiDivider-root': {
                      margin: '10px 0 !important'
                    },
                    '& .MuiPaper-root': {
                      fontFamily: 'Roboto, Arial, sans-serif !important'
                    }
                  }}>
                    {!isLoading && gridData.length > 0 && containerWidth > 0 ? (
                      <WaterfallLayout {...gridProps} />
                    ) : (
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '200px',
                        color: 'text.secondary'
                      }}>
                        <Typography>
                          {isLoading ? 'Loading Review of Systems...' : containerWidth === 0 ? 'Initializing layout...' : 'No Review of Systems data available'}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                  <AnnoDiglog {...annoProps} />
                  <GirdDialog {...annoListProps} />
                  <ThemeProvider theme={theme}>
                    <ConfirmDlg {...confirmProps} />
                    <CommonToast
                      tag="ReviewOfSystemsDialog"
                      anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
                    />
                  </ThemeProvider>
                </RosContentRoot>
              </ThemeProvider>
            ) : (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary">
                  Please select a patient to view Review of Systems
                </Typography>
              </Box>
            )}
          </Box>

          <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="outlined"
              onClick={handleClose}
              size="medium"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSaveClick}
              disabled={isSaving}
              size="medium"
              sx={{ backgroundColor: '#3D6EBF' }}
            >
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
          </Box>
        </DialogContent>
      </Dialog>


    </>
  );
}; 