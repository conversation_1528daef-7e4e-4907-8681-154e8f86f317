import React, { useRef, useState, useCallback } from "react";
import { Box, IconButton, Tooltip } from "@mui/material";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import MenuIcon from "@mui/icons-material/Menu";
import PersonIcon from "@mui/icons-material/Person";
import ListIcon from "@mui/icons-material/List";
import { useEditorSetup } from "../hooks/useEditorSetup";
import { usePatientPanel } from "../hooks/usePatientPanel";
import { usePanel } from "../hooks/usePanel";
import { useReportGeneration } from "../hooks/useReportGeneration";
import { useReportManagement } from "../hooks/useReportManagement";
import { usePrintReport } from "../hooks/usePrintReport";
import { EditorContainer } from "./EditorContainer";
import { SplitEditor } from "./SplitEditor";
import { PatientPanel } from "./PatientPanel";
import { ReportListPanel } from "./ReportListPanel";
import { SaveAsDialog } from "./SaveAsDialog";
import {
  MIN_PANEL_WIDTH,
  MAX_PANEL_WIDTH,
  DEFAULT_LEFT_WIDTH,
  DEFAULT_RIGHT_WIDTH,
} from "../context/EditorContext";

const cci = window.Cci || window.cci;

interface EditorProps {
  timeRange?: {
    startTime: number;
    endTime: number;
  };
  onTimeRangeChange?: (timeRange: { startTime: number; endTime: number }) => void;
}

export const Editor: React.FC<EditorProps> = ({
  timeRange: externalTimeRange,
  onTimeRangeChange: externalOnTimeRangeChange
}) => {
  const editorRef = useRef(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const editor = useEditorSetup(editorRef, typingTimeoutRef);

  // Internal time range state management
  const [internalTimeRange, setInternalTimeRange] = useState(() => {
    // Default to last 3 months (to the minute)
    const now = new Date();
    const endDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes(), 0);
    const startDateTime = new Date(endDateTime.getTime() - (3 * 30 * 24 * 60 * 60) * 1000); // 3 months in seconds

    return {
      startTime: Math.floor(startDateTime.getTime() / 1000),
      endTime: Math.floor(endDateTime.getTime() / 1000),
    };
  });

  // Use external time range if provided, otherwise use internal state
  const timeRange = externalTimeRange || internalTimeRange;

  const handleTimeRangeChange = useCallback((newTimeRange: { startTime: number; endTime: number }) => {
    // Update internal state if no external handler
    if (!externalOnTimeRangeChange) {
      setInternalTimeRange(newTimeRange);
    } else {
      // Call external handler if provided
      externalOnTimeRangeChange(newTimeRange);
    }
  }, [externalOnTimeRangeChange]);

  // Get campus and patID directly from CCI variables, consistent with other parts of the codebase
  const campus = cci?.cfg?.campus || 'dod'; // Fallback to 'dod' if cfg.campus is not available
  const dbpath = cci?.util?.Patient?.getDbpath?.() || '';
  const patID = dbpath ? dbpath.substring(dbpath.lastIndexOf("p") + 1) : "";



  // Panel state
  const {
    isPanelOpen,
    panelWidth,
    isDragging,
    handlePanelToggle,
    handleDragStart,
    handleDragEnd,
    handleDrag,
  } = usePanel();

  // Patient panel state
  const {
    isPatientPanelOpen,
    patientPanelWidth,
    isPatientPanelDragging,
    handlePatientPanelToggle,
    handlePatientPanelDragStart,
    handlePatientPanelDragEnd,
    handlePatientPanelDrag,
  } = usePatientPanel();

  const [isLeftOpen, setIsLeftOpen] = useState(true);
  const [isRightOpen, setIsRightOpen] = useState(true);
  const [leftWidth, setLeftWidth] = useState(DEFAULT_LEFT_WIDTH);
  const [rightWidth, setRightWidth] = useState(DEFAULT_RIGHT_WIDTH);
  const dragRef = useRef<"left" | "right" | null>(null);

  // Editor refs and setup
  const dictationEditorRef = useRef<any>(null);
  const reportEditorRef = useRef<any>(null);

  // Initialize editors
  const dictationEditor = useEditorSetup(dictationEditorRef, typingTimeoutRef);
  const reportEditor = useEditorSetup(reportEditorRef, typingTimeoutRef, true);

  // Report management state
  const {
    reportList,
    isLoadingReports,
    selectedReport,
    isSaveAsDialogOpen,
    isVersionHistoryOpen,
    setSelectedReport,
    setIsSaveAsDialogOpen,
    setIsVersionHistoryOpen,
    fetchReportList,
    handleSaveReport,
    handleDeleteReport,
    handleSaveReportAs,
    handleVersionHistory,
    handleVersionSelect,
  } = useReportManagement({ campus, patID }, reportEditor);

  // Print functionality
  const { handlePrint } = usePrintReport(reportEditor);

  // Report generation state
  const { reportContent, handleGenerateReport, isGeneratingReport } =
    useReportGeneration(editor, editor, selectedReport);

  // Update report editor content when a report is selected
  React.useEffect(() => {
    if (selectedReport && reportEditor && !reportEditor.isDestroyed && !isGeneratingReport) {


      // Simply set the content without trying to restore cursor position
      // This prevents interference with text selection
      reportEditor.commands.setContent(selectedReport.content);
    }
  }, [selectedReport, reportEditor, isGeneratingReport]);

  // Mouse drag handlers
  const onMouseDown =
    (side: "left" | "right") => (e: React.MouseEvent<HTMLDivElement>) => {
      dragRef.current = side;
      document.body.style.cursor = "col-resize";
    };

  React.useEffect(() => {
    const onMouseMove = (e: MouseEvent) => {
      if (!dragRef.current) return;
      if (dragRef.current === "left" && isLeftOpen) {
        const newWidth = Math.min(
          Math.max(e.clientX, MIN_PANEL_WIDTH),
          MAX_PANEL_WIDTH
        );
        setLeftWidth(newWidth);
      } else if (dragRef.current === "right" && isRightOpen) {
        const winWidth = window.innerWidth;
        const newWidth = Math.min(
          Math.max(winWidth - e.clientX, MIN_PANEL_WIDTH),
          MAX_PANEL_WIDTH
        );
        setRightWidth(newWidth);
      }
    };
    const onMouseUp = () => {
      dragRef.current = null;
      document.body.style.cursor = "";
    };
    window.addEventListener("mousemove", onMouseMove);
    window.addEventListener("mouseup", onMouseUp);
    return () => {
      window.removeEventListener("mousemove", onMouseMove);
      window.removeEventListener("mouseup", onMouseUp);
    };
  }, [isLeftOpen, isRightOpen]);

  // Editor handlers
  const handleToggleRecording = useCallback(() => {
    // TODO: Implement recording toggle
  }, []);

  const handleRunMacro = useCallback(() => {
    // TODO: Implement macro functionality
  }, []);

  const handleClear = () => {
    if (dictationEditor) {
      dictationEditor.commands.clearContent();
    }
  };

  const handleSave = () => {
    handleSaveReport();
  };

  const onSaveAsClick = () => {
    setIsSaveAsDialogOpen(true);
  };

  return (
    <Box
      ref={editorRef}
      sx={{
        display: "flex",
        height: "100%",
        width: "100%",
        overflow: "hidden",
        position: "relative",
      }}
    >
      {/* Left Menu */}
      <Box
        sx={{
          width: 48,
          background: "#3C4B4F",
          borderRight: "1px solid #ddd",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          paddingTop: 2,
          gap: 1,
        }}
      >
        <Tooltip title="Toggle Report List" placement="right">
          <IconButton
            size="small"
            onClick={() => setIsLeftOpen(!isLeftOpen)}
            sx={{
              color: isLeftOpen ? "#ffffff" : "rgba(255, 255, 255, 0.7)",
              "&:hover": { background: "rgba(255, 255, 255, 0.1)" },
            }}
          >
            <ListIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Toggle Patient Info" placement="right">
          <IconButton
            size="small"
            onClick={() => setIsRightOpen(!isRightOpen)}
            sx={{
              color: isRightOpen ? "#ffffff" : "rgba(255, 255, 255, 0.7)",
              "&:hover": { background: "rgba(255, 255, 255, 0.1)" },
            }}
          >
            <PersonIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Left Panel */}
      {isLeftOpen && (
        <Box
          sx={{
            width: leftWidth,
            minWidth: MIN_PANEL_WIDTH,
            maxWidth: MAX_PANEL_WIDTH,
            background: "#f5f5f5",
            display: "flex",
            flexDirection: "column",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              flex: 1,
              overflow: "auto",
              width: "100%",
              display: "flex",
              flexDirection: "column",
              "& > *": { width: "100%" }, // Ensure all children take full width
            }}
          >
            <ReportListPanel
              panelWidth={leftWidth}
              isLoadingReports={isLoadingReports}
              isRefreshing={false}
              reportList={reportList}
              selectedReport={selectedReport}
              onDragStart={(e: React.MouseEvent) =>
                onMouseDown("left")(e as React.MouseEvent<HTMLDivElement>)
              }
              onRefresh={fetchReportList}
              onReportClick={setSelectedReport}
              campus={campus}
              patID={patID}
            />
          </Box>
        </Box>
      )}
      {/* Left Resizer */}
      {isLeftOpen && (
        <Box
          onMouseDown={onMouseDown("left")}
          sx={{
            width: 6,
            cursor: "col-resize",
            background: "#e0e0e0",
            zIndex: 10,
          }}
        />
      )}
      {/* Middle Panel */}
      <Box
        sx={{
          flex: 1,
          background: "#fff",
          display: "flex",
          flexDirection: "column",
          position: "relative",
          mt: 0,
        }}
      >
        <SplitEditor
          dictationEditor={dictationEditor}
          reportEditor={reportEditor}
          onPrint={handlePrint}
          onToggleRecording={handleToggleRecording}
          onRunMacro={handleRunMacro}
          onClear={handleClear}
          onGenerateReport={handleGenerateReport}
          onSave={handleSave}
          onSaveAs={onSaveAsClick}
          isEditorEmpty={!dictationEditor?.getText().trim()}
          isRecording={false}
          isReportSelected={!!selectedReport}
          isGeneratingReport={isGeneratingReport}
          selectedReport={selectedReport}
          onRefresh={fetchReportList}
          campus={campus}
          patID={patID}
        />
      </Box>
      {/* Right Resizer */}
      {isRightOpen && (
        <Box
          onMouseDown={onMouseDown("right")}
          sx={{
            width: 6,
            cursor: "col-resize",
            background: "#e0e0e0",
            zIndex: 10,
          }}
        />
      )}
      {/* Right Panel */}
      {isRightOpen && (
        <Box
          sx={{
            width: rightWidth,
            minWidth: MIN_PANEL_WIDTH,
            maxWidth: MAX_PANEL_WIDTH,
            background: "#f5f5f5",
            display: "flex",
            flexDirection: "column",
            position: "relative",
          }}
        >
          <Box sx={{ flex: 1, overflow: "auto" }}>
            <PatientPanel
              panelWidth={100}
              editor={reportEditor}
              onDragStart={(e: React.MouseEvent) =>
                onMouseDown("right")(e as React.MouseEvent<HTMLDivElement>)
              }
            />
          </Box>
        </Box>
      )}
      <SaveAsDialog
        open={isSaveAsDialogOpen}
        onClose={() => setIsSaveAsDialogOpen(false)}
        onSave={(title: string) => handleSaveReportAs(title)}
      />
    </Box>
  );
};
