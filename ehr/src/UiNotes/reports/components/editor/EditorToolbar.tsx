import React from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import {
  Save as SaveIcon,
  SaveAs as SaveAsIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  AutoFixHigh as AutoFixHighIcon,
  Spellcheck as SpellcheckIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  Code as CodeIcon,
  History as HistoryIcon,
} from '@mui/icons-material';

interface EditorToolbarProps {
  isListening: boolean;
  selectedReport: any;
  isAIFixing?: boolean;
  onSave: () => void;
  onSaveAs: () => void;
  onDelete: () => void;
  onRefresh: () => void;
  onClear: () => void;
  onGenerateReport: () => void;
  onAIFixDocument: () => void;
  onToggleRecording: () => void;
  onRunMacro: () => void;
  onVersionHistory: () => void;
}

export const EditorToolbar: React.FC<EditorToolbarProps> = ({
  isListening,
  selectedReport,
  isAIFixing = false,
  onSave,
  onSaveAs,
  onDelete,
  onRefresh,
  onClear,
  onGenerateReport,
  onAIFixDocument,
  onToggleRecording,
  onRunMacro,
  onVersionHistory,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px',
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: '#f5f5f5',
      }}
    >
      <Tooltip title="Save">
        <IconButton onClick={onSave} disabled={!selectedReport}>
          <SaveIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="Save As">
        <IconButton onClick={onSaveAs}>
          <SaveAsIcon />
        </IconButton>
      </Tooltip>

      {!selectedReport?.signed && (
        <Tooltip title="Delete">
          <IconButton onClick={onDelete} disabled={!selectedReport}>
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      )}

      <Tooltip title="Refresh">
        <IconButton onClick={onRefresh}>
          <RefreshIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="Clear">
        <IconButton onClick={onClear}>
          <ClearIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="Generate Report">
        <IconButton onClick={onGenerateReport}>
          <AutoFixHighIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="AI Fix Document - Fix grammar and typos in entire document">
        <IconButton onClick={onAIFixDocument} disabled={isAIFixing}>
          <SpellcheckIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="Run Macro">
        <IconButton onClick={onRunMacro}>
          <CodeIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title={isListening ? "Stop Recording" : "Start Recording"}>
        <IconButton onClick={onToggleRecording}>
          {isListening ? <MicOffIcon /> : <MicIcon />}
        </IconButton>
      </Tooltip>

      <Tooltip title="Version History">
        <IconButton onClick={onVersionHistory} disabled={!selectedReport}>
          <HistoryIcon />
        </IconButton>
      </Tooltip>
    </Box>
  );
}; 