import React, { useCallback, useRef } from 'react';
import { Box } from '@mui/material';
import { Editor } from '@tiptap/core';
import { EditorToolbar } from './EditorToolbar';
import { EditorContent } from './EditorContent';
import { EditorPanels } from './EditorPanels';
import { useAtom, useAtomValue } from 'jotai';
import { selectedDataAtom } from '../../context/SelectedDataContext';
import { useReportGeneration } from '../../hooks/useReportGeneration';
import { useEditorContent } from '../../hooks/useEditorContent';
import { ChatMessageType } from '../chat/ChatMessage';
import { ReportListPanel } from '../ReportListPanel';
import {
  isPanelOpenAtom,
  panelWidthAtom,
  isDraggingAtom,
  isPatientPanelOpenAtom,
  patientPanelWidthAtom,
  isPatientPanelDragging<PERSON>tom,
  isReportList<PERSON><PERSON><PERSON>tom,
  messages<PERSON>tom,
  inputValue<PERSON><PERSON>,
  isRef<PERSON><PERSON><PERSON>,
  report<PERSON>ontent<PERSON><PERSON>,
  selectedReport<PERSON>tom,
  isLoadingReportsAtom,
  reportListAtom,
  timeRangeAtom,
} from '../../context/EditorContext';
import { ResizablePanel, PanelContent } from '../styles';
import { List, Chat, Person } from '@mui/icons-material';

// Constants
const MIN_PANEL_WIDTH = 300;
const MAX_PANEL_WIDTH = 35;
const DEFAULT_PANEL_WIDTH = 20;
const MIN_PATIENT_PANEL_WIDTH = 600;
const MAX_PATIENT_PANEL_WIDTH = 60;
const DEFAULT_PATIENT_PANEL_WIDTH = 33.33;

interface EditorContainerProps {
  editor: Editor | null;
  onSave: () => void;
  onSaveAs: () => void;
  onDelete: () => void;
  onRefresh: () => void;
  onClear: () => void;
  onToggleRecording: () => void;
  onRunMacro: () => void;
  onVersionHistory: () => void;
  isListening: boolean;
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onInputKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onSend: () => void;
  onClearHistory: () => void;
  onReportClick: (report: any) => void;
  onTimeRangeChange: (timeRange: { startTime: number; endTime: number }) => void;
  onAIFixDocument: () => void;
}

export const EditorContainer: React.FC<EditorContainerProps> = ({
  editor,
  onSave,
  onSaveAs,
  onDelete,
  onRefresh,
  onClear,
  onToggleRecording,
  onRunMacro,
  onVersionHistory,
  isListening,
  onInputChange,
  onInputKeyDown,
  onSend,
  onClearHistory,
  onReportClick,
  onTimeRangeChange,
  onAIFixDocument,
}) => {
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const editorContentRef = useRef<HTMLDivElement>(null);

  // Get selectedData from atom
  const selectedData = useAtomValue(selectedDataAtom);

  // Panel state
  const [isPanelOpen, setIsPanelOpen] = useAtom(isPanelOpenAtom);
  const [panelWidth, setPanelWidth] = useAtom(panelWidthAtom);
  const [isDragging, setIsDragging] = useAtom(isDraggingAtom);
  const [isPatientPanelOpen, setIsPatientPanelOpen] = useAtom(isPatientPanelOpenAtom);
  const [patientPanelWidth, setPatientPanelWidth] = useAtom(patientPanelWidthAtom);
  const [isPatientPanelDragging, setIsPatientPanelDragging] = useAtom(isPatientPanelDraggingAtom);
  const [isReportListOpen, setIsReportListOpen] = useAtom(isReportListOpenAtom);

  // Chat state
  const messages = useAtomValue(messagesAtom);
  const inputValue = useAtomValue(inputValueAtom);
  const isRefreshing = useAtomValue(isRefreshingAtom);

  // Report state
  const reportContent = useAtomValue(reportContentAtom);
  const selectedReport = useAtomValue(selectedReportAtom);
  const isLoadingReports = useAtomValue(isLoadingReportsAtom);
  const reportList = useAtomValue(reportListAtom);
  const timeRange = useAtomValue(timeRangeAtom);

  // Add useReportGeneration hook
  const { reportContent: generatedReportContent, handleGenerateReport } = useReportGeneration(
    editor, // dictationEditor
    editor, // reportEditor - using the same editor for both since this is the container
    selectedData
  );

  // Add useEditorContent hook
  useEditorContent(editor, generatedReportContent);

  // Panel handlers
  const handleTogglePanel = useCallback(() => {
    setIsPanelOpen((prev) => {
      const newState = !prev;
      if (newState) {
        setIsReportListOpen(false);
      }
      localStorage.setItem('editorPanelOpen', JSON.stringify(newState));
      return newState;
    });
  }, [setIsPanelOpen, setIsReportListOpen]);

  const handleTogglePatientPanel = useCallback(() => {
    setIsPatientPanelOpen((prev) => {
      const newState = !prev;
      localStorage.setItem('patientPanelOpen', JSON.stringify(newState));
      return newState;
    });
  }, [setIsPatientPanelOpen]);

  const handleToggleReportList = useCallback(() => {
    setIsReportListOpen((prev) => {
      const newState = !prev;
      if (newState) {
        setIsPanelOpen(false);
      }
      localStorage.setItem('reportListOpen', JSON.stringify(newState));
      return newState;
    });
  }, [setIsReportListOpen, setIsPanelOpen]);

  // Drag handlers
  const handleDrag = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100;
      const minWidthPercent = (MIN_PANEL_WIDTH / containerRect.width) * 100;
      const clampedWidth = Math.min(Math.max(newWidth, minWidthPercent), MAX_PANEL_WIDTH);
      setPanelWidth(clampedWidth);
    },
    [isDragging, setPanelWidth]
  );

  const handlePatientPanelDrag = useCallback(
    (e: MouseEvent) => {
      if (!isPatientPanelDragging || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = ((containerRect.right - e.clientX) / containerRect.width) * 100;
      const minWidthPercent = (MIN_PATIENT_PANEL_WIDTH / containerRect.width) * 100;
      const clampedWidth = Math.min(Math.max(newWidth, minWidthPercent), MAX_PATIENT_PANEL_WIDTH);
      setPatientPanelWidth(clampedWidth);
    },
    [isPatientPanelDragging, setPatientPanelWidth]
  );

  const handleDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [setIsDragging]);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    localStorage.setItem('editorPanelWidth', panelWidth.toString());
  }, [panelWidth, setIsDragging]);

  const handlePatientPanelDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsPatientPanelDragging(true);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [setIsPatientPanelDragging]);

  const handlePatientPanelDragEnd = useCallback(() => {
    setIsPatientPanelDragging(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    localStorage.setItem('patientPanelWidth', patientPanelWidth.toString());
  }, [patientPanelWidth, setIsPatientPanelDragging]);

  // Effects for drag handlers
  React.useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDrag);
      window.addEventListener('mouseup', handleDragEnd);
    }
    return () => {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', handleDragEnd);
    };
  }, [isDragging, handleDrag, handleDragEnd]);

  React.useEffect(() => {
    if (isPatientPanelDragging) {
      window.addEventListener('mousemove', handlePatientPanelDrag);
      window.addEventListener('mouseup', handlePatientPanelDragEnd);
    }
    return () => {
      window.removeEventListener('mousemove', handlePatientPanelDrag);
      window.removeEventListener('mouseup', handlePatientPanelDragEnd);
    };
  }, [isPatientPanelDragging, handlePatientPanelDrag, handlePatientPanelDragEnd]);

  // Set initial panel width if not set
  React.useEffect(() => {
    const savedWidth = localStorage.getItem('editorPanelWidth');
    if (!savedWidth) {
      setPanelWidth(DEFAULT_PANEL_WIDTH);
    }
  }, [setPanelWidth]);

  // Add debugging for campus and patID
  const getCampus = () => {
    // In development mode, use environment variable
    if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_EHR_CAMPUS) {
      console.log("Using dev campus:", process.env.REACT_APP_EHR_CAMPUS);
      return process.env.REACT_APP_EHR_CAMPUS;
    }

    // In production, use Cci runtime
    const campus = Cci?.RunTime?.getEncounterInfo()?.campus;
    console.log("Campus from Cci:", campus);
    console.log("Cci object:", Cci);
    console.log("RunTime:", Cci?.RunTime);
    console.log("getEncounterInfo:", Cci?.RunTime?.getEncounterInfo);
    return campus || "default";
  };

  const getPatID = () => {
    let dbpath: string | undefined;

    // In development mode, use environment variable
    if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_DEV_PATIENT) {
      dbpath = process.env.REACT_APP_DEV_PATIENT;
      console.log("Using dev patient dbpath:", dbpath);
    } else {
      // In production, use Cci runtime
      dbpath = Cci?.util?.Patient?.getDbpath();
      console.log("DBPath from Cci:", dbpath);
      const cci = window.Cci || window.cci;
      console.log("Cci.util:", cci?.util);
      console.log("Cci.util.Patient:", cci?.util?.Patient);
    }

    if (dbpath) {
      const lastIndexOfP = dbpath.lastIndexOf("p");
      if (lastIndexOfP > 0 && lastIndexOfP < dbpath.length) {
        const patID = dbpath.substr(lastIndexOfP + 1);
        console.log("Extracted patID:", patID);
        return patID;
      }
    }
    console.log("Using default patID");
    return "default";
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        display: 'flex',
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Left Panel */}
      {isPanelOpen && (
        <ResizablePanel
          className="chat-panel"
          sx={{
            width: `${panelWidth}%`,
            minWidth: isPanelOpen ? '300px' : 0,
            transition: 'width 0.3s ease',
            borderRight: '1px solid #e0e0e0',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <PanelContent>
            <EditorPanels
              isPanelOpen={isPanelOpen}
              isReportListOpen={isReportListOpen}
              isPatientPanelOpen={isPatientPanelOpen}
              panelWidth={panelWidth}
              patientPanelWidth={patientPanelWidth}
              onDragStart={handleDragStart}
              onPatientPanelDragStart={handlePatientPanelDragStart}
              editor={editor}
              messages={messages}
              inputValue={inputValue}
              isProcessing={false}
              isLoadingReports={isLoadingReports}
              isRefreshing={isRefreshing}
              reportList={reportList}
              selectedReport={selectedReport}
              campus={getCampus()}
              patID={getPatID()}
              timeRange={timeRange}
              onInputChange={onInputChange}
              onInputKeyDown={onInputKeyDown}
              onSend={onSend}
              onClearHistory={onClearHistory}
              onRefresh={onRefresh}
              onReportClick={onReportClick}
              onTimeRangeChange={onTimeRangeChange}
            />
          </PanelContent>
        </ResizablePanel>
      )}

      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <EditorToolbar
          selectedReport={selectedReport}
          onSave={onSave}
          onSaveAs={onSaveAs}
          onDelete={onDelete}
          onRefresh={onRefresh}
          onClear={onClear}
          onGenerateReport={handleGenerateReport}
          onToggleRecording={onToggleRecording}
          onRunMacro={onRunMacro}
          onVersionHistory={onVersionHistory}
          isListening={isListening}
          onAIFixDocument={onAIFixDocument}
        />
        <Box
          ref={editorContentRef}
          sx={{
            flex: 1,
            overflow: 'auto',
            padding: '20px',
            backgroundColor: '#fff',
          }}
        >
          <EditorContent
            editor={editor}
            selectedReport={selectedReport}
            isListening={isListening}
            onSave={onSave}
            onSaveAs={onSaveAs}
            onDelete={onDelete}
            onRefresh={onRefresh}
            onClear={onClear}
            onGenerateReport={handleGenerateReport}
            onToggleRecording={onToggleRecording}
            onRunMacro={onRunMacro}
            onVersionHistory={onVersionHistory}
          />
        </Box>
      </Box>

      {/* Right Panel */}
      {isPatientPanelOpen && (
        <ResizablePanel
          className="patient-panel"
          sx={{
            width: `${patientPanelWidth}%`,
            minWidth: isPatientPanelOpen ? '600px' : 0,
            transition: 'width 0.3s ease',
            borderLeft: '1px solid #e0e0e0',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <PanelContent>
            <ReportListPanel
              panelWidth={panelWidth}
              isLoadingReports={isLoadingReports}
              isRefreshing={isRefreshing}
              reportList={reportList}
              selectedReport={selectedReport}
              onDragStart={handleDragStart}
              onRefresh={onRefresh}
              onReportClick={onReportClick}
              campus={getCampus()}
              patID={getPatID()}
            />
          </PanelContent>
        </ResizablePanel>
      )}
    </Box>
  );
}; 