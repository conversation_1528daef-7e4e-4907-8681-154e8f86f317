import { Box, AppBar, Toolbar, <PERSON><PERSON><PERSON><PERSON>on } from "@mui/material";
import { styled } from "@mui/material/styles";
import { Refresh } from "@mui/icons-material";

// Shared icon styles
export const actionIconStyles = {
  color: "rgb(61, 110, 191)",
  fontSize: "20px",
  filter: "drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15))",
  transition: "all 0.2s ease",
} as const;

export const disabledIconStyles = {
  color: "rgba(0, 0, 0, 0.26)",
  fontSize: "20px",
  filter: "none",
} as const;

export const recordingIconStyles = {
  color: "#45B766",
  fontSize: "20px",
  filter: "drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15))",
  transition: "all 0.2s ease",
} as const;

export const recordingIconActiveStyles = {
  color: "#ff0000",
  fontSize: "20px",
  filter: "drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15))",
  transition: "all 0.2s ease",
} as const;

export const iconSize = {
  fontSize: "20px",
} as const;

export const chargeIconStyles = {
  color: "#4B6EAF",
  fontSize: "20px",
  filter: "drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15))",
} as const;

export const StyledContainer = styled(Box)({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  backgroundColor: "#ffffff",
  color: "#333333",
  "&.reports-container": {
    fontFamily: "Roboto, sans-serif",
    fontSize: "15px",
  },
});

export const StyledAppBar = styled(AppBar)({
  backgroundColor: "#3D6EBF",
  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  width: "64px",
  height: "100vh",
  position: "fixed",
  left: 0,
  top: 0,
  zIndex: 1200,
  transition: "width 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  "&:hover": {
    width: "72px",
  },
});

export const StyledToolbar = styled(Toolbar)({
  flexDirection: "column",
  justifyContent: "flex-start",
  minHeight: "100%",
  padding: "16px 0",
  gap: "16px",
  "&.MuiToolbar-root": {
    minHeight: "100%",
    "@media (min-width: 600px)": {
      minHeight: "100%",
      paddingTop: "16px",
      paddingBottom: "16px",
    },
  },
});

export const StyledActionButton = styled(IconButton)({
  backgroundColor: "transparent",
  "&:hover": {
    backgroundColor: "transparent",
    transform: "scale(1.1)",
  },
  transition: "all 0.2s ease",
  padding: "8px",
  width: "56px",
  height: "56px",
  "& .MuiSvgIcon-root": {
    fontSize: "28px",
    color: "#ffffff",
    filter: "drop-shadow(0 3px 4px rgba(0, 0, 0, 0.25))",
    transition: "all 0.2s ease",
  },
  "&:hover .MuiSvgIcon-root": {
    filter: "drop-shadow(0 4px 5px rgba(0, 0, 0, 0.3))",
  },
});

export const StyledRefreshIcon = styled(Refresh)<{ isrefreshing: string }>(
  ({ isrefreshing }) => ({
    animation: isrefreshing === "true" ? "spin 1s linear infinite" : "none",
    "@keyframes spin": {
      "0%": {
        transform: "rotate(0deg)",
      },
      "100%": {
        transform: "rotate(360deg)",
      },
    },
  })
);

export const ReportListRefreshButton = styled(IconButton)({
  backgroundColor: "transparent",
  "&:hover": {
    backgroundColor: "transparent",
    transform: "scale(1.1)",
  },
  transition: "all 0.2s ease",
  padding: "4px",
  width: "32px",
  height: "32px",
  "& .MuiSvgIcon-root": {
    fontSize: "20px",
    color: "#ffffff",
    transition: "all 0.2s ease",
  },
  "&:hover .MuiSvgIcon-root": {
    opacity: 0.8,
  },
});

export const ReportListDeleteButton = styled(IconButton)({
  backgroundColor: "transparent",
  "&:hover": {
    backgroundColor: "transparent",
    transform: "scale(1.1)",
  },
  transition: "all 0.2s ease",
  padding: "4px",
  width: "32px",
  height: "32px",
  "& .MuiSvgIcon-root": {
    fontSize: "20px",
    color: "#ffffff",
    transition: "all 0.2s ease",
  },
  "&:hover .MuiSvgIcon-root": {
    opacity: 0.8,
  },
  "&.Mui-disabled": {
    "& .MuiSvgIcon-root": {
      color: "rgba(255, 255, 255, 0.3)",
    },
  },
});

export const ReportListVersionHistoryButton = styled(IconButton)({
  backgroundColor: "transparent",
  "&:hover": {
    backgroundColor: "transparent",
    transform: "scale(1.1)",
  },
  transition: "all 0.2s ease",
  padding: "4px",
  width: "32px",
  height: "32px",
  "& .MuiSvgIcon-root": {
    fontSize: "20px",
    color: "#ffffff",
    transition: "all 0.2s ease",
  },
  "&:hover .MuiSvgIcon-root": {
    opacity: 0.8,
  },
  "&.Mui-disabled": {
    "& .MuiSvgIcon-root": {
      color: "rgba(255, 255, 255, 0.3)",
    },
  },
});

export const StyledEditorContainer = styled(Box)({
  height: "calc(100vh - 16px)", // Account for top padding
  overflow: "hidden",
  display: "flex",
  flexDirection: "row",
  backgroundColor: "#ffffff",
  // Override the white text color from reports-container
  "& .tiptap": {
    color: "#000000 !important",
    "div, p": {
      fontSize: "15px",
      fontFamily: "'Roboto', sans-serif",
      lineHeight: "1.5",
      marginLeft: "0px",
      color: "#000000 !important",
    },
    "& ul, & ol": {
      "li p": {
        marginTop: "0.5em",
        marginBottom: "0.5em",
        lineHeight: "1.5",
        color: "#000000 !important",
      },
    },
    // Ensure cursor is visible by overriding the white color
    "& .ProseMirror": {
      color: "#000000 !important",
      caretColor: "#000000 !important",
      // Ensure text selection works properly
      userSelect: "text !important",
      "-webkit-user-select": "text !important",
      "-moz-user-select": "text !important",
      "-ms-user-select": "text !important",
    },
    "& .ProseMirror *": {
      color: "#000000 !important",
      // Ensure text selection works on all elements
      userSelect: "text !important",
      "-webkit-user-select": "text !important",
      "-moz-user-select": "text !important",
      "-ms-user-select": "text !important",
    },
    // Ensure text selection highlighting is visible
    "& .ProseMirror ::selection": {
      background: "#b3d4fc !important",
      color: "#000000 !important",
    },
    "& .ProseMirror ::-moz-selection": {
      background: "#b3d4fc !important",
      color: "#000000 !important",
    },
  },
});

export const StyledEditorContent = styled(Box)({
  flex: 1,
  overflowY: "auto",
  padding: "1rem",
  height: "calc(100% - 64px)", // Account for the header
  position: "relative",
  "& .ProseMirror": {
    height: "100%",
    overflowY: "auto",
    padding: "1rem",
  },
});

export const StyledErrorMessage = styled(Box)({
  position: "fixed",
  top: "20px",
  left: "50%",
  transform: "translateX(-50%)",
  backgroundColor: "#ff0000",
  color: "#ffffff",
  padding: "8px 16px",
  borderRadius: "0",
  zIndex: 1000,
});

export const StyledBubbleMenu = styled(Box)({
  display: "flex",
  backgroundColor: "#ffffff",
  borderRadius: "0",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  padding: "4px",
});

export const StyledFormatButton = styled(IconButton, {
  shouldForwardProp: (prop: string) => prop !== "isActive",
})<{ isActive?: boolean }>(({ isActive }: { isActive?: boolean }) => ({
  backgroundColor: isActive ? "rgba(0, 0, 0, 0.08)" : "transparent",
  "&:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.12)",
  },
  padding: "2px",
  width: "28px",
  height: "28px",
  "& .MuiSvgIcon-root": {
    fontSize: "18px",
    color: isActive ? "#333333" : "rgba(0, 0, 0, 0.7)",
  },
}));

export const ResizablePanel = styled(Box)(({ theme }: { theme: any }) => ({
  position: "relative",
  height: "calc(100vh - 16px)", // Account for top padding
  backgroundColor: "#ffffff",
  transition: "width 0.2s ease",
  overflowY: "hidden",
  overflowX: "hidden",
  color: "#333333",
  borderRadius: "0",
  "&.chat-panel": {
    maxWidth: "35%",
    minWidth: "300px",
  },
  "&.patient-panel": {
    maxWidth: "60%",
    minWidth: "600px",
  },
}));

export const EditorContainer = styled(Box)({
  display: "flex",
  height: "100%",
  overflow: "hidden",
});

export const MainContent = styled(Box)({
  flex: 1,
  height: "100%",
  overflow: "hidden",
  display: "flex",
  flexDirection: "column",
  position: "relative",
});

export const PanelContent = styled(Box)({
  height: "100%",
  padding: 0,
  overflowY: "auto",
  overflowX: "hidden",
  backgroundColor: "#ffffff",
  color: "#333333",
});

export const PanelAppBar = styled(AppBar)({
  backgroundColor: "#3C4B50",
  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  minHeight: "40px",
  height: "40px",
  borderRadius: "0",
  position: "sticky",
  top: 0,
  zIndex: 2,
});

export const PanelToolbar = styled(Toolbar)({
  justifyContent: "space-between",
  minHeight: "40px",
  height: "40px",
  "&.MuiToolbar-root": {
    minHeight: "40px",
    "@media (min-width: 600px)": {
      minHeight: "40px",
      paddingLeft: "4px",
      paddingRight: "4px",
    },
  },
});

export const StyledChatBox = styled(Box)({
  display: "flex",
  flexDirection: "column",
  height: "100%",
  backgroundColor: "#ffffff",
});

export const ReportListContainer = styled(Box)({
  display: "flex",
  flexDirection: "column",
  height: "100%",
  backgroundColor: "#ffffff",
});
