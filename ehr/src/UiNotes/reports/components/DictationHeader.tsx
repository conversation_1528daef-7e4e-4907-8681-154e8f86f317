import React from 'react';
import { Box, IconButton, Tooltip, FormControl, Select, MenuItem, SelectChangeEvent, Typography, Divider, CircularProgress } from '@mui/material';
import { Mic, LocalFlorist, Clear, Summarize, RecordVoiceOver, Spellcheck } from '@mui/icons-material';
import { useAtom } from 'jotai';
import { reportTypeAtom } from '../context/EditorContext';
import { REPORT_TYPES, ReportType } from '../Constants';
import { useTranscription } from '../hooks/useTranscription';
import { TranscriptionDialog } from './TranscriptionDialog';
import { Editor } from '@tiptap/core';

// Shared styles
const iconButtonStyle = {
  color: "white",
  "& .MuiSvgIcon-root": {
    filter: "drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15))",
    transition: "all 0.2s ease",
  },
  "&:hover .MuiSvgIcon-root": {
    filter: "drop-shadow(0 4px 5px rgba(0, 0, 0, 0.3))",
    transform: "scale(1.1)",
  },
};

const recordingIconButtonStyle = {
  ...iconButtonStyle,
  "& .MuiSvgIcon-root": {
    ...iconButtonStyle["& .MuiSvgIcon-root"],
    color: "white", // Will be overridden for recording state
  },
};

interface DictationHeaderProps {
  onToggleRecording: () => void;
  onRunMacro: () => void;
  onClear: () => void;
  onGenerateReport: () => void;
  onAIFixDocument: () => void;
  isRecording: boolean;
  isEditorEmpty: boolean;
  isAIFixing?: boolean;
  reportEditor: Editor | null;
  isGeneratingReport?: boolean;
}

export const DictationHeader: React.FC<DictationHeaderProps> = ({
  onToggleRecording,
  onRunMacro,
  onClear,
  onGenerateReport,
  onAIFixDocument,
  isRecording,
  isEditorEmpty,
  isAIFixing = false,
  reportEditor,
  isGeneratingReport = false,
}) => {
  const [reportType, setReportType] = useAtom(reportTypeAtom);
  const { transcriptionState, toggleTranscription } = useTranscription();

  const handleReportTypeChange = (event: SelectChangeEvent) => {
    const newReportType = event.target.value as ReportType;
    setReportType(newReportType);

    // Clear the report editor content when report type changes
    if (reportEditor && !reportEditor.isDestroyed) {
      reportEditor.commands.clearContent();
    }
  };

  const handleTranscriptionClick = () => {
    toggleTranscription();
  };

  // Disable all buttons when generating report
  const isDisabled = isGeneratingReport;

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '8px 12px 8px 48px',
          backgroundColor: '#45B766',
          height: '40px',
          minHeight: '40px',
          maxHeight: '40px',
          boxSizing: 'border-box',
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: 'white',
            fontSize: '16px',
            fontFamily: 'Roboto',
            fontWeight: 500,
          }}
        >
          Dictation
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title={isRecording ? "Stop Recording" : "Start Recording"}>
              <IconButton
                onClick={onToggleRecording}
                size="small"
                disabled={isDisabled}
                sx={{
                  ...recordingIconButtonStyle,
                  "& .MuiSvgIcon-root": {
                    ...recordingIconButtonStyle["& .MuiSvgIcon-root"],
                    color: isRecording ? "#ff4444" : "white",
                  },
                  color: isDisabled ? "rgba(255, 255, 255, 0.3)" : "white",
                }}
              >
                <Mic />
              </IconButton>
            </Tooltip>

            <Tooltip title={transcriptionState.isRecording ? "Stop Transcription" : "Start Transcription"}>
              <IconButton
                onClick={handleTranscriptionClick}
                size="small"
                disabled={isDisabled}
                sx={{
                  ...recordingIconButtonStyle,
                  "& .MuiSvgIcon-root": {
                    ...recordingIconButtonStyle["& .MuiSvgIcon-root"],
                    color: transcriptionState.isRecording ? "#ff4444" : "white",
                  },
                  color: isDisabled ? "rgba(255, 255, 255, 0.3)" : "white",
                }}
              >
                <RecordVoiceOver />
              </IconButton>
            </Tooltip>

            <Tooltip title="Macro">
              <IconButton
                onClick={onRunMacro}
                size="small"
                disabled={isDisabled}
                sx={{
                  ...iconButtonStyle,
                  color: isDisabled ? "rgba(255, 255, 255, 0.3)" : "white",
                }}
              >
                <LocalFlorist />
              </IconButton>
            </Tooltip>

            <Divider
              orientation="vertical"
              flexItem
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.6)',
                width: '2px',
                height: '24px',
                alignSelf: 'center'
              }}
            />

            <Tooltip title="Clear Content">
              <span>
                <IconButton
                  onClick={onClear}
                  size="small"
                  disabled={isEditorEmpty || isDisabled}
                  sx={{
                    ...iconButtonStyle,
                    color: (isEditorEmpty || isDisabled) ? "rgba(255, 255, 255, 0.3)" : "white",
                  }}
                >
                  <Clear />
                </IconButton>
              </span>
            </Tooltip>

            <Tooltip title={isAIFixing ? "AI is fixing document..." : "AI Fix Document - Fix grammar and typos in entire dictation"}>
              <span>
                <IconButton
                  onClick={onAIFixDocument}
                  size="small"
                  disabled={isEditorEmpty || isAIFixing || isDisabled}
                  sx={{
                    ...iconButtonStyle,
                    color: (isEditorEmpty || isAIFixing || isDisabled) ? "rgba(255, 255, 255, 0.3)" : "white",
                    position: 'relative',
                  }}
                >
                  {isAIFixing ? (
                    <CircularProgress size={20} sx={{ color: 'white' }} />
                  ) : (
                    <Spellcheck />
                  )}
                </IconButton>
              </span>
            </Tooltip>

            <Tooltip title="Generate Report">
              <IconButton
                onClick={onGenerateReport}
                size="small"
                disabled={isDisabled}
                sx={{
                  ...iconButtonStyle,
                  color: isDisabled ? "rgba(255, 255, 255, 0.3)" : "white",
                }}
              >
                <Summarize />
              </IconButton>
            </Tooltip>
          </Box>

          <FormControl size="small" sx={{ minWidth: 120, backgroundColor: 'white', color: 'black', borderRadius: '4px' }}>
            <Select
              value={reportType}
              onChange={handleReportTypeChange}
              disabled={isDisabled}
              sx={{
                height: '28px',
                '& .MuiSelect-select': {
                  padding: '4px 8px',
                  fontSize: '14px',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.23)',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.87)',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.23)',
                  borderWidth: '1px',
                },
                '& .MuiSvgIcon-root': {
                  color: 'rgba(0, 0, 0, 0.54)',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(0, 0, 0, 0.12)',
                },
              }}
            >
              {Object.entries(REPORT_TYPES).map(([key, value]) => (
                <MenuItem key={key} value={key}>
                  {value}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* New Transcription Dialog */}
      <TranscriptionDialog
        open={transcriptionState.isPlaybackDialogOpen}
        onClose={() => toggleTranscription()}
      />
    </>
  );
}; 