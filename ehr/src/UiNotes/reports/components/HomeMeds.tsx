import React, { useState, useEffect, useCallback } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Box } from '@mui/material';
import { getDynamicPatientId, API_BASE_URL, DEFAULT_TIME_RANGE_SECONDS } from '../Constants';
import { styled } from '@mui/material/styles';
import { CompactDataComponent } from './CompactDataComponent';

export interface HomeMedsData {
  name?: string;
  dosage?: string;
  frequency?: string;
  startDate?: string;
  status?: string;
  provider?: string;
  starttime?: string;
}

interface HomeMedsProps {
  patientId?: string;
  editor?: any;
  mode?: 'copy' | 'delete';
  onDelete?: () => void;
  startTime?: number;
  endTime?: number;
}

// Styled table for home medications (compact version)
const StyledTable = styled(Table)({
  '& .MuiTableCell-root': {
    padding: '2px 4px',
    fontSize: '15px',
    fontFamily: 'Roboto',
    borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
    '&.MuiTableCell-head': {
      backgroundColor: '#f0f0f0',
      fontWeight: 500,
      color: '#333333',
    },
    '&.MuiTableCell-body': {
      backgroundColor: '#ffffff',
      color: '#333333',
    },
  },
  '& .MuiTableRow-root:hover': {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
});

export const HomeMeds: React.FC<HomeMedsProps> = ({
  patientId = getDynamicPatientId(),
  editor,
  mode = 'copy',
  onDelete,
  startTime,
  endTime,
}) => {
  const [homeMeds, setHomeMeds] = useState<HomeMedsData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  const fetchHomeMeds = useCallback(async () => {
    if (!patientId) return;

    setLoading(true);
    setError(null);

    try {
      // Always use time range from 0 (beginning of time) to now for home medications
      // This ensures we get all current home medications regardless of selected time range
      const endTimeValue = Math.floor(Date.now() / 1000); // Current time
      const startTimeValue = 0; // Beginning of time (Unix epoch)

      const url = `${API_BASE_URL}/getData?object=HomeMeds&patid=${patientId}&type=UnifiedCCO&starttime=${startTimeValue}&endtime=${endTimeValue}`;

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('webtoken'),
          'X-Forwarded-For': '127.0.0.1',
          'X-Real-IP': '127.0.0.1',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && Array.isArray(result.data)) {
        setHomeMeds(result.data);
      } else {
        setHomeMeds([]);
      }
    } catch (error) {
      console.error('Error fetching home medications data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch home medications');
    } finally {
      setLoading(false);
    }
  }, [patientId]);

  const handleExpand = () => {
    if (!isExpanded) {
      fetchHomeMeds();
    }
  };

  // Fetch home medications on mount since component is auto-expanded
  useEffect(() => {
    if (isExpanded) {
      fetchHomeMeds();
    }
  }, [patientId, isExpanded, fetchHomeMeds]);

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <CompactDataComponent
      title="Home Medications"
      isLoading={loading}
      error={error}
      initialExpanded={isExpanded}
      data={homeMeds as unknown as Record<string, unknown>}
      mode={mode}
      onDelete={handleDelete}
      onExpand={handleExpand}
    >
      {homeMeds.length > 0 ? (
        <TableContainer component={Paper} sx={{ boxShadow: 'none', border: 'none' }}>
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Medication</TableCell>
                <TableCell>Dosage</TableCell>
                <TableCell>Frequency</TableCell>
                <TableCell>Start Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Provider</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {homeMeds.map((med, index) => (
                <TableRow key={index}>
                  <TableCell>{med.name || ''}</TableCell>
                  <TableCell>{med.dosage || ''}</TableCell>
                  <TableCell>{med.frequency || ''}</TableCell>
                  <TableCell>{med.startDate || med.starttime || ''}</TableCell>
                  <TableCell>{med.status || ''}</TableCell>
                  <TableCell>{med.provider || ''}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      ) : (
        <Box sx={{ padding: '12px', textAlign: 'center', color: '#666666', fontSize: '13px' }}>
          No home medications found
        </Box>
      )}
    </CompactDataComponent>
  );
}; 