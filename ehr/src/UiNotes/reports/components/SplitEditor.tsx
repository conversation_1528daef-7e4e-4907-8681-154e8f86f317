import React, { useState, useRef, useEffect } from 'react';
import { Box, Typography, IconButton, Tooltip, Snackbar, Alert } from '@mui/material';
import { Editor } from '@tiptap/core';
import { EditorContent } from '@tiptap/react';
import Print from '@mui/icons-material/Print';
import Mic from '@mui/icons-material/Mic';
import MicOff from '@mui/icons-material/MicOff';
import LocalFlorist from '@mui/icons-material/LocalFlorist';
import Clear from '@mui/icons-material/Clear';
import ExpandMore from '@mui/icons-material/ExpandMore';
import ExpandLess from '@mui/icons-material/ExpandLess';
import AutoFixHigh from '@mui/icons-material/AutoFixHigh';
import Save from '@mui/icons-material/Save';
import SaveAs from '@mui/icons-material/SaveAs';
import { EditorContentArea } from './EditorContent';
import { useAtom, useAtomValue } from 'jotai';
import { OpenConfigDialog<PERSON>tom, MacroData<PERSON>tom } from '../context/MacroContext';
import MacroConfig from './MacroConfig';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';
import { useReportVoiceRecognition } from '../hooks/useReportVoiceRecognition';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { DictationHeader } from './DictationHeader';
import { useReportGeneration } from '../hooks/useReportGeneration';
import { useTranscription } from '../hooks/useTranscription';
import { selectedDataAtom } from '../context/SelectedDataContext';
import { transcriptionStateAtom } from '../atoms/transcriptionAtoms';
import { normalQuery } from '../../util/AIPilot';

interface SplitEditorProps {
  dictationEditor: Editor | null;
  reportEditor: Editor | null;
  onPrint: () => void;
  onToggleRecording: () => void;
  onRunMacro: () => void;
  onClear: () => void;
  onGenerateReport: () => void;
  onSave: () => void;
  onSaveAs: () => void;
  isEditorEmpty: boolean;
  isRecording: boolean;
  isReportSelected: boolean;
  isGeneratingReport?: boolean;
  selectedReport?: any; // Add selected report to access signature info
  onRefresh?: () => void; // Add refresh callback for after signing
  refreshSelectedReport?: () => void; // Add specific refresh for selected report
  campus?: string;
  patID?: string;
}

export const SplitEditor: React.FC<SplitEditorProps> = ({
  dictationEditor,
  reportEditor,
  onPrint,
  onToggleRecording,
  onRunMacro,
  onClear,
  onGenerateReport,
  onSave,
  onSaveAs,
  isEditorEmpty,
  isRecording,
  isReportSelected,
  isGeneratingReport = false,
  selectedReport,
  onRefresh,
  refreshSelectedReport,
  campus,
  patID,
}) => {
  const [isDictationExpanded, setIsDictationExpanded] = useState(true);
  const [isReportExpanded, setIsReportExpanded] = useState(true);
  // Macro state management
  const [openConfigDialog, setOpenConfigDialog] = useAtom(OpenConfigDialogAtom);
  const [macroData] = useAtom(MacroDataAtom);
  const [isMacroSelectionMode, setIsMacroSelectionMode] = useState(false);
  const selectedData = useAtomValue(selectedDataAtom);
  const transcriptionState = useAtomValue(transcriptionStateAtom);
  const [isAIFixing, setIsAIFixing] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');

  // Determine if save should be disabled based on signature status
  const isSaveDisabled = () => {
    // Basic conditions for disabling save
    const basicConditions = !reportEditor || !reportEditor.getText().trim() || isGenerating || !isReportSelected;
    
    if (basicConditions) {
      return true;
    }

    // Check if note has required signature and has been signed
    if (selectedReport?.required_signature && selectedReport?.signed) {
      return true;
    }

    return false;
  };

  const dictationEditorRef = useRef<Editor | null>(null);
  const reportEditorRef = useRef<Editor | null>(null);

  // Keyboard shortcut handler for macro selection (Ctrl+M / Cmd+M)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
        event.preventDefault();
        setIsMacroSelectionMode(true);
        setOpenConfigDialog(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [setOpenConfigDialog]);

  // Handle macro selection and insertion
  const handleMacroSelect = (macro: any) => {
    const activeEditor = dictationEditor?.isDestroyed ? reportEditor : dictationEditor;
    if (activeEditor && !activeEditor.isDestroyed) {
      activeEditor.commands.insertContent(macro.description);
      // Move cursor to the end of the inserted content
      activeEditor.commands.focus('end');
    }
  };

  const {
    isListening: isDictationListening,
    browserSupportsSpeechRecognition: browserSupportsDictationRecognition,
    startListening: startDictationListening,
    stopListening: stopDictationListening,
    finalTranscript: finalDictationTranscript,
    captureCursorPosition: captureDictationCursorPosition
  } = useSpeechRecognition({
    editor: dictationEditor,
    macroData: macroData || [],
    onProcessTranscript: (text: string) => {
      if (dictationEditor && !dictationEditor.isDestroyed) {
        dictationEditor.commands.insertContent(text);
      }
    },
    editorId: 'dictation'
  });

  const {
    isListening: isReportListening,
    browserSupportsSpeechRecognition: browserSupportsReportRecognition,
    startListening: startReportListening,
    stopListening: stopReportListening,
    finalTranscript: finalReportTranscript
  } = useReportVoiceRecognition({
    editor: reportEditor,
    macroData: macroData || [],
    onProcessTranscript: (text: string) => {
      if (reportEditor && !reportEditor.isDestroyed) {
        reportEditor.commands.insertContent(text);
      }
    }
  });

  const {
    handleGenerateReport: generateReport,
    isGeneratingReport: isGenerating
  } = useReportGeneration(
    dictationEditor,
    reportEditor,
    selectedData,
    (loading) => {
      // Handle loading state if needed
    }
  );

  const handleGenerateReport = () => {
    setIsDictationExpanded(false);
    setIsReportExpanded(true);
    generateReport();
  };

  useEffect(() => {
    if (isReportSelected) {
      setIsDictationExpanded(false);
      setIsReportExpanded(true);
    }
  }, [isReportSelected]);

  const handleClearReport = () => {
    if (reportEditor) {
      reportEditor.commands.clearContent();
    }
  };

  const handleToggleRecording = () => {
    if (isDictationListening) {
      stopDictationListening();
    } else {
      startDictationListening();
    }
  };

  const handleToggleReportRecording = () => {
    if (isReportListening) {
      stopReportListening();
    } else {
      startReportListening();
    }
  };

  const handleAIFixDocument = async () => {
    if (!dictationEditor || !dictationEditor.getText().trim()) {
      setMessage('No content to fix');
      setMessageType('error');
      setShowMessage(true);
      return;
    }

    setIsAIFixing(true);

    try {
      const content = dictationEditor.getText();

      await normalQuery({
        prompt: 'Fix grammar, spelling, and punctuation in this text. Return ONLY the corrected text without any explanation.',
        content: content,
        setResult: () => { }, // We'll handle the result in the callback
        inFetchData: isAIFixing,
        setInFetchData: setIsAIFixing,
        endCallback: (correctedText: string) => {
          if (correctedText && correctedText !== 'thinking...') {
            // Replace the entire content with the corrected version
            dictationEditor.commands.setContent(correctedText.trim());

            setMessage('Document fixed successfully!');
            setMessageType('success');
            setShowMessage(true);
          }
          setIsAIFixing(false);
        },
      });
    } catch (error) {
      console.error('AI fix error:', error);
      setMessage('Error fixing document. Please try again.');
      setMessageType('error');
      setShowMessage(true);
      setIsAIFixing(false);
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflow: 'hidden',
      position: 'relative'
    }}>
      {/* Macro dialog - handles both selection mode (Ctrl+M) and management mode (icon click) */}
      <MacroConfig
        selectionMode={isMacroSelectionMode}
        onMacroSelect={handleMacroSelect}
        onClose={() => setIsMacroSelectionMode(false)}
      />
      {/* Dictation Section */}
      <Box
        sx={{
          position: 'relative',
          height: isDictationExpanded ? '50%' : '40px',
          display: 'flex',
          flexDirection: 'column',
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: '#ffffff',
          transition: 'height 0.3s ease-in-out',
          overflow: 'hidden',
          margin: 0,
          padding: 0
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', backgroundColor: '#45B766' }}>
          <IconButton
            size="small"
            onClick={() => setIsDictationExpanded(!isDictationExpanded)}
            sx={{
              color: 'white',
              position: 'absolute',
              left: '4px',
              zIndex: 1,
              width: '32px',
              height: '32px',
              padding: '4px',
            }}
          >
            {isDictationExpanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
          <Box sx={{ width: '100%' }}>
            <DictationHeader
              onToggleRecording={handleToggleRecording}
              onRunMacro={() => {
                // Open macro dialog in full management mode
                setIsMacroSelectionMode(false);
                setOpenConfigDialog(true);
              }}
              onClear={onClear}
              onGenerateReport={handleGenerateReport}
              onAIFixDocument={handleAIFixDocument}
              isRecording={isDictationListening}
              isEditorEmpty={!dictationEditor?.getText().trim()}
              isAIFixing={isAIFixing}
              reportEditor={reportEditor}
              isGeneratingReport={isGenerating}
            />
          </Box>
        </Box>
        {isDictationExpanded && (
          <Box
            sx={{
              flex: 1,
              overflowY: 'auto',
              '& .ProseMirror': {
                fontSize: '16px',
                fontFamily: 'Roboto'
              }
            }}
          >
            <EditorContentArea
              editor={dictationEditor}
              isDictationEditor={true}
              captureCursorPosition={captureDictationCursorPosition}
            />
          </Box>
        )}
      </Box>

      {/* Generated Report Section */}
      <Box
        sx={{
          position: 'relative',
          height: isDictationExpanded ? '50%' : 'calc(100% - 40px)',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff',
          transition: 'height 0.3s ease-in-out',
          overflow: 'hidden',
          margin: 0,
          padding: 0
        }}
      >
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '8px 12px',
          backgroundColor: '#1976d2',
          height: '40px',
          minHeight: '40px',
          maxHeight: '40px',
          boxSizing: 'border-box',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              size="small"
              onClick={() => setIsReportExpanded(!isReportExpanded)}
              sx={{ color: 'white', mr: 1 }}
            >
              {isReportExpanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
            <Typography
              variant="subtitle1"
              sx={{
                color: 'white',
                fontSize: '16px',
                fontFamily: 'Roboto',
                fontWeight: 500,
              }}
            >
              Generated Report
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Voice Edit">
              <span>
                <IconButton
                  size="small"
                  onClick={handleToggleReportRecording}
                  disabled={!browserSupportsReportRecognition || isGenerating}
                  sx={{
                    color: isGenerating ? 'rgba(255, 255, 255, 0.3)' : (isReportListening ? '#ff6b6b' : 'white'),
                    '&:disabled': {
                      color: 'rgba(255, 255, 255, 0.3)',
                    },
                    '&:hover': {
                      backgroundColor: isReportListening ? 'rgba(255, 107, 107, 0.1)' : 'rgba(255, 255, 255, 0.1)',
                    }
                  }}
                >
                  {isReportListening ? <MicOff /> : <Mic />}
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title="Print">
              <span>
                <IconButton
                  size="small"
                  onClick={onPrint}
                  disabled={!reportEditor || !reportEditor.getText().trim() || isGenerating}
                  sx={{
                    color: isGenerating ? 'rgba(255, 255, 255, 0.3)' : 'white',
                    '&:disabled': {
                      color: 'rgba(255, 255, 255, 0.3)',
                    }
                  }}
                >
                  <Print />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title={
              selectedReport?.required_signature && selectedReport?.signed 
                ? "Note is read-only - has been signed" 
                : "Save"
            }>
              <span>
                <IconButton
                  size="small"
                  data-testid="save-button"
                  onClick={() => {
                    console.log("Save button clicked in SplitEditor");
                    console.log("reportEditor exists:", !!reportEditor);
                    console.log("reportEditor has text:", reportEditor?.getText().trim());
                    console.log("isGenerating:", isGenerating);
                    console.log("isReportSelected:", isReportSelected);
                    console.log("About to call onSave function:", onSave);
                    onSave();
                  }}
                  disabled={isSaveDisabled()}
                  sx={{
                    color: isGenerating ? 'rgba(255, 255, 255, 0.3)' : 'white',
                    '&:disabled': {
                      color: 'rgba(255, 255, 255, 0.3)',
                    }
                  }}
                >
                  <Save />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title="Save As">
              <span>
                <IconButton
                  size="small"
                  onClick={onSaveAs}
                  disabled={!reportEditor || !reportEditor.getText().trim() || isGenerating}
                  sx={{
                    color: isGenerating ? 'rgba(255, 255, 255, 0.3)' : 'white',
                    '&:disabled': {
                      color: 'rgba(255, 255, 255, 0.3)',
                    }
                  }}
                >
                  <SaveAs />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title="Clear">
              <span>
                <IconButton
                  size="small"
                  onClick={handleClearReport}
                  disabled={!reportEditor || !reportEditor.getText().trim() || isGenerating}
                  sx={{
                    color: isGenerating ? 'rgba(255, 255, 255, 0.3)' : 'white',
                    '&:disabled': {
                      color: 'rgba(255, 255, 255, 0.3)',
                    }
                  }}
                >
                  <Clear />
                </IconButton>
              </span>
            </Tooltip>

          </Box>
        </Box>
        {isReportExpanded && (
          <Box
            sx={{
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              minHeight: 0,
              '& .ProseMirror': {
                fontSize: '16px',
                fontFamily: 'Roboto'
              }
            }}
          >
            <EditorContentArea
              editor={reportEditor}
              isReportEditor={true}
              isGeneratingReport={isGenerating}
              selectedReport={selectedReport}
              onRefresh={onRefresh}
              refreshSelectedReport={refreshSelectedReport}
              campus={campus}
              patID={patID}
            />
          </Box>
        )}
      </Box>

      <Snackbar
        open={showMessage}
        autoHideDuration={3000}
        onClose={() => setShowMessage(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowMessage(false)}
          severity={messageType}
          sx={{ width: '100%' }}
        >
          {message}
        </Alert>
      </Snackbar>
    </Box>
  );
}; 