import React, { useState, useRef } from 'react';
import { Box, Typography, IconButton, Checkbox, Dialog, DialogContent, DialogTitle, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import { ExpandMore, ExpandLess, MedicalServices, Close, Save, Assessment } from '@mui/icons-material';
import { useAtomValue, useSetAtom } from 'jotai';
import { selectedDataAtom, updateSelectedData<PERSON>tom, SelectedData } from '../context/SelectedDataContext';
import ExamComponent from '../../exam/ExamComponent';
import { getCampus, getPatID, getVisitKey } from '../../common/utils/runtimeUtils';

// Shared styled components for compact data display
const CompactContainer = styled(Box)({
  backgroundColor: "#ffffff",
  borderRadius: "0",
  padding: "0",
  marginBottom: "4px",
  border: "none",
  color: "#333333",
  boxShadow: "none",
});

const CompactHeader = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: "4px",
  color: "#000000",
  backgroundColor: "#3D6EBF",
  borderRadius: "0",
  padding: "2px 6px",
  minHeight: "24px",
});

const CompactTitle = styled(Typography)({
  fontSize: "15px",
  fontWeight: "500",
  color: "#ffffff",
  fontFamily: "Roboto",
});

const StyledButton = styled(IconButton)({
  color: 'white',
  transition: 'all 0.2s ease-in-out',
  padding: '2px',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    color: 'white',
    transform: 'scale(1.05)',
  },
  '&:active': {
    transform: 'scale(0.95)',
  },
});

const StyledCheckbox = styled(Checkbox)({
  color: 'white',
  padding: '2px',
  '&.Mui-checked': {
    color: '#44B866',
    '& .MuiSvgIcon-root': {
      color: '#ffffff',
    },
  },
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  }
});

interface CompactDataComponentProps {
  title: string;
  children: React.ReactNode;
  isLoading?: boolean;
  error?: string | null;
  initialExpanded?: boolean;
  data?: Record<string, unknown>;
  mode?: 'copy' | 'delete';
  onDelete?: () => void;
  onCopy?: () => void;
  onExpand?: () => void;
  disableExamDialog?: boolean; // New prop to disable Exam dialog
  onExamIconClick?: () => void; // Custom exam icon click handler
  isEnabled?: boolean; // New prop to control if component is enabled
  onEnabledChange?: (enabled: boolean) => void; // Callback when enabled state changes
}

/**
 * Shared CompactDataComponent for Demographics and Procedures
 */
export const CompactDataComponent: React.FC<CompactDataComponentProps> = ({
  title,
  children,
  isLoading = false,
  error = null,
  initialExpanded = false,
  data,
  mode,
  onDelete,
  onCopy,
  onExpand,
  disableExamDialog = false,
  onExamIconClick,
  isEnabled: initialIsEnabled = true,
  onEnabledChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const [isChecked, setIsChecked] = useState(initialIsEnabled);
  const [isExamDialogOpen, setIsExamDialogOpen] = useState(false);
  const examComponentRef = React.useRef<any>(null);

  const selectedData = useAtomValue(selectedDataAtom);
  const updateSelectedData = useSetAtom(updateSelectedDataAtom);
  // Map component titles to selectedData keys
  const getTypeFromTitle = (title: string): keyof SelectedData => {
    switch (title.toLowerCase()) {
      case 'home medications':
        return 'medications';
      case 'medication reconciliation':
        return 'medRec';
      case 'medications':
        return 'inpatientMedications';
      case 'patient demographics':
      case 'demographics':
        return 'demographics';
      case 'vital signs':
      case 'vitals':
        return 'vitals';
      case 'problems':
        return 'problems';
      case 'diagnosis':
        return 'diagnosis';
      case 'procedures':
        return 'procedures';
      case 'lab results':
        return 'labResults';
      case 'review of systems':
        return 'ros_data';
      case 'exam':
        return 'exam_data';
      default:
        return 'demographics'; // fallback
    }
  };

  const type = getTypeFromTitle(title);

  // Auto-update atom when data changes
  React.useEffect(() => {
    if (data && isChecked) {
      updateSelectedData({ type, isSelected: true, data });
    }
  }, [data, isChecked, title, type]); // Removed updateSelectedData from dependencies

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newCheckedState = event.target.checked;
    setIsChecked(newCheckedState);
    updateSelectedData({ type, isSelected: newCheckedState, data: newCheckedState ? data : undefined });

    // Notify parent component of enabled state change
    if (onEnabledChange) {
      onEnabledChange(newCheckedState);
    }
  };

  const toggleExpand = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    if (newExpandedState && onExpand) {
      onExpand();
    }
  };

  const handleSaveExam = (examDataForSave: any) => {
    // Store the exam data in the selectedData atom
    updateSelectedData({
      type: 'exam_data',
      isSelected: true,
      data: examDataForSave
    });
    setIsExamDialogOpen(false);
  };

  const handleActualSave = () => {
    // Get the current exam data including checkbox states
    const examDataForSave = examComponentRef.current?.();
    if (examDataForSave) {
      handleSaveExam(examDataForSave);
    }
  };

  const handleExamIconClick = () => {
    setIsExamDialogOpen(true);
  };

  return (
    <CompactContainer>
      <Box sx={{ position: 'relative', width: '100%' }}>
        <CompactHeader>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <StyledButton onClick={toggleExpand}>
              {isExpanded ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
            </StyledButton>
            <CompactTitle>{title}</CompactTitle>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {title.toLowerCase() === 'exam' && (
              <StyledButton
                onClick={onExamIconClick || handleExamIconClick}
                title="Open Physical Exam"
              >
                <MedicalServices fontSize="small" />
              </StyledButton>
            )}
            {title.toLowerCase() === 'review of systems' && (
              <StyledButton
                onClick={onExamIconClick}
                title="Open Review of Systems"
              >
                <Assessment fontSize="small" />
              </StyledButton>
            )}
            <StyledCheckbox
              checked={isChecked}
              onChange={handleCheckboxChange}
            />
          </Box>
        </CompactHeader>
        {isExpanded && (
          isLoading ? (
            <Box sx={{ padding: '12px', textAlign: 'center', color: '#3D6EBF' }}>
              Loading {title.toLowerCase()}...
            </Box>
          ) : error ? (
            <Typography sx={{ color: '#d32f2f', padding: '8px', fontSize: '15px', fontFamily: 'Roboto, sans-serif' }}>
              {error}
            </Typography>
          ) : (
            children
          )
        )}
      </Box>

      {/* Exam Dialog */}
      <Dialog
        open={isExamDialogOpen}
        onClose={() => setIsExamDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            height: '90vh',
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box component="span" sx={{ fontWeight: 'bold', fontSize: '1.25rem' }}>Physical Exam</Box>
          <IconButton onClick={() => setIsExamDialogOpen(false)}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {(() => {
              const campus = getCampus();
              const patID = getPatID();
              const visitkey = getVisitKey();

              return (
                <ExamComponent
                  campus={campus}
                  patID={patID}
                  visitkey={visitkey}
                  onGetSaveData={(getDataFn) => {
                    examComponentRef.current = getDataFn;
                  }}
                />
              );
            })()}
          </Box>
          <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              variant="outlined"
              onClick={() => setIsExamDialogOpen(false)}
              size="medium"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleActualSave}
              size="medium"
            >
              Save
            </Button>
          </Box>
        </DialogContent>
      </Dialog>
    </CompactContainer>
  );
}; 