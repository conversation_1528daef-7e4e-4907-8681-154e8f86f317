import React, { useState, useEffect } from 'react';
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { DataComponent } from './DataComponent';
import { VitalsData } from './Vitals';
import { DEFAULT_TIME_RANGE_SECONDS } from '../Constants';

interface TestVitalsProps {
  patientId?: string;
  startTime?: number;
  endTime?: number;
  editor?: any;
  mode?: "copy" | "delete";
  onDelete?: () => void;
}

// Mock data generator
const generateMockVitals = (startTime: number, endTime: number): VitalsData[] => {
  // Fixed test data spanning 2 months
  const fixedData: VitalsData[] = [
    {
      key: Math.floor(Date.now() / 1000) - 60 * 24 * 60 * 60, // 60 days ago
      heartRate: 72,
      respirationRate: 16,
      pulseOx: 98,
      temperatureF: 98.6,
      temperatureC: 37,
      painScore: 3,
      systolicBP: 120,
      diastolicBP: 80,
    },
    {
      key: Math.floor(Date.now() / 1000) - 45 * 24 * 60 * 60, // 45 days ago
      heartRate: 85,
      respirationRate: 18,
      pulseOx: 97,
      temperatureF: 99.1,
      temperatureC: 37.3,
      painScore: 5,
      systolicBP: 135,
      diastolicBP: 85,
    },
    {
      key: Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60, // 30 days ago
      heartRate: 68,
      respirationRate: 14,
      pulseOx: 99,
      temperatureF: 98.4,
      temperatureC: 36.9,
      painScore: 2,
      systolicBP: 118,
      diastolicBP: 78,
    },
    {
      key: Math.floor(Date.now() / 1000) - 21 * 24 * 60 * 60, // 21 days ago
      heartRate: 92,
      respirationRate: 20,
      pulseOx: 96,
      temperatureF: 99.5,
      temperatureC: 37.5,
      painScore: 7,
      systolicBP: 145,
      diastolicBP: 90,
    },
    {
      key: Math.floor(Date.now() / 1000) - 14 * 24 * 60 * 60, // 14 days ago
      heartRate: 75,
      respirationRate: 15,
      pulseOx: 98,
      temperatureF: 98.8,
      temperatureC: 37.1,
      painScore: 4,
      systolicBP: 125,
      diastolicBP: 82,
    },
    {
      key: Math.floor(Date.now() / 1000) - 10 * 24 * 60 * 60, // 10 days ago
      heartRate: 88,
      respirationRate: 19,
      pulseOx: 97,
      temperatureF: 99.2,
      temperatureC: 37.3,
      painScore: 6,
      systolicBP: 140,
      diastolicBP: 88,
    },
    {
      key: Math.floor(Date.now() / 1000) - 7 * 24 * 60 * 60, // 7 days ago
      heartRate: 70,
      respirationRate: 16,
      pulseOx: 99,
      temperatureF: 98.5,
      temperatureC: 36.9,
      painScore: 3,
      systolicBP: 122,
      diastolicBP: 79,
    },
    {
      key: Math.floor(Date.now() / 1000) - DEFAULT_TIME_RANGE_SECONDS, // 3 months ago
      heartRate: 95,
      respirationRate: 22,
      pulseOx: 95,
      temperatureF: 100.1,
      temperatureC: 37.8,
      painScore: 8,
      systolicBP: 150,
      diastolicBP: 95,
    },
    {
      key: Math.floor(Date.now() / 1000) - 1 * 24 * 60 * 60, // 1 day ago
      heartRate: 78,
      respirationRate: 17,
      pulseOx: 98,
      temperatureF: 98.9,
      temperatureC: 37.2,
      painScore: 4,
      systolicBP: 128,
      diastolicBP: 84,
    },
    {
      key: Math.floor(Date.now() / 1000), // Current time
      heartRate: 82,
      respirationRate: 18,
      pulseOx: 97,
      temperatureF: 99.0,
      temperatureC: 37.2,
      painScore: 5,
      systolicBP: 130,
      diastolicBP: 86,
    },
  ];

  // Filter the fixed data based on the provided time range
  return fixedData.filter(vital =>
    vital.key >= startTime && vital.key <= endTime
  );
};

const StyledTable = styled(Table)({
  "& .MuiTableCell-root": {
    padding: "4px 8px",
    fontSize: "16px",
    borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
    "&.MuiTableCell-head": {
      backgroundColor: "#A1A7AF",
      fontWeight: 600,
      color: "#ffffff",
    },
    "&.MuiTableCell-body": {
      backgroundColor: "#ffffff",
      color: "#000000",
    },
  },
  "& .MuiTableRow-root:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  },
});

const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const month = date.toLocaleString("en-US", { month: "short" });
  const year = date.getFullYear();
  return `${hours}${minutes} ${day} ${month} ${year}`;
};

export const TestVitals: React.FC<TestVitalsProps> = ({
  patientId,
  startTime,
  endTime,
  editor,
  mode = "delete",
  onDelete,
}) => {
  const [vitalsData, setVitalsData] = useState<VitalsData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (startTime && endTime) {
      setIsLoading(true);
      try {
        const mockData = generateMockVitals(startTime, endTime);
        setVitalsData(mockData);
        setError(null);
      } catch (err) {
        setError("Failed to generate test data");
      } finally {
        setIsLoading(false);
      }
    }
  }, [startTime, endTime]);

  const copyToEditor = () => {
    if (!vitalsData.length || !editor) return;

    const vitalsText = vitalsData
      .map(
        (vital) =>
          `${formatTimestamp(vital.key)}: HR ${vital.heartRate}, RR ${vital.respirationRate}, SpO2 ${vital.pulseOx}%, Temp ${vital.temperatureF}°F, Pain ${vital.painScore}`
      )
      .join("\n");

    editor.chain().focus().insertContent(vitalsText).run();
  };

  return (
    <DataComponent
      title="Test Vitals"
      mode={mode}
      onDelete={onDelete}
      onCopy={copyToEditor}
      isLoading={isLoading}
      error={error}
      initialExpanded={true}
      data={{ vitals: vitalsData }}
    >
      {!vitalsData.length ? null : (
        <TableContainer component={Paper} sx={{ backgroundColor: 'transparent' }}>
          <StyledTable>
            <TableHead>
              <TableRow>
                <TableCell>Time</TableCell>
                <TableCell>Heart Rate</TableCell>
                <TableCell>Respiratory Rate</TableCell>
                <TableCell>SpO2</TableCell>
                <TableCell>Temperature</TableCell>
                <TableCell>Pain Score</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {vitalsData.map((vital) => (
                <TableRow key={vital.key}>
                  <TableCell>{formatTimestamp(vital.key)}</TableCell>
                  <TableCell>{vital.heartRate}</TableCell>
                  <TableCell>{vital.respirationRate}</TableCell>
                  <TableCell>{vital.pulseOx}%</TableCell>
                  <TableCell>{vital.temperatureF}°F</TableCell>
                  <TableCell>{vital.painScore}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </StyledTable>
        </TableContainer>
      )}
    </DataComponent>
  );
};
