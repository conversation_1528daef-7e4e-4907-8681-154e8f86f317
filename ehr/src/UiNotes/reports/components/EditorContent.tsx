import React, { useRef, useEffect, useState } from "react";
import { <PERSON>, Typography, Button } from "@mui/material";
import { EditorContent } from "@tiptap/react";
import { Editor } from "@tiptap/core";
import { Check } from "@mui/icons-material";
import { SignatureDialog } from "./SignatureDialog";
import { ReportAPI } from "../services/ReportAPI";
import { BubbleMenu } from "./BubbleMenu";
import { useSignatureConfig } from "../hooks/useSignatureConfig";
import { useAtomValue } from "jotai";
import { reportTypeAtom } from "../context/EditorContext";
import { REPORT_TYPES } from "../Constants";

interface EditorContentProps {
  editor: Editor | null;
  scrollToTop?: boolean;
  isReportEditor?: boolean;
  isDictationEditor?: boolean;
  isGeneratingReport?: boolean;
  selectedReport?: any; // Add selected report to access signature info
  captureCursorPosition?: () => void;
  onRefresh?: () => void; // Add refresh callback for after signing
  refreshSelectedReport?: () => void; // Add specific refresh for selected report
  campus?: string;
  patID?: string;
}

export const EditorContentArea: React.FC<EditorContentProps> = ({
  editor,
  scrollToTop = false,
  isReportEditor = false,
  isDictationEditor = false,
  isGeneratingReport = false,
  selectedReport,
  captureCursorPosition,
  onRefresh,
  refreshSelectedReport,
  campus,
  patID,
}) => {
  const editorContentRef = useRef<HTMLDivElement>(null);
  const hasClickedRef = useRef(false);
  const [signatureDialogOpen, setSignatureDialogOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { isSignableType } = useSignatureConfig();
  const currentReportType = useAtomValue(reportTypeAtom);

  // Check if report needs signature and hasn't been signed
  const isSignableNoteType = selectedReport?.type ? isSignableType(selectedReport.type) : false;
  const needsSignature = isSignableNoteType && !selectedReport?.signed;

  // Only show sign button for existing saved reports that need signatures
  const hasContent = !(!editor || !editor.getText().trim());
  const shouldShowSignButton = needsSignature && hasContent;

  const scrollToPosition = () => {
    if (scrollToTop && editorContentRef.current) {
      editorContentRef.current.scrollTop = 0;
    }
  };

  useEffect(() => {
    scrollToPosition();
  }, [scrollToTop]);

  useEffect(() => {
    if (editor && !editor.isDestroyed) {
      const handleUpdate = () => {
        hasClickedRef.current = true;
      };

      editor.on("update", handleUpdate);
      return () => {
        editor.off("update", handleUpdate);
      };
    }
  }, [editor]);

  const handleContentClick = (e: React.MouseEvent) => {
    if (captureCursorPosition) {
      captureCursorPosition();
    }
  };

  const handleSignClick = () => {
    setSignatureDialogOpen(true);
  };

  const handleSignConfirm = async (comment: string) => {
    if (!selectedReport || !campus || !patID) return;

    try {
      const staffid = typeof Cci !== 'undefined' && Cci ? Cci.util.Staff.getSid() : undefined;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";

      console.log("Signing report:", selectedReport.noteID, "with comment:", comment);

      // Sign existing report
      const signResponse = await ReportAPI.signReport(
        campus,
        patID,
        selectedReport.noteID,
        selectedReport.type,
        comment,
        staffid,
        ccitoken
      );

      console.log("Sign response:", signResponse);

      // Refresh the reports list to show updated signature status
      console.log("Refreshing report list...");
      setIsRefreshing(true);
      onRefresh?.();
      
      // Also refresh the selected report specifically to get latest signature data
      console.log("Scheduling individual report refresh...");
      setTimeout(async () => {
        console.log("Executing individual report refresh...");
        await refreshSelectedReport?.();
        setIsRefreshing(false);
      }, 1000); // Increased delay to ensure backend has processed the signature
      
      // Additional refresh after a longer delay to ensure all data is updated
      setTimeout(async () => {
        console.log("Executing final refresh to ensure all data is updated...");
        await refreshSelectedReport?.();
      }, 3000);
      
    } catch (error) {
      console.error("Failed to sign report:", error);
      throw error;
    } finally {
      setSignatureDialogOpen(false);
    }
  };

  const isEmpty = !editor || !editor.getText().trim();

  return (
    <div
      style={{
        position: "relative",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        minHeight: 0
      }}
    >
      <div
        ref={editorContentRef}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: (shouldShowSignButton || selectedReport?.signed) ? "200px" : 0, // Increased space for signature section to prevent overlap
          overflowY: "auto",
          overflowX: "hidden",
          padding: "1rem",
          paddingBottom: (shouldShowSignButton || selectedReport?.signed) ? "3rem" : "1rem" // Extra bottom padding when signature section is present
        }}
        onClick={handleContentClick}
      >
        <div
          className={`editor-container ${isDictationEditor ? 'dictation-editor' : ''} ${isReportEditor ? 'report-editor' : ''}`}
          style={{
            height: "100%",
            position: "relative"
          }}
        >
          {/* Read-only indicator for signed notes */}
          {isReportEditor && selectedReport?.required_signature && selectedReport?.signed && (
            <Box
              data-testid="readonly-indicator"
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                backgroundColor: "#fff3cd",
                border: "1px solid #ffeaa7",
                borderBottom: "none",
                padding: "8px 12px",
                zIndex: 10,
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "#856404",
                  fontWeight: 500,
                  fontSize: "12px"
                }}
              >
                📝 This note is read-only - it has been signed and cannot be modified
              </Typography>
            </Box>
          )}
          <EditorContent 
            editor={editor} 
            style={{
              opacity: isReportEditor && selectedReport?.required_signature && selectedReport?.signed ? 0.7 : 1,
              pointerEvents: isReportEditor && selectedReport?.required_signature && selectedReport?.signed ? 'none' : 'auto'
            }}
          />
          {editor && <BubbleMenu editor={editor} />}

          {/* Generated Report placeholder */}
          {isReportEditor && isEmpty && !isGeneratingReport && !hasClickedRef.current && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                textAlign: "center",
                color: "#666",
                pointerEvents: "none",
                width: "80%",
                maxWidth: "600px",
                zIndex: 1
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <Typography variant="h6" sx={{ mb: 2, color: "#3C4B50" }}>
                Generated Report Area
              </Typography>
              <Typography variant="body1" sx={{ color: "#666" }}>
                This area will display the generated report when you click the "Generate Report" button.
                The report will be formatted with proper sections and styling for medical documentation.
              </Typography>
            </Box>
          )}

          {/* Dictation editor placeholder */}
          {isDictationEditor && isEmpty && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                textAlign: "center",
                color: "#666",
                pointerEvents: "none",
                width: "80%",
                maxWidth: "600px",
                zIndex: 1
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <Typography variant="h6" sx={{ mb: 2, color: "#3C4B50" }}>
                Dictation Area
              </Typography>
              <Typography variant="body1" sx={{ color: "#666" }}>
                Start dictating your notes here. You can use voice input or type directly.
                The content will be used to generate a structured medical report.
              </Typography>
            </Box>
          )}


        </div>
      </div>

      {/* Provider Signature Section - Show sign button or signature info */}
      {isReportEditor && (shouldShowSignButton || selectedReport?.signed) && (
        <Box
          sx={{
            position: "absolute",
            bottom: "60px", // Increased distance from bottom to avoid system UI
            left: 0,
            right: 0,
            minHeight: "120px", // Increased minimum height
            maxHeight: "240px", // Increased maximum height for longer signature content
            backgroundColor: "#f8f9fa",
            borderTop: "1px solid #e0e0e0",
            display: "flex",
            alignItems: "flex-start", // Changed from center to flex-start for better alignment
            justifyContent: "flex-start",
            padding: "20px", // Increased padding
            overflowY: "auto", // Allow scrolling if content is too long
            zIndex: 1000 // Ensure it's on top
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              gap: 1
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: "#000000",
                fontWeight: 700,
                textAlign: "left",
                fontSize: "16px"
              }}
            >
              Provider Signature
              {isRefreshing && (
                <span style={{ marginLeft: "8px", fontSize: "12px", color: "#666" }}>
                  (Updating...)
                </span>
              )}
            </Typography>
            
            {/* Show Sign Button when not signed */}
            {shouldShowSignButton && (
              <Button
                variant="contained"
                color="primary"
                startIcon={
                  <Box
                    sx={{
                      backgroundColor: "white",
                      borderRadius: "50%",
                      width: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 1
                    }}
                  >
                    <Check sx={{ fontSize: "14px", color: "#1976d2" }} />
                  </Box>
                }
                onClick={handleSignClick}
                sx={{
                  backgroundColor: "#1976d2",
                  color: "white",
                  fontWeight: 500,
                  textTransform: "none",
                  fontSize: "14px",
                  px: 3,
                  py: 1,
                  minWidth: "auto",
                  width: "auto",
                  "&:hover": {
                    backgroundColor: "#1565c0"
                  }
                }}
              >
                Sign Note
              </Button>
            )}
            
            {/* Show Signature Information when signed */}
            {selectedReport?.signed && (
              <Box>
                {/* Display signatures from signatures array (new format) */}
                {selectedReport?.signatures && selectedReport.signatures.length > 0 ? (
                  selectedReport.signatures.map((signature: any, index: number) => (
                    <Box key={index} sx={{ marginBottom: index < selectedReport.signatures.length - 1 ? "0.5rem" : 0 }}>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 700,
                          fontSize: "16px",
                          color: "#000000",
                          marginBottom: "0.5rem",
                          fontFamily: "sans-serif"
                        }}
                      >
                        {signature.signedByName || signature.signedBy || "Unknown Provider"}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 400,
                          fontSize: "16px",
                          color: "#666666",
                          fontFamily: "sans-serif"
                        }}
                      >
                        Signed: {signature.signedAt ? (() => {
                          try {
                            const date = new Date(signature.signedAt);
                            if (isNaN(date.getTime())) {
                              // Try parsing as timestamp (seconds since epoch)
                              const timestamp = parseInt(signature.signedAt);
                              if (!isNaN(timestamp)) {
                                const dateFromTimestamp = new Date(timestamp * 1000);
                                return `${dateFromTimestamp.toLocaleDateString('en-US', {
                                  month: '2-digit',
                                  day: '2-digit',
                                  year: 'numeric'
                                })} | ${dateFromTimestamp.toLocaleTimeString('en-US', {
                                  hour: '2-digit',
                                  minute: '2-digit',
                                  hour12: false
                                })}`;
                              }
                              return 'Unknown Date | Unknown Time';
                            }
                            return `${date.toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric'
                            })} | ${date.toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: false
                            })}`;
                          } catch (error) {
                            return 'Unknown Date | Unknown Time';
                          }
                        })() : 'Unknown Date | Unknown Time'}
                      </Typography>
                      {signature.comment && (
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 400,
                            fontSize: "16px",
                            color: "#888888",
                            fontStyle: "italic",
                            marginTop: "0.25rem"
                          }}
                        >
                          Comment: {signature.comment}
                        </Typography>
                      )}
                    </Box>
                  ))
                ) : (
                  /* Fallback to legacy signature data */
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 700,
                        fontSize: "16px",
                        color: "#000000",
                        marginBottom: "0.5rem",
                        fontFamily: "sans-serif"
                      }}
                    >
                      {selectedReport.lastSignedBy || "Unknown Provider"}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 400,
                        fontSize: "16px",
                        color: "#666666",
                        fontFamily: "sans-serif"
                      }}
                    >
                      Signed: {selectedReport.lastSignedAt ? (() => {
                        try {
                          const date = new Date(selectedReport.lastSignedAt);
                          if (isNaN(date.getTime())) {
                            // Try parsing as timestamp (seconds since epoch)
                            const timestamp = parseInt(selectedReport.lastSignedAt);
                            if (!isNaN(timestamp)) {
                              const dateFromTimestamp = new Date(timestamp * 1000);
                              return `${dateFromTimestamp.toLocaleDateString('en-US', {
                                month: '2-digit',
                                day: '2-digit',
                                year: 'numeric'
                              })} | ${dateFromTimestamp.toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: false
                              })}`;
                            }
                            return 'Unknown Date | Unknown Time';
                          }
                          return `${date.toLocaleDateString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric'
                          })} | ${date.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                          })}`;
                        } catch (error) {
                          return 'Unknown Date | Unknown Time';
                        }
                      })() : 'Unknown Date | Unknown Time'}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        </Box>
      )}

      {/* Signature Dialog */}
      <SignatureDialog
        open={signatureDialogOpen}
        onClose={() => setSignatureDialogOpen(false)}
        onSign={handleSignConfirm}
        reportTitle={selectedReport?.title || "Generated Report"}
        reportType={selectedReport?.type || "Report"}
      />
    </div>
  );
}; 