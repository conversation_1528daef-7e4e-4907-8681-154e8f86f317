import React, { useState, useEffect, useRef } from "react";
import { Box, Typography, IconButton, Tooltip } from "@mui/material";
import { DateRange, ContentCopy } from "@mui/icons-material";
import { useAtom, useAtomValue } from "jotai";
import { PatientDemographics } from "./PatientDemographics";
import { Procedures } from "./Procedures";
import { HomeMeds } from "./HomeMeds";
import { MedRec } from "./MedRec";
import { LabResults } from "./LabResults";
import { Medications } from "./Medications";
import { Diagnosis } from "./Diagnosis";
import { Problems } from "./Problems";
import { Vitals } from "./Vitals";
import { VitalsWithCheckbox } from "./VitalsWithCheckbox";
import { TestVitals } from "./TestVitals";
import { ReviewOfSystems } from "./ReviewOfSystems";
import { Exam } from "./Exam";
import { ResizablePanel } from "./styles";
import { RightPanelDragHandle } from "./PanelDragHandles";
import {
  DEFAULT_PATIENT_ID,
  SHOW_TEST_VITALS,
  REPORT_TYPE_COMPONENTS,
  API_BASE_URL,
  EXAM_SECTION,
  DEFAULT_TIME_RANGE_SECONDS,
} from "../Constants";
import {
  getCampus,
  getPatID,
  getVisitKey,
} from "../../common/utils/runtimeUtils";
import { ExamAPI } from "../services/ExamAPI";
import { reportTypeAtom, timeRangeAtom } from "../context/EditorContext";
import { TimeRangeDialog } from "./TimeRangeDialog";
import { getPNGridScreenConf, getPNGridScreenData } from "../../../PNEditGridRs/util/PNGridData";

interface PatientPanelProps {
  panelWidth: number;
  editor: any;
  onDragStart: (e: React.MouseEvent) => void;
}

export const PatientPanel: React.FC<PatientPanelProps> = ({
  panelWidth,
  editor,
  onDragStart,
}) => {
  const reportType = useAtomValue(reportTypeAtom);
  const allowedComponents = REPORT_TYPE_COMPONENTS[reportType] || [];
  const [isTimeRangeDialogOpen, setIsTimeRangeDialogOpen] = useState(false);
  const vitalsSelectedDataRef = useRef<any>(null);

  // Use centralized time range
  const [timeRange, setTimeRange] = useAtom(timeRangeAtom);

  // Dynamic patient ID state management
  const [dynamicPatientId, setDynamicPatientId] = useState<string>(() => {
    const globalCci = (window as any)?.Cci || (window as any)?.cci;
    return globalCci?.Patient?._mrn || DEFAULT_PATIENT_ID;
  });

  // Monitor Cci.Patient._mrn changes
  useEffect(() => {
    const checkPatientId = () => {
      const globalCci = (window as any)?.Cci || (window as any)?.cci;
      const currentPatientId = globalCci?.Patient?._mrn;

      if (currentPatientId && currentPatientId !== dynamicPatientId) {
        setDynamicPatientId(currentPatientId);
      }
    };

    checkPatientId();
    const interval = setInterval(checkPatientId, 1000);
    return () => clearInterval(interval);
  }, [dynamicPatientId]);

  const shouldShowComponent = (componentName: string) => {
    return allowedComponents.includes(componentName);
  };

  const handleTimeRangeApply = (newTimeRange: {
    startTime: number;
    endTime: number;
  }) => {
    setTimeRange(newTimeRange);
    setIsTimeRangeDialogOpen(false);
  };

  const handleCopyProblemsToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      // Fetch latest problems data
      const response = await fetch(
        `${API_BASE_URL}/getData?object=Problems&patid=${dynamicPatientId}&type=UnifiedCCO`,
        {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            Authorization: "Bearer " + localStorage.getItem("webtoken"),
            "X-Forwarded-For": "127.0.0.1",
            "X-Real-IP": "127.0.0.1",
            "X-Requested-With": "XMLHttpRequest",
          },
          credentials: "include",
        }
      );

      const result = await response.json();
      const problemsData = result.data || [];

      // Format the problems data
      const formattedProblems = formatProblemsForReport(problemsData);

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Update the problems section in the editor
      await updateProblemsSection(editor, formattedProblems);
    } catch (error) {
      console.error("Error copying problems data to report:", error);
    }
  };

  const handleCopyHomeMedsToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      // Fetch latest home medications data
      const endTimeValue = Math.floor(Date.now() / 1000); // Current time
      const startTimeValue = 0; // Beginning of time (Unix epoch)

      const response = await fetch(
        `${API_BASE_URL}/getData?object=HomeMeds&patid=${dynamicPatientId}&type=UnifiedCCO&starttime=${startTimeValue}&endtime=${endTimeValue}`,
        {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            Authorization: "Bearer " + localStorage.getItem("webtoken"),
            "X-Forwarded-For": "127.0.0.1",
            "X-Real-IP": "127.0.0.1",
            "X-Requested-With": "XMLHttpRequest",
          },
          credentials: "include",
        }
      );

      const result = await response.json();
      const homeMedsData = result.data || [];

      // Format the home medications data
      const formattedHomeMeds = formatHomeMedsForReport(homeMedsData);

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Update the home medications section in the editor
      await updateHomeMedsSection(editor, formattedHomeMeds);
    } catch (error) {
      console.error("Error copying home medications data to report:", error);
    }
  };

  const handleCopyReviewOfSystemsToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      // Get the dbpath for the current patient
      const dbpath = Cci?.Patient?.getDbpath();
      if (!dbpath) {
        console.error("No patient selected");
        return;
      }

      // Fetch PNEditGridRs configuration and data
      const screenConf = await getPNGridScreenConf();
      if (!screenConf || !screenConf.conf || !screenConf.conf.data || screenConf.conf.data.length === 0) {
        console.error("Failed to get PNEditGridRs configuration");
        return;
      }

      // Get the grid data
      const gridData = await getPNGridScreenData(screenConf, dbpath);
      if (!gridData || !Array.isArray(gridData) || gridData.length === 0) {
        console.error("No Review of Systems data available");
        return;
      }

      // Format the review of systems data from grid data
      const formattedROS = formatReviewOfSystemsForReport(gridData);

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Update the review of systems section in the editor
      await updateReviewOfSystemsSection(editor, formattedROS);
    } catch (error) {
      console.error("Error copying review of systems data to report:", error);
    }
  };

  const handleCopyExamToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      // Fetch latest exam data using ExamAPI
      const campus = getCampus();
      const patID = getPatID();
      const visitkey = getVisitKey();

      const examData = await ExamAPI.getExam(campus, patID, visitkey);

      // Format the exam data
      const formattedExam = formatExamForReport(examData);

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Update the exam section in the editor
      await updateExamSection(editor, formattedExam);
    } catch (error) {
      console.error("Error copying exam data to report:", error);
    }
  };

  const handleCopyProceduresToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      // Fetch latest procedures data
      let url = `${API_BASE_URL}/getData?object=ProcedureOrder&patid=${dynamicPatientId}&type=UnifiedCCO`;

      if (timeRange.startTime && timeRange.endTime) {
        url += `&starttime=${timeRange.startTime}&endtime=${timeRange.endTime}`;
      }

      const response = await fetch(url, {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        credentials: "include",
      });

      const result = await response.json();
      const proceduresData = result.data || [];

      // Format the procedures data (will handle empty case in formatting)
      const formattedProcedures = formatProceduresForReport(proceduresData);

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Always update the procedures section, even if empty (to replace placeholders)
      // Update the procedures section in the editor
      await updateProceduresSection(editor, formattedProcedures);
    } catch (error) {
      console.error("Error copying procedures data to report:", error);
    }
  };

  const updateVitalsSelectedData = (vitalsDataWithSelection: any) => {
    vitalsSelectedDataRef.current = vitalsDataWithSelection;
  };

  const handleCopyVitalsToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      let formattedVitals: string;

      // Always use the latest selection from the ref
      const dataToUse = vitalsSelectedDataRef.current;

      if (dataToUse && dataToUse.vitals && Array.isArray(dataToUse.vitals)) {
        // Check if vitals component is enabled
        if (!dataToUse.isEnabled) {
          return; // Don't copy if component is disabled
        }

        // Use the data passed from the Vitals component (includes selected columns)

        // Filter the vitals data based on selected columns
        let vitalsDataToFormat = dataToUse.vitals;
        if (
          dataToUse.isSelectiveMode &&
          dataToUse.selectedColumns &&
          dataToUse.selectedColumns.length > 0
        ) {
          const selectedTimeKeys = dataToUse.selectedColumns.map(
            (col: any) => col.timeKey
          );
          vitalsDataToFormat = dataToUse.vitals.filter((vital: any) =>
            selectedTimeKeys.includes(vital.key)
          );
        }

        // Convert camelCase properties to snake_case for formatVitalsForReport
        const convertedVitalsData = vitalsDataToFormat.map((vital: any) => ({
          key: vital.key,
          heart_rate: vital.heartRate,
          respiration_rate: vital.respirationRate,
          pulse_ox: vital.pulseOx,
          temperature_f: vital.temperatureF,
          temperature_c: vital.temperatureC,
          pain_score: vital.painScore,
        }));

        // Use the working formatVitalsForReport function
        formattedVitals = formatVitalsForReport(convertedVitalsData);
      } else {
        // Fallback: fetch latest vitals data (for backward compatibility)
        let url = `${API_BASE_URL}/getData?object=Vitals&patid=${dynamicPatientId}&type=UnifiedCCO`;

        // Always include time range parameters
        const endTimeValue = timeRange.endTime || Math.floor(Date.now() / 1000);
        const startTimeValue =
          timeRange.startTime || endTimeValue - DEFAULT_TIME_RANGE_SECONDS;
        url += `&starttime=${startTimeValue}&endtime=${endTimeValue}`;

        const response = await fetch(url, {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            Authorization: "Bearer " + localStorage.getItem("webtoken"),
            "X-Forwarded-For": "127.0.0.1",
            "X-Real-IP": "127.0.0.1",
            "X-Requested-With": "XMLHttpRequest",
          },
          credentials: "include",
        });

        const result = await response.json();
        const vitalsData = result.data || [];

        // Format the vitals data (will handle empty case in formatting)
        formattedVitals = formatVitalsForReport(vitalsData);
      }

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Always update the vitals section, even if empty (to replace placeholders)
      // Update the vitals section in the editor
      await updateVitalsSection(editor, formattedVitals);
    } catch (error) {
      console.error("Error copying vitals data to report:", error);
    }
  };

  const handleCopyLabResultsToReport = async () => {
    if (!editor) {
      return;
    }

    try {
      // Fetch latest lab results data
      let url = `${API_BASE_URL}/getData?object=LabResults&patid=${dynamicPatientId}&type=UnifiedCCO`;

      // Always include time range parameters
      const endTimeValue = timeRange.endTime || Math.floor(Date.now() / 1000);
      const startTimeValue =
        timeRange.startTime || endTimeValue - DEFAULT_TIME_RANGE_SECONDS;
      url += `&starttime=${startTimeValue}&endtime=${endTimeValue}`;

      const response = await fetch(url, {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        credentials: "include",
      });

      const result = await response.json();
      const labResultsData = result.data || [];

      // Format the lab results data (will handle empty case in formatting)
      const formattedLabResults = formatLabResultsForReport(labResultsData);

      // Add a small delay to ensure the editor content is updated
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Always update the lab results section, even if empty (to replace placeholders)
      // Update the lab results section in the editor
      await updateLabResultsSection(editor, formattedLabResults);
    } catch (error) {
      console.error("Error copying lab results data to report:", error);
    }
  };

  return (
    <ResizablePanel
      className="patient-panel"
      sx={{
        width: `${panelWidth}px`,
        height: "100vh", // Force full viewport height
        display: "flex",
        flexDirection: "column",
        overflow: "hidden", // Prevent panel from scrolling
      }}
    >
      <RightPanelDragHandle onMouseDown={onDragStart} />

      {/* Fixed header with Patient Data title and time range selector */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "8px 12px",
          backgroundColor: "#45B766",
          borderBottom: "1px solid #e0e0e0",
          minHeight: "40px",
          maxHeight: "40px",
          flexShrink: 0,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: "16px",
            fontFamily: "Roboto",
            fontWeight: 500,
            color: "white",
          }}
        >
          Patient Data
        </Typography>

        <Box sx={{ display: "flex", gap: 1 }}>
          <Tooltip title="Copy Latest Problems, Home Medications, Review of Systems, Exam, Procedures, Vitals & Lab Results to Report">
            <IconButton
              onClick={async () => {
                await handleCopyProblemsToReport();
                await handleCopyHomeMedsToReport();
                await handleCopyReviewOfSystemsToReport();
                await handleCopyExamToReport();
                await handleCopyProceduresToReport();
                await handleCopyVitalsToReport();
                await handleCopyLabResultsToReport();
              }}
              size="small"
              sx={{
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <ContentCopy />
            </IconButton>
          </Tooltip>
          <Tooltip title="Set Time Range for Data">
            <IconButton
              onClick={() => setIsTimeRangeDialogOpen(true)}
              size="small"
              sx={{
                color: "white",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <DateRange />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Scrollable content area */}
      <Box sx={{
        flex: 1,
        overflow: "auto",
        p: 0.625,
        minHeight: 0, // Allow flex to shrink
        display: "flex",
        flexDirection: "column"
      }}>
        {shouldShowComponent("Demographics") && (
          <PatientDemographics
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
          />
        )}
        {shouldShowComponent("Procedures") && (
          <Procedures
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
          />
        )}
        {shouldShowComponent("HomeMeds") && (
          <HomeMeds
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
          />
        )}
        {shouldShowComponent("MedRec") && (
          <MedRec
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
          />
        )}
        {shouldShowComponent("LabResults") && (
          <LabResults
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            onCopy={handleCopyLabResultsToReport}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
          />
        )}
        {shouldShowComponent("Medications") && (
          <Medications
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
          />
        )}
        {shouldShowComponent("Diagnosis") && (
          <Diagnosis
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            initialExpanded={true}
          />
        )}
        {shouldShowComponent("Problems") && (
          <Problems
            patientId={dynamicPatientId}
            onDelete={() => { }}
            editor={editor}
            mode="copy"
            initialExpanded={true}
          />
        )}
        {shouldShowComponent("Vitals") && (
          <VitalsWithCheckbox
            patientId={dynamicPatientId}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
            editor={editor}
            mode="copy"
            onCopy={updateVitalsSelectedData}
          />
        )}
        {SHOW_TEST_VITALS && shouldShowComponent("Vitals") && (
          <TestVitals
            patientId={dynamicPatientId}
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
            editor={editor}
            mode="copy"
            onDelete={() => {
              if (editor) {
                editor.commands.deleteNode("vitals");
              }
            }}
          />
        )}
        {shouldShowComponent("ReviewOfSystems") && (
          <ReviewOfSystems
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            initialExpanded={true}
          />
        )}
        {shouldShowComponent("Exam") && (
          <Exam
            patientId={dynamicPatientId}
            mode="copy"
            editor={editor}
            onDelete={() => { }}
            initialExpanded={true}
          />
        )}

        {/* Spacer to fill remaining height */}
        <Box sx={{ flex: 1, minHeight: 0 }} />
      </Box>

      <TimeRangeDialog
        open={isTimeRangeDialogOpen}
        onClose={() => setIsTimeRangeDialogOpen(false)}
        onApply={handleTimeRangeApply}
        currentTimeRange={timeRange}
      />
    </ResizablePanel>
  );
};

// Helper function to format problems data for report
const formatProblemsForReport = (data: any[]): string => {
  if (!data || data.length === 0) {
    return '<div class="report-section"><strong>Problems</strong><br>No problems available.</div>';
  }

  const tableRows = data
    .map((problem: any) => {
      const name = problem.name || problem.problem_name || "N/A";
      const onset = problem.onset_date || "N/A";
      const status = problem.status || "N/A";
      return `<tr><td>${name}</td><td>${onset}</td><td>${status}</td></tr>`;
    })
    .join("");

  return `<div class="report-section"><strong>Problems</strong></div><div><table style="border-collapse: collapse; width: 100%; margin: 10px 0;"><thead><tr style="background-color: #f5f5f5;"><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Problem</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Onset Date</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Status</th></tr></thead><tbody>${tableRows}</tbody></table></div>`;
};

// Helper function to format home medications data for report
const formatHomeMedsForReport = (data: any[]): string => {
  if (!data || data.length === 0) {
    return '<div class="report-section"><strong>Home Medications</strong><br>No home medications available.</div>';
  }

  const tableRows = data
    .map((med: any) => {
      const name = med.name || "N/A";
      const dosage = med.dosage || "N/A";
      const frequency = med.frequency || "N/A";
      const startDate = med.startDate || med.starttime || "N/A";
      const status = med.status || "N/A";
      const provider = med.provider || "N/A";
      return `<tr><td>${name}</td><td>${dosage}</td><td>${frequency}</td><td>${startDate}</td><td>${status}</td><td>${provider}</td></tr>`;
    })
    .join("");

  return `<div class="report-section"><strong>Home Medications</strong></div><div><table style="border-collapse: collapse; width: 100%; margin: 10px 0;"><thead><tr style="background-color: #f5f5f5;"><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Medication</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Dosage</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Frequency</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Start Date</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Status</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Provider</th></tr></thead><tbody>${tableRows}</tbody></table></div>`;
};

// Helper function to format review of systems data for report from PNEditGridRs grid data
const formatReviewOfSystemsForReport = (gridData: any[]): string => {
  if (!gridData || !Array.isArray(gridData) || gridData.length === 0) {
    return '<div class="report-section"><strong>Review of Systems</strong><br>No review of systems data available.</div>';
  }

  const sections: string[] = [];

  // Process each grid item (section)
  gridData.forEach((item) => {
    if (!item || !item.label || !item.rows || !Array.isArray(item.rows)) {
      return;
    }

    // Skip if the entire section is marked as N/A
    if (item.isNA) {
      return;
    }

    const sectionFindings: string[] = [];

    // Process each row in the section
    item.rows.forEach((row: any) => {
      if (!row || !row.name) {
        return;
      }

      if (row.positive) {
        sectionFindings.push(`Positive for ${row.name}`);
      } else if (row.negative) {
        sectionFindings.push(`Negative for ${row.name}`);
      }
    });

    // Only add the section if it has findings
    if (sectionFindings.length > 0) {
      sections.push(`<strong>${item.label}:</strong> ${sectionFindings.join('. ')}`);
    }
  });

  if (sections.length === 0) {
    return '<div class="report-section"><strong>Review of Systems</strong><br>No review of systems findings documented.</div>';
  }

  return `<div class="report-section"><strong>Review of Systems</strong><br>${sections.join('<br>')}</div>`;
};

// Helper function to format exam data for report
const formatExamForReport = (data: any): string => {
  if (!data) {
    return '<div class="report-section"><strong>Exam</strong><br>No exam data available.</div>';
  }

  // Handle both new format (examData) and legacy format (exam_data)
  const examData = data.examData || data.exam_data;
  if (!examData) {
    return '<div class="report-section"><strong>Exam</strong><br>No exam findings documented.</div>';
  }

  const sections: string[] = [];

  // Add vitals and nursing note status
  if (data.vitalsReviewed) {
    sections.push("✓ Vital signs reviewed");
  }
  if (data.nursingNoteReviewed) {
    sections.push("✓ Nursing note reviewed");
  }

  // Process exam data from different tabs - only show selected findings
  Object.keys(examData).forEach((tabName) => {
    if (tabName === "ALL") return; // Skip ALL tab as it's just references

    const tabData = examData[tabName];
    if (Array.isArray(tabData) && tabData.length > 0) {
      const tabFindings: string[] = [];

      tabData.forEach((card: any) => {
        if (card.sections && card.sections.length > 0) {
          card.sections.forEach((section: any) => {
            section.rows.forEach((row: any) => {
              if (row.type === "checkbox" && row.value === true) {
                tabFindings.push(row.label);
              } else if (row.type === "plusminus" && row.value) {
                const symbol =
                  row.value === "+" ? "+" : row.value === "-" ? "-" : "";
                if (symbol) {
                  const prefix =
                    symbol === "+" ? "Positive for " : "Negative for ";
                  tabFindings.push(prefix + row.label);
                }
              } else if (
                row.type === "multiCheckbox" &&
                row.checkboxes &&
                Array.isArray(row.checkboxes)
              ) {
                // Process multiCheckbox rows - find checked checkboxes
                const checkedCheckboxes = row.checkboxes.filter(
                  (checkbox: any) => checkbox.value === true
                );
                if (checkedCheckboxes.length > 0) {
                  const checkboxLabels = checkedCheckboxes.map(
                    (checkbox: any) => checkbox.label
                  );
                  tabFindings.push(
                    `${row.label}: ${checkboxLabels.join(", ")}`
                  );
                }
              }
            });
          });
        }

        // Add additional notes if present
        if (card.notes && card.notes.trim()) {
          tabFindings.push(card.notes.trim());
        }
      });

      // Only add the tab if it has findings
      if (tabFindings.length > 0) {
        const tabDisplayName = getTabDisplayName(tabName);
        sections.push(
          `<strong>${tabDisplayName}:</strong> ${tabFindings.join(". ")}`
        );
      }
    }
  });

  if (sections.length === 0) {
    return '<div class="report-section"><strong>Exam</strong><br>No exam findings documented.</div>';
  }

  return `<div class="report-section"><strong>Exam</strong><br>${sections.join("<br>")}</div>`;
};

// Helper function to format procedures data for report
const formatProceduresForReport = (data: any[]): string => {
  if (!data || data.length === 0) {
    return '<div class="report-section"><strong>Procedures</strong><br>No procedures available.</div>';
  }

  const tableRows = data
    .map((procedure: any) => {
      // Clean the name to remove any HTML tags that might be present
      let name = procedure.name || "N/A";
      if (typeof name === "string") {
        // More aggressive HTML cleaning
        name = name
          .replace(/<strong>/g, "")
          .replace(/<\/strong>/g, "")
          .replace(/<b>/g, "")
          .replace(/<\/b>/g, "")
          .replace(/<[^>]*>/g, ""); // Remove any remaining HTML tags
      }
      const date = procedure.starttime || "N/A";
      return `<tr><td>${name}</td><td>${date}</td></tr>`;
    })
    .join("");

  return `<div class="report-section"><strong>Procedures</strong></div><div><table style="border-collapse: collapse; width: 100%; margin: 10px 0;"><thead><tr style="background-color: #f5f5f5;"><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Name</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Date Performed</th></tr></thead><tbody>${tableRows}</tbody></table></div>`;
};

// Helper function to format vitals data for report
const formatVitalsForReport = (data: any[]): string => {
  if (!data || data.length === 0) {
    return '<div class="report-section"><strong>Vital Signs</strong><br>No vitals available.</div>';
  }

  // Define normal ranges for vital signs
  const normalRanges = {
    heart_rate: { min: 60, max: 100 },
    respiration_rate: { min: 12, max: 18 },
    pulse_ox: { min: 95, max: 100 },
    temperature_f: { min: 97.8, max: 100.4 },
    pain_score: { min: 0, max: 10 },
    cuff_bp_systolic: { min: 90, max: 120 },
    cuff_bp_diastolic: { min: 60, max: 80 },
  };

  // Helper function to check if a value is abnormal
  const isAbnormal = (value: number, vitalType: string): boolean => {
    const range = normalRanges[vitalType as keyof typeof normalRanges];
    if (!range) return false;
    return value < range.min || value > range.max;
  };

  // Filter time keys to only include those that have at least some vital data
  const timeKeysWithData = data
    .map((vital) => vital.key)
    .filter((key) => key)
    .filter((key) => {
      const vital = data.find((v) => v.key === key);
      if (!vital) return false;
      // Check if this time point has any vital data at all
      return (
        (vital.heart_rate !== null && vital.heart_rate !== undefined) ||
        (vital.respiration_rate !== null &&
          vital.respiration_rate !== undefined) ||
        (vital.pulse_ox !== null && vital.pulse_ox !== undefined) ||
        (vital.temperature_f !== null && vital.temperature_f !== undefined) ||
        (vital.pain_score !== null && vital.pain_score !== undefined) ||
        (vital.cuff_bp_systolic !== null && vital.cuff_bp_systolic !== undefined) ||
        (vital.cuff_bp_diastolic !== null && vital.cuff_bp_diastolic !== undefined)
      );
    })
    .sort((a, b) => a - b);

  if (timeKeysWithData.length === 0) {
    return '<div class="report-section"><strong>Vital Signs</strong><br>No vitals available.</div>';
  }

  // Create header row
  let headerRow = "<tr><td><strong>Vital Sign</strong></td>";
  timeKeysWithData.forEach((key) => {
    const date = new Date(key * 1000);
    const dateStr = `${(date.getMonth() + 1).toString().padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")}/${date.getFullYear()}`;
    const timeStr = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    headerRow += `<td><strong>${dateStr}</strong><br>${timeStr}</td>`;
  });
  headerRow += "</tr>";

  // Create data rows
  const vitalTypes = [
    { key: "heart_rate", name: "Heart Rate", unit: "bpm" },
    { key: "respiration_rate", name: "Respiratory Rate", unit: "breaths/min" },
    { key: "pulse_ox", name: "O2 Saturation", unit: "%" },
    { key: "temperature_f", name: "Temperature (°F)", unit: "°F" },
    { key: "pain_score", name: "Pain Score", unit: "" },
    { key: "blood_pressure", name: "Blood Pressure", unit: "mmHg" },
  ];

  let dataRows = "";
  vitalTypes.forEach((vitalType) => {
    // Check if this vital type has any data at all
    const hasAnyData = timeKeysWithData.some((key) => {
      const vital = data.find((v) => v.key === key);

      if (vitalType.key === "blood_pressure") {
        return (
          vital &&
          vital.cuff_bp_systolic !== null &&
          vital.cuff_bp_systolic !== undefined &&
          vital.cuff_bp_diastolic !== null &&
          vital.cuff_bp_diastolic !== undefined
        );
      }

      return (
        vital &&
        vital[vitalType.key] !== null &&
        vital[vitalType.key] !== undefined
      );
    });

    // Skip row if no data for this vital type
    if (!hasAnyData) {
      return; // Skip this row
    }

    let row = `<tr><td><strong>${vitalType.name}</strong></td>`;
    timeKeysWithData.forEach((key) => {
      const vital = data.find((v) => v.key === key);

      if (vitalType.key === "blood_pressure") {
        // Special handling for blood pressure
        const systolic = vital ? vital.cuff_bp_systolic : null;
        const diastolic = vital ? vital.cuff_bp_diastolic : null;

        if (systolic !== null && systolic !== undefined && diastolic !== null && diastolic !== undefined) {
          const systolicAbnormal = isAbnormal(systolic, 'cuff_bp_systolic');
          const diastolicAbnormal = isAbnormal(diastolic, 'cuff_bp_diastolic');

          if (systolicAbnormal || diastolicAbnormal) {
            row += `<td><span style="color: #ff0000;">${systolic}/${diastolic}</span> ${vitalType.unit}</td>`;
          } else {
            row += `<td>${systolic}/${diastolic} ${vitalType.unit}</td>`;
          }
        } else {
          row += "<td>N/A</td>";
        }
      } else {
        // Handle other vital types
        const value = vital ? vital[vitalType.key] : null;
        if (value !== null && value !== undefined) {
          const abnormal = isAbnormal(value, vitalType.key);
          if (abnormal) {
            row += `<td><span style="color: #ff0000;">${value}</span> ${vitalType.unit}</td>`;
          } else {
            row += `<td>${value} ${vitalType.unit}</td>`;
          }
        } else {
          row += "<td>N/A</td>";
        }
      }
    });
    row += "</tr>";
    dataRows += row;
  });

  return `<div class="report-section"><strong>Vital Signs</strong></div><table>${headerRow}${dataRows}</table>`;
};

// Helper function to format date as MM/DD/YYYY
const formatDate = (dateString: string): string => {
  if (!dateString) return "Unknown";

  try {
    // Handle format: "HHMM DD MMM YYYY" (e.g., "1430 15 Jan 2024")
    const match = dateString.match(/^\d{4}\s+(\d{1,2})\s+(\w{3})\s+(\d{4})$/);
    if (match) {
      const day = match[1].padStart(2, "0");
      const monthName = match[2];
      const year = match[3];

      // Convert month name to number
      const monthMap: { [key: string]: string } = {
        Jan: "01",
        Feb: "02",
        Mar: "03",
        Apr: "04",
        May: "05",
        Jun: "06",
        Jul: "07",
        Aug: "08",
        Sep: "09",
        Oct: "10",
        Nov: "11",
        Dec: "12",
      };

      const month = monthMap[monthName];
      if (month) {
        return `${month}/${day}/${year}`;
      }
    }

    // If it doesn't match the expected format, return original
    return dateString;
  } catch (error) {
    return dateString; // Return original if any error occurs
  }
};

// Helper function to format lab results data for report
const formatLabResultsForReport = (data: any[]): string => {
  if (!data || data.length === 0) {
    return '<div class="report-section"><strong>Lab Results</strong><br>No lab results available.</div>';
  }

  // Group lab results by panel
  const panels: { [panel: string]: any[] } = {};
  data.forEach((result) => {
    const panel = result.lab_panel_name || "Other";
    if (!panels[panel]) panels[panel] = [];
    panels[panel].push(result);
  });

  // For each panel, build a table
  const panelTables = Object.entries(panels).map(([panelName, results]) => {
    // Get all unique collection times (columns)
    const allTimesSet = new Set<string>();
    results.forEach((r) => {
      if (r.lab_collection_time) allTimesSet.add(r.lab_collection_time);
    });
    const allTimes = Array.from(allTimesSet).sort();

    // Get all unique lab result names (rows)
    const allNamesSet = new Set<string>();
    results.forEach((r) => {
      if (r.labresult_name) allNamesSet.add(r.labresult_name);
    });
    const allNames = Array.from(allNamesSet).sort();

    // Build a lookup: name -> time -> result
    const lookup: { [name: string]: { [time: string]: any } } = {};
    results.forEach((r) => {
      const name = r.labresult_name;
      const time = r.lab_collection_time;
      if (!lookup[name]) lookup[name] = {};
      lookup[name][time] = r;
    });

    // Table header
    const tableHeader = `<tr><td><strong>Lab Result</strong></td>${allTimes.map((time) => `<td><strong>${formatDate(time)}</strong></td>`).join("")}</tr>`;

    // Table rows
    const tableRows = allNames
      .map((name) => {
        const rowCells = allTimes
          .map((time) => {
            const r = lookup[name][time];
            if (!r) return "<td></td>";
            const value = r.labresult_value || "N/A";
            const unit = r.labresult_unit || "";
            const flag = r.labresult_flag || "";
            const isAbnormal = flag && flag.trim() !== "";
            if (isAbnormal) {
              return `<td><span style="color: #ff0000;">${value}</span> ${unit} <span style="color: #ff0000;">${flag}</span></td>`;
            } else {
              return `<td>${value} ${unit}</td>`;
            }
          })
          .join("");
        return `<tr><td><strong>${name}</strong></td>${rowCells}</tr>`;
      })
      .join("");

    return `<div style="margin-bottom: 1.5em;"><div style="font-weight: bold; margin-bottom: 0.5em;">${panelName}</div><table>${tableHeader}${tableRows}</table></div>`;
  });

  return `<div class="report-section"><strong>Lab Results</strong></div>${panelTables.join("")}`;
};

// Helper function to get tab display name
const getTabDisplayName = (tabName: string): string => {
  const tabNameMap: { [key: string]: string } = {
    HEENT: "HEENT",
    Neck: "Neck",
    Cardio: "Cardiovascular",
    PULM: "Pulmonary",
    Abd: "Abdominal",
    "GU/AR": "Genitourinary/Anorectal",
    Musc: "Musculoskeletal",
    UPPER: "Upper Extremities",
    LOWER: "Lower Extremities",
  };

  return tabNameMap[tabName] || tabName;
};

// Helper function to update Problems section in editor using TipTap
const updateProblemsSection = async (editor: any, newContent: string) => {
  try {
    // First, let's traverse the document to find the problems section
    let problemsStartPos = -1;
    let problemsEndPos = -1;
    let foundProblemsHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Problems" heading node - handle both plain text and HTML content
      if (node.isText && node.textContent) {
        const text = node.textContent.trim();
        // Check if this is the actual heading (not just part of other text)
        if (
          text === "Problems" ||
          text.includes("<strong>Problems</strong>") ||
          (text.includes("Problems") &&
            !text.includes(" ") &&
            text.length <= 20)
        ) {
          problemsStartPos = pos;
          foundProblemsHeading = true;
        }
      }

      // If we found the heading, look for the end of the problems section
      if (foundProblemsHeading && problemsEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Medications",
          "Lab Results",
          "Vital Signs",
          "Review of Systems",
          "Exam",
          "Assessment",
          "Home Medications",
          "Procedures",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            problemsEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (problemsStartPos !== -1 && problemsEndPos === -1) {
      problemsEndPos = editor.state.doc.content.size;
    }

    if (problemsStartPos !== -1 && problemsEndPos !== -1) {
      // Delete the problems section and insert new content
      editor.commands.deleteRange({
        from: problemsStartPos,
        to: problemsEndPos,
      });
      editor.commands.insertContentAt(problemsStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating problems section:", error);
  }
};

// Helper function to update Home Medications section in editor using TipTap
const updateHomeMedsSection = async (editor: any, newContent: string) => {
  try {
    // First, let's traverse the document to find the home medications section
    let homeMedsStartPos = -1;
    let homeMedsEndPos = -1;
    let foundHomeMedsHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Home Medications" heading node - handle both plain text and HTML content
      if (node.isText && node.textContent) {
        const text = node.textContent.trim();
        // Check if this is the actual heading (not just part of other text)
        if (
          text === "Home Medications" ||
          text.includes("<strong>Home Medications</strong>") ||
          (text.includes("Home Medications") && text.length <= 30)
        ) {
          homeMedsStartPos = pos;
          foundHomeMedsHeading = true;
        }
      }

      // If we found the heading, look for the end of the home medications section
      if (foundHomeMedsHeading && homeMedsEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Lab Results",
          "Vital Signs",
          "Review of Systems",
          "Exam",
          "Assessment",
          "Problems",
          "Procedures",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            homeMedsEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (homeMedsStartPos !== -1 && homeMedsEndPos === -1) {
      homeMedsEndPos = editor.state.doc.content.size;
    }

    if (homeMedsStartPos !== -1 && homeMedsEndPos !== -1) {
      // Delete the home medications section and insert new content
      editor.commands.deleteRange({
        from: homeMedsStartPos,
        to: homeMedsEndPos,
      });
      editor.commands.insertContentAt(homeMedsStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating home medications section:", error);
  }
};

// Helper function to update Review of Systems section in editor using TipTap
const updateReviewOfSystemsSection = async (
  editor: any,
  newContent: string
) => {
  try {
    // First, let's traverse the document to find the review of systems section
    let rosStartPos = -1;
    let rosEndPos = -1;
    let foundROSHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Review of Systems" heading node - handle both plain text and HTML content
      if (node.isText && node.textContent) {
        const text = node.textContent.trim();
        // Check if this is the actual heading (not just part of other text)
        if (
          text === "Review of Systems" ||
          text.includes("<strong>Review of Systems</strong>") ||
          (text.includes("Review of Systems") && text.length <= 40)
        ) {
          rosStartPos = pos;
          foundROSHeading = true;
        }
      }

      // If we found the heading, look for the end of the review of systems section
      if (foundROSHeading && rosEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Exam",
          "Assessment",
          "Problems",
          "Home Medications",
          "Lab Results",
          "Vital Signs",
          "Procedures",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            rosEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (rosStartPos !== -1 && rosEndPos === -1) {
      rosEndPos = editor.state.doc.content.size;
    }

    if (rosStartPos !== -1 && rosEndPos !== -1) {
      // Delete the review of systems section and insert new content
      editor.commands.deleteRange({ from: rosStartPos, to: rosEndPos });
      editor.commands.insertContentAt(rosStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating review of systems section:", error);
  }
};

// Helper function to update Exam section in editor using TipTap
const updateExamSection = async (editor: any, newContent: string) => {
  try {
    // First, let's traverse the document to find the exam section
    let examStartPos = -1;
    let examEndPos = -1;
    let foundExamHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Physical Exam" or "Exam" heading node - handle both plain text and HTML content
      if (!foundExamHeading && node.isText && node.textContent) {
        const text = node.textContent.trim();
        if (
          text === "Physical Exam" ||
          text === "Exam" ||
          text.includes("<strong>Physical Exam</strong>") ||
          text.includes("<strong>Exam</strong>") ||
          (text.includes("Physical Exam") && text.length <= 30) ||
          (text.includes("Exam") && !text.includes(" ") && text.length <= 10)
        ) {
          examStartPos = pos;
          foundExamHeading = true;
        }
      }

      // If we found the heading, look for the end of the exam section
      if (foundExamHeading && examEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Assessment",
          "Problems",
          "Home Medications",
          "Lab Results",
          "Vital Signs",
          "Review of Systems",
          "Procedures",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            examEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (examStartPos !== -1 && examEndPos === -1) {
      examEndPos = editor.state.doc.content.size;
    }

    // Make sure we have valid positions (start should be before end)
    if (examStartPos !== -1 && examEndPos !== -1 && examStartPos < examEndPos) {
      // Delete the exam section and insert new content
      editor.commands.deleteRange({ from: examStartPos, to: examEndPos });
      editor.commands.insertContentAt(examStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating exam section:", error);
  }
};

// Helper function to update Procedures section in editor using TipTap
const updateProceduresSection = async (editor: any, newContent: string) => {
  try {
    // First, let's traverse the document to find the procedures section
    let proceduresStartPos = -1;
    let proceduresEndPos = -1;
    let foundProceduresHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Procedures" heading node - handle both plain text and HTML content
      if (node.isText && node.textContent) {
        const text = node.textContent.trim();
        // Check if this is the actual heading (not just part of other text)
        if (
          text === "Procedures" ||
          text.includes("<strong>Procedures</strong>") ||
          (text.includes("Procedures") &&
            !text.includes(" ") &&
            text.length <= 20)
        ) {
          proceduresStartPos = pos;
          foundProceduresHeading = true;
        }
      }

      // If we found the heading, look for the end of the procedures section
      if (foundProceduresHeading && proceduresEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Medications",
          "Lab Results",
          "Vital Signs",
          "Review of Systems",
          "Exam",
          "Assessment",
          "Problems",
          "Home Medications",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            proceduresEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (proceduresStartPos !== -1 && proceduresEndPos === -1) {
      proceduresEndPos = editor.state.doc.content.size;
    }

    if (proceduresStartPos !== -1 && proceduresEndPos !== -1) {
      // Delete the procedures section and insert new content
      editor.commands.deleteRange({
        from: proceduresStartPos,
        to: proceduresEndPos,
      });
      editor.commands.insertContentAt(proceduresStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating procedures section:", error);
  }
};

// Helper function to update Vitals section in editor using TipTap
const updateVitalsSection = async (editor: any, newContent: string) => {
  try {
    // First, let's traverse the document to find the vitals section
    let vitalsStartPos = -1;
    let vitalsEndPos = -1;
    let foundVitalsHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Vital Signs" heading node - handle both plain text and HTML content
      if (node.isText && node.textContent) {
        const text = node.textContent.trim();
        // Check if this is the actual heading (not just part of other text)
        if (
          text === "Vital Signs" ||
          text.includes("<strong>Vital Signs</strong>") ||
          (text.includes("Vital Signs") && text.length <= 30)
        ) {
          vitalsStartPos = pos;
          foundVitalsHeading = true;
        }
      }

      // If we found the heading, look for the end of the vitals section
      if (foundVitalsHeading && vitalsEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Review of Systems",
          "Exam",
          "Assessment",
          "Problems",
          "Home Medications",
          "Lab Results",
          "Procedures",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            vitalsEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (vitalsStartPos !== -1 && vitalsEndPos === -1) {
      vitalsEndPos = editor.state.doc.content.size;
    }

    if (vitalsStartPos !== -1 && vitalsEndPos !== -1) {
      // Delete the vitals section and insert new content
      editor.commands.deleteRange({ from: vitalsStartPos, to: vitalsEndPos });
      editor.commands.insertContentAt(vitalsStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating vitals section:", error);
  }
};

// Helper function to update Lab Results section in editor using TipTap
const updateLabResultsSection = async (editor: any, newContent: string) => {
  try {
    // First, let's traverse the document to find the lab results section
    let labResultsStartPos = -1;
    let labResultsEndPos = -1;
    let foundLabResultsHeading = false;

    editor.state.doc.descendants((node: any, pos: number) => {
      // Look for the "Lab Results" heading node - handle both plain text and HTML content
      if (node.isText && node.textContent) {
        const text = node.textContent.trim();
        // Check if this is the actual heading (not just part of other text)
        if (
          text === "Lab Results" ||
          text.includes("<strong>Lab Results</strong>") ||
          (text.includes("Lab Results") && text.length <= 30)
        ) {
          labResultsStartPos = pos;
          foundLabResultsHeading = true;
        }
      }

      // If we found the heading, look for the end of the lab results section
      if (foundLabResultsHeading && labResultsEndPos === -1) {
        // Look for the next major section or end of document
        const nextSections = [
          "Vital Signs",
          "Review of Systems",
          "Exam",
          "Assessment",
          "Problems",
          "Home Medications",
          "Procedures",
        ];

        for (const section of nextSections) {
          if (node.textContent && node.textContent.includes(section)) {
            labResultsEndPos = pos;
            return false; // Stop traversal
          }
        }
      }
    });

    // If we didn't find an end, use the end of document
    if (labResultsStartPos !== -1 && labResultsEndPos === -1) {
      labResultsEndPos = editor.state.doc.content.size;
    }

    if (labResultsStartPos !== -1 && labResultsEndPos !== -1) {
      // Delete the lab results section and insert new content
      editor.commands.deleteRange({
        from: labResultsStartPos,
        to: labResultsEndPos,
      });
      editor.commands.insertContentAt(labResultsStartPos, newContent);
    } else {
      // Insert at the end of the document
      const endPos = editor.state.doc.content.size;
      editor.commands.insertContentAt(endPos, newContent);
    }
  } catch (error) {
    console.error("Error updating lab results section:", error);
  }
};
