import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Alert,
  CircularProgress,
} from '@mui/material';

interface SignatureDialogProps {
  open: boolean;
  onClose: () => void;
  onSign: (comment: string) => Promise<void>;
  reportTitle: string;
  reportType: string;
}

export const SignatureDialog: React.FC<SignatureDialogProps> = ({
  open,
  onClose,
  onSign,
  reportTitle,
  reportType,
}) => {
  const [comment, setComment] = useState('');
  const [isSigning, setIsSigning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSign = async () => {
    if (!comment.trim()) {
      setError('Please enter a signature comment');
      return;
    }

    try {
      setIsSigning(true);
      setError(null);
      await onSign(comment);
      setComment('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign report');
    } finally {
      setIsSigning(false);
    }
  };

  const handleClose = () => {
    setComment('');
    setError(null);
    setIsSigning(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Sign Report</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" gutterBottom>
            <strong>Report:</strong> {reportTitle}
          </Typography>
          <Typography variant="body1" gutterBottom>
            <strong>Type:</strong> {reportType}
          </Typography>
        </Box>
        
        <TextField
          fullWidth
          multiline
          rows={3}
          label="Signature Comment"
          placeholder="Enter your signature comment (e.g., 'Reviewed and approved by Dr. Smith')"
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          disabled={isSigning}
          sx={{ mb: 2 }}
        />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Typography variant="body2" color="text.secondary">
          By signing this report, you confirm that you have reviewed and approved its contents.
          This action cannot be undone.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={isSigning}>
          Cancel
        </Button>
        <Button 
          onClick={handleSign} 
          color="primary" 
          variant="contained"
          disabled={isSigning || !comment.trim()}
          startIcon={isSigning ? <CircularProgress size={16} /> : null}
        >
          {isSigning ? 'Signing...' : 'Sign Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 