import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, DialogTitle, DialogContent, DialogActions, TextField, Alert } from '@mui/material';
import { ReportAPI } from '../services/ReportAPI';
import { NoteReport } from '../types/NoteVersion';

interface SignButtonProps {
  report: NoteReport;
  campus: string;
  patID: string;
  onSignSuccess?: () => void;
  onSignError?: (error: string) => void;
}

export const SignButton: React.FC<SignButtonProps> = ({
  report,
  campus,
  patID,
  onSignSuccess,
  onSignError
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [comment, setComment] = useState('');
  const [isSigning, setIsSigning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSignClick = () => {
    setIsDialogOpen(true);
    setError(null);
  };

  const handleSign = async () => {
    if (!comment.trim()) {
      setError('Please enter a comment for the signature');
      return;
    }

    setIsSigning(true);
    setError(null);

    try {
      // Retrieve staffid and ccitoken the same way as other write endpoints
      const staffid = typeof Cci !== 'undefined' && Cci ? Cci.util.Staff.getSid() : undefined;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";

      const result = await ReportAPI.signReport(
        campus,
        patID,
        report.noteID,
        report.type,
        comment.trim(),
        staffid,
        ccitoken
      );

      console.log('Note signed successfully:', result.message);
      setIsDialogOpen(false);
      setComment('');
      onSignSuccess?.();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign note';
      console.error('Failed to sign note:', error);
      setError(errorMessage);
      onSignError?.(errorMessage);
    } finally {
      setIsSigning(false);
    }
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
    setComment('');
    setError(null);
  };

  // Don't show sign button if note doesn't require signature or is already signed
  if (!report.required_signature || report.signed) {
    return null;
  }

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={handleSignClick}
        disabled={isSigning}
        sx={{ ml: 1 }}
      >
        Sign Note
      </Button>

      <Dialog open={isDialogOpen} onClose={handleCancel} maxWidth="sm" fullWidth>
        <DialogTitle>Sign Note</DialogTitle>
        <DialogContent>
          <p>
            You are about to sign the note: <strong>{report.title}</strong>
          </p>
          <p>
            <strong>Note Type:</strong> {report.type}
          </p>
          <p>
            <strong>Note ID:</strong> {report.noteID}
          </p>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            autoFocus
            margin="dense"
            label="Signature Comment"
            type="text"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Enter your signature comment (e.g., 'Reviewed and approved by Dr. Smith')"
            disabled={isSigning}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancel} disabled={isSigning}>
            Cancel
          </Button>
          <Button 
            onClick={handleSign} 
            variant="contained" 
            disabled={isSigning || !comment.trim()}
          >
            {isSigning ? 'Signing...' : 'Sign Note'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}; 