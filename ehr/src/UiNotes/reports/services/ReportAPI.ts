import { NoteReport, Note<PERSON><PERSON><PERSON>, SignNoteRequest, SignNoteResponse } from "../types/NoteVersion";

export class ReportAPI {
  private static baseUrl = (typeof process !== 'undefined' && process.env && process.env.REACT_APP_PROXY)
    ? `${process.env.REACT_APP_PROXY}/api/patients`
    : "/api/patients";
  private staffName = typeof Cci !== 'undefined' && Cci ? Cci.RunTime.getStaffname() : "DEMO";

  static async getReports(
    campus: string,
    patID: string
  ): Promise<NoteReport[]> {
    const response = await fetch(`${this.baseUrl}/${campus}/${patID}/notes`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + localStorage.getItem("webtoken"),
        "X-Forwarded-For": "127.0.0.1",
        "X-Real-IP": "127.0.0.1",
        "X-Requested-With": "XMLHttpRequest",
      },
      mode: "cors",
      credentials: "omit",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reports = await response.json();
    return reports;
  }

  static async createReport(
    campus: string,
    patID: string,
    report: Omit<NoteReport, "noteID" | "creationTime" | "modifiedTime">,
    staffid: number,
    ccitoken: string
  ): Promise<NoteReport> {
    const response = await fetch(`${this.baseUrl}/${campus}/${patID}/notes`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + localStorage.getItem("webtoken"),
        "X-Forwarded-For": "127.0.0.1",
        "X-Real-IP": "127.0.0.1",
        "X-Requested-With": "XMLHttpRequest",
      },
      mode: "cors",
      credentials: "omit",
      body: JSON.stringify({
        ...report,
        staffid,
        ccitoken,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Server response:", response.status, errorText);
      throw new Error(`Failed to create report: ${errorText}`);
    }

    return response.json();
  }

  static async updateReport(
    campus: string,
    patID: string,
    report: Partial<NoteReport> & { noteID: string },
    staffid: number,
    ccitoken: string
  ): Promise<NoteReport> {
    // Build URL with note_type as query parameter
    const url = new URL(`${this.baseUrl}/${campus}/${patID}/notes/${report.noteID}`);
    url.searchParams.append('note_type', report.type || 'Note');
    url.searchParams.append('staffid', staffid.toString());
    url.searchParams.append('ccitoken', ccitoken);
    
    const response = await fetch(url.toString(), {
      method: "PUT",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + localStorage.getItem("webtoken"),
        "X-Forwarded-For": "127.0.0.1",
        "X-Real-IP": "127.0.0.1",
        "X-Requested-With": "XMLHttpRequest",
      },
      mode: "cors",
      credentials: "omit",
      body: JSON.stringify({
        ...report,
        // Remove staffid and ccitoken from body since they're now in URL
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to update report");
    }

    return response.json();
  }

  /**
   * Delete a specific note by noteID and noteType.
   * Both noteID (path) and noteType (query) are required.
   * Throws an error if noteType is missing.
   *
   * @param campus - The campus identifier
   * @param patID - The patient ID
   * @param noteID - The note ID (required)
   * @param noteType - The note type (required, used to look up NIT)
   * @param staffid - Staff ID (required for CCIDB)
   * @param ccitoken - CCIDB token (required for CCIDB)
   * @returns Promise<void>
   *
   * Example:
   *   await ReportAPI.deleteReport("campus1", "12345", "**********", "Progress Note", staffid, ccitoken);
   */
  static async deleteReport(
    campus: string,
    patID: string,
    noteID: string,
    noteType: string,
    staffid: number,
    ccitoken: string
  ): Promise<void> {
    if (!noteID || !noteType) {
      throw new Error("Both noteID and noteType are required to delete a specific note.");
    }
    const url = `${this.baseUrl}/${campus}/${patID}/notes/${noteID}?note_type=${encodeURIComponent(noteType)}&staffid=${staffid}&ccitoken=${ccitoken}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + localStorage.getItem("webtoken"),
        "X-Forwarded-For": "127.0.0.1",
        "X-Real-IP": "127.0.0.1",
        "X-Requested-With": "XMLHttpRequest",
      },
      mode: "cors",
      credentials: "omit",
    });

    if (!response.ok) {
      throw new Error("Failed to delete report");
    }
  }

  static async getHistory(
    campus: string,
    patID: string,
    noteID: string
  ): Promise<NoteVersion[]> {
    const response = await fetch(
      `${this.baseUrl}/${campus}/${patID}/notes/${noteID}/versions`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        mode: "cors",
        credentials: "omit",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  static async getVersion(
    campus: string,
    patID: string,
    noteID: string,
    commitHash: string
  ): Promise<NoteReport> {
    const response = await fetch(
      `${this.baseUrl}/${campus}/${patID}/notes/${noteID}/versions/${commitHash}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        mode: "cors",
        credentials: "omit",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  static async compareVersions(
    campus: string,
    patID: string,
    noteID: string,
    commit1: string,
    commit2: string
  ): Promise<{ diff: string }> {
    const response = await fetch(
      `${this.baseUrl}/${campus}/${patID}/notes/${noteID}/compare?commit1=${commit1}&commit2=${commit2}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        mode: "cors",
        credentials: "omit",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /**
   * Sign a note/report using the sign_note endpoint
   * 
   * @param campus - The campus identifier
   * @param patID - The patient ID
   * @param noteID - The note ID to sign
   * @param noteType - The type of note (e.g., "Progress Note", "ED Progress Note")
   * @param comment - The signature comment
   * @param staffid - Optional staff ID (will use Cci.util.Staff.getSid() if not provided)
   * @param ccitoken - Optional CCIToken (will use localStorage/sessionStorage if not provided)
   * @returns Promise<SignNoteResponse> - Response with success message
   * 
   * @example
   * ```typescript
   * try {
   *   const result = await ReportAPI.signReport(
   *     "campus1",
   *     "12345",
   *     "**********",
   *     "Progress Note",
   *     "Reviewed and approved by Dr. Smith"
   *   );
   *   console.log(result.message); // "Note signed successfully"
   * } catch (error) {
   *   console.error("Failed to sign note:", error);
   * }
   * ```
   */
  static async signReport(
    campus: string,
    patID: string,
    noteID: string,
    noteType: string,
    comment: string,
    staffid?: number,
    ccitoken?: string
  ): Promise<SignNoteResponse> {
    // Get staffid and ccitoken if not provided
    const finalStaffid = staffid || (typeof Cci !== 'undefined' && Cci ? Cci.util.Staff.getSid() : undefined);
    const finalCcitoken = ccitoken || localStorage.getItem("webtoken") || window.sessionStorage.getItem("webtoken") || "";

    const response = await fetch(
      `${this.baseUrl}/${campus}/${patID}/notes/${noteID}/sign`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        mode: "cors",
        credentials: "omit",
        body: JSON.stringify({
          note_type: noteType,
          comment: comment,
          staffid: finalStaffid,
          ccitoken: finalCcitoken,
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Server response:", response.status, errorText);
      throw new Error(`Failed to sign report: ${errorText}`);
    }

    return response.json();
  }

  /**
   * Fetch a specific note by noteID and noteType.
   * Both noteID (path) and noteType (query) are required.
   * Throws an error if noteType is missing.
   *
   * @param campus - The campus identifier
   * @param patID - The patient ID
   * @param noteID - The note ID (required)
   * @param noteType - The note type (required, used to look up NIT)
   * @returns Promise<NoteReport>
   *
   * Example:
   *   await ReportAPI.getNote("campus1", "12345", "**********", "Progress Note");
   */
  static async getNote(
    campus: string,
    patID: string,
    noteID: string,
    noteType: string
  ): Promise<NoteReport> {
    if (!noteID || !noteType) {
      throw new Error("Both noteID and noteType are required to fetch a specific note.");
    }
    const url = `${this.baseUrl}/${campus}/${patID}/notes/${noteID}?note_type=${encodeURIComponent(noteType)}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: "Bearer " + localStorage.getItem("webtoken"),
        "X-Forwarded-For": "127.0.0.1",
        "X-Real-IP": "127.0.0.1",
        "X-Requested-With": "XMLHttpRequest",
      },
      mode: "cors",
      credentials: "omit",
    });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch note: ${errorText}`);
    }
    return response.json();
  }
}
