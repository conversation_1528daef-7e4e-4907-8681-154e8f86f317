export interface Exam {
  [key: string]: any;
}

export interface ExamUpdateRequest {
  data: Exam;
  staffid: number;
  ccitoken: string;
}

export class ExamAPI {
  private static baseUrl = (typeof process !== 'undefined' && process.env && process.env.REACT_APP_PROXY)
    ? `${process.env.REACT_APP_PROXY}/api/patients`
    : "/api/patients";

  static async getExam(campus: string, patID: string, visitkey: number): Promise<Exam> {
    try {
      const response = await fetch(`${this.baseUrl}/${campus}/${patID}/exam/${visitkey}`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        mode: "cors",
        credentials: "omit",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const examResponse = await response.json();

      // Handle both correct and legacy double-nested data structures
      let examData = examResponse.data;
      if (examData && typeof examData === 'object' && 'data' in examData) {
        // This is legacy double-nested data, extract the inner data
        console.log("Found legacy double-nested data structure, extracting inner data");
        examData = examData.data;
      }

      return examData; // Return the exam data
    } catch (error) {
      console.error("Error getting exam:", error);
      throw error;
    }
  }

  static async updateExam(
    campus: string,
    patID: string,
    visitkey: number,
    exam: Exam,
    staffid: number,
    ccitoken: string
  ): Promise<void> {
    try {
      const requestBody: ExamUpdateRequest = {
        data: exam,
        staffid: staffid,
        ccitoken: ccitoken
      };

      const response = await fetch(`${this.baseUrl}/${campus}/${patID}/exam/${visitkey}`, {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
          "X-Forwarded-For": "127.0.0.1",
          "X-Real-IP": "127.0.0.1",
          "X-Requested-With": "XMLHttpRequest",
        },
        mode: "cors",
        credentials: "omit",
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Server response:", response.status, errorText);
        throw new Error(`Failed to update exam: ${errorText}`);
      }

      const result = await response.json();
      console.log("Exam updated successfully:", result);
    } catch (error) {
      console.error("Error updating exam:", error);
      throw error;
    }
  }
} 