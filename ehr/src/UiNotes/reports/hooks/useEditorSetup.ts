import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Table as TiptapTable } from "@tiptap/extension-table";
import { TableRow as TiptapTableRow } from "@tiptap/extension-table-row";
import { TableCell as TiptapTableCell } from "@tiptap/extension-table-cell";
import { TableHeader as TiptapTableHeader } from "@tiptap/extension-table-header";
import { TextStyle } from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';

import { SUBJECTIVE } from '../Constants';

export const useEditorSetup = (
  editorRef: React.MutableRefObject<any>,
  typingTimeoutRef: React.MutableRefObject<NodeJS.Timeout | null>,
  isReportEditor: boolean = false
) => {
  return useEditor({
    extensions: [
      // Minimal configuration - just the basics
      StarterKit.configure({
        // Disable potentially problematic extensions
        heading: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
      }),
      TextStyle,
      Color,
      // Add Table extensions for report editor
      ...(isReportEditor ? [
        TiptapTable,
        TiptapTableRow,
        TiptapTableCell,
        TiptapTableHeader,
      ] : []),
    ] as any,
    content: isReportEditor ? "" : `<p>${SUBJECTIVE}</p>`,
    autofocus: !isReportEditor,
    editable: true,
    editorProps: {
      attributes: {
        class: "editor-content",
        style: "background: white; min-height: 200px;",
      },
    },
    onBeforeCreate: ({ editor }) => {
      editorRef.current = editor;
    },
    onCreate: ({ editor }) => {
      // Let the editor handle its own focus behavior naturally
      // Don't force any cursor positioning
    },
    onUpdate: ({ editor }) => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    },
  });
}; 