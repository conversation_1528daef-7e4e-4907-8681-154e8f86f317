import { useState, useEffect } from 'react';
import noteTypesConfig from '../config/noteTypes.json';

interface SignatureConfig {
  requireDigitalSignature: boolean;
  allowElectronicSignature: boolean;
  requireWitness: boolean;
}

interface NoteTypesConfig {
  signableTypes: string[];
  signatureConfig: SignatureConfig;
}

export const useSignatureConfig = () => {
  const [config, setConfig] = useState<NoteTypesConfig>(noteTypesConfig);

  const isSignableType = (noteType: string): boolean => {
    return config.signableTypes.includes(noteType);
  };

  const getSignatureConfig = (): SignatureConfig => {
    return config.signatureConfig;
  };

  return {
    isSignableType,
    getSignatureConfig,
    signableTypes: config.signableTypes,
  };
}; 