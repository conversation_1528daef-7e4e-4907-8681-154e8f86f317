import { useCallback, useState, useEffect } from "react";
import { useAtom } from "jotai";
import {
  reportList<PERSON>tom,
  isLoadingReports<PERSON>tom,
  selectedReportAtom,
  isSaveAsDialogOpenAtom,
  isVersionHistoryOpenAtom,
  reportTypeAtom,
} from "../context/EditorContext";
import { ReportAPI } from "../services/ReportAPI";
import { Editor } from "@tiptap/core";
import { REPORT_TYPES } from "../Constants";

// Fallback for cci, consistent with Editor component
const cci = window.Cci || window.cci;

interface ReportParams {
  campus: string;
  patID: string;
}

export const useReportManagement = (
  reportParams: ReportParams,
  editor: Editor | null
) => {
  const [reportList, setReportList] = useAtom(reportListAtom);
  const [isLoadingReports, setIsLoadingReports] = useAtom(isLoadingReportsAtom);
  const [selectedReport, setSelectedReport] = useAtom(selectedReportAtom);
  const [isSaveAsDialogOpen, setIsSaveAsDialogOpen] = useAtom(
    isSaveAsDialogOpenAtom
  );
  const [isVersionHistoryOpen, setIsVersionHistoryOpen] = useAtom(
    isVersionHistoryOpenAtom
  );
  const [reportType] = useAtom(reportTypeAtom);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchReportList = useCallback(async () => {
    setIsLoadingReports(true);
    try {
      const data = await ReportAPI.getReports(
        reportParams.campus,
        reportParams.patID
      );
      setReportList(data || []);
    } catch (error) {
      console.error("Error fetching report list:", error);
    } finally {
      setIsLoadingReports(false);
    }
  }, [
    reportParams.campus,
    reportParams.patID,
    setIsLoadingReports,
    setReportList,
  ]);

  // Fetch report list when component mounts
  useEffect(() => {
    if (reportParams.campus && reportParams.patID) {
      fetchReportList();
    }
  }, [reportParams.campus, reportParams.patID, fetchReportList]);

  const handleSaveReport = async () => {
    if (!selectedReport || !editor) return;

    try {
      const staffid = cci ? cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";

      await ReportAPI.updateReport(
        reportParams.campus,
        reportParams.patID,
        {
          ...selectedReport,
          content: editor.getHTML(),
          lastModifiedTime: new Date().toISOString(),
          updatedBy: cci.RunTime.getStaffname() || "DEMO",
        },
        staffid,
        ccitoken
      );
      await fetchReportList();
    } catch (error) {
      console.error("Error saving report:", error);
    }
  };

  const handleDeleteReport = async () => {
    if (!selectedReport) return;

    try {
      const staffid = cci ? cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";
      await ReportAPI.deleteReport(
        reportParams.campus,
        reportParams.patID,
        selectedReport.noteID,
        selectedReport.type, // Pass note type for NIT
        staffid,
        ccitoken
      );
      if (editor) {
        editor.commands.setContent("");
      }
      setSelectedReport(null);
      await fetchReportList();
    } catch (error) {
      console.error("Error deleting report:", error);
    }
  };

  const handleSaveReportAs = async (title: string) => {
    if (!editor) return;

    try {
      // Get current timestamp in MM/DD/YYYY HH:MM format
      const now = new Date();
      const timestamp = now.toLocaleString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      // Get current user
      const currentUser = cci ? cci.RunTime.getStaffname() : "DEMO";

      // Get note type from report type
      const noteType = REPORT_TYPES[reportType] || "Note";

      // Create header table
      const headerContent = `
<table class="report-header-table">
  <tbody>
    <tr>
      <td style="background-color: #f5f5f5;" colspan="2"><strong>${noteType}</strong></td>
    </tr>
    <tr>
      <td><strong>Attending:</strong></td>
      <td><strong>Last Stored At:</strong> ${timestamp}</td>
    </tr>
    <tr>
      <td><strong>Specialty:</strong></td>
      <td><strong>Last Stored By:</strong> ${currentUser}</td>
    </tr>
  </tbody>
</table>
`;

      // Insert header at the beginning of the editor content
      editor.commands.setContent(headerContent + editor.getHTML());

      const staffid = cci ? cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";
      const newReport = {
        title,
        content: editor.getHTML(),
        type: REPORT_TYPES[reportType],
        updatedBy: cci.RunTime.getStaffname() || "DEMO",
      };

      await ReportAPI.createReport(
        reportParams.campus,
        reportParams.patID,
        newReport,
        staffid,
        ccitoken
      );
      await fetchReportList();

      setIsSaveAsDialogOpen(false);
    } catch (error) {
      console.error("Error saving report as:", error);
    }
  };

  const handleVersionHistory = () => {
    if (selectedReport) {
      setIsVersionHistoryOpen(true);
    }
  };

  const handleVersionSelect = (version: any) => {
    if (editor) {
      editor.commands.setContent(version.content);
      setIsVersionHistoryOpen(false);
    }
  };

  return {
    reportList,
    isLoadingReports,
    isRefreshing,
    selectedReport,
    isSaveAsDialogOpen,
    isVersionHistoryOpen,
    setSelectedReport,
    setIsSaveAsDialogOpen,
    setIsVersionHistoryOpen,
    fetchReportList,
    handleSaveReport,
    handleDeleteReport,
    handleSaveReportAs,
    handleVersionHistory,
    handleVersionSelect,
  };
};
