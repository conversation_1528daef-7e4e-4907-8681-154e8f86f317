import { useCallback, useRef } from 'react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Table as TiptapTable } from '@tiptap/extension-table';
import { TableRow as TiptapTableRow } from '@tiptap/extension-table-row';
import { TableCell as TiptapTableCell } from '@tiptap/extension-table-cell';
import { TableHeader as TiptapTableHeader } from '@tiptap/extension-table-header';
import { TextStyle } from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import {
  VitalsExtension,
  PatientDemographicsExtension,
  DiagnosisExtension,
  MedicationsExtension,
} from '../extensions';
import { useAtom } from 'jotai';
import {
  isAutoFixLoadingAtom,
  inFetchDataAtom,
  lastCorrectedTextAtom,
  tooltipOpenAtom,
  tooltipPositionAtom,
} from '../context/EditorContext';
import { normalQuery } from '../../util/AIPilot';
import { ChainedCommands } from '@tiptap/core';

interface EditorInstance {
  getHTML: () => string;
  state: {
    doc: {
      descendants: (callback: (node: any) => void) => void;
    };
  };
  chain: () => ChainedCommands;
  commands: {
    setContent: (content: any) => void;
    focus: () => void;
  };
  view: {
    coordsAtPos: (pos: number) => { left: number; top: number } | null;
  };
}

export const useEditorSetup = () => {
  const editorRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isApplyingCorrectionRef = useRef(false);
  const editorContentRef = useRef<HTMLDivElement>(null);

  const [isAutoFixLoading, setIsAutoFixLoading] = useAtom(isAutoFixLoadingAtom);
  const [inFetchData, setInFetchData] = useAtom(inFetchDataAtom);
  const [lastCorrectedText, setLastCorrectedText] = useAtom(lastCorrectedTextAtom);
  const [tooltipOpen, setTooltipOpen] = useAtom(tooltipOpenAtom);
  const [tooltipPosition, setTooltipPosition] = useAtom(tooltipPositionAtom);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TiptapTable.configure({
        resizable: true,
      }),
      TiptapTableRow,
      TiptapTableCell,
      TiptapTableHeader,
      VitalsExtension,
      PatientDemographicsExtension,
      DiagnosisExtension,
      MedicationsExtension,
      TextStyle,
      Color,
    ],
    content: `<div class="editor-content">
      <p></p>
    </div>`,
    autofocus: 'end',
    editorProps: {
      attributes: {
        class: 'editor-content',
      },
    },
    onBeforeCreate: ({ editor }) => {
      editorRef.current = editor;
    },
    onCreate: ({ editor }) => {
      editor.commands.focus('end');
      const { view } = editor;
      if (view) {
        view.dom.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    },
    onUpdate: ({ editor }) => {
      if (isApplyingCorrectionRef.current) return;

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    },
  });

  const showTooltip = useCallback((pos: { left: number; top: number }) => {
    setTooltipPosition(pos);
    setTooltipOpen(true);
    setTimeout(() => setTooltipOpen(false), 2000);
  }, [setTooltipPosition, setTooltipOpen]);

  const correctText = useCallback(async (text: string, from: number, to: number) => {
    if (!editor || isApplyingCorrectionRef.current) return;

    setIsAutoFixLoading(true);
    let isMounted = true;

    try {
      const finalResponse = await new Promise<string>((resolve) => {
        normalQuery({
          prompt: `Please correct any grammar, spelling, or punctuation errors in the following text. Return ONLY the corrected text, nothing else:

"${text}"`,
          content: '',
          setResult: (response: string | ((prev: string) => string)) => {
            const result = typeof response === 'function' ? response('') : response;
            if (result === 'thinking...') return;
            resolve(result);
          },
          inFetchData,
          setInFetchData,
        });
      });

      if (!isMounted) return;

      // Clean up the response
      const cleanedContent = finalResponse.replace(/^["']|["']$/g, '').trim();

      try {
        const currentDocSize = editor.state.doc.content.size;
        if (from < 0 || to > currentDocSize || from > to) return;

        isApplyingCorrectionRef.current = true;
        editor
          .chain()
          .focus()
          .deleteRange({ from, to })
          .insertContent(cleanedContent)
          .run();
        setLastCorrectedText(cleanedContent);

        const selection = editor.state.selection;
        const { from: selFrom } = selection;
        const pos = editor.view.coordsAtPos(selFrom);

        if (pos) {
          showTooltip(pos);
        }
      } catch (error) {
        console.error('Error applying text correction:', error);
      } finally {
        isApplyingCorrectionRef.current = false;
      }
    } catch (error) {
      console.error('Error during text correction:', error);
    } finally {
      isMounted = false;
      setIsAutoFixLoading(false);
    }
  }, [editor, inFetchData, setInFetchData, setIsAutoFixLoading, setLastCorrectedText, showTooltip]);

  return {
    editor,
    editorRef,
    typingTimeoutRef,
    isApplyingCorrectionRef,
    editorContentRef,
    isAutoFixLoading,
    inFetchData,
    lastCorrectedText,
    tooltipOpen,
    tooltipPosition,
    showTooltip,
    correctText,
  };
};

export const useEditorState = (editor: EditorInstance | null) => {
  const [isAutoFixLoading, setIsAutoFixLoading] = useAtom(isAutoFixLoadingAtom);
  const [inFetchData, setInFetchData] = useAtom(inFetchDataAtom);
  const [lastCorrectedText, setLastCorrectedText] = useAtom(lastCorrectedTextAtom);
  const [tooltipOpen, setTooltipOpen] = useAtom(tooltipOpenAtom);
  const [tooltipPosition, setTooltipPosition] = useAtom(tooltipPositionAtom);

  const handleAutoFix = useCallback(async () => {
    if (!editor || isAutoFixLoading) return;

    setIsAutoFixLoading(true);
    try {
      const content = editor.getHTML();
      // ... rest of auto-fix logic ...
    } catch (error) {
      console.error('Error in auto-fix:', error);
    } finally {
      setIsAutoFixLoading(false);
    }
  }, [editor, isAutoFixLoading, setIsAutoFixLoading]);

  const handleTooltipOpen = useCallback((position: { left: number; top: number }) => {
    setTooltipPosition(position);
    setTooltipOpen(true);
  }, [setTooltipPosition, setTooltipOpen]);

  const handleTooltipClose = useCallback(() => {
    setTooltipOpen(false);
  }, [setTooltipOpen]);

  return {
    isAutoFixLoading,
    inFetchData,
    lastCorrectedText,
    tooltipOpen,
    tooltipPosition,
    handleAutoFix,
    handleTooltipOpen,
    handleTooltipClose,
  };
}; 