import { useEffect, useCallback, useRef, useState } from 'react';
import { Editor } from '@tiptap/core';
import { translatePunctuation } from '../../editorchat/Dictation/Helper';

interface UseSpeechRecognitionProps {
  editor: Editor | null;
  macroData: any[];
  onProcessTranscript: (text: string) => void;
  editorId?: string;
}

// ============================================================================
// TEXT PROCESSING UTILITIES
// ============================================================================

/**
 * Adds automatic punctuation to text
 */
const addAutoPunctuation = (text: string): string => {
  if (!text.trim()) return text;

  let processedText = text.trim();

  // Add period at the end if no punctuation exists
  if (!/[.!?]$/.test(processedText)) {
    processedText += '.';
  }

  // Add space after periods and question marks (but not at the very end)
  processedText = processedText.replace(/([.!?])([A-Za-z])/g, '$1 $2');

  // Basic sentence detection and capitalization
  const sentences = processedText.split(/(?<=[.!?])\s+/);
  const processedSentences = sentences.map((sentence, index) => {
    let processed = sentence.trim();

    // Capitalize first letter of each sentence
    if (processed.length > 0) {
      processed = processed.charAt(0).toUpperCase() + processed.slice(1);
    }

    // Add space after sentence if it's not the last one
    if (index < sentences.length - 1 && !processed.endsWith(' ')) {
      processed += ' ';
    }

    return processed;
  });

  return processedSentences.join('');
};

/**
 * Enhances punctuation for common speech patterns
 */
const enhancePunctuation = (text: string): string => {
  let enhanced = text;

  // Add commas for natural pauses and conjunctions
  enhanced = enhanced.replace(/\b(and|or|but|so|because|however|therefore|meanwhile)\b/gi, (match) => {
    return `, ${match}`;
  });

  // Add question marks for question words
  enhanced = enhanced.replace(/\b(what|when|where|who|why|how|which|whose|whom)\b.*?[.!?]?$/gi, (match) => {
    if (!match.endsWith('?')) {
      return match.replace(/[.!]?$/, '?');
    }
    return match;
  });

  // Add exclamation marks for emphasis words
  enhanced = enhanced.replace(/\b(amazing|incredible|wow|oh|ah|yes|no|stop|wait)\b.*?[.!?]?$/gi, (match) => {
    if (!match.endsWith('!')) {
      return match.replace(/[.!]?$/, '!');
    }
    return match;
  });

  // Clean up multiple punctuation
  enhanced = enhanced.replace(/[.!?]{2,}/g, (match) => match.charAt(0));
  enhanced = enhanced.replace(/[,]{2,}/g, ',');

  return enhanced;
};

/**
 * Corrects typos in text (placeholder for future spellchecker integration)
 */
const correctTypos = (text: string): string => {
  // TODO: Integrate a real spellchecker. For now, just return the text.
  return text;
};

/**
 * Detects the current browser for compatibility handling
 */
const getBrowserInfo = (): string => {
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  return 'Unknown';
};

// ============================================================================
// MAIN HOOK
// ============================================================================

export const useSpeechRecognition = ({
  editor,
  macroData,
  onProcessTranscript,
  editorId = 'default',
}: UseSpeechRecognitionProps) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(false);
  const [finalTranscript, setFinalTranscript] = useState('');

  // ============================================================================
  // REFS
  // ============================================================================

  const recognitionRef = useRef<any>(null);
  const isMountedRef = useRef(true);
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const transcriptQueueRef = useRef<string[]>([]);
  const isProcessingQueueRef = useRef(false);
  const interimTextRef = useRef<string>('');
  const lastInterimUpdateRef = useRef<number>(0);

  // Cursor position tracking
  const beforeCursorRef = useRef<string>('');
  const afterCursorRef = useRef<string>('');
  const isDictationActiveRef = useRef<boolean>(false);

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    setBrowserSupportsSpeechRecognition(!!SpeechRecognition);

    return () => {
      isMountedRef.current = false;
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (processingTimeoutRef.current) {
        clearTimeout(processingTimeoutRef.current);
      }
    };
  }, [editorId]);

  // ============================================================================
  // CURSOR POSITION MANAGEMENT
  // ============================================================================

  /**
   * Captures the current cursor position and updates refs
   */
  const captureCursorPosition = useCallback(() => {
    if (editor && !editor.isDestroyed) {
      const tipTapPos = editor.state.selection.from;
      const content = editor.getText();

      // TipTap's cursor position is off by 1 to the right, so we need to subtract 1
      const actualCursorPos = Math.max(0, tipTapPos - 1);
      beforeCursorRef.current = content.substring(0, actualCursorPos);
      afterCursorRef.current = content.substring(actualCursorPos);
    }
  }, [editor]);

  /**
   * Sets up event listener for cursor position changes
   */
  useEffect(() => {
    if (editor && !editor.isDestroyed && isDictationActiveRef.current) {
      const handleSelectionUpdate = () => {
        if (isDictationActiveRef.current) {
          captureCursorPosition();
        }
      };

      editor.on('selectionUpdate', handleSelectionUpdate);
      return () => {
        editor.off('selectionUpdate', handleSelectionUpdate);
      };
    }
  }, [editor, captureCursorPosition]);

  // ============================================================================
  // TEXT PROCESSING
  // ============================================================================

  /**
   * Updates interim text in the editor
   */
  const updateInterimText = useCallback((text: string) => {
    if (!editor || editor.isDestroyed || !isMountedRef.current || !isDictationActiveRef.current) return;

    const now = Date.now();
    // Throttle updates to avoid too frequent editor updates
    if (now - lastInterimUpdateRef.current < 100) return;
    lastInterimUpdateRef.current = now;

    // Clean the interim text - remove leading/trailing spaces
    const cleanText = text.trim();
    const beforeText = beforeCursorRef.current;
    const afterText = afterCursorRef.current;

    // For interim updates, don't add spaces - just insert the text as-is
    const spaceBefore = '';
    const spaceAfter = '';

    const newContent = beforeText + spaceBefore + cleanText + spaceAfter + afterText;
    editor.commands.setContent(newContent);

    // Position cursor at the end of the inserted transcript
    const cursorPosition = beforeText.length + spaceBefore.length + cleanText.length + 1;
    editor.commands.setTextSelection(cursorPosition);

    interimTextRef.current = cleanText;
  }, [editor]);

  /**
   * Processes the transcript queue
   */
  const processQueue = useCallback(async () => {
    if (!isMountedRef.current || isProcessingQueueRef.current || transcriptQueueRef.current.length === 0) return;

    isProcessingQueueRef.current = true;
    const text = transcriptQueueRef.current.shift() || '';

    try {
      // Check if the transcript matches any macro
      const normalizedText = text.trim().toLowerCase();
      const macro = macroData.find(
        (m) => m.type === "text" && normalizedText.includes(m.name.trim().toLowerCase())
      );

      if (macro) {
        await handleMacroReplacement(macro);
      } else {
        await handleRegularTranscript(text);
      }
    } finally {
      isProcessingQueueRef.current = false;
      // Process next item in queue if any
      if (transcriptQueueRef.current.length > 0) {
        processingTimeoutRef.current = setTimeout(() => {
          processQueue();
        }, 100);
      }
    }
  }, [macroData, onProcessTranscript, editor]);

  /**
   * Handles macro replacement
   */
  const handleMacroReplacement = async (macro: any) => {
    if (!editor) {
      onProcessTranscript(macro.description);
      return;
    }

    if (!editor.isDestroyed) {
      const currentContent = editor.getText();
      let contentWithoutInterim = currentContent;

      // Remove the interim text from the end
      if (interimTextRef.current) {
        const lastInterimIndex = currentContent.lastIndexOf(interimTextRef.current);
        if (lastInterimIndex !== -1) {
          contentWithoutInterim = currentContent.substring(0, lastInterimIndex);
        }
      }

      // Find and remove the trigger word from the end of the content
      const triggerWord = macro.name.trim().toLowerCase();
      const contentLower = contentWithoutInterim.toLowerCase();
      const lastTriggerIndex = contentLower.lastIndexOf(triggerWord);

      if (lastTriggerIndex !== -1 && lastTriggerIndex === contentLower.length - triggerWord.length) {
        contentWithoutInterim = contentWithoutInterim.substring(0, lastTriggerIndex);
      }

      editor.commands.setContent(contentWithoutInterim);
      onProcessTranscript(macro.description);
    }

    interimTextRef.current = '';
  };

  /**
   * Handles regular transcript processing
   */
  const handleRegularTranscript = async (text: string) => {
    // Apply text processing
    const translatedText = translatePunctuation(text);
    const correctedText = correctTypos(translatedText);

    // Only apply auto-punctuation and capitalization if inserting at the end of text
    let punctuatedText = translatedText;
    if (afterCursorRef.current.trim() === '') {
      punctuatedText = enhancePunctuation(addAutoPunctuation(correctedText));
    } else {
      punctuatedText = correctedText;
    }

    // Clear interim text first
    if (editor && !editor.isDestroyed && interimTextRef.current) {
      const currentContent = editor.getText();
      const lastInterimIndex = currentContent.lastIndexOf(interimTextRef.current);
      if (lastInterimIndex !== -1) {
        const contentWithoutInterim = currentContent.substring(0, lastInterimIndex);
        editor.commands.setContent(contentWithoutInterim);
      }
    }

    // Insert final text
    if (editor && !editor.isDestroyed && isDictationActiveRef.current) {
      const beforeText = beforeCursorRef.current;
      const afterText = afterCursorRef.current;

      // Clean the punctuated text - remove leading/trailing spaces
      const cleanPunctuatedText = punctuatedText.trim();

      // For final updates, add 1 space after the text
      const spaceBefore = '';
      const spaceAfter = ' ';

      const finalContent = beforeText + spaceBefore + cleanPunctuatedText + spaceAfter + afterText;
      editor.commands.setContent(finalContent);

      // Calculate cursor position: after the transcript + space
      const cursorPosition = beforeText.length + spaceBefore.length + cleanPunctuatedText.length + spaceAfter.length + 1;

      editor.commands.setTextSelection(cursorPosition);

      // Update beforeCursorRef to include the newly inserted text + space for the next transcript
      beforeCursorRef.current = beforeText + spaceBefore + cleanPunctuatedText + spaceAfter;
    } else {
      // Fallback: use callback
      onProcessTranscript(punctuatedText.trim());
    }

    interimTextRef.current = '';
  };

  // ============================================================================
  // SPEECH RECOGNITION SETUP
  // ============================================================================

  /**
   * Sets up Edge-specific speech recognition with fallback
   */
  const setupEdgeRecognition = (recognition: any) => {
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onerror = (event: any) => {
      console.error('Edge Speech Recognition Error:', event.error);

      if (event.error === 'not-allowed' || event.error === 'service-not-allowed') {
        if (recognitionRef.current) {
          recognitionRef.current.stop();
          recognitionRef.current = null;
        }

        // Try with different settings
        setTimeout(() => {
          if (isMountedRef.current) {
            const altRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            altRecognition.continuous = true;
            altRecognition.interimResults = true;
            altRecognition.lang = 'en-US';
            altRecognition.maxAlternatives = 1;

            altRecognition.onstart = () => {
              if (isMountedRef.current) {
                setIsListening(true);
                setIsProcessing(false);
              }
            };

            altRecognition.onend = () => {
              if (isMountedRef.current) {
                setIsListening(false);
                recognitionRef.current = null;
              }
            };

            altRecognition.onresult = (event: any) => {
              if (!isMountedRef.current) return;

              let finalTranscript = '';
              for (let i = event.resultIndex || 0; i < event.results.length; i++) {
                const result = event.results[i];
                if (result && result[0] && result.isFinal) {
                  finalTranscript += result[0].transcript;
                }
              }

              if (finalTranscript) {
                setFinalTranscript(finalTranscript);
                transcriptQueueRef.current.push(finalTranscript);
                if (!isProcessingQueueRef.current) {
                  processQueue();
                }
              }
            };

            altRecognition.onerror = (event: any) => {
              console.error('Edge alternative settings also failed:', event.error);
              if (isMountedRef.current) {
                setIsListening(false);
                recognitionRef.current = null;
              }
            };

            recognitionRef.current = altRecognition;
            altRecognition.start();
          }
        }, 100);
      }
    };
  };

  /**
   * Sets up standard speech recognition event handlers
   */
  const setupStandardRecognition = (recognition: any) => {
    recognition.onstart = () => {
      if (isMountedRef.current) {
        setIsListening(true);
        setIsProcessing(false);
      }
    };

    recognition.onend = () => {
      if (isMountedRef.current) {
        setIsListening(false);
        recognitionRef.current = null;
      }
    };

    recognition.onresult = (event: any) => {
      if (!isMountedRef.current) return;

      let interimTranscript = '';
      let finalTranscript = '';

      const startIndex = event.resultIndex || 0;

      for (let i = startIndex; i < event.results.length; i++) {
        const result = event.results[i];
        if (!result || !result[0]) continue;

        const transcript = result[0].transcript;
        const isFinal = result.isFinal;

        if (isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (interimTranscript && editor) {
        updateInterimText(interimTranscript);
      }

      if (finalTranscript) {
        setFinalTranscript(finalTranscript);
        transcriptQueueRef.current.push(finalTranscript);
        if (!isProcessingQueueRef.current) {
          processQueue();
        }
      }
    };

    recognition.onerror = (event: any) => {
      console.error('Speech Recognition Error:', {
        error: event.error,
        message: event.message,
        browser: getBrowserInfo()
      });

      if (isMountedRef.current) {
        setIsListening(false);
        recognitionRef.current = null;

        // Provide specific error messages for common issues
        let errorMessage = 'Speech recognition error occurred.';
        switch (event.error) {
          case 'not-allowed':
            errorMessage = 'Microphone access denied. Please allow microphone permissions.';
            break;
          case 'no-speech':
            errorMessage = 'No speech detected. Please try speaking again.';
            break;
          case 'audio-capture':
            errorMessage = 'Audio capture failed. Please check your microphone.';
            break;
          case 'network':
            errorMessage = 'Network error. Please check your internet connection.';
            break;
          case 'service-not-allowed':
            errorMessage = 'Speech recognition service not allowed.';
            break;
          default:
            errorMessage = `Speech recognition error: ${event.error}`;
        }

        console.error('Error Message:', errorMessage);
      }
    };
  };

  // ============================================================================
  // PUBLIC METHODS
  // ============================================================================

  /**
   * Starts speech recognition
   */
  const startListening = useCallback(() => {
    if (!browserSupportsSpeechRecognition || recognitionRef.current) return;

    // Set dictation as active and capture cursor position
    if (editor && !editor.isDestroyed) {
      if (!isDictationActiveRef.current || (beforeCursorRef.current === '' && afterCursorRef.current === '')) {
        const tipTapPos = editor.state.selection.from;
        const content = editor.getText();
        const actualCursorPos = Math.max(0, tipTapPos - 1);
        beforeCursorRef.current = content.substring(0, actualCursorPos);
        afterCursorRef.current = content.substring(actualCursorPos);
      } else {
        // Using existing cursor refs
      }

      isDictationActiveRef.current = true;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognitionRef.current = recognition;

    // Standard configuration
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 1;

    // Browser-specific setup
    const browser = getBrowserInfo();
    if (browser === 'Edge') {
      setupEdgeRecognition(recognition);
    } else {
      setupStandardRecognition(recognition);
    }

    recognition.start();
  }, [browserSupportsSpeechRecognition, setIsListening, setIsProcessing, setFinalTranscript, processQueue, editor, updateInterimText, editorId]);

  /**
   * Stops speech recognition
   */
  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      recognitionRef.current = null;
    }
    setIsListening(false);

    // Reset dictation refs when user explicitly stops dictation
    beforeCursorRef.current = '';
    afterCursorRef.current = '';
    isDictationActiveRef.current = false;
  }, [setIsListening]);

  // ============================================================================
  // RETURN
  // ============================================================================

  return {
    isListening,
    isProcessing,
    browserSupportsSpeechRecognition,
    startListening,
    stopListening,
    finalTranscript,
    captureCursorPosition,
  };
}; 