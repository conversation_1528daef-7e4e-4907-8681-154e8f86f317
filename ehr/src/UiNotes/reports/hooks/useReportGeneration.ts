import { useState, useCallback, Dispatch, SetStateAction } from 'react';
import { useAtom, useAtomValue } from 'jotai';
import {
  reportContentAtom,
  reportTypeAtom,
} from '../context/EditorContext';
import { normalQuery } from '../../util/AIPilot';
import {
  DISCHARGE_SUMMARY_PROMPT,
  ADMISSION_HISTORY_PHYSICAL_PROMPT,
  PROGRESS_NOTE_PROMPT,
  ED_PROGRESS_NOTE_PROMPT,
  CONSULT_NOTE_PROMPT
} from '../Constants';
import { Editor, ChainedCommands } from '@tiptap/core';
import { Node } from '@tiptap/pm/model';
import { EditorState } from '@tiptap/pm/state';

interface EditorInstance {
  getHTML: () => string;
  state: EditorState;
  chain: () => ChainedCommands;
  commands: {
    setContent: (content: any) => void;
    focus: () => void;
  };
  isDestroyed?: boolean;
}

interface SelectedData {
  demographics?: Record<string, any>;
  vitals?: Array<Record<string, any>>;
  medications?: Array<Record<string, any>>;
  inpatientMedications?: Array<Record<string, any>>;
  medRec?: Array<Record<string, any>>;
  problems?: Array<Record<string, any>>;
  diagnosis?: Array<Record<string, any>>;
  procedures?: Array<Record<string, any>>;
  labResults?: Array<Record<string, any>>;
  ros_data?: Record<string, any>;
  exam_data?: Record<string, any>;
}

// Helper function to convert markdown to HTML
function convertMarkdownToHtml(content: string): string {
  return content
    // Convert escaped asterisks to proper HTML
    .replace(/\\\*/g, '*')
    // Convert markdown bold to HTML bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert markdown italic to HTML italic
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert markdown bullet points to HTML lists
    .replace(/^\s*\*\s+(.*)$/gm, '<li>$1</li>')
    // Wrap consecutive list items in ul tags (simplified approach)
    .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>')
    // Clean up multiple ul tags
    .replace(/<\/ul>\s*<ul>/g, '');
}

export const useReportGeneration = (
  dictationEditor: EditorInstance | null,
  reportEditor: EditorInstance | null,
  selectedData: SelectedData,
  onLoadingChange?: (loading: boolean) => void
) => {
  const [reportContent, setReportContent] = useAtom(reportContentAtom);
  const reportType = useAtomValue(reportTypeAtom);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const getPromptForType = (type: string) => {
    switch (type) {
      case 'DISCHARGE_SUMMARY':
        return DISCHARGE_SUMMARY_PROMPT;
      case 'ADMISSION_HISTORY_PHYSICAL':
        return ADMISSION_HISTORY_PHYSICAL_PROMPT;
      case 'PROGRESS_NOTE':
        return PROGRESS_NOTE_PROMPT;
      case 'ED_PROGRESS_NOTE':
        return ED_PROGRESS_NOTE_PROMPT;
      case 'CONSULT_NOTE':
        return CONSULT_NOTE_PROMPT;
      default:
        return DISCHARGE_SUMMARY_PROMPT;
    }
  };

  const handleGenerateReport = useCallback(async () => {
    if (!dictationEditor || !reportEditor) {
      console.error('Editors not initialized');
      return;
    }

    try {
      setIsGeneratingReport(true);
      onLoadingChange?.(true);

      // Get the raw text content from the dictation editor
      const dictationContent = dictationEditor.getHTML()
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .trim();

      // Get the appropriate prompt template based on report type
      const promptTemplate = getPromptForType(reportType);

      // Combine dictation content with selected data
      const prompt = `
        ${promptTemplate}

        DICTATION CONTENT (for Subjective section only):
        ${dictationContent}

        SELECTED DATA (for all other sections):
        ${JSON.stringify(selectedData, null, 2)}
        
        CRITICAL INSTRUCTIONS:
        1. The dictation content above can be used for any section as needed
        2. Use the [DIAGNOSIS_SECTION] placeholder for Current Diagnoses - do NOT generate actual diagnosis text
        3. The Chief Complaint must be generated as a separate, patient-friendly statement
      `;

      // Call the normalQuery function
      const result = await normalQuery({
        prompt,
        setResult: ((value: SetStateAction<string>) => {
          const content = typeof value === 'function' ? value(reportContent) : value;

          // Check if content is already HTML (starts with <html> or <body>)
          let htmlContent;
          if (content.trim().startsWith('<html>') || content.trim().startsWith('<body>')) {
            htmlContent = content;
          } else {
            htmlContent = convertMarkdownToHtml(content);
          }

          // Handle nested lab results structure
          let labResultsData = selectedData.labResults;
          if (labResultsData && typeof labResultsData === 'object' && !Array.isArray(labResultsData)) {
            // If it's an object with labResults property, extract it
            labResultsData = (labResultsData as any).labResults;
          }

          const formattedLabResults = formatLabResults(labResultsData || []);
          const formattedVitalSigns = formatVitalSigns(selectedData.vitals || []);
          const formattedProblems = formatProblems(selectedData.problems || []);

          // Format medications for [MEDICATIONS_SECTION] - use MedRec for Discharge Summary, Medications for others
          let medsArray = [];
          if (reportType === 'DISCHARGE_SUMMARY') {
            // Use MedRec for Discharge Summary
            const medRec = selectedData.medRec as any;
            if (Array.isArray(medRec)) {
              medsArray = medRec;
            } else if (medRec && Array.isArray(medRec.medRec)) {
              medsArray = medRec.medRec;
            }
          } else {
            // Use Medications for other reports
            const medications = selectedData.medications as any;
            if (Array.isArray(medications)) {
              medsArray = medications;
            } else if (medications && Array.isArray(medications.medications)) {
              medsArray = medications.medications;
            }
          }
          // For Discharge Summary, use formatMedRec for medications section too
          const formattedMedications = reportType === 'DISCHARGE_SUMMARY' ? formatMedRec(medsArray) : formatCurrentMedications(medsArray);

          // Format MedRec for [MEDREC_SECTION]
          const medRec = selectedData.medRec as any;
          let medRecArray = [];
          if (Array.isArray(medRec)) {
            medRecArray = medRec;
          } else if (medRec && Array.isArray(medRec.medRec)) {
            medRecArray = medRec.medRec;
          }
          const formattedMedRec = formatMedRec(medRecArray);

          // Format home medications for [HOME_MEDICATIONS_SECTION]
          const homeMeds = selectedData.medications as any;

          let homeMedsArray: any[] = [];
          if (Array.isArray(homeMeds)) {
            homeMedsArray = homeMeds;
          } else if (homeMeds && Array.isArray(homeMeds.medications)) {
            homeMedsArray = homeMeds.medications;
          } else if (homeMeds && typeof homeMeds === 'object') {
            // Try to extract from any nested structure
            const values = Object.values(homeMeds);
            for (const val of values) {
              if (Array.isArray(val)) {
                homeMedsArray = val;
                break;
              }
            }
          }

          const formattedHomeMedications = formatHomeMedications(homeMedsArray);

          // Format procedures for [PROCEDURES_SECTION]
          const procedures = selectedData.procedures as any;
          let proceduresArray = [];
          if (Array.isArray(procedures)) {
            proceduresArray = procedures;
          } else if (procedures && Array.isArray(procedures.procedures)) {
            proceduresArray = procedures.procedures;
          }
          const formattedProcedures = formatProcedures(proceduresArray);

          // Format diagnosis for [DIAGNOSIS_SECTION]
          const diagnosis = selectedData.diagnosis as any;
          const formattedDiagnosis = formatDiagnosis(diagnosis);

          // Format review of systems for [REVIEW_OF_SYSTEMS_SECTION]
          const rosData = selectedData.ros_data || {};
          const formattedReviewOfSystems = formatReviewOfSystems(rosData);

          // Format exam for [EXAM_SECTION]
          const examData = selectedData.exam_data || {};
          const formattedExam = formatExam(examData);

          let processedContent = htmlContent
            .replace('[LAB_RESULTS_SECTION]', formattedLabResults)
            .replace('[VITAL_SIGNS_SECTION]', formattedVitalSigns)
            .replace('[MEDICATIONS_SECTION]', formattedMedications)
            .replace('[MEDREC_SECTION]', formattedMedRec)
            .replace('[HOME_MEDICATIONS_SECTION]', formattedHomeMedications)
            .replace('[DIAGNOSIS_SECTION]', formattedDiagnosis)
            .replace('[REVIEW_OF_SYSTEMS_SECTION]', formattedReviewOfSystems)
            .replace('[EXAM_SECTION]', formattedExam)
            .replace('[PROBLEMS_SECTION]', formattedProblems)
            .replace('[PROCEDURES_SECTION]', formattedProcedures);

          // Handle raw placeholder text that AI might generate
          processedContent = processedContent
            .replace('Home Medications [HOME_MEDICATIONS_SECTION]', formattedHomeMedications)
            .replace('Vital Signs [VITAL_SIGNS_SECTION]', formattedVitalSigns)
            .replace('Lab Results [LAB_RESULTS_SECTION]', formattedLabResults)
            .replace('Procedures [PROCEDURES_SECTION]', formattedProcedures)
            .replace('Medication at Discharge [MEDREC_SECTION]', formattedMedRec)
            .replace('Current Diagnoses [DIAGNOSIS_SECTION]', formattedDiagnosis)
            .replace('Review of Systems [REVIEW_OF_SYSTEMS_SECTION]', formattedReviewOfSystems)
            .replace('Problems [PROBLEMS_SECTION]', formattedProblems)
            .replace('[HOME_MEDICATIONS_SECTION]', formattedHomeMedications)
            .replace('[VITAL_SIGNS_SECTION]', formattedVitalSigns)
            .replace('[LAB_RESULTS_SECTION]', formattedLabResults)
            .replace('[PROCEDURES_SECTION]', formattedProcedures)
            .replace('[MEDREC_SECTION]', formattedMedRec)
            .replace('[DIAGNOSIS_SECTION]', formattedDiagnosis)
            .replace('[REVIEW_OF_SYSTEMS_SECTION]', formattedReviewOfSystems)
            .replace('[EXAM_SECTION]', formattedExam)
            .replace('[PROBLEMS_SECTION]', formattedProblems);

          // Handle problematic instruction text that appears with placeholders
          processedContent = processedContent
            .replace(/Reason for Hospitalization\s*\[REASON_SECTION\]/g, '[REASON_SECTION]')
            .replace(/<strong>Reason for Hospitalization<\/strong>\s*<br>\s*\[REASON_SECTION\]/g, '[REASON_SECTION]');

          // Remove extra discharge content that AI might generate
          processedContent = processedContent
            .replace(/Follow-up Care:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Additional Instructions:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Discharge Medications:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Patient Education:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Attending Physician:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Discharge Date:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Follow-up Care<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Additional Instructions<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Discharge Medications<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Patient Education<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Attending Physician<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Discharge Date<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            // Remove unwanted sections that AI is generating
            .replace(/Patient Information:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Admitting Diagnoses:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Discharge Condition:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/Date of Discharge:[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Patient Information<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Admitting Diagnoses<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Discharge Condition<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            .replace(/<strong>Date of Discharge<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
            // Remove custom placeholders that AI is creating
            .replace(/\[PLACEHOLDER_NAME\]/g, '')
            .replace(/\[PLACEHOLDER_DATE_OF_BIRTH\]/g, '')
            .replace(/\[PLACEHOLDER_MRN\]/g, '')
            .replace(/\[PLACEHOLDER_CONDITION\]/g, '')
            .replace(/\[PLACEHOLDER_DATE_OF_DISCHARGE\]/g, '');

          // Only remove Assessment & Plan for Discharge Summary, not for other reports
          if (reportType === 'DISCHARGE_SUMMARY') {
            processedContent = processedContent
              .replace(/<strong>Assessment & Plan<\/strong>[\s\S]*?(?=<br\/?><strong>|$)/gi, '')
              .replace(/Assessment & Plan[\s\S]*?(?=<br\/?><strong>|$)/gi, '');
          }

          // Test the regex pattern specifically
          const testText = "Reason for Hospitalization [REASON_SECTION]";
          const testPattern = /Reason for Hospitalization\s*\[REASON_SECTION\]/g;

          // Fallback: if AI didn't use placeholders, replace any sections
          if (!htmlContent.includes('[LAB_RESULTS_SECTION]')) {
            // Look for common patterns the AI might use for lab results
            const labResultsPatterns = [
              /<strong>Lab Results<\/strong>\s*<br>\s*No lab results available\./gi,
              /<strong>Lab Results<\/strong>\s*<br>\s*<p>No lab results available\.<\/p>/gi,
              /Lab Results:\s*No lab results available\./gi
            ];

            for (const pattern of labResultsPatterns) {
              if (pattern.test(processedContent)) {
                processedContent = processedContent.replace(pattern, formattedLabResults);
                break;
              }
            }
          }

          if (!htmlContent.includes('[VITAL_SIGNS_SECTION]')) {
            // Look for common patterns the AI might use for vital signs
            const vitalSignsPatterns = [
              /<strong>Vital Signs<\/strong>\s*<br>\s*No vital signs available\./gi,
              /<strong>Vital Signs<\/strong>\s*<br>\s*<p>No vital signs available\.<\/p>/gi,
              /Vital Signs:\s*No vital signs available\./gi
            ];

            for (const pattern of vitalSignsPatterns) {
              if (pattern.test(processedContent)) {
                processedContent = processedContent.replace(pattern, formattedVitalSigns);
                break;
              }
            }
          }

          if (!htmlContent.includes('[PROBLEMS_SECTION]')) {
            // Look for common patterns the AI might use for problems
            const problemsPatterns = [
              /<strong>Problems<\/strong>\s*<br>\s*No problems available\./gi,
              /<strong>Problems<\/strong>\s*<br>\s*<p>No problems available\.<\/p>/gi,
              /Problems:\s*No problems available\./gi
            ];
            for (const pattern of problemsPatterns) {
              if (pattern.test(processedContent)) {
                processedContent = processedContent.replace(pattern, formattedProblems);
                break;
              }
            }
          }

          if (!htmlContent.includes('[MEDICATIONS_SECTION]')) {
            // Look for common patterns the AI might use for medications
            const medicationsPatterns = [
              /<strong>Current Medications<\/strong>\s*<br>\s*No current medications available\./gi,
              /<strong>Current Medications<\/strong>\s*<br>\s*<p>No current medications available\.<\/p>/gi,
              /Current Medications:\s*No current medications available\./gi
            ];
            for (const pattern of medicationsPatterns) {
              if (pattern.test(processedContent)) {
                processedContent = processedContent.replace(pattern, formattedMedications);
                break;
              }
            }
          }

          // Prevent MedRec section from being replaced by medications patterns
          if (reportType === 'DISCHARGE_SUMMARY') {
            // Ensure MedRec section uses the correct format
            processedContent = processedContent.replace(
              /<strong>Medication at Discharge<\/strong>\s*<br>\s*<table>[\s\S]*?<\/table>/gi,
              `<strong>Medication at Discharge</strong><br>${formattedMedRec}`
            );
          }

          if (!htmlContent.includes('[HOME_MEDICATIONS_SECTION]')) {
            // Look for common patterns the AI might use for home medications
            const homeMedicationsPatterns = [
              /<strong>Home Medications<\/strong>\s*<br>\s*No home medications available\./gi,
              /<strong>Home Medications<\/strong>\s*<br>\s*<p>No home medications available\.<\/p>/gi,
              /Home Medications:\s*No home medications available\./gi
            ];
            for (const pattern of homeMedicationsPatterns) {
              if (pattern.test(processedContent)) {
                processedContent = processedContent.replace(pattern, formattedHomeMedications);
                break;
              }
            }
          }











          // Remove any instruction text that might appear in the output
          processedContent = processedContent
            .replace(/- \[GENERATE ONLY [^\]]+\]/g, '')
            .replace(/\[GENERATE ONLY [^\]]+\]/g, '')
            .replace(/That's it\. Include ALL sections shown in the REQUIRED OUTPUT FORMAT above\./g, '');



          // Remove dictation content that appears at the very end (after the last section)
          const lastSectionMatch = processedContent.match(/<br\/?><strong>[^<]+<\/strong>/g);
          if (lastSectionMatch) {
            const lastSectionIndex = processedContent.lastIndexOf(lastSectionMatch[lastSectionMatch.length - 1]);
            const beforeDictation = processedContent.substring(0, lastSectionIndex);
            const afterLastSection = processedContent.substring(lastSectionIndex);

            // Find where the last section ends (before dictation content)
            const dictationStart = afterLastSection.indexOf('DICTATION CONTENT');
            if (dictationStart !== -1) {
              processedContent = beforeDictation + afterLastSection.substring(0, dictationStart);
            }
          }

          // Remove duplicate Assessment & Plan sections if they exist
          const assessmentPlanMatches = processedContent.match(/<br\/?><strong>Assessment & Plan<\/strong>/g);
          if (assessmentPlanMatches && assessmentPlanMatches.length > 1) {
            // Keep only the first Assessment & Plan section
            const sections = processedContent.split('<br/><strong>Assessment & Plan</strong>');
            if (sections.length > 1) {
              const firstSection = sections[0];
              const secondSection = sections[1];
              // Find where the first Assessment & Plan section ends (before the next major section)
              const nextSectionMatch = secondSection.match(/<br\/?><strong>[^<]+<\/strong>/);
              if (nextSectionMatch) {
                const firstAssessmentEnd = secondSection.indexOf(nextSectionMatch[0]);
                const firstAssessmentContent = secondSection.substring(0, firstAssessmentEnd);
                processedContent = firstSection + '<br/><strong>Assessment & Plan</strong>' + firstAssessmentContent;
              }
            }
          }

          // Clean up AI instruction text and internal dialogue from Assessment & Plan section
          processedContent = processedContent
            // Remove common AI instruction text patterns
            .replace(/Please let me know if I should proceed with generating the rest of the content\./gi, '')
            .replace(/Here is what I have generated so far\./gi, '')
            .replace(/Let me know if you would like me to continue\./gi, '')
            .replace(/Please find below what I have been able to generate\./gi, '')
            .replace(/Please let me know if I should continue with generating Assessment and Plan section\./gi, '')
            .replace(/Let me know if I can proceed\./gi, '')
            .replace(/Here is a possible Assessment and Plan:/gi, '')
            .replace(/Please let me know if the information meets your requirements or if I should revise anything\./gi, '')
            .replace(/I have provided information for [^.]* sections as available from provided dictation content/gi, '')
            .replace(/however, some information might not be directly available but can be generated based on provided information\./gi, '')
            .replace(/Please provide feedback so I can assist you better\./gi, '')
            .replace(/I used provided dictation content and tried to adhere to given instructions and format while generating [^.]* sections\./gi, '')
            .replace(/Generated content seems accurate based on provided information/gi, '')
            .replace(/however, it is recommended to review and validate generated content for accuracy and completeness before finalizing\./gi, '')
            .replace(/Please provide further instructions if you need any modifications or if the information seems sufficient\./gi, '')
            .replace(/You can also provide additional information that can help in generating [^.]* sections accurately and completely\./gi, '')
            .replace(/Please let me know if I can assist you further\./gi, '')
            .replace(/Thanks\./gi, '')
            // Remove repetitive content patterns
            .replace(/(Current Diagnosis:[\s\S]*?)(Current Diagnosis:)/gi, '$1')
            .replace(/(Suggested Diagnosis:[\s\S]*?)(Suggested Diagnosis:)/gi, '$1')
            .replace(/(Suggested Plan:[\s\S]*?)(Suggested Plan:)/gi, '$1')
            // Remove extra whitespace and line breaks
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .replace(/\s+$/gm, '');

          // Debug logging for ED Progress Note
          if (reportType === 'ED_PROGRESS_NOTE') {
            // Debug logging removed for cleaner console output
          }

          setReportContent(processedContent);
          if (reportEditor && !reportEditor.isDestroyed) {
            reportEditor.commands.setContent(processedContent);
          }
        }) as Dispatch<SetStateAction<string>>,
        endCallback: () => {
          setIsGeneratingReport(false);
          onLoadingChange?.(false);
        },
      });

      // Final update to ensure content is set
      if (result?.content) {
        // Report generation completed
      }
    } catch (error) {
      console.error('Error generating report:', error);
      setIsGeneratingReport(false);
      onLoadingChange?.(false);
    }
  }, [dictationEditor, reportEditor, selectedData, reportType, setReportContent, reportContent, onLoadingChange]);

  return {
    reportContent,
    handleGenerateReport,
    isGeneratingReport,
  };
};

// Helper function to format lab results with proper HTML table
function formatLabResults(labResults: any[]): string {
  if (!labResults || !Array.isArray(labResults) || labResults.length === 0) {
    return 'No lab results available.';
  }

  // Group lab results by panel
  const panels: { [panel: string]: any[] } = {};
  labResults.forEach(result => {
    const panel = result.lab_panel_name || 'Other';
    if (!panels[panel]) panels[panel] = [];
    panels[panel].push(result);
  });

  // For each panel, build a table
  const panelTables = Object.entries(panels).map(([panelName, results]) => {
    // Get all unique collection times (columns)
    const allTimesSet = new Set<string>();
    results.forEach(r => {
      if (r.lab_collection_time) allTimesSet.add(r.lab_collection_time);
    });

    // Sort times chronologically (oldest to newest)
    const allTimes = Array.from(allTimesSet).sort((a, b) => {
      // Convert lab collection times to Date objects for proper chronological sorting
      const dateA = parseLabDateTime(a);
      const dateB = parseLabDateTime(b);

      if (dateA && dateB) {
        return dateA.getTime() - dateB.getTime(); // Oldest first
      }

      // Fallback to string comparison if parsing fails
      return a.localeCompare(b);
    });

    // Get all unique lab result names (rows)
    const allNamesSet = new Set<string>();
    results.forEach(r => {
      if (r.labresult_name) allNamesSet.add(r.labresult_name);
    });
    const allNames = Array.from(allNamesSet).sort();

    // Build a lookup: name -> time -> result
    const lookup: { [name: string]: { [time: string]: any } } = {};
    results.forEach(r => {
      const name = r.labresult_name;
      const time = r.lab_collection_time;
      if (!lookup[name]) lookup[name] = {};
      lookup[name][time] = r;
    });

    // Table header
    const tableHeader = `<tr><td><strong>Lab Result</strong></td>${allTimes.map(time => {
      const { date, time: timeStr } = formatLabDateTime(time);
      return `<td><strong>${date}</strong><br>${timeStr}</td>`;
    }).join('')}</tr>`;

    // Table rows
    const tableRows = allNames.map(name => {
      const rowCells = allTimes.map(time => {
        const r = lookup[name][time];
        if (!r) return '<td></td>';
        const value = r.labresult_value || 'N/A';
        const unit = r.labresult_unit || '';
        const flag = r.labresult_flag || '';
        const isAbnormal = flag && flag.trim() !== '';
        if (isAbnormal) {
          return `<td><span style=\"color: #ff0000;\">${value}</span> ${unit} <span style=\"color: #ff0000;\">${flag}</span></td>`;
        } else {
          return `<td>${value} ${unit}</td>`;
        }
      }).join('');
      return `<tr><td><strong>${name}</strong></td>${rowCells}</tr>`;
    }).join('');

    return `<div style=\"margin-bottom: 1.5em;\"><div style=\"font-weight: bold; margin-bottom: 0.5em;\">${panelName}</div><table>${tableHeader}${tableRows}</table></div>`;
  });

  return panelTables.join('');
}

// Helper to parse lab collection time to Date object for sorting
function parseLabDateTime(dateStr: string): Date | null {
  if (!dateStr) return null;

  // Handle the specific format "0440 30 Jun 2025" (HHMM DD MMM YYYY)
  const match = dateStr.match(/(\d{4})\s+(\d{1,2})\s+(\w{3})\s+(\d{4})/);
  if (match) {
    const timeStr = match[1];
    const day = match[2];
    const monthName = match[3];
    const year = match[4];

    // Convert month name to number
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthIndex = monthNames.indexOf(monthName);
    if (monthIndex !== -1) {
      const month = monthIndex; // Date constructor expects 0-based month
      const hours = parseInt(timeStr.substring(0, 2));
      const minutes = parseInt(timeStr.substring(2, 4));
      return new Date(parseInt(year), month, parseInt(day), hours, minutes);
    }
  }

  // Check if it's a Unix timestamp (numeric string)
  const timestamp = parseFloat(dateStr);
  if (!isNaN(timestamp)) {
    // If it's a reasonable Unix timestamp (between 1970 and 2100)
    if (timestamp > 0 && timestamp < 4102444800) {
      // Try as seconds first (multiply by 1000)
      let d = new Date(timestamp * 1000);
      if (!isNaN(d.getTime()) && d.getFullYear() > 1970) {
        return d;
      }

      // If that didn't work, try as milliseconds
      d = new Date(timestamp);
      if (!isNaN(d.getTime()) && d.getFullYear() > 1970) {
        return d;
      }
    }
  }

  // Try parsing as regular date string
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) {
    return null;
  }
  return d;
}

// Helper to format date as MM/DD/YYYY
function formatDate(dateStr: string): string {
  if (!dateStr) return '';

  // Handle the specific format "0440 30 Jun 2025"
  const match = dateStr.match(/(\d{1,2})\s+(\w{3})\s+(\d{4})/);
  if (match) {
    const day = match[1].padStart(2, '0');
    const monthName = match[2];
    const year = match[3];

    // Convert month name to number
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthIndex = monthNames.indexOf(monthName);
    if (monthIndex !== -1) {
      const month = (monthIndex + 1).toString().padStart(2, '0');
      return `${month}/${day}/${year}`;
    }
  }

  // Check if it's a Unix timestamp (numeric string)
  const timestamp = parseFloat(dateStr);
  if (!isNaN(timestamp)) {
    // If it's a reasonable Unix timestamp (between 1970 and 2100)
    if (timestamp > 0 && timestamp < 4102444800) {
      // Try as seconds first (multiply by 1000)
      let d = new Date(timestamp * 1000);
      if (!isNaN(d.getTime()) && d.getFullYear() > 1970) {
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        const year = d.getFullYear();
        return `${month}/${day}/${year}`;
      }

      // If that didn't work, try as milliseconds
      d = new Date(timestamp);
      if (!isNaN(d.getTime()) && d.getFullYear() > 1970) {
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        const year = d.getFullYear();
        return `${month}/${day}/${year}`;
      }
    }
  }

  // Try parsing as regular date string
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) return dateStr;
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const year = d.getFullYear();
  return `${month}/${day}/${year}`;
}

// Helper to format lab collection time as date and time (similar to vitals)
function formatLabDateTime(dateStr: string): { date: string, time: string } {
  if (!dateStr) return { date: '', time: '' };

  // Handle the specific format "0440 30 Jun 2025" (HHMM DD MMM YYYY)
  const match = dateStr.match(/(\d{4})\s+(\d{1,2})\s+(\w{3})\s+(\d{4})/);
  if (match) {
    const timeStr = match[1];
    const day = match[2].padStart(2, '0');
    const monthName = match[3];
    const year = match[4];

    // Convert month name to number
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthIndex = monthNames.indexOf(monthName);
    if (monthIndex !== -1) {
      const month = (monthIndex + 1).toString().padStart(2, '0');
      const hours = timeStr.substring(0, 2);
      const minutes = timeStr.substring(2, 4);
      return {
        date: `${month}/${day}/${year}`,
        time: `${hours}:${minutes}`
      };
    }
  }

  // Check if it's a Unix timestamp (numeric string)
  const timestamp = parseFloat(dateStr);
  if (!isNaN(timestamp)) {
    // If it's a reasonable Unix timestamp (between 1970 and 2100)
    if (timestamp > 0 && timestamp < 4102444800) {
      // Try as seconds first (multiply by 1000)
      let d = new Date(timestamp * 1000);
      if (!isNaN(d.getTime()) && d.getFullYear() > 1970) {
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        const year = d.getFullYear();
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        return {
          date: `${month}/${day}/${year}`,
          time: `${hours}:${minutes}`
        };
      }

      // If that didn't work, try as milliseconds
      d = new Date(timestamp);
      if (!isNaN(d.getTime()) && d.getFullYear() > 1970) {
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        const year = d.getFullYear();
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        return {
          date: `${month}/${day}/${year}`,
          time: `${hours}:${minutes}`
        };
      }
    }
  }

  // Try parsing as regular date string
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) {
    return { date: dateStr, time: '' };
  }
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');
  return {
    date: `${month}/${day}/${year}`,
    time: `${hours}:${minutes}`
  };
}

// Helper function to format procedures with proper HTML table
function formatProcedures(procedures: any[]): string {
  if (!procedures || !Array.isArray(procedures) || procedures.length === 0) {
    return '<br>No procedures available.';
  }

  // Create table header
  const tableHeader = `<tr>
    <th>Name</th>
    <th>Date Performed</th>
  </tr>`;

  // Create table rows for each procedure
  const tableRows = procedures.map(procedure => {
    const name = procedure.procedure || procedure.procedure_name || procedure.name || 'Unknown';
    const date = procedure.dateperformed || procedure.procedure_date || procedure.date || procedure.starttime || 'N/A';

    return `<tr>
      <td>${name}</td>
      <td>${date}</td>
    </tr>`;
  }).join('');

  return `<table>
    <thead>
      ${tableHeader}
    </thead>
    <tbody>
      ${tableRows}
    </tbody>
  </table>`;
}

// Helper function to format vital signs using TipTap table commands
export function formatVitalSigns(vitals: any): string {
  // Handle nested vitals structure: {vitals: Array} or just Array
  let vitalsArray = vitals;
  let selectedColumns: any[] = [];
  let isSelectiveMode = false;

  if (vitals && typeof vitals === 'object' && !Array.isArray(vitals)) {
    if ((vitals as any).vitals) {
      vitalsArray = (vitals as any).vitals;
    }
    if ((vitals as any).selectedColumns) {
      selectedColumns = (vitals as any).selectedColumns;
    }
    if ((vitals as any).isSelectiveMode !== undefined) {
      isSelectiveMode = (vitals as any).isSelectiveMode;
    }
  }

  if (!vitalsArray || !Array.isArray(vitalsArray) || vitalsArray.length === 0) {
    return 'No vital signs available.';
  }

  // If in selective mode and no columns are selected, return empty
  if (isSelectiveMode && selectedColumns.length === 0) {
    return 'No vital signs selected.';
  }

  // Filter vitals data to only include selected time keys
  let filteredVitalsArray = vitalsArray;
  if (isSelectiveMode && selectedColumns.length > 0) {
    const selectedTimeKeys = selectedColumns.map(col => col.timeKey);
    filteredVitalsArray = vitalsArray.filter(vital =>
      selectedTimeKeys.includes(vital.key)
    );
  }

  // Define normal ranges for vital signs
  const normalRanges = {
    temperatureF: { min: 97.8, max: 100.4, unit: '°F' },
    temperatureC: { min: 36.5, max: 38.0, unit: '°C' },
    systolicBP: { min: 90, max: 120, unit: 'mmHg' },
    diastolicBP: { min: 60, max: 80, unit: 'mmHg' },
    heartRate: { min: 60, max: 100, unit: 'bpm' },
    respirationRate: { min: 12, max: 18, unit: 'breaths/min' },
    pulseOx: { min: 95, max: 100, unit: '%' },
    painScore: { min: 0, max: 10, unit: '' }
  };

  // Helper function to check if a value is abnormal
  const isAbnormal = (value: number, vitalType: string): boolean => {
    const range = normalRanges[vitalType as keyof typeof normalRanges];
    if (!range) return false;
    return value < range.min || value > range.max;
  };

  // Helper function to format blood pressure
  const formatBloodPressure = (systolic: number, diastolic: number): string => {
    const systolicAbnormal = isAbnormal(systolic, 'systolicBP');
    const diastolicAbnormal = isAbnormal(diastolic, 'diastolicBP');

    if (systolicAbnormal || diastolicAbnormal) {
      return `<span style="color: #ff0000;">${systolic}/${diastolic}</span> mmHg`;
    } else {
      return `${systolic}/${diastolic} mmHg`;
    }
  };

  // Group vitals by time and get all unique vital sign types
  const groupedByTime: { [key: number]: any } = {};
  const allVitalTypes = new Set<string>();
  const timeKeyToDisplay: { [key: number]: { date: string, time: string } } = {};

  filteredVitalsArray.forEach((vital: any) => {
    let timeKey: number = -1;
    if (vital.key) {
      timeKey = vital.key;
      const dateObj = new Date(timeKey * 1000);
      const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
      const day = dateObj.getDate().toString().padStart(2, '0');
      const year = dateObj.getFullYear();
      const hours = dateObj.getHours().toString().padStart(2, '0');
      const minutes = dateObj.getMinutes().toString().padStart(2, '0');
      timeKeyToDisplay[timeKey] = {
        date: `${month}/${day}/${year}`,
        time: `${hours}:${minutes}`
      };
    }

    if (!groupedByTime[timeKey]) {
      groupedByTime[timeKey] = {};
    }

    // Include all vital types for this time key
    if (vital.heartRate !== undefined) {
      groupedByTime[timeKey].heartRate = vital.heartRate;
      allVitalTypes.add('heartRate');
    }
    if (vital.respirationRate !== undefined) {
      groupedByTime[timeKey].respirationRate = vital.respirationRate;
      allVitalTypes.add('respirationRate');
    }
    if (vital.pulseOx !== undefined) {
      groupedByTime[timeKey].pulseOx = vital.pulseOx;
      allVitalTypes.add('pulseOx');
    }
    if (vital.temperatureF !== undefined) {
      groupedByTime[timeKey].temperatureF = vital.temperatureF;
      allVitalTypes.add('temperatureF');
    }
    if (vital.painScore !== undefined) {
      groupedByTime[timeKey].painScore = vital.painScore;
      allVitalTypes.add('painScore');
    }
    if (vital.systolicBP !== undefined && vital.diastolicBP !== undefined) {
      groupedByTime[timeKey].bloodPressure = { systolic: vital.systolicBP, diastolic: vital.diastolicBP };
      allVitalTypes.add('bloodPressure');
    }
  });

  // Get sorted time keys and vital types
  const timeKeys = Object.keys(groupedByTime).map(Number).sort((a, b) => a - b);
  const vitalTypes = Array.from(allVitalTypes).sort();

  if (timeKeys.length === 0) {
    return 'No vital signs available.';
  }

  // Filter out vital types that have all N/A values
  const filteredVitalTypes = vitalTypes.filter(vitalType => {
    return timeKeys.some(timeKey => {
      const value = groupedByTime[timeKey]?.[vitalType];
      return value !== undefined && value !== null;
    });
  });

  // Create table header with times
  const tableHeader = `<tr>
    <td><strong>Vital Sign</strong></td>
    ${timeKeys.map(key => {
    if (key === -1 || !timeKeyToDisplay[key]) {
      return `<td><strong>Unknown</strong><br>Time</td>`;
    }
    const { date, time } = timeKeyToDisplay[key];
    return `<td><strong>${date}</strong><br>${time}</td>`;
  }).join('')}
  </tr>`;

  // Create table rows for each vital sign type
  const tableRows = filteredVitalTypes.map(vitalType => {
    const rowCells = timeKeys.map(key => {
      if (vitalType === 'bloodPressure') {
        const bp = groupedByTime[key].bloodPressure;
        if (
          bp &&
          typeof bp.systolic === 'number' &&
          typeof bp.diastolic === 'number'
        ) {
          return `<td>${formatBloodPressure(bp.systolic, bp.diastolic)}</td>`;
        } else {
          return `<td></td>`;
        }
      } else {
        const value = groupedByTime[key][vitalType];
        if (value === undefined || value === null) {
          return `<td></td>`;
        }
        const isAbnormalValue = isAbnormal(value, vitalType);
        const range = normalRanges[vitalType as keyof typeof normalRanges];
        const unit = range ? range.unit : '';
        if (isAbnormalValue) {
          return `<td><span style="color: #ff0000; font-weight: bold;">${value}</span> ${unit}</td>`;
        } else {
          return `<td>${value} ${unit}</td>`;
        }
      }
    }).join('');
    // Map vital type to display name
    const displayName = {
      heartRate: 'Heart Rate',
      respirationRate: 'Respiratory Rate',
      pulseOx: 'O2 Saturation',
      temperatureF: 'Temperature (°F)',
      painScore: 'Pain Score',
      bloodPressure: 'Blood Pressure'
    }[vitalType] || vitalType;
    return `<tr>
      <td><strong>${displayName}</strong></td>
      ${rowCells}
    </tr>`;
  }).join('');

  const htmlTable = `<div class="report-section">
    <table>
      ${tableHeader}
      ${tableRows}
    </table>
  </div>`;
  // Count header columns
  const headerColCount = 1 + timeKeys.length;
  // Count columns in each row
  const rowColCounts = filteredVitalTypes.map(vitalType => {
    const rowCells = timeKeys.map(key => {
      if (vitalType === 'bloodPressure') {
        const bp = groupedByTime[key].bloodPressure;
        return bp ? 1 : 1;
      } else {
        return 1;
      }
    });
    return 1 + rowCells.length;
  });
  return htmlTable;
}

// Helper function to format problems as a TipTap-compatible HTML table
function formatProblems(problems: any): string {
  // Handle nested problems structure: {problems: Array} or just Array
  let problemsArray = problems;
  if (problems && typeof problems === 'object' && !Array.isArray(problems) && (problems as any).problems) {
    problemsArray = (problems as any).problems;
  }
  if (!problemsArray || !Array.isArray(problemsArray) || problemsArray.length === 0) {
    return '<div class="report-section">No problems available.</div>';
  }
  const tableRows = problemsArray.map((problem: any) => {
    const name = problem.name || 'N/A';
    const onset = problem.onset_date || 'N/A';
    const status = problem.status || 'N/A';
    return `<tr>
      <td>${name}</td>
      <td>${onset}</td>
      <td>${status}</td>
    </tr>`;
  }).join('');
  return `<div class="report-section"><table>
    <thead>
      <tr>
        <th>Problem</th>
        <th>Onset Date</th>
        <th>Status</th>
      </tr>
    </thead>
    <tbody>
      ${tableRows}
    </tbody>
  </table></div>`;
}

// Helper function to format current medications as a TipTap-compatible HTML table
function formatCurrentMedications(medications: any): string {
  // Handle nested medications structure: {medications: Array} or just Array
  let medicationsArray = medications;
  if (medications && typeof medications === 'object' && !Array.isArray(medications) && (medications as any).medications) {
    medicationsArray = (medications as any).medications;
  }
  if (!medicationsArray || !Array.isArray(medicationsArray) || medicationsArray.length === 0) {
    return '<div class="report-section">No current medications available.</div>';
  }

  const tableRows = medicationsArray.map((medication: any) => {
    const name = medication.fsname || medication.medication_name || medication.name || '';
    const dose = medication.dose || medication.dose_value || '';
    const unit = medication.unit || medication.dose_unit || '';
    return `<tr>
      <td>${name}</td>
      <td>${dose}</td>
      <td>${unit}</td>
    </tr>`;
  }).join('');
  return `<div class="report-section"><table>
    <thead>
      <tr>
        <th>Name</th>
        <th>Dose</th>
        <th>Unit</th>
      </tr>
    </thead>
    <tbody>
      ${tableRows}
    </tbody>
  </table></div>`;
}

// Helper function to format MedRec as a TipTap-compatible HTML table
function formatMedRec(medRec: any): string {
  // Handle nested MedRec structure: {medRec: Array} or just Array
  let medRecArray = medRec;
  if (medRec && typeof medRec === 'object' && !Array.isArray(medRec) && (medRec as any).medRec) {
    medRecArray = (medRec as any).medRec;
  }
  if (!medRecArray || !Array.isArray(medRecArray) || medRecArray.length === 0) {
    return '<div class="report-section">No medication reconciliation data available.</div>';
  }

  const tableRows = medRecArray.map((med: any) => {
    // Use the actual field names from the MedRec data
    const name = med.name || '';
    const dosage = med.dose_value || '';
    const frequency = med.frequency || '';
    const startDate = med.fmtlgtime || '';
    const status = med.status || '';
    const provider = med.lgname || '';
    return `<tr>
      <td>${name}</td>
      <td>${dosage}</td>
      <td>${frequency}</td>
      <td>${startDate}</td>
      <td>${status}</td>
      <td>${provider}</td>
    </tr>`;
  }).join('');
  return `<div class="report-section"><table>
    <thead>
      <tr>
        <th>Medication</th>
        <th>Dosage</th>
        <th>Frequency</th>
        <th>Start Date</th>
        <th>Status</th>
        <th>Provider</th>
      </tr>
    </thead>
    <tbody>
      ${tableRows}
    </tbody>
  </table></div>`;
}

// Helper function to format home medications as a TipTap-compatible HTML table
function formatHomeMedications(medications: any): string {
  // Handle nested medications structure: {medications: Array} or just Array
  let medicationsArray = medications;
  if (medications && typeof medications === 'object' && !Array.isArray(medications) && (medications as any).medications) {
    medicationsArray = (medications as any).medications;
  }
  if (!medicationsArray || !Array.isArray(medicationsArray) || medicationsArray.length === 0) {
    return '<div class="report-section">No home medications available.</div>';
  }

  const tableRows = medicationsArray.map((medication: any) => {
    const name = medication.name || '';
    const dosage = medication.dosage || '';
    const frequency = medication.frequency || '';
    const startDate = medication.startDate || medication.starttime || '';
    const status = medication.status || '';
    const provider = medication.provider || '';
    return `<tr>
      <td>${name}</td>
      <td>${dosage}</td>
      <td>${frequency}</td>
      <td>${startDate}</td>
      <td>${status}</td>
      <td>${provider}</td>
    </tr>`;
  }).join('');
  return `<div class="report-section"><table>
    <thead>
      <tr>
        <th>Medication</th>
        <th>Dosage</th>
        <th>Frequency</th>
        <th>Start Date</th>
        <th>Status</th>
        <th>Provider</th>
      </tr>
    </thead>
    <tbody>
      ${tableRows}
    </tbody>
  </table></div>`;
}

// Helper function to format diagnosis data
function formatDiagnosis(diagnosis: any): string {
  if (!diagnosis) {
    return '<div class="report-section">No current diagnoses available.</div>';
  }

  let diagnosisArray = diagnosis;
  if (Array.isArray(diagnosis)) {
    diagnosisArray = diagnosis;
  } else if (diagnosis && Array.isArray(diagnosis.diagnosis)) {
    diagnosisArray = diagnosis.diagnosis;
  }

  if (!Array.isArray(diagnosisArray) || diagnosisArray.length === 0) {
    return '<div class="report-section">No current diagnoses available.</div>';
  }

  const diagnosisItems = diagnosisArray
    .map((item: any) => {
      const name = item.name || item.diagnosis_name || 'Unknown Diagnosis';

      // Check for all possible ICD-10 code field names
      const icd10Code = item.icd10code ||
        item.icd10_code ||
        item.icd_code ||
        item.diagnosis_code ||
        item.diagnosis_icd10 ||
        item.icd_10 ||
        item.diagnosis_snomed ||
        item.ICD10_CM_Code ||
        '';

      if (icd10Code && icd10Code.trim()) {
        return `${name} (${icd10Code.trim()})`;
      } else {
        return name;
      }
    })
    .join('<br>');

  return `<div class="report-section">${diagnosisItems}</div>`;
}

// Helper function to format review of systems data from PNEditGridRs grid data
function formatReviewOfSystems(rosData: any): string {
  if (!rosData) {
    return '<br>No review of systems data available.';
  }

  // Check if this is the new grid data format (array of grid items)
  if (Array.isArray(rosData)) {
    const sections: string[] = [];

    // Process each grid item (section)
    rosData.forEach((item) => {
      if (!item || !item.label || !item.rows || !Array.isArray(item.rows)) {
        return;
      }

      // Skip if the entire section is marked as N/A
      if (item.isNA) {
        return;
      }

      const sectionFindings: string[] = [];

      // Process each row in the section
      item.rows.forEach((row: any) => {
        if (!row || !row.name) {
          return;
        }

        if (row.positive) {
          sectionFindings.push(`Positive for ${row.name}`);
        } else if (row.negative) {
          sectionFindings.push(`Negative for ${row.name}`);
        }
      });

      // Only add the section if it has findings
      if (sectionFindings.length > 0) {
        sections.push(`<strong>${item.label}:</strong> ${sectionFindings.join('. ')}`);
      }
    });

    if (sections.length === 0) {
      return '<br>No review of systems findings documented.';
    }

    return '<br>' + sections.join('<br>');
  }

  // Handle legacy format (object with HTML strings)
  const htmlSections: string[] = [];

  Object.values(rosData).forEach((html) => {
    if (html && typeof html === 'string' && html.trim()) {
      htmlSections.push(html.trim());
    }
  });

  if (htmlSections.length === 0) {
    return '<br>No review of systems data available.';
  }

  return '<br>' + htmlSections.join('<br>');
}

// Helper function to format exam data
function formatExam(examData: Record<string, any>): string {
  if (!examData) {
    return 'No exam data available.';
  }

  // Check if examData has the expected structure
  if (!examData.examData) {
    return 'No exam data available.';
  }

  const sections: string[] = [];

  // Add vitals and nursing note status
  if (examData.vitalsReviewed) {
    sections.push('✓ Vital signs reviewed');
  }
  if (examData.nursingNoteReviewed) {
    sections.push('✓ Nursing note reviewed');
  }

  // Process exam data from different tabs - only show selected findings
  Object.keys(examData.examData).forEach(tabName => {
    if (tabName === 'ALL') return; // Skip ALL tab as it's just references

    const tabData = examData.examData[tabName];
    if (Array.isArray(tabData) && tabData.length > 0) {
      const tabFindings: string[] = [];

      tabData.forEach((card: any) => {
        if (card.sections && card.sections.length > 0) {
          card.sections.forEach((section: any) => {
            section.rows.forEach((row: any) => {
              if (row.type === 'checkbox' && row.value === true) {
                tabFindings.push(row.label);
              } else if (row.type === 'plusminus' && row.value) {
                const symbol = row.value === '+' ? '+' : row.value === '-' ? '-' : '';
                if (symbol) {
                  const prefix = symbol === '+' ? 'Positive for ' : 'Negative for ';
                  tabFindings.push(prefix + row.label);
                }
              } else if (row.type === 'multiCheckbox' && row.checkboxes && Array.isArray(row.checkboxes)) {
                // Process multiCheckbox rows - find checked checkboxes
                const checkedCheckboxes = row.checkboxes.filter((checkbox: any) => checkbox.value === true);
                if (checkedCheckboxes.length > 0) {
                  const checkboxLabels = checkedCheckboxes.map((checkbox: any) => checkbox.label);
                  tabFindings.push(`${row.label}: ${checkboxLabels.join(', ')}`);
                }
              }
            });
          });
        }

        // Add additional notes if present
        if (card.notes && card.notes.trim()) {
          tabFindings.push(card.notes.trim());
        }
      });

      // Only add the tab if it has findings
      if (tabFindings.length > 0) {
        const tabDisplayName = getTabDisplayName(tabName);
        sections.push(`<strong>${tabDisplayName}:</strong> ${tabFindings.join('. ')}`);
      }
    }
  });

  if (sections.length === 0) {
    return 'No exam findings available.';
  }

  return sections.join('<br>');
}

// Helper function to get tab display name
function getTabDisplayName(tabName: string): string {
  const tabNameMap: { [key: string]: string } = {
    'HEENT': 'HEENT',
    'Neck': 'Neck',
    'Cardio': 'Cardiovascular',
    'PULM': 'Pulmonary',
    'Abd': 'Abdominal',
    'GU/AR': 'Genitourinary/Anorectal',
    'Musc': 'Musculoskeletal',
    'UPPER': 'Upper Extremities',
    'LOWER': 'Lower Extremities'
  };

  return tabNameMap[tabName] || tabName;
}

// Helper function to convert selected data to text
function selectedDataToText(selectedData: SelectedData | null | undefined): string {
  if (!selectedData) return '';

  let result = '';
  if (selectedData.demographics) {
    result += 'demographics\n';
    for (const [key, value] of Object.entries(selectedData.demographics)) {
      result += `${key}: ${JSON.stringify(value ?? '')}\n`;
    }
    result += '\n';
  }
  if (selectedData.vitals && Array.isArray(selectedData.vitals)) {
    result += 'vitals\n';
    selectedData.vitals.forEach((vital) => {
      for (const [key, value] of Object.entries(vital)) {
        result += `${key}: ${JSON.stringify(value ?? '')}\n`;
      }
      result += '\n';
    });
  }
  if (selectedData.medications && Array.isArray(selectedData.medications)) {
    result += 'medications\n';
    selectedData.medications.forEach((med) => {
      for (const [key, value] of Object.entries(med)) {
        result += `${key}: ${JSON.stringify(value ?? '')}\n`;
      }
      result += '\n';
    });
  }
  if (selectedData.problems && Array.isArray(selectedData.problems)) {
    result += 'problems\n';
    selectedData.problems.forEach((problem) => {
      for (const [key, value] of Object.entries(problem)) {
        result += `${key}: ${JSON.stringify(value ?? '')}\n`;
      }
      result += '\n';
    });
  }
  if (selectedData.diagnosis && Array.isArray(selectedData.diagnosis)) {
    result += 'diagnosis\n';
    selectedData.diagnosis.forEach((diag) => {
      for (const [key, value] of Object.entries(diag)) {
        result += `${key}: ${JSON.stringify(value ?? '')}\n`;
      }
      result += '\n';
    });
  }
  if (selectedData.procedures && Array.isArray(selectedData.procedures)) {
    result += 'procedures\n';
    selectedData.procedures.forEach((proc) => {
      for (const [key, value] of Object.entries(proc)) {
        result += `${key}: ${JSON.stringify(value ?? '')}\n`;
      }
      result += '\n';
    });
  }
  return result.trim();
}

// Add this style block to the report output or main report component if not already present
const reportSectionStyle = `<style>
.report-section {
  margin-top: 1.5em;
  margin-bottom: 1.5em;
}
.report-section:first-child {
  margin-top: 0;
}
</style>`; 