import { useState, useCallback, useEffect } from "react";
import { ReportAPI } from "../services/ReportAPI";

interface ReportParams {
  campus: string;
  dbpath: string;
  reportEditor: any;
}

export const useReportState = ({
  campus,
  dbpath,
  reportEditor,
}: ReportParams) => {
  const [reportList, setReportList] = useState<any[]>([]);
  const [isLoadingReports, setIsLoadingReports] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [isSaveAsDialogOpen, setIsSaveAsDialogOpen] = useState(false);
  const [isVersionHistoryOpen, setIsVersionHistoryOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const patID = useCallback(() => {
    const lastIndexOfP = dbpath.lastIndexOf("p");
    if (lastIndexOfP > 0 && lastIndexOfP < dbpath.length) {
      return dbpath.substr(lastIndexOfP + 1);
    }
    return "";
  }, [dbpath])();

  // Function to refresh just the selected report with latest signature data
  const refreshSelectedReport = useCallback(async () => {
    if (!selectedReport || !campus || !patID) return;
    
    try {
      console.log("Refreshing selected report:", selectedReport.noteID);
      const individualReport = await ReportAPI.getNote(campus, patID, selectedReport.noteID, selectedReport.type);
      if (individualReport) {
        console.log("Updated report data:", individualReport);
        console.log("Signature data:", individualReport.signatures, individualReport.signed, individualReport.lastSignedBy);
        setSelectedReport(individualReport);
        
        // Update editor content if the report content has changed
        if (reportEditor && individualReport.content && individualReport.content !== reportEditor.getHTML()) {
          console.log("Updating editor content with latest data");
          reportEditor.commands.setContent(individualReport.content);
        }
      } else {
        console.log("No individual report data received");
      }
    } catch (error) {
      console.error("Error refreshing selected report:", error);
    }
  }, [selectedReport, campus, patID, reportEditor]);

  const fetchReportList = useCallback(async () => {
    setIsLoadingReports(true);
    try {
      const data = await ReportAPI.getReports(campus, patID);
      setReportList(data || []);
      
      // Update selectedReport with latest data if it exists
      if (selectedReport && data) {
        const updatedReport = data.find((report: any) => report.noteID === selectedReport.noteID);
        if (updatedReport) {
          // Always fetch the individual report to get complete signature data
          try {
            const individualReport = await ReportAPI.getNote(campus, patID, selectedReport.noteID, selectedReport.type);
            if (individualReport) {
              setSelectedReport(individualReport);
            } else {
              setSelectedReport(updatedReport);
            }
          } catch (error) {
            console.error("Error fetching individual report, using list data:", error);
            setSelectedReport(updatedReport);
          }
        } else {
          // If the report is not in the list, fetch it individually to get latest signature data
          try {
            const individualReport = await ReportAPI.getNote(campus, patID, selectedReport.noteID, selectedReport.type);
            if (individualReport) {
              setSelectedReport(individualReport);
            }
          } catch (error) {
            console.error("Error fetching individual report:", error);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching report list:", error);
    } finally {
      setIsLoadingReports(false);
    }
  }, [campus, patID, selectedReport]);

  // Fetch report list when component mounts
  useEffect(() => {
    if (campus && patID) {
      fetchReportList();
    }
  }, [campus, patID, fetchReportList]);

  // Auto-refresh selected report when it changes to ensure latest data
  useEffect(() => {
    if (selectedReport && campus && patID) {
      console.log("Selected report changed, ensuring latest data...");
      // Small delay to avoid excessive API calls
      const timer = setTimeout(() => {
        refreshSelectedReport();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [selectedReport?.noteID, selectedReport?.signed, campus, patID, refreshSelectedReport]);

  const handleSaveReport = async (editor: any) => {
    console.log("=== handleSaveReport FUNCTION ENTERED ===");
    if (!selectedReport || !editor) {
      console.log("Early return - selectedReport:", !!selectedReport, "editor:", !!editor);
      return;
    }

    try {
      console.log("=== REPORT SAVE BUTTON CLICKED ===");
      console.log("handleSaveReport called");

      // Add header to the report content
      const currentContent = editor.getText();
      console.log("Current content:", currentContent);

      const headerContent = "put header here...\n\n";
      const contentWithHeader = headerContent + currentContent;
      console.log("Content with header:", contentWithHeader);

      // Update the editor content with the header
      editor.commands.setContent(contentWithHeader);
      console.log("Content set in editor");

      // Comment out actual saving for now - uncomment when ready to save
      /*
      const staffid = Cci ? Cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";
      await ReportAPI.updateReport(
        campus,
        patID,
        {
          ...selectedReport,
          content: contentWithHeader,
          lastModifiedTime: new Date().toISOString(),
        },
        staffid,
        ccitoken
      );
      await fetchReportList();
      */

      console.log("Header added to report (saving commented out for testing)");
    } catch (error) {
      console.error("Error saving report:", error);
    }
  };

  const handleDeleteReport = async (editor: any) => {
    if (!selectedReport) return;

    try {
      const staffid = Cci ? Cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";

      await ReportAPI.deleteReport(
        campus,
        patID,
        selectedReport.noteID,
        selectedReport.type, // Pass note type for NIT
        staffid,
        ccitoken
      );
      if (editor) {
        editor.commands.setContent("");
      }
      setSelectedReport(null);
      await fetchReportList();
    } catch (error) {
      console.error("Error deleting report:", error);
    }
  };

  const handleSaveReportAs = async (title: string, type: string) => {
    if (!reportEditor) return;

    try {
      const content = reportEditor.getHTML();
      const staffid = Cci ? Cci.util.Staff.getSid() : 0;
      const ccitoken =
        localStorage.getItem("webtoken") ||
        window.sessionStorage.getItem("webtoken") ||
        "";
      const newReport = await ReportAPI.createReport(
        campus,
        patID,
        {
          title,
          type,
          content,
        },
        staffid,
        ccitoken
      );

      setSelectedReport(newReport);
      fetchReportList();
      setIsSaveAsDialogOpen(false);
    } catch (error) {
      console.error("Failed to save report:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleVersionHistory = () => {
    if (selectedReport) {
      setIsVersionHistoryOpen(true);
    }
  };

  const handleVersionSelect = (version: any, editor: any) => {
    if (editor) {
      editor.commands.setContent(version.content);
      setIsVersionHistoryOpen(false);
    }
  };

  return {
    reportList,
    isLoadingReports,
    selectedReport,
    isSaveAsDialogOpen,
    isVersionHistoryOpen,
    isRefreshing,
    setSelectedReport,
    setIsSaveAsDialogOpen,
    setIsVersionHistoryOpen,
    setIsRefreshing,
    fetchReportList,
    refreshSelectedReport,
    handleSaveReport,
    handleDeleteReport,
    handleSaveReportAs,
    handleVersionHistory,
    handleVersionSelect,
  };
};
