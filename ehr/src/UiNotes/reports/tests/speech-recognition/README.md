# Speech Recognition Test Suite

This folder contains comprehensive tests for the speech recognition functionality in the UiNotes application.

## 📁 Test Files

### `cursor-positioning.test.js`
Tests the cursor positioning logic for speech dictation, including:
- Basic cursor positioning scenarios
- Multiple consecutive transcripts
- Text cleaning (removing leading/trailing spaces)
- Manual cursor movement during dictation
- Edge cases and bug scenarios

### `text-processing.test.js`
Tests the text processing features, including:
- Punctuation translation (e.g., "period" → ".")
- Auto-punctuation and capitalization
- Enhanced punctuation for speech patterns
- Text cleaning and whitespace handling
- Combined processing pipeline
- Edge cases

### `run-all-tests.js`
A test runner that executes all tests and provides a summary report.

## 🚀 Running Tests

### Run all tests:
```bash
node ehr/src/UiNotes/reports/tests/speech-recognition/run-all-tests.js
```

### Run individual test files:
```bash
# Cursor positioning tests
node ehr/src/UiNotes/reports/tests/speech-recognition/cursor-positioning.test.js

# Text processing tests
node ehr/src/UiNotes/reports/tests/speech-recognition/text-processing.test.js
```

## 📊 Test Results

The tests provide detailed output showing:
- ✅ PASS - Test case passed
- ❌ FAIL - Test case failed (with expected vs actual results)
- Summary statistics
- Success rate percentage

## 🧪 Test Coverage

### Cursor Positioning Tests
- ✅ Basic cursor positioning (front of text, middle of text)
- ✅ Multiple consecutive transcripts
- ✅ Text cleaning (leading/trailing spaces)
- ✅ Manual cursor movement during dictation
- ✅ Edge cases (empty text, single characters)

### Text Processing Tests
- ✅ Punctuation translation
- ✅ Auto-punctuation and capitalization
- ✅ Enhanced punctuation (conjunctions, question words, emphasis words)
- ✅ Text cleaning
- ✅ Combined processing pipeline
- ✅ Edge cases

## 🔧 Adding New Tests

To add new test files:

1. Create a new `.test.js` file in this folder
2. Follow the existing naming convention: `feature-name.test.js`
3. Add the test file to the `tests` array in `run-all-tests.js`
4. Include comprehensive test cases with clear descriptions
5. Use the same output format for consistency

## 📝 Test Structure

Each test file should:
- Have a clear header comment describing its purpose
- Include mock implementations of dependencies
- Test both success and failure scenarios
- Provide clear, readable output
- Use consistent formatting and emojis for visual clarity

## 🎯 Purpose

These tests help ensure:
- Cursor positioning works correctly during speech dictation
- Text processing features function as expected
- Edge cases are handled properly
- Bugs are caught early in development
- Code changes don't break existing functionality

The test suite is particularly valuable for the cursor positioning fixes we implemented, as it simulates the exact scenarios that were problematic and verifies they now work correctly. 