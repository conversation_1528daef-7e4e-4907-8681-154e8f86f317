/**
 * Test file for text processing features in speech recognition
 * Tests punctuation translation, auto-punctuation, and text cleaning
 */

// Mock the translatePunctuation function (simplified version for testing)
const translatePunctuation = (text) => {
  // Simple mock - in real implementation this would be more complex
  return text
    .replace(/period/g, '.')
    .replace(/comma/g, ',')
    .replace(/question mark/g, '?')
    .replace(/exclamation mark/g, '!')
    .replace(/semicolon/g, ';')
    .replace(/colon/g, ':');
};

// Auto-punctuation function (copied from the actual implementation)
const addAutoPunctuation = (text) => {
  if (!text.trim()) return text;

  let processedText = text.trim();

  // Add period at the end if no punctuation exists
  if (!/[.!?]$/.test(processedText)) {
    processedText += '.';
  }

  // Add space after periods and question marks (but not at the very end)
  processedText = processedText.replace(/([.!?])([A-Za-z])/g, '$1 $2');

  // Basic sentence detection and capitalization
  const sentences = processedText.split(/(?<=[.!?])\s+/);
  const processedSentences = sentences.map((sentence, index) => {
    let processed = sentence.trim();

    // Capitalize first letter of each sentence
    if (processed.length > 0) {
      processed = processed.charAt(0).toUpperCase() + processed.slice(1);
    }

    // Add space after sentence if it's not the last one
    if (index < sentences.length - 1 && !processed.endsWith(' ')) {
      processed += ' ';
    }

    return processed;
  });

  return processedSentences.join('');
};

// Enhanced punctuation function (copied from the actual implementation)
const enhancePunctuation = (text) => {
  let enhanced = text;

  // Add commas for natural pauses and conjunctions
  enhanced = enhanced.replace(/\b(and|or|but|so|because|however|therefore|meanwhile)\b/gi, (match) => {
    return `, ${match}`;
  });

  // Add question marks for question words
  enhanced = enhanced.replace(/\b(what|when|where|who|why|how|which|whose|whom)\b.*?[.!?]?$/gi, (match) => {
    if (!match.endsWith('?')) {
      return match.replace(/[.!]?$/, '?');
    }
    return match;
  });

  // Add exclamation marks for emphasis words
  enhanced = enhanced.replace(/\b(amazing|incredible|wow|oh|ah|yes|no|stop|wait)\b.*?[.!?]?$/gi, (match) => {
    if (!match.endsWith('!')) {
      return match.replace(/[.!]?$/, '!');
    }
    return match;
  });

  // Clean up multiple punctuation
  enhanced = enhanced.replace(/[.!?]{2,}/g, (match) => match.charAt(0));
  enhanced = enhanced.replace(/[,]{2,}/g, ',');

  return enhanced;
};

// Text cleaning function (simulating the .trim() functionality)
const cleanText = (text) => {
  return text.trim();
};

// Test functions
function testPunctuationTranslation() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING PUNCTUATION TRANSLATION");
  console.log("=".repeat(60));

  const testCases = [
    { input: "hello period", expected: "hello ." },
    { input: "hello comma world", expected: "hello , world" },
    { input: "what is this question mark", expected: "what is this ?" },
    { input: "wow exclamation mark", expected: "wow !" },
    { input: "hello semicolon there", expected: "hello ; there" },
    { input: "hello colon world", expected: "hello : world" },
    { input: "normal text", expected: "normal text" },
  ];

  testCases.forEach((testCase, index) => {
    const result = translatePunctuation(testCase.input);
    const passed = result === testCase.expected;
    console.log(`${index + 1}. Input: "${testCase.input}"`);
    console.log(`   Expected: "${testCase.expected}"`);
    console.log(`   Result: "${result}"`);
    console.log(`   ${passed ? "✅ PASS" : "❌ FAIL"}\n`);
  });
}

function testAutoPunctuation() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING AUTO-PUNCTUATION");
  console.log("=".repeat(60));

  const testCases = [
    { input: "hello world", expected: "Hello world." },
    { input: "hello world.", expected: "Hello world." },
    { input: "hello world?", expected: "Hello world?" },
    { input: "hello world!", expected: "Hello world!" },
    { input: "hello. world", expected: "Hello. World." },
    { input: "hello? world", expected: "Hello? World." },
    { input: "hello! world", expected: "Hello! World." },
    { input: "hello. world. test", expected: "Hello. World. Test." },
    { input: "", expected: "" },
    { input: "   ", expected: "" },
  ];

  testCases.forEach((testCase, index) => {
    const result = addAutoPunctuation(testCase.input);
    const passed = result === testCase.expected;
    console.log(`${index + 1}. Input: "${testCase.input}"`);
    console.log(`   Expected: "${testCase.expected}"`);
    console.log(`   Result: "${result}"`);
    console.log(`   ${passed ? "✅ PASS" : "❌ FAIL"}\n`);
  });
}

function testEnhancedPunctuation() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING ENHANCED PUNCTUATION");
  console.log("=".repeat(60));

  const testCases = [
    { input: "hello and world", expected: "hello, and world" },
    { input: "hello or world", expected: "hello, or world" },
    { input: "hello but world", expected: "hello, but world" },
    { input: "what is this", expected: "what is this?" },
    { input: "where are you", expected: "where are you?" },
    { input: "how do you do", expected: "how do you do?" },
    { input: "wow that's amazing", expected: "wow that's amazing!" },
    { input: "oh my god", expected: "oh my god!" },
    { input: "yes I agree", expected: "yes I agree!" },
    { input: "hello and world but also", expected: "hello, and world, but also" },
    { input: "what is this and how do you do", expected: "what is this? and how do you do?" },
    { input: "hello... world", expected: "hello. world" },
    { input: "hello,,, world", expected: "hello, world" },
  ];

  testCases.forEach((testCase, index) => {
    const result = enhancePunctuation(testCase.input);
    const passed = result === testCase.expected;
    console.log(`${index + 1}. Input: "${testCase.input}"`);
    console.log(`   Expected: "${testCase.expected}"`);
    console.log(`   Result: "${result}"`);
    console.log(`   ${passed ? "✅ PASS" : "❌ FAIL"}\n`);
  });
}

function testTextCleaning() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING TEXT CLEANING");
  console.log("=".repeat(60));

  const testCases = [
    { input: "hello world", expected: "hello world" },
    { input: " hello world", expected: "hello world" },
    { input: "hello world ", expected: "hello world" },
    { input: "  hello world  ", expected: "hello world" },
    { input: "\thello world\t", expected: "hello world" },
    { input: "\nhello world\n", expected: "hello world" },
    { input: "   ", expected: "" },
    { input: "", expected: "" },
  ];

  testCases.forEach((testCase, index) => {
    const result = cleanText(testCase.input);
    const passed = result === testCase.expected;
    console.log(`${index + 1}. Input: "${testCase.input}"`);
    console.log(`   Expected: "${testCase.expected}"`);
    console.log(`   Result: "${result}"`);
    console.log(`   ${passed ? "✅ PASS" : "❌ FAIL"}\n`);
  });
}

function testCombinedProcessing() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING COMBINED TEXT PROCESSING");
  console.log("=".repeat(60));

  const testCases = [
    {
      input: "hello world",
      description: "Basic text with auto-punctuation",
      expected: "Hello world."
    },
    {
      input: "what is this and how do you do",
      description: "Question words with conjunctions",
      expected: "What is this? and how do you do?"
    },
    {
      input: "wow that's amazing and incredible",
      description: "Emphasis words with conjunctions",
      expected: "Wow that's amazing! and incredible!"
    },
    {
      input: "  hello world  ",
      description: "Text with leading/trailing spaces",
      expected: "Hello world."
    },
    {
      input: "hello period comma world question mark",
      description: "Punctuation translation",
      expected: "Hello . , world ?"
    },
  ];

  testCases.forEach((testCase, index) => {
    // Simulate the full processing pipeline
    let result = cleanText(testCase.input);
    result = translatePunctuation(result);
    result = enhancePunctuation(result);
    result = addAutoPunctuation(result);

    const passed = result === testCase.expected;
    console.log(`${index + 1}. ${testCase.description}`);
    console.log(`   Input: "${testCase.input}"`);
    console.log(`   Expected: "${testCase.expected}"`);
    console.log(`   Result: "${result}"`);
    console.log(`   ${passed ? "✅ PASS" : "❌ FAIL"}\n`);
  });
}

function testEdgeCases() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING EDGE CASES");
  console.log("=".repeat(60));

  const testCases = [
    {
      input: "",
      description: "Empty string",
      expected: ""
    },
    {
      input: "   ",
      description: "Only whitespace",
      expected: ""
    },
    {
      input: "a",
      description: "Single character",
      expected: "A."
    },
    {
      input: "a.",
      description: "Single character with period",
      expected: "A."
    },
    {
      input: "hello...world",
      description: "Multiple periods",
      expected: "Hello. world."
    },
    {
      input: "hello,,,world",
      description: "Multiple commas",
      expected: "Hello, world."
    },
    {
      input: "what?",
      description: "Question word with existing question mark",
      expected: "What?"
    },
    {
      input: "wow!",
      description: "Emphasis word with existing exclamation mark",
      expected: "Wow!"
    },
  ];

  testCases.forEach((testCase, index) => {
    // Simulate the full processing pipeline
    let result = cleanText(testCase.input);
    result = translatePunctuation(result);
    result = enhancePunctuation(result);
    result = addAutoPunctuation(result);

    const passed = result === testCase.expected;
    console.log(`${index + 1}. ${testCase.description}`);
    console.log(`   Input: "${testCase.input}"`);
    console.log(`   Expected: "${testCase.expected}"`);
    console.log(`   Result: "${result}"`);
    console.log(`   ${passed ? "✅ PASS" : "❌ FAIL"}\n`);
  });
}

// Run all tests
console.log("🧪 TEXT PROCESSING TESTS");
console.log("Testing speech recognition text processing features...");

testPunctuationTranslation();
testAutoPunctuation();
testEnhancedPunctuation();
testTextCleaning();
testCombinedProcessing();
testEdgeCases();

console.log("=".repeat(60));
console.log("✅ ALL TEXT PROCESSING TESTS COMPLETED");
console.log("=".repeat(60)); 