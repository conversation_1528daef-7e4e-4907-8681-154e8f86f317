/**
 * Test runner for all speech recognition tests
 */

const { execSync } = require('child_process');
const path = require('path');

console.log("🧪 SPEECH RECOGNITION TEST SUITE");
console.log("=".repeat(60));

const tests = [
  {
    name: "Cursor Positioning Tests",
    file: "cursor-positioning.test.js"
  },
  {
    name: "Text Processing Tests",
    file: "text-processing.test.js"
  }
];

let passedTests = 0;
let totalTests = tests.length;

tests.forEach((test, index) => {
  console.log(`\n${index + 1}. Running ${test.name}...`);
  console.log("-".repeat(40));

  try {
    const testPath = path.join(__dirname, test.file);
    execSync(`node "${testPath}"`, { stdio: 'inherit' });
    console.log(`✅ ${test.name} completed successfully`);
    passedTests++;
  } catch (error) {
    console.log(`❌ ${test.name} failed`);
    console.error(error.message);
  }
});

console.log("\n" + "=".repeat(60));
console.log("📊 TEST SUMMARY");
console.log("=".repeat(60));
console.log(`Total tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${totalTests - passedTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log("\n🎉 ALL TESTS PASSED!");
} else {
  console.log("\n⚠️  Some tests failed. Please review the output above.");
}

console.log("=".repeat(60)); 