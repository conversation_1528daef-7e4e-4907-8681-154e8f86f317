// Test script to verify cursor positioning logic
// This simulates the behavior described in the user's scenarios

function testCursorPositioning() {
  console.log("=== Testing Cursor Positioning Logic ===\n");

  // Scenario 1: Cursor in front of "a" in "a b c d e"
  console.log("Scenario 1: Cursor in front of 'a' in 'a b c d e'");
  let originalText = "a b c d e";
  let beforeCursor = ""; // Cursor is at position 0
  let afterCursor = "a b c d e";

  console.log(`Original text: "${originalText}"`);
  console.log(`Before cursor: "${beforeCursor}"`);
  console.log(`After cursor: "${afterCursor}"`);

  // First transcript: "testing"
  let transcript1 = "testing";
  let spaceBefore = "";
  let spaceAfter = " ";

  let newBeforeCursor = beforeCursor + spaceBefore + transcript1 + spaceAfter;
  let newAfterCursor = afterCursor;
  let cursorPosition = newBeforeCursor.length;

  console.log(`\nAfter first transcript "${transcript1}":`);
  console.log(`New before cursor: "${newBeforeCursor}"`);
  console.log(`New after cursor: "${newAfterCursor}"`);
  console.log(`Cursor position: ${cursorPosition}`);
  console.log(`Result: "${newBeforeCursor}|${newAfterCursor}"`);

  // Second transcript: "testing again"
  let transcript2 = "testing again";
  beforeCursor = newBeforeCursor;
  afterCursor = newAfterCursor;

  newBeforeCursor = beforeCursor + spaceBefore + transcript2 + spaceAfter;
  newAfterCursor = afterCursor;
  cursorPosition = newBeforeCursor.length;

  console.log(`\nAfter second transcript "${transcript2}":`);
  console.log(`New before cursor: "${newBeforeCursor}"`);
  console.log(`New after cursor: "${newAfterCursor}"`);
  console.log(`Cursor position: ${cursorPosition}`);
  console.log(`Result: "${newBeforeCursor}|${newAfterCursor}"`);

  console.log("\n" + "=".repeat(50) + "\n");

  // Scenario 2: Cursor after "a" in "a b c d e" (add space after transcript)
  console.log("Scenario 2: Cursor after 'a' in 'a b c d e' (add space after transcript)");
  originalText = "a b c d e";
  beforeCursor = "a "; // Cursor is after "a "
  afterCursor = "b c d e";

  console.log(`Original text: "${originalText}"`);
  console.log(`Before cursor: "${beforeCursor}"`);
  console.log(`After cursor: "${afterCursor}"`);

  // First transcript: "testing"
  transcript1 = "testing";
  spaceBefore = "";
  spaceAfter = " ";

  newBeforeCursor = beforeCursor + spaceBefore + transcript1 + spaceAfter;
  newAfterCursor = afterCursor;
  cursorPosition = newBeforeCursor.length;

  console.log(`\nAfter first transcript "${transcript1}":`);
  console.log(`New before cursor: "${newBeforeCursor}"`);
  console.log(`New after cursor: "${newAfterCursor}"`);
  console.log(`Cursor position: ${cursorPosition}`);
  console.log(`Result: "${newBeforeCursor}|${newAfterCursor}"`);

  // Second transcript: "testing again"
  transcript2 = "testing again";
  beforeCursor = newBeforeCursor;
  afterCursor = newAfterCursor;

  newBeforeCursor = beforeCursor + spaceBefore + transcript2 + spaceAfter;
  newAfterCursor = afterCursor;
  cursorPosition = newBeforeCursor.length;

  console.log(`\nAfter second transcript "${transcript2}":`);
  console.log(`New before cursor: "${newBeforeCursor}"`);
  console.log(`New after cursor: "${newAfterCursor}"`);
  console.log(`Cursor position: ${cursorPosition}`);
  console.log(`Result: "${newBeforeCursor}|${newAfterCursor}"`);

  console.log("\n" + "=".repeat(50) + "\n");

  // Test the actual logic from the code
  console.log("Testing the actual code logic:");

  function simulateCodeLogic(beforeText, afterText, punctuatedText) {
    const spaceBefore = "";
    const spaceAfter = " ";

    const finalContent = beforeText + spaceBefore + punctuatedText + spaceAfter + afterText;
    const cursorPosition = beforeText.length + spaceBefore.length + punctuatedText.length + spaceAfter.length + 1; // +1 for TipTap's 1-based positioning

    // Update beforeCursorRef for next transcript
    const newBeforeCursor = beforeText + spaceBefore + punctuatedText + spaceAfter;

    return {
      finalContent,
      cursorPosition,
      newBeforeCursor,
      insertedText: spaceBefore + punctuatedText + spaceAfter
    };
  }

  // Test with scenario 1
  console.log("Scenario 1 with code logic:");
  let beforeText = "";
  let afterText = "a b c d e";

  let result1 = simulateCodeLogic(beforeText, afterText, "testing");
  console.log(`After "testing": "${result1.newBeforeCursor}|${afterText}"`);
  console.log(`Cursor position: ${result1.cursorPosition}`);

  let result2 = simulateCodeLogic(result1.newBeforeCursor, afterText, "testing again");
  console.log(`After "testing again": "${result2.newBeforeCursor}|${afterText}"`);
  console.log(`Cursor position: ${result2.cursorPosition}`);

  // Test with scenario 2
  console.log("\nScenario 2 with code logic:");
  beforeText = "a ";
  afterText = "b c d e";

  result1 = simulateCodeLogic(beforeText, afterText, "testing");
  console.log(`After "testing": "${result1.newBeforeCursor}|${afterText}"`);
  console.log(`Cursor position: ${result1.cursorPosition}`);

  result2 = simulateCodeLogic(result1.newBeforeCursor, afterText, "testing again");
  console.log(`After "testing again": "${result2.newBeforeCursor}|${afterText}"`);
  console.log(`Cursor position: ${result2.cursorPosition}`);
}

// New function to test 3-4 consecutive transcripts
function testMultipleTranscripts() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING 4 CONSECUTIVE TRANSCRIPTS");
  console.log("=".repeat(60));

  function simulateCodeLogic(beforeText, afterText, punctuatedText) {
    const spaceBefore = "";
    const spaceAfter = " ";

    const finalContent = beforeText + spaceBefore + punctuatedText + spaceAfter + afterText;
    const cursorPosition = beforeText.length + spaceBefore.length + punctuatedText.length + spaceAfter.length + 1; // +1 for TipTap's 1-based positioning

    // Update beforeCursorRef for next transcript
    const newBeforeCursor = beforeText + spaceBefore + punctuatedText + spaceAfter;

    return {
      finalContent,
      cursorPosition,
      newBeforeCursor,
      insertedText: spaceBefore + punctuatedText + spaceAfter
    };
  }

  // Test Scenario 1: Cursor in front of "a" in "a b c d e"
  console.log("\nScenario 1: Cursor in front of 'a' in 'a b c d e'");
  console.log("Original: |a b c d e");

  let beforeText = "";
  let afterText = "a b c d e";
  const transcripts = ["testing", "testing again", "third transcript", "fourth transcript"];

  for (let i = 0; i < transcripts.length; i++) {
    const transcript = transcripts[i];
    const result = simulateCodeLogic(beforeText, afterText, transcript);

    console.log(`\n${i + 1}. After "${transcript}":`);
    console.log(`   Result: "${result.newBeforeCursor}|${afterText}"`);
    console.log(`   Cursor position: ${result.cursorPosition}`);
    console.log(`   Before cursor length: ${result.newBeforeCursor.length}`);
    console.log(`   Character at cursor: "${result.newBeforeCursor.charAt(result.newBeforeCursor.length - 1)}"`);

    // Update for next iteration
    beforeText = result.newBeforeCursor;
  }

  // Test Scenario 2: Cursor after "a" in "a b c d e"
  console.log("\n" + "-".repeat(40));
  console.log("Scenario 2: Cursor after 'a' in 'a b c d e'");
  console.log("Original: a |b c d e");

  beforeText = "a ";
  afterText = "b c d e";

  for (let i = 0; i < transcripts.length; i++) {
    const transcript = transcripts[i];
    const result = simulateCodeLogic(beforeText, afterText, transcript);

    console.log(`\n${i + 1}. After "${transcript}":`);
    console.log(`   Result: "${result.newBeforeCursor}|${afterText}"`);
    console.log(`   Cursor position: ${result.cursorPosition}`);
    console.log(`   Before cursor length: ${result.newBeforeCursor.length}`);
    console.log(`   Character at cursor: "${result.newBeforeCursor.charAt(result.newBeforeCursor.length - 1)}"`);

    // Update for next iteration
    beforeText = result.newBeforeCursor;
  }

  // Test Scenario 3: Empty text, cursor at beginning
  console.log("\n" + "-".repeat(40));
  console.log("Scenario 3: Empty text, cursor at beginning");
  console.log("Original: |");

  beforeText = "";
  afterText = "";

  for (let i = 0; i < transcripts.length; i++) {
    const transcript = transcripts[i];
    const result = simulateCodeLogic(beforeText, afterText, transcript);

    console.log(`\n${i + 1}. After "${transcript}":`);
    console.log(`   Result: "${result.newBeforeCursor}|${afterText}"`);
    console.log(`   Cursor position: ${result.cursorPosition}`);
    console.log(`   Before cursor length: ${result.newBeforeCursor.length}`);
    console.log(`   Character at cursor: "${result.newBeforeCursor.charAt(result.newBeforeCursor.length - 1)}"`);

    // Update for next iteration
    beforeText = result.newBeforeCursor;
  }
}

// New function to test the ACTUAL issue from the logs
function testActualIssue() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING THE ACTUAL ISSUE FROM LOGS");
  console.log("=".repeat(60));

  function simulateCodeLogicWithCleaning(beforeText, afterText, punctuatedText) {
    // Clean the punctuated text - remove leading/trailing spaces
    const cleanPunctuatedText = punctuatedText.trim();

    const spaceBefore = "";
    const spaceAfter = " ";

    const finalContent = beforeText + spaceBefore + cleanPunctuatedText + spaceAfter + afterText;
    const cursorPosition = beforeText.length + spaceBefore.length + cleanPunctuatedText.length + spaceAfter.length + 1; // +1 for TipTap's 1-based positioning

    // Update beforeCursorRef for next transcript
    const newBeforeCursor = beforeText + spaceBefore + cleanPunctuatedText + spaceAfter;

    return {
      finalContent,
      cursorPosition,
      newBeforeCursor,
      insertedText: spaceBefore + cleanPunctuatedText + spaceAfter,
      cleanText: cleanPunctuatedText
    };
  }

  // Test the actual scenario from logs
  console.log("\nActual scenario from logs:");
  console.log("Original: |a b c d e");

  let beforeText = "";
  let afterText = "a b c d e";

  // Simulate the transcripts with leading spaces (as they come from speech recognition)
  const transcriptsWithSpaces = ["testing", " test", " testing", " testing again"];

  for (let i = 0; i < transcriptsWithSpaces.length; i++) {
    const transcript = transcriptsWithSpaces[i];
    const result = simulateCodeLogicWithCleaning(beforeText, afterText, transcript);

    console.log(`\n${i + 1}. After "${transcript}" (cleaned to "${result.cleanText}"):`);
    console.log(`   Result: "${result.newBeforeCursor}|${afterText}"`);
    console.log(`   Cursor position: ${result.cursorPosition}`);
    console.log(`   Before cursor length: ${result.newBeforeCursor.length}`);
    console.log(`   Character at cursor: "${result.newBeforeCursor.charAt(result.newBeforeCursor.length - 1)}"`);

    // Update for next iteration
    beforeText = result.newBeforeCursor;
  }

  // Test what happens if we DON'T clean the text
  console.log("\n" + "-".repeat(40));
  console.log("What happens WITHOUT cleaning (the bug):");

  function simulateCodeLogicWithoutCleaning(beforeText, afterText, punctuatedText) {
    const spaceBefore = "";
    const spaceAfter = " ";

    const finalContent = beforeText + spaceBefore + punctuatedText + spaceAfter + afterText;
    const cursorPosition = beforeText.length + spaceBefore.length + punctuatedText.length + spaceAfter.length + 1;

    const newBeforeCursor = beforeText + spaceBefore + punctuatedText + spaceAfter;

    return {
      finalContent,
      cursorPosition,
      newBeforeCursor,
      insertedText: spaceBefore + punctuatedText + spaceAfter
    };
  }

  beforeText = "";
  afterText = "a b c d e";

  for (let i = 0; i < transcriptsWithSpaces.length; i++) {
    const transcript = transcriptsWithSpaces[i];
    const result = simulateCodeLogicWithoutCleaning(beforeText, afterText, transcript);

    console.log(`\n${i + 1}. After "${transcript}":`);
    console.log(`   Result: "${result.newBeforeCursor}|${afterText}"`);
    console.log(`   Cursor position: ${result.cursorPosition}`);
    console.log(`   Before cursor length: ${result.newBeforeCursor.length}`);
    console.log(`   Character at cursor: "${result.newBeforeCursor.charAt(result.newBeforeCursor.length - 1)}"`);

    // Update for next iteration
    beforeText = result.newBeforeCursor;
  }
}

// New function to test cursor position updates
function testCursorPositionUpdates() {
  console.log("\n" + "=".repeat(60));
  console.log("TESTING CURSOR POSITION UPDATES");
  console.log("=".repeat(60));

  // Simulate the scenario where user clicks to move cursor during dictation
  console.log("\nScenario: User clicks to move cursor during dictation");

  let beforeText = "testing ";
  let afterText = "a b c d e";

  console.log(`Initial state: "${beforeText}|${afterText}"`);
  console.log(`Before cursor: "${beforeText}"`);
  console.log(`After cursor: "${afterText}"`);

  // Simulate user clicking to move cursor to position 3 (after "tes")
  console.log("\nUser clicks to move cursor to position 3 (after 'tes'):");
  beforeText = "tes";
  afterText = "ting a b c d e";

  console.log(`After cursor move: "${beforeText}|${afterText}"`);
  console.log(`Before cursor: "${beforeText}"`);
  console.log(`After cursor: "${afterText}"`);

  // Now simulate a new transcript
  const newTranscript = "new text";
  const spaceBefore = "";
  const spaceAfter = " ";

  const finalContent = beforeText + spaceBefore + newTranscript + spaceAfter + afterText;
  const cursorPosition = beforeText.length + spaceBefore.length + newTranscript.length + spaceAfter.length + 1;
  const newBeforeCursor = beforeText + spaceBefore + newTranscript + spaceAfter;

  console.log(`\nAfter new transcript "${newTranscript}":`);
  console.log(`Result: "${newBeforeCursor}|${afterText}"`);
  console.log(`Cursor position: ${cursorPosition}`);
  console.log(`Before cursor length: ${newBeforeCursor.length}`);
  console.log(`Character at cursor: "${newBeforeCursor.charAt(newBeforeCursor.length - 1)}"`);

  // Test multiple cursor movements
  console.log("\n" + "-".repeat(40));
  console.log("Testing multiple cursor movements:");

  let currentBefore = "testing ";
  let currentAfter = "a b c d e";

  const movements = [
    { name: "Click after 't'", before: "t", after: "esting a b c d e" },
    { name: "Click after 'e'", before: "te", after: "sting a b c d e" },
    { name: "Click after 's'", before: "tes", after: "ting a b c d e" },
    { name: "Click after 't'", before: "test", after: "ing a b c d e" }
  ];

  for (let i = 0; i < movements.length; i++) {
    const movement = movements[i];
    currentBefore = movement.before;
    currentAfter = movement.after;

    console.log(`\n${i + 1}. ${movement.name}:`);
    console.log(`   Cursor position: "${currentBefore}|${currentAfter}"`);
    console.log(`   Before cursor: "${currentBefore}"`);
    console.log(`   After cursor: "${currentAfter}"`);
  }
}

// Run all tests
testCursorPositioning();
testMultipleTranscripts();
testActualIssue();
testCursorPositionUpdates(); 