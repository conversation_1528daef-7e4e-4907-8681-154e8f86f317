import React, { useState, useRef, useEffect, useCallback } from "react";
import { Box, Tooltip } from "@mui/material";
import "./styles/global.css";
import { useAtomValue } from "jotai";
import { selectedDataAtom } from "./context/SelectedDataContext";
import { PatientInfoAtom } from "../context/NoteContext";
import { DEFAULT_TIME_RANGE_SECONDS } from "./Constants";
import MacroConfig from "./components/MacroConfig";
import VersionHistoryDialog from "./components/VersionHistoryDialog";
import { SpeechRecognitionButton } from "./components/SpeechRecognition";
import { useSpeechRecognition } from "./hooks/useSpeechRecognition";
import { StyledContainer, EditorContainer } from "./components/styles";
import { ReportListPanel } from "./components/ReportListPanel";
import { ChatPanel } from "./components/ChatPanel";
import { PatientPanel } from "./components/PatientPanel";
import { EditorToolbar } from "./components/EditorToolbar";
import { useReportGeneration } from "./hooks/useReportGeneration";
import { useEditorSetup } from "./hooks/useEditorSetup";
import { useEditorInitialization } from "./hooks/useEditorInitialization";
import { useEditorEventListeners } from "./hooks/useEditorEventListeners";
import { usePanelState } from "./hooks/usePanelState";
import { useReportState } from "./hooks/useReportState";
import { useChatState } from "./hooks/useChatState";
import { useMacroState } from "./hooks/useMacroState";
import { usePrintReport } from "./hooks/usePrintReport";
import { SplitEditor } from "./components/SplitEditor";
import { SaveReportDialog } from "./components/SaveReportDialog";

interface TimeRange {
  startTime: number;
  endTime: number;
  hours?: number;
}

const Editor = () => {
  // Refs
  const editorRef = useRef<any>(null);
  const reportEditorRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const editorContentRef = useRef<HTMLDivElement>(null);
  const patientInfo = useAtomValue(PatientInfoAtom);

  // Tooltip state
  const [tooltipOpen, setTooltipOpen] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<{
    left: number;
    top: number;
  } | null>(null);

  // Time range state
  const [timeRange, setTimeRange] = useState<TimeRange>(() => {
    const now = new Date();
    // Round to the current minute (set seconds to 0)
    const endTime = Math.floor(new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes(), 0).getTime() / 1000);
    // Exactly 3 months ago to the minute
    const startTime = endTime - DEFAULT_TIME_RANGE_SECONDS; // Last 3 months
    return { startTime, endTime };
  });

  // Get selectedData from atom
  const selectedData = useAtomValue(selectedDataAtom);

  // Initialize editors
  const dictationEditor = useEditorSetup(editorRef, typingTimeoutRef);
  const reportEditor = useEditorSetup(reportEditorRef, typingTimeoutRef);

  // Initialize hooks
  const {
    isPanelOpen,
    panelWidth,
    isPatientPanelOpen,
    patientPanelWidth,
    isReportListOpen,
    handleTogglePanel,
    handleTogglePatientPanel,
    handleToggleReportList,
    handleDragStart,
    handlePatientPanelDragStart,
  } = usePanelState();

  const {
    reportList,
    isLoadingReports,
    selectedReport,
    isSaveAsDialogOpen,
    isVersionHistoryOpen,
    isRefreshing,
    setSelectedReport,
    setIsSaveAsDialogOpen,
    setIsVersionHistoryOpen,
    fetchReportList,
    refreshSelectedReport,
    handleSaveReport,
    handleDeleteReport,
    handleSaveReportAs,
    handleVersionHistory,
    handleVersionSelect,
  } = useReportState({
    campus: patientInfo?.campus || "",
    dbpath: patientInfo?.dbpath || "",
    reportEditor,
  });

  const {
    messages,
    inputValue,
    isProcessing,
    handleInputChange,
    handleClearHistory,
    handleSend,
  } = useChatState();

  const { macroData, handleRunMacro } = useMacroState();

  // Speech recognition for dictation
  const {
    isListening,
    startListening,
    stopListening,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition({
    editor: dictationEditor,
    macroData: macroData,
    onProcessTranscript: (text) => {
      if (dictationEditor) {
        dictationEditor.commands.insertContent(text);
      }
    },
  });

  // Report generation functionality
  const { handleGenerateReport, reportContent, isGeneratingReport } =
    useReportGeneration(
      dictationEditor,
      reportEditor,
      selectedData,
      (loading) => {
      }
    );

  // Initialize editor functionality
  useEditorInitialization(dictationEditor);

  // Set up editor event listeners
  useEditorEventListeners(dictationEditor, setTooltipOpen, editorContentRef);

  // Print functionality
  const { handlePrint } = usePrintReport(reportEditor);

  // Update report editor when report content changes
  useEffect(() => {
    if (reportContent && reportEditor) {
      reportEditor.commands.setContent(reportContent);
    }
  }, [reportContent, reportEditor]);

  // Wrapper function for report generation
  const handleGenerateReportWrapper = useCallback(async () => {
    handleGenerateReport();
  }, [handleGenerateReport]);

  // Process commands (e.g., macro insertion)
  const processCommand = async (text: string): Promise<boolean> => {
    // Macro insertion logic
    const macro = macroData.find(
      (m) =>
        m.type === "text" &&
        m.name.trim().toLowerCase() === text.trim().toLowerCase()
    );
    if (macro && dictationEditor) {
      dictationEditor.chain().focus().insertContent(macro.description).run();
      return true; // Mark as handled
    }

    return false; // Ensure a return statement
  };

  // Handle keyboard events
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Escape") {
      setTooltipOpen(false);
    }
  };

  // Toggle speech recognition recording
  const handleToggleRecording = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Clear editor content and stop recording
  const handleClearContent = () => {
    if (dictationEditor) {
      dictationEditor.commands.clearContent();
      dictationEditor.commands.focus();
    }
    stopListening();
  };

  return (
    <StyledContainer className="reports-container">
      <EditorToolbar
        isReportListOpen={isReportListOpen}
        isPanelOpen={isPanelOpen}
        isPatientPanelOpen={isPatientPanelOpen}
        onToggleReportList={handleToggleReportList}
        onTogglePanel={handleTogglePanel}
        onTogglePatientPanel={handleTogglePatientPanel}
      />
      <Box
        sx={{
          marginLeft: "64px",
          width: "calc(100% - 64px)",
          transition: "margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          paddingTop: "16px", // Match the left toolbar padding
        }}
      >
        <EditorContainer ref={containerRef}>
          {isPanelOpen && (
            <ChatPanel
              panelWidth={panelWidth}
              messages={messages}
              inputValue={inputValue}
              isProcessing={isProcessing}
              editor={dictationEditor}
              onDragStart={handleDragStart}
              onInputChange={handleInputChange}
              onInputKeyDown={handleKeyDown}
              onSend={() => handleSend(processCommand)}
              onClearHistory={handleClearHistory}
            />
          )}

          {isReportListOpen && (
            <ReportListPanel
              panelWidth={panelWidth}
              isLoadingReports={isLoadingReports}
              isRefreshing={isRefreshing}
              reportList={reportList}
              selectedReport={selectedReport}
              onDragStart={handleDragStart}
              onRefresh={fetchReportList}
              onReportClick={(report) => {
                setSelectedReport(report);
                if (reportEditor && report.content) {
                  reportEditor.commands.setContent(report.content);
                }
              }}
              campus={patientInfo?.campus || ""}
              patID={
                patientInfo?.dbpath
                  ? patientInfo.dbpath.substring(
                    patientInfo.dbpath.lastIndexOf("p") + 1
                  )
                  : ""
              }
            />
          )}

          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              flex: 1,
              height: "calc(100vh - 16px)", // Account for top padding
              minWidth: "45%",
              overflow: "hidden",
              maxHeight: "calc(100vh - 16px)", // Account for top padding
            }}
          >
            <SplitEditor
              dictationEditor={dictationEditor}
              reportEditor={reportEditor}
              onPrint={handlePrint}
              onToggleRecording={handleToggleRecording}
              onRunMacro={() => handleRunMacro(dictationEditor)}
              onClear={handleClearContent}
              onGenerateReport={handleGenerateReportWrapper}
              onSave={() => {
                handleSaveReport(reportEditor);
              }}
              onSaveAs={() => setIsSaveAsDialogOpen(true)}
              isEditorEmpty={!dictationEditor?.getText().trim()}
              isRecording={isListening}
              isReportSelected={!!selectedReport}
              isGeneratingReport={isGeneratingReport}
              selectedReport={selectedReport}
              onRefresh={fetchReportList}
              refreshSelectedReport={refreshSelectedReport}
              campus={patientInfo?.campus || ""}
              patID={
                patientInfo?.dbpath
                  ? patientInfo.dbpath.substring(
                    patientInfo.dbpath.lastIndexOf("p") + 1
                  )
                  : ""
              }
            />
          </Box>

          {isPatientPanelOpen && (
            <PatientPanel
              panelWidth={patientPanelWidth}
              editor={reportEditor}
              onDragStart={handlePatientPanelDragStart}
            />
          )}
        </EditorContainer>
      </Box>

      {tooltipPosition && (
        <Tooltip
          open={tooltipOpen}
          title="Grammar check complete."
          placement="top"
          PopperProps={{
            anchorEl: {
              getBoundingClientRect: () => ({
                top: tooltipPosition.top,
                left: tooltipPosition.left,
                right: tooltipPosition.left,
                bottom: tooltipPosition.top,
                width: 0,
                height: 0,
                x: tooltipPosition.left,
                y: tooltipPosition.top,
                toJSON: () => { },
              }),
            },
          }}
        >
          <div
            style={{
              position: "absolute",
              left: tooltipPosition.left,
              top: tooltipPosition.top,
            }}
          />
        </Tooltip>
      )}

      <SaveReportDialog
        open={isSaveAsDialogOpen}
        onClose={() => setIsSaveAsDialogOpen(false)}
        onSave={handleSaveReportAs}
      />

      {/* Macro Dialog */}
      <MacroConfig />

      {selectedReport && (
        <VersionHistoryDialog
          open={isVersionHistoryOpen}
          onClose={() => setIsVersionHistoryOpen(false)}
          campus={patientInfo?.campus || ""}
          patID={
            patientInfo?.dbpath
              ? patientInfo.dbpath.substring(
                patientInfo.dbpath.lastIndexOf("p") + 1
              )
              : ""
          }
          noteID={selectedReport.noteID}
          onVersionSelect={(version) =>
            handleVersionSelect(version, reportEditor)
          }
        />
      )}
      <SpeechRecognitionButton
        isListening={isListening}
        onToggleRecording={handleToggleRecording}
        browserSupportsSpeechRecognition={browserSupportsSpeechRecognition}
      />
    </StyledContainer>
  );
};

export default Editor;
