/* Import Roboto font */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');


/* Ensure no CSS is hiding the cursor */
.reports-container * {
  caret-color: inherit !important;
}

/* Force cursor visibility in all editors */
.reports-container .ProseMirror,
.reports-container .ProseMirror-focused,
.reports-container [contenteditable],
.reports-container [contenteditable]:focus {
  caret-color: #ff0000 !important;
  outline: none !important;
  min-height: 20px !important;
  width: 100% !important;
}

/* Ensure cursor is visible even when editor is empty */
.reports-container .ProseMirror:empty::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 2px;
  height: 1.2em;
  background-color: #ff0000;
  animation: blink 1s infinite;
  z-index: 10;
}

/* Ensure editor content area is properly sized */
.reports-container .ProseMirror {
  min-width: 100% !important;
  box-sizing: border-box !important;
  padding: 0.5rem !important;
}

/* Specific styles for report editor to ensure cursor visibility */
.reports-container .report-editor .ProseMirror {
  background-color: #ffffff !important;
  border: 1px solid #ddd !important;
  min-height: 100px !important;
  padding: 1rem !important;
  margin: 0 !important;
}

.reports-container .report-editor .ProseMirror:focus {
  border-color: #007cba !important;
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2) !important;
}

/* Force cursor visibility specifically in report editor */
.reports-container .report-editor .ProseMirror {
  caret-color: #000000 !important;
}

.reports-container .report-editor .ProseMirror:focus {
  caret-color: #ff0000 !important;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Adjust specific elements while maintaining the base size */
.reports-container h1 {
  font-size: 1.5em; /* 24px */
  font-weight: 500;
}

.reports-container h2 {
  font-size: 1.25em; /* 20px */
  font-weight: 500;
}

.reports-container h3 {
  font-size: 1.125em; /* 18px */
  font-weight: 500;
}

.reports-container small {
  font-size: 0.875em; /* 14px */
}

/* Tiptap editor styles */
.tiptap {
  /* color: #000000; */
  
  div, p {
    font-size: 15px;
    font-family: 'Roboto', sans-serif;
    line-height: 1.5;
    margin-left: 0px;
    /* color: #000000; */
  }

  & ul, & ol {
    li p {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
      line-height: 1.5em;
      /* color: #000000; */
    }
  }
}

/* Report header table specific styling */
.report-header-table {
  border: 1px solid #ddd !important;
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 8px 0 !important;
}

.report-header-table td {
  border: 0 !important;
  padding: 2px 8px !important;
  margin: 0 !important;
  vertical-align: top !important;
  line-height: 1.2 !important;
}

/* Ensure other tables keep their borders */
.tiptap table:not(.report-header-table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.tiptap table:not(.report-header-table) td,
.tiptap table:not(.report-header-table) th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}


