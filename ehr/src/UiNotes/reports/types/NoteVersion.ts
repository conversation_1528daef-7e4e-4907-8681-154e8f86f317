export interface SignatureInfo {
  signedBy?: number;
  signedByName?: string;
  signedAt?: string;
  comment?: string;
}

export interface NoteReport {
  noteID: string;
  title: string;
  type: string;
  content: string;
  creationTime: string;
  modifiedTime: string;
  deleted?: boolean;
  required_signature?: boolean;
  signed?: boolean;
  signatures?: SignatureInfo[];
  lastSignedAt?: string;
  lastSignedBy?: string;
}

export interface SignNoteRequest {
  note_type: string;
  comment: string;
  staffid?: number;
  ccitoken?: string;
}

export interface SignNoteResponse {
  message: string;
}

export interface NoteVersion {
  commit_hash: string;
  author?: string;
  updatedBy?: string;
  date: string;
  message: string;
  content: string;
}
