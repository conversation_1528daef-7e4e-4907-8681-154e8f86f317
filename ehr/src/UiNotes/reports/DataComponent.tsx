import React, { useState } from "react";

const DataComponent = () => {
  const cci = window.Cci || window.cci;
  const [loading, setLoading] = useState(false);
  const [reportList, setReportList] = useState<any[]>([]);
  const [patID, setPatID] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!patID) return;
    setLoading(true);
    try {
      const response = await fetch(`${cci?.cfg?.baseUrl || ''}/index.php/Api/getData`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + localStorage.getItem("webtoken"),
        },
        body: JSON.stringify({
          patID,
          tableName: "reports",
          fields: ["*"],
          conditions: [
            {
              field: "patID",
              operator: "=",
              value: patID,
            },
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        setReportList(data.data);
      } else {
        setError(data.message || "Failed to fetch reports");
      }
    } catch (error) {
      console.error("Error fetching reports:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (reportData: any) => {
    if (!patID) return;
    try {
      const response = await fetch(
        `${cci?.cfg?.baseUrl || ''}/index.php/Api/saveData`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + localStorage.getItem("webtoken"),
          },
          body: JSON.stringify({
            patID,
            tableName: "reports",
            data: reportData,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        fetchData();
      } else {
        setError(data.message || "Failed to save report");
      }
    } catch (error) {
      console.error("Error saving report:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    }
  };

  const handleDelete = async (reportID: string) => {
    if (!patID) return;
    try {
      const response = await fetch(
        `${cci?.cfg?.baseUrl || ''}/index.php/Api/deleteData`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + localStorage.getItem("webtoken"),
          },
          body: JSON.stringify({
            patID,
            tableName: "reports",
            conditions: [
              {
                field: "reportID",
                operator: "=",
                value: reportID,
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        fetchData();
      } else {
        setError(data.message || "Failed to delete report");
      }
    } catch (error) {
      console.error("Error deleting report:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    }
  };

  return (
    <div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div>{/* Render your report list here */}</div>
      )}
      {error && <div>Error: {error}</div>}
    </div>
  );
};

export default DataComponent;
