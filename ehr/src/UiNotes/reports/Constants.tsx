/**
 * Constants used throughout the reports module
 */

export const CRITICAL_PLACEHOLDER_INSTRUCTIONS = `
CRITICAL INSTRUCTIONS FOR <PERSON><PERSON><PERSON><PERSON>OLDER SECTIONS:
- PROBLEMS SECTION: Use ONLY the [PROBLEMS_SECTION] placeholder. Do NOT generate any content for this section.
- <PERSON>OME MEDICATIONS SECTION: Use ONLY the [HOME_MEDICATIONS_SECTION] placeholder. Do NOT generate any content for this section.
- VITAL SIGNS SECTION: Use ONLY the [VITAL_SIGNS_SECTION] placeholder. Do NOT generate any content for this section.
- LAB RESULTS SECTION: Use ONLY the [LAB_RESULTS_SECTION] placeholder. Do NOT generate any content for this section.
- DIAGNOSIS SECTION: Use ONLY the [DIAGNOSIS_SECTION] placeholder. Do NOT generate any content for this section.
- REVIEW OF SYSTEMS SECTION: Use ONLY the [REVIEW_OF_SYSTEMS_SECTION] placeholder. Do NOT generate any content for this section.
- EXAM SECTION: Use ONLY the [EXAM_SECTION] placeholder. Do NOT generate any content for this section.
- PROCEDURES SECTION: Use ONLY the [PROCEDURES_SECTION] placeholder. Do NOT generate any content for this section.
- These sections are COMPLETELY ISOLATED from lab results, vital signs, or any other clinical data.
- If lab results are missing or empty, this does NOT give you permission to modify Subjective, Review of Systems, or Exam sections.

PLACEHOLDER RULES:
1. You MUST include ALL sections shown in the REQUIRED OUTPUT FORMAT below
2. You MUST use the [PROBLEMS_SECTION], [HOME_MEDICATIONS_SECTION], [VITAL_SIGNS_SECTION], [LAB_RESULTS_SECTION], [DIAGNOSIS_SECTION], [REVIEW_OF_SYSTEMS_SECTION], [EXAM_SECTION], and [PROCEDURES_SECTION] placeholders exactly as shown below
3. DO NOT generate any actual data content for placeholders - ONLY use the placeholders
4. DO NOT replace the placeholders with real data - leave them as [PLACEHOLDER_NAME]
5. If you replace placeholders with data, you are FAILING the task

SECTION-SPECIFIC RULES:
6. CHIEF COMPLAINT: Generate content directly based on dictation - do NOT use placeholders
7. SUBJECTIVE: Use the dictation text EXACTLY as provided - do NOT modify, expand, or alter
8. ASSESSMENT & PLAN: Generate content directly with three subsections (Current Diagnosis, Suggested Diagnosis, Suggested Plan)
   - Current Diagnosis: Use the [DIAGNOSIS_SECTION] placeholder - do NOT generate actual diagnosis text
   - Suggested Diagnosis: Generate 2-3 most likely differential diagnoses based on available clinical data - do NOT modify other sections if lab results are missing
   - Suggested Plan: Generate treatment plan including immediate interventions, diagnostic workup, treatment modifications, monitoring, and follow-up
   - Use paragraph format, NOT bullet points or lists
   - DO NOT include any instruction text, placeholder text, or AI internal dialogue in the output
   - CRITICAL: Do NOT modify Subjective, Review of Systems, Exam, or other sections based on missing lab results`;

export const DISCHARGE_CRITICAL_INSTRUCTIONS = `
CRITICAL INSTRUCTIONS FOR DISCHARGE SUMMARY:
1. Generate a comprehensive discharge summary based on the provided patient data
2. Include ALL sections shown in the REQUIRED OUTPUT FORMAT below
3. Use the [PROCEDURES_SECTION], [MEDICATIONS_SECTION] placeholders exactly as shown
5. Focus on discharge-specific information: reason for hospitalization, procedures performed, medications at discharge
6. Ensure all medical terminology is accurate and appropriate for discharge documentation
7. Present information in a clear, professional format suitable for patient handoff and continuity of care
8. CRITICAL: For Procedures and Medications sections, ONLY use the placeholders - do NOT generate any actual procedure or medication text
9. CRITICAL: Do NOT add any bullet points, lists, or additional text after the placeholders
10. CRITICAL: The placeholders will be replaced with formatted data automatically`;

// Base URL for API endpoints
export const API_BASE_URL = `${cci.cfg.baseUrl}/index.php/Api`;

// Default patient ID for testing and development
export const DEFAULT_PATIENT_ID = "M8042925";

// Utility function to get dynamic patient ID from Cci or fallback to default
export const getDynamicPatientId = (): string => {
  try {
    const globalCci = (window as any).Cci;
    const dynamicId = globalCci?.Patient?._mrn;

    if (dynamicId) {
      return dynamicId;
    }

    return DEFAULT_PATIENT_ID;
  } catch (error) {
    console.warn(`[PatientID] Error accessing Cci.Patient._mrn:`, error);
    return DEFAULT_PATIENT_ID;
  }
};

// Test function to debug gender detection - can be called from browser console
export const debugPatientGender = () => {
  console.log("=== PATIENT GENDER DEBUG ===");
  const globalCci = (window as any).Cci;
  console.log("globalCci:", globalCci);
  console.log("globalCci.Patient:", globalCci?.Patient);
  console.log("globalCci.Patient._gender:", globalCci?.Patient?._gender);
  console.log("globalCci.Patient.gender:", globalCci?.Patient?.gender);
  console.log("globalCci.Patient.sex:", globalCci?.Patient?.sex);
  console.log("Detected gender:", getPatientGender());
  console.log("=== END DEBUG ===");
};

// Utility function to get patient gender from Cci or fallback to default
export const getPatientGender = (): "M" | "F" | null => {
  try {
    const globalCci = (window as any).Cci;

    // Check for gender in various possible field names
    const gender =
      globalCci?.Patient?._gender ||
      globalCci?.Patient?.gender ||
      globalCci?.Patient?.sex;

    if (gender) {
      const normalizedGender = gender.toString().toUpperCase();

      if (normalizedGender === "M" || normalizedGender === "MALE") {
        return "M";
      } else if (normalizedGender === "F" || normalizedGender === "FEMALE") {
        return "F";
      } else {
        return null;
      }
    } else {
      return null;
    }
  } catch (error) {
    console.warn(`[PatientGender] Error accessing Cci.Patient.gender:`, error);
    return null; // Return null on error
  }
};

// Feature flags
export const SHOW_TEST_VITALS = false; // Set to true to show TestVitals component for testing purposes

// Default time range settings (in seconds)
export const DEFAULT_TIME_RANGE_DAYS = 30; // 3 months (90 days)
export const DEFAULT_TIME_RANGE_SECONDS =
  DEFAULT_TIME_RANGE_DAYS * 24 * 60 * 60; // Convert days to seconds
export const DEFAULT_TIME_RANGE_MILLISECONDS =
  DEFAULT_TIME_RANGE_SECONDS * 1000; // Convert seconds to milliseconds

export const REASON_FOR_HOSPITALIZATION = `The patient was admitted on June 4, 2025, after developing a diffuse pruritic rash, facial swelling, and left-sided facial pain 48 hours after starting
amoxicillin for a tooth abscess. Initial vital signs showed fever (100.2°F) and decreased blood pressure (100/60 mmHg). Physical exam revealed a maculopapular rash, facial
edema, and mandibular tenderness with trismus. Dental X-ray confirmed a left subperiosteal abscess and periapical abscess at tooth 19. Labs showed leukocytosis and eosinophilia.
Amoxicillin was discontinued, and she was started on benadryl 25mg PO daily, omeprazole 20 mg daily, normal saline IV at 100 mL/hr, and clindamycin 600 mg
IV every 6 hours. On Day 1, rash and edema improved, OMFS performed incision and drainage of the abscess, and symptoms continued to improve.
By the end of the day the rash and edema resolved, the surgical site was healing, and vital signs were normal. Cultures sensitivity are pending.
The patient was stable for discharge.`;

export const SUBJECTIVE = `The patient is a 24-year-old previously healthy female who presents to the ED with a 4-day history of progressively worsening sore throat, odynophagia, and muffled "hot potato" voice. She initially developed symptoms consistent with viral pharyngitis—low-grade fever, fatigue, and mild throat pain—but over the past 48 hours, she has experienced escalating unilateral oropharyngeal pain localized to the left side, accompanied by difficulty swallowing both solids and liquids, trismus, and pooling of saliva.
She denies rash, cough, recent dental procedures, or international travel. Patient has no known history of recurrent tonsillitis, recent antibiotic use, or immunocompromise. Patient does endorse subjective fevers and chills over the past two nights and reports increasing difficulty in articulating speech due to discomfort and restricted jaw opening.
On physical exam, the patient appears mildly toxic and febrile to 101.8°F. Oropharyngeal exam reveals marked left peritonsillar swelling with uvular deviation to the right, erythema of the left tonsillar pillar, and a fluctuant area suggestive of purulence. Trismus is present, with limited jaw opening, and cervical lymphadenopathy is noted in the left submandibular chain. Airway is patent at this time, but close monitoring is warranted.`;

export const HOME_MEDICATIONS_SECTION_PROMPT = `<div class="report-section"><strong>Home Medications</strong>
   [HOME_MEDICATIONS_SECTION]</div>`;

export const LAB_RESULTS_SECTION_PROMPT = `<div class="report-section"><strong>Lab Results</strong><br>
   [LAB_RESULTS_SECTION]</div>`;

export const SUBJECTIVE_SECTION_PROMPT = `<div class="report-section"><strong>Subjective</strong><br>
[Use the provided dictation text exactly as provided - do not modify, expand, or alter the content]</div>`;

export const REASON_SECTION_PROMPT = `<div class="report-section"><strong>Reason for Hospitalization</strong><br>
   [Generate the reason for hospitalization based on the patient data and diagnosis information]</div>`;

export const DIAGNOSIS_SECTION_PROMPT = `<div class="report-section"><strong>Current Diagnoses</strong>
   [DIAGNOSIS_SECTION]</div>`;

export const ASSESSMENT_PLAN_SECTION_PROMPT = `<div class="report-section"><strong>Assessment & Plan</strong>

${DIAGNOSIS_SECTION_PROMPT}

<div class="report-section"><strong>Suggested Diagnosis:</strong><br>
[Explain the most likely diagnosis based on clinical presentation and available data (vital signs, review of systems, and any lab results if available)]</div>

<div class="report-section"><strong>Suggested Plan:</strong><br>
[Generate comprehensive treatment plan including immediate interventions, diagnostic workup, treatment modifications, monitoring, and follow-up]</div></div>`;

export const DISCHARGE_ASSESSMENT_PLAN_SECTION_PROMPT = `<div class="report-section"><strong>Assessment & Plan</strong>

<div class="report-section"><strong>Suggested Diagnosis:</strong><br>
[Generate AI-suggested diagnoses based on clinical presentation]</div>

<div class="report-section"><strong>Suggested Plan:</strong><br>
[Generate AI-suggested treatment plan based on clinical presentation]</div></div>`;

export const PATIENT_CONDITION_PROMPT = `<div class="report-section"><strong>Patient's Condition at Discharge</strong><br></div>`;

export const DISCHARGE_INSTRUCTIONS_PROMPT = `<div class="report-section"><strong>Discharge Instructions</strong><br></div>`;

export const FOLLOW_UP_PROMPT = `<div class="report-section"><strong>Follow-up Plan</strong><br></div>`;

export const CHIEF_COMPLAINT_INSTRUCTIONS = `- FORBIDDEN: Do not paste, quote, or reference the actual dictation text
- Analyze the dictation content to understand the patient's main problem
- Generate the chief complaint as if it's coming from the patient's own words
- Use patient-friendly language that a layperson would use to describe their symptoms
- The chief complaint should be 1-2 sentences maximum
- Example: If dictation says "Patient came in complaining of chest pain for 3 days", write "Chest pain for 3 days" or "I have chest pain that started 3 days ago"`;

export const CHIEF_COMPLAINT_SECTION = `<div class="report-section">
<strong>Chief Complaint</strong><br>
   [Generate the chief complaint based on the dictation content]
</div>`;

export const IMAGING_RESULTS_SECTION = `<div class="report-section"><strong>Imaging Results</strong><br>
  None</div>`;

export const MEDICAL_DECISION_MAKING_SECTION = `<div class="report-section"><strong>Medical Decision Making</strong><br>

<strong>Differential Diagnosis:</strong>
[Generate 3-5 most likely diagnoses based on clinical presentation and available data (vital signs, review of systems, and any lab results if available), ordered by likelihood. Format as a bullet list:
<ul>
<li>[Diagnosis 1] - [brief rationale]</li>
<li>[Diagnosis 2] - [brief rationale]</li>
<li>[Diagnosis 3] - [brief rationale]</li>
</ul>]</div>`;

export const REVIEW_OF_SYSTEMS_SECTION = `<div class="report-section"><strong>Review of Systems</strong>
   [REVIEW_OF_SYSTEMS_SECTION]</div>`;

export const EXAM_SECTION = `<div class="report-section"><strong>Exam</strong><br>
   [EXAM_SECTION]</div>`;

export const MEDICATIONS_SECTION_PROMPT = `<div class="report-section"><strong>Current Medications</strong>
   [MEDICATIONS_SECTION]</div>`;

export const MEDREC_SECTION_PROMPT = `<div class="report-section"><strong>Medications at Discharge</strong>
   [MEDREC_SECTION]</div>`;

export const VITAL_SIGNS_SECTION_PROMPT = `<div class="report-section"><strong>Vital Signs</strong>
   [VITAL_SIGNS_SECTION]</div>`;

export const PROBLEMS_SECTION_PROMPT = `<div class="report-section"><strong>Problems</strong>
   [PROBLEMS_SECTION]</div>`;

export const PROCEDURES_SECTION_PROMPT = `<div class="report-section"><strong>Procedures Performed</strong>
   [PROCEDURES_SECTION]</div>`;

// Report Types
export const REPORT_TYPES = {
  ADMISSION_HISTORY_PHYSICAL: "Admission History & Physical",
  PROGRESS_NOTE: "Progress Note",
  ED_PROGRESS_NOTE: "ED Progress Note",
  DISCHARGE_SUMMARY: "Discharge Summary",
  CONSULT_NOTE: "Consult Note",
} as const;

export type ReportType = keyof typeof REPORT_TYPES;

// Default Report Type
export const DEFAULT_REPORT_TYPE: ReportType = "ED_PROGRESS_NOTE";

// Data Components for each Report Type
export const REPORT_TYPE_COMPONENTS: Record<ReportType, string[]> = {
  DISCHARGE_SUMMARY: ["Demographics", "Diagnosis", "Procedures", "MedRec"],
  ADMISSION_HISTORY_PHYSICAL: [
    "Demographics",
    "Problems",
    "Diagnosis",
    "HomeMeds",
    "LabResults",
    "Vitals",
  ],
  PROGRESS_NOTE: [
    "Demographics",
    "Diagnosis",
    "Problems",
    "LabResults",
    "Medications",
    "Vitals",
  ],
  ED_PROGRESS_NOTE: [
    "Demographics",
    "Diagnosis",
    "Problems",
    "LabResults",
    "HomeMeds",
    "Vitals",
    "ReviewOfSystems",
    "Exam",
    "Procedures",
  ],
  CONSULT_NOTE: [
    "Demographics",
    "Problems",
    "Diagnosis",
    "HomeMeds",
    "LabResults",
    "Vitals",
  ],
};

export const DISCHARGE_SUMMARY_PROMPT = `
You are a medical report generator. Your task is to generate a Discharge Summary Note.

${DISCHARGE_CRITICAL_INSTRUCTIONS}

REQUIRED OUTPUT FORMAT:

${REASON_SECTION_PROMPT}

${PROCEDURES_SECTION_PROMPT}

${PATIENT_CONDITION_PROMPT}

${MEDREC_SECTION_PROMPT}

${DISCHARGE_INSTRUCTIONS_PROMPT}

${FOLLOW_UP_PROMPT}`;

export const ADMISSION_HISTORY_PHYSICAL_PROMPT = `
  You are a medical report generator.Your task is to generate an Admission History & Physical Note.

  ${CRITICAL_PLACEHOLDER_INSTRUCTIONS}

  CHIEF COMPLAINT INSTRUCTIONS:
  ${CHIEF_COMPLAINT_INSTRUCTIONS}

  REQUIRED OUTPUT FORMAT:

  ${CHIEF_COMPLAINT_SECTION}

  ${SUBJECTIVE_SECTION_PROMPT}

  ${PROBLEMS_SECTION_PROMPT}

  ${HOME_MEDICATIONS_SECTION_PROMPT}

  ${EXAM_SECTION}

  ${VITAL_SIGNS_SECTION_PROMPT}

  ${LAB_RESULTS_SECTION_PROMPT}

  ${IMAGING_RESULTS_SECTION}

  ${ASSESSMENT_PLAN_SECTION_PROMPT}`;

export const ED_PROGRESS_NOTE_PROMPT = `
  You are a medical report generator. Your task is to generate an ED Progress Note.

  ${CRITICAL_PLACEHOLDER_INSTRUCTIONS}

  CHIEF COMPLAINT INSTRUCTIONS:
  ${CHIEF_COMPLAINT_INSTRUCTIONS}

  REQUIRED OUTPUT FORMAT:

  ${CHIEF_COMPLAINT_SECTION}

  ${SUBJECTIVE_SECTION_PROMPT}

  ${PROBLEMS_SECTION_PROMPT}

  ${HOME_MEDICATIONS_SECTION_PROMPT}

  ${REVIEW_OF_SYSTEMS_SECTION}

  ${EXAM_SECTION}

  ${PROCEDURES_SECTION_PROMPT}

  ${VITAL_SIGNS_SECTION_PROMPT}

  ${LAB_RESULTS_SECTION_PROMPT}

  ${IMAGING_RESULTS_SECTION}

  ${MEDICAL_DECISION_MAKING_SECTION}

  ${ASSESSMENT_PLAN_SECTION_PROMPT}`;

export const PROGRESS_NOTE_PROMPT = `
  You are a medical report generator. Your task is to generate a Progress Note.

  ${CRITICAL_PLACEHOLDER_INSTRUCTIONS}

  CHIEF COMPLAINT INSTRUCTIONS:
  ${CHIEF_COMPLAINT_INSTRUCTIONS}

  REQUIRED OUTPUT FORMAT:

  ${CHIEF_COMPLAINT_SECTION}

  ${SUBJECTIVE_SECTION_PROMPT}

  ${PROBLEMS_SECTION_PROMPT}

  ${MEDICATIONS_SECTION_PROMPT}

  ${EXAM_SECTION}

  ${VITAL_SIGNS_SECTION_PROMPT}

  ${LAB_RESULTS_SECTION_PROMPT}

  ${IMAGING_RESULTS_SECTION}

  ${ASSESSMENT_PLAN_SECTION_PROMPT}`;

export const CONSULT_NOTE_PROMPT = `
  You are a medical report generator.Your task is to generate a Consult Note.

  ${CRITICAL_PLACEHOLDER_INSTRUCTIONS}

  CHIEF COMPLAINT INSTRUCTIONS:
  ${CHIEF_COMPLAINT_INSTRUCTIONS}

  REQUIRED OUTPUT FORMAT:

  ${CHIEF_COMPLAINT_SECTION}

  ${SUBJECTIVE_SECTION_PROMPT}

  ${PROBLEMS_SECTION_PROMPT}

  ${HOME_MEDICATIONS_SECTION_PROMPT}

  ${EXAM_SECTION}

  ${VITAL_SIGNS_SECTION_PROMPT}

  ${LAB_RESULTS_SECTION_PROMPT}

  ${IMAGING_RESULTS_SECTION}

  ${ASSESSMENT_PLAN_SECTION_PROMPT}`;
