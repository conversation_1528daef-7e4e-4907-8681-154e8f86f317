import { Grid } from "@mui/material";

import { Allotment } from "allotment";
import "allotment/dist/style.css";

import { useAtom, useAtomValue } from "jotai";

import { MaximizeSummaryWidthAtom } from "@cci-monorepo/UiNotes/context/NoteContext";
import Summary from "./Summary";
import NoteSummary from "./NoteSummary";
import type { ScreenComponentProps } from "@cci-monorepo/common/utils/screenUtil";
import { Editor } from "./reports/components/Editor";
import ReportEditor from "./editorchat/Dictation/ReportEditor";

export default function ProgressNote(props: ScreenComponentProps) {
  const [maximizeSummaryWidth, setMaximizeSummaryWidth] = useAtom(
    MaximizeSummaryWidthAtom
  );

  // @ts-ignore
  const noteSummaryComponent = () => {
    return (
      <Allotment
        vertical={false}
        onVisibleChange={(_index, value) => {
          setMaximizeSummaryWidth(value);
        }}
      >
        <Allotment.Pane minSize={0} visible={!maximizeSummaryWidth}>
          <Grid container flexDirection={"row"} spacing={1}>
            <Grid item xs>
              <NoteSummary />
            </Grid>
          </Grid>
        </Allotment.Pane>
      </Allotment>
    );
  };
  const noteSummaryAndSummaryComponent = () => {
    return (
      <Allotment
        vertical={false}
        onVisibleChange={(_index, value) => {
          setMaximizeSummaryWidth(value);
        }}
      >
        <Allotment.Pane minSize={0} visible={!maximizeSummaryWidth}>
          <Grid container flexDirection={"row"} spacing={1}>
            <Grid item xs>
              {/* <NoteSummary /> */}
            </Grid>
          </Grid>
        </Allotment.Pane>
        <Allotment.Pane snap>
          <Grid container flexDirection={"row"}>
            <Grid
              item
              sx={{ width: "8px", backgroundColor: "lightgray" }}
            ></Grid>
            <Grid item sx={{ backgroundColor: "lightgray" }} xs={12}>
              <Summary />
            </Grid>
          </Grid>
        </Allotment.Pane>
      </Allotment>
    );
  };

  const UIComponent = () => {
    return noteSummaryAndSummaryComponent();
  };

  return UIComponent();
  // return (
  //   <Editor
  //   />
  // );
}
