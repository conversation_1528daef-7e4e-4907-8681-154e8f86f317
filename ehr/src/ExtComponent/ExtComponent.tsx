import { ThemeProvider, styled } from "@mui/material/styles";

import EhrTheme from "../theme/theme";
import ExtCmp, { ExtCmpProps } from "./components/ExtCmp";
import type { ScreenComponentProps } from "@cci-monorepo/common/utils/screenUtil";

const Root = styled("main")(({ theme }) => ({
  position: "relative",
  height: "100%",
  width: "100%",
}));

export default function ExtComponent(props: ScreenComponentProps) {
  return (
    <ThemeProvider theme={EhrTheme}>
      <Root>
        <ExtCmp {...(props.screenParams as unknown as ExtCmpProps)} />
      </Root>
    </ThemeProvider>
  );
}
