import React from "react";
import ExtCmpDiv from "./ExtCmpDiv";

export interface ExtCmpProps {
  style: any;
  widgetId: string;
  conf: any;
  applyCfg: any;
  deleteWidget?: (id: string) => void;
  collapseWidget?: (id: string) => void;
  expandWidget?: (id: string) => void;
}

export default class ExtCmp extends React.Component<ExtCmpProps> {
  private div: React.RefObject<HTMLDivElement>;
  private ctn: any;
  private cmp: any;

  constructor(props: any) {
    super(props);
    this.div = React.createRef();
    this.ctn = undefined;
    this.cmp = undefined;
  }

  render(): React.ReactNode {
    return (
      <ExtCmpDiv
        divref={this.div}
        style={this.props.style}
        onResize={(width: any, height: any) => this.ctn?.setSize(width, height)}
      />
    );
  }

  private createCmp(): void {
    if (!this.ctn?.rendered) return;
    this.ctn.removeAll();
    this.cmp = Cps.util.WidgetFactory.createWidget(
      this.props.conf,
      this.props.applyCfg,
      this.ctn
    );

    if (this.cmp.rendered && this.cmp.rParams && this.cmp.rParams.args) {
      this.cmp.refresh?.();
    }

    this.cmp.on("reactcloseclicked", this.handleCloseClicked);
    this.cmp.on("expand", this.handleExpand);
    this.cmp.on("collapse", this.handleCollapse);
  }

  componentDidMount(): void {
    if (this.ctn) return;
    this.ctn = Ext.create({
      xtype: "container",
      name: "container-closeable",
      renderTo: this.div.current,
      layout: "fit",
    });
    if (this.props.conf) {
      this.createCmp();
    }
  }

  componentDidUpdate(prevProps: Readonly<ExtCmpProps>): void {
    if (prevProps.conf !== this.props.conf) {
      if (this.props.conf) this.createCmp();
      else this.ctn.removeAll();
    }
  }

  handleCloseClicked = () => {
    this.props.deleteWidget?.(this.props.widgetId);
  };

  handleExpand = () => {
    this.props.expandWidget?.(this.props.widgetId);
  };

  handleCollapse = () => {
    this.props.collapseWidget?.(this.props.widgetId);
  };

  componentWillUnmount(): void {
    if (!this.ctn?.rendered) return;
    this.ctn.destroy();
    this.ctn = undefined;
  }

  refresh(): void {
    this.cmp?.refresh?.();
  }
}
