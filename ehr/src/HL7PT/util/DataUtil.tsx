import { boolean } from "zod";
import { EditableTextField } from "../componets/EditableTextField";

const attributeColumn = [
  {
    field: "ID",
    headerName: "ID",
    width: 50,
  },
  {
    field: "segment_num",
    headerName: "Segment Num",
    width: 100,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "seq",
    headerName: "Seq",
    width: 50,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "len",
    headerName: "Len",
    width: 50,
    editable: true,
  },
  {
    field: "dt",
    headerName: "Dt",
    width: 100,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "opt",
    headerName: "Opt",
    width: 100,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "rp_num",
    headerName: "Rp Num",
    width: 100,
    editable: true,
  },
  {
    field: "tbl_num",
    headerName: "Tbl Num",
    width: 100,
    editable: true,
  },
  {
    field: "item_num",
    headerName: "Item Num",
    width: 100,
    editable: true,
  },
  {
    field: "element_name",
    headerName: "Element Name",
    flex: 1,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
];

const messageTypeColumn = [
  {
    field: "ID",
    headerName: "ID",
    width: 50,
  },
  {
    field: "message_type",
    headerName: "Message Type",
    width: 100,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "description",
    headerName: "Description",
    width: 500,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "chapter",
    headerName: "Chapter",
    flex: 1,
    editable: true,
  },
];

const eventTypeColumn = [
  {
    field: "ID",
    headerName: "ID",
    width: 50,
  },
  {
    field: "event_type",
    headerName: "Event Type",
    width: 100,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "description",
    headerName: "Description",
    flex: 1,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
];

const dataTypeColumn = [
  {
    field: "ID",
    headerName: "ID",
    width: 50,
  },
  {
    field: "data_type",
    headerName: "Data Type",
    width: 100,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "data_type_name",
    headerName: "Data Type Name",
    width: 300,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "len",
    headerName: "Len",
    width: 50,
    editable: true,
  },
  {
    field: "category",
    headerName: "Category",
    width: 200,
    editable: true,
  },
  {
    field: "comment",
    headerName: "Comment",
    flex: 1,
    editable: true,
  },
];

const segmentNameColumn = [
  {
    field: "ID",
    headerName: "ID",
    width: 50,
  },
  {
    field: "segment_name",
    headerName: "Segment Name",
    width: 120,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
  {
    field: "description",
    headerName: "Description",
    flex: 1,
    editable: true,
    renderEditCell: (params: any) => <EditableTextField {...params} />,
  },
];

const attributeRequired = ["segment_num", "seq", "dt", "opt", "element_name"];
const messageTypeRequired = ["message_type", "description"];
const eventTypeRequired = ["event_type", "description"];
const dataTypeRequired = ["data_type", "data_type_name"];
const segmentNameRequired = ["segment_name", "description"];

export const dbList = [
  {
    id: "1",
    label: "hl7Attribute",
    db: "hl7_attributes",
    column: attributeColumn,
    required: attributeRequired,
  },
  {
    id: "2",
    label: "hl7MessageType",
    db: "hl7_message_types",
    column: messageTypeColumn,
    required: messageTypeRequired,
  },
  {
    id: "3",
    label: "hl7EventType",
    db: "hl7_event_types",
    column: eventTypeColumn,
    required: eventTypeRequired,
  },
  {
    id: "4",
    label: "hl7DataType",
    db: "hl7_data_types",
    column: dataTypeColumn,
    required: dataTypeRequired,
  },
  {
    id: "5",
    label: "hl7SegmentName",
    db: "hl7_segment_names",
    column: segmentNameColumn,
    required: segmentNameRequired,
  },
];

export const getEmptyRow = (label: string) => {
  let emptyRow = {};
  switch (label) {
    case "hl7Attribute":
      emptyRow = {
        segment_num: "T",
        seq: "0",
        len: "0",
        dt: "T",
        opt: "T",
        rp_num: "T",
        tbl_num: "0",
        item_num: "0",
        element_name: "T",
      };
      break;
    case "hl7MessageType":
      emptyRow = {
        message_type: "T",
        description: "T",
        chapter: "0",
      };
      break;
    case "hl7EventType":
      emptyRow = {
        event_type: "T",
        description: "T",
      };
      break;
    case "hl7DataType":
      emptyRow = {
        data_type: "T",
        data_type_name: "T",
        len: "0",
        category: "T",
        comment: "T",
      };
      break;
    case "hl7SegmentName":
      emptyRow = {
        segment_name: "T",
        description: "T",
      };
      break;
  }
  return emptyRow;
};

export const checkEmpty = (row: any, required: string[]) => {
  let empty = false;
  for (let i = 0; i < required.length; i++) {
    if (!row[required[i]]) {
      empty = true;
      break;
    }
  }
  return empty;
};
