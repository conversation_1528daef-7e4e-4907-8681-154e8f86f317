import { atom } from "jotai";

// Global Error Dialog
export const openErrorDialogAtom = atom({
  message: "",
  open: false,
});

export const drawerExpandedAtom = atom(true);

export const flipDrawerAtom = atom(null, (get, set) => {
  let drawerExpanded = get(drawerExpandedAtom);
  set(drawerExpandedAtom, !drawerExpanded);
});

export const showDeleteDialogAtom = atom(false);

export const selectedRowAtom = atom<any>({});

export const selectedDbIdAtom = atom<string>("1");

export const rowsAtom = atom<any[]>([]);

export const rowModesModelAtom = atom({});

export const needSaveAtom = atom<{
  value: boolean;
  action: string;
  row: any;
}>({ value: false, action: "", row: null });
