import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Box, Drawer, IconButton, Typography } from "@mui/material";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import DBList from "./DBList";
import {
  drawerExpandedAtom,
  flipDrawer<PERSON>tom,
} from "@cci-monorepo/HL7PT/context/HL7PTAtoms";

export default function HL7PTDrawer() {
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const flipDrawer = useSetAtom(flipDrawerAtom);

  return (
    <Drawer
      sx={{
        width: "235px",
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          height: "100%",
          flexShrink: 0,
          whiteSpace: "nowrap",
          backgroundColor: "#d2e0f2",
          width: "235px",
          boxSizing: "border-box",
          overflow: "hidden",
          padding: "5px",
          position: "absolute",
        },
      }}
      variant="persistent"
      anchor="left"
      open={drawerExpanded}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          backgroundColor: "#d2e0f2",
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          border: "1px solid #98c0f4",
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "25px",
            display: "flex",
            paddingLeft: "5px",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography
            sx={{
              color: "#15428b",
              font: "bold 11px tahoma,arial,verdana,sans-serif",
            }}
          >
            DBView
          </Typography>
          <IconButton onClick={flipDrawer}>
            <KeyboardDoubleArrowLeftIcon sx={{ fontSize: "15px" }} />
          </IconButton>
        </Box>
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <DBList />
        </Box>
      </Box>
    </Drawer>
  );
}
