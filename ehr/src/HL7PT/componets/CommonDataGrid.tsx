/**
 * CommonDataGrid.tsx
 *
 * @author: jfsys
 * @description Common Data Grid
 */
import {
  DataGridPro,
  GridColumnMenu,
  GridColumnMenuProps,
} from "@mui/x-data-grid-pro";
import Stack from "@mui/material/Stack";
import Box from "@mui/material/Box";
import { Divider, Menu, MenuItem, styled } from "@mui/material";
import { useState } from "react";

const StyledDataGrid = styled(DataGridPro)({
  background: "white",
  "& .MuiDataGrid-column:focus-within, .MuiDataGrid-cell:focus-within": {
    outline: "none",
  },
  "& .MuiDataGrid-columnHeaders": {
    background: "#f9f9f9",
    borderRadius: "0px",
    fontSize: "13px",
    fontFamily: "Roboto",
    color: "black",
    border: "1px",
    "& .MuiDataGrid-columnHeaderTitle": {
      fontWeight: "500",
    },
    "& .MuiDataGrid-columnHeader": {
      borderRight: "1px solid #C2C2C2",
      borderBottom: "1px solid #C2C2C2",
      "&:focus": {
        outline: "none",
      },
      "&:hover": {
        outline: "none",
      },
    },
  },
  "& .MuiDataGrid-cell": {
    fontSize: "12px",
    fontWeight: "400",
    color: "black",
    "&:focus": {
      outline: "none",
    },
  },
  "&.MuiDataGrid-root .MuiDataGrid-row": {
    "&.Mui-hovered, &.Mui-selected": {
      backgroundColor: "#d2e0f2",
    },
  },
});

const CustomColumnMenu = (props: GridColumnMenuProps) => {
  return (
    <GridColumnMenu
      {...props}
      slots={{
        // Hide `columnMenuPinningItem`
        columnMenuPinningItem: null,
        columnMenuFilterItem: null,
      }}
    />
  );
};

export const NoRowsOverlay = (props: any) => {
  return (
    <Stack
      sx={{
        backgroundColor: "#F2F2F2",
        height: props.height ? props.height : "100%",
        borderBottomLeftRadius: "8px",
        borderBottomRightRadius: "8px",
      }}
      alignItems="center"
      justifyContent="center"
      fontSize={24}
      lineHeight="24px"
      color="#AAA9A9"
    >
      <span style={{ fontFamily: "Raleway" }}>{props.msg}</span>
    </Stack>
  );
};

export const CommonDataGrid = (props: any) => {
  const {
    rows,
    columns,
    contextMenuItems,
    noDataMsg,
    slots,
    selectedRow,
    setSelectedRow,
    setRowSelectionModel,
    rowModesModel,
    setRowModesModel,
    onRowUpdate,
    ...other
  } = props;
  const noRows = rows?.length === 0 ? true : false;
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);

  // Trigger when a row is right mouse clicked
  const handleContextMenuClick = (event: React.MouseEvent) => {
    event.preventDefault();
    const rowId = event.currentTarget.getAttribute("data-id");

    const selectingRow = rows.find((item: any) => {
      return item.id === rowId;
    });

    if (setSelectedRow) {
      setSelectedRow(selectingRow);
    }
    setRowSelectionModel([selectingRow.id]);
    setContextMenu(
      contextMenu === null
        ? { mouseX: event.clientX - 2, mouseY: event.clientY - 4 }
        : null
    );
  };

  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  const handleItemMenuAction = (handler: any) => {
    if (handler && selectedRow) {
      handler(selectedRow);
    }
    handleContextMenuClose();
  };

  return (
    <Box sx={{ height: noRows ? 150 : "auto", width: "100%" }}>
      <StyledDataGrid
        hideFooter
        rowHeight={25}
        rows={rows}
        columns={columns}
        columnHeaderHeight={25}
        rowModesModel={rowModesModel}
        setRowModesModel={setRowModesModel}
        onRowSelectionModelChange={setRowSelectionModel}
        processRowUpdate={(newRow: any, oldRow: any) => {
          if (onRowUpdate) {
            let row = onRowUpdate(newRow, oldRow);
            if (!row.id) {
              return { ...row, _action: "delete" };
            } else {
              return row;
            }
          }
          return newRow;
        }}
        onProcessRowUpdateError={(error: any) => {
          console.log(error.message);
        }}
        slots={{
          noRowsOverlay: NoRowsOverlay,
          columnMenu: CustomColumnMenu,
          ...slots,
        }}
        slotProps={{
          noRowsOverlay: { msg: noDataMsg },
          row: {
            onContextMenu: handleContextMenuClick,
            style: { cursor: "context-menu" },
          },
        }}
        {...other}
      />
      {contextMenuItems ? (
        <Menu
          open={contextMenu !== null}
          onClose={handleContextMenuClose}
          anchorReference="anchorPosition"
          anchorPosition={
            contextMenu !== null
              ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
              : undefined
          }
          slotProps={{
            root: {
              onContextMenu: (e: any) => {
                e.preventDefault();
                handleContextMenuClose();
              },
            },
          }}
        >
          {contextMenuItems.map((item: any, idx: any) => {
            if (item.type === "divider")
              return (
                <Divider
                  key={"divider_" + idx}
                  sx={{
                    borderBottomWidth: "1px",
                    margin: "0px 16px 0px 16px !important",
                  }}
                />
              );
            return (
              <MenuItem
                key={"menu_item_" + idx}
                disabled={item.disabled || false}
                onClick={(e: React.MouseEvent<HTMLElement>) => {
                  e.stopPropagation();
                  handleItemMenuAction(item.handler);
                }}
              >
                {item.label}
              </MenuItem>
            );
          })}
        </Menu>
      ) : null}
    </Box>
  );
};
