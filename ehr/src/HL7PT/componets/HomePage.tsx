import { styled } from "@mui/material/styles";
import OpenDrawer from "./OpenDrawer";
import { Provider } from "jotai";
import HL7PTDrawer from "./HL7PTDrawer";
import HL7PTGrid from "./HL7PTGrid";

export default function HomePage() {
  const Root = styled("main")(({ theme }) => ({
    position: "relative",
    height: "100%",
    width: "100%",
  }));

  return (
    <Provider>
      <Root>
        <OpenDrawer />
        <HL7PTDrawer />
        <HL7PTGrid />
      </Root>
    </Provider>
  );
}
