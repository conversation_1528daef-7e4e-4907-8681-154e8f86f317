import { Box, Grid, Typography } from "@mui/material";
import { Button, CciDialog } from "@cci/mui-components";
import HelpIcon from "@mui/icons-material/Help";

interface DeleteDialogProps {
  open: boolean;
  setOpen: (status: boolean) => void;
  onDelete: () => void;
}

export const DeleteDialog = (props: DeleteDialogProps) => {
  const { open, setOpen, onDelete } = props;

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = () => {
    setOpen(false);
    onDelete();
  };

  const content = (
    <Box
      sx={{
        padding: "5px",
        display: "flex",
        alignItems: "center",
        marginTop: "15px",
      }}
    >
      <HelpIcon color="primary" sx={{ fontSize: "30px" }} />
      <Typography
        sx={{
          fontFamily: "Roboto",
          fontSize: "14px",
          fontWeight: 500,
          color: "#000000",
          marginLeft: "5px",
        }}
      >
        You really want to delete this Record?
      </Typography>
    </Box>
  );

  const noButton = (
    <Button
      color="secondary"
      variant="contained"
      onClick={handleClose}
      sx={{ marginLeft: "10px" }}
    >
      No
    </Button>
  );

  const yesButton = (
    <Button color="primary" variant="contained" onClick={handleSave}>
      Yes
    </Button>
  );

  const buttons = (
    <Grid
      container
      style={{ margin: "10px 10px 0px 10px" }}
      display="flex"
      spacing={2}
      justifyContent="flex-end"
    >
      {yesButton}
      {noButton}
    </Grid>
  );
  return (
    <CciDialog
      open={open}
      setOpen={setOpen}
      handleClose={handleClose}
      title={"Info"}
      content={content}
      buttons={buttons}
      sx={{
        "& .MuiDialogTitle-root": {
          padding: "16px 24px",
        },
      }}
    />
  );
};
