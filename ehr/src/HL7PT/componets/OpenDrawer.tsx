import { Box, IconButton } from "@mui/material";
import { useAtomValue, useSetAtom } from "jotai";
import { drawerExpandedAtom, flipDrawerAtom } from "../context/HL7PTAtoms";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";

export default function OpenDrawer() {
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const flipDrawer = useSetAtom(flipDrawerAtom);

  return (
    <Box
      sx={{
        visibility: drawerExpanded ? "hidden" : "visible",
        backgroundColor: "#d2e0f2",
        height: "100%",
        width: "30px",
        padding: "5px",
        position: "absolute",
      }}
    >
      <Box
        sx={{
          backgroundColor: "#d2e0f2",
          height: "100%",
          width: "20px",
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          border: "1px solid #98c0f4",
        }}
        onClick={flipDrawer}
      >
        <IconButton>
          <KeyboardDoubleArrowRightIcon sx={{ fontSize: "15px" }} />
        </IconButton>
      </Box>
    </Box>
  );
}
