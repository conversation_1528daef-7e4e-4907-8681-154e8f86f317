import FolderIcon from "@mui/icons-material/Folder";
import FolderOpenIcon from "@mui/icons-material/FolderOpen";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { styled } from "@mui/material/styles";
import { TreeView } from "@mui/x-tree-view/TreeView";
import {
  TreeItem,
  TreeItemProps,
  treeItemClasses,
} from "@mui/x-tree-view/TreeItem";
import React, { ReactNode } from "react";
import { Box } from "@mui/material";
import { useAtom } from "jotai";
import { selectedDbIdAtom } from "../context/HL7PTAtoms";
import { dbList } from "../util/DataUtil";

function CollapseIcon() {
  return <FolderOpenIcon sx={{ color: "#98c0f4", fontSize: 40 }} />;
}

function ExpandIcon() {
  return <FolderIcon sx={{ color: "#98c0f4", fontSize: 40 }} />;
}

function EndIcon() {
  return <InsertDriveFileIcon sx={{ color: "#98c0f4", fontSize: 40 }} />;
}

const toLabelNode = (str: string): ReactNode => {
  return <span style={{ fontSize: "12px", color: "#000" }}>{str}</span>;
};

const CustomTreeItem = React.forwardRef(
  (props: TreeItemProps, ref: React.Ref<HTMLLIElement>) => (
    <TreeItem {...props} ref={ref} />
  )
);

const StyledTreeItem = styled(CustomTreeItem)(({ theme }) => ({
  [`& .${treeItemClasses.iconContainer}`]: {
    "& .close": {
      opacity: 0.3,
    },
  },
  [`& .${treeItemClasses.group}`]: {
    marginLeft: 13,
    paddingLeft: 10,
  },
}));

export default function DBList() {
  const [selectedDbId, setSelectedDbId] = useAtom(selectedDbIdAtom);
  const [expandedItems, setExpandedItems] = React.useState<string[]>(["DB"]);

  const setExpand = () => {
    if (expandedItems.length > 0) {
      setExpandedItems([]);
    } else {
      setExpandedItems(["DB"]);
    }
  };

  const handleClick = (id: string) => {
    setSelectedDbId(id);
  };

  return (
    <Box sx={{ overflow: "auto", border: "0px" }}>
      <TreeView
        aria-label="customized"
        defaultCollapseIcon={<CollapseIcon />}
        defaultExpandIcon={<ExpandIcon />}
        defaultEndIcon={<EndIcon />}
        expanded={expandedItems}
        selected={selectedDbId}
        sx={{ overflowX: "hidden" }}
      >
        <StyledTreeItem
          nodeId={"DB"}
          key={0}
          label={toLabelNode("DB")}
          onClick={setExpand}
        >
          {dbList.map((item: any) => {
            return (
              <StyledTreeItem
                nodeId={item.id}
                key={item.id}
                label={toLabelNode(item.label)}
                onClick={() => handleClick(item.id)}
              />
            );
          })}
        </StyledTreeItem>
      </TreeView>
    </Box>
  );
}
