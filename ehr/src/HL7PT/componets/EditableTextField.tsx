import React, { useState, useRef } from "react";
import TextField from "@mui/material/TextField";
import {
  GridRenderEditCellParams,
  useGridApiContext,
} from "@mui/x-data-grid-pro";
import { styled } from "@mui/material/styles";
import { Box, Popper, Typography } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";

const BaseTextField = styled(TextField)((props: any) => ({
  background: "white",
  border: 0,
  "& .MuiOutlinedInput-input": {
    color: "black",
    overflow: "hidden",
    border: 0,
    fontFamily: "Roboto",
    fontSize: "13px",
    padding: "0px 5px",
    height: "23px",
  },
  "& .MuiOutlinedInput-root": {
    "&.Mui-focused fieldset,  & .MuiOutlinedInput-notchedOutline,  &:hover .MuiOutlinedInput-notchedOutline":
      {
        border: 0,
      },
    "&.Mui-error": {
      backgroundColor: "#ffeeee",
    },
  },
  "& .MuiOutlinedInput-inputMarginDense": {
    padding: 0,
  },
}));

export const EditableTextField = (params: GridRenderEditCellParams) => {
  const { id, value, field } = params;
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [error, setError] = useState(false);
  const textFieldRef = useRef<HTMLInputElement>(null);
  const apiRef = useGridApiContext();

  const handleTextFieldChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setError(event.target.value === "");
    apiRef.current.setEditCellValue({ id, field, value: event.target.value });
  };

  const onMouseEnter = () => {
    if (error) {
      setAnchorEl(textFieldRef.current);
    }
  };

  const onMouseLeave = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <BaseTextField
        sx={{ width: "100%" }}
        inputRef={textFieldRef}
        defaultValue={value}
        onChange={handleTextFieldChange}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        error={error}
      />
      <Popper open={open} anchorEl={anchorEl} placement="bottom-start">
        <Box>
          <Box
            sx={{
              backgroundColor: "#fff",
              padding: "2px 4px",
              border: "2px solid #ff9999",
              borderRadius: "4px",
              boxShadow: "3px 3px 5px rgba(0, 0, 0, 0.5)",
              marginTop: "20px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <ErrorOutlineIcon color="error" />
            <Typography sx={{ marginLeft: "5px" }} variant="body2">
              This field is required
            </Typography>
          </Box>
        </Box>
      </Popper>
    </>
  );
};
