import { useAtom, useAtomValue } from "jotai";
import {
  drawerExpandedAtom,
  rowModesModelAtom,
  rowsAtom,
  selectedDbIdAtom,
  selectedRowAtom,
  showDeleteDialogAtom,
  needSaveAtom,
} from "../context/HL7PTAtoms";
import { Box } from "@mui/material";
import { CommonDataGrid } from "./CommonDataGrid";
import { useState } from "react";
import React from "react";
import { useGridApiRef } from "@mui/x-data-grid-pro";
import { checkEmpty, dbList, getEmptyRow } from "../util/DataUtil";
import { DeleteDialog } from "./DeleteDialog";

export default function HL7PTGrid() {
  const [rows, setRows] = useAtom(rowsAtom);
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const [selectedRow, setSelectedRow] = useAtom(selectedRowAtom);
  const [rowSelectionModel, setRowSelectionModel] = useState([]);
  const [rowModesModel, setRowModesModel] = useAtom(rowModesModelAtom);
  const selectedDbId = useAtomValue(selectedDbIdAtom);
  const [showDeleteDialog, setShowDeleteDialog] = useAtom(showDeleteDialogAtom);
  const [columnData, setColumnData] = useState<any>([]);
  const [loaded, setLoaded] = useState(false);
  const [needSave, setNeedSave] = useAtom(needSaveAtom);
  const [refresh, setRefresh] = useState(false);
  const [selectedDbItem, setSelectedDbItem] = useState<any>(dbList[0]);

  const apiRef = useGridApiRef();
  const margin = drawerExpanded ? "235px" : "30px";

  React.useEffect(() => {
    let selectedDb = dbList.find((db: any) => db.id === selectedDbId);
    if (selectedDb) {
      setSelectedDbItem(selectedDb);
    }
    handleGetData(selectedDb, () => {
      setLoaded(true);
      setSelectedRow({});
      setRowSelectionModel([]);
    });
  }, [setRows, selectedDbId]);

  React.useEffect(() => {
    if (refresh) {
      handleGetData(selectedDbItem, () => {
        setRefresh(false);
      });
    }
  }, [setRows, refresh]);

  React.useEffect(() => {
    if (needSave.value) {
      setNeedSave({ value: false, action: "", row: null });
      let params = {
        hobj:
          needSave.action === "updated"
            ? "hl7pt/updatedata"
            : "hl7pt/insertdata",
        params: {
          table: selectedDbItem.db,
          data: needSave.row,
        },
      };
      Cci.util.CciObject.request(params)
        .then(function (jsondata: any) {
          if (jsondata) {
            if (needSave.action === "added") {
              setRefresh(true);
            }
          }
        })
        .catch(function (Error2: any) {
          console.log("handle data failed: " + Error2);
          setLoaded(true);
        });
    }
  }, [needSave]);

  const handleGetData = (selectedDb: any, handle: () => void) => {
    setColumnData(selectedDb.column);
    if (loaded && !refresh) {
      apiRef.current.scrollToIndexes({ rowIndex: 0 });
    }
    let params = {
      hobj: "hl7pt/getdata",
      params: {
        table: selectedDb.db,
      },
    };
    Cci.util.CciObject.request(params)
      .then(function (jsondata: any) {
        if (jsondata && jsondata.undefined) {
          let data = jsondata.undefined;
          if (data.length > 0) {
            for (let i = 0; i < data.length; i++) {
              data[i].id = data[i].ID;
            }
            setRows(data);
          }
        }
        handle();
      })
      .catch(function (Error2: any) {
        console.log("get data failed: " + Error2);
        handle();
      });
  };

  const handleAddData = () => {
    const id = parseInt(rows[rows.length - 1].id) + 1;
    let emptyRow: any = getEmptyRow(selectedDbItem.label);
    emptyRow.id = id;
    const updatedRowData: any = [...rows, emptyRow];
    setRows(updatedRowData);
    setSelectedRow({});
    setRowSelectionModel([]);
    setTimeout(() => {
      apiRef.current.scrollToIndexes({ rowIndex: rows.length });
      setNeedSave({
        value: true,
        action: "added",
        row: emptyRow,
      });
    }, 1000);
  };

  const handleDeleteData = () => {
    let params = {
      hobj: "hl7pt/deletedata",
      params: {
        table: selectedDbItem.db,
        id: selectedRow.ID,
      },
    };
    Cci.util.CciObject.request(params)
      .then(function (jsondata: any) {
        if (jsondata) {
          setRows(rows.filter((row) => row.id !== selectedRow.id));
          setSelectedRow({});
          setRowSelectionModel([]);
        }
      })
      .catch(function (Error2: any) {
        console.log("delete data failed: " + Error2);
      });
  };

  const getContextMenuItems = () => {
    let contextMenuItems = [
      {
        label: "Add Record",
        handler: handleAddData,
      },
      {
        label: "Delete Record",
        handler: () => {
          setShowDeleteDialog(true);
        },
      },
    ];

    return contextMenuItems;
  };

  const handleRowUpdate = (newRow: any, oldRow: any) => {
    if (checkEmpty(newRow, selectedDbItem.required)) {
      return oldRow;
    } else if (JSON.stringify(newRow) !== JSON.stringify(oldRow)) {
      // Update rowData with updated data from newRow
      const updatedRowData: any = rows.map((entry: any) => {
        if (entry.id === newRow.id) {
          Object.keys(entry).forEach((field) => {
            entry[field] = newRow[field];
          });
        }
        return entry;
      });
      setRows(updatedRowData);
      setNeedSave({
        value: true,
        action: newRow.id.toString().startsWith("new") ? "added" : "updated",
        row: newRow,
      });
    }
    return newRow;
  };

  return (
    loaded && (
      <Box
        sx={{
          left: margin,
          right: "0px",
          top: "0px",
          bottom: "0px",
          backgroundColor: "#d2e0f2",
          padding: "5px",
          border: "1px solid #98c0f4",
          position: "absolute",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <CommonDataGrid
          apiRef={apiRef}
          sx={{
            height: "calc(100vh - 10px)",
            width: `calc(100% - ${margin}px - 10px)`,
            backgroundColor: "#ffffff",
          }}
          rows={rows}
          onRowUpdate={handleRowUpdate}
          rowModesModel={rowModesModel}
          setRowModesModel={setRowModesModel}
          selectedRow={selectedRow}
          setSelectedRow={setSelectedRow}
          rowSelectionModel={rowSelectionModel}
          setRowSelectionModel={setRowSelectionModel}
          columns={columnData}
          contextMenuItems={getContextMenuItems()}
          noDataMsg="No Data"
        />
        <DeleteDialog
          open={showDeleteDialog}
          setOpen={setShowDeleteDialog}
          onDelete={handleDeleteData}
        />
      </Box>
    )
  );
}
