import { styled } from "@mui/material/styles";
import { useEffect } from "react";
import { Box } from "@mui/material";
import { useAtom } from "jotai";
import HL7TreePanel from "./HL7TreePanel";
import HL7HtmlPanel from "./HL7HtmlPanel";
import SearchPanel from "./SearchPanel";
import PropertiesGridPanel from "./PropertiesGridPanel";
import { hl7ResourceAtom } from "../context/HL7Inspector";

export default function HomePage() {
  const Root = styled("main")(({ theme }) => ({
    position: "relative",
    height: "100%",
    width: "100%",
    display: "flex",
    overflow: "hidden",
  }));

  const ContentArea = styled(Box)(({ theme }) => ({
    flex: 1,
    height: "100%",
    display: "flex",
    flexDirection: "column",
    overflow: "hidden",
  }));

  const [, setHl7Resource] = useAtom(hl7ResourceAtom);

  useEffect(() => {
    let types = [
      "hl7_attributes",
      "hl7_message_types",
      "hl7_event_types",
      "hl7_data_types",
      "hl7_segment_names",
    ];
    let promises = types.map((type) => {
      return Cci.util.CciObject.request({
        hobj: "hl7pt/getdata",
        params: {
          table: type,
        },
      });
    });
    Promise.all(promises)
      .then(function (jsondata: any) {
        const result: any = types.reduce(
          (acc, type, index) => ({
            ...acc,
            [type]: jsondata[index].undefined,
          }),
          {}
        );
        let printObj: any = {};
        for (let type in result) {
          printObj[type] = result[type].slice(0, 3);
        }
        setHl7Resource(result);
      })
      .catch(function (Error2: any) {
        console.error("get data failed: " + Error2);
      });
  }, []);

  return (
    <Root>
      <HL7TreePanel />

      <ContentArea>
        <SearchPanel />
        <Box sx={{ flex: 1, overflow: "hidden" }}>
          <HL7HtmlPanel />
        </Box>
        <PropertiesGridPanel />
      </ContentArea>
    </Root>
  );
}
