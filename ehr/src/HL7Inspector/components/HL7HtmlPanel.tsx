import React, { useState, useRef, useCallback } from "react";
import { Box, Typography, Menu, MenuItem } from "@mui/material";
import ContentPasteIcon from "@mui/icons-material/ContentPaste";
import { useAtom } from "jotai";
import {
  parseHL7<PERSON><PERSON>,
  hl7<PERSON>reeAtom,
  selectNodeAtom,
  expandedNodesAtom,
  searchTextAtom,
  searchMatchInfoAtom,
  selectedNodePathAtom,
  htmlSearchHighlightAtom,
  getDelimitersFromTree,
  findDelimitersForNode,
} from "../context/HL7Inspector";
import { HL7Node, HL7Delimiters } from "../types/hl7";
import { findNodeByPath } from "../utils/hl7Parser";
import { styled } from "@mui/material/styles";
import StyledPanel from "./StyledPanel";
import { Icon, IconName } from "../assets/index";

interface ContextMenuPosition {
  mouseX: number;
  mouseY: number;
}

const HL7Container = styled(Box)({
  overflow: "auto",
  height: "100%",
  fontSize: "18px",
  lineHeight: "1.5",
  whiteSpace: "nowrap",
  "& .highlight": {
    backgroundColor: "yellow !important",
    borderRadius: "2px",
  },
  "& .clickable": {
    cursor: "pointer",
    borderRadius: "2px",
    padding: "1px 2px",
    margin: "-1px -2px",
    "&:hover": {
      backgroundColor: "#e3f2fd",
    },
    "&:active": {
      backgroundColor: "#bbdefb",
    },
  },
  "& .separator": {
    color: "blue",
    padding: "0 0.25em",
  },
  "& .msh-field-delimiter": {
    padding: "0 0.25em",
  },
});

const DelimiterInfoPanel = styled(Box)({
  backgroundColor: "#fff",
  margin: "40px 0",
  fontSize: "18px",
  fontStyle: "italic",
  "& .delimiter-label": {
    padding: "0 8px 0 0",
  },
  "& .delimiter-value": {
    color: "blue",
  },
});

const HL7HtmlPanel: React.FC = () => {
  const [contextMenu, setContextMenu] = useState<ContextMenuPosition | null>(
    null
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const [, parseHL7] = useAtom(parseHL7Atom);
  const [hl7Tree] = useAtom(hl7TreeAtom);
  const [, setExpandedNodes] = useAtom(expandedNodesAtom);
  const [, selectNode] = useAtom(selectNodeAtom);
  const [selectedNodePath, setSelectedNodePath] = useAtom(selectedNodePathAtom);
  const [searchText] = useAtom(searchTextAtom);
  const [searchMatchInfo] = useAtom(searchMatchInfoAtom);
  const [htmlSearchHighlight] = useAtom(htmlSearchHighlightAtom);

  // Helper function to get unique delimiters from tree
  const getUniqueDelimiters = useCallback((): HL7Delimiters[] => {
    if (!hl7Tree?.length) return [];
    return getDelimitersFromTree(hl7Tree);
  }, [hl7Tree]);

  // Unified highlight processing function
  const getHighlightInfo = useCallback(
    (
      elementPath: string,
      type: "element" | "separator" | "text" = "element",
      text?: string
    ): {
      shouldHighlight: boolean;
      highlightedText?: React.ReactNode;
    } => {
      let shouldHighlight = false;

      // Priority 1: Check HTML search highlighting (if any search results exist)
      if (htmlSearchHighlight.length > 0) {
        for (const matchInfo of htmlSearchHighlight) {
          if (matchInfo.path === elementPath) {
            shouldHighlight = true;
            break;
          }
          // For messagetype matches, highlight entire message content
          if (matchInfo.elementType === "messagetype") {
            if (
              elementPath.startsWith(matchInfo.path + "/") ||
              elementPath === matchInfo.path
            ) {
              shouldHighlight = true;
              break;
            }
          }
          // For separators, check if within matching path range
          if (type === "separator" && elementPath.startsWith(matchInfo.path)) {
            shouldHighlight = true;
            break;
          }
        }
      }
      // Priority 2: Check selection highlighting (only if no search results)
      else if (selectedNodePath) {
        const selectedNode = findNodeByPath(hl7Tree, selectedNodePath);
        if (selectedNode) {
          // Root message node - highlight everything
          if (selectedNode.type === "message") {
            shouldHighlight = true;
          }
          // Message type node - highlight corresponding message
          else if (selectedNode.type === "messagetype") {
            if (type === "separator") {
              shouldHighlight = elementPath.startsWith(selectedNodePath + "/");
            } else {
              shouldHighlight =
                elementPath === selectedNodePath ||
                elementPath.startsWith(selectedNodePath + "/");
            }
          }
          // Repetition node - highlight the repetition and its children
          else if (selectedNode.type === "repetition") {
            // For repetition nodes, also check if current element is a child of the repetition
            const currentNode = findNodeByPath(hl7Tree, elementPath);
            shouldHighlight =
              elementPath === selectedNodePath ||
              elementPath.startsWith(selectedNodePath + "/") ||
              !!(currentNode && currentNode.parent === selectedNode.id);
          }
          // Other node types - highlight if element path starts with selected path
          else {
            shouldHighlight = elementPath.startsWith(selectedNodePath);
          }
        }
      }

      // Handle text highlighting
      if (type === "text" && text) {
        let isTextMatch = false;

        // Check for search text match
        if (htmlSearchHighlight.length > 0) {
          isTextMatch =
            !!searchText.trim() &&
            !!elementPath &&
            htmlSearchHighlight.some(
              (matchInfo) => matchInfo.path === elementPath
            );
        }

        return {
          shouldHighlight,
          highlightedText: isTextMatch ? (
            <span className="highlight">{text}</span>
          ) : (
            text
          ),
        };
      }

      return { shouldHighlight };
    },
    [htmlSearchHighlight, selectedNodePath, hl7Tree, searchText]
  );

  // Main render function
  const renderHL7Content = () => {
    if (!hl7Tree?.length) {
      return (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            minHeight: "200px",
            padding: "40px 20px",
            textAlign: "center",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: "rgba(0, 0, 0, 0.6)",
              fontWeight: "500",
              marginBottom: "8px",
            }}
          >
            No HL7 Data
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: "rgba(0, 0, 0, 0.4)",
              maxWidth: "300px",
              lineHeight: 1.5,
              fontSize: "14px",
            }}
          >
            Right-click to paste HL7 messages
          </Typography>
        </Box>
      );
    }
    // Find message type nodes from tree
    const findMessageNodes = (nodes: HL7Node[]): HL7Node[] => {
      const messageNodes: HL7Node[] = [];
      for (const node of nodes) {
        if (node.type === "messagetype") {
          messageNodes.push(node);
        }
        if (node.children) {
          messageNodes.push(...findMessageNodes(node.children));
        }
      }
      return messageNodes;
    };

    const messageNodes = findMessageNodes(hl7Tree);

    // Check if root message node is selected (only highlight if no search results)
    const rootNode = hl7Tree[0];
    const isRootSelected =
      htmlSearchHighlight.length === 0 && selectedNodePath === rootNode?.path;

    return (
      <>
        {/* Main HL7 content container with potential root highlighting */}
        <div className={isRootSelected ? "highlight" : ""}>
          {messageNodes.map(renderMessageFromTree)}
        </div>

        {/* Delimiter info */}
        {getUniqueDelimiters().map((delimiters, index) => (
          <div key={`delimiter-${index}`}>
            {renderDelimiterInfo(delimiters)}
          </div>
        ))}
      </>
    );
  };

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    setContextMenu(
      contextMenu === null
        ? { mouseX: event.clientX, mouseY: event.clientY }
        : null
    );
  };

  const handleClose = () => {
    setContextMenu(null);
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (text) {
        const parsedTree = await parseHL7(text);
        if (parsedTree && parsedTree.length > 0) {
          const firstTwoLevelsNodes = parsedTree
            .map((node) =>
              [node.path].concat(
                (node.children || []).map((child) => child.path)
              )
            )
            .flat()
            .filter(Boolean);
          setExpandedNodes(new Set(firstTwoLevelsNodes));
        }
      }
    } catch (error) {
      console.error("Failed to read clipboard contents: ", error);
    }
    handleClose();
  };

  const handleElementClick = useCallback(
    (path: string) => {
      setSelectedNodePath(path);
      selectNode(path, true, true);
    },
    [selectNode, setSelectedNodePath]
  );

  // Render delimiter information panel
  const renderDelimiterInfo = (delimiters: HL7Delimiters) => {
    if (!delimiters) return null;

    const delimiterItems: Array<{
      icon: IconName;
      label: string;
    }> = [
      {
        icon: "plugin",
        label: "Message:",
      },
      {
        icon: "segment",
        label: "Segment:",
      },
      {
        icon: "field",
        label: "Field:",
      },
      {
        icon: "repetition",
        label: "Repetition:",
      },
      {
        icon: "component",
        label: "Component:",
      },
      {
        icon: "subcomponent",
        label: "SubComponent:",
      },
    ];

    const encodingItems = [
      {
        label: "Field:",
        value: delimiters.fieldDelimiter,
      },
      {
        label: "Repetition:",
        value: delimiters.repetitionDelimiter,
      },
      {
        label: "Component:",
        value: delimiters.componentDelimiter,
      },
      {
        label: "SubComponent:",
        value: delimiters.subcomponentDelimiter,
      },
      {
        label: "Escape:",
        value: delimiters.escapeCharacter,
      },
      {
        label: "ENC:",
        value: delimiters.encodingChars,
      },
    ];

    return (
      <DelimiterInfoPanel>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: "16px",
            marginBottom: "8px",
          }}
        >
          {delimiterItems.map((item, index) => (
            <Box
              key={index}
              sx={{
                display: "flex",
                alignItems: "center",
                marginRight: "8px",
              }}
            >
              <span className="delimiter-label">{item.label}</span>
              <Icon name={item.icon} size={22} />
            </Box>
          ))}
        </Box>
        <Box sx={{ display: "flex", flexWrap: "wrap", gap: "16px" }}>
          {encodingItems.map((item, index) => (
            <Box
              key={index}
              sx={{
                display: "flex",
                alignItems: "center",
                marginRight: "8px",
              }}
            >
              <span className="delimiter-label">{item.label}</span>
              <span className="delimiter-value">{item.value}</span>
            </Box>
          ))}
        </Box>
      </DelimiterInfoPanel>
    );
  };

  // Render message from tree node
  const renderMessageFromTree = (messageNode: HL7Node) => {
    if (messageNode.type !== "messagetype") return null;

    const delimiters =
      messageNode.delimiters ||
      findDelimitersForNode(hl7Tree, messageNode.path);
    if (!delimiters) return null;

    const segmentNodes =
      messageNode.children?.filter((node) => node.type === "segment") || [];

    return (
      <Box key={messageNode.id} sx={{ mb: 2 }}>
        {segmentNodes.map((segmentNode) =>
          renderSegmentFromTree(segmentNode, delimiters)
        )}
      </Box>
    );
  };

  // Render segment from tree node
  const renderSegmentFromTree = (
    segmentNode: HL7Node,
    delimiters: HL7Delimiters
  ) => {
    if (segmentNode.type !== "segment") return null;

    const isSelected = getHighlightInfo(
      segmentNode.path,
      "element"
    ).shouldHighlight;
    const segmentClass = isSelected ? "highlight clickable" : "clickable";
    const fieldNodes =
      segmentNode.children?.filter((node) => node.type === "field") || [];
    const isMSH = segmentNode.name === "MSH";

    // Helper function to render separator
    const renderSeparator = () => (
      <span
        className={`separator ${getHighlightInfo(segmentNode.path, "separator").shouldHighlight ? "highlight" : ""}`}
      >
        {delimiters?.fieldDelimiter}
      </span>
    );

    // Helper function to render segment header
    const renderSegmentHeader = () => (
      <span
        className={segmentClass}
        onClick={() => handleElementClick(segmentNode.path)}
      >
        {
          getHighlightInfo(segmentNode.path, "text", segmentNode.name)
            .highlightedText
        }
      </span>
    );

    return (
      <div key={segmentNode.id}>
        {renderSegmentHeader()}

        {/* Add initial separator for non-MSH segments */}
        {!isMSH && renderSeparator()}

        {fieldNodes.map((fieldNode, fieldIndex) => {
          // MSH special logic: no separator before field 0 and 1, separator before field 2+
          // Regular segments: separator before all fields except the first
          const needsSeparator = isMSH ? fieldIndex > 1 : fieldIndex > 0;

          // Special handling for MSH-1 (field delimiter)
          const isMSHFieldDelimiter = isMSH && fieldIndex === 0;

          return (
            <React.Fragment key={fieldNode.id}>
              {needsSeparator && renderSeparator()}
              {isMSHFieldDelimiter ? (
                <span
                  key={fieldNode.id}
                  className={`msh-field-delimiter ${getHighlightInfo(fieldNode.path, "element").shouldHighlight ? "highlight" : ""} clickable`}
                  onClick={() => handleElementClick(fieldNode.path)}
                >
                  {
                    getHighlightInfo(
                      fieldNode.path,
                      "text",
                      fieldNode.value || ""
                    ).highlightedText
                  }
                </span>
              ) : (
                renderFieldFromTree(fieldNode, delimiters)
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  // Render field from tree node
  const renderFieldFromTree = (
    fieldNode: HL7Node,
    delimiters: HL7Delimiters
  ) => {
    if (fieldNode.type !== "field") return null;

    const isSelected = getHighlightInfo(
      fieldNode.path,
      "element"
    ).shouldHighlight;

    // Check if field has repetitions
    if (
      fieldNode.repeats &&
      fieldNode.children &&
      fieldNode.children.length > 0
    ) {
      // Handle repetitions - find repetition nodes
      const repetitionNodes = fieldNode.children.filter(
        (child) => child.type === "repetition"
      );

      return (
        <span key={fieldNode.id}>
          {repetitionNodes.map((repNode, idx) => (
            <React.Fragment key={repNode.id}>
              {idx > 0 && (
                <span
                  className={`separator ${getHighlightInfo(fieldNode.path, "separator").shouldHighlight ? "highlight" : ""}`}
                >
                  {delimiters?.repetitionDelimiter}
                </span>
              )}
              {renderRepetitionFromTree(repNode, delimiters)}
            </React.Fragment>
          ))}
        </span>
      );
    }

    // Check if field has components
    const componentNodes =
      fieldNode.children?.filter((node) => node.type === "component") || [];

    if (
      componentNodes.length <= 1 &&
      (!componentNodes[0] || !componentNodes[0].children?.length)
    ) {
      // Simple field
      const fieldClass = isSelected ? "highlight clickable" : "clickable";

      return (
        <span
          key={fieldNode.id}
          className={fieldClass}
          onClick={() => handleElementClick(fieldNode.path)}
        >
          {
            getHighlightInfo(fieldNode.path, "text", fieldNode.value || "")
              .highlightedText
          }
        </span>
      );
    }

    // Field with multiple components
    return (
      <span key={fieldNode.id}>
        {componentNodes.map((componentNode, componentIndex) => (
          <React.Fragment key={componentNode.id}>
            {componentIndex > 0 && (
              <span
                className={`separator ${getHighlightInfo(fieldNode.path, "separator").shouldHighlight ? "highlight" : ""}`}
              >
                {delimiters?.componentDelimiter}
              </span>
            )}
            {renderComponentFromTree(componentNode, delimiters)}
          </React.Fragment>
        ))}
      </span>
    );
  };

  // Render repetition from tree node
  const renderRepetitionFromTree = (
    repetitionNode: HL7Node,
    delimiters: HL7Delimiters
  ) => {
    const componentNodes =
      repetitionNode.children?.filter((node) => node.type === "component") ||
      [];

    return (
      <span
        key={repetitionNode.id}
        onClick={(e) => {
          // Only handle click if it's on the background (not on a component)
          if (e.target === e.currentTarget) {
            handleElementClick(repetitionNode.path);
          }
        }}
      >
        {componentNodes.map((componentNode, componentIndex) => (
          <React.Fragment key={componentNode.id}>
            {componentIndex > 0 && (
              <span
                className={`separator ${getHighlightInfo(repetitionNode.path, "separator").shouldHighlight ? "highlight" : ""}`}
              >
                {delimiters?.componentDelimiter}
              </span>
            )}
            {renderComponentFromTree(componentNode, delimiters)}
          </React.Fragment>
        ))}
      </span>
    );
  };

  // Render component from tree node
  const renderComponentFromTree = (
    componentNode: HL7Node,
    delimiters: HL7Delimiters
  ) => {
    if (componentNode.type !== "component") return null;

    const isSelected = getHighlightInfo(
      componentNode.path,
      "element"
    ).shouldHighlight;
    const subComponentNodes =
      componentNode.children?.filter((node) => node.type === "subcomponent") ||
      [];

    // Handle subcomponents
    if (subComponentNodes.length > 1) {
      return (
        <span key={componentNode.id}>
          {subComponentNodes.map((subComponentNode, subIndex) => (
            <React.Fragment key={subComponentNode.id}>
              {subIndex > 0 && (
                <span
                  className={`separator ${getHighlightInfo(componentNode.path, "separator").shouldHighlight ? "highlight" : ""}`}
                >
                  {delimiters?.subcomponentDelimiter}
                </span>
              )}
              {renderSubComponentFromTree(subComponentNode)}
            </React.Fragment>
          ))}
        </span>
      );
    }

    // Simple component
    const isEmpty = !componentNode.value || componentNode.value.trim() === "";
    const componentClass = isSelected ? "highlight clickable" : "clickable";

    return (
      <span
        key={componentNode.id}
        className={componentClass}
        onClick={() => handleElementClick(componentNode.path)}
      >
        {
          getHighlightInfo(
            componentNode.path,
            "text",
            componentNode.value || ""
          ).highlightedText
        }
      </span>
    );
  };

  // Render subcomponent from tree node
  const renderSubComponentFromTree = (subComponentNode: HL7Node) => {
    if (subComponentNode.type !== "subcomponent") return null;

    const isSelected = getHighlightInfo(
      subComponentNode.path,
      "element"
    ).shouldHighlight;
    const subComponentClass = isSelected ? "highlight clickable" : "clickable";

    return (
      <span
        key={subComponentNode.id}
        className={subComponentClass}
        onClick={() => handleElementClick(subComponentNode.path)}
      >
        {
          getHighlightInfo(
            subComponentNode.path,
            "text",
            subComponentNode.value || ""
          ).highlightedText
        }
      </span>
    );
  };

  return (
    <StyledPanel title="HL7 HTML" sx={{ height: "100%" }}>
      <HL7Container ref={containerRef} onContextMenu={handleContextMenu}>
        {renderHL7Content()}
      </HL7Container>
      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={handlePaste}>
          <ContentPasteIcon fontSize="small" sx={{ mr: 1 }} />
          Paste HL7 Message
        </MenuItem>
      </Menu>
    </StyledPanel>
  );
};

export default HL7HtmlPanel;
