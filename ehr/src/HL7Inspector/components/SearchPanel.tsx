import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  Select,
  MenuItem,
  SelectChangeEvent,
  IconButton,
} from "@mui/material";
import { Clear as ClearIcon } from "@mui/icons-material";
import { useAtom } from "jotai";
import {
  searchTextAtom,
  searchType<PERSON>tom,
  performSearchAtom,
} from "../context/HL7Inspector";
import { SearchType } from "../types/hl7";
import StyledPanel from "./StyledPanel";
import { Button as CciButton } from "@cci/mui-components";

const SearchPanel: React.FC = () => {
  const [searchText, setSearchText] = useAtom(searchTextAtom);
  const [searchType, setSearchType] = useAtom(searchTypeAtom);
  const [, performSearch] = useAtom(performSearchAtom);
  const [localSearchText, setLocalSearchText] = useState("");
  const previousSearchTextRef = useRef(searchText);

  useEffect(() => {
    const previousSearchText = previousSearchTextRef.current;
    if (previousSearchText !== "" && searchText === "") {
      setLocalSearchText("");
    }
    previousSearchTextRef.current = searchText;
  }, [searchText]);

  const handleTypeChange = (event: SelectChangeEvent) => {
    setSearchType(event.target.value as SearchType);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchText(event.target.value);
  };

  const handleSearchSubmit = (event?: React.FormEvent) => {
    if (event) {
      event.preventDefault();
    }
    setSearchText(localSearchText);
    performSearch();
  };

  const handleClearSearch = () => {
    setLocalSearchText("");
    setSearchText("");
    performSearch();
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleSearchSubmit();
    }
  };

  return (
    <StyledPanel title="Search">
      <Box
        component="form"
        onSubmit={handleSearchSubmit}
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 2,
          flexWrap: "wrap",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography variant="body2" sx={{ whiteSpace: "nowrap" }}>
            Type
          </Typography>
          <Select
            value={searchType}
            onChange={handleTypeChange}
            size="small"
            sx={{ minWidth: 100 }}
          >
            <MenuItem value="name">name</MenuItem>
            <MenuItem value="value">value</MenuItem>
          </Select>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography variant="body2" sx={{ whiteSpace: "nowrap" }}>
            Text
          </Typography>
          <TextField
            sx={{ width: "400px" }}
            size="small"
            variant="outlined"
            value={localSearchText}
            onChange={handleSearchChange}
            onKeyPress={handleKeyPress}
            InputProps={{
              endAdornment: localSearchText && (
                <IconButton
                  size="small"
                  onClick={handleClearSearch}
                  sx={{ padding: 0.5 }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              ),
            }}
          />
        </Box>

        <CciButton
          color="primary"
          size="small"
          onClick={handleSearchSubmit}
          disabled={!localSearchText.trim()}
        >
          Search
        </CciButton>
      </Box>
    </StyledPanel>
  );
};

export default SearchPanel;
