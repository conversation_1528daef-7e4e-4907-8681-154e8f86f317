import React, { useRef, useEffect, useCallback, useMemo } from "react";
import { Box, Typography, Tooltip, Divider } from "@mui/material";
import { TreeView, TreeItem } from "@mui/x-tree-view";
import { useAtom, useAtomValue } from "jotai";
import {
  hl7Tree<PERSON>tom,
  selectedNode<PERSON>ath<PERSON><PERSON>,
  select<PERSON>ode<PERSON><PERSON>,
  expandedNodes<PERSON>tom,
  shouldScrollToNode<PERSON>tom,
} from "../context/HL7Inspector";
import { HL7Node } from "../types/hl7";
import ResizableDrawer from "./ResizableDrawer";
import { Icon, IconName } from "../assets";

const TREE_STYLES = {
  treeItem: {
    "& .MuiTreeItem-label": {
      padding: "0",
    },
  },
  treeView: {
    "& .MuiTreeItem-content.Mui-selected": {
      backgroundColor: "#dce7f9 !important",
    },
  },
  tooltip: {
    backgroundColor: "#ebf2fe !important",
    color: "#000000 !important",
    fontSize: "14px",
    maxWidth: "70vw",
    border: "1px solid #ccc",
    "& .MuiTooltip-arrow": {
      color: "#ebf2fe",
    },
  },
  nodeLabel: {
    display: "flex",
    alignItems: "center",
    cursor: "pointer",
    width: "100%",
    maxWidth: "100%",
    padding: "4px 0",
    overflow: "hidden",
  },
  nodeName: {
    margin: "0 8px 0 4px",
    fontWeight: "bold",
    whiteSpace: "nowrap",
    overflow: "visible",
    flexShrink: 0,
  },
  nodeDescription: {
    whiteSpace: "nowrap",
    overflow: "visible",
    flex: 1,
    minWidth: 0,
  },
  emptyState: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
    textAlign: "center",
  },
};

interface NodeTooltipProps {
  node: HL7Node;
}

const NodeTooltip: React.FC<NodeTooltipProps> = ({ node }) => {
  const titleParts = [node.name];
  if (node.description) {
    titleParts.push(`(${node.description}`);
    if (node.dataType) {
      titleParts.push(`, ${node.dataType})`);
    } else {
      titleParts.push(")");
    }
  } else if (node.dataType) {
    titleParts.push(`(${node.dataType})`);
  }
  const titleText = titleParts.join(" ");

  return (
    <Box>
      <Typography>{titleText}</Typography>
      <Divider sx={{ margin: "8px 0" }} />
      <Typography sx={{ whiteSpace: "pre-wrap", wordBreak: "break-word" }}>
        {node.raw}
      </Typography>
    </Box>
  );
};

interface TreeNodeLabelProps {
  node: HL7Node;
  expandedNodes: Set<string>;
  onLabelClick: (event: React.SyntheticEvent, nodeId: string) => void;
}

const TreeNodeLabel: React.FC<TreeNodeLabelProps> = ({
  node,
  expandedNodes,
  onLabelClick,
}) => {
  const getNodeIcon = useCallback(
    (type: string, nodePath: string, raw: string) => {
      const isExpanded = expandedNodes.has(nodePath);
      const isEmpty = raw.trim() === "";
      const EmptyPlaceholder = () => <div style={{ width: 16 }}></div>;

      // Icon mapping for types that need empty checking
      const conditionalIcons: { [key: string]: IconName } = {
        field: "field" as IconName,
        component: "component" as IconName,
        subcomponent: "subcomponent" as IconName,
        repetition: "repetition" as IconName,
      };

      // Handle conditional icons (show placeholder if empty)
      if (conditionalIcons[type]) {
        return isEmpty ? (
          <EmptyPlaceholder />
        ) : (
          <Icon name={conditionalIcons[type]} />
        );
      }

      // Handle other types
      const iconMap: { [key: string]: () => JSX.Element } = {
        message: () => <Icon name={isExpanded ? "folder-open" : "folder"} />,
        messagetype: () => <Icon name="plugin" />,
        segment: () => <Icon name="segment" />,
      };

      return iconMap[type]?.() || <></>;
    },
    [expandedNodes]
  );

  return (
    <Tooltip
      title={<NodeTooltip node={node} />}
      enterDelay={800}
      placement="bottom-start"
      componentsProps={{
        tooltip: {
          sx: TREE_STYLES.tooltip,
        },
      }}
    >
      <Box
        sx={TREE_STYLES.nodeLabel}
        onClick={(event) => onLabelClick(event, node.path)}
      >
        {getNodeIcon(node.type, node.path, node.raw)}
        <Typography sx={TREE_STYLES.nodeName}>{node.name}</Typography>
        <Typography sx={TREE_STYLES.nodeDescription}>
          {node.description}
        </Typography>
      </Box>
    </Tooltip>
  );
};

const useAutoScroll = (
  selectedNodePath: string | null,
  shouldScrollToNode: boolean,
  setShouldScrollToNode: (value: boolean) => void
) => {
  const treeRef = useRef<HTMLUListElement>(null);

  useEffect(() => {
    if (selectedNodePath && shouldScrollToNode && treeRef.current) {
      const scrollTimeout = setTimeout(() => {
        const selectedElement = treeRef.current?.querySelector(
          `[data-nodeid="${selectedNodePath}"]`
        ) as HTMLElement;

        if (selectedElement) {
          const boxContainer = treeRef.current?.parentElement;
          if (boxContainer) {
            const containerRect = boxContainer.getBoundingClientRect();
            const elementRect = selectedElement.getBoundingClientRect();
            const relativeTop =
              elementRect.top - containerRect.top + boxContainer.scrollTop;
            const targetScrollTop =
              relativeTop - containerRect.height / 2 + elementRect.height / 2;

            boxContainer.scrollTo({
              top: Math.max(0, targetScrollTop),
              behavior: "smooth",
            });
          }
        }
        setShouldScrollToNode(false);
      }, 200);

      return () => clearTimeout(scrollTimeout);
    }
  }, [selectedNodePath, shouldScrollToNode, setShouldScrollToNode]);

  return treeRef;
};

const EmptyState: React.FC = () => (
  <Box sx={TREE_STYLES.emptyState}>
    <Typography variant="body2" color="text.secondary" fontSize={14}>
      No HL7 data to display. Please paste HL7 messages in the HL7 HTML panel.
    </Typography>
  </Box>
);

const HL7TreePanel: React.FC = () => {
  const tree = useAtomValue(hl7TreeAtom);
  const [selectedNodePath] = useAtom(selectedNodePathAtom);
  const [, selectNode] = useAtom(selectNodeAtom);
  const [expandedNodes, setExpandedNodes] = useAtom(expandedNodesAtom);
  const [shouldScrollToNode, setShouldScrollToNode] = useAtom(
    shouldScrollToNodeAtom
  );

  const treeRef = useAutoScroll(
    selectedNodePath,
    shouldScrollToNode,
    setShouldScrollToNode
  );

  const handleLabelClick = useCallback(
    (event: React.SyntheticEvent, nodeId: string) => {
      event.stopPropagation();
      selectNode(nodeId, false);
    },
    [selectNode]
  );

  const handleNodeToggle = useCallback(
    (_event: React.SyntheticEvent, nodeIds: string[]) => {
      setExpandedNodes(new Set(nodeIds));
    },
    [setExpandedNodes]
  );

  const renderTree = useCallback(
    (node: HL7Node): React.ReactNode => {
      return (
        <TreeItem
          key={node.path}
          nodeId={node.path}
          data-nodeid={node.path}
          sx={TREE_STYLES.treeItem}
          label={
            <TreeNodeLabel
              node={node}
              expandedNodes={expandedNodes}
              onLabelClick={handleLabelClick}
            />
          }
        >
          {Array.isArray(node.children) && node.children.length > 0
            ? node.children.map((child) => renderTree(child))
            : null}
        </TreeItem>
      );
    },
    [expandedNodes, handleLabelClick]
  );

  const treeViewProps = useMemo(
    () => ({
      ref: treeRef,
      defaultCollapseIcon: <Icon name="elbow-end-minus" size={25} />,
      defaultExpandIcon: <Icon name="elbow-end-plus" size={25} />,
      expanded: Array.from(expandedNodes),
      onNodeToggle: handleNodeToggle,
      selected: selectedNodePath || "",
      disableSelection: false,
      sx: TREE_STYLES.treeView,
    }),
    [treeRef, expandedNodes, handleNodeToggle, selectedNodePath]
  );

  return (
    <ResizableDrawer
      anchor="left"
      title="HL7 Tree"
      initialSize={450}
      minSize={450}
      maxSize={650}
    >
      <Box sx={{ height: "100%", overflow: "auto", p: 1 }}>
        {tree.length > 0 ? (
          <TreeView {...treeViewProps}>{tree.map(renderTree)}</TreeView>
        ) : (
          <EmptyState />
        )}
      </Box>
    </ResizableDrawer>
  );
};

export default HL7TreePanel;
