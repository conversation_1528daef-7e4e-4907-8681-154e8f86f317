import React, { useMemo } from "react";
import { Box, Typography } from "@mui/material";
import {
  DataGridPro,
  GridColDef,
  GridRenderCellParams,
} from "@mui/x-data-grid-pro";
import { useAtomValue } from "jotai";
import { nodePropertiesAtom } from "../context/HL7Inspector";
import ResizableDrawer from "./ResizableDrawer";

const PropertiesGridPanel: React.FC<any> = ({}) => {
  const properties = useAtomValue(nodePropertiesAtom);

  const rows = useMemo(() => {
    return properties.map((prop, index) => ({
      id: index,
      name: prop.name,
      value: prop.value,
    }));
  }, [properties]);

  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      minWidth: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: "bold" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "value",
      headerName: "Value",
      flex: 2,
      minWidth: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography
          variant="body2"
          sx={{
            wordBreak: "break-word",
            whiteSpace: "normal",
            lineHeight: 1.4,
          }}
        >
          {params.value}
        </Typography>
      ),
    },
  ];

  return (
    <ResizableDrawer anchor="bottom" title="Properties Grid" initialSize={280}>
      <Box sx={{ width: "100%", height: "100%" }}>
        {rows.length > 0 ? (
          <DataGridPro
            rows={rows}
            columns={columns}
            disableColumnMenu
            disableRowSelectionOnClick
            autoHeight={false}
            hideFooter={true}
            hideFooterPagination={true}
            disableColumnFilter
            getRowHeight={() => 32}
            getEstimatedRowHeight={() => 32}
            density="compact"
            sx={{
              fontSize: "14px",
              lineHeight: "1.2",
              border: "none",
              height: "100%",
              "& .MuiDataGrid-columnHeaders": {
                minHeight: 32,
                maxHeight: 32,
                backgroundColor: "rgba(0, 0, 0, 0.04)",
                fontWeight: 500,
              },
              "& .MuiDataGrid-cell": {
                padding: "4px 8px",
                whiteSpace: "normal",
                lineHeight: 1.4,
                fontSize: "14px",
              },
              "& .MuiDataGrid-row": {
                maxHeight: 32,
                minHeight: 32,
              },
              "& .MuiDataGrid-virtualScrollerRenderZone": {
                maxHeight: "100%",
              },
            }}
          />
        ) : (
          <Box
            sx={{
              display: "flex",
              height: "100%",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "14px",
              color: "#888",
            }}
          >
            Select a node to view it's properties
          </Box>
        )}
      </Box>
    </ResizableDrawer>
  );
};

export default PropertiesGridPanel;
