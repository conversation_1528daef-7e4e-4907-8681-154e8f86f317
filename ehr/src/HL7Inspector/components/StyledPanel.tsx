import React from "react";
import { Box, IconButton, Typography } from "@mui/material";

interface StyledPanelProps {
  title?: React.ReactNode;
  onCollapse?: () => void;
  collapsible?: boolean;
  children: React.ReactNode;
  style?: React.CSSProperties;
  sx?: object;
  icon?: React.ReactNode;
  hidden?: boolean;
}

const StyledPanel: React.FC<StyledPanelProps> = ({
  title,
  onCollapse,
  collapsible = true,
  children,
  style,
  sx,
  icon,
  hidden = false,
}) => {
  if (hidden) return null;

  return (
    <Box
      sx={{ padding: "5px", backgroundColor: "#d2e0f2", ...sx }}
      style={style}
    >
      <Box
        sx={{
          display: "flex",
          width: "100%",
          height: "100%",
          flexDirection: "column",
          boxSizing: "border-box",
          backgroundColor: "#d2e0f2",
          border: "1px solid #98c0f4",
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "25px",
            display: "flex",
            paddingLeft: "5px",
            alignItems: "center",
            justifyContent: "space-between",
            flexShrink: 0,
            borderBottom: "1px solid #98c0f4",
          }}
        >
          <Typography
            sx={{
              color: "#15428b",
              font: "bold 11px tahoma,arial,verdana,sans-serif",
            }}
          >
            {title}
          </Typography>
          {collapsible && (
            <IconButton onClick={onCollapse} size="small" sx={{ p: "2px" }}>
              {icon}
            </IconButton>
          )}
        </Box>
        <Box
          sx={{
            p: 2,
            backgroundColor: "#ffffff",
            flexGrow: 1,
            overflow: "auto",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default StyledPanel;
