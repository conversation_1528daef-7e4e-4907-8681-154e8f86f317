import React, { useRef, useState, useEffect, useCallback } from "react";
import { Drawer, Box, IconButton, Typography } from "@mui/material";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import KeyboardDoubleArrowDownIcon from "@mui/icons-material/KeyboardDoubleArrowDown";
import KeyboardDoubleArrowUpIcon from "@mui/icons-material/KeyboardDoubleArrowUp";

type Anchor = "left" | "right" | "top" | "bottom";

interface ResizableDrawerProps {
  anchor: Anchor;
  title: string | React.ReactNode;
  children: React.ReactNode;
  minSize?: number;
  maxSize?: number;
  initialSize?: number;
  storageKey?: string;
}

const DEFAULTS: Record<Anchor, { min: number; max: number; initial: number }> =
  {
    left: { min: 180, max: 600, initial: 240 },
    right: { min: 180, max: 600, initial: 240 },
    top: { min: 120, max: 480, initial: 200 },
    bottom: { min: 120, max: 480, initial: 200 },
  };

const ICONS = {
  collapse: {
    left: <KeyboardDoubleArrowLeftIcon sx={{ fontSize: "15px" }} />,
    right: <KeyboardDoubleArrowRightIcon sx={{ fontSize: "15px" }} />,
    top: <KeyboardDoubleArrowUpIcon sx={{ fontSize: "15px" }} />,
    bottom: <KeyboardDoubleArrowDownIcon sx={{ fontSize: "15px" }} />,
  },
  expand: {
    left: <KeyboardDoubleArrowRightIcon sx={{ fontSize: "15px" }} />,
    right: <KeyboardDoubleArrowLeftIcon sx={{ fontSize: "15px" }} />,
    top: <KeyboardDoubleArrowDownIcon sx={{ fontSize: "15px" }} />,
    bottom: <KeyboardDoubleArrowUpIcon sx={{ fontSize: "15px" }} />,
  },
};

const ResizableDrawer: React.FC<ResizableDrawerProps> = ({
  anchor,
  title,
  children,
  minSize,
  maxSize,
  initialSize,
  storageKey = `resizable-drawer-${anchor}`,
}) => {
  const config = DEFAULTS[anchor];
  const min = minSize ?? config.min;
  const max = maxSize ?? config.max;
  const initial = initialSize ?? config.initial;

  const isHorizontal = anchor === "top" || anchor === "bottom";

  const [drawerOpen, setDrawerOpen] = useState(true);
  const [size, setSize] = useState<number>(() => {
    const saved = localStorage.getItem(storageKey);
    return saved ? parseInt(saved) : initial;
  });
  const [lastSize, setLastSize] = useState(size);
  const sizeRef = useRef(size);

  const isResizing = useRef(false);
  const animationFrame = useRef<number>();

  const updateSize = (newSize: number) => {
    setSize(newSize);
    sizeRef.current = newSize;
  };

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing.current) return;

      let newSize = 0;
      if (anchor === "left") newSize = e.clientX;
      else if (anchor === "right") newSize = window.innerWidth - e.clientX;
      else if (anchor === "top") newSize = e.clientY;
      else if (anchor === "bottom") newSize = window.innerHeight - e.clientY;

      newSize = Math.min(max, Math.max(min, newSize));

      if (animationFrame.current) cancelAnimationFrame(animationFrame.current);
      animationFrame.current = requestAnimationFrame(() => {
        updateSize(newSize);
      });
    },
    [anchor, min, max]
  );

  const handleMouseUp = () => {
    isResizing.current = false;
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
    document.body.style.userSelect = "auto";
    localStorage.setItem(storageKey, String(sizeRef.current));
    setLastSize(sizeRef.current);
  };

  const handleMouseDown = () => {
    isResizing.current = true;
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    document.body.style.userSelect = "none";
  };

  useEffect(() => {
    return () => {
      if (animationFrame.current) cancelAnimationFrame(animationFrame.current);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.body.style.userSelect = "auto";
    };
  }, [handleMouseMove]);

  const toggleCollapsed = () => {
    if (!drawerOpen) {
      updateSize(lastSize);
      setDrawerOpen(true);
    } else {
      setLastSize(sizeRef.current);
      updateSize(0);
      setDrawerOpen(false);
    }
  };

  const drawerSizeStyle = isHorizontal
    ? { height: size, width: "100%" }
    : { width: size, height: "100%" };

  const resizerStyle: React.CSSProperties = {
    position: "absolute",
    zIndex: 2,
    cursor: isHorizontal ? "row-resize" : "col-resize",
    ...(anchor === "left" && { top: 0, right: 0, bottom: 0, width: "2px" }),
    ...(anchor === "right" && { top: 0, left: 0, bottom: 0, width: "2px" }),
    ...(anchor === "top" && { bottom: 0, left: 0, right: 0, height: "2px" }),
    ...(anchor === "bottom" && { top: 0, left: 0, right: 0, height: "2px" }),
  };

  const collapsedBoxStyle = isHorizontal
    ? { height: "30px", width: "100%" }
    : { width: "30px", height: "100%" };

  return (
    <>
      <Drawer
        variant="persistent"
        open={drawerOpen}
        anchor={anchor}
        PaperProps={{
          sx: {
            ...drawerSizeStyle,
            position: "relative",
            overflow: "visible",
            backgroundColor: "#d2e0f2",
            padding: "5px",
            boxSizing: "border-box",
          },
        }}
        sx={{
          "&.MuiDrawer-root.MuiDrawer-docked": {
            display: drawerOpen ? "block" : "none",
          },
        }}
      >
        <Box
          sx={{
            display: size === 0 ? "none" : "flex",
            height: "100%",
            width: "100%",
            flexDirection: "column",
            boxSizing: "border-box",
            backgroundColor: "#d2e0f2",
            border: "1px solid #98c0f4",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "25px",
              display: "flex",
              paddingLeft: "5px",
              alignItems: "center",
              justifyContent: "space-between",
              flexShrink: 0,
              borderBottom: "1px solid #98c0f4",
            }}
          >
            <Typography
              sx={{
                color: "#15428b",
                font: "bold 11px tahoma,arial,verdana,sans-serif",
              }}
            >
              {title}
            </Typography>
            <IconButton
              onClick={toggleCollapsed}
              size="small"
              sx={{ p: "2px" }}
            >
              {ICONS.collapse[anchor]}
            </IconButton>
          </Box>
          <Box
            sx={{
              backgroundColor: "#ffffff",
              flexGrow: 1,
              overflow: "auto",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {children}
          </Box>
        </Box>

        <Box
          onMouseDown={handleMouseDown}
          sx={{
            ...resizerStyle,
            "&:hover": {
              backgroundColor: "rgba(0,0,0,0.1)",
            },
          }}
        />
      </Drawer>

      {!drawerOpen && (
        <Box
          sx={{
            ...collapsedBoxStyle,
            backgroundColor: "#d2e0f2",
            padding: "5px",
          }}
        >
          <Box
            onClick={toggleCollapsed}
            sx={{
              backgroundColor: "#d2e0f2",
              height: isHorizontal ? "20px" : "100%",
              width: isHorizontal ? "100%" : "20px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              border: "1px solid #98c0f4",
            }}
          >
            <IconButton>{ICONS.expand[anchor]}</IconButton>
          </Box>
        </Box>
      )}
    </>
  );
};

export default ResizableDrawer;
