import { atom } from "jotai";
import {
  HL7Node,
  HL7Property,
  SearchType,
  HL7Delimiters,
  SearchMatchInfo,
} from "../types/hl7";
import {
  parseHL7ToTree,
  searchHL7Tree,
  findNodeByPath,
  getNodeProperties,
} from "../utils/hl7Parser";

export const hl7ResourceAtom = atom<any>(null);
export const hl7TreeAtom = atom<HL7Node[]>([]);

export const selectedNodePathAtom = atom<string | null>(null);
export const nodePropertiesAtom = atom<HL7Property[]>([]);
export const shouldScrollToNodeAtom = atom<boolean>(false);

export const searchTextAtom = atom<string>("");
export const searchTypeAtom = atom<SearchType>("name");
export const searchMatchInfoAtom = atom<SearchMatchInfo[]>([]);

// New atom specifically for HTML panel search highlighting
export const htmlSearchHighlightAtom = atom<SearchMatchInfo[]>([]);

export const expandedNodesAtom = atom<Set<string>>(new Set<string>());

/**
 * Extract delimiter information from tree structure
 * @param tree - HL7 tree nodes
 * @returns Array of unique delimiters
 */
export function getDelimitersFromTree(tree: HL7Node[]): HL7Delimiters[] {
  const delimiterStrings = new Set<string>();
  const uniqueDelimiters: HL7Delimiters[] = [];

  const findDelimiters = (nodes: HL7Node[]) => {
    for (const node of nodes) {
      if (node.delimiters) {
        const delimiterString = JSON.stringify(node.delimiters);
        if (!delimiterStrings.has(delimiterString)) {
          delimiterStrings.add(delimiterString);
          uniqueDelimiters.push(node.delimiters);
        }
      }
      if (node.children) {
        findDelimiters(node.children);
      }
    }
  };

  findDelimiters(tree);
  return uniqueDelimiters;
}

/**
 * Find delimiter information for a specific node or its ancestors
 * @param tree - HL7 tree nodes
 * @param targetPath - Path of the target node
 * @returns Delimiter information
 */
export function findDelimitersForNode(
  tree: HL7Node[],
  targetPath: string
): HL7Delimiters | null {
  const findNode = (nodes: HL7Node[], path: string): HL7Node | null => {
    for (const node of nodes) {
      if (node.path === path) {
        return node;
      }
      if (node.children) {
        const found = findNode(node.children, path);
        if (found) return found;
      }
    }
    return null;
  };

  let currentNode = findNode(tree, targetPath);
  while (currentNode) {
    if (currentNode.delimiters) {
      return currentNode.delimiters;
    }
    if (currentNode.parent) {
      currentNode = findNode(tree, currentNode.parent);
    } else {
      break;
    }
  }

  return null;
}

export const parseHL7Atom = atom(null, (get, set, rawMessage: string) => {
  try {
    const hl7Resource = get(hl7ResourceAtom);

    // Use the new unified parser
    const tree = parseHL7ToTree(rawMessage, hl7Resource);
    set(hl7TreeAtom, tree);

    set(selectedNodePathAtom, null);
    set(nodePropertiesAtom, []);
    set(searchMatchInfoAtom, []);
    set(htmlSearchHighlightAtom, []);

    return tree;
  } catch (error) {
    console.error("Failed to parse HL7 message:", error);
    return [];
  }
});

export const selectNodeAtom = atom(
  null,
  (
    get,
    set,
    path: string,
    expand: boolean = true,
    shouldScroll: boolean = false
  ) => {
    set(selectedNodePathAtom, path);
    set(shouldScrollToNodeAtom, shouldScroll);

    // clear the search highlight status, only show the selected node highlight
    set(searchTextAtom, "");
    set(searchMatchInfoAtom, []);

    // Clear HTML panel search highlight when selecting a node
    set(htmlSearchHighlightAtom, []);

    const tree = get(hl7TreeAtom);
    const node = findNodeByPath(tree, path);

    if (!node) {
      return;
    }
    set(nodePropertiesAtom, getNodeProperties(node));

    if (expand) {
      const pathParts = path.split("/");
      const expandedPaths = new Set(get(expandedNodesAtom));
      let currentPath = "";
      pathParts.forEach((part) => {
        if (currentPath) {
          currentPath += "/";
        }
        currentPath += part;
        expandedPaths.add(currentPath);
      });
      set(expandedNodesAtom, expandedPaths);
    }
  }
);

export const performSearchAtom = atom(null, (get, set) => {
  const searchText = get(searchTextAtom);
  const searchType = get(searchTypeAtom);
  const tree = get(hl7TreeAtom);

  // Clear HTML panel search highlight before applying new search highlight
  set(htmlSearchHighlightAtom, []);

  if (!searchText.trim() || tree.length === 0) {
    set(searchMatchInfoAtom, []);
    return;
  }

  const { matchInfo } = searchHL7Tree(tree, searchText, searchType);
  set(searchMatchInfoAtom, matchInfo);

  // Apply search results to HTML panel highlighting
  set(htmlSearchHighlightAtom, matchInfo);
});
