import { v4 as uuidv4 } from "uuid";
import {
  HL7N<PERSON>,
  HL7Delimiters,
  SearchMatchInfo,
  SearchResults,
} from "../types/hl7";

/**
 * Generate a short unique ID using UUID
 * @returns Short UUID string (first 8 characters)
 */
function generateShortId(): string {
  return uuidv4().substring(0, 8);
}

/**
 * Parse HL7 delimiters from MSH segment
 * @param mshSegment - MSH segment string
 * @returns Object containing all HL7 delimiters
 */
function parseHL7Delimiters(mshSegment: string): HL7Delimiters {
  // Default delimiters
  const delimiters: HL7Delimiters = {
    segmentDelimiter: "\r",
    fieldDelimiter: "|",
    componentDelimiter: "^",
    repetitionDelimiter: "~",
    escapeCharacter: "\\",
    subcomponentDelimiter: "&",
    encodingChars: "^~\\&",
  };

  if (mshSegment && mshSegment.length >= 8) {
    // Extract delimiters from MSH segment
    // MSH|^~\&|... format
    delimiters.fieldDelimiter = mshSegment[3];
    delimiters.componentDelimiter = mshSegment[4];
    delimiters.repetitionDelimiter = mshSegment[5];
    delimiters.escapeCharacter = mshSegment[6];
    delimiters.subcomponentDelimiter = mshSegment[7];

    delimiters.encodingChars =
      delimiters.componentDelimiter +
      delimiters.repetitionDelimiter +
      delimiters.escapeCharacter +
      delimiters.subcomponentDelimiter;
  }

  return delimiters;
}

/**
 * Get valid segment names from HL7 resource
 * @param hl7Resource - HL7 configuration data
 * @returns Array of valid segment names
 */
function getValidSegmentNames(hl7Resource?: any): string[] {
  if (hl7Resource && hl7Resource.hl7_segment_names) {
    return hl7Resource.hl7_segment_names.map((s: any) => s.segment_name);
  }
  return [];
}

/**
 * Split HL7 message into segments based on valid segment names
 * @param messageText - Raw HL7 message text (with line endings removed)
 * @param hl7Resource - HL7 configuration data
 * @returns Array of segment strings
 */
function splitMessageIntoSegments(
  messageText: string,
  hl7Resource?: any
): string[] {
  const validSegmentNames = getValidSegmentNames(hl7Resource);

  // Create regex pattern to match valid segment names followed by delimiter
  // Since line endings are already removed, we only need to match segment names at start or after other segments
  const segmentPattern = new RegExp(`(${validSegmentNames.join("|")})\\|`, "g");

  const segments: string[] = [];

  // Find all segment matches
  const matches: { index: number; segmentName: string }[] = [];
  let match;
  while ((match = segmentPattern.exec(messageText)) !== null) {
    matches.push({
      index: match.index, // Position of segment name
      segmentName: match[1],
    });
  }

  // Extract segments based on matches
  for (let i = 0; i < matches.length; i++) {
    const currentMatch = matches[i];
    const nextMatch = matches[i + 1];

    const startIndex = currentMatch.index;
    const endIndex = nextMatch ? nextMatch.index : messageText.length;

    const segmentText = messageText.substring(startIndex, endIndex).trim();
    if (segmentText) {
      segments.push(segmentText);
    }
  }

  return segments;
}

/**
 * Parse raw HL7 message directly into tree structure
 * @param rawMessage - The raw HL7 message string
 * @param hl7Resource - HL7 configuration data
 * @returns Tree structure of HL7 nodes
 */
export function parseHL7ToTree(
  rawMessage: string,
  hl7Resource?: any
): HL7Node[] {
  // Handle null, undefined, or non-string inputs
  if (!rawMessage || typeof rawMessage !== "string") {
    const rootId = generateShortId();
    const rootPath = rootId;

    return [
      {
        id: rootId,
        type: "message",
        name: "HL7 Message",
        path: rootPath,
        raw: "",
        children: [],
      },
    ];
  }

  // Clean the message text by removing all line ending characters
  const cleanedMessage = rawMessage
    .replace(/\^M/g, "") // Remove visual ^M characters
    .replace(/\r\n/g, "") // Remove Windows CRLF
    .replace(/\r/g, "") // Remove Mac CR
    .replace(/\n/g, "") // Remove Unix LF
    .trim(); // Remove leading/trailing whitespace

  // Split multiple messages using MSH segment as separator
  const messageTexts = cleanedMessage.split(/(?=MSH\|)/g).filter(Boolean);

  // Create a single root node to contain all messages
  const rootId = generateShortId();
  const rootPath = rootId;

  const rootNode: HL7Node = {
    id: rootId,
    type: "message",
    name: "HL7 Message",
    path: rootPath,
    raw: cleanedMessage,
    children: [],
  };

  // Parse each message text directly into tree nodes
  rootNode.children = messageTexts.map((messageText, index) => {
    try {
      const messageId = generateShortId();
      const messagePath = messageId;

      // Use segment-based splitting instead of line-based splitting
      const segmentLines = splitMessageIntoSegments(messageText, hl7Resource);

      // Extract message type and event type from MSH segment
      let messageType = "";
      let eventType = "";
      const mshLine = segmentLines.find((line) => line.startsWith("MSH|"));

      // Parse HL7 delimiters from MSH segment
      const delimiters = parseHL7Delimiters(mshLine || "");

      if (mshLine) {
        const mshFields = mshLine.split(delimiters.fieldDelimiter);
        if (mshFields.length > 8) {
          // With MSH special handling:
          // mshFields[0] = "MSH"
          // mshFields[1] = encoding chars (but we treat | as MSH-1 and ^~\& as MSH-2)
          // mshFields[8] = MSH-9 (message type field)
          const msgTypeField = mshFields[8]; // MSH.9 - Message Type
          const typeParts = msgTypeField.split(delimiters.componentDelimiter);
          messageType = typeParts[0] || "";
          if (typeParts.length >= 2) {
            eventType = typeParts[1] || "";
          }
        }
      }

      // Create message type node
      const messageNode: HL7Node = {
        id: messageId,
        type: "messagetype",
        name: `${messageType}${delimiters.componentDelimiter}${eventType}`,
        value: `${messageType}${delimiters.componentDelimiter}${eventType}`,
        description: getMessageTypeDescription(eventType, hl7Resource),
        path: messagePath,
        raw: messageText,
        parent: rootId,
        children: [],
        delimiters,
        messageType,
        eventType,
      };

      // Parse segments directly into tree nodes
      messageNode.children = segmentLines.map((segmentLine) =>
        parseSegmentToTree(segmentLine, hl7Resource, messagePath, delimiters)
      );

      return messageNode;
    } catch (error) {
      console.error(`Error parsing message ${index + 1}:`, error);
      const errorMessageId = generateShortId();
      const errorMessagePath = errorMessageId;
      const errorSegmentId = generateShortId();
      const errorSegmentPath = `${errorMessagePath}/${errorSegmentId}`;

      // Use default delimiters for error case
      const defaultDelimiters = parseHL7Delimiters("");

      return {
        id: errorMessageId,
        type: "messagetype" as const,
        name: "ERROR",
        description: "Failed to parse message",
        path: errorMessagePath,
        raw: messageText,
        parent: rootId,
        delimiters: defaultDelimiters,
        messageType: "",
        eventType: "",
        children: [
          {
            id: errorSegmentId,
            type: "segment" as const,
            name: "ERROR",
            description: "Failed to parse message",
            path: errorSegmentPath,
            raw: messageText,
            parent: errorMessagePath,
            children: [],
          },
        ],
      };
    }
  });

  return [rootNode];
}

/**
 * Parse HL7 segment line directly into tree node
 * @param segmentLine - The HL7 segment line
 * @param hl7Resource - HL7 configuration data
 * @param messagePath - Parent message path
 * @param delimiters - HL7 delimiters
 * @returns Tree node for the segment
 */
function parseSegmentToTree(
  segmentLine: string,
  hl7Resource: any,
  messagePath: string,
  delimiters: HL7Delimiters
): HL7Node {
  const segmentId = generateShortId();
  const segmentPath = `${messagePath}/${segmentId}`;

  // Split segment into fields
  const fieldParts = segmentLine.split(delimiters.fieldDelimiter);
  const segmentName = fieldParts[0];

  let segmentFields: string[];

  if (segmentName === "MSH") {
    // MSH segment special handling according to HL7 standard:
    // MSH-1: Field delimiter (|)
    // MSH-2: Encoding characters (^~\&)
    // MSH-3 and beyond: Regular fields
    segmentFields = [
      delimiters.fieldDelimiter, // MSH-1: |
      delimiters.encodingChars, // MSH-2: ^~\&
      ...fieldParts.slice(2), // MSH-3 onwards
    ];
  } else {
    // Other segments: skip segment name as usual
    segmentFields = fieldParts.slice(1);
  }

  const segmentNode: HL7Node = {
    id: segmentId,
    type: "segment",
    name: segmentName,
    raw: segmentLine,
    description: getSegmentDescription(segmentName, hl7Resource),
    path: segmentPath,
    parent: messagePath,
    children: [],
  };

  // Parse fields directly into tree nodes
  segmentNode.children = segmentFields.map((fieldValue, index) =>
    parseFieldToTree(
      fieldValue,
      index,
      segmentName,
      segmentPath,
      hl7Resource,
      delimiters
    )
  );

  return segmentNode;
}

/**
 * Parse HL7 field directly into tree node
 * @param fieldValue - The field value string
 * @param fieldIndex - Field index (0-based)
 * @param segmentName - Parent segment name
 * @param segmentPath - Parent segment path
 * @param hl7Resource - HL7 configuration data
 * @param delimiters - HL7 delimiters
 * @returns Tree node for the field
 */
function parseFieldToTree(
  fieldValue: string,
  fieldIndex: number,
  segmentName: string,
  segmentPath: string,
  hl7Resource: any,
  delimiters: HL7Delimiters
): HL7Node {
  const fieldId = generateShortId();
  const fieldPath = `${segmentPath}/${fieldId}`;
  const fieldDisplayName = `${segmentName}-${fieldIndex + 1}`;

  const cleanFieldValue = fieldValue.replace(/\^M/g, "");

  // Special handling for MSH-2 (encoding characters) - treat as complete field without parsing
  const isMSH2 = segmentName === "MSH" && fieldIndex === 1;

  if (isMSH2) {
    // For MSH-2, don't parse components - treat as complete field
    const fieldNode: HL7Node = {
      id: fieldId,
      type: "field",
      name: fieldDisplayName,
      value: cleanFieldValue,
      description: getFieldDescription(fieldIndex, segmentName, hl7Resource),
      path: fieldPath,
      raw: cleanFieldValue,
      parent: segmentPath,
      children: [],
      dataType: getFieldDataType(fieldIndex, segmentName, hl7Resource),
      fieldIndex,
      repeats: false,
    };
    return fieldNode;
  }

  // Parse repetitions and components for other fields
  const repetitionParts = cleanFieldValue.split(delimiters.repetitionDelimiter);
  const hasRepetitions = repetitionParts.length > 1;

  const fieldNode: HL7Node = {
    id: fieldId,
    type: "field",
    name: fieldDisplayName,
    value: cleanFieldValue,
    description: getFieldDescription(fieldIndex, segmentName, hl7Resource),
    path: fieldPath,
    raw: cleanFieldValue,
    parent: segmentPath,
    children: [],
    dataType: getFieldDataType(fieldIndex, segmentName, hl7Resource),
    fieldIndex,
    repeats: hasRepetitions,
  };

  // Parse components
  let allComponents: HL7Node[] = [];

  if (hasRepetitions) {
    // Handle repetitions
    repetitionParts.forEach((repValue, repIndex) => {
      const componentParts = repValue.split(delimiters.componentDelimiter);
      componentParts.forEach((compValue, compIndex) => {
        const componentNode = parseComponentToTree(
          compValue,
          compIndex,
          fieldPath,
          fieldIndex,
          segmentName,
          delimiters,
          repIndex
        );
        allComponents.push(componentNode);
      });
    });
  } else {
    // Handle single occurrence
    const componentParts = cleanFieldValue.split(delimiters.componentDelimiter);
    componentParts.forEach((compValue, compIndex) => {
      const componentNode = parseComponentToTree(
        compValue,
        compIndex,
        fieldPath,
        fieldIndex,
        segmentName,
        delimiters
      );
      allComponents.push(componentNode);
    });
  }

  // Determine if field should show children
  const shouldShowChildren =
    allComponents.length > 1 ||
    (allComponents.length === 1 &&
      allComponents[0].children &&
      allComponents[0].children.length > 1) ||
    hasRepetitions;

  if (shouldShowChildren) {
    if (hasRepetitions) {
      // Group components by repetition and create repetition nodes
      const repetitionGroups = new Map<number, HL7Node[]>();
      allComponents.forEach((component) => {
        const repIndex = component.repetitionIndex ?? 0;
        if (!repetitionGroups.has(repIndex)) {
          repetitionGroups.set(repIndex, []);
        }
        repetitionGroups.get(repIndex)!.push(component);
      });

      fieldNode.children = Array.from(repetitionGroups.entries()).map(
        ([repIndex, components]) => {
          const repetitionId = generateShortId();
          const repetitionDisplayName = `${segmentName}-${fieldIndex + 1}[${repIndex + 1}]`;
          const repetitionPath = `${fieldPath}/${repetitionId}`;

          return {
            id: repetitionId,
            type: "repetition" as const,
            name: repetitionDisplayName,
            value: components.map((c) => c.value).join("^"),
            path: repetitionPath,
            raw: components.map((c) => c.raw).join("^"),
            parent: fieldId,
            children: components.map((comp) => ({
              ...comp,
              parent: repetitionId,
            })),
            repetitionIndex: repIndex,
            isRepetition: true,
          };
        }
      );
    } else {
      fieldNode.children = allComponents;
    }
  }

  return fieldNode;
}

/**
 * Parse HL7 component directly into tree node
 * @param compValue - Component value string
 * @param compIndex - Component index
 * @param fieldPath - Parent field path
 * @param fieldIndex - Parent field index
 * @param segmentName - Parent segment name
 * @param delimiters - HL7 delimiters
 * @param repIndex - Repetition index (optional)
 * @returns Tree node for the component
 */
function parseComponentToTree(
  compValue: string,
  compIndex: number,
  fieldPath: string,
  fieldIndex: number,
  segmentName: string,
  delimiters: HL7Delimiters,
  repIndex?: number
): HL7Node {
  const componentId = generateShortId();
  const componentPath = `${fieldPath}/${componentId}`;

  // Clean up component value to remove any remaining ^M characters
  const cleanCompValue = compValue.replace(/\^M/g, "");

  const baseName =
    repIndex !== undefined
      ? `${segmentName}-${fieldIndex + 1}-${repIndex + 1}-${compIndex + 1}`
      : `${segmentName}-${fieldIndex + 1}-${compIndex + 1}`;

  const componentNode: HL7Node = {
    id: componentId,
    type: "component",
    name: baseName,
    value: cleanCompValue,
    path: componentPath,
    raw: cleanCompValue,
    parent: fieldPath,
    children: [],
    componentIndex: compIndex,
    repetitionIndex: repIndex,
    isRepetition: repIndex !== undefined,
  };

  // Parse subcomponents
  const subComponentParts = cleanCompValue.split(
    delimiters.subcomponentDelimiter
  );
  if (subComponentParts.length > 1) {
    componentNode.children = subComponentParts.map((subValue, subIndex) => ({
      id: generateShortId(),
      type: "subcomponent" as const,
      name: `${baseName}-${subIndex + 1}`,
      value: subValue.replace(/\^M/g, ""), // Clean any remaining ^M characters
      path: `${componentPath}/${generateShortId()}`,
      raw: subValue.replace(/\^M/g, ""),
      parent: componentId,
      subComponentIndex: subIndex,
    }));
  }

  return componentNode;
}

function capitalize(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Get properties for a given node
 * @param node - HL7 node to get properties for
 * @returns Array of name-value pairs representing node properties
 */
export function getNodeProperties(
  node: HL7Node
): { name: string; value: string }[] {
  const properties: { name: string; value: string }[] = [
    { name: "name", value: node.name },
    { name: "value", value: node.value || "" },
    { name: "type", value: capitalize(node.type) },
    { name: "dataType", value: node.dataType || "" },
    { name: "description", value: node.description || "" },
    { name: "valueDisp", value: node.value || "" },
    { name: "nodeId", value: node.id },
    { name: "treePath", value: node.path },
  ];

  return properties;
}

/**
 * Find a node by its path in the tree
 * @param nodes - Array of HL7 nodes to search
 * @param path - Path to search for
 * @returns Matching node or null if not found
 */
export function findNodeByPath(nodes: HL7Node[], path: string): HL7Node | null {
  for (const node of nodes) {
    if (node.path === path) {
      return node;
    }

    if (node.children && node.children.length > 0) {
      const found = findNodeByPath(node.children, path);
      if (found) {
        return found;
      }
    }
  }

  return null;
}

/**
 * Get description for message type and event type
 * @param eventType - Event type (e.g. A01)
 * @param hl7Resource - HL7 configuration data
 * @returns Combined description string
 */
function getMessageTypeDescription(
  eventType?: string,
  hl7Resource?: any
): string {
  let eventDesc;

  if (hl7Resource) {
    // Get event type description
    if (eventType && hl7Resource.hl7_event_types) {
      const evtType = hl7Resource.hl7_event_types.find(
        (e: any) => e.event_type === eventType
      );
      if (evtType) {
        eventDesc = evtType.description;
      }
    }
  }

  return eventDesc;
}

/**
 * Get description for HL7 segment
 * @param segmentName - Segment name
 * @param hl7Resource - HL7 configuration data
 * @returns Description string
 */
function getSegmentDescription(segmentName: string, hl7Resource: any): string {
  // try to get description from configuration
  if (hl7Resource && hl7Resource.hl7_segment_names) {
    const segment = hl7Resource.hl7_segment_names.find(
      (s: any) => s.segment_name === segmentName
    );
    if (segment) {
      return segment.description;
    }
  }

  return "";
}

/**
 * Get field description based on index and segment
 * @param index - Field index
 * @param segmentName - Segment name
 * @param hl7Resource - HL7 configuration data
 * @returns Field description string
 */
function getFieldDescription(
  index: number,
  segmentName?: string,
  hl7Resource?: any
): string {
  if (hl7Resource && hl7Resource.hl7_attributes && segmentName) {
    const fieldKey = `${segmentName}-${index + 1}`;
    const attribute = hl7Resource.hl7_attributes.find(
      (attr: any) => attr.segment_num === fieldKey
    );
    if (attribute) {
      return attribute.element_name || "";
    }
  }

  return "";
}

/**
 * Get field data type based on index and segment
 * @param index - Field index
 * @param segmentName - Segment name
 * @param hl7Resource - HL7 configuration data
 * @returns Field data type string
 */
function getFieldDataType(
  index: number,
  segmentName?: string,
  hl7Resource?: any
): string {
  if (hl7Resource && hl7Resource.hl7_attributes && segmentName) {
    const fieldKey = `${segmentName}-${index + 1}`;
    const attribute = hl7Resource.hl7_attributes.find(
      (attr: any) => attr.segment_num === fieldKey
    );
    if (attribute) {
      return attribute.dt || "";
    }
  }
  return "";
}

/**
 * Search HL7 tree for matching nodes
 * @param nodes - Array of HL7 nodes to search
 * @param searchText - Text to search for
 * @param searchType - Type of search (name or value)
 * @returns Search results with match information
 */
export function searchHL7Tree(
  nodes: HL7Node[],
  searchText: string,
  searchType: "name" | "value"
): SearchResults {
  if (!searchText || searchText.trim() === "") {
    return { results: [], matchInfo: [] };
  }

  const searchLower = searchText.toLowerCase();

  function searchRecursive(nodeList: HL7Node[]): SearchResults {
    const results: string[] = [];
    const matchInfo: SearchMatchInfo[] = [];

    for (const node of nodeList) {
      let isMatch = false;
      let matchText = "";

      // Unified search logic for messagetype nodes
      if (node.type === "messagetype") {
        if (searchType === "name") {
          if (
            node.messageType &&
            node.messageType?.toLowerCase().includes(searchLower)
          ) {
            isMatch = true;
            matchText = node.messageType;
          }
        }
        // For value search, don't match at messagetype level - let it continue to field level
        // This way only specific fields (like MSH-9) will be highlighted, not the entire message
      } else {
        // Regular search logic for other node types
        if (searchType === "name" && node.name) {
          if (node.name.toLowerCase().includes(searchLower)) {
            isMatch = true;
            matchText = node.name;
          }
        } else if (searchType === "value" && node.value) {
          if (node.value.toLowerCase().includes(searchLower)) {
            isMatch = true;
            matchText = node.value;
          }
        }
      }

      if (isMatch) {
        results.push(node.path);
        matchInfo.push({
          path: node.path,
          elementId: node.id,
          elementType: node.type,
          matchText: matchText,
          matchType: searchType,
        });
      }

      if (node.children && node.children.length > 0) {
        const childResults = searchRecursive(node.children);
        results.push(...childResults.results);
        matchInfo.push(...childResults.matchInfo);
      }
    }

    return { results, matchInfo };
  }

  return searchRecursive(nodes);
}
