import { describe, it, expect, beforeEach } from "vitest";
import {
  parseHL7ToTree,
  findNodeByPath,
  searchHL7Tree,
  getNodeProperties,
} from "./hl7Parser";
import { HL7Node, HL7Delimiters } from "../types/hl7";

describe("HL7Parser", () => {
  let simpleMessage: string;
  let complexMessage: string;
  let messageWithRepeats: string;
  let messageWithComponents: string;
  let messageWithSubcomponents: string;

  beforeEach(() => {
    // Simple ADT message
    simpleMessage = `MSH|^~\&|SENDING_APPLICATION|SENDING_FACILITY|RECEIVING_APPLICATION|RECEIVING_FACILITY|20230101120000||ADT^A01|12345|P|2.5
PID|1||123456^^^HOSPITAL^MR||DOE^JOHN^MIDDLE^^JR||19801231|M|||123 MAIN ST^^CITY^STATE^12345||555-1234|555-5678|||||||||||||||||||
PV1|1|I|ICU^101^01|||12345^DOCTOR^ATTENDING^^^DR|||SUR||||A|||12345^DOCTOR^ATTENDING^^^DR|INP|CAT||||||||||||||||||||SF||A|||20230101120000`;

    // Complex message with components and subcomponents
    complexMessage = `MSH|^~\&|LAB|HOSPITAL|HIS|HOSPITAL|20230101120000||ORU^R01|MSG123|P|2.5
PID|1||987654321^^^HOSPITAL^MR~123456789^^^SSN^SS||SMITH^MARY^ELIZABETH^MS^PhD|MAIDEN|19750615|F||2106-3^WHITE^HL70005|789 OAK AVE^^HOMETOWN^TX^75001^USA^H||214-555-0123^PRN^PH~214-555-0124^WPN^PH|EN^ENGLISH^ISO639||M|CHR^CHRISTIAN^HL70006|||***********|||||||||||20230101
OBR|1|ORDER123^LAB|SPECIMEN456^LAB|CBC^COMPLETE BLOOD COUNT^L|||20230101120000|20230101120000||||||||12345^ORDERING^PHYSICIAN^^^DR||||||20230101130000||F
OBX|1|NM|WBC^White Blood Count^L|1|7.5|10*3/uL|4.0-11.0|N|||F|||20230101130000`;

    // Message with repetitions
    messageWithRepeats = `MSH|^~\&|SYSTEM|FACILITY|TARGET|FACILITY|20230101120000||ADT^A08|MSG001|P|2.5
PID|1||111222333^^^HOSPITAL^MR~444555666^^^SSN^SS||JONES^ROBERT^||19900101|M|||456 ELM ST^^CITY^STATE^54321~789 PINE AVE^^OTHERCITY^STATE^98765||555-0001^PRN^PH~555-0002^WPN^PH~555-0003^FAX^FX`;

    // Message with subcomponents
    messageWithSubcomponents = `MSH|^~\&|LAB|FACILITY|HIS|FACILITY|20230101120000||ORU^R01|LAB001|P|2.5
OBX|1|ST|NOTE^Clinical Note^L|1|Patient&has&mild&symptoms&but&is&stable||||F|||20230101120000`;
  });

  describe("parseHL7ToTree", () => {
    it("should handle null or undefined input", () => {
      const result1 = parseHL7ToTree(null as any);
      const result2 = parseHL7ToTree(undefined as any);
      const result3 = parseHL7ToTree("");

      expect(result1).toHaveLength(1);
      expect(result1[0].type).toBe("message");
      expect(result1[0].name).toBe("HL7 Message");
      expect(result1[0].raw).toBe("");

      expect(result2).toHaveLength(1);
      expect(result3).toHaveLength(1);
    });

    it("should parse simple HL7 message correctly", () => {
      const tree = parseHL7ToTree(simpleMessage);

      expect(tree).toHaveLength(1);
      expect(tree[0].type).toBe("message");
      expect(tree[0].children).toHaveLength(1); // One messagetype node

      const messageTypeNode = tree[0].children![0];
      expect(messageTypeNode.type).toBe("messagetype");
      expect(messageTypeNode.messageType).toBe("ADT");
      expect(messageTypeNode.eventType).toBe("A01");
      expect(messageTypeNode.children).toHaveLength(3); // MSH, PID, PV1 segments
    });

    it("should parse message with components correctly", () => {
      const tree = parseHL7ToTree(complexMessage);
      const messageTypeNode = tree[0].children![0];
      const pidSegment = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      );

      expect(pidSegment).toBeDefined();

      // Check patient name field (PID-5) with components
      const nameField = pidSegment!.children!.find(
        (child) => child.name === "PID-5"
      );
      expect(nameField).toBeDefined();
      expect(nameField!.children).toBeDefined();
      expect(nameField!.children!.length).toBeGreaterThan(1);

      // Check first component contains "SMITH"
      const firstNameComponent = nameField!.children!.find((child) =>
        child.value?.includes("SMITH")
      );
      expect(firstNameComponent).toBeDefined();
    });

    it("should parse message with repetitions correctly", () => {
      const tree = parseHL7ToTree(messageWithRepeats);
      const messageTypeNode = tree[0].children![0];
      const pidSegment = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      );

      expect(pidSegment).toBeDefined();

      // Check for repeated fields (patient identifiers and phone numbers)
      const idField = pidSegment!.children!.find(
        (child) => child.name === "PID-3"
      );
      const phoneField = pidSegment!.children!.find(
        (child) => child.name === "PID-13"
      );

      expect(idField).toBeDefined();
      expect(phoneField).toBeDefined();

      // Should have repetition children
      if (idField?.repeats) {
        expect(idField.children).toBeDefined();
        expect(idField.children!.length).toBeGreaterThan(1);
      }
    });

    it("should parse message with subcomponents correctly", () => {
      const tree = parseHL7ToTree(messageWithSubcomponents);
      const messageTypeNode = tree[0].children![0];
      const obxSegment = messageTypeNode.children!.find(
        (child) => child.name === "OBX"
      );

      expect(obxSegment).toBeDefined();

      // Check OBX-5 (observation value) field with subcomponents
      const valueField = obxSegment!.children!.find(
        (child) => child.name === "OBX-5"
      );
      expect(valueField).toBeDefined();

      // Should contain subcomponent separators (&)
      expect(valueField!.raw).toContain("&");
    });

    it("should correctly parse delimiters from MSH segment", () => {
      const tree = parseHL7ToTree(simpleMessage);
      const messageTypeNode = tree[0].children![0];

      expect(messageTypeNode.delimiters).toBeDefined();
      expect(messageTypeNode.delimiters!.fieldDelimiter).toBe("|");
      expect(messageTypeNode.delimiters!.componentDelimiter).toBe("^");
      expect(messageTypeNode.delimiters!.repetitionDelimiter).toBe("~");
      expect(messageTypeNode.delimiters!.escapeCharacter).toBe("\\");
      expect(messageTypeNode.delimiters!.subcomponentDelimiter).toBe("&");
      expect(messageTypeNode.delimiters!.encodingChars).toBe("^~\\&");
    });

    it("should handle empty fields correctly", () => {
      const messageWithEmptyFields = `MSH|^~\\&|SYSTEM||TARGET||20230101120000||ADT^A01|MSG001|P|2.5
PID|1||123456|||DOE^JOHN||||||||||||||||||||`;

      const tree = parseHL7ToTree(messageWithEmptyFields);
      const messageTypeNode = tree[0].children![0];
      const pidSegment = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      );

      expect(pidSegment).toBeDefined();

      // Check that empty fields are still created
      const emptyField = pidSegment!.children!.find(
        (child) => child.name === "PID-4"
      );
      expect(emptyField).toBeDefined();
      expect(emptyField!.value).toBe("");
    });

    it("should handle messages with custom delimiters", () => {
      // HL7 MSH segment must start with MSH| followed by encoding characters
      // Custom delimiters should be in the encoding characters field
      const customDelimiterMessage =
        "MSH|@~\\%|SYSTEM|FACILITY|TARGET|FACILITY|20230101120000||ADT^A01|MSG001|P|2.5";
      const tree = parseHL7ToTree(customDelimiterMessage);

      expect(tree).toHaveLength(1);
      const messageTypeNode = tree[0].children![0];
      expect(messageTypeNode.delimiters!.fieldDelimiter).toBe("|");
      expect(messageTypeNode.delimiters!.componentDelimiter).toBe("@");
      expect(messageTypeNode.delimiters!.repetitionDelimiter).toBe("~");
      expect(messageTypeNode.delimiters!.subcomponentDelimiter).toBe("%");
    });

    it("should handle very long field values", () => {
      const longValue = "A".repeat(1000);
      // PID|1||123456||LongName|||||||||||||||||||||
      // PID-1=1, PID-2=empty, PID-3=123456, PID-4=empty, PID-5=LongName
      const messageWithLongValue = `MSH|^~\\&|SYSTEM|FACILITY|TARGET|FACILITY|20230101120000||ADT^A01|MSG001|P|2.5
PID|1||123456||${longValue}||||||||||||||||||||`;

      const tree = parseHL7ToTree(messageWithLongValue);
      const messageTypeNode = tree[0].children![0];
      const pidSegment = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      );
      const nameField = pidSegment!.children!.find(
        (child) => child.name === "PID-5"
      );

      expect(nameField!.value).toBe(longValue);
      expect(nameField!.value!.length).toBe(1000);
    });
  });

  describe("findNodeByPath", () => {
    let tree: HL7Node[];

    beforeEach(() => {
      tree = parseHL7ToTree(complexMessage);
    });

    it("should find root message node", () => {
      const rootPath = tree[0].path;
      const node = findNodeByPath(tree, rootPath);

      expect(node).toBeDefined();
      expect(node!.type).toBe("message");
    });

    it("should find messagetype node", () => {
      const messageTypeNode = tree[0].children![0];
      const node = findNodeByPath(tree, messageTypeNode.path);

      expect(node).toBeDefined();
      expect(node!.type).toBe("messagetype");
      expect(node!.messageType).toBe("ORU");
    });

    it("should find segment node", () => {
      const messageTypeNode = tree[0].children![0];
      const pidSegment = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      );
      const node = findNodeByPath(tree, pidSegment!.path);

      expect(node).toBeDefined();
      expect(node!.type).toBe("segment");
      expect(node!.name).toBe("PID");
    });

    it("should find field node", () => {
      const messageTypeNode = tree[0].children![0];
      const pidSegment = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      );
      const nameField = pidSegment!.children!.find(
        (child) => child.name === "PID-5"
      );
      const node = findNodeByPath(tree, nameField!.path);

      expect(node).toBeDefined();
      expect(node!.type).toBe("field");
      expect(node!.name).toBe("PID-5");
    });

    it("should return null for non-existent path", () => {
      const node = findNodeByPath(tree, "non-existent-path");
      expect(node).toBeNull();
    });
  });

  describe("searchHL7Tree", () => {
    let tree: HL7Node[];

    beforeEach(() => {
      tree = parseHL7ToTree(complexMessage);
    });

    it("should return empty results for empty search text", () => {
      const results = searchHL7Tree(tree, "", "name");
      expect(results.results).toHaveLength(0);
      expect(results.matchInfo).toHaveLength(0);
    });

    it("should search by name successfully", () => {
      const results = searchHL7Tree(tree, "PID", "name");

      expect(results.results.length).toBeGreaterThan(0);
      expect(results.matchInfo.length).toBeGreaterThan(0);

      // Should find PID segment
      const pidMatch = results.matchInfo.find((match) =>
        match.matchText.includes("PID")
      );
      expect(pidMatch).toBeDefined();
    });

    it("should search by value successfully", () => {
      const results = searchHL7Tree(tree, "SMITH", "value");

      expect(results.results.length).toBeGreaterThan(0);
      expect(results.matchInfo.length).toBeGreaterThan(0);

      // Should find the patient name
      const nameMatch = results.matchInfo.find((match) =>
        match.matchText.includes("SMITH")
      );
      expect(nameMatch).toBeDefined();
    });

    it("should search messagetype correctly", () => {
      const results = searchHL7Tree(tree, "ORU", "name");

      expect(results.results.length).toBeGreaterThan(0);

      // Should find messagetype node
      const messageTypeMatch = results.matchInfo.find(
        (match) => match.elementType === "messagetype"
      );
      expect(messageTypeMatch).toBeDefined();
    });

    it("should handle case-insensitive search", () => {
      const results1 = searchHL7Tree(tree, "smith", "value");
      const results2 = searchHL7Tree(tree, "SMITH", "value");

      expect(results1.results.length).toBe(results2.results.length);
      expect(results1.matchInfo.length).toBe(results2.matchInfo.length);
    });

    it("should find matches in components and subcomponents", () => {
      const results = searchHL7Tree(tree, "ELIZABETH", "value");

      expect(results.results.length).toBeGreaterThan(0);

      // Should find the middle name component
      const middleNameMatch = results.matchInfo.find((match) =>
        match.matchText.includes("ELIZABETH")
      );
      expect(middleNameMatch).toBeDefined();
    });

    describe("search with repetitions", () => {
      let treeWithRepeats: HL7Node[];

      beforeEach(() => {
        treeWithRepeats = parseHL7ToTree(messageWithRepeats);
      });

      it("should find matches in repeated fields", () => {
        const results = searchHL7Tree(treeWithRepeats, "555-0001", "value");

        expect(results.results.length).toBeGreaterThan(0);

        // Should find the phone number in repetitions
        const phoneMatch = results.matchInfo.find((match) =>
          match.matchText.includes("555-0001")
        );
        expect(phoneMatch).toBeDefined();
      });

      it("should find matches in multiple repetitions", () => {
        const results = searchHL7Tree(treeWithRepeats, "SSN", "value");

        expect(results.results.length).toBeGreaterThan(0);

        // Should find SSN identifier type
        const ssnMatch = results.matchInfo.find((match) =>
          match.matchText.includes("SSN")
        );
        expect(ssnMatch).toBeDefined();
      });
    });

    describe("search with subcomponents", () => {
      let treeWithSubcomponents: HL7Node[];

      beforeEach(() => {
        treeWithSubcomponents = parseHL7ToTree(messageWithSubcomponents);
      });

      it("should find matches in subcomponents", () => {
        const results = searchHL7Tree(treeWithSubcomponents, "mild", "value");

        expect(results.results.length).toBeGreaterThan(0);

        // Should find the word in subcomponent
        const mildMatch = results.matchInfo.find((match) =>
          match.matchText.includes("mild")
        );
        expect(mildMatch).toBeDefined();
      });
    });
  });

  describe("getNodeProperties", () => {
    let tree: HL7Node[];
    let pidNode: HL7Node;

    beforeEach(() => {
      tree = parseHL7ToTree(complexMessage);
      const messageTypeNode = tree[0].children![0];
      pidNode = messageTypeNode.children!.find(
        (child) => child.name === "PID"
      )!;
    });

    it("should return correct properties for segment node", () => {
      const properties = getNodeProperties(pidNode);

      expect(properties).toBeDefined();
      expect(Array.isArray(properties)).toBe(true);

      const nameProperty = properties.find((prop) => prop.name === "name");
      const typeProperty = properties.find((prop) => prop.name === "type");
      const pathProperty = properties.find((prop) => prop.name === "treePath");

      expect(nameProperty?.value).toBe("PID");
      expect(typeProperty?.value).toBe("Segment");
      expect(pathProperty?.value).toBeDefined();
    });

    it("should return correct properties for field node", () => {
      const nameField = pidNode.children!.find(
        (child) => child.name === "PID-5"
      )!;
      const properties = getNodeProperties(nameField);

      expect(properties).toBeDefined();
      expect(Array.isArray(properties)).toBe(true);

      const nameProperty = properties.find((prop) => prop.name === "name");
      const typeProperty = properties.find((prop) => prop.name === "type");

      expect(nameProperty?.value).toBe("PID-5");
      expect(typeProperty?.value).toBe("Field");
    });

    it("should return correct properties for component node", () => {
      const nameField = pidNode.children!.find(
        (child) => child.name === "PID-5"
      )!;
      if (nameField.children && nameField.children.length > 0) {
        const component = nameField.children[0];
        const properties = getNodeProperties(component);

        expect(properties).toBeDefined();
        expect(Array.isArray(properties)).toBe(true);

        const typeProperty = properties.find((prop) => prop.name === "type");
        expect(typeProperty?.value).toBe("Component");
      }
    });
  });

  describe("Edge cases", () => {
    it("should handle malformed HL7 messages gracefully", () => {
      const malformedMessage = "This is not a valid HL7 message";
      const tree = parseHL7ToTree(malformedMessage);

      expect(tree).toHaveLength(1);
      expect(tree[0].type).toBe("message");
    });

    it("should handle messages with only MSH segment", () => {
      const mshOnlyMessage =
        "MSH|^~\\&|SYSTEM|FACILITY|TARGET|FACILITY|20230101120000||ADT^A01|MSG001|P|2.5";
      const tree = parseHL7ToTree(mshOnlyMessage);

      expect(tree).toHaveLength(1);
      const messageTypeNode = tree[0].children![0];
      expect(messageTypeNode.type).toBe("messagetype");
      expect(messageTypeNode.children).toHaveLength(1); // Only MSH segment
    });
  });
});
