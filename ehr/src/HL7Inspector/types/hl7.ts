export interface HL7Delimiters {
  segmentDelimiter: string;
  fieldDelimiter: string;
  componentDelimiter: string;
  repetitionDelimiter: string;
  escapeCharacter: string;
  subcomponentDelimiter: string;
  encodingChars: string;
}

// Element type definitions
export type ElementType =
  | "message"
  | "messagetype"
  | "segment"
  | "field"
  | "component"
  | "subcomponent"
  | "repetition";

// Search match information interface
export interface SearchMatchInfo {
  path: string;
  elementId: string;
  elementType: ElementType;
  matchText: string;
  matchType: "name" | "value";
}

// Search results interface
export interface SearchResults {
  results: string[];
  matchInfo: SearchMatchInfo[];
}

export interface HL7Node {
  id: string;
  type: ElementType;
  name: string;
  value?: string;
  path: string;
  children?: HL7Node[];
  raw: string;
  parent?: string;
  description?: string;
  dataType?: string;
  // Extended properties for unified structure
  delimiters?: HL7Delimiters; // Delimiter information (stored at message/messagetype level)
  messageType?: string; // Message type (for messagetype nodes)
  eventType?: string; // Event type (for messagetype nodes)
  fieldIndex?: number; // Original field index (for field nodes)
  componentIndex?: number; // Component index (for component nodes)
  subComponentIndex?: number; // SubComponent index (for subcomponent nodes)
  repetitionIndex?: number; // Repetition index (for repeated elements)
  isRepetition?: boolean; // Whether this is a repetition element
  repeats?: boolean; // Whether this field repeats
}

export interface HL7Property {
  name: string;
  value: string;
}

export type SearchType = "name" | "value";
