import React from "react";

// Import all icon assets
import folderIcon from "./folder.gif";
import folderOpenIcon from "./folder-open.gif";
import elbowEndPlusIcon from "./elbow-end-plus.gif";
import elbowEndMinusIcon from "./elbow-end-minus.gif";
import subcomponentIcon from "./subcomponent_16x16.gif";
import segmentIcon from "./segment_16x16.gif";
import repetitionIcon from "./repetition_16x16.gif";
import pluginIcon from "./plugin.gif";
import fieldIcon from "./field_16x16.gif";
import componentIcon from "./component_16x16.gif";

// Icon name type
export type IconName =
  | "folder"
  | "folder-open"
  | "elbow-end-plus"
  | "elbow-end-minus"
  | "subcomponent"
  | "segment"
  | "repetition"
  | "plugin"
  | "field"
  | "component";

// Icon mapping
const iconMap: Record<IconName, string> = {
  folder: folderIcon,
  "folder-open": folderOpenIcon,
  "elbow-end-plus": elbowEndPlusIcon,
  "elbow-end-minus": elbowEndMinusIcon,
  subcomponent: subcomponentIcon,
  segment: segmentIcon,
  repetition: repetitionIcon,
  plugin: pluginIcon,
  field: fieldIcon,
  component: componentIcon,
};

// Icon component props
export interface IconProps {
  name: IconName;
  size?: number | string;
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

// Icon component
export const Icon: React.FC<IconProps> = ({
  name,
  size = 16,
  alt,
  className,
  style,
  onClick,
}) => {
  const iconSrc = iconMap[name];

  if (!iconSrc) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }

  const iconStyle: React.CSSProperties = {
    width: typeof size === "number" ? `${size}px` : size,
    height: typeof size === "number" ? `${size}px` : size,
    display: "inline-block",
    ...style,
  };

  return (
    <img
      src={iconSrc}
      alt={alt || name}
      className={className}
      style={iconStyle}
      onClick={onClick}
    />
  );
};

export default Icon;
