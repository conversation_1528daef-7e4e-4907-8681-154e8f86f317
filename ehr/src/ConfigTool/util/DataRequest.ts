/**
 * DataRequest.ts
 *
 * Get rule data from server
 */
import axios from "axios";

const baseUrl: string = cci.cfg.cdsBaseUrl ? cci.cfg.cdsBaseUrl : "";

const getCommonUrl = (url: string) => {
  let ret = baseUrl + "/Flask/" + url;
  return ret;
};

const getData = (url: string, formData?: object) => {
  return axios
    .get(url, formData)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log("Error:", error);
    });
};

const postData = (url: string, formData?: object) => {
  return axios
    .post(url, formData, {})
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log("Error:", error);
    });
};

export const getRuleFileData = (params: object) => {
  const url: string = "CDSRule/filedata";
  let queryUrl = getCommonUrl(url);
  return getData(queryUrl, { params });
};

export const saveRuleFileData = (params: object) => {
  const url: string = "CDSRule/filedata";
  let queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const commitRuleFileData = (params: object) => {
  const url: string = "CDSRule/commit";
  let queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const revertFileData = (params: object) => {
  const url: string = "File/revert";
  let queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const revertNotesMoverData = (params: object) => {
  const url: string = "NotesMover/revert";
  let queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const getFunctionPara1List = (type: string, keyword: string) => {
  const url: string = `${baseUrl}/Flask/CDSRule/search?type=${type}&keyword=${keyword}`;
  return getData(url);
};

export const validateRule = (rule: string) => {
  const url: string = "CDSRule/validaterule";
  let queryUrl = getCommonUrl(url);
  return postData(queryUrl, { rule: rule });
};

export const getAllNotes = () => {
  return axios.get(getCommonUrl("NotesMover/allnotes"));
};

export const getConfList = () => {
  return axios.get(getCommonUrl("NotesMover/conflist"));
};

export const getNoteRcf = (filepath: string, params?: object) => {
  return getData(getCommonUrl("NotesMover/filedata?filepath=" + filepath), {
    params,
  });
};

export const saveNoteRcf = (formData: object) => {
  return postData(getCommonUrl("NotesMover/save"), formData);
};

export const commitNoteRcf = (formData: object) => {
  return postData(getCommonUrl("NotesMover/commit"), formData);
};

export const getNoteCatTypeList = () => {
  return getData(getCommonUrl("NotesEditor/getNoteCatTypeList"));
};

export const getNoteHistory = (name: string) => {
  return getData(
    getCommonUrl("NotesEditor/getNoteHistory?name=" + encodeURI(name))
  );
};

export const readNoteTemplate = (tpl: string) => {
  return getData(
    getCommonUrl("NotesEditor/readNoteTemplate?tpl=" + encodeURI(tpl))
  );
};

export const writeNoteTemplate = (
  name: string,
  version: number,
  comment: string,
  tpldata: any
) => {
  return postData(
    getCommonUrl(
      "NotesEditor/writeNoteTemplate?name=" +
        encodeURI(name) +
        "&version=" +
        version +
        "&comment=" +
        encodeURI(comment)
    ),
    tpldata
  );
};

export const saveNoteTypes = (
  comment: string,
  notetypes: any,
  orgNoteTypes: any
) => {
  return postData(
    getCommonUrl("NotesEditor/saveNoteTypes?comment=" + encodeURI(comment)),
    { notetypes: notetypes, orgnotetypes: orgNoteTypes }
  );
};

export const getGlobalChcList = () => {
  return getData(getCommonUrl("NotesEditor/getGlobalChcList"));
};

export const readChcList = (name: string) => {
  return getData(
    getCommonUrl("NotesEditor/readChcList?name=" + encodeURI(name))
  );
};

export const writeChcList = (comment: string, chclists: any) => {
  return postData(
    getCommonUrl("NotesEditor/writeChcList?comment=" + encodeURI(comment)),
    chclists
  );
};

export const getDbiNames = () => {
  return getData(getCommonUrl("NotesEditor/getDbiNames"));
};

export const getFormatStrings = () => {
  return getData(getCommonUrl("NotesEditor/getFormatStrings"));
};

export const getGraphicsList = () => {
  return getData(getCommonUrl("NotesEditor/getGraphicsList"));
};

export const readGraphicsFile = (name: string) => {
  return getData(
    getCommonUrl("NotesEditor/readGraphicsFile?name=" + encodeURI(name))
  );
};
export const getRepList = () => {
  return getData(getCommonUrl("NotesEditor/getRepList"));
};
export const getReferralResources = () => {
  return getData(getCommonUrl("NotesEditor/getReferralResources"));
};
export const getIts = () => {
  return getData(getCommonUrl("NotesEditor/getIts"));
};
export const dumpNoteInfo = (infoType: string) => {
  return getData(
    getCommonUrl("NotesEditor/dumpNoteInfo?infoType=" + encodeURI(infoType))
  );
};

type moduleName = "notesmover" | "noteseditor" | "terminal" | "cdsrule";

export const checkEdit = (module: moduleName) => {
  return axios.get(getCommonUrl("System/checkedit"), {
    params: { module },
  });
};

export const stealLock = (module: moduleName) => {
  return axios.get(getCommonUrl("System/steallock"), {
    params: { module },
  });
};

export const releaseLock = (module: moduleName) => {
  return axios.get(getCommonUrl("System/releaselock"), {
    params: { module },
  });
};

export const commitsaved = (module: moduleName, comment: string) => {
  return axios.get(getCommonUrl("System/commitsaved"), {
    params: { module, comment },
  });
};

export const revertsaved = (module: moduleName) => {
  return axios.get(getCommonUrl("System/revertsaved"), {
    params: { module },
  });
};
