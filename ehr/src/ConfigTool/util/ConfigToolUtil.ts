// @ts-nocheck

/*
 * Utility for communicating with EHR
 */
import { atom } from "jotai";
import Config from "@cci-monorepo/config/Config";
import { checkEdit } from "./DataRequest";
export const notesEditorRefreshAtom = atom<boolean>(false);
export const cdsRuleRefreshAtom = atom<boolean>(false);
export const notesMenuEditorRefreshAtom = atom<boolean>(false);
export const terminalRefreshAtom = atom<boolean>(false);
export const terminalReOrderAtom = atom<boolean>(false);
export const summaryConfigToolRefreshAtom = atom<boolean>(false);

// Supported ConfigTool module names
const moduleNameList: string[] = [
  "cdsrule",
  "notesmenueditor",
  "noteseditor",
  "summaryconftool",
  "terminalconfig",
];

let currentModule: string = "";

/**
 * The data status for each module.
 * * Each module may have:
 *  - dirty?:boolean, default is false
 *  - saveFn?: function object, default is undefined
 */
let moduleDataStatus = {};

/**
 * Initialize / re-initialize moduleDataStatus.
 */
export const initModuleDataStatus = () => {
  moduleDataStatus = {};
  moduleNameList.forEach((module) => {
    moduleDataStatus[module] = {};
  });
  currentModule = "";
};

initModuleDataStatus();

/**
 * Get data status for given module name
 * @param moduleName
 * @returns object or undefined
 */
export const getModuleDataStatus = (moduleName: string) => {
  if (moduleNameList.includes(moduleName)) {
    return moduleDataStatus[moduleName];
  }
  return undefined;
};
/**
 * Get data status for given module name
 * @param moduleName
 * @returns object or undefined
 */
export const getModuleDataStatusByRegex = (moduleName: string) => {
  const regex = new RegExp(`${moduleName}`, "i");
  for (let i = 0; i < moduleNameList.length; ++i) {
    if (regex.test(moduleNameList[i])) {
      return moduleDataStatus[moduleNameList[i]];
    }
  }
  return undefined;
};

/**
 * For EHR DirtyLeaveWin to show Save button or not.
 * If the saveFn is available, it will show Save button
 * @param {boolean} flag -- from EHR
 * @returns {boolean}
 */
const wantSaveBtn = (flag: boolean): boolean => {
  let tmpFlag: boolean = false;
  const tmpMod = getModuleDataStatus(currentModule);
  if (tmpMod && tmpMod.saveFn) {
    tmpFlag = true;
  }
  return tmpFlag;
};

/**
 * The interface to set dirty data for EHR.
 * It holds a data dirty flag for a module, and attach the saveFn if available
 *
 * @param moduleName string
 * @param flag boolean -- data is dirty or not
 * @param saveFn {object} -- optional, save function
 */
export const setEhrDataDirty = (
  moduleName: string,
  flag: boolean,
  saveFn?: any
): void => {
  if (!moduleNameList.includes(moduleName)) {
    currentModule = "";
    return;
  }

  moduleDataStatus[moduleName] = {
    dirty: flag,
    saveFn: saveFn,
  };
  currentModule = moduleName;

  if (!Config.inDevMode) {
    const configName = Cci.RunTime.appCtnController.configname;
    Cci.util.RunTime.wantDirtySaveFuncList[configName] = wantSaveBtn;
    Cci.util.RunTime.setDirtyFlag(moduleName, flag);
  }
};

export const setCdsRuleDataDirty = (flag: boolean, saveFn?: any): void => {
  setEhrDataDirty(moduleNameList[0], flag, saveFn);
};

export const setNotesMenuEditorDataDirty = (
  flag: boolean,
  saveFn?: any
): void => {
  setEhrDataDirty(moduleNameList[1], flag, saveFn);
};

export const setNotesEditorDataDirty = (flag: boolean, saveFn?: any): void => {
  setEhrDataDirty(moduleNameList[2], flag, saveFn);
};

export const setSummaryConftoolDataDirty = (
  flag: boolean,
  saveFn?: any
): void => {
  setEhrDataDirty(moduleNameList[3], flag, saveFn);
};

export const setTerminalConfigDataDirty = (
  flag: boolean,
  saveFn?: any
): void => {
  setEhrDataDirty(moduleNameList[4], flag, saveFn);
};

const heartbeatTimerIds: { [key: string]: number } = {};

export function startHeartbeat(module: string) {
  if (!(module in heartbeatTimerIds)) {
    const doHeartbeat = () => {
      checkEdit(module)
        .then((res) => res.data.locker && Promise.reject())
        .catch(() => stopHeartbeat(module));
      heartbeatTimerIds[module] = setTimeout(doHeartbeat, 5000);
    };
    doHeartbeat();
  }
}

export function stopHeartbeat(module: string) {
  if (module in heartbeatTimerIds) {
    clearTimeout(heartbeatTimerIds[module]);
    delete heartbeatTimerIds[module];
  }
}
