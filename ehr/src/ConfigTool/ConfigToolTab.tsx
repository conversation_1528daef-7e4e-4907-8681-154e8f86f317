import React from "react";

import { SyntheticEvent } from "react";
import { SvgIcon, Tab, useTheme } from "@mui/material";
import Divider from "@cci-monorepo/common/mui-components/src/components/layout/Divider";
import { useAtomValue } from "jotai";
import {
  dirtyAtom as cdsDirty<PERSON>tom,
  pageIndicesAtom,
} from "./Cdsrule/context/CtaAtoms";
import { dirtyAtom as tcDirtyAtom } from "./TerminalConfig/context/TerminalConfigAtoms";
import { dirtyAtom as NeDirtyAtom } from "./NotesEditor/context/NeAtoms";
import { dirtyAtom as NmeDirtyAtom } from "./NotesMenuEditor/context/NmeAtoms";

/**
 * Tab props type
 *
 * @private
 *
 * @description define a type for tab props
 */
export type TabProps = {
  label: String;
  id: string;
  "aria-controls": string;
  ismaintab?: boolean | string;
  onClickTab?: (event: SyntheticEvent) => void;
  sx?: any;
  index: number;
};

const UnsavedIcon = () => {
  return (
    <SvgIcon>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        width="8px"
        height="8px"
        viewBox="0 0 8 8"
      >
        <circle fill="#3F4851" cx="5" cy="3.5" r="1.6" />
      </svg>
    </SvgIcon>
  );
};

/**
 * @description Wrapper of Tab with customized styling
 * @param props - TabProps
 * @returns Tab
 */
export default function ConfigToolTab(props: TabProps) {
  const { onClickTab, index, ...baseProps } = props;
  const theme = useTheme();
  const pageIndices = useAtomValue(pageIndicesAtom);
  const notesEditorDirty = useAtomValue(NeDirtyAtom);
  const cdsRuleDirty = useAtomValue(cdsDirtyAtom);
  const notesMenuEditorDirty = useAtomValue(NmeDirtyAtom);
  const terminalDirty = useAtomValue(tcDirtyAtom);

  const subTabStyling = {
    color: "#000000",
    fontWeight: "bold",
    fontFamily: `${theme.typography.subtitle1.fontFamily}`,
    fontSize: `${theme.typography.subtitle1.fontSize}`,
    backgroundColor: "#FFFFFF",
    "&.Mui-selected": {
      color: "#000000",
      backgroundColor: "#FFD053",
    },
    minHeight: "30px",
    height: "30px",
    textTransform: "none",
  };

  const unsaved = () => {
    switch (index) {
      case pageIndices.NOTESEDITOR:
        return notesEditorDirty;
      case pageIndices.CDSRULE:
        return cdsRuleDirty;
      case pageIndices.NOTESMENUEDITOR:
        return notesMenuEditorDirty;
      case pageIndices.TERMINALCONFIG:
        return terminalDirty;
      default:
        return false;
    }
  };

  const UnsavedTab = () => {
    if (unsaved()) {
      return (
        <Tab
          icon={<UnsavedIcon />}
          iconPosition="end"
          data-testid="cci-tab"
          {...baseProps}
          sx={[subTabStyling, props.sx]}
          onClick={onClickTab}
        />
      );
    } else {
      return (
        <Tab
          data-testid="cci-tab"
          {...baseProps}
          sx={[subTabStyling, props.sx]}
          onClick={onClickTab}
        />
      );
    }
  };

  return (
    <div>
      {UnsavedTab()}
      {!props.ismaintab ? (
        <Divider
          mx={1}
          width={2}
          height={16}
          color="#7F7F7F"
          style={{ display: "inline-block", verticalAlign: "middle" }}
        />
      ) : null}
    </div>
  );
}
