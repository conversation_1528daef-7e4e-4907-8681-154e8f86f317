/**
 * type.d.ts
 */

export interface Volume {
  num: number;
  value: string;
}

export interface Printer {
  name: string;
  device?: string;
}

export interface Bed {
  name: string;
  printer?: string[];
  room?: string;
}

export interface Userprinter {
  printer?: string;
  printerType?: string;
}

export interface Unit {
  name: string;
  printers: Userprinter[];
  bed?: Bed[];
  nobed?: string;
}

export interface Terminal {
  id: number;
  name: string;
  printers: Userprinter[];
  unitAccess?: string[];
  bedAccess?: string[];
  screenLock?: number | string;
  defaultUnit?: string;
  defaultBed?: string;
  loadBalance?: string;
  smartCard?: string;
}

export interface TcData {
  printer: Printer[];
  unit: Unit[];
  terminal: Terminal[];
  volume: Volume[];
  hashCode: string;
  preHashCode: string;
}

export interface CellParams {
  rowid: GridRowId;
  col: string;
  ind: number;
}

export interface CellParams_multi {
  rowid: GridRowId;
  col: string;
  val: string[];
}

export interface OptionItem {
  name: string;
  checked: boolean;
}

export interface MasterDetail {
  parent: OptionItem;
  children: OptionItem[];
}

export interface MasterDetail3Layer {
  parent: OptionItem;
  children: MasterDetail[];
}

export interface alertMsg {
  show: boolean;
  title: string;
  content: string;
  severity: string; //success, info, warning, error
}

export interface termRow {
  rowId: number;
  termName: string;
}

export interface termExportColumn {
  field: string;
  title: string;
  selected: boolean;
  required: boolean;
}

export interface myCell {
  rowId: number;
  field: string;
  value: string;
  myData?: any;
  selectedRows?: termRow[];
}

export interface multiRets {
  ret: any[];
}

export interface errorMsg {
  id: number;
  line: number;
  column: string;
  value: string;
  issue: string;
}

export interface MyLog {
  title: string;
  subtitle: string;
  msg: string;
  Data: errorMsg[];
}
