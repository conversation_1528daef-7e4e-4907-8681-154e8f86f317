import { atom } from "jotai";
import {
  Terminal,
  TcData,
  CellParams,
  CellParams_multi,
  termRow,
  termExportColumn,
  MyLog,
} from "../type";

export const isSmartCardEditingEnabledAtom = atom<boolean>(false);

export const preHashCodeAtom = atom<string>("");

export const showAlertAtom = atom<string>("");

export const showInfoDialogAtom = atom<string>("");

export const showTerminalConfigConfirmAtom = atom<string>("");

export const exportedColumnsAtom = atom<termExportColumn[]>([
  { field: "name", title: "Name", selected: true, required: true },
  { field: "printers", title: "Printer", selected: true, required: false },
  { field: "unitAccess", title: "Units", selected: true, required: false },
  { field: "bedAccess", title: "Beds", selected: true, required: false },
  {
    field: "defaultUnit",
    title: "Default Unit",
    selected: true,
    required: false,
  },
  {
    field: "defaultBed",
    title: "Default Bed",
    selected: true,
    required: false,
  },
  {
    field: "screenLock",
    title: "Screen Lock",
    selected: true,
    required: false,
  },
  { field: "smartCard", title: "Smart Card", selected: true, required: false },
]);

export const screenLocksAtom = atom<string[]>([
  "0",
  "ON",
  "1",
  "2",
  "3",
  "4",
  "5",
  "10",
  "15",
  "20",
  "25",
  "30",
  "45",
  "60",
]);

export const smartCardOptionsAtom = atom<string[]>([
  "ENFORCED",
  "DISABLED",
  "OPTIONAL",
]);

export const exportUnitMapAtom = atom(null);
export const exportBedMapAtom = atom(null);
export const exportUnitMapRevAtom = atom(null);
export const exportBedMapRevAtom = atom(null);
export const exportDataActionAtom = atom(false);
export const exportDataAtom = atom(false);
export const exportSelectedDataAtom = atom(false);

export const showLogAtom = atom(false);

export const logContentAtom = atom<MyLog>({
  title: "",
  subtitle: "",
  msg: "",
  Data: [],
});

export const delTerminalsActionAtom = atom(false);

export const alllTerminalsAtom = atom<string[]>([]);

export const dataAtom = atom<TcData | null>(null);
export const oldDataAtom = atom<TcData | null>(null);
export const loadingAtom = atom<boolean>(false);
export const dirtyAtom = atom<boolean>(false);
export const needCommitOrRevertAtom = atom<boolean>(false);
export const retValueAtom = atom<CellParams>({ rowid: -1, col: "", ind: -1 });
export const retValueAtom_multi = atom<CellParams_multi>({
  rowid: -1,
  col: "",
  val: [],
});
export const retValueAtom_multi3Layers = atom<CellParams_multi>({
  rowid: -1,
  col: "",
  val: [],
});
//for refresh grid when a terminal added
export const terminalAddedAtom = atom<boolean>(false);
export const terminalEditAtom = atom<boolean>(false);
export const unselectedTerminalsAtom = atom<Terminal[]>([]);
export const selectedRowsAtom = atom<termRow[]>([]);
export const quickTreeViewSelectedAtom = atom<string[]>([]);
export const quickTreeViewExpandedAtom = atom<string[]>([]);
export const isEditAtom = atom<boolean>(false);
export const isStealLockableAtom = atom<boolean>(false);
// Flag to trigger loading rule file data from the server for the List
export const refreshFlagAtom = atom<boolean>(false);
export const doSaveAtom = atom<boolean>(false);
export const doCommitAtom = atom<boolean>(false);
export const doRevertAtom = atom<boolean>(false);
export const doStealLockAtom = atom<boolean>(false);
export const switchModeAtom = atom<boolean>(false);
export const clearSelectedRowsAtom = atom<boolean>(false);
export const importedRowsAtom = atom<number[]>([]);
export const importDataAtom = atom<boolean>(false);
export const selectedGridRowsAtom = atom<any[]>([]);
