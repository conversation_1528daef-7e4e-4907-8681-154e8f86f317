/**
 * TcData.ts
 *
 * Get terminal data from server
 */
import axios from "axios";

import { Volume, Printer, Unit, Terminal, TcData, Userprinter } from "../type";

const baseUrl: string = cci.cfg.cdsBaseUrl ? cci.cfg.cdsBaseUrl : "";

const getCommonUrl = (url: string) => {
  const ret = baseUrl + "/Flask/" + url;
  return ret;
};

const getData = (url: string, formData?: object) => {
  return axios
    .get(url, formData)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.error("Error:", error);
    });
};

const postData = (url: string, formData?: object) => {
  return axios
    .post(url, formData, {})
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.error("Error:", error);
    });
};

export const getFileData = (params?: object) => {
  const url: string = "RawFile/data?module=terminal&filename=SystemConfig";
  const queryUrl = getCommonUrl(url);
  return getData(queryUrl, { params });
};

export const getBoottabData = () => {
  const url: string = "RawFile/data?module=boottab&filename=boottab";
  const queryUrl = getCommonUrl(url);
  return getData(queryUrl);
};

export const saveCommitFile = (params: object) => {
  const url: string = "File/save";
  const queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const saveFile = (params: object) => {
  const url: string = "File/saveonly";
  const queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const commitFile = (params: object) => {
  const url: string = "File/commitonly";
  const queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

export const revertFile = (params: object) => {
  //const url: string = "RawFile/revert";
  const url: string = "File/revert";
  const queryUrl = getCommonUrl(url);
  return postData(queryUrl, params);
};

const getArray = (
  arr: string[],
  data: any,
  prop: string,
  startIdx: number,
  len?: number
) => {
  if (data[prop] === undefined) {
    data[prop] = [];
  }
  if (len) {
    for (let i = startIdx; arr && i < startIdx + len; ++i) {
      data[prop].push(arr[i]);
    }
  } else {
    for (let i = startIdx; arr && i < arr.length; ++i) {
      data[prop].push(arr[i]);
    }
  }
};

export const parseBoottab = (str: string) => {
  let termArr: string[] = [];
  if (!str) {
    return termArr;
  }

  let arr = str.split("\n");
  for (let i = 0; i < arr.length; ++i) {
    let line = arr[i].trim();
    if (!line || line.startsWith("#")) {
      continue;
    }

    const tmpArr = line.split(/\s+/);
    if (tmpArr.length > 1 && tmpArr[1].toUpperCase() === "T") {
      termArr.push(tmpArr[0]);
    }
  }
  return termArr;
};

const doTerminal = (str: string, id: number): Terminal => {
  const reName = /^\s*TERMINAL\s+/;
  const rePrinter = /^\s*TERMINAL_PRINTER\s+/;
  const reUnit = /^\s*UNIT_ACCESS\s+/;
  const reBed = /^\s*BED_ACCESS\s+/;
  const reDefaultUnit = /^\s*DEFAULT_UNIT\s+/;
  const reDefaultBed = /^\s*DEFAULT_BED\s+/;
  const reLock = /^\s*TERMINAL_SCREEN_LOCK\s+/;
  let term: Terminal = {
    id: id,
    name: "",
    printers: [],
    smartCard: "OPTIONAL",
  };
  let arr = str.split("\n");
  for (let i = 0; i < arr.length; ++i) {
    let line = arr[i].trim();
    if (line.length) {
      const tmpArr = line.split(/\s+/);
      if (line.match(reName)) {
        if (tmpArr && tmpArr.length === 2) {
          term.name = tmpArr[1];
        }
      } else if (line.match(rePrinter)) {
        for (let i = 1; i < tmpArr.length; i += 2) {
          let up: Userprinter = { printerType: tmpArr[i] };
          if (tmpArr[i + 1]) {
            up.printer = tmpArr[i + 1];
          }
          term.printers.push(up);
        }
      } else if (line.match(reUnit) && tmpArr.length > 1) {
        getArray(tmpArr, term, "unitAccess", 1, tmpArr.length - 1);
      } else if (line.match(reBed) && tmpArr.length > 1) {
        getArray(tmpArr, term, "bedAccess", 1, tmpArr.length - 1);
      } else if (line.match(reDefaultUnit) && tmpArr.length > 1) {
        term.defaultUnit = tmpArr[1];
      } else if (line.match(reDefaultBed) && tmpArr.length > 1) {
        term.defaultBed = tmpArr[1];
      } else if (line.match(reLock) && tmpArr.length > 1) {
        term.screenLock = Number.isNaN(parseInt(tmpArr[1]))
          ? tmpArr[1]
          : parseInt(tmpArr[1]);
      }
    }
  }
  return term;
};

const doUnit = (str: string): Unit => {
  const reName = /^\s*UNIT\s+/;
  const rePrinter = /^\s*UNIT_PRINTER\s+/;
  const reNobed = /^\s*NOBED\s+/;
  const reBed = /^\s*BED\s*.*/;
  let unit: Unit = { name: "", printers: [] };
  let arr = str.split("\n");
  for (let i = 0; i < arr.length; ++i) {
    let line = arr[i].trim();
    if (line.length) {
      const tmpArr = line.split(/\s+/);
      if (line.match(reName)) {
        if (tmpArr && tmpArr.length === 2) {
          unit.name = tmpArr[1];
        }
      } else if (line.match(rePrinter) && tmpArr.length > 1) {
        for (let i = 1; i < tmpArr.length; i += 2) {
          let up: Userprinter = { printerType: tmpArr[i] };
          if (tmpArr[i + 1]) {
            up.printer = tmpArr[i + 1];
          }
          unit.printers.push(up);
        }
      } else if (line.match(reBed) && tmpArr.length > 1) {
        unit.bed = unit.bed || [];
        if (tmpArr.length === 3) {
          unit.bed.push({ name: tmpArr[1], room: tmpArr[2] });
        } else {
          unit.bed.push({ name: tmpArr[1] });
        }
      } else if (line.match(reNobed) && tmpArr.length > 1) {
        unit.nobed = tmpArr[1];
      }
    }
  }
  return unit;
};

const doPrinter = (str: string): Printer => {
  const reName = /^\s*PRINTER\s+/;
  let printer: Printer = { name: "" };
  let arr = str.split("\n");
  for (let i = 0; i < arr.length; ++i) {
    let line = arr[i].trim();
    if (line.length) {
      const tmpArr = line.split(/\s+/);
      if (line.match(reName)) {
        if (tmpArr && tmpArr.length === 3) {
          printer.name = tmpArr[1];
          printer.device = tmpArr[2];
        }
      }
    }
  }
  return printer;
};

const doVolume = (str: string): Volume => {
  const reName = /^\s*VOLUME\s+/;
  let volume: Volume = { num: -1, value: "" };
  let arr = str.split("\n");
  for (let i = 0; i < arr.length; ++i) {
    let line = arr[i].trim();
    if (line.length) {
      const tmpArr = line.split(/\s+/);
      if (line.match(reName)) {
        if (tmpArr && tmpArr.length === 3) {
          volume.num = parseInt(tmpArr[1]);
          volume.value = tmpArr[2];
        }
      }
    }
  }
  return volume;
};

export const getTcData = (filedata: string, hashcode: string): TcData => {
  const re = /(.*(?:\n(?!^(TERMINAL|VOLUME|PRINTER|UNIT)\s).*)*)/gm;
  const reterminals = /^\s*TERMINAL\s+.+/;
  const reunit = /^\s*UNIT\s+.+/;
  const reprinter = /^\s*PRINTER\s+.+/;
  const revolume = /^\s*VOLUME\s+.+/;
  let terminalId: number = 1;

  let data: TcData = {
    volume: [],
    printer: [],
    unit: [],
    terminal: [],
    hashCode: hashcode,
    preHashCode: "",
  };

  const fcontent: string = filedata;
  for (const match of Array.from(fcontent.matchAll(re))) {
    if (!match.length || !match[0].length) {
      continue;
    }
    if (match[1].match(reterminals)) {
      data.terminal.push(doTerminal(match[1], terminalId++));
    } else if (match[1].match(reunit)) {
      data.unit.push(doUnit(match[1]));
    } else if (match[1].match(reprinter)) {
      data.printer.push(doPrinter(match[1]));
    } else if (match[1].match(revolume)) {
      data.volume.push(doVolume(match[1]));
    }
  }
  return data;
};

const volume2str = (arr: Volume[]) => {
  let str = "";
  for (let i = 0; arr && i < arr.length; ++i) {
    str += `VOLUME ${arr[i].num} ${arr[i].value}\n`;
  }
  return str;
};

const printer2str = (arr: Printer[]) => {
  let str = "";
  for (let i = 0; arr && i < arr.length; ++i) {
    str += `PRINTER ${arr[i].name}\t\t${arr[i].device}\n`;
  }
  return str;
};

const unit2str = (arr: Unit[]) => {
  let str = "";
  for (let i = 0; arr && i < arr.length; ++i) {
    const d = arr[i];
    str += `UNIT ${d.name}\n`;
    if (d.printers && d.printers.length) {
      str += "\tUNIT_PRINTER";
      for (let j = 0; j < d.printers.length; ++j) {
        str += ` ${d.printers[j].printerType} ${d.printers[j].printer}`;
      }
      str += "\n";
    }
    if (d.bed && d.bed.length) {
      for (let j = 0; j < d.bed.length; ++j) {
        if (d.bed[j].room) {
          str += `\tBED ${d.bed[j].name} ${d.bed[j].room}\n`;
        } else {
          str += `\tBED ${d.bed[j].name}\n`;
        }
      }
    }
    if (d.nobed) {
      str += `\tNOBED ${d.nobed}\n`;
    }
  }
  return str;
};

const terminal2str = (arr: Terminal[]) => {
  let str = "";
  for (let i = 0; arr && i < arr.length; ++i) {
    const d = arr[i];
    str += `TERMINAL ${d.name}\n`;
    for (let j = 0; d.printers && j < d.printers.length; ++j) {
      str += j === 0 ? `\tTERMINAL_PRINTER` : "";
      str += ` ${d.printers[j].printerType} ${d.printers[j].printer}`;
      str += j === d.printers.length - 1 ? "\n" : "";
    }
    for (let j = 0; d.unitAccess && j < d.unitAccess.length; ++j) {
      str += j === 0 ? `\tUNIT_ACCESS` : "";
      str += ` ${d.unitAccess[j]}`;
      str += j === d.unitAccess.length - 1 ? "\n" : "";
    }
    for (let j = 0; d.bedAccess && j < d.bedAccess.length; ++j) {
      str += j === 0 ? `\tBED_ACCESS` : "";
      str += ` ${d.bedAccess[j]}`;
      str += j === d.bedAccess.length - 1 ? "\n" : "";
    }
    str += d.defaultUnit ? `\tDEFAULT_UNIT ${d.defaultUnit}\n` : "";
    str += d.defaultBed ? `\tDEFAULT_BED ${d.defaultBed}\n` : "";
    str +=
      Number.isInteger(d.screenLock) || typeof d.screenLock === "string"
        ? `\tTERMINAL_SCREEN_LOCK ${d.screenLock}\n`
        : "";
  }
  return str;
};

export const jsonData2String = (tcData: TcData) => {
  let str = "";
  str += volume2str(tcData.volume);
  str += "\n";
  str += printer2str(tcData.printer);
  str += "\n";
  str += unit2str(tcData.unit);
  str += "\n";
  str += terminal2str(tcData.terminal);
  return str;
};

export const createUnitBedMap = (tcData: TcData): any => {
  let ret: any = { ret: [] };
  let unitMap = new Map();
  let bedMap = new Map();
  let unitMapRev = new Map();
  let bedMapRev = new Map();
  let units = tcData.unit;
  unitMap.set("ALL", "LocalCampus:ALL");
  bedMap.set("ALL", "LocalCampus:ALL:ALL");
  unitMapRev.set("LocalCampus:ALL", "ALL");
  bedMapRev.set("LocalCampus:ALL:ALL", "ALL");
  units.forEach((ut) => {
    let preStr = "";
    if (ut.name.split(":").length === 1) {
      preStr = "LocalCampus:" + ut.name;
    } else {
      preStr = ut.name;
    }
    if (!unitMap.has(ut.name)) {
      unitMap.set(ut.name, preStr);
      unitMapRev.set(preStr, ut.name);
    }
    ut.bed?.forEach((bd) => {
      if (bd.name.split(":").length === 1) {
        preStr = "LocalCampus:" + ut.name + ":" + bd.name;
      } else if (bd.name.split(":").length === 2) {
        preStr = bd.name + ":ALL";
      } else {
        preStr = bd.name;
      }
      if (!bedMap.has(bd.name)) {
        bedMap.set(bd.name, preStr);
        bedMapRev.set(preStr, bd.name);
      }
    });
  });
  ret[0] = unitMap;
  ret[1] = bedMap;
  ret[2] = unitMapRev;
  ret[3] = bedMapRev;
  return ret;
};
