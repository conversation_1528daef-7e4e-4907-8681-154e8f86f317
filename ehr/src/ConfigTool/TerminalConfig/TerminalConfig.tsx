import { useEffect } from "react";
import { useAtom } from "jotai";
import { ThemeProvider, styled } from "@mui/material/styles";
import EhrTheme from "../../theme/theme";
import TerminalConfigAppBar from "./components/appbar/TerminalConfigAppBar";
import TcMain from "./components/TcMain";
import { isEditAtom } from "./context/TerminalConfigAtoms";
import { startHeartbeat, stopHeartbeat } from "../util/ConfigToolUtil";
import { releaseLock } from "../util/DataRequest";

const Root = styled("main")(({ theme }) => ({
  position: "relative",
  height: "95%",
  width: "100%",
}));

export default function TerminalConfig() {
  const [isEdit, setIsEdit] = useAtom(isEditAtom);

  useEffect(() => {
    return () => {
      setIsEdit(false);
      stopHeartbeat("terminal");
      releaseLock("terminal");
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isEdit) startHeartbeat("terminal");
    else stopHeartbeat("terminal");
  }, [isEdit]);

  return (
    <ThemeProvider theme={EhrTheme}>
      <Root>
        <TerminalConfigAppBar />
        <TcMain />
      </Root>
    </ThemeProvider>
  );
}
