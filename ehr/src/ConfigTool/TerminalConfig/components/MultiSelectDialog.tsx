import * as React from "react";
import { Box } from "@mui/material";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { ScopeTabDialog } from "@cci-monorepo/common";
import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";
import TerminalConfigButton from "./buttons/TerminalConfigButton";
import { useAtom } from "jotai/react";
import {
  dirtyAtom,
  retValueAtom_multi,
  quickTreeViewSelectedAtom,
  quickTreeViewExpandedAtom,
} from "../context/TerminalConfigAtoms";
import QuickTreeView from "./QuickTreeView";

interface IQuickTreeItemProps {
  id: string;
  level: number;
  label: string;
  parent?: IQuickTreeItemProps;
  children?: IQuickTreeItemProps[];
  expanded: string[];
  selected: string[];
  style?: any;
}

const MultiSelectDialogStyle = {
  "& .MuiDialog-paper": {
    width: 600,
    minHeight: 700,
    maxHeight: 900,
    backgroundColor: "rgb(227, 234, 245)",
    overflowY: "visible",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

export const MultiSelectDialog = (props: any) => {
  const title = props.title;
  const subtitle = props.subtitle;
  const onClose = props.onClose;
  const open = props.open;
  const selected = props.selected;
  const propOpened = props.propOpened;
  const [retValue_multi, setRetValue_multi] = useAtom(retValueAtom_multi);
  const [, setDirty] = useAtom(dirtyAtom);
  const [quickTreeViewSelected, setQuickTreeViewSelected] = useAtom(
    quickTreeViewSelectedAtom
  );
  const [, setQuickTreeViewExpanded] = useAtom(quickTreeViewExpandedAtom);
  const nodes: IQuickTreeItemProps[] = props.nodes;

  React.useEffect(() => {
    setQuickTreeViewSelected(selected);
    setQuickTreeViewExpanded(propOpened);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    setQuickTreeViewSelected(selected);
    setQuickTreeViewExpanded(propOpened);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [retValue_multi, selected, propOpened]);

  const handleOK = () => {
    onClose();
    let uts: any = [];
    for (let i = 0; i < nodes.length; i++) {
      let parent = nodes[i];
      let ind = quickTreeViewSelected.indexOf(parent.id);
      if (ind >= 0) {
        uts.push(parent.label);
        continue;
      } else {
        uts = handleOK_Children(parent, uts);
      }
    }
    setRetValue_multi({
      rowid: retValue_multi.rowid,
      col: retValue_multi.col,
      val: uts,
    });
    setDirty(true);
  };

  const handleOK_Children = (
    parent: IQuickTreeItemProps,
    uts: string[]
  ): string[] => {
    let children = parent.children || [];
    for (let i = 0; i < children.length; i++) {
      let ind = quickTreeViewSelected.indexOf(children[i].id);
      if (ind >= 0) {
        uts.push(children[i].label);
      }
    }
    return uts;
  };

  const handleCancel = () => {
    onClose();
  };

  const uncheckAll = () => {
    setQuickTreeViewSelected([]);
  };

  const checkAll = () => {
    let newQuickTreeViewSelected: string[] = [];
    for (let i = 0; i < nodes.length; i++) {
      newQuickTreeViewSelected.push(nodes[i].id);
      newQuickTreeViewSelected = checkAllChildren(
        nodes[i],
        newQuickTreeViewSelected
      );
    }
    setQuickTreeViewSelected(newQuickTreeViewSelected);
  };

  const checkAllChildren = (
    parent: IQuickTreeItemProps,
    newQuickTreeViewSelected: string[]
  ): string[] => {
    let children = parent.children || [];
    for (let i = 0; i < children.length; i++) {
      let child = children[i];
      newQuickTreeViewSelected.push(child.id);
      newQuickTreeViewSelected = checkAllChildren(
        child,
        newQuickTreeViewSelected
      );
    }
    return newQuickTreeViewSelected;
  };

  const expandAll = () => {
    let newQuickTreeViewExpanded: string[] = [];
    for (let i = 0; i < nodes.length; i++) {
      newQuickTreeViewExpanded.push(nodes[i].id);
      newQuickTreeViewExpanded = expandAllChildren(
        nodes[i],
        newQuickTreeViewExpanded
      );
    }
    setQuickTreeViewExpanded(newQuickTreeViewExpanded);
  };

  const expandAllChildren = (
    parent: IQuickTreeItemProps,
    newQuickTreeViewExpanded: string[]
  ): string[] => {
    let children = parent.children || [];
    for (let i = 0; i < children.length; i++) {
      let child = children[i];
      let childrenNext = child.children || [];
      if (childrenNext.length > 0) {
        newQuickTreeViewExpanded.push(child.id);
        newQuickTreeViewExpanded = checkAllChildren(
          child,
          newQuickTreeViewExpanded
        );
      }
    }
    return newQuickTreeViewExpanded;
  };

  const CollapseAll = () => {
    setQuickTreeViewExpanded([]);
  };

  return (
    <Box>
      <ScopeTabDialog
        open={open}
        sx={MultiSelectDialogStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        onClose={handleCancel}
      >
        <DialogTitle
          style={{
            cursor: "move",
            height: "50px",
            color: "white",
            backgroundColor: "black",
            display: "flex",
            alignItems: "center",
          }}
        >
          {title}
          <Button
            size="small"
            variant="outlined"
            onClick={handleCancel}
            sx={{
              fontWeight: "bold",
              fontSize: "14px",
              marginLeft: "450px",
              backgroundColor: "white",
              color: "black",
              textTransform: "none",
            }}
          >
            Close
          </Button>
        </DialogTitle>
        <DialogContent>
          <Box
            sx={{
              height: "700px",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "white",
              padding: "15px",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            {subtitle}
            <Box
              sx={{
                height: "650px",
                marginTop: "20px",
                border: 1,
                display: "flex",
                flexDirection: "row",
                backgroundColor: "white",
                overflowY: "auto",
                padding: "15px",
              }}
            >
              <Box
                sx={{
                  overflowY: "auto",
                  display: "flex",
                  alignItems: "left",
                  width: "400px",
                  heigth: "600px",
                  fontSize: "16px",
                  fontWeight: "light",
                }}
              >
                {nodes.length > 0 && (
                  <QuickTreeView
                    id="root"
                    allTreeData={nodes}
                    treeData={nodes}
                  />
                )}
              </Box>
              <Box
                sx={{
                  display: "flex",
                  border: 0,
                  padding: "2px",
                  flexDirection: "column",
                  alignItems: "center",
                  width: "cal(100% - 400px)",
                  heigth: "100%",
                }}
              >
                <Button sx={{ mb: "2px", width: "100px" }} onClick={uncheckAll}>
                  Uncheck All
                </Button>
                <Button sx={{ mb: "50px", width: "100px" }} onClick={checkAll}>
                  CheckAll
                </Button>
                <Button sx={{ mb: "2px", width: "100px" }} onClick={expandAll}>
                  Expand All
                </Button>
                <Button
                  sx={{ mb: "2px", width: "100px" }}
                  onClick={CollapseAll}
                >
                  Collapse All
                </Button>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text="Cancel" handleClick={handleCancel} />
          <TerminalConfigButton text="OK" handleClick={handleOK} />
        </DialogActions>
      </ScopeTabDialog>
    </Box>
  );
};
