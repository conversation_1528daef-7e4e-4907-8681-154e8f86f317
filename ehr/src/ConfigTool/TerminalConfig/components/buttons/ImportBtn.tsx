import React, { MutableRefObject, useRef } from "react";
import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { useAtom, useAtomValue, useSetAtom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import { cloneDeep } from "lodash";
import {
  loading<PERSON>tom,
  dirtyAtom,
  dataAtom,
  exportedColumnsAtom,
  exportUnitMapRevAtom,
  exportBedMapRevAtom,
  screenLocksAtom,
  smartCardOptionsAtom,
  showLogAtom,
  logContentAtom,
  isSmartCardEditingEnabledAtom,
  isEditAtom,
  importedRowsAtom,
  importDataAtom,
} from "../../context/TerminalConfigAtoms";

import {
  MyLog,
  TcData,
  Terminal,
  Unit,
  errorMsg,
  Userprinter,
} from "../../type";

function ImportIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M17.5 4.5c-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .65.73.45.75.45C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.41.21.75-.19.75-.45V6c-1.49-1.12-3.63-1.5-5.5-1.5zm3.5 14c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z" />
    </SvgIcon>
  );
}

export default function ImportBtn() {
  const [loading] = useAtom(loadingAtom);
  const isEdit = useAtomValue(isEditAtom);
  const [, setDirty] = useAtom(dirtyAtom);
  const [data, setData] = useAtom(dataAtom);
  const [screenLocks] = useAtom(screenLocksAtom);
  const [smartCardOptions] = useAtom(smartCardOptionsAtom);
  const [exportedColumns] = useAtom(exportedColumnsAtom);
  const [exportUnitMapRev] = useAtom(exportUnitMapRevAtom);
  const [exportBedMapRev] = useAtom(exportBedMapRevAtom);
  const [isSmartCardEditingEnabled] = useAtom(isSmartCardEditingEnabledAtom);
  const [, setLoading] = useAtom(loadingAtom);
  const [, setShowLog] = useAtom(showLogAtom);
  const [logContent, setLogContent] = useAtom(logContentAtom);
  const setImportedRows = useSetAtom(importedRowsAtom);
  const [importData, setImportData] = useAtom(importDataAtom);

  const inputRef: MutableRefObject<any> = useRef(null);

  const handleClickOpen = (event: any) => {
    inputRef.current.click();
    event.currentTarget.blur();
  };

  const handleFileReader = (e: any) => {
    let termsAdded = 0,
      termsChanged = 0,
      termsIgnored = 0,
      termsWrong = 0;
    let changeList: string[] = [];
    let clonedTcData = cloneDeep(data) as TcData;
    let nameValue = new Map();
    clonedTcData.terminal.forEach((tn) => {
      nameValue.set(tn.name, tn);
    });
    let exportColMap = new Map();
    let cols2import = new Map();
    let cols2importInd = new Map();
    let maxImportCsvErrors = 50;
    let errmsg = "";
    let errorMsgArrHeader: errorMsg[] = [];
    let errorMsgArrValue: errorMsg[] = [];
    let errorObj: errorMsg;
    let field = "";
    let nameIndex = -1;
    let isAdd = false;
    let isIgnored = true;
    let isWrong = false;
    let currRow: Terminal;
    let cloneRow: Terminal;

    exportedColumns.forEach((col) => {
      exportColMap.set(col.title, col.field);
    });
    let localUnitMap = new Map<string, Unit>();
    let allUnits: Unit[] = clonedTcData.unit || [];
    allUnits.forEach((ut) => {
      if (ut.name.indexOf(":") < 0) {
        localUnitMap.set(ut.name, ut);
      }
    });

    try {
      const fileObj = e.target.files && e.target.files[0];
      if (!fileObj) {
        return;
      }
      let reader = new FileReader();
      reader.onload = function (e: any) {
        try {
          let content = e.target.result as string;
          let arrLines = content.split("\n");
          let uMapRev = exportUnitMapRev || new Map();
          let bMapRev = exportBedMapRev || new Map();
          let lineNum = 0;

          for (let j = 0; j < arrLines.length; j++) {
            let row = arrLines[j];
            if (row.length === 0) {
              //skip blank line
              continue;
            }
            if (lineNum > 0 && errorMsgArrHeader.length > 0) {
              //if header in file is wrong, stop importing data.
              throw Error("oh, crap.");
            }
            if (lineNum === 0) {
              lineNum++;
              let arrHeader = row.split(",");
              if (
                arrHeader.length < 1 ||
                arrHeader.length > exportedColumns.length
              ) {
                errmsg =
                  "Found " +
                  arrHeader.length +
                  " fields in header line, expected between 1 and " +
                  exportedColumns.length;
                if (errorMsgArrHeader.length < maxImportCsvErrors) {
                  errorMsgArrHeader.push({
                    id: 0,
                    line: lineNum,
                    column: "",
                    value: "",
                    issue: errmsg,
                  } as errorMsg);
                } else {
                  throw Error("oh, crap.");
                }
              }
              let ind = 0;
              for (let i = 0; i < arrHeader.length; i++) {
                let hd = arrHeader[i];
                let header = hd
                  .replace(/"/g, "")
                  .replace(/\r/g, "")
                  .replace(/\n/g, "");
                if (!exportColMap.has(header)) {
                  errmsg = "Unknown column '" + header + "' in header line";
                  if (errorMsgArrHeader.length < maxImportCsvErrors) {
                    errorObj = {
                      id: 0,
                      line: lineNum,
                      column: "",
                      value: "",
                      issue: errmsg,
                    } as errorMsg;
                    errorMsgArrHeader.push(errorObj);
                  } else {
                    throw Error("oh, crap.");
                  }
                } else {
                  field = exportColMap.get(header);
                  if (cols2import.has(field)) {
                    errmsg = "Repeated column '" + header + "'in header line";
                    if (errorMsgArrHeader.length < maxImportCsvErrors) {
                      errorMsgArrHeader.push({
                        id: 0,
                        line: lineNum,
                        column: "",
                        value: "",
                        issue: errmsg,
                      });
                    } else {
                      throw Error("oh, crap.");
                    }
                  }
                  cols2import.set(field, header);
                  cols2importInd.set(ind, field);
                  if (field.toLowerCase() === "name") {
                    nameIndex = ind;
                  }
                }
                ind++;
              }
              if (!cols2import.has("name")) {
                errmsg = "Importing requires column 'name'";
                if (errorMsgArrHeader.length < maxImportCsvErrors) {
                  errorMsgArrHeader.push({
                    id: 0,
                    line: lineNum,
                    column: "",
                    value: "",
                    issue: errmsg,
                  });
                } else {
                  throw Error("oh, crap.");
                }
              }
              if (errorMsgArrHeader.length > 0) {
                throw Error("oh, crap.");
              }
            } else {
              //lineNum > 0, data
              lineNum++;
              let arrData = row.split(",");
              if (arrData.length !== cols2import.size) {
                errmsg =
                  "Found " +
                  arrData.length +
                  " fields in line, expected " +
                  cols2import.size;
                if (errorMsgArrValue.length < maxImportCsvErrors) {
                  errorObj = {
                    id: 0,
                    line: lineNum,
                    column: "",
                    value: "",
                    issue: errmsg,
                  };
                  errorMsgArrValue.push(errorObj);
                } else {
                  throw Error("oh, crap.");
                }
              }
              //we first fetch the terminal from TcData to edit. If not found, create a new terminal.
              let header = "";
              let field = "";
              isAdd = false;
              isIgnored = true;
              isWrong = false;
              let strArr: string[];
              let obj = arrData[nameIndex]
                .replace(/"/g, "")
                .replace(/\r/g, "")
                .replace(/\n/g, "");
              field = cols2importInd.get(nameIndex);
              header = cols2import.get(field);
              if (obj === "") {
                isWrong = true;
                errmsg = header + " may not be empty";
                if (errorMsgArrValue.length < maxImportCsvErrors) {
                  errorMsgArrValue.push({
                    id: 0,
                    line: lineNum,
                    column: field,
                    value: obj,
                    issue: errmsg,
                  });
                } else {
                  throw Error("oh, crap.");
                }
              }
              if (obj.length > 32) {
                isWrong = true;
                errmsg =
                  header + " '" + obj + "' may not be longer than 32 chars.";
                if (errorMsgArrValue.length < maxImportCsvErrors) {
                  errorMsgArrValue.push({
                    id: 0,
                    line: lineNum,
                    column: field,
                    value: obj,
                    issue: errmsg,
                  });
                } else {
                  throw Error("oh, crap.");
                }
              }
              let reg = new RegExp("^[0-9a-z][.\\-_a-z0-9]*$", "i");
              if (!reg.test(obj)) {
                isWrong = true;
                errmsg = header + " '" + obj + "' is in bad format.";
                if (errorMsgArrValue.length < maxImportCsvErrors) {
                  errorMsgArrValue.push({
                    id: 0,
                    line: lineNum,
                    column: field,
                    value: obj,
                    issue: errmsg,
                  });
                } else {
                  throw Error("oh, crap.");
                }
              }
              currRow = nameValue.get(obj);
              if (!currRow) {
                cloneRow = {
                  id: -1,
                  name: obj,
                  printers: [],
                  unitAccess: [],
                  bedAccess: [],
                  screenLock: 0,
                  defaultUnit: "",
                  defaultBed: "",
                  loadBalance: "",
                  smartCard: "",
                };
                isAdd = true;
              } else {
                cloneRow = cloneDeep(currRow);
              }

              for (let i = 0; i < arrData.length; i++) {
                let obj = arrData[i]
                  .replace(/"/g, "")
                  .replace(/\r/g, "")
                  .replace(/\n/g, "");
                field = cols2importInd.get(i);
                header = cols2import.get(field);
                let hasLocalCampus = false;
                switch (field) {
                  case "name":
                    break;
                  case "printers":
                    strArr = obj.split("|");
                    if (strArr.length > 0 && strArr[0].length > 0) {
                      let exists = false;
                      let allPrinters = clonedTcData.printer || [];
                      for (let i = 0; i < allPrinters.length; i++) {
                        if (
                          allPrinters[i].name.toUpperCase() ===
                          strArr[0].toUpperCase()
                        ) {
                          exists = true;
                          break;
                        }
                      }
                      if (!exists) {
                        isWrong = true;
                        errmsg = "Unknown printer '" + obj + "'";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        } else {
                          throw Error("oh, crap.");
                        }
                      }
                      if (
                        cloneRow.printers.length === 0 ||
                        cloneRow.printers[0].printer?.toUpperCase() !==
                          obj.toUpperCase()
                      ) {
                        isIgnored = false;
                      }
                    } else {
                      if (cloneRow.printers.length > 0) {
                        let prn = cloneRow.printers[0];
                        if (prn.printer && prn.printer.length > 0) {
                          isIgnored = false;
                        }
                      }
                    }

                    cloneRow.printers = [];
                    if (strArr[0].length > 0) {
                      cloneRow.printers.push({
                        printer: strArr[0],
                        printerType: "MANUAL",
                      });
                    }
                    break;
                  case "unitAccess":
                    let newUnitAccess = "";
                    if (obj.length === 0) {
                      isWrong = true;
                      errmsg = header + " may not be empty.";
                      if (errorMsgArrValue.length < maxImportCsvErrors) {
                        errorMsgArrValue.push({
                          id: 0,
                          line: lineNum,
                          column: field,
                          value: obj,
                          issue: errmsg,
                        });
                        break;
                      } else {
                        throw Error("oh, crap.");
                      }
                    }
                    hasLocalCampus = false;
                    strArr = obj.split("|");
                    let unitz = new Map<string, string>();
                    for (let i = 0; i < strArr.length; i++) {
                      let ut = strArr[i];
                      let arr2 = ut.split(":");
                      if (arr2.length !== 2) {
                        isWrong = true;
                        errmsg = header + " '" + ut + "' is in bad format.";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        } else {
                          throw Error("oh, crap.");
                        }
                      } else {
                        newUnitAccess = uMapRev.get(ut);
                        if (!newUnitAccess) {
                          isWrong = true;
                          errmsg = "Unknown unit name: '" + ut + "' ";
                          if (errorMsgArrValue.length < maxImportCsvErrors) {
                            errorMsgArrValue.push({
                              id: 0,
                              line: lineNum,
                              column: field,
                              value: obj,
                              issue: errmsg,
                            });
                            break;
                          } else {
                            throw Error("oh, crap.");
                          }
                        } else {
                          if (unitz.has(newUnitAccess)) {
                            isWrong = true;
                            errmsg =
                              header + " '" + ut + "' appears more than once";
                            if (errorMsgArrValue.length < maxImportCsvErrors) {
                              errorMsgArrValue.push({
                                id: 0,
                                line: lineNum,
                                column: field,
                                value: obj,
                                issue: errmsg,
                              });
                              break;
                            } else {
                              throw Error("oh, crap.");
                            }
                          } else {
                            unitz.set(newUnitAccess, newUnitAccess);
                            if (arr2[0].toLowerCase() === "localcampus") {
                              hasLocalCampus = true;
                            }
                          }
                        }
                      }
                      if (!hasLocalCampus) {
                        isWrong = true;
                        errmsg =
                          header + " must include at least one of local campus";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        }
                      }
                    }
                    let currUA = cloneRow.unitAccess || [];
                    let newUA: string[] = [];
                    unitz.forEach((k, v) => {
                      newUA.push(v);
                    });
                    if (!isAdd) {
                      if (!arraysSame(newUA, currUA)) {
                        isIgnored = false;
                      }
                    }
                    cloneRow.unitAccess = [];
                    cloneRow.unitAccess = cloneRow.unitAccess.concat(newUA);
                    break;
                  case "bedAccess":
                    let newBedAccess = "";
                    if (obj.length === 0) {
                      isWrong = true;
                      errmsg = header + " may not be empty.";
                      if (errorMsgArrValue.length < maxImportCsvErrors) {
                        errorMsgArrValue.push({
                          id: 0,
                          line: lineNum,
                          column: field,
                          value: obj,
                          issue: errmsg,
                        });
                        break;
                      } else {
                        throw Error("oh, crap.");
                      }
                    }
                    hasLocalCampus = false;
                    strArr = obj.split("|");
                    let bedz = new Map<string, string>();
                    for (let i = 0; i < strArr.length; i++) {
                      let bd = strArr[i];
                      let arr2 = bd.split(":");
                      if (arr2.length !== 3) {
                        isWrong = true;
                        errmsg = header + " '" + bd + "' is in bad format.";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        } else {
                          throw Error("oh, crap.");
                        }
                      } else {
                        newBedAccess = bMapRev.get(bd);
                        if (!newBedAccess) {
                          isWrong = true;
                          errmsg = "Unknown bed name: '" + bd + "' ";
                          if (errorMsgArrValue.length < maxImportCsvErrors) {
                            errorMsgArrValue.push({
                              id: 0,
                              line: lineNum,
                              column: field,
                              value: obj,
                              issue: errmsg,
                            });
                            break;
                          } else {
                            throw Error("oh, crap.");
                          }
                        } else {
                          if (bedz.has(newBedAccess)) {
                            isWrong = true;
                            errmsg =
                              header + " '" + bd + "' appears more than once";
                            if (errorMsgArrValue.length < maxImportCsvErrors) {
                              errorMsgArrValue.push({
                                id: 0,
                                line: lineNum,
                                column: field,
                                value: obj,
                                issue: errmsg,
                              });
                              break;
                            } else {
                              throw Error("oh, crap.");
                            }
                          } else {
                            bedz.set(newBedAccess, newBedAccess);
                            if (arr2[0].toLowerCase() === "localcampus") {
                              hasLocalCampus = true;
                            }
                          }
                        }
                      }
                    }
                    let currBD = cloneRow.bedAccess || [];
                    let newBD: string[] = [];
                    bedz.forEach((k, v) => {
                      newBD.push(v);
                    });
                    if (!isAdd) {
                      if (!arraysSame(newBD, currBD)) {
                        isIgnored = false;
                      }
                    }
                    cloneRow.bedAccess = [];
                    cloneRow.bedAccess = cloneRow.bedAccess.concat(newBD);
                    break;
                  case "defaultUnit":
                    let revDefaultUnit = "";
                    if (obj.length > 0) {
                      let arr = obj.split(":");
                      if (arr.length !== 2) {
                        isWrong = true;
                        errmsg = header + " '" + obj + "' is in bad format.";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        } else {
                          throw Error("oh, crap.");
                        }
                      } else {
                        revDefaultUnit = arr[1];
                        if (arr[0].toLocaleLowerCase() !== "localcampus") {
                          isWrong = true;
                          errmsg = header + " must be at local campus";
                          if (errorMsgArrValue.length < maxImportCsvErrors) {
                            errorMsgArrValue.push({
                              id: 0,
                              line: lineNum,
                              column: field,
                              value: obj,
                              issue: errmsg,
                            });
                            break;
                          } else {
                            throw Error("oh, crap.");
                          }
                        } else {
                          if (!localUnitMap.has(revDefaultUnit)) {
                            isWrong = true;
                            errmsg =
                              header +
                              "'" +
                              obj +
                              "' not among terminal's local units.";
                            if (errorMsgArrValue.length < maxImportCsvErrors) {
                              errorMsgArrValue.push({
                                id: 0,
                                line: lineNum,
                                column: field,
                                value: obj,
                                issue: errmsg,
                              });
                              break;
                            } else {
                              throw Error("oh, crap.");
                            }
                          }
                        }
                      }
                    }
                    let defUT = cloneRow.defaultUnit || "";
                    if (!isAdd) {
                      if (
                        defUT.toUpperCase() !== revDefaultUnit.toUpperCase()
                      ) {
                        isIgnored = false;
                      }
                    }
                    cloneRow[field] = revDefaultUnit;
                    break;
                  case "defaultBed":
                    let revDefUnit = "";
                    let revDefaultBed = "";
                    if (obj.length > 0) {
                      let arr = obj.split(":");
                      if (arr.length !== 3) {
                        isWrong = true;
                        errmsg = header + " '" + obj + "' is in bad format.";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        } else {
                          throw Error("oh, crap.");
                        }
                      } else {
                        revDefUnit = arr[1];
                        revDefaultBed = arr[2];
                        if (arr[0].toLocaleLowerCase() !== "localcampus") {
                          isWrong = true;
                          errmsg = header + " must be at local campus";
                          if (errorMsgArrValue.length < maxImportCsvErrors) {
                            errorMsgArrValue.push({
                              id: 0,
                              line: lineNum,
                              column: field,
                              value: obj,
                              issue: errmsg,
                            });
                            break;
                          } else {
                            throw Error("oh, crap.");
                          }
                        } else {
                          let currUnit;
                          if (!localUnitMap.has(revDefUnit)) {
                            isWrong = true;
                            errmsg =
                              header +
                              " '" +
                              obj +
                              "' is in a unit not at local campus.";
                            if (errorMsgArrValue.length < maxImportCsvErrors) {
                              errorMsgArrValue.push({
                                id: 0,
                                line: lineNum,
                                column: field,
                                value: obj,
                                issue: errmsg,
                              });
                              break;
                            } else {
                              throw Error("oh, crap.");
                            }
                          } else {
                            currUnit = localUnitMap.get(revDefUnit) as Unit;
                            let currUnitBeds = currUnit.bed || [];
                            let exists = false;
                            for (let i = 0; i < currUnitBeds.length; i++) {
                              if (
                                revDefaultBed.toUpperCase() ===
                                currUnitBeds[i].name.toUpperCase()
                              ) {
                                exists = true;
                                break;
                              }
                            }
                            if (!exists) {
                              isWrong = true;
                              errmsg =
                                header +
                                " '" +
                                obj +
                                "' not exists in local unit '" +
                                currUnit.name +
                                "'";
                              if (
                                errorMsgArrValue.length < maxImportCsvErrors
                              ) {
                                errorMsgArrValue.push({
                                  id: 0,
                                  line: lineNum,
                                  column: field,
                                  value: obj,
                                  issue: errmsg,
                                });
                                break;
                              } else {
                                throw Error("oh, crap.");
                              }
                            }
                          }
                        }
                      }
                    }
                    let defBed: string = cloneRow[field] || "";
                    if (!isAdd) {
                      if (
                        defBed.toUpperCase() !== revDefaultBed.toUpperCase()
                      ) {
                        isIgnored = false;
                      }
                    }
                    cloneRow[field] = revDefaultBed;
                    break;
                  case "screenLock":
                    if (obj.length > 0) {
                      let exists = false;
                      for (let i = 0; i < screenLocks.length; i++) {
                        if (
                          obj.toUpperCase() === screenLocks[i].toUpperCase()
                        ) {
                          exists = true;
                          break;
                        }
                      }
                      if (!exists) {
                        isWrong = true;
                        errmsg =
                          obj + " not among defined " + header + " values.";
                        if (errorMsgArrValue.length < maxImportCsvErrors) {
                          errorMsgArrValue.push({
                            id: 0,
                            line: lineNum,
                            column: field,
                            value: obj,
                            issue: errmsg,
                          });
                          break;
                        } else {
                          throw Error("oh, crap.");
                        }
                      }
                    }
                    let defSL = cloneRow.screenLock + "";
                    if (!isAdd) {
                      if (obj.toUpperCase() !== defSL.toUpperCase()) {
                        isIgnored = false;
                      }
                    }
                    cloneRow.screenLock = obj;
                    break;
                  case "smartCard":
                    if (isSmartCardEditingEnabled) {
                      if (obj.length > 0) {
                        let exists = false;
                        for (let i = 0; i < smartCardOptions.length; i++) {
                          if (
                            obj.toUpperCase() ===
                            smartCardOptions[i].toUpperCase()
                          ) {
                            exists = true;
                            break;
                          }
                        }
                        if (!exists) {
                          isWrong = true;
                          errmsg =
                            obj + " not among defined " + header + " values.";
                          if (errorMsgArrValue.length < maxImportCsvErrors) {
                            errorMsgArrValue.push({
                              id: 0,
                              line: lineNum,
                              column: field,
                              value: obj,
                              issue: errmsg,
                            });
                            break;
                          } else {
                            throw Error("oh, crap.");
                          }
                        }
                      }
                      let defSC: string = cloneRow.smartCard || "";
                      if (!isAdd) {
                        if (obj.toUpperCase() !== defSC.toUpperCase()) {
                          isIgnored = false;
                        }
                      }
                      cloneRow.smartCard = obj;
                      break;
                    }
                }
              }
            }
            if (lineNum === 1 && errorMsgArrHeader.length > 0) {
              break;
            }

            if (lineNum > 1) {
              if (!isWrong) {
                if (isAdd) {
                  termsAdded++;
                  clonedTcData.terminal.push(cloneRow);
                  add(cloneRow.name, changeList);
                } else if (isIgnored && lineNum > 1) {
                  termsIgnored++;
                } else {
                  termsChanged++;
                  updateData(cloneRow, clonedTcData);
                  currRow = cloneRow;
                  add(cloneRow.name, changeList);
                }
              } else {
                termsWrong++;
              }
            }
          }

          let changedRows: number[] = [];
          for (let i = 0; i < clonedTcData.terminal.length; i++) {
            clonedTcData.terminal[i].id = i + 1;
            if (changed(clonedTcData.terminal[i].name, changeList)) {
              changedRows.push(clonedTcData.terminal[i].id);
            }
          }
          setImportedRows(changedRows);
          if (changeList.length > 0) {
            setImportData(!importData);
          }

          let msg: MyLog = cloneDeep(logContent);
          msg.title = "Error List";
          msg.subtitle = "";
          msg.msg =
            termsAdded +
            termsChanged +
            termsIgnored +
            termsWrong +
            " lines processed (not including header): \n\n" +
            termsAdded +
            " terminals added;\n" +
            termsChanged +
            " terminals changed;\n" +
            termsIgnored +
            " lines ignored (terminals unchanged);\n" +
            termsWrong +
            " erroneous lines (not used).\n";
          msg.Data = errorMsgArrValue;
          for (let i = 0; i < msg.Data.length; i++) {
            msg.Data[i].id = i;
          }
          setLogContent(msg);
          if (errorMsgArrHeader.length === 0 && termsAdded + termsChanged > 0) {
            setData(clonedTcData);
            setDirty(true);
          }
          inputRef.current.value = ""; //allow open one file more than one time in a row.
          setLoading(false);
          setShowLog(true);
        } catch (e) {
          let msg: MyLog = cloneDeep(logContent);
          msg.title = "Error List";
          msg.subtitle = "";
          msg.msg = "No lines processed\n\n";
          for (let i = 0; i < errorMsgArrHeader.length; i++) {
            msg.Data.push(errorMsgArrHeader[i]);
          }
          for (let i = 0; i < errorMsgArrValue.length; i++) {
            msg.Data.push(errorMsgArrValue[i]);
          }
          for (let i = 0; i < msg.Data.length; i++) {
            msg.Data[i].id = i;
          }
          setLogContent(msg);
          setLoading(false);
          setShowLog(true);
          inputRef.current.value = ""; //allow open one file more than one time in a row.
          return;
        }
      };
      reader.readAsText(fileObj);
    } catch (error) {
      let msg: MyLog = cloneDeep(logContent);
      msg.title = "Error List";
      msg.subtitle = "";
      msg.msg = "No lines processed\n\n";
      for (let i = 0; i < errorMsgArrHeader.length; i++) {
        msg.Data.push(errorMsgArrHeader[i]);
      }
      for (let i = 0; i < errorMsgArrValue.length; i++) {
        msg.Data.push(errorMsgArrValue[i]);
      }
      for (let i = 0; i < msg.Data.length; i++) {
        msg.Data[i].id = i;
      }
      setLogContent(msg);
    } finally {
      setLoading(false);
      setShowLog(true);
      inputRef.current.value = ""; //allow open one file more than one time in a row.
    }
  };

  const add = (termName: string, arr: string[]) => {
    for (let i = 0; i < arr.length; i++) {
      if (termName.trim().toLowerCase() === arr[i].trim().toLocaleLowerCase()) {
        return;
      }
    }
    arr.push(termName);
  };

  const changed = (termName: string, arr: string[]) => {
    for (let i = 0; i < arr.length; i++) {
      if (
        termName.trim().toLocaleLowerCase() ===
        arr[i].trim().toLocaleLowerCase()
      ) {
        return true;
      }
    }
    return false;
  };

  const updateData = (row: Terminal, tcData: TcData) => {
    for (let i = 0; i < tcData.terminal.length; i++) {
      let tm = tcData.terminal[i];
      if (row.name.toUpperCase() === tm.name.toUpperCase()) {
        let ps: Userprinter[] = [];
        if (row.printers.length > 0) {
          ps = [
            {
              printer: row.printers[0].printer,
              printerType:
                tm.printers.length > 0 ? tm.printers[0].printerType : "MANUAL",
            },
          ];
          row.printers = ps;
        }
        tcData.terminal[i] = row;
        return;
      }
    }
  };

  const arraysSame = (arr1: string[], arr2: string[]): boolean => {
    if (arr1.length !== arr2.length) {
      return false;
    }

    for (let i = 0; i < arr1.length; i++) {
      let a1 = arr1[i];
      let exists = false;
      for (let j = 0; j < arr2.length; j++) {
        let a2 = arr2[j];
        if (a1.toUpperCase() === a2.toUpperCase()) {
          exists = true;
          break;
        }
      }
      if (!exists) {
        return false;
      }
    }
    return true;
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Import",
    icon: <ImportIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  return (
    <div>
      <input
        accept="csv"
        style={{ display: "none" }}
        ref={inputRef}
        type="file"
        onChange={handleFileReader}
      />
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !isEdit}
      />
    </div>
  );
}
