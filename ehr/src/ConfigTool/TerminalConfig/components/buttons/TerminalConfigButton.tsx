import React from "react";
import Button from "@mui/material/Button";

interface IButtonProps {
  text: string;
  disabled?: boolean;
  handleClick?: () => void;
  type?: "button" | "submit" | "reset" | undefined;
}

const TerminalConfigButton: React.FunctionComponent<IButtonProps> = ({
  text,
  disabled = false,
  handleClick,
  type = "button",
}: IButtonProps): JSX.Element => {
  return (
    <span>
      <Button
        variant="outlined"
        disabled={disabled}
        onClick={handleClick}
        type={type}
      >
        {text}
      </Button>
    </span>
  );
};

export default TerminalConfigButton;
