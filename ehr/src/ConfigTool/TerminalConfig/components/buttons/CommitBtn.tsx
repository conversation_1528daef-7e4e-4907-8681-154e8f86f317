import React from "react";
import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai/react";
import { jsonData2String, commitFile } from "../../util/TcData";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import {
  dataAtom,
  oldDataAtom,
  loadingAtom,
  showAlertAtom,
  showInfoDialogAtom,
  needCommitOrRevertAtom,
  dirtyAtom,
  doCommitAtom,
  isEditAtom,
  isStealLockableAtom,
  switchModeAtom,
} from "../../context/TerminalConfigAtoms";
import TerminalConfigAlert from "../popups/TerminalConfigAlert";
import TerminalConfigDialog, {
  IPopupProps as IDialogPopupProps,
} from "../popups/TerminalConfigDialog";
import TerminalInfoDialog from "../popups/TerminalConfigInfoDialog";
import { TcData } from "../../type";
import { cloneDeep } from "lodash";
import { CommitIcon } from "@cci-monorepo/common";
import CtaConfirm, {
  IPopupProps,
} from "@cci-monorepo/ConfigTool/NotesMenuEditor/components/CtaConfirm";

export default function CommitBtn() {
  const [loading, setLoading] = useAtom(loadingAtom);
  const [data, setData] = useAtom(dataAtom);
  const [, setOldData] = useAtom(oldDataAtom);
  const [needCommitOrRevert, setNeedCommitOrRevert] = useAtom(
    needCommitOrRevertAtom
  );
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [content, setContent] = React.useState("");
  const [severity, setSeverity] = React.useState("info");
  const [createCommentOpened, setCreateCommentOpened] = React.useState(false);
  const [, setComment] = React.useState("");
  const [title, setTitle] = React.useState("");
  const [open, setOpen] = React.useState(false);
  const isDirty = useAtomValue(dirtyAtom);

  const [doCommit, setDoCommit] = useAtom(doCommitAtom);
  const [isEdit, setIsEdit] = useAtom(isEditAtom);
  const setIsStealLockable = useSetAtom(isStealLockableAtom);
  const [confirmOpened, setConfirmOpened] = React.useState(false);
  const [switchMode, setSwitchMode] = useAtom(switchModeAtom);

  React.useEffect(() => {
    if (doCommit) {
      setCreateCommentOpened(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doCommit]);

  const handleCreateComment = (open: boolean) => {
    setCreateCommentOpened(open);
  };

  const handleCommit = (comment: string, overwrite: number) => {
    let tcData = data as TcData;
    const content = jsonData2String(tcData);
    setLoading(true);
    commitFile({
      content: content,
      module: "terminal",
      comment: comment,
      commithash: tcData.hashCode,
      overwrite: overwrite,
    })
      .then((r) => {
        if (r.success) {
          setContent("Commit succeeded!");
          setSeverity("success");
          setShowAlert("CommitBtn");
          setNeedCommitOrRevert(false);
          tcData.hashCode = r.hash;
          setData(tcData);
          let clonedTcData = cloneDeep(tcData);
          setOldData(clonedTcData);
          if (switchMode) {
            setIsEdit(false);
            setSwitchMode(false);
          }
        } else {
          if (r.status === 0) {
            // Indicates that Git data has been changed, you can follow the prompt to overwrite it or not
            setConfirmOpened(true);
          } else {
            setTitle("Commit");
            setContent("Commit failed. Reason: " + r.message);
            setOpen(true);
            setShowInfoDialog("CommitBtn");
            if (r.locker) {
              setIsStealLockable(true);
            }
          }
        }
      })
      .catch((error) => {
        setTitle("Commit");
        setContent(
          "Unexpected error occurs. Please contact administrator for assistance"
        );
        setOpen(true);
        setShowInfoDialog("CommitBtn");
        console.log("error: " + error.message);
      })
      .finally(() => {
        setLoading(false);
        setDoCommit(false);
      });
  };

  const handleSubmit = (value: string) => {
    setComment(value);
    handleCommit(value, 0);
  };

  const dialogSetting: IDialogPopupProps = {
    title: "Commit comment",
    text: "Please enter comment:",
    open: createCommentOpened,
    onOK: handleSubmit,
    show: handleCreateComment,
  };

  const handleClickOpen = () => {
    if (isDirty) {
      setTitle("Commit");
      setContent(
        "You have unsaved data, please save or refresh before commit."
      );
      setOpen(true);
      setShowInfoDialog("CommitBtn");
    } else {
      setCreateCommentOpened(true);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Commit",
    icon: <CommitIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  const handleConfirm = () => {
    handleCommit(content, 1);
    setConfirmOpened(false);
  };

  const confirmSetting: IPopupProps = {
    title: "Confirm overwrite",
    text: "Git data has been changed, are you sure you want to overwrite it?",
    open: confirmOpened,
    onOK: handleConfirm,
    show: setConfirmOpened,
  };

  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !needCommitOrRevert || !isEdit}
      />
      <TerminalConfigDialog {...dialogSetting} />
      <CtaConfirm {...confirmSetting} />
      {showAlert === "CommitBtn" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
      {showInfoDialog === "CommitBtn" && (
        <TerminalInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
    </div>
  );
}
