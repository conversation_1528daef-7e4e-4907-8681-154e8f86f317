import React, { useState } from "react";
import { ReviewIcon } from "@cci-monorepo/common/assets";
import { useAtom, useAtomValue, useSetAtom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import {
  loadingAtom,
  isEditAtom,
  isStealLockableAtom,
  refreshFlag<PERSON>tom,
  dirtyAtom,
  doSaveAtom,
  doRevertAtom,
  clearSelectedRowsAtom,
} from "../../context/TerminalConfigAtoms";
import TerminalConfigConfirm from "../popups/TerminalConfigConfirm";
import { releaseLock } from "@cci-monorepo/ConfigTool/util/DataRequest";

export default function ReviewBtn() {
  const [loading] = useAtom(loadingAtom);
  const [isEdit, setIsEdit] = useAtom(isEditAtom);
  const setIsStealLockable = useSetAtom(isStealLockableAtom);
  const dirty = useAtomValue(dirtyAtom);
  const setRefreshFlag = useSetAtom(refreshFlagAtom);
  const [open, setOpen] = React.useState(false);
  const setDoSave = useSetAtom(doSaveAtom);
  const setDoRevert = useSetAtom(doRevertAtom);
  const [clearSelectedRows, setClearSelectedRows] = useAtom(
    clearSelectedRowsAtom
  );

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Review",
    icon: <ReviewIcon />,
    handleIconBtnClick: (event) => {
      handleReviewBtnClick(event);
    },
  };

  const releaseFunc = () => {
    releaseLock("terminal").then((res: any) => {
      const data = res.data;
      if (data.success) {
        setIsEdit(false);
        setIsStealLockable(false);
      }
    });
  };

  const handleReload = () => {
    setRefreshFlag(true);
    releaseFunc();
    setOpen(false);
    setClearSelectedRows(!clearSelectedRows);
  };

  const handleSave = () => {
    setOpen(false);
    setDoSave(true);
    setClearSelectedRows(!clearSelectedRows);
  };

  const [confirmSetting, setConfirmSetting] = React.useState({
    text: "",
    onYes: () => {},
    onNO: () => {},
    yesText: "",
    noText: "",
  });

  const handleReviewBtnClick = (event: any) => {
    event.currentTarget.blur();
    if (dirty) {
      setOpen(true);
      setConfirmSetting({
        text: "You have unsaved data in Terminal Configuration. Do you want to save the changes?",
        onYes: handleSave,
        onNO: handleReload,
        yesText: "",
        noText: "",
      });
    } else {
      releaseFunc();
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !isEdit}
      />
      <TerminalConfigConfirm
        open={open}
        show={() => {}}
        text={confirmSetting.text}
        onYes={confirmSetting.onYes}
        onNo={confirmSetting.onNO}
        yesText={confirmSetting.yesText}
        noText={confirmSetting.noText}
        onCancel={handleClose}
        title="Review"
      />
    </div>
  );
}
