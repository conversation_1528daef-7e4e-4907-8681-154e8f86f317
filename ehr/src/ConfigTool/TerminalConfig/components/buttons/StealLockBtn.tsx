import { StealLockIcon } from "@cci-monorepo/common/assets";
import React from "react";
import { useAtom, useSetAtom } from "jotai/react";
import { useState } from "react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import TerminalConfigDialog, {
  IPopupProps as IDialogPopupProps,
} from "../popups/TerminalConfigDialog";
import TerminalConfigConfirm from "../popups/TerminalConfigConfirm";
import TerminalConfigAlert from "../popups/TerminalConfigAlert";
import {
  loadingAtom,
  isEditAtom,
  isStealLockableAtom,
  showAlertAtom,
  doStealLockAtom,
  refreshFlagAtom,
} from "../../context/TerminalConfigAtoms";
import {
  stealLock,
  commitsaved,
  revertsaved,
} from "@cci-monorepo/ConfigTool/util/DataRequest";

export default function StealLockBtn() {
  const [loading] = useAtom(loadingAtom);
  const [, setIsEdit] = useAtom(isEditAtom);
  const [isStealLockable, setIsStealLockable] = useAtom(isStealLockableAtom);
  // Alert
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [content, setContent] = useState("");
  const [severity, setSeverity] = useState("info");
  const setRefreshFlag = useSetAtom(refreshFlagAtom);

  // comment dialog
  const [createCommentOpened, setCreateCommentOpened] = useState(false);

  // confirm
  const [open, setOpen] = useState(false);
  const [dialogText, setDialogText] = useState("");

  const [doStealLock, setDoStealLock] = useAtom(doStealLockAtom);

  React.useEffect(() => {
    if (doStealLock) {
      handleStealLockClick();
      setDoStealLock(false);
      setRefreshFlag(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doStealLock]);

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Steal Lock",
    icon: <StealLockIcon />,
    handleIconBtnClick: (event) => {
      handleStealLockClick(event);
    },
  };

  const handleStealLockClick = (event?: any) => {
    if (event) {
      event.currentTarget.blur();
    }
    stealLock("terminal").then((res: any) => {
      const data = res.data;
      if (data.success) {
        setIsEdit(true);
        setIsStealLockable(false);
      } else {
        if (data.uncommitted === 1) {
          setDialogText(data.message);
          setOpen(true);
        }
      }
    });
  };

  const handleCreateComment = (open: boolean) => {
    setCreateCommentOpened(open);
  };

  const handleCommitSubmit = (comment: string) => {
    commitsaved("terminal", comment)
      .then((res: any) => {
        setContent("Commit Succeeded!");
        setSeverity("success");
        setShowAlert("StealLockBtn");
        setDoStealLock(true);
      })
      .catch((err: any) => {});
  };

  const handleRevert = () => {
    revertsaved("terminal")
      .then(() => {
        setContent("Revert Successful!");
        setSeverity("success");
        setShowAlert("StealLockBtn");
        setDoStealLock(true);
      })
      .catch((err: any) => {});
  };

  const confirmSetting = {
    // commit
    onYes: () => {
      setCreateCommentOpened(true);
      setOpen(false);
    },
    // revert
    onNO: () => {
      handleRevert();
      setOpen(false);
    },
    yesText: "Commit",
    noText: "Revert",
  };

  const dialogSetting: IDialogPopupProps = {
    title: "Save & Commit Comment",
    text: "Please enter comment:",
    open: createCommentOpened,
    onOK: handleCommitSubmit,
    show: handleCreateComment,
  };

  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !isStealLockable}
      />
      <TerminalConfigDialog {...dialogSetting} />
      <TerminalConfigConfirm
        open={open}
        show={() => {}}
        text={dialogText}
        onYes={confirmSetting.onYes}
        onNo={confirmSetting.onNO}
        yesText={confirmSetting.yesText}
        noText={confirmSetting.noText}
        onCancel={() => {
          setOpen(false);
        }}
        title="Review"
      />
      {showAlert === "StealLockBtn" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
    </div>
  );
}
