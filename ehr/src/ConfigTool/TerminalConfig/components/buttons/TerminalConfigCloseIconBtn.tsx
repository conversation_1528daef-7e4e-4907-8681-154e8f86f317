import React from "react";

import CloseIcon from "@mui/icons-material/Close";

import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";

interface ICloseIconButtonProps {
  handleClick: () => void;
}

const TerminalConfigCloseIconBtn: React.FunctionComponent<
  ICloseIconButtonProps
> = ({ handleClick }) => {
  const iconBtnClose: IIconButtonProps = {
    tooltip: "Close",
    icon: <CloseIcon />,
    handleIconBtnClick: handleClick,
    color: "white",
  };

  return <TerminalConfigIconBtn {...iconBtnClose} />;
};

export default TerminalConfigCloseIconBtn;
