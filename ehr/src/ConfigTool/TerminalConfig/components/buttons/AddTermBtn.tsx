import React from "react";
import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { useAtom, useAtomValue } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import { AddTerminalDialog } from "../AddTerminalDialog";
import {
  loadingAtom,
  isEditAtom,
  selectedGridRowsAtom,
} from "../../context/TerminalConfigAtoms";

function AddIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
    </SvgIcon>
  );
}

export default function AddTermBtn() {
  const [loading] = useAtom(loadingAtom);
  const [openAddTerminal, setOpenAddTerminal] = React.useState(false);
  const isEdit = useAtomValue(isEditAtom);
  const selectedGridRows = useAtomValue(selectedGridRowsAtom);

  const handleClickOpen = (event: any) => {
    setOpenAddTerminal(true);
    event.currentTarget.blur();
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Add New Terminal",
    icon: <AddIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  const handleCloseAddTerminal = (value: string) => {
    setOpenAddTerminal(false);
  };
  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !isEdit}
      />
      <AddTerminalDialog
        open={openAddTerminal}
        onClose={handleCloseAddTerminal}
        editName={false}
        selectedGridRows={selectedGridRows}
      />
    </div>
  );
}
