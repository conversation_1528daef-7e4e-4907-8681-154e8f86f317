import React from "react";
import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON><PERSON> } from "jotai/react";
import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { jsonData2String, saveCommitFile } from "../../util/TcData";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import {
  dataAtom,
  oldData<PERSON>tom,
  loading<PERSON>tom,
  showAlert<PERSON>tom,
  showInfoDialog<PERSON>tom,
  dirtyAtom,
  doSave<PERSON>tom,
  isEditAtom,
  isStealLockableAtom,
  clearSelectedRowsAtom,
  preHashCode<PERSON>tom,
} from "../../context/TerminalConfigAtoms";
import TerminalConfigAlert from "../popups/TerminalConfigAlert";
import TerminalConfigDialog, {
  IPopupProps as IDialogPopupProps,
} from "../popups/TerminalConfigDialog";
import TerminalInfoDialog from "../popups/TerminalConfigInfoDialog";
import { TcData } from "../../type";
import { cloneDeep } from "lodash";
function SaveIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z" />
    </SvgIcon>
  );
}
import CtaConfirm, {
  IPopupProps,
} from "@cci-monorepo/ConfigTool/NotesMenuEditor/components/CtaConfirm";

export default function SaveCommitBtn() {
  const [loading, setLoading] = useAtom(loadingAtom);
  const [, setPreHashCode] = useAtom(preHashCodeAtom);
  const [data, setData] = useAtom(dataAtom);
  const [, setOldData] = useAtom(oldDataAtom);
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [content, setContent] = React.useState("");
  const [severity, setSeverity] = React.useState("info");
  const [createCommentOpened, setCreateCommentOpened] = React.useState(false);
  const [comment, setComment] = React.useState("");
  const [title, setTitle] = React.useState("");
  const [open, setOpen] = React.useState(false);
  const [isDirty, setDirty] = useAtom(dirtyAtom);
  const [doSave, setDoSave] = useAtom(doSaveAtom);
  const isEdit = useAtomValue(isEditAtom);
  const setIsStealLockable = useSetAtom(isStealLockableAtom);
  const [confirmOpened, setConfirmOpened] = React.useState(false);
  const [clearSelectedRows, setClearSelectedRows] = useAtom(
    clearSelectedRowsAtom
  );

  React.useEffect(() => {
    if (doSave) {
      setCreateCommentOpened(true);
      setDoSave(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doSave]);

  const handleCreateComment = (open: boolean) => {
    setCreateCommentOpened(open);
  };

  const handleSaveCommit = (comment: string, overwrite: number) => {
    setLoading(true);
    let tcData = data as TcData;
    const content = jsonData2String(tcData);
    let hash = data?.hashCode || "";
    saveCommitFile({
      filedata: content,
      module: "terminal",
      filename: "SystemConfig",
      comment: comment,
      commithash: hash,
      overwrite: overwrite,
    })
      .then((resp) => {
        if (resp.success) {
          setContent("Save & Commit Succeeded!");
          setSeverity("success");
          setShowAlert("SaveCommitBtn");

          setPreHashCode(tcData.hashCode);
          tcData.preHashCode = tcData.hashCode;
          tcData.hashCode = resp.commithash;
          setData(tcData);
          let clonedTcData = cloneDeep(tcData);
          setOldData(clonedTcData);
          setDirty(false);
        } else {
          if (resp.status === 0) {
            // Indicates that Git data has been changed, you can follow the prompt to overwrite it or not
            setConfirmOpened(true);
            setDirty(true);
          } else {
            setTitle("Save && Commit");
            setContent(
              "Save succeeded but commit fail. Reason: " + resp.message
            );
            setOpen(true);
            setShowInfoDialog("SaveCommitBtn");
            if (resp.locker) {
              setIsStealLockable(true);
            }
            setDirty(true);
          }

          setTitle("Save & Commit");
          setContent("Save & Commit fail. Reason: " + resp.message);
          setOpen(true);
          setShowInfoDialog("SaveCommitBtn");
          setDirty(true);
          if (resp.locker) {
            setIsStealLockable(true);
          }
        }
      })
      .catch((error) => {
        setTitle("Save & Commit");
        setContent(
          "Unexpected error occurs. Please contact administrator for assistance"
        );
        setOpen(true);
        setShowInfoDialog("SaveCommitBtn");
        console.log("error: " + error.message);
      })
      .finally(() => {
        setLoading(false);
        setClearSelectedRows(!clearSelectedRows);
      });
  };

  const handleSubmit = (value: string) => {
    setComment(value);
    handleSaveCommit(value, 0);
  };

  const dialogSetting: IDialogPopupProps = {
    title: "Save & Commit Comment",
    text: "Please enter comment:",
    open: createCommentOpened,
    onOK: handleSubmit,
    show: handleCreateComment,
  };

  const handleClickOpen = (event: any) => {
    setCreateCommentOpened(true);
    event.currentTarget.blur();
  };

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Save & Commit",
    icon: <SaveIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  const handleConfirm = () => {
    handleSaveCommit(comment, 1);
    setConfirmOpened(false);
  };

  const confirmSetting: IPopupProps = {
    title: "Confirm overwrite",
    text: "Git data has been changed, are you sure you want to overwrite it?",
    open: confirmOpened,
    onOK: handleConfirm,
    show: setConfirmOpened,
  };

  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !isDirty || !isEdit}
      />
      <TerminalConfigDialog {...dialogSetting} />
      <CtaConfirm {...confirmSetting} />
      {showAlert === "SaveCommitBtn" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
      {showInfoDialog === "SaveCommitBtn" && (
        <TerminalInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
    </div>
  );
}
