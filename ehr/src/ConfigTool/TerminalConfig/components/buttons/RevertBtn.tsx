import React from "react";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import TerminalConfigAlert from "../popups/TerminalConfigAlert";
import TerminalInfoDialog from "../popups/TerminalConfigInfoDialog";
import TerminalConfigConfirm from "../popups/TerminalConfigConfirm";
import { getTcData, getFileData } from "../../util/TcData";
import { TcData, Terminal } from "../../type";
import { revertFile } from "../../util/TcData";
import { cloneDeep } from "lodash";
import {
  dataAtom,
  oldDataAtom,
  showAlertAtom,
  showInfoDialogAtom,
  loadingAtom,
  dirtyAtom,
  preHashCodeAtom,
  alllTerminalsAtom,
  unselectedTerminalsAtom,
  doRevertAtom,
  isEditAtom,
  clearSelectedRows<PERSON>tom,
} from "../../context/TerminalConfigAtoms";
import { RevertIcon } from "@cci-monorepo/common";

export default function RevertBtn() {
  const [data, setData] = useAtom(dataAtom);
  const [, setOldData] = useAtom(oldDataAtom);
  const [loading] = useAtom(loadingAtom);
  const [preHashCode, setPreHashCode] = useAtom(preHashCodeAtom);
  const [allTerminals] = useAtom(alllTerminalsAtom);
  const [, setUnselectedTerminals] = useAtom(unselectedTerminalsAtom);
  const [, setLoading] = useAtom(loadingAtom);
  const [isDirty, setDirty] = useAtom(dirtyAtom);
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [clearSelectedRows, setClearSelectedRows] = useAtom(
    clearSelectedRowsAtom
  );
  const [content, setContent] = React.useState("");
  const [severity, setSeverity] = React.useState("info");
  const [title, setTitle] = React.useState("");
  const [open, setOpen] = React.useState(false);

  const [doRevert, setDoRevert] = useAtom(doRevertAtom);
  const isEdit = useAtomValue(isEditAtom);

  const handleClickOpen = (event: any) => {
    event.currentTarget.blur();
    setLoading(false);
    if (isDirty) {
      setContent(
        "You have unsaved data in Terminal Configuration. Do you want to discard the changes and revert?"
      );
      setOpen(true);
    } else {
      handleRevert();
    }
  };

  React.useEffect(() => {
    if (doRevert) {
      handleRevert();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doRevert]);

  const handleRevert = () => {
    setOpen(false);
    setLoading(true);
    let tcData = data as TcData;
    revertFile({
      module: "terminal",
      hashtorevert: tcData.preHashCode,
      currenthash: tcData.hashCode,
      filename: "SystemConfig",
    })
      .then((d) => {
        if (d.success) {
          setContent("Revert Successful!");
          setSeverity("success");
          setShowAlert("RevertBtn");
          getFileData().then((rawdata) => {
            if (rawdata.success) {
              let tcData = getTcData(rawdata.content, rawdata.hash);
              tcData.preHashCode = "";
              setPreHashCode("");
              let clonedTcData = cloneDeep(tcData);
              setDirty(false);
              setData(tcData);
              setOldData(clonedTcData);
              resetUnselectedTerms(tcData);
            } else {
              console.log(rawdata.message);
            }
          });
          setLoading(false);
        } else {
          setTitle("Revert");
          setContent("Revert failed. Reason: " + d.message);
          setOpen(true);
          setShowInfoDialog("RevertBtn");
          setDirty(true);
          setLoading(false);
        }
      })
      .catch((error) => {
        setTitle("Revert");
        setContent(
          "Unexpected error occurs. Please contact administrator for assistance"
        );
        setOpen(true);
        setShowInfoDialog("RevertBtn");
        console.log("error: " + error.message);
        setLoading(false);
      })
      .finally(() => {
        setDoRevert(false);
        setClearSelectedRows(!clearSelectedRows);
      });
  };

  const resetUnselectedTerms = (tcData: TcData) => {
    let unselectedTerm: Terminal[] = [];
    for (let i = 0; i < allTerminals.length; i++) {
      let exists: boolean = false;
      let terminals = tcData.terminal || [];
      for (let j = 0; j < terminals.length; j++) {
        if (allTerminals[i].toUpperCase() === terminals[j].name.toUpperCase()) {
          exists = true;
          break;
        }
      }
      if (!exists) {
        let newTerm: Terminal = {
          id: getNewTerminalId(tcData),
          name: allTerminals[i],
          printers: [],
          unitAccess: [],
          bedAccess: [],
          screenLock: 0,
          defaultUnit: "",
          defaultBed: "",
          loadBalance: "",
          smartCard: "OPTIONAL",
        };
        unselectedTerm.push(newTerm);
      }
    }
    setUnselectedTerminals(unselectedTerm);
  };

  const getNewTerminalId = (myData: TcData): number => {
    let ret = 0;
    let terminals: Terminal[] = myData.terminal;
    for (let j = 0; j < terminals.length; j++) {
      if (ret <= terminals[j].id) {
        ret = terminals[j].id + 1;
      }
    }
    return ret;
  };

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const show = (show: boolean) => {};

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Revert",
    icon: <RevertIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={loading || !isEdit || preHashCode === ""}
      />
      {showAlert === "RevertBtn" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
      {showInfoDialog === "RevertBtn" && (
        <TerminalInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
      <TerminalConfigConfirm
        open={open}
        show={show}
        text="You have unsaved data in Terminal Configuration. Do you want to discard the changes and revert?"
        onYes={handleRevert}
        onNo={handleCancel}
        title="Revert"
      />
    </div>
  );
}
