import React from "react";
import { useAtom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import { ExportColumnsDialog } from "../ExportColumnsDialog";
import {
  loadingAtom,
  exportDataActionAtom,
} from "../../context/TerminalConfigAtoms";
import { ExportNoteInfoIcon } from "@cci-monorepo/common";

export default function ExportBtn() {
  const [loading] = useAtom(loadingAtom);
  const [exportDataAction, setExportDataAction] = useAtom(exportDataActionAtom);
  const [openDialog, setOpenDialog] = React.useState(false);

  const handleClickOpen = (event: any) => {
    setOpenDialog(true);
    event.currentTarget.blur();
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setExportDataAction(!exportDataAction);
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Export",
    icon: <ExportNoteInfoIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  return (
    <div>
      <TerminalConfigIconBtn {...iconBtnSetting} disabled={loading} />
      <ExportColumnsDialog
        open={openDialog}
        onClose={handleCloseDialog}
        title="Columns to Export"
        subtitle="Select columns to export:"
      />
    </div>
  );
}
