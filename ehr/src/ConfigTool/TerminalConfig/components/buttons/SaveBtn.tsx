import React from "react";
import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { jsonData2String } from "../../util/TcData";
import { useAtom, useSetAtom } from "jotai/react";
import { cloneDeep } from "lodash";
import TerminalConfigAlert from "../popups/TerminalConfigAlert";
import TerminalInfoDialog from "../popups/TerminalConfigInfoDialog";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import {
  dataAtom,
  oldDataAtom,
  showAlert<PERSON>tom,
  loading<PERSON>tom,
  dirtyAtom,
  showInfoDialog<PERSON>tom,
  needCommitOrRevert<PERSON>tom,
  doSaveAtom,
  isStealLockableAtom,
  clearSelectedRowsAtom,
} from "../../context/TerminalConfigAtoms";

import { saveFile } from "../../util/TcData";
import { TcData } from "../../type";

function SaveIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z" />
    </SvgIcon>
  );
}

export default function SaveBtn() {
  const [loading, setLoading] = useAtom(loadingAtom);
  const [isDirty, setDirty] = useAtom(dirtyAtom);
  const setNeedCommitOrRevert = useSetAtom(needCommitOrRevertAtom);
  const [data] = useAtom(dataAtom);
  const [, setOldData] = useAtom(oldDataAtom);
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [content, setContent] = React.useState("");
  const [severity, setSeverity] = React.useState("info");
  const [title, setTitle] = React.useState("");
  const [open, setOpen] = React.useState(false);
  const [doSave, setDoSave] = useAtom(doSaveAtom);
  const setIsStealLockable = useSetAtom(isStealLockableAtom);
  const [clearSelectedRows, setClearSelectedRows] = useAtom(
    clearSelectedRowsAtom
  );

  React.useEffect(() => {
    if (doSave) {
      handleSave();
      setDoSave(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doSave]);

  // Handle save
  const handleSave = () => {
    setLoading(true);
    let tcData = data as TcData;
    const content = jsonData2String(tcData);
    saveFile({
      content: content,
      module: "terminal",
    })
      .then((resp) => {
        if (resp.success) {
          if (resp.message.length === 0) {
            setContent(
              "Save succeeded, Please Test and then Commit or Revert All your changes."
            );
            setSeverity("success");
            setShowAlert("SaveBtn");
            setDirty(false);
            setNeedCommitOrRevert(true);
            let clonedTcData = cloneDeep(data) as TcData;
            setOldData(clonedTcData);
          } else {
            setTitle("Save");
            setContent(
              "Save succeeded, but " +
                resp.message +
                ". Please revert all your changes."
            );
            setOpen(true);
            setShowInfoDialog("SaveBtn");
            setDirty(true);
            setNeedCommitOrRevert(false);
            if (resp.locker) {
              setIsStealLockable(true);
            }
          }
        } else {
          setTitle("Save");
          setContent("Save failed. Reason: " + resp.message);
          setOpen(true);
          setShowInfoDialog("SaveBtn");
          setDirty(true);
          setNeedCommitOrRevert(false);
          if (resp.locker) {
            setIsStealLockable(true);
          }
        }
      })
      .catch((error) => {
        setTitle("Save");
        setContent(
          "Unexpected error occurs. Please contact administrator for assistance"
        );
        setOpen(true);
        setShowInfoDialog("SaveBtn");
        console.log("error: " + error.message);
      })
      .finally(() => {
        setLoading(false);
        setClearSelectedRows(!clearSelectedRows);
      });
  };

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };

  const iconBtnSave: IIconButtonProps = {
    tooltip: "Save",
    icon: <SaveIcon />,
    handleIconBtnClick: handleSave,
  };

  return (
    <div>
      <TerminalConfigIconBtn {...iconBtnSave} disabled={loading || !isDirty} />
      {showAlert === "SaveBtn" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
      {showInfoDialog === "SaveBtn" && (
        <TerminalInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
    </div>
  );
}
