import React from "react";
import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { getTcData, getFileData } from "../../util/TcData";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import TerminalConfigAlert from "../popups/TerminalConfigAlert";
import TerminalConfigConfirm from "../popups/TerminalConfigConfirm";
import TerminalInfoDialog from "../popups/TerminalConfigInfoDialog";
import { Terminal, TcData } from "../../type";

import {
  oldDataAtom,
  dataAtom,
  loadingAtom,
  dirtyAtom,
  preHashCode<PERSON>tom,
  alllTerminals<PERSON>tom,
  unselectedTermina<PERSON><PERSON><PERSON>,
  show<PERSON><PERSON><PERSON><PERSON><PERSON>,
  showInfoD<PERSON><PERSON><PERSON><PERSON>,
  doSave<PERSON><PERSON>,
} from "../../context/TerminalConfigAtoms";

import { terminalRefresh<PERSON>tom } from "../../../util/ConfigToolUtil";

function RefreshIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" />
    </SvgIcon>
  );
}

export default function RefreshBtn() {
  const [loading, setLoading] = useAtom(loadingAtom);
  const [isDirty, setDirty] = useAtom(dirtyAtom);
  const [data, setData] = useAtom(dataAtom);
  const [, setOldData] = useAtom(oldDataAtom);
  const [open, setOpen] = React.useState(false);
  const [preHashCode] = useAtom(preHashCodeAtom);
  const [allTerminals] = useAtom(alllTerminalsAtom);
  const [, setUnselectedTerminals] = useAtom(unselectedTerminalsAtom);
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [content, setContent] = React.useState("");
  const [severity, setSeverity] = React.useState("info");
  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [title, setTitle] = React.useState("");
  const setDoSave = useSetAtom(doSaveAtom);
  const [terminalRefresh, setTerminalRefresh] = useAtom(terminalRefreshAtom);

  interface IParams {
    loadFromWorkDir?: boolean;
  }

  const handleClickOpen = (event: any) => {
    event.currentTarget.blur();
    setLoading(false);
    if (isDirty) {
      setOpen(true);
    } else {
      handleReload();
    }
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Refresh",
    icon: <RefreshIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  const handleSave = () => {
    setOpen(false);
    setDoSave(true);
  };
  const handleReload = () => {
    setOpen(false);
    setTerminalRefresh(true);
  };
  const resetUnselectedTerms = (tcData: TcData) => {
    let unselectedTerm: Terminal[] = [];
    for (let i = 0; i < allTerminals.length; i++) {
      let exists: boolean = false;
      let terminals = tcData.terminal || [];
      for (let j = 0; j < terminals.length; j++) {
        if (allTerminals[i].toUpperCase() === terminals[j].name.toUpperCase()) {
          exists = true;
          break;
        }
      }
      if (!exists) {
        let newTerm: Terminal = {
          id: getNewTerminalId(tcData),
          name: allTerminals[i],
          printers: [],
          unitAccess: [],
          bedAccess: [],
          screenLock: 0,
          defaultUnit: "",
          defaultBed: "",
          loadBalance: "",
          smartCard: "OPTIONAL",
        };
        unselectedTerm.push(newTerm);
      }
    }
    setUnselectedTerminals(unselectedTerm);
  };

  const getNewTerminalId = (myData: TcData): number => {
    let ret = 0;
    let terminals: Terminal[] = myData.terminal;
    for (let j = 0; j < terminals.length; j++) {
      if (ret <= terminals[j].id) {
        ret = terminals[j].id + 1;
      }
    }
    return ret;
  };

  const show = (show: boolean) => {};

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };

  return (
    <div>
      <TerminalConfigIconBtn {...iconBtnSetting} disabled={loading} />
      {showAlert === "RefreshBtn" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
      {showInfoDialog === "RefreshBtn" && (
        <TerminalInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
      <TerminalConfigConfirm
        open={open}
        show={show}
        text="You have unsaved data in Terminal Configuration. Do you want to save the changes?"
        onYes={handleSave}
        onNo={handleReload}
        onCancel={handleClose}
        title="Refresh"
      />
    </div>
  );
}
