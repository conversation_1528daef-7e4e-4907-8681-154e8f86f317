import React from "react";
import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { useAtom, useAtomValue } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import TerminalConfigConfirm from "../popups/TerminalConfigConfirm";
import { Terminal, TcData } from "../../type";
import { cloneDeep } from "lodash";
import {
  dataAtom,
  loadingAtom,
  selectedRowsAtom,
  dirtyAtom,
  unselectedTerminalsAtom,
  delTerminalsActionAtom,
  isEditAtom,
  clearSelectedRowsAtom,
} from "../../context/TerminalConfigAtoms";

function DeleteIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" />
    </SvgIcon>
  );
}

export default function DelTermBtn() {
  const [loading, setLoading] = useAtom(loadingAtom);
  const isEdit = useAtomValue(isEditAtom);
  const [selectedRows, setSelectedRows] = useAtom(selectedRowsAtom);
  const [unselectedTerminals, setUnselectedTerminals] = useAtom(
    unselectedTerminalsAtom
  );
  const [, setDirty] = useAtom(dirtyAtom);
  const [data, setData] = useAtom(dataAtom);
  const [delTerminalsAction, setDelTerminalsAction] = useAtom(
    delTerminalsActionAtom
  );
  const [clearSelectedRows, setClearSelectedRows] = useAtom(
    clearSelectedRowsAtom
  );
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = (event: any) => {
    setLoading(false);
    setOpen(true);
    event.currentTarget.blur();
  };

  const delTerminals = () => {
    setOpen(false);
    let rows = selectedRows || [];
    let tmpData = cloneDeep(data) as TcData;
    let terminals: Terminal[] = tmpData?.terminal || [];
    let unselected = cloneDeep(unselectedTerminals);
    for (let i = 0; i < rows.length; i++) {
      let termName = rows[i].termName;
      for (let j = 0; j < terminals.length; j++) {
        if (termName.toUpperCase() === terminals[j].name.toUpperCase()) {
          unselected.push(terminals[j]);
          terminals.splice(j, 1);
          setDirty(true);
        }
      }
    }
    for (let i = 0; i < terminals.length; i++) {
      terminals[i].id = i + 1;
    }
    setData(tmpData);
    setUnselectedTerminals(unselected);
    setSelectedRows([]);
    setLoading(false);
    setDelTerminalsAction(!delTerminalsAction);
    setClearSelectedRows(!clearSelectedRows);
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Delete Terminal(s)",
    icon: <DeleteIcon />,
    handleIconBtnClick: handleClickOpen,
  };
  const show = (show: boolean) => {};
  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <div>
      <TerminalConfigIconBtn
        {...iconBtnSetting}
        disabled={
          loading || !selectedRows || selectedRows.length === 0 || !isEdit
        }
      />
      <TerminalConfigConfirm
        open={open}
        show={show}
        text="Are you sure you want to delete the terminal(s)?"
        onYes={delTerminals}
        onNo={handleCancel}
        title="Delete Terminals"
      />
    </div>
  );
}
