import SvgIcon, { SvgIconProps } from "@mui/material/SvgIcon";
import { useAtom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import { showLogAtom, loadingAtom } from "../../context/TerminalConfigAtoms";

function ShowLogIcon(props: SvgIconProps) {
  return (
    <SvgIcon {...props}>
      <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" />
    </SvgIcon>
  );
}

export default function ShowLogBtn() {
  const [, setShowLog] = useAtom(showLogAtom);
  const [loading, setLoading] = useAtom(loadingAtom);

  const handleClickOpen = (event: any) => {
    event.currentTarget.blur();
    setLoading(false);
    setShowLog(true);
  };

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Show Import Log",
    icon: <ShowLogIcon />,
    handleIconBtnClick: handleClickOpen,
  };

  return (
    <div>
      <TerminalConfigIconBtn {...iconBtnSetting} disabled={loading} />
    </div>
  );
}
