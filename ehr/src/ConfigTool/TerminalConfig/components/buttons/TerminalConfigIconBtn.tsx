import React from "react";
import { Tooltip, IconButton } from "@mui/material";

export interface IIconButtonProps {
  tooltip: string;
  icon: any;
  handleIconBtnClick: (event: any) => void;
  color?: string;
  disableColor?: string;
  style?: any;
  disabled?: boolean;
}

const TerminalConfigIconBtn: React.FunctionComponent<IIconButtonProps> = (
  props
): JSX.Element => {
  const color = props.color || "#426EB6";
  const disableColor = props.disableColor || "#b3b3b3";

  return (
    <Tooltip title={props.tooltip}>
      <span>
        <IconButton
          disabled={props.disabled}
          aria-label="button"
          onClick={props.handleIconBtnClick}
          sx={{
            marginLeft: "3px",
            padding: "2px",
          }}
          style={props.style}
          size="large"
        >
          {React.cloneElement(props.icon, {
            style: {
              ...props.icon.props.style,
              fill: props.disabled ? disableColor : color,
            },
          })}
        </IconButton>
      </span>
    </Tooltip>
  );
};
export default TerminalConfigIconBtn;
