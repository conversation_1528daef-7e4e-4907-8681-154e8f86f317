import { useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import { useAtom, useSetAtom } from "jotai/react";
import TerminalConfigIconBtn, {
  IIconButtonProps,
} from "./TerminalConfigIconBtn";
import TerminalInfoDialog from "../popups/TerminalConfigInfoDialog";
import {
  loadingAtom,
  isEditAtom,
  isStealLockableAtom,
  showInfoDialogAtom,
} from "../../context/TerminalConfigAtoms";
import {
  checkEdit,
  stealLock,
} from "@cci-monorepo/ConfigTool/util/DataRequest";

export default function EditBtn() {
  const [loading] = useAtom(loadingAtom);

  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [open, setOpen] = useState(false);
  const [content, setContent] = useState("");
  const [title, setTitle] = useState("");
  const [isEdit, setIsEdit] = useAtom(isEditAtom);
  const setIsStealLockable = useSetAtom(isStealLockableAtom);

  const iconBtnSetting: IIconButtonProps = {
    tooltip: "Edit",
    icon: <EditIcon />,
    handleIconBtnClick: (event) => {
      handleEditBtnClick(event);
    },
  };

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };

  const handleEditBtnClick = (event: any) => {
    event.currentTarget.blur();
    checkEdit("terminal").then(async (res: any) => {
      const data = res.data;
      if (!data.locker) {
        setIsEdit(true);
        setIsStealLockable(false);
        // lock
        await stealLock("terminal");
      } else {
        setIsEdit(false);
        setIsStealLockable(true);

        setTitle("Alert");
        setContent(data.message);
        setOpen(true);
        setShowInfoDialog("EditBtn");
      }
    });
  };

  return (
    <div>
      {showInfoDialog === "EditBtn" && (
        <TerminalInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
      <TerminalConfigIconBtn {...iconBtnSetting} disabled={loading || isEdit} />
    </div>
  );
}
