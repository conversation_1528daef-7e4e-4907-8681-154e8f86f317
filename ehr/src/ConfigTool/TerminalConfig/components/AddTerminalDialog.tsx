import React, { useState } from "react";
import { Box } from "@mui/material";
import { cloneDeep } from "lodash";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import TextField from "@mui/material/TextField";
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import TerminalConfigButton from "./buttons/TerminalConfigButton";
import TerminalConfigAlert from "./popups/TerminalConfigAlert";
import { ScopeTabDialog } from "@cci-monorepo/common";
import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";
import { useAtom } from "jotai/react";
import {
  data<PERSON>tom,
  dirty<PERSON>tom,
  loading<PERSON><PERSON>,
  terminal<PERSON><PERSON><PERSON><PERSON>,
  terminalE<PERSON><PERSON><PERSON>,
  unselectedTerminals<PERSON><PERSON>,
  showAlertAtom,
  retValue<PERSON>tom,
} from "../context/TerminalConfigAtoms";
import { TcData, Terminal } from "../type";

const addTerminalDialogStyle = {
  "& .MuiDialog-paper": {
    width: 500,
    //maxWidth: 900,
    minHeight: 700,
    maxHeight: 900,
    backgroundColor: "rgb(227, 234, 245)",
    overflowY: "visible",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

export const AddTerminalDialog = (props: any) => {
  const onClose = props.onClose;
  const open = props.open;
  const isEditName = props.editName;
  const selectedGridRows = props.selectedGridRows;

  const [retValue] = useAtom(retValueAtom);
  const [selectedIndex, setSelectedIndex] = React.useState(-1);
  const [terminalAdded, setTerminalAdded] = useAtom(terminalAddedAtom);
  const [terminalEdit, setTerminalEdit] = useAtom(terminalEditAtom);
  const [unselectedTerminals, setUnselectedTerminals] = useAtom(
    unselectedTerminalsAtom
  );
  const [, setDirty] = useAtom(dirtyAtom);
  const [, setLoading] = useAtom(loadingAtom);
  const [data, setData] = useAtom(dataAtom);
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);
  const [content, setContent] = React.useState("");
  const [severity, setSeverity] = React.useState("info");
  const [newTerminal, setNewTerminal] = React.useState<string>("");
  const [validTerminal, setValidTerminal] = React.useState<boolean>(true);
  const [sourceTerm, setSourceTerm] = useState<Terminal>({
    id: 0,
    name: "",
    printers: [],
    unitAccess: ["ALL"],
    bedAccess: ["ALL"],
    screenLock: 0,
    defaultUnit: "",
    defaultBed: "",
    loadBalance: "",
    smartCard: "OPTIONAL",
  });

  React.useEffect(() => {
    if (selectedGridRows.length === 0) {
      setSourceTerm({
        id: 0,
        name: "",
        printers: [],
        unitAccess: ["ALL"],
        bedAccess: ["ALL"],
        screenLock: 0,
        defaultUnit: "",
        defaultBed: "",
        loadBalance: "",
        smartCard: "OPTIONAL",
      });
    } else {
      let term = data?.terminal[selectedGridRows[0] - 1];
      setSourceTerm({
        id: 0,
        name: "",
        printers: term?.printers || [],
        unitAccess: term?.unitAccess || ["ALL"],
        bedAccess: term?.bedAccess || ["ALL"],
        screenLock: term?.screenLock || 0,
        defaultUnit: term?.defaultUnit,
        defaultBed: term?.defaultBed,
        loadBalance: term?.loadBalance,
        smartCard: term?.smartCard || "OPTIONAL",
      });
    }
  }, [selectedGridRows]);

  const handleListItemClick = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    index: number
  ) => {
    setSelectedIndex(index);
    setValidTerminal(true);
    setNewTerminal(unselectedTerminals[index].name);
  };

  React.useEffect(() => {}, []);

  const handleTextInput = (inputStr: string) => {
    const reg = new RegExp("^[0-9a-z][\\-a-z0-9]*$", "i");
    setValidTerminal(reg.test(inputStr));
    setNewTerminal(inputStr);
  };

  const handleOK = () => {
    setLoading(true);
    let tmpData = cloneDeep(data) as TcData;
    let terminals: Terminal[] = tmpData?.terminal || [];
    let unselected = cloneDeep(unselectedTerminals);
    if (!validTerminal) {
      setLoading(false);
      return;
    }
    if (!isEditName) {
      for (let i = 0; i < terminals.length; i++) {
        if (newTerminal.toUpperCase() === terminals[i].name.toUpperCase()) {
          setShowAlert("AddTerminal");
          setContent("Terminal name exists. Please choose another name.");
          setSeverity("error");
          setLoading(false);
          return;
        }
      }
      for (let j = 0; j < unselectedTerminals.length; j++) {
        if (newTerminal.toUpperCase() === unselected[j].name.toUpperCase()) {
          unselected.splice(j, 1);
          break;
        }
      }
      let newTerm: Terminal = {
        id: 0,
        name: newTerminal,
        printers: sourceTerm.printers,
        unitAccess: sourceTerm.unitAccess,
        bedAccess: sourceTerm.bedAccess,
        screenLock: sourceTerm.screenLock,
        defaultUnit: sourceTerm.defaultUnit,
        defaultBed: sourceTerm.defaultBed,
        loadBalance: sourceTerm.loadBalance,
        smartCard: sourceTerm.smartCard,
      };
      terminals.unshift(newTerm);

      for (let i = 0; i < terminals.length; i++) {
        terminals[i].id = i + 1;
      }
      setData(tmpData);
      setUnselectedTerminals(unselected);
      setDirty(true);
      setTerminalAdded(!terminalAdded);
      setLoading(false);
      onClose();
    } else {
      //edit terminal name
      let term: Terminal = cloneDeep(terminals[retValue.rowid - 1]);
      unselected.unshift(term);
      terminals[retValue.rowid - 1].name = newTerminal;
      for (let j = 0; j < unselectedTerminals.length; j++) {
        if (newTerminal.toUpperCase() === unselected[j].name.toUpperCase()) {
          unselected.splice(j, 1);
          break;
        }
      }
      setData(tmpData);
      setUnselectedTerminals(unselected);
      setDirty(true);
      setTerminalEdit(!terminalEdit);
      setLoading(false);
      onClose();
    }
  };

  const handleCancel = () => {
    onClose();
  };

  const handleClose = () => {
    handleCancel();
  };

  return (
    <Box>
      <ScopeTabDialog
        open={open}
        sx={addTerminalDialogStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        onClose={handleCancel}
      >
        <DialogTitle
          style={{
            cursor: "move",
            height: "50px",
            color: "white",
            backgroundColor: "black",
            display: "flex",
            alignItems: "center",
          }}
        >
          Name
          <Button
            size="small"
            variant="outlined"
            onClick={handleClose}
            sx={{
              fontWeight: "bold",
              fontSize: "14px",
              marginLeft: "360px",
              backgroundColor: "white",
              color: "black",
              textTransform: "none",
            }}
          >
            Close
          </Button>
        </DialogTitle>
        <DialogContent>
          <Box
            component="div"
            sx={{
              height: "700px",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "white",
              overflowY: "auto",
              padding: "15px",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            Select Name for this Terminal
            <Box
              component="div"
              sx={{
                height: "500px",
                marginTop: "20px",
                display: "flex",
                border: 1,
                flexDirection: "column",
                backgroundColor: "white",
                overflowY: "auto",
                padding: "15px",
                fontSize: "16px",
                fontWeight: "light",
              }}
            >
              <List component="nav">
                {unselectedTerminals.map((ter: Terminal, index: number) => (
                  <ListItemButton
                    key={index}
                    selected={selectedIndex === index}
                    onClick={(event) => handleListItemClick(event, index)}
                  >
                    <ListItemText primary={ter.name} />
                  </ListItemButton>
                ))}
              </List>
            </Box>
            <Box
              sx={{
                marginBottom: 0,
                marginTop: 2,
                overflowY: "auto",
                padding: "0px",
                fontWeight: "bold",
              }}
            >
              Enter Name for this terminal:
            </Box>
            <Box
              sx={{
                fontSize: "16px",
                fontWeight: "light",
              }}
            >
              <TextField
                autoFocus
                InputProps={{
                  inputProps: {
                    style: {
                      height: "30px",
                      width: "395px",
                      textAlign: "left",
                    },
                  },
                }}
                helperText={!validTerminal ? "Please input a valid name!" : ""}
                size="small"
                variant="outlined"
                error={!validTerminal}
                onChange={(event) => handleTextInput(event.target.value)}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text="Cancel" handleClick={handleCancel} />
          <TerminalConfigButton
            text="OK"
            handleClick={handleOK}
            disabled={newTerminal === ""}
          />
        </DialogActions>
      </ScopeTabDialog>
      {showAlert === "AddTerminal" && (
        <TerminalConfigAlert content={content} severity={severity} />
      )}
    </Box>
  );
};
