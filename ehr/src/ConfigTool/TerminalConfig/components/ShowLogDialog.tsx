import { Box } from "@mui/material";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { ScopeTabDialog } from "@cci-monorepo/common";
import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";
import TerminalConfigButton from "./buttons/TerminalConfigButton";
import { useAtom } from "jotai/react";
import { logContentAtom } from "../context/TerminalConfigAtoms";
import { DataGridPro, GridColDef, gridClasses } from "@mui/x-data-grid-pro";
import { styled } from "@mui/material/styles";

const StripedDataGrid = styled(DataGridPro)({
  cursor: "pointer",
  [`& .${gridClasses.row}.even`]: {
    backgroundColor: "#F7F7F7",
    "&:hover, &.Mui-hovered": {
      backgroundColor: "#F6E596",
    },
    "&.Mui-selected": {
      backgroundColor: "#F7F7F7",
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#F6E596",
      },
    },
  },
  [`& .${gridClasses.row}.odd`]: {
    backgroundColor: "#FFFFFF",
    "&:hover, &.Mui-hovered": {
      backgroundColor: "#F6E596",
    },
    "&.Mui-selected": {
      backgroundColor: "#FFFFFF",
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#F6E596",
      },
    },
  },
  "& .MuiDataGrid-columnHeaders": {
    backgroundColor: "#D8DCE3",
    color: "black",
  },
});

const ShowLogDialogStyle = {
  "& .MuiDialog-paper": {
    minWidth: 1000,
    maxWidth: 1500,
    minHeight: 600,
    maxHeight: 900,
    backgroundColor: "rgb(255, 255, 255)",
    overflowY: "visible",
    fontWeight: "light",
    fontSize: "16px",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
    minWidth: 1000,
    maxWidth: 1500,
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

export const ShowLogDialog = (props: any) => {
  const title = props.title;
  const onClose = props.onClose;
  const open = props.open;
  const [logContent] = useAtom(logContentAtom);
  const columns: GridColDef[] = [
    { field: "line", headerName: "Line", width: 80, disableColumnMenu: true },
    {
      field: "column",
      headerName: "Column Line",
      width: 200,
      disableColumnMenu: true,
    },
    {
      field: "value",
      headerName: "Value",
      width: 200,
      disableColumnMenu: true,
    },
    {
      field: "issue",
      headerName: "Issue",
      width: 350,
      disableColumnMenu: true,
    },
  ];
  const handleOK = () => {
    onClose();
  };

  return (
    <Box>
      <ScopeTabDialog
        open={open}
        sx={ShowLogDialogStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        onClose={handleOK}
      >
        <DialogTitle
          style={{
            cursor: "move",
            height: "50px",
            color: "white",
            backgroundColor: "black",
            display: "flex",
            alignItems: "center",
          }}
        >
          {title}
          <Button
            size="small"
            variant="outlined"
            onClick={handleOK}
            sx={{
              fontWeight: "bold",
              fontSize: "16px",
              marginLeft: "800px",
              backgroundColor: "white",
              color: "black",
              textTransform: "none",
            }}
          >
            Close
          </Button>
        </DialogTitle>
        <DialogContent>
          <Box>
            <Box
              component="div"
              sx={{
                height: "5%",
                width: "900px",
                display: "flex",
                flexDirection: "row",
                marginTop: "30px",
                marginBottom: "10px",
                alignItems: "center",
              }}
            >
              {logContent.msg}
            </Box>
            <Box
              component="div"
              sx={{
                height: "5%",
                width: "900px",
                display: "flex",
                flexDirection: "row",
                marginTop: "20px",
                marginBottom: "10px",
                alignItems: "center",
                fontWeight: "bold",
                fontSize: "16px",
              }}
            >
              {logContent.title}
            </Box>
            <Box
              sx={{
                height: "80%",
                width: "1000px",
                padding: "0 25px 0 25px",
              }}
            >
              <StripedDataGrid
                columns={columns}
                rows={logContent.Data || []}
                showCellVerticalBorder={true}
                showColumnVerticalBorder={true}
                hideFooter
                getRowClassName={(params) =>
                  params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even"
                }
                sx={{
                  height: "500px",
                  width: "900px",
                  marginTop: "20px",
                  overflowY: "auto",
                  padding: "15px",
                  boxShadow: 2,
                  border: 2,
                }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text="OK" handleClick={handleOK} />
        </DialogActions>
      </ScopeTabDialog>
    </Box>
  );
};
