import QuickTreeItem from "./QuickTreeItem";

interface IQuickTreeItemProps {
  id: string;
  level: number;
  label: string;
  parent?: IQuickTreeItemProps;
  children?: IQuickTreeItemProps[];
}

export const QuickTreeView = (props: any) => {
  return (
    <ul id={"ul" + props.id} key={"ul_" + props.id}>
      {props.treeData.map((node: IQuickTreeItemProps) => (
        <QuickTreeItem
          id={
            "QuickTreeItem" + node.parent
              ? node.parent?.id + "_"
              : "_" + node.id
          }
          node={node}
          allTreeData={props.allTreeData}
        ></QuickTreeItem>
      ))}
    </ul>
  );
};
export default QuickTreeView;
