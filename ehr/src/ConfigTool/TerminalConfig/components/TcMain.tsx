import React, { useState } from "react";
import { styled } from "@mui/material/styles";
import { format } from "date-fns";
import { Box } from "@mui/material";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import {
  DataGridPro,
  useGridApiRef,
  GridColDef,
  GridValueGetterParams,
  GridCellParams,
  GridRowSelectionModel,
  GridCallbackDetails,
  gridClasses,
} from "@mui/x-data-grid-pro";
import { cloneDeep, parseInt } from "lodash";
import {
  getTcData,
  getFileData,
  getBoottabData,
  parseBoottab,
  createUnitBedMap,
} from "../util/TcData";
import {
  MasterDetail,
  OptionItem,
  Terminal,
  Printer,
  Unit,
  TcData,
  Userprinter,
  MasterDetail3Layer,
  myCell,
  termRow,
  Bed,
} from "../type";
import { SingleSelectDialog } from "./SingleSelectDialog";
import { AddTerminalDialog } from "./AddTerminalDialog";
import { MultiSelectDialog } from "./MultiSelectDialog";
import { MultiSelectDialog3Layers } from "./MultiSelectDialog3Layers";
import TerminalConfigInfoDialog from "./popups/TerminalConfigInfoDialog";
import TerminalConfigConfirm from "./popups/TerminalConfigConfirm";
import { ShowLogDialog } from "./ShowLogDialog";
import {
  terminalRefreshAtom,
  terminalReOrderAtom,
} from "../../util/ConfigToolUtil";

import {
  dataAtom,
  oldDataAtom,
  dirtyAtom,
  loadingAtom,
  retValueAtom,
  retValueAtom_multi,
  retValueAtom_multi3Layers,
  delTerminalsActionAtom,
  exportDataActionAtom,
  exportDataAtom,
  terminalAddedAtom,
  terminalEditAtom,
  selectedRowsAtom,
  unselectedTerminalsAtom,
  exportedColumnsAtom,
  exportSelectedDataAtom,
  exportUnitMapAtom,
  exportBedMapAtom,
  exportUnitMapRevAtom,
  exportBedMapRevAtom,
  screenLocksAtom,
  smartCardOptionsAtom,
  showLogAtom,
  preHashCodeAtom,
  isSmartCardEditingEnabledAtom,
  alllTerminalsAtom,
  showInfoDialogAtom,
  isEditAtom,
  clearSelectedRowsAtom,
  importedRowsAtom,
  importDataAtom,
  selectedGridRowsAtom,
} from "../context/TerminalConfigAtoms";

import { useAtom, useAtomValue } from "jotai/react";
import { setTerminalConfigDataDirty } from "@cci-monorepo/ConfigTool/util/ConfigToolUtil";
import {
  pageIndicesAtom,
  selectedTabIndexAtom,
} from "@cci-monorepo/ConfigTool/Cdsrule/context/CtaAtoms";

interface IQuickTreeItemProps {
  id: string;
  level: number;
  label: string;
  parent?: IQuickTreeItemProps;
  children?: IQuickTreeItemProps[];
}

interface IParams {
  loadFromWorkDir?: boolean;
}

export type MenuItemHandler = {
  label: string;
  disabled: boolean;
  handler: (cell: myCell) => void;
};

export default function TcMain() {
  const apiRef = useGridApiRef();
  const [data, setData] = useAtom(dataAtom);
  const [oldData, setOldData] = useAtom(oldDataAtom);
  const [exportedColumns] = useAtom(exportedColumnsAtom);
  const [screenLocks] = useAtom(screenLocksAtom);
  const [smartCardOptions] = useAtom(smartCardOptionsAtom);

  const isEdit = useAtomValue(isEditAtom);

  const [delTerminalsAction] = useAtom(delTerminalsActionAtom);

  const [, setPreHashCode] = useAtom(preHashCodeAtom);

  const [, setIsSmartCardEditingEnabled] = useAtom(
    isSmartCardEditingEnabledAtom
  );

  const [content, setContent] = React.useState("");

  const [showInfoDialog, setShowInfoDialog] = useAtom(showInfoDialogAtom);
  const [open, setOpen] = React.useState(false);

  const [exportDataAction] = useAtom(exportDataActionAtom);
  const [exportData, setExportData] = useAtom(exportDataAtom);
  const [exportSelectedData, setExportSelectedData] = useAtom(
    exportSelectedDataAtom
  );
  const [exportUnitMap, setExportUnitMap] = useAtom(exportUnitMapAtom);
  const [exportBedMap, setExportBedMap] = useAtom(exportBedMapAtom);
  const [, setExportUnitMapRev] = useAtom(exportUnitMapRevAtom);
  const [, setExportBedMapRev] = useAtom(exportBedMapRevAtom);

  const [showLog, setShowLog] = useAtom(showLogAtom);

  const [isDirty, setDirty] = useAtom(dirtyAtom);

  const [, setLoading] = useAtom(loadingAtom);

  const [retValue, setRetValue] = useAtom(retValueAtom);

  const [retValue_multi, setRetValue_multi] = useAtom(retValueAtom_multi);

  const [retValue_multi3Layers, setRetValue_multi3Layers] = useAtom(
    retValueAtom_multi3Layers
  );

  const [terminalAdded, setTerminalAdded] = useAtom(terminalAddedAtom);

  const [terminalEdit] = useAtom(terminalEditAtom);

  const [unselectedTerminals, setUnselectedTerminals] = useAtom(
    unselectedTerminalsAtom
  );

  const [allTerminals, setAllTerminals] = useAtom(alllTerminalsAtom);

  const [selectedGridRows, setSelectedGridRows] = useAtom(selectedGridRowsAtom);

  const [selectedRows, setSelectedRows] = useAtom(selectedRowsAtom);

  const [clearSelectedRows] = useAtom(clearSelectedRowsAtom);
  const importData = useAtomValue(importDataAtom);
  const importedRows = useAtomValue(importedRowsAtom);

  const [selectedCell, setSelectedCell] = React.useState<myCell>({
    rowId: -1,
    field: "",
    value: "",
    myData: null,
    selectedRows: [],
  });

  //only useful for right-clicke. Value will be ignored on left-click.
  const [currentRow, setCurrentRow] = React.useState<number>(-1);
  const [openAddTerminal, setOpenAddTerminal] = React.useState(false);

  const [openSingleSelect, setOpenSingleSelect] = React.useState(false);
  const [openMultiSelect, setOpenMultiSelect] = React.useState(false);
  const [openMultiSelect3Layer, setOpenMulti3LayerSelect] =
    React.useState(false);
  const [isEditName, setIsEditName] = React.useState(false);

  const [items, setItems] = React.useState<string[]>([]);
  const [, setMultiItems] = React.useState<MasterDetail[]>([]);
  const [, setMultiItems3Layer] = React.useState<MasterDetail3Layer[]>([]);
  const [title, setTitle] = React.useState("");
  const [subtitle, setSubtitle] = React.useState("");
  const [columns, setColumns] = React.useState<GridColDef[]>([]);

  const [checked, setChecked] = React.useState<string[]>([]);

  const [propOpened, setPropOpened] = React.useState<string[]>([]);

  const [showConfirm, setShowConfirm] = React.useState<string>("");
  const [terminalRefresh, setTerminalRefresh] = useAtom(terminalRefreshAtom);
  const [terminalReOrder, setTerminalReOrder] = useAtom(terminalReOrderAtom);
  const [searchText, setSearchText] = React.useState("");
  const [matchedIndex, setMatchedIndex] = React.useState(-1);
  const [matchedId, setMatchedId] = React.useState(-1);
  const [shift, setShift] = React.useState(false);

  const handleSingleSelectClickOpen = () => {
    setOpenSingleSelect(true);
  };

  const handleSingleSelectClose = (value: string) => {
    setOpenSingleSelect(false);
  };

  const handleMultiSelectClickOpen = () => {
    setOpenMultiSelect(true);
  };

  const handleMultiSelectClose = (value: string) => {
    setOpenMultiSelect(false);
  };

  const handleMultiSelect3LayerClickOpen = () => {
    setOpenMulti3LayerSelect(true);
  };

  const handleMultiSelect3LayerClose = (value: string) => {
    setOpenMulti3LayerSelect(false);
  };

  const [contextMenu, setContextMenu] = React.useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);
  const [contextMenuItems, setContextMenuItems] = React.useState<
    MenuItemHandler[]
  >([]);

  const [nodes, setNodes] = React.useState<IQuickTreeItemProps[]>([]);
  const [nodes3Layer, setNodes3Layer] = React.useState<IQuickTreeItemProps[]>(
    []
  );

  const selectedTabIndex = useAtomValue(selectedTabIndexAtom);
  const pageIndices = useAtomValue(pageIndicesAtom);

  React.useEffect(() => {
    setTerminalConfigDataDirty(isDirty);
  }, [isDirty]);

  React.useEffect(() => {
    reload();
  }, []);

  React.useEffect(() => {
    if (terminalRefresh) {
      setTerminalRefresh(false);
      reload();
      setTerminalConfigDataDirty(false);
    }
  }, [terminalRefresh]);

  React.useEffect(() => {
    if (terminalReOrder) {
      setTerminalReOrder(false);
      if (apiRef.current && apiRef.current.setSortModel) {
        apiRef.current.setSortModel([
          {
            field: "name",
            sort: "asc",
          },
          {
            field: "totalCharges",
            sort: "desc",
          },
        ]);
      }
    }
  }, [terminalReOrder]);

  const reload = () => {
    getFileData().then((rawdata) => {
      if (rawdata.success) {
        setIsSmartCardEditingEnabled(rawdata.isCacAuthenEditingEnabled);
        let cols: GridColDef[] = generateColumns(
          rawdata.isCacAuthenEditingEnabled
        );
        setColumns(cols);
        let tcData = getTcData(rawdata.content, rawdata.hash);
        tcData.preHashCode = "";
        setPreHashCode("");
        let clonedTcData = cloneDeep(tcData);
        setData(tcData);
        setDirty(false);
        setOldData(clonedTcData);
        let retMaps = createUnitBedMap(tcData);
        setExportUnitMap(retMaps[0]);
        setExportBedMap(retMaps[1]);
        setExportUnitMapRev(retMaps[2]);
        setExportBedMapRev(retMaps[3]);
        getBoottabData().then((bd) => {
          const termArr = parseBoottab(bd.content);
          setAllTerminals(termArr);
        });
        setSelectedRows([]);
        setSelectedGridRows([]);
        if (apiRef.current && apiRef.current.setSortModel) {
          apiRef.current.setSortModel([
            {
              field: "name",
              sort: "asc",
            },
            {
              field: "totalCharges",
              sort: "desc",
            },
          ]);
        }
      }
    });
  };

  React.useEffect(() => {
    let unselectedTerm: Terminal[] = [];
    let tcData = data as TcData;
    for (let i = 0; i < allTerminals.length; i++) {
      let exists: boolean = false;
      let terminals = data?.terminal || [];
      for (let j = 0; j < terminals.length; j++) {
        if (allTerminals[i].toUpperCase() === terminals[j].name.toUpperCase()) {
          exists = true;
          break;
        }
      }
      if (!exists) {
        let newTerm: Terminal = {
          id: getNewTerminalId(tcData),
          name: allTerminals[i],
          printers: [],
          unitAccess: [],
          bedAccess: [],
          screenLock: 0,
          defaultUnit: "",
          defaultBed: "",
          loadBalance: "",
          smartCard: "OPTIONAL",
        };
        unselectedTerm.push(newTerm);
      }
    }
    setUnselectedTerminals(unselectedTerm);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allTerminals]);

  React.useEffect(() => {
    let tmpData = data as TcData;
    if (tmpData !== null) {
      switch (retValue.col) {
        case "printers":
          if (retValue.ind >= 0) {
            let ps: Userprinter[] = [
              {
                printer: tmpData.printer[retValue.ind].name,
                printerType:
                  tmpData.terminal[retValue.rowid - 1].printers.length > 0
                    ? tmpData.terminal[retValue.rowid - 1].printers[0]
                        .printerType
                    : "MANUAL",
              },
            ];
            tmpData.terminal[retValue.rowid - 1].printers = ps;
          }
          break;
        case "defaultUnit":
          if (retValue.ind >= 0) {
            if (retValue.ind === tmpData.unit.length) {
              tmpData.terminal[retValue.rowid - 1].defaultUnit = "";
              tmpData.terminal[retValue.rowid - 1].defaultBed = "";
            } else {
              tmpData.terminal[retValue.rowid - 1].defaultUnit =
                tmpData.unit[retValue.ind].name;
              let defBed: string =
                apiRef?.current?.getRow(retValue.rowid).defaultBed || "";
              if (defBed.length > 0) {
                let beds: Bed[] = [];
                for (let i = 0; i < tmpData.unit.length; i++) {
                  let ut = tmpData.unit[i];
                  if (
                    ut.name.toUpperCase() ===
                    tmpData.unit[retValue.ind].name.toUpperCase()
                  ) {
                    beds = ut.bed || [];
                    break;
                  }
                }
                if (beds.length > 0) {
                  let exists = false;
                  for (let j = 0; j < beds.length; j++) {
                    if (beds[j].name.toUpperCase() === defBed.toUpperCase()) {
                      exists = true;
                      break;
                    }
                  }
                  if (!exists) {
                    tmpData.terminal[retValue.rowid - 1].defaultBed = "";
                  }
                } else {
                  tmpData.terminal[retValue.rowid - 1].defaultBed = "";
                }
              }
            }
          }
          break;
        case "defaultBed":
          if (retValue.ind >= 0) {
            let defUnit: string =
              apiRef?.current?.getRow(retValue.rowid).defaultUnit || "";
            for (let i = 0; i < tmpData.unit.length; i++) {
              let ut = tmpData.unit[i];
              if (ut.name.toUpperCase() === defUnit.toUpperCase()) {
                let beds = ut.bed || [];
                if (retValue.ind === beds.length) {
                  tmpData.terminal[retValue.rowid - 1].defaultBed = "";
                } else {
                  tmpData.terminal[retValue.rowid - 1].defaultBed =
                    beds[retValue.ind].name;
                }
                break;
              }
            }
          }
          break;
        case "smartCard":
          if (retValue.ind >= 0) {
            tmpData.terminal[retValue.rowid - 1].smartCard =
              items[retValue.ind];
          }
          break;
        case "screenLock":
          if (retValue.ind >= 0) {
            tmpData.terminal[retValue.rowid - 1].screenLock =
              items[retValue.ind];
          }
          break;
      }
      setData(tmpData);
      //force update a cell before the datagrid's state is changed.
      if (apiRef && apiRef.current && apiRef.current.selectRow) {
        apiRef.current.selectRow(retValue.rowid, true, true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [retValue]);

  React.useEffect(() => {
    let tmpData = data as TcData;
    if (tmpData !== null) {
      switch (retValue_multi.col) {
        case "unitAccess":
          tmpData.terminal[retValue_multi.rowid - 1].unitAccess =
            retValue_multi.val;
          let arr = generateMultiItems3Layer(
            retValue_multi.rowid,
            "unitAccessBeds",
            tmpData
          );
          rebuildBedAccess(
            retValue_multi.rowid as number,
            tmpData.terminal[retValue_multi.rowid - 1] as Terminal,
            arr as MasterDetail3Layer[]
          );
          break;
      }
      setData(tmpData);
      //force update a cell before the datagrid's state is changed.
      if (apiRef && apiRef.current && apiRef.current.selectRow) {
        apiRef.current.selectRow(retValue_multi.rowid, true, true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [retValue_multi]);

  React.useEffect(() => {
    let tmpData = data as TcData;
    if (tmpData !== null) {
      switch (retValue_multi3Layers.col) {
        case "bedAccess":
          tmpData.terminal[retValue_multi3Layers.rowid - 1].bedAccess =
            retValue_multi3Layers.val;
          break;
      }
      setData(tmpData);
      //force update a cell before the datagrid's state is changed.
      if (apiRef && apiRef.current && apiRef.current.selectRow) {
        apiRef.current.selectRow(retValue_multi3Layers.rowid, true, true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [retValue_multi3Layers]);

  React.useEffect(() => {
    if (apiRef.current && exportData) {
      setExportData(false);
      let allRows = apiRef.current.getSortedRows();
      let now: string = format(new Date(), "yyyy-MMM-dd");
      let fileName = "terminals-" + now + ".csv";
      let fileContent: string = generateExportData(allRows);
      let blob = new Blob([fileContent], { type: "text/cvs" });
      let url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      a.click();
      setTimeout(() => {
        URL.revokeObjectURL(url);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exportDataAction]);

  React.useEffect(() => {
    if (!isEditName) {
      if (apiRef && apiRef.current && apiRef.current.selectRow) {
        apiRef.current.selectRow(1, true, true);
        let tmpArr: termRow[] = [];
        tmpArr.push({
          rowId: 1,
          termName: data?.terminal[0].name as string,
        });
        setSelectedRows(tmpArr);
        setSelectedGridRows([1]);
      }
    } else {
      apiRef.current.selectRow(retValue.rowid, true, true);
      let tmpArr: termRow[] = [];
      tmpArr.push({
        rowId: retValue.rowid,
        termName: data?.terminal[retValue.rowid - 1].name as string,
      });
      setSelectedRows(tmpArr);
      setSelectedGridRows([1]);
    }
  }, [terminalAdded, terminalEdit, apiRef, isEditName]);

  React.useEffect(() => {
    if (apiRef && apiRef.current && apiRef.current.selectRows) {
      apiRef.current.selectRows(apiRef.current.getAllRowIds(), false, true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [delTerminalsAction]);

  React.useEffect(() => {
    setSelectedRows([]);
    setSelectedGridRows([]);
  }, [clearSelectedRows]);

  React.useEffect(() => {
    let tmpArr: termRow[] = [];
    let rowId = 0;
    for (let i = 0; i < importedRows.length; i++) {
      rowId = importedRows[i];
      tmpArr.push({
        rowId: rowId,
        termName: data?.terminal[rowId - 1].name as string,
      });
    }
    setSelectedRows(tmpArr);
    setSelectedGridRows(importedRows);
  }, [importData]);

  const setMatched = (i: number) => {
    setMatchedIndex(i);
    const sortedRows = apiRef.current.getSortedRows() ?? [];
    if (i >= 0 && i < sortedRows.length) {
      setMatchedId(sortedRows[i].id);
      scrollToRow(i);
    } else {
      setMatchedId(-1);
    }
  };

  const scrollToRow = (i: number) => {
    const sortedRows = apiRef.current.getSortedRows() ?? [];
    if (i >= 0 && i < sortedRows.length) {
      const rowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(
        sortedRows[i].id
      );
      const colIndex = apiRef.current.getColumnIndex("name", false);
      const indexes = { rowIndex, colIndex };
      setTimeout(() => {
        apiRef.current.scrollToIndexes(indexes);
      }, 200);
    }
  };

  React.useEffect(() => {
    const sortedRows = apiRef.current.getSortedRows() ?? [];
    const findPrevious = () => {
      findItem(searchText, matchedIndex - 1, 0, false, false);
    };

    const findNext = () => {
      findItem(
        searchText,
        matchedIndex + 1,
        sortedRows.length ? sortedRows.length - 1 : 0,
        true,
        false
      );
    };

    const findStart = (newText: string) => {
      findItem(
        newText,
        0,
        sortedRows.length ? sortedRows.length - 1 : 0,
        true,
        true
      );
    };

    const findItem = (
      text: string,
      start: number,
      end: number,
      forward: boolean,
      removeSelectionIfNotMatched: boolean
    ) => {
      let i: number,
        hasItem: boolean = false;
      for (
        i = start;
        text && (forward ? i <= end : i >= end);
        forward ? i++ : i--
      ) {
        if (
          sortedRows.length > 0 &&
          sortedRows[i].name.toLowerCase().includes(text.toLowerCase())
        ) {
          setMatched(i);
          hasItem = true;
          break;
        }
      }
      if (!hasItem && removeSelectionIfNotMatched) {
        setMatched(-1);
      }
    };
    const handleKeyDown = (event: any) => {
      let current = apiRef.current;
      if (
        !current ||
        (document.activeElement?.className &&
          document.activeElement?.className.indexOf("MuiInputBase-input") >= 0)
      ) {
        return;
      }
      let newText = "";
      if (event.key.length === 1) {
        newText = searchText + event.key;
        setSearchText(newText);
        findStart(newText);
      } else {
        switch (event.key) {
          case "Escape":
            setSearchText("");
            setMatched(-1);
            break;
          case "Backspace":
            if (searchText.length > 0) {
              newText = searchText.slice(0, -1);
              setSearchText(newText);
              findStart(newText);
              if (newText.length === 0) {
                setMatched(-1);
              }
            }
            break;
          case "F3":
            if (shift) {
              findPrevious();
            } else {
              findNext();
            }
            event.preventDefault();
            event.stopPropagation();
            break;
          case "Shift":
            setShift(true);
            break;
          case "Enter":
            if (selectedGridRows.includes(sortedRows[matchedIndex].id)) {
              setSelectedGridRows(
                selectedGridRows.filter(
                  (item) => item !== sortedRows[matchedIndex].id
                )
              );
            } else {
              setSelectedGridRows([
                ...selectedGridRows,
                sortedRows[matchedIndex].id,
              ]);
            }
            scrollToRow(matchedIndex);
            setSearchText("");
            setMatched(-1);
        }
      }
    };

    const handleKeyUp = (event: any) => {
      switch (event.key) {
        case "Shift":
          setShift(false);
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("keyup", handleKeyUp);
    };
  }, [searchText, matchedIndex, shift]);

  const datagridSx = {
    marginTop: "0px",
    position: "relative",
    width: "100%",
    height: "100%",
    maxHeight: "100%",
    minHeight: "800px",
    cursor: "pointer",

    [`& div.MuiDataGrid-row[data-id="${matchedId}"]`]: {
      backgroundColor: "#C3D8F6",
    },
    [`& .${gridClasses.row}.even`]: {
      backgroundColor: "#F7F7F7",
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#F6E596",
      },
      "&.Mui-selected": {
        backgroundColor: "#FEC341",
        "&:hover, &.Mui-hovered": {
          backgroundColor: "#FEC341",
        },
      },
    },
    [`& .${gridClasses.row}.odd`]: {
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#F6E596",
      },
      "&.Mui-selected": {
        backgroundColor: "#FEC341",
        "&:hover, &.Mui-hovered": {
          backgroundColor: "#FEC341",
        },
      },
    },
    "& .MuiDataGrid-columnHeaders": {
      backgroundColor: "#D8DCE3",
      color: "black",
    },
  };

  const generateColumns = (IsSmartCardEdit: boolean): GridColDef[] => {
    let cols: GridColDef[] = [];
    cols.push({
      field: "name",
      headerName: "Name",
      width: 200,
      disableColumnMenu: true,
    });
    cols.push({
      field: "printers",
      headerName: "Printer",
      width: 150,
      disableColumnMenu: true,
      valueGetter: (params: GridValueGetterParams) => {
        return !params.row.printers?.length
          ? ""
          : params.row.printers[0].printer;
      },
    });
    cols.push({
      field: "unitAccess",
      headerName: "Units",
      width: 200,
      disableColumnMenu: true,
      valueGetter: (params: GridValueGetterParams) => {
        return !params.row.unitAccess?.length
          ? ""
          : params.row.unitAccess.join("|");
      },
    });
    cols.push({
      field: "bedAccess",
      headerName: "Beds",
      width: 200,
      disableColumnMenu: true,
      valueGetter: (params: GridValueGetterParams) => {
        return !params.row.bedAccess?.length
          ? ""
          : params.row.bedAccess.join("|");
      },
    });
    cols.push({
      field: "defaultUnit",
      headerName: "Default Unit",
      width: 150,
      disableColumnMenu: true,
    });
    cols.push({
      field: "defaultBed",
      headerName: "Default Bed",
      width: 150,
      disableColumnMenu: true,
    });
    cols.push({
      field: "screenLock",
      headerName: "Screen Lock",
      width: 100,
      disableColumnMenu: true,
    });
    if (IsSmartCardEdit) {
      cols.push({
        field: "smartCard",
        headerName: "Smart Card",
        width: 150,
        disableColumnMenu: true,
      });
    }
    setColumns(cols);
    return cols;
  };

  const generateExportData = (allRows: any[]): string => {
    let ret: string = "";
    let unitMap = exportUnitMap || new Map();
    let bedMap = exportBedMap || new Map();
    exportedColumns.forEach((m) => {
      if (m.selected) {
        if (ret.length === 0) {
          ret = '"' + m.title + '"';
        } else {
          ret = ret + ',"' + m.title + '"';
        }
      }
    });
    ret = ret + "\n";
    allRows.forEach((m) => {
      let sel = false;
      let first = true;
      if (exportSelectedData) {
        sel = false;
        for (let i = 0; i < selectedRows.length; i++) {
          if (m.id === selectedRows[i].rowId) {
            sel = true;
            break;
          }
        }
      } else {
        sel = true;
      }
      //back to "Export all data" mode for the next export
      setExportSelectedData(false);
      if (sel) {
        exportedColumns.forEach((k) => {
          let cellContent = "";
          let content = "";
          let arr: string[] = [];
          if (k.selected) {
            switch (k.field) {
              case "unitAccess":
                arr = m[k.field];
                if (arr) {
                  arr.forEach((el) => {
                    content = unitMap.get(el);
                    if (content !== undefined) {
                      if (cellContent === "") {
                        cellContent = content;
                      } else {
                        cellContent = cellContent + "|" + content;
                      }
                    }
                  });
                  cellContent = '"' + cellContent + '"';
                }
                if (first) {
                  ret = ret + cellContent;
                  first = false;
                } else {
                  ret = ret + "," + cellContent;
                }
                break;
              case "bedAccess":
                arr = m[k.field];
                if (arr) {
                  arr.forEach((el) => {
                    content = bedMap.get(el);
                    if (content !== undefined) {
                      if (cellContent === "") {
                        cellContent = content;
                      } else {
                        cellContent = cellContent + "|" + content;
                      }
                    }
                  });
                  cellContent = '"' + cellContent + '"';
                }
                if (first) {
                  ret = ret + cellContent;
                  first = false;
                } else {
                  ret = ret + "," + cellContent;
                }
                break;
              case "defaultUnit":
                content = unitMap.get(m[k.field]);
                if (content !== undefined) {
                  cellContent = '"' + content + '"';
                } else {
                  cellContent = '""';
                }
                if (first) {
                  ret = ret + cellContent;
                  first = false;
                } else {
                  ret = ret + "," + cellContent;
                }
                break;
              case "defaultBed":
                content = bedMap.get(m[k.field]);
                if (content !== undefined) {
                  cellContent = '"' + content + '"';
                } else {
                  cellContent = '""';
                }
                if (first) {
                  ret = ret + cellContent;
                  first = false;
                } else {
                  ret = ret + "," + cellContent;
                }
                break;
              case "printers":
                if (m[k.field] !== undefined && m[k.field].length > 0) {
                  cellContent = '"' + m[k.field][0].printer + '"';
                } else {
                  cellContent = '""';
                }
                if (first) {
                  ret = ret + cellContent;
                  first = false;
                } else {
                  ret = ret + "," + cellContent;
                }
                break;
              default:
                content = m[k.field];
                if (content !== undefined) {
                  cellContent = '"' + content + '"';
                } else {
                  cellContent = '""';
                }
                if (first) {
                  ret = ret + cellContent;
                  first = false;
                } else {
                  ret = ret + "," + cellContent;
                }
                break;
            }
          }
        });
        ret = ret + "\n";
      }
    });
    return ret;
  };

  const getNewTerminalId = (myData: TcData): number => {
    let ret = 0;
    let terminals: Terminal[] = myData.terminal;
    for (let j = 0; j < terminals.length; j++) {
      if (ret <= terminals[j].id) {
        ret = terminals[j].id + 1;
      }
    }
    return ret;
  };

  const allChildrenChecked = (arr: OptionItem[]): boolean => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].checked === false) {
        return false;
      }
    }
    return true;
  };

  const allMidChecked = (arr: MasterDetail[]): boolean => {
    for (let i = 0; i < arr.length; i++) {
      for (let j = 0; j < arr[i].children.length; j++) {
        if (arr[i].children[j].checked === false) {
          return false;
        }
      }
    }
    return true;
  };

  const rebuildBedAccess = (
    rowid: number,
    terminal: Terminal,
    arr: MasterDetail3Layer[]
  ) => {
    let bedAccess: string[] = terminal.bedAccess || [];
    for (let a = 0; a < bedAccess.length; a++) {
      for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < arr[i].children.length; j++) {
          for (let k = 0; k < arr[i].children[j].children.length; k++) {
            if (
              arr[i].children[j].children[k].name.toUpperCase() ===
              bedAccess[a].toUpperCase()
            ) {
              arr[i].children[j].children[k].checked = true;
              if (allChildrenChecked(arr[i].children[j].children)) {
                arr[i].children[j].parent.checked = true;
              }
              if (allMidChecked(arr[i].children)) {
                arr[i].parent.checked = true;
              }
            }
          }
        }
      }
    }
    let uts = [];
    for (let i = 0; i < arr.length; i++) {
      let camp = arr[i].parent.name;
      if (arr[i].parent.checked) {
        uts.push(camp);
        continue;
      } else {
        for (let j = 0; j < arr[i].children.length; j++) {
          for (let k = 0; k < arr[i].children[j].children.length; k++) {
            if (arr[i].children[j].children[k].checked === true) {
              uts.push(arr[i].children[j].children[k].name);
            }
          }
        }
      }
    }
    terminal.bedAccess = uts;
  };

  const handleCloseAddTerminal = (value: string) => {
    setOpenAddTerminal(false);
  };

  const onCellDoubleClick = (cell: GridCellParams) => {
    if (isEdit) {
      handleCellClick(
        cell.id as number,
        cell.field,
        cell.value as string,
        data
      );
    } else {
      setTitle("Alert");
      setContent(
        "Application is in Review mode, change to Edit mode to edit data."
      );
      setOpen(true);
      setShowInfoDialog("clickCell");
    }
  };

  const handleCellClick = (
    cellId: number,
    cellField: string,
    cellValue: string,
    myData: any
  ) => {
    let selectedIndex = -1;
    let multiSelected = [];
    let arr = [];
    let strcell: string = "";
    let tmpData = myData as TcData;
    let isTermCommitted = terminalCommitted(
      apiRef?.current?.getRow(cellId).name
    );
    switch (cellField) {
      case "name":
        if (!isTermCommitted) {
          setRetValue({ rowid: cellId, col: cellField, ind: cellId - 1 });
          setIsEditName(true);
          setOpenAddTerminal(true);
        }
        break;
      case "printers":
        setTitle("Printer");
        setSubtitle("Select default Printer for this terminal.");
        arr = generateItems("printers", tmpData);
        selectedIndex = getOptionSelectedIndex(arr, cellValue);
        setRetValue({ rowid: cellId, col: cellField, ind: selectedIndex });
        handleSingleSelectClickOpen();
        break;
      case "defaultUnit":
        setTitle("Default Unit");
        setSubtitle(
          "Select default Unit for this terminal (local campus only)."
        );
        arr = generateItems("units", tmpData);
        selectedIndex = getOptionSelectedIndex(arr, cellValue);
        setRetValue({ rowid: cellId, col: cellField, ind: selectedIndex });
        handleSingleSelectClickOpen();
        break;
      case "defaultBed":
        let defUnit: string = apiRef?.current?.getRow(cellId).defaultUnit || "";
        if (defUnit.length === 0) {
          setTitle("Info");
          setContent(
            "Default Unit must be defined before setting Default Bed."
          );
          setOpen(true);
          setShowInfoDialog("clickCell");
        } else {
          for (let i = 0; i < tmpData.unit.length; i++) {
            let ut = tmpData.unit[i];
            if (ut.name.toUpperCase() === defUnit.toUpperCase()) {
              let arr2: Bed[] = ut.bed || [];
              for (let i = 0; i < arr2.length; i++) {
                arr.push(arr2[i].name);
              }
              arr.push("<NONE>");
            }
          }
          setItems(arr);
          if (arr.length > 1) {
            setTitle("Default Bed");
            setSubtitle("Select default Bed for this terminal.");
            selectedIndex = getOptionSelectedIndex(arr as string[], cellValue);
            setRetValue({ rowid: cellId, col: cellField, ind: selectedIndex });
            handleSingleSelectClickOpen();
          } else {
            setTitle("Info");
            setContent("Unit '" + defUnit + "' has contains no beds");
            setOpen(true);
            setShowInfoDialog("clickCell");
          }
        }
        break;
      case "screenLock":
        setTitle("Screen Lock");
        setSubtitle(
          "Select Screen Lock for this terminal in miniutes (0=off, ON=always)."
        );
        arr = generateItems("screenLock", tmpData);
        selectedIndex = getOptionSelectedIndex(arr, cellValue + "");
        setRetValue({ rowid: cellId, col: cellField, ind: selectedIndex });
        handleSingleSelectClickOpen();
        break;
      case "smartCard":
        setTitle("Smart Card");
        setSubtitle(
          "Select Smart Card Authentication method for this terminal."
        );
        arr = generateItems("smartCard", tmpData);
        selectedIndex = getOptionSelectedIndex(arr, cellValue);
        setRetValue({ rowid: cellId, col: cellField, ind: selectedIndex });
        handleSingleSelectClickOpen();
        break;
      case "unitAccess":
        setTitle("Units");
        setSubtitle("Select Units to be accessible by this terminal.");

        strcell = cellValue as string;
        multiSelected = strcell.split("|");

        arr = generateMultiItems("unitAccess", tmpData, multiSelected);
        let opens = generateOpened(arr);
        setPropOpened(opens);

        arr = checkedMultiItems(multiSelected, arr);
        setRetValue_multi({
          rowid: cellId,
          col: cellField,
          val: multiSelected,
        });
        let checkedArr: string[] = generateChecked(arr);
        createNodes(arr, opens, checkedArr);

        handleMultiSelectClickOpen();
        break;
      case "bedAccess":
        setTitle("Beds");
        setSubtitle("Select beds that this terminal can access.");
        arr = generateMultiItems3Layer(
          cellId as number,
          "unitAccessBeds",
          tmpData
        );
        strcell = cellValue as string;
        multiSelected = strcell.split("|");
        arr = checkedMultiItems3Layer(multiSelected, arr);
        setRetValue_multi3Layers({
          rowid: cellId,
          col: cellField,
          val: multiSelected,
        });
        let checkedArr3Layer: string[] = generateChecked3Layer(arr);
        let opens3Layer = generateOpened3Layer(arr);
        setPropOpened(opens3Layer);
        createNodes3Layer(arr, opens3Layer, checkedArr3Layer);
        handleMultiSelect3LayerClickOpen();
        break;
    }
  };

  const generateOpened = (arr: MasterDetail[]): string[] => {
    let ret = [];
    for (let i = 0; i < arr.length; i++) {
      ret.push("Parent:" + arr[i].parent.name);
    }
    return ret;
  };

  const generateOpened3Layer = (arr: MasterDetail3Layer[]): string[] => {
    let ret = [];
    for (let i = 0; i < arr.length; i++) {
      ret.push("Parent:" + arr[i].parent.name); //initially expand campus node
      for (let j = 0; j < arr[i].children.length; j++) {
        let mid = arr[i].children[j];
        let midChildren = mid.children;
        if (midChildren.length > 0 && childrenChecked(midChildren)) {
          //initailly expand a unit node if it contains at least one selected bed.
          ret.push("Mid:" + arr[i].children[j].parent.name);
        }
      }
    }
    return ret;
  };

  const childrenChecked = (children: OptionItem[]): boolean => {
    for (let i = 0; i < children.length; i++) {
      if (children[i].checked) {
        return true;
      }
    }
    return false;
  };

  const generateChecked = (arr: MasterDetail[]): string[] => {
    let newChecked = [];
    for (let i = 0; i < arr.length; i++) {
      let obj = arr[i];
      if (obj.parent.checked) {
        newChecked.push("Parent:" + obj.parent.name);
      }
      for (let j = 0; j < obj.children.length; j++) {
        if (obj.children[j].checked) {
          newChecked.push("Child:" + obj.children[j].name);
        }
      }
    }
    setChecked(newChecked);
    return newChecked;
  };

  const generateChecked3Layer = (arr: MasterDetail3Layer[]): string[] => {
    let newChecked = [];
    for (let i = 0; i < arr.length; i++) {
      let obj = arr[i];
      if (obj.parent.checked) {
        newChecked.push("Parent:" + obj.parent.name);
      }
      for (let j = 0; j < obj.children.length; j++) {
        if (obj.children[j].parent.checked) {
          newChecked.push("Mid:" + obj.children[j].parent.name);
        }
        for (let k = 0; k < obj.children[j].children.length; k++) {
          if (obj.children[j].children[k].checked) {
            newChecked.push("Child:" + obj.children[j].children[k].name);
          }
        }
      }
    }
    setChecked(newChecked);
    return newChecked;
  };

  const generateItems = (type: string, myData: TcData): string[] => {
    let arr: string[] = [];
    switch (type) {
      case "printers":
        let arr1: Printer[] = myData.printer as Printer[];
        for (let i = 0; i < arr1?.length; ++i) {
          arr.push(arr1[i].name as string);
        }
        break;
      case "units":
        let arr2: Unit[] = myData.unit as Unit[];
        for (let i = 0; i < arr2?.length; ++i) {
          if (arr2[i].name.indexOf(":") < 0) {
            //local unit only
            arr.push(arr2[i].name as string);
          }
        }
        arr.push("<NONE>");
        break;
      case "smartCard":
        arr = smartCardOptions;
        break;
      case "screenLock":
        arr = screenLocks;
        break;
    }
    setItems(arr);
    return arr;
  };

  const generateMultiItems = (
    type: string,
    myData: TcData,
    multiSelected: string[]
  ): MasterDetail[] => {
    let arr: MasterDetail[] = [
      { parent: { name: "ALL", checked: false }, children: [] },
    ];
    switch (type) {
      case "unitAccess":
        let camp = "";
        let ut = "";
        let arr2: Unit[] = myData.unit as Unit[];
        for (let k = 0; k < multiSelected.length; k++) {
          if (multiSelected[k].indexOf(":") >= 0) {
            camp = multiSelected[k].split(":")[0] + ":ALL";
            ut = multiSelected[k].split(":")[0];
          } else {
            camp = "ALL";
            ut = multiSelected[k];
          }
          if (!parentExists(camp, arr)) {
            arr.push({ parent: { name: camp, checked: false }, children: [] });
          }
        }
        for (let i = 0; i < arr2?.length; ++i) {
          for (let j = 0; j < arr.length; j++) {
            if (arr2[i].name.indexOf(":") >= 0) {
              camp = arr2[i].name.split(":")[0] + ":ALL";
              ut = arr2[i].name.split(":")[0];
            } else {
              camp = "ALL";
              ut = arr2[i].name;
            }
            let beds = arr2[i].bed || [];
            if (
              arr[j].parent.name.toUpperCase() === camp.toUpperCase() &&
              beds.length > 0
            ) {
              //do not display units with no beds defined
              if (parentExists(camp, arr)) {
                arr[j].children.push({ name: arr2[i].name, checked: false });
              } else {
                arr.push({
                  parent: { name: camp, checked: false },
                  children: [{ name: ut, checked: false }],
                });
              }
              break;
            }
          }
        }
        break;
    }
    setMultiItems(arr);
    return arr;
  };

  const generateMultiItems3Layer = (
    rowid: number,
    type: string,
    myData: TcData
  ): MasterDetail3Layer[] => {
    let arr: MasterDetail3Layer[] = [
      { parent: { name: "ALL", checked: false }, children: [] },
    ];
    switch (type) {
      case "unitAccessBeds":
        let arrUnits: Unit[] = myData.unit as Unit[];
        let myUNits: Unit[] = [];
        let camp = "";
        let ut = "";

        let arr1: string[] = apiRef?.current?.getRow(rowid).unitAccess || [];

        for (let i = 0; i < arr1.length; i++) {
          let arrUnitName = arr1[i].split(":");
          if (arrUnitName.length === 1) {
            //local campus unit
            camp = "ALL";
          } else {
            camp = arrUnitName[0];
          }
        }

        for (let i = 0; i < arr1.length; i++) {
          let arrUnitName = arr1[i].split(":");
          if (arrUnitName.length === 1) {
            //local campus unit
            camp = "ALL";
            ut = arrUnitName[0];
            if (ut.toUpperCase() === "ALL") {
              for (let k = 0; k < arrUnits.length; k++) {
                let beds = arrUnits[k].bed || [];
                if (
                  arrUnits[k].name.split(":").length === 1 &&
                  beds.length > 0
                ) {
                  //do not show units with no beds defined.
                  myUNits.push(arrUnits[k]);
                }
              }
            } else {
              for (let k = 0; k < arrUnits.length; k++) {
                let beds = arrUnits[k].bed || [];
                if (
                  arrUnits[k].name.toUpperCase() === ut.toUpperCase() &&
                  beds.length > 0
                ) {
                  //do not show units with no beds defined.
                  myUNits.push(arrUnits[k]);
                }
              }
            }
          } else {
            camp = arrUnitName[0];
            ut = arrUnitName[1];
            if (ut.toUpperCase() === "ALL") {
              for (let k = 0; k < arrUnits.length; k++) {
                let beds = arrUnits[k].bed || [];
                if (
                  arrUnits[k].name.split(":").length === 2 &&
                  arrUnits[k].name.split(":")[0].toUpperCase() ===
                    camp.toUpperCase() &&
                  beds.length > 0
                ) {
                  //do not show units with no beds defined.
                  myUNits.push(arrUnits[k]);
                }
              }
            } else {
              for (let k = 0; k < arrUnits.length; k++) {
                let beds = arrUnits[k].bed || [];
                if (
                  arrUnits[k].name.toUpperCase() === arr1[i].toUpperCase() &&
                  beds.length > 0
                ) {
                  //do not show units with no beds defined.
                  myUNits.push(arrUnits[k]);
                }
              }
            }
          }
          for (let i = 0; i < myUNits.length; i++) {
            ut = myUNits[i].name;
            let myBeds = myUNits[i].bed || [];
            let myBedsArr: OptionItem[] = [];
            for (let i = 0; i < myBeds.length; i++) {
              myBedsArr.push({ name: myBeds[i].name, checked: false });
            }
            add2Items3Layer(camp, ut, myBedsArr, arr);
          }
          myUNits = [];
        }
        break;
    }
    setMultiItems3Layer(arr);
    return arr;
  };

  const checkedMultiItems = (
    multiSelected: string[],
    arr: MasterDetail[]
  ): MasterDetail[] => {
    let camp = "";
    let ut = "";
    if (multiSelected.length === 1 && multiSelected[0].length === 0) {
      setMultiItems(arr);
      return arr;
    }
    for (let i = 0; i < multiSelected.length; i++) {
      let ms = multiSelected[i].split(":");
      if (ms.length > 1) {
        camp = ms[0] + ":ALL";
        ut = ms[1];
      } else {
        camp = "ALL";
        ut = ms[0];
      }
      for (let j = 0; j < arr.length; j++) {
        if (camp.toUpperCase() === arr[j].parent.name.toUpperCase()) {
          for (let k = 0; k < arr[j].children.length; k++) {
            if (ut === "ALL") {
              arr[j].children[k].checked = true;
            } else {
              if (ut.toUpperCase() === arr[j].children[k].name.toUpperCase()) {
                arr[j].children[k].checked = true;
                break;
              }
            }
          }
        }
      }
    }
    for (let p = 0; p < arr.length; p++) {
      let allchecked = childrenAllChecked(arr[p].children);
      arr[p].parent.checked = allchecked;
    }
    setMultiItems(arr);
    return arr;
  };

  const createNodes = (
    items: MasterDetail[],
    opens: string[],
    checks: string[]
  ) => {
    let elements = [];
    for (let i = 0; i < items.length; i++) {
      let obj = items[i];
      let parent: IQuickTreeItemProps = {
        id: "Parent:" + obj.parent.name,
        level: 1,
        label: obj.parent.name,
        children: [],
      };
      for (let j = 0; j < obj.children.length; j++) {
        let child: IQuickTreeItemProps = {
          id: "Child:" + obj.children[j].name,
          level: 2,
          label: obj.children[j].name,
          parent: parent,
          children: [],
        };
        parent.children && parent.children.push(child);
      }
      elements.push(parent);
    }
    setNodes(elements);
  };

  const createNodes3Layer = (
    items: MasterDetail3Layer[],
    opens: string[],
    checks: string[]
  ) => {
    let elements = [];
    for (let i = 0; i < items.length; i++) {
      let obj = items[i];
      let parent: IQuickTreeItemProps = {
        id: "Parent:" + obj.parent.name,
        level: 1,
        label: obj.parent.name,
        children: [],
      };
      for (let j = 0; j < obj.children.length; j++) {
        let mid: IQuickTreeItemProps = {
          id: "Mid:" + obj.children[j].parent.name,
          level: 2,
          label: obj.children[j].parent.name,
          parent: parent,
          children: [],
        };
        parent.children && parent.children.push(mid);
        for (let k = 0; k < obj.children[j].children.length; k++) {
          let child: IQuickTreeItemProps = {
            id: "Child:" + obj.children[j].children[k].name,
            level: 3,
            label: obj.children[j].children[k].name,
            parent: mid,
            children: [],
          };
          mid.children && mid.children.push(child);
        }
      }
      elements.push(parent);
    }
    setNodes3Layer(elements);
  };

  const checkedMultiItems3Layer = (
    multiSelected: string[],
    arr: MasterDetail3Layer[]
  ): MasterDetail3Layer[] => {
    let camp = "";
    let bd = "";
    if (multiSelected.length === 1 && multiSelected[0].length === 0) {
      setMultiItems3Layer(arr);
      return arr;
    }
    for (let i = 0; i < multiSelected.length; i++) {
      let ms = multiSelected[i].split(":");
      if (ms.length > 1) {
        camp = ms[0] + ":ALL";
        bd = ms[1];
      } else {
        camp = "ALL";
        bd = ms[0];
      }
      for (let j = 0; j < arr.length; j++) {
        if (camp.toUpperCase() === arr[j].parent.name.toUpperCase()) {
          if (bd.toUpperCase() === "ALL") {
            for (let k = 0; k < arr[j].children.length; k++) {
              for (let m = 0; m < arr[j].children[k].children.length; m++) {
                arr[j].children[k].children[m].checked = true;
              }
            }
          } else {
            for (let k = 0; k < arr[j].children.length; k++) {
              for (let m = 0; m < arr[j].children[k].children.length; m++) {
                if (
                  arr[j].children[k].children[m].name.toUpperCase() ===
                  multiSelected[i].toUpperCase()
                ) {
                  arr[j].children[k].children[m].checked = true;
                }
              }
            }
          }
        }
      }
    }
    for (let p = 0; p < arr.length; p++) {
      arr[p].parent.checked = true;
      for (let m = 0; m < arr[p].children.length; m++) {
        arr[p].children[m].parent.checked = true;
        for (let n = 0; n < arr[p].children[m].children.length; n++) {
          if (arr[p].children[m].children[n].checked === false) {
            arr[p].children[m].parent.checked = false;
            arr[p].parent.checked = false;
            break;
          }
        }
      }
    }
    setMultiItems3Layer(arr);
    return arr;
  };

  const childrenAllChecked = (arr: OptionItem[]): boolean => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].checked === false) {
        return false;
      }
    }
    return true;
  };

  const parentExists = (name: string, arr: MasterDetail[]): boolean => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].parent.name === name) {
        return true;
      }
    }
    return false;
  };

  const add2Items3Layer = (
    camp: string,
    mid: string,
    child: OptionItem[],
    arr1: MasterDetail3Layer[]
  ) => {
    let find1 = false;
    for (let i = 0; i < arr1.length; i++) {
      if (camp.toUpperCase() === arr1[i].parent.name.toUpperCase()) {
        find1 = true;
        let find2 = false;
        for (let j = 0; j < arr1[i].children.length; j++) {
          if (
            arr1[i].children[j].parent.name.toUpperCase() === mid.toUpperCase()
          ) {
            find2 = true;
            arr1[i].children[j].children = child;
            break;
          }
        }
        if (!find2) {
          let midObj: MasterDetail = {
            parent: { name: mid, checked: false },
            children: child,
          };
          arr1[i].children.push(midObj);
        }
      }
    }
    if (!find1) {
      let paObj = {
        parent: { name: camp, checked: false },
        children: [{ parent: { name: mid, checked: false }, children: child }],
      };
      arr1.push(paObj);
    }
  };

  const addTerminal = (cell: myCell) => {
    setIsEditName(false);
    setOpenAddTerminal(true);
  };

  const confirmDelete = (cell: myCell) => {
    setOpen(true);
    setShowConfirm("deleteTerm");
  };

  const delTerminals = () => {
    setOpen(false);
    setShowConfirm("");
    let rows = selectedRows;
    let tmpData = cloneDeep(data) as TcData;
    let terminals: Terminal[] = tmpData?.terminal || [];
    let unselected = cloneDeep(unselectedTerminals);
    for (let i = 0; i < rows.length; i++) {
      let termName = rows[i].termName;
      for (let j = 0; j < terminals.length; j++) {
        if (termName.toUpperCase() === terminals[j].name.toUpperCase()) {
          unselected.push(terminals[j]);
          terminals.splice(j, 1);
          break;
        }
      }
    }
    for (let i = 0; i < terminals.length; i++) {
      terminals[i].id = i + 1;
    }
    setData(tmpData);
    setUnselectedTerminals(unselected);
    setLoading(false);
    setDirty(true);
    setSelectedRows([]);
    setSelectedGridRows([]);
  };

  const terminalCommitted = (name: string): boolean => {
    if (!isDirty) {
      return true;
    }
    let oldTerminals = oldData?.terminal || [];
    for (let i = 0; i < oldTerminals.length; i++) {
      if (name.toUpperCase() === oldTerminals[i].name.toUpperCase()) {
        return true;
      }
    }
    return false;
  };

  const handleCellFocus = React.useCallback(
    (event: any) => {
      const editName = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };
      const editPrinter = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };
      const editUnitAccess = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };
      const editBedAccesss = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };
      const editDefaultUnit = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };
      const editDefaultBed = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };
      const editScreenLock = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };

      const editSmartCard = (cell: myCell) => {
        handleCellClick(cell.rowId, cell.field, cell.value, cell.myData);
      };

      const row = event.currentTarget.parentElement;
      const id = parseInt(row.dataset.id);
      const field = event.currentTarget.dataset.field as string;
      const value = apiRef?.current.getCellValue(id, field);
      let isTermCommitted = terminalCommitted(apiRef?.current?.getRow(id).name);
      let arr: MenuItemHandler[] = [];
      setCurrentRow(id);
      setSelectedCell({
        rowId: id,
        field: field,
        value: value,
        myData: data,
        selectedRows: selectedRows,
      });
      let item: MenuItemHandler = {
        label: "",
        disabled: true,
        handler: editName,
      };
      switch (field) {
        case "name":
          item = {
            label: "Edit Name",
            disabled: isTermCommitted,
            handler: editName,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "printers":
          item = {
            label: "Edit Printer",
            disabled: !isEdit,
            handler: editPrinter,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "unitAccess":
          item = {
            label: "Edit Units",
            disabled: !isEdit,
            handler: editUnitAccess,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "bedAccess":
          item = {
            label: "Edit Beds",
            disabled: !isEdit,
            handler: editBedAccesss,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "defaultUnit":
          item = {
            label: "Edit Default Unit",
            disabled: !isEdit,
            handler: editDefaultUnit,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "defaultBed":
          item = {
            label: "Edit Default Bed",
            disabled: !isEdit,
            handler: editDefaultBed,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "screenLock":
          item = {
            label: "Edit Screen Lock",
            disabled: !isEdit,
            handler: editScreenLock,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
        case "smartCard":
          item = {
            label: "Edit Smart Card",
            disabled: !isEdit,
            handler: editSmartCard,
          };
          arr.push(item);
          item = {
            label: "Add Terminal",
            disabled: !isEdit,
            handler: addTerminal,
          };
          arr.push(item);
          item = {
            label: "Delete Terminal(s)",
            disabled: !isEdit,
            handler: confirmDelete,
          };
          arr.push(item);
          setContextMenuItems(arr);
          break;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data, unselectedTerminals, isEdit, terminalCommitted]
  );

  const handleContextMenuClick = (event: React.MouseEvent) => {
    event.preventDefault();

    if (currentRow > 0) {
      let exists: boolean = false;
      for (let i = 0; i < selectedGridRows.length; i++) {
        if (currentRow === parseInt(selectedGridRows[i])) {
          exists = true;
          break;
        }
      }
      if (!exists) {
        let tmpArr: termRow[] = [];
        tmpArr.push({
          rowId: currentRow,
          termName: data?.terminal[currentRow - 1].name as string,
        });
        setSelectedRows(tmpArr);
        setSelectedGridRows([currentRow]);
      }
    }

    setContextMenu(
      contextMenu === null
        ? { mouseX: event.clientX - 2, mouseY: event.clientY - 4 }
        : null
    );
  };

  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  const handleItemMenuAction = (handler: any) => {
    if (handler && selectedCell) {
      selectedCell.selectedRows = selectedRows;
      handler(selectedCell);
    }
    handleContextMenuClose();
  };

  const getOptionSelectedIndex = (arr: string[], val: any): number => {
    if (val !== null && val !== undefined) {
      for (let i = 0; i < arr.length; ++i) {
        let a1 = arr[i] as string;
        let a2 = val as string;
        if (a1.toUpperCase() === a2.toUpperCase()) {
          return i;
        }
      }
    }
    return -1;
  };

  function handleRowSelectionChange(
    rowSelectionModel: GridRowSelectionModel,
    details: GridCallbackDetails
  ) {
    let tmpArr: termRow[] = [];
    for (let i = 0; i < rowSelectionModel.length; i++) {
      if (!apiRef?.current?.getRow(rowSelectionModel[i])) {
        continue;
      } else {
        let termName = apiRef?.current?.getRow(rowSelectionModel[i]).name;
        tmpArr.push({
          rowId: rowSelectionModel[i] as number,
          termName: termName,
        });
      }
    }
    setSelectedRows(tmpArr);
    setSelectedGridRows(rowSelectionModel);
  }

  const handleClose = () => {
    setOpen(false);
    setShowInfoDialog("");
  };
  const show = (show: boolean) => {};
  const handleCancelConfirm = () => {
    setShowConfirm("");
    setOpen(false);
  };

  const closeLog = () => {
    setShowLog(false);
  };

  return (
    <Box
      sx={{
        position: "relative",
        top: "5px",
        height: `-webkit-calc(100% - 75px)`,
        width: "100%",
      }}
    >
      <div
        style={{
          position: "absolute",
          top: "57px",
          right: "8px",
          backgroundColor: "#FFFFE1",
          border: "1px solid",
          display: searchText ? "block" : "none",
          zIndex: 9999,
        }}
      >
        <p>{searchText}</p>
      </div>

      <SingleSelectDialog
        open={openSingleSelect}
        onClose={handleSingleSelectClose}
        title={title}
        subtitle={subtitle}
        items={items}
      />
      <MultiSelectDialog
        open={openMultiSelect}
        onClose={handleMultiSelectClose}
        title={title}
        subtitle={subtitle}
        selected={checked}
        propOpened={propOpened}
        nodes={nodes}
      />
      <MultiSelectDialog3Layers
        open={openMultiSelect3Layer}
        onClose={handleMultiSelect3LayerClose}
        title={title}
        subtitle={subtitle}
        selected={checked}
        propOpened={propOpened}
        nodes={nodes3Layer}
      />
      <AddTerminalDialog
        open={openAddTerminal}
        onClose={handleCloseAddTerminal}
        editName={isEditName}
        selectedGridRows={selectedGridRows}
      />
      {showInfoDialog === "clickCell" && (
        <TerminalConfigInfoDialog
          open={open}
          text={content}
          onOK={handleClose}
          title={title}
        />
      )}
      {showConfirm === "deleteTerm" && (
        <TerminalConfigConfirm
          open={open}
          show={show}
          text="Are you sure you want to delete the terminal(s)?"
          onYes={delTerminals}
          onNo={handleCancelConfirm}
          title="Delete Terminals"
        />
      )}
      <ShowLogDialog title="Import Log" open={showLog} onClose={closeLog} />

      {selectedTabIndex === pageIndices.TERMINALCONFIG && (
        <DataGridPro
          apiRef={apiRef}
          sx={datagridSx}
          columns={columns}
          rows={data?.terminal || []}
          onRowSelectionModelChange={handleRowSelectionChange}
          rowSelectionModel={selectedGridRows}
          onCellDoubleClick={onCellDoubleClick}
          showCellVerticalBorder={true}
          showColumnVerticalBorder={true}
          sortingOrder={["asc", "desc"]}
          hideFooter
          getRowClassName={(params) =>
            params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even"
          }
          slotProps={{
            cell: {
              onFocus: handleCellFocus,
              onContextMenu: handleContextMenuClick,
            },
          }}
        />
      )}

      {contextMenuItems ? (
        <Menu
          open={contextMenu !== null}
          onClose={handleContextMenuClose}
          anchorReference="anchorPosition"
          anchorPosition={
            contextMenu !== null
              ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
              : undefined
          }
          slotProps={{
            root: {
              onContextMenu: (e: any) => {
                e.preventDefault();
                handleContextMenuClose();
              },
            },
          }}
        >
          {contextMenuItems.map((item, idx) => {
            return (
              <MenuItem
                key={idx}
                disabled={item.disabled || false}
                onClick={() => handleItemMenuAction(item.handler)}
              >
                {item.label}
              </MenuItem>
            );
          })}
        </Menu>
      ) : null}
    </Box>
  );
}
