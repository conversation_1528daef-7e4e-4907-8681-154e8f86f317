import { AppBar, Too<PERSON><PERSON> } from "@mui/material";

import RefreshBtn from "../buttons/RefreshBtn";
import RevertBtn from "../buttons/RevertBtn";
import AddTermBtn from "../buttons/AddTermBtn";
import DelTermBtn from "../buttons/DelTermBtn";
import ImportBtn from "../buttons/ImportBtn";
import ExportBtn from "../buttons/ExportBtn";
import ShowLogBtn from "../buttons/ShowLogBtn";
import EditBtn from "../buttons/EditBtn";
import ReviewBtn from "../buttons/ReviewBtn";
import StealLockBtn from "../buttons/StealLockBtn";
import SaveCommitBtn from "../buttons/SaveCommitBtn";

export default function TerminalConfigAppBar() {
  return (
    <AppBar position="static" sx={{ backgroundColor: "white" }}>
      <Toolbar>
        <RefreshBtn />
        <SaveCommitBtn />
        <EditBtn />
        <ReviewBtn />
        <StealLockBtn />
        <RevertBtn />
        <AddTermBtn />
        <DelTermBtn />
        <ImportBtn />
        <ExportBtn />
        <ShowLogBtn />
      </Toolbar>
    </AppBar>
  );
}
