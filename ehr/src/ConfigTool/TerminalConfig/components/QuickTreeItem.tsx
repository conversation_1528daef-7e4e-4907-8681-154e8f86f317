import * as React from "react";
import { useAtom } from "jotai/react";
import QuickTreeView from "./QuickTreeView";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import {
  quickTreeViewSelectedAtom,
  quickTreeViewExpandedAtom,
} from "../context/TerminalConfigAtoms";

function QuickTreeItem(para: any) {
  interface IQuickTreeItemProps {
    id: string;
    level: number;
    label: string;
    parent?: IQuickTreeItemProps;
    children?: IQuickTreeItemProps[];
  }

  const [quickTreeViewSelected, setQuickTreeViewSelected] = useAtom(
    quickTreeViewSelectedAtom
  );
  const [quickTreeViewExpanded, setQuickTreeViewExpanded] = useAtom(
    quickTreeViewExpandedAtom
  );
  const node: IQuickTreeItemProps = para.node;

  const showChildren = () => {
    for (let i = 0; i < quickTreeViewExpanded.length; i++) {
      if (quickTreeViewExpanded[i].toUpperCase() === node.id.toUpperCase()) {
        return true;
      }
    }
    return false;
  };
  const expandCollapse = () => {
    let newExpanded: string[] = [...quickTreeViewExpanded];
    let ind = newExpanded.indexOf(node.id);
    if (ind >= 0) {
      newExpanded.splice(ind, 1);
      setQuickTreeViewExpanded(newExpanded);
      return;
    }
    newExpanded.push(node.id);
    setQuickTreeViewExpanded(newExpanded);
  };
  const isChecked = () => {
    for (let i = 0; i < quickTreeViewSelected.length; i++) {
      if (quickTreeViewSelected[i].toUpperCase() === node.id.toUpperCase()) {
        return true;
      }
    }
    return false;
  };

  const checkItem = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newSelected: string[] = [...quickTreeViewSelected];
    let doCheck: boolean = true;
    let ind = newSelected.indexOf(node.id);
    if (ind >= 0) {
      //action: uncheck a node
      doCheck = false;
    }
    handleToggle(node.id, doCheck, newSelected);
  };

  const handleToggle = (
    nodeId: string,
    doCheck: boolean,
    newSelected: string[]
  ) => {
    let allData: IQuickTreeItemProps[] = para.allTreeData;
    let myself = getNodeById(nodeId, allData);
    if (myself) {
      if (!doCheck) {
        //uncheck a node, uncheck all its ancestors and its descendants, and keep its siblings unchanged
        let ind = newSelected.indexOf(myself.id);
        if (ind >= 0) {
          newSelected.splice(ind, 1);
        }
        if (myself.parent) {
          newSelected = uncheckParent(myself.parent, newSelected);
        }
      } else {
        //check a node, check all its descendants, keep its siblings unchanged. Check its ancestors based on its siblings
        let ind = newSelected.indexOf(myself.id);
        if (ind < 0) {
          newSelected.push(nodeId);
        }
        let newExpanded = [...quickTreeViewExpanded];
        ind = quickTreeViewExpanded.indexOf(myself.id);
        if (ind < 0) {
          newExpanded.push(myself.id);
          setQuickTreeViewExpanded(newExpanded);
        }
        if (myself.parent) {
          newSelected = checkParentBySiblings(myself.parent, newSelected);
        }
      }
      let children = myself.children || [];
      newSelected = toggleChildren(children, doCheck, newSelected);
      setQuickTreeViewSelected(newSelected);
    }
  };

  const uncheckParent = (
    parentNode: IQuickTreeItemProps,
    newSelected: string[]
  ): string[] => {
    let ind = newSelected.indexOf(parentNode.id);
    if (ind >= 0) {
      newSelected.splice(ind, 1);
    }
    if (parentNode.parent) {
      let nextNode: IQuickTreeItemProps = parentNode.parent;
      newSelected = uncheckParent(nextNode, newSelected);
    }
    return newSelected;
  };

  const checkParentBySiblings = (
    parentNode: IQuickTreeItemProps,
    newSelected: string[]
  ): string[] => {
    let children = parentNode.children || [];
    let checkMe = true;
    for (let i = 0; i < children.length; i++) {
      let ind = newSelected.indexOf(children[i].id);
      if (ind < 0) {
        checkMe = false;
        break;
      }
    }
    if (checkMe) {
      let ind = newSelected.indexOf(parentNode.id);
      if (ind < 0) {
        newSelected.push(parentNode.id);
      }
      if (parentNode.parent) {
        newSelected = checkParentBySiblings(parentNode.parent, newSelected);
      }
    }
    return newSelected;
  };

  const toggleChildren = (
    children: IQuickTreeItemProps[],
    doCheck: boolean,
    newSelected: string[]
  ): string[] => {
    for (let i = 0; i < children.length; i++) {
      let ind = newSelected.indexOf(children[i].id);
      if (ind >= 0 && !doCheck) {
        newSelected.splice(ind, 1);
      }
      if (ind < 0 && doCheck) {
        newSelected.push(children[i].id);
      }

      let nextChildren: IQuickTreeItemProps[] = children[i].children || [];
      if (nextChildren.length > 0) {
        newSelected = toggleChildren(nextChildren, doCheck, newSelected);
      }
    }
    return newSelected;
  };

  const getNodeById = (
    nodeId: string,
    allData: IQuickTreeItemProps[]
  ): IQuickTreeItemProps | null => {
    let ret: any;
    for (let i = 0; i < allData.length; i++) {
      ret = getNodeById_Reg(nodeId, allData[i]);
      if (ret) {
        return ret;
      }
    }
    return ret;
  };

  const getNodeById_Reg = (
    nodeId: string,
    node2: IQuickTreeItemProps
  ): IQuickTreeItemProps | null => {
    if (nodeId === node2.id) {
      return node2;
    }
    let children = node2.children || [];
    for (let i = 0; i < children.length; i++) {
      let found = getNodeById_Reg(nodeId, children[i]);
      if (found) {
        return found;
      }
    }
    return null;
  };

  const ImgButton = ({ variant, ...rest }: any) => {
    switch (variant) {
      case "expanded":
        return <ChevronRightIcon />;
      case "collasped":
        return <ExpandMoreIcon />;
      default:
        return null;
    }
  };

  return (
    <>
      <li
        id={"li_" + node.id}
        key={"li" + node.id}
        style={{ listStyle: "none" }}
      >
        <div
          id={"div1_" + node.id}
          key={"div1" + node.id}
          style={{ display: "flex", alignItems: "center" }}
        >
          {node.children && node.children.length > 0 && (
            <div
              id={"divImg_" + node.id}
              key={"divImg" + node.id}
              onClick={(e: any) => {
                expandCollapse();
                e.stopPropagation();
              }}
            >
              <ImgButton
                id={"img_" + node.id}
                key={"img" + node.id}
                variant={showChildren() ? "expanded" : "collasped"}
              />
            </div>
          )}

          <input
            id={"chk_" + node.id}
            key={"chk" + node.id}
            type="checkbox"
            style={{ height: "42px", textAlign: "center" }}
            checked={isChecked()}
            onChange={(e: any) => {
              checkItem(e);
              e.stopPropagation();
            }}
          />
          <span
            id={"span_label_" + node.id}
            key={"spanlabel" + node.id}
            style={{ fontSize: "16px" }}
          >
            {node.label}
          </span>
        </div>
      </li>

      <ul
        id={"showChildren_" + node.id}
        key={"showChildren_" + node.id}
        style={{
          paddingLeft: "10px",
          display: showChildren() ? "Block" : "none",
        }}
      >
        {
          <QuickTreeView
            treeData={node.children}
            allTreeData={para.allTreeData}
            id={"QuickTreeItem_" + node.id}
          />
        }
      </ul>
    </>
  );
}
export default QuickTreeItem;
