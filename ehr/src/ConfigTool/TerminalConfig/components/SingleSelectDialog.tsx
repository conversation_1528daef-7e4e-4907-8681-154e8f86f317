import * as React from "react";
import { Box } from "@mui/material";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import TerminalConfigButton from "./buttons/TerminalConfigButton";
import { useAtom } from "jotai/react";
import { ScopeTabDialog } from "@cci-monorepo/common";
import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";
import { dirtyAtom, retValueAtom } from "../context/TerminalConfigAtoms";

const SingleSelectDialogStyle = {
  "& .MuiDialog-paper": {
    width: 500,
    //maxWidth: 900,
    minHeight: 700,
    maxHeight: 900,
    backgroundColor: "rgb(227, 234, 245)",
    overflowY: "visible",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

export const SingleSelectDialog = (props: any) => {
  const title = props.title;
  const subtitle = props.subtitle;
  const onClose = props.onClose;
  const open = props.open;
  const items = props.items;
  const [selectedIndex, setSelectedIndex] = React.useState(-1);
  const [retValue, setRetValue] = useAtom(retValueAtom);
  const [, setDirty] = useAtom(dirtyAtom);

  const handleListItemClick = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    index: number
  ) => {
    setSelectedIndex(index);
  };

  React.useEffect(() => {
    setSelectedIndex(retValue.ind);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    setSelectedIndex(retValue.ind);
  }, [retValue]);

  const handleOK = () => {
    setRetValue({
      rowid: retValue.rowid,
      col: retValue.col,
      ind: selectedIndex,
    });
    setDirty(true);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Box>
      <ScopeTabDialog
        open={open}
        sx={SingleSelectDialogStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        onClose={handleCancel}
      >
        <DialogTitle
          style={{
            cursor: "move",
            height: "50px",
            color: "white",
            backgroundColor: "black",
            display: "flex",
            alignItems: "center",
          }}
        >
          {title}
          <Button
            size="small"
            variant="outlined"
            onClick={handleCancel}
            sx={{
              fontWeight: "bold",
              fontSize: "14px",
              marginLeft: "310px",
              backgroundColor: "white",
              color: "black",
              textTransform: "none",
            }}
          >
            Close
          </Button>
        </DialogTitle>
        <DialogContent>
          <Box
            component="div"
            sx={{
              height: "700px",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "white",
              overflowY: "auto",
              padding: "15px",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            {subtitle}

            <Box
              component="div"
              sx={{
                height: "650px",
                marginTop: "20px",
                display: "flex",
                border: 1,
                flexDirection: "column",
                backgroundColor: "white",
                overflowY: "auto",
                padding: "15px",
                fontSize: "16px",
                fontWeight: "light",
              }}
            >
              <List component="nav">
                {items.map((p: string, index: number) => (
                  <ListItemButton
                    key={index}
                    selected={selectedIndex === index}
                    onClick={(event) => handleListItemClick(event, index)}
                  >
                    <ListItemText primary={p} />
                  </ListItemButton>
                ))}
              </List>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text="Cancel" handleClick={handleCancel} />
          <TerminalConfigButton text="OK" handleClick={handleOK} />
        </DialogActions>
      </ScopeTabDialog>
    </Box>
  );
};
