import React from "react";

import { TextField } from "@mui/material";

interface IProps<T> {
  label?: string;
  dataType: string;
  style: React.CSSProperties;
  data: T;
  disabled?: boolean;
  hideError?: boolean;
  onDataChange: (data: T) => void; // Called on every single input char
}

function TerminalConfigTextFieldInput<T>(props: IProps<T>) {
  return (
    <TextField
      autoFocus
      disabled={props.disabled}
      label={props.label}
      style={props.style || {}}
      InputProps={{
        inputProps: {
          style: props.style,
        },
      }}
      size="small"
      variant="outlined"
      value={props.data}
      type={props.dataType}
      onChange={(event) =>
        props.onDataChange(event.target.value as unknown as T)
      }
      error={props.hideError ? false : !props.data}
    />
  );
}

export default TerminalConfigTextFieldInput;
