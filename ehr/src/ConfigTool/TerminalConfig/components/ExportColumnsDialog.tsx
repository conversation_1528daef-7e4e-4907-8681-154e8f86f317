import * as React from "react";
import { Box } from "@mui/material";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import Checkbox from "@mui/material/Checkbox";
import { ScopeTabDialog } from "@cci-monorepo/common";
import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";
import TerminalConfigButton from "./buttons/TerminalConfigButton";
import { useAtom } from "jotai/react";
import {
  exportedColumnsAtom,
  exportDataAtom,
  exportSelectedDataAtom,
  selectedRowsAtom,
} from "../context/TerminalConfigAtoms";
import { termExportColumn } from "../type";
import FormControlLabel from "@mui/material/FormControlLabel";

const ExportColumnsDialogStyle = {
  "& .MuiDialog-paper": {
    width: 600,
    minHeight: 550,
    maxHeight: 900,
    backgroundColor: "rgb(227, 234, 245)",
    overflowY: "visible",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

export const ExportColumnsDialog = (props: any) => {
  const title = props.title;
  const subtitle = props.subtitle;
  const onClose = props.onClose;
  const open = props.open;
  const [selectedRows] = useAtom(selectedRowsAtom);
  const [exportedColumns, setExportedColumns] = useAtom(exportedColumnsAtom);
  const [, setExportData] = useAtom(exportDataAtom);
  const [exportSelectedData, setExportSelectedData] = useAtom(
    exportSelectedDataAtom
  );

  const [selected, setSelected] =
    React.useState<termExportColumn[]>(exportedColumns);

  React.useEffect(() => {
    setSelected(exportedColumns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOK = () => {
    setExportedColumns(selected);
    setExportData(true);
    onClose();
  };

  const handleCancel = () => {
    setExportData(false);
    onClose();
  };

  const handleToggle = (node: termExportColumn) => () => {
    let newSelected = [...selected];
    newSelected.forEach((md) => {
      if (md.field === node.field) {
        md.selected = !node.selected;
      }
    });
    setSelected(newSelected);
  };

  const uncheckAll = () => {
    let newSelected = [...selected];
    newSelected.forEach((md) => {
      if (!md.required) {
        md.selected = false;
      }
    });
    setSelected(newSelected);
  };

  const checkAll = () => {
    let newSelected = [...selected];
    selected.forEach((md) => {
      md.selected = true;
    });
    setSelected(newSelected);
  };

  return (
    <Box>
      <ScopeTabDialog
        open={open}
        sx={ExportColumnsDialogStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        onClose={handleCancel}
      >
        <DialogTitle
          style={{
            cursor: "move",
            height: "50px",
            color: "white",
            backgroundColor: "black",
            display: "flex",
            alignItems: "center",
          }}
        >
          {title}
          <Button
            size="small"
            variant="outlined"
            onClick={handleCancel}
            sx={{
              fontWeight: "bold",
              fontSize: "14px",
              marginLeft: "350px",
              backgroundColor: "white",
              color: "black",
              textTransform: "none",
            }}
          >
            Close
          </Button>
        </DialogTitle>
        <DialogContent>
          <Box
            component="div"
            sx={{
              height: "550px",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "white",
              padding: "15px",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            {subtitle}
            <FormControlLabel
              sx={{
                fontWeight: "light",
              }}
              label="Export only the selected terminals"
              control={
                <Checkbox
                  checked={exportSelectedData && selectedRows.length > 0}
                  disabled={selectedRows.length === 0}
                  onChange={(event) => {
                    setExportSelectedData(!exportSelectedData);
                  }}
                />
              }
            />
            <Box
              sx={{
                height: "450px",
                marginTop: "20px",
                display: "flex",
                border: 1,
                flexDirection: "row",
                backgroundColor: "white",
                overflowY: "auto",
                padding: "15px",
              }}
            >
              <Box
                sx={{
                  overflowY: "auto",
                  display: "flex",
                  alignItems: "left",
                  width: "400px",
                  heigth: "600px",
                  fontSize: "16px",
                  fontWeight: "light",
                }}
              >
                <List component="nav">
                  {selected.map((md: termExportColumn, index: number) => {
                    return (
                      <ListItemButton
                        key={"exp" + index}
                        alignItems="flex-start"
                        sx={{
                          px: 0,
                          pt: 0,
                          pb: 0,
                          mt: 0,
                          mb: 0,
                        }}
                      >
                        <FormControlLabel
                          key={"expCheck" + index}
                          label={md.title}
                          control={
                            <Checkbox
                              checked={md.selected || md.required}
                              disabled={md.required}
                              onChange={handleToggle(md)}
                            />
                          }
                        />
                      </ListItemButton>
                    );
                  })}
                </List>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  border: 0,
                  padding: "2px",
                  flexDirection: "column",
                  alignItems: "center",
                  width: "cal(100% - 400px)",
                  heigth: "100%",
                }}
              >
                <Button sx={{ mb: "2px", width: "100px" }} onClick={uncheckAll}>
                  Uncheck All
                </Button>
                <Button sx={{ mb: "50px", width: "100px" }} onClick={checkAll}>
                  CheckAll
                </Button>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text="Cancel" handleClick={handleCancel} />
          <TerminalConfigButton text="OK" handleClick={handleOK} />
        </DialogActions>
      </ScopeTabDialog>
    </Box>
  );
};
