import React from "react";
import Box from "@mui/material/Box";
import { ScopeTabDialog } from "@cci-monorepo/common";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TerminalConfigButton from "../buttons/TerminalConfigButton";
import TerminalConfigCloseIconBtn from "../buttons/TerminalConfigCloseIconBtn";

import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";

const TerminalConfigConfirmStyle = {
  "& .MuiDialog-paper": {
    width: 400,
    //maxWidth: 900,
    minHeight: 200,
    maxHeight: 600,
    backgroundColor: "rgb(227, 234, 245)",
    overflowY: "visible",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}
export interface IPopupProps {
  title?: string;
  text?: string;
  open: boolean;
  onYes: () => void;
  onNo: () => void;
  onCancel?: () => void;
  yesText?: string;
  noText?: string;
  show: (status: boolean) => void;
}

const TerminalConfigConfirm: React.FunctionComponent<IPopupProps> = (
  props
): JSX.Element => {
  const {
    title,
    text,
    open,
    onYes,
    onNo,
    onCancel,
    yesText,
    noText,
    show,
    ...other
  } = props;

  const onClose = onCancel ? onCancel : onNo;
  let noCancel = !onCancel;

  return (
    <>
      <ScopeTabDialog
        open={open}
        onClose={onCancel}
        sx={TerminalConfigConfirmStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        {...other}
        onKeyUp={(e) => {
          e.preventDefault();
          if (e.key === "Escape") {
            onClose();
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <Box flexGrow={1}>{title}</Box>
            <Box>
              <TerminalConfigCloseIconBtn handleClick={onClose} />
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>{text}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text={yesText || "Yes"} handleClick={onYes} />
          <TerminalConfigButton text={noText || "No"} handleClick={onNo} />
          {!noCancel ? (
            <TerminalConfigButton text="Cancel" handleClick={onClose} />
          ) : null}
        </DialogActions>
      </ScopeTabDialog>
    </>
  );
};

export default TerminalConfigConfirm;
