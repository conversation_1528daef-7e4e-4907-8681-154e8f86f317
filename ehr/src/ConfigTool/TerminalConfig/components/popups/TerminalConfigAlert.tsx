import * as React from "react";

import Alert, { AlertColor } from "@mui/material/Alert";
import { useAtom } from "jotai/react";
import { showAlertAtom } from "../../context/TerminalConfigAtoms";

export default function TerminalConfigAlert(props: any) {
  const content = props.content;
  const severity = props.severity;

  const [showAlert, setShowAlert] = useAtom(showAlertAtom);

  const successAlertStyle = {
    height: "40px",
    whiteSpace: "nowrap",
    marginLeft: "calc(50% - 400px)",
    position: "absolute",
    border: "1px solid rgb(207 198 198)",
    borderColor: "#A8D7A9",
    borderRadius: "4px",
    backgroundColor: "#E9F5E9",
    alignItems: "center",
    left: "350px",
    bottom: "10px",
    "& .MuiAlert-message": {
      height: "40px",
      fontSize: "13px",
      fontWeight: "bold",
      display: "flex",
      alignItems: "center",
    },
  };

  React.useEffect(() => {
    if (showAlert) {
      setTimeout(() => setShowAlert(""), 3000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showAlert]);

  return (
    <Alert
      variant="outlined"
      severity={severity as AlertColor}
      sx={successAlertStyle}
      onClose={() => setShowAlert("")}
    >
      {content}
    </Alert>
  );
}
