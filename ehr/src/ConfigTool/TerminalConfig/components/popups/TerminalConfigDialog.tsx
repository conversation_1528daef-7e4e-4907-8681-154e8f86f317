import React, { FormEvent } from "react";

import Box from "@mui/material/Box";
import { ScopeTabDialog } from "@cci-monorepo/common";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TerminalConfigButton from "../buttons/TerminalConfigButton";
import TerminalConfigCloseIconBtn from "../buttons/TerminalConfigCloseIconBtn";
import TerminalConfigTextFieldInput from "../inputs/TerminalConfigTextFieldInput";

export interface IPopupProps {
  title: string;
  text?: string;
  open: boolean;
  onOK: (value: string) => void;
  onCancel?: () => void;
  show: (status: boolean) => void;
}

const TerminalConfigDialog: React.FunctionComponent<IPopupProps> = (
  props
): JSX.Element => {
  const { title, text, open, onOK, onCancel, show, ...other } = props;

  const [inputName, setInputName] = React.useState<string>("");

  const handleClose = () => {
    setInputName("");
    show(false);
  };

  const onClose = onCancel ? onCancel : handleClose;

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onOK(inputName);
    handleClose();
  };

  return (
    <>
      <ScopeTabDialog
        open={open}
        onClose={onCancel}
        {...other}
        onKeyUp={(e) => {
          e.preventDefault();
          if (e.key === "Escape") {
            onClose();
          }
        }}
      >
        <form onSubmit={handleSubmit}>
          <DialogTitle>
            <Box display="flex" alignItems="center">
              <Box flexGrow={1}>{title}</Box>
              <Box>
                <TerminalConfigCloseIconBtn handleClick={onClose} />
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent>
            <DialogContentText>{text}</DialogContentText>
            <TerminalConfigTextFieldInput<string>
              style={{ width: "100%", textAlign: "left" }}
              dataType={"text"}
              data={inputName}
              onDataChange={setInputName}
              hideError={true}
            />
          </DialogContent>
          <DialogActions>
            <TerminalConfigButton disabled={false} text="OK" type="submit" />
            <TerminalConfigButton text="Cancel" handleClick={onClose} />
            <input type="submit" hidden />
          </DialogActions>
        </form>
      </ScopeTabDialog>
    </>
  );
};

export default TerminalConfigDialog;
