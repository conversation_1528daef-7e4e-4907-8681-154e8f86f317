import React from "react";
import Box from "@mui/material/Box";
import { ScopeTabDialog } from "@cci-monorepo/common";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TerminalConfigButton from "../buttons/TerminalConfigButton";
import TerminalConfigCloseIconBtn from "../buttons/TerminalConfigCloseIconBtn";

import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";

const TerminalConfigInfoStyle = {
  "& .MuiDialog-paper": {
    width: 400,
    //maxWidth: 900,
    minHeight: 200,
    maxHeight: 400,
    backgroundColor: "rgb(227, 234, 245)",
    overflowY: "visible",
  },
  "& .MuiDialog-paper .MuiDialogContent-root": {
    overflowY: "visible",
  },
};

function PaperComponent(props: PaperProps) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}
export interface IPopupProps {
  title?: string;
  text?: string;
  open: boolean;
  onOK: () => void;
}

const TerminalConfigInfoDialog: React.FunctionComponent<IPopupProps> = (
  props
): JSX.Element => {
  const { title, text, open, onOK, ...other } = props;

  return (
    <>
      <ScopeTabDialog
        open={open}
        onClose={onOK}
        sx={TerminalConfigInfoStyle}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        disableEscapeKeyDown={true}
        {...other}
        onKeyUp={(e) => {
          e.preventDefault();
          if (e.key === "Escape") {
            onOK();
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <Box flexGrow={1}>{title}</Box>
            <Box>
              <TerminalConfigCloseIconBtn handleClick={onOK} />
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>{text}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <TerminalConfigButton text="OK" handleClick={onOK} />
        </DialogActions>
      </ScopeTabDialog>
    </>
  );
};

export default TerminalConfigInfoDialog;
