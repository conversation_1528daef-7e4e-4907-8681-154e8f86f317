import React from "react";
import { CciTabPanel } from "@cci/mui-components";
import { use<PERSON>tom, useSet<PERSON>tom, useAtomValue } from "jotai";
import { ReactNode, SyntheticEvent, useEffect, useState } from "react";
import {
  selectedTabIndexAtom,
  pageIndicesAtom,
  setPageIndicesAtom,
} from "./Cdsrule/context/CtaAtoms";

import { Box, Tabs } from "@mui/material";
import { PAGE_LABELS } from "./Cdsrule/util/CtaUtil";
import NotesEditor from "./NotesEditor";
import Cta from "./Cdsrule";
import Nme from "./NotesMenuEditor";
import TerminalConfig from "./TerminalConfig";
import SummaryConfTool from "./SummaryConfTool";
import {
  notesEditorRefreshAtom,
  cdsRuleRefreshAtom,
  notesMenuEditorRefreshAtom,
  summaryConfigToolRefreshAtom,
  terminalRefreshAtom,
  terminalReOrderAtom,
  getModuleDataStatusByRegex,
} from "./util/ConfigToolUtil";
import { ThemeProvider } from "@mui/material/styles";
import EhrTheme from "../theme/theme";
import TerminalConfigConfirm from "./TerminalConfig/components/popups/TerminalConfigConfirm";
import ConfigToolTab from "./ConfigToolTab";
import type { ScreenComponentProps } from "@cci-monorepo/common/utils/screenUtil";
/**
 * TabProps
 *
 * @private
 *
 * @description Helper function to generate props including id and aria-controls based on index
 * @param index - number
 * @returns object that contains id, aria-control, and isSubTab true for props
 */
function TabProps(index: number) {
  return {
    value: index,
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}
/**
 * @param props - When starting in dev mode, the props is undifiend, you need to set the tabs yourself.
 * - Use the tabs you defined instead of props.tabs, For example：
 * - if(Config.inDevMode)
 * - yourTabs = ["NotesEditor", "CdsRule", "NotesMenuEditor", "TerminalConfig", "SummaryConfTool"]
 * - ...
 */
export default function ConfigTool(props: ScreenComponentProps) {
  const { tabs } = (props.screenParams as unknown as { tabs: any[] }) || {
    tabs: [],
  };
  const [selectedTabIndex, setSelectedTabIndex] = useAtom(selectedTabIndexAtom);
  const pageIndices = useAtomValue(pageIndicesAtom);
  const setPageIndices = useSetAtom(setPageIndicesAtom);
  const [tabList, setTabList] = useState<string[]>([]);
  const setNotesEditorRefresh = useSetAtom(notesEditorRefreshAtom);
  const setCdsRuleRefresh = useSetAtom(cdsRuleRefreshAtom);
  const setNotesMenuEditorRefresh = useSetAtom(notesMenuEditorRefreshAtom);
  const setTerminalRefresh = useSetAtom(terminalRefreshAtom);
  const setTerminalReOrder = useSetAtom(terminalReOrderAtom);
  const setSummaryConfigToolRefresh = useSetAtom(summaryConfigToolRefreshAtom);
  const [dirtyOpen, setDirtyOpen] = React.useState(false);
  const [newIndex, setNewIndex] = React.useState(0);

  useEffect(() => {
    if (tabs && tabs.length > 0) {
      let tabNames: string[] = [];
      let pageIndices: Record<string, number> = {};
      tabs.forEach((tab: any, index: number) => {
        let tabName: string = tab.toUpperCase();
        tabNames.push(tabName);
        switch (tabName) {
          case PAGE_LABELS.NOTESEDITOR:
            pageIndices = { ...pageIndices, NOTESEDITOR: index };
            break;
          case PAGE_LABELS.CDSRULE:
            pageIndices = { ...pageIndices, CDSRULE: index };
            break;
          case PAGE_LABELS.NOTESMENUEDITOR:
            pageIndices = { ...pageIndices, NOTESMENUEDITOR: index };
            break;
          case PAGE_LABELS.TERMINALCONFIG:
            pageIndices = { ...pageIndices, TERMINALCONFIG: index };
            break;
          case PAGE_LABELS.SUMMARYCONFTOOL:
            pageIndices = { ...pageIndices, SUMMARYCONFTOOL: index };
            break;
        }
      });
      setTabList(tabNames);
      setPageIndices(pageIndices);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // handle tab change
  const handleChange = (event: SyntheticEvent, nIndex: number) => {
    const name = tabList[selectedTabIndex];
    const isDirty = getModuleDataStatusByRegex(name)?.dirty;
    setNewIndex(nIndex);
    if (isDirty) {
      setDirtyOpen(true);
    } else {
      setDirtyOpen(false);
      setSelectedTabIndex(nIndex);
    }
    const cModule = Object.keys(pageIndices)[nIndex];
    switch (cModule) {
      case PAGE_LABELS.TERMINALCONFIG:
        setTerminalReOrder(true);
        break;
      default:
        break;
    }
  };

  // Render the sub tab component
  const renderTabPanel = (index: number, Component: ReactNode): JSX.Element => {
    return (
      <CciTabPanel
        key={index}
        value={selectedTabIndex}
        index={index}
        reserved={true}
        optStyle={{ padding: "0px 5px 10px 5px" }}
      >
        {Component}
      </CciTabPanel>
    );
  };

  const getComponent = (tabName: string) => {
    switch (tabName) {
      case PAGE_LABELS.NOTESEDITOR:
        return <NotesEditor />;
      case PAGE_LABELS.CDSRULE:
        return <Cta />;
      case PAGE_LABELS.NOTESMENUEDITOR:
        return <Nme />;
      case PAGE_LABELS.TERMINALCONFIG:
        return <TerminalConfig />;
      case PAGE_LABELS.SUMMARYCONFTOOL:
        return <SummaryConfTool />;
      default:
        return <></>;
    }
  };

  function handleDiscard() {
    try {
      const cModule = Object.keys(pageIndices)[selectedTabIndex];
      switch (cModule) {
        case PAGE_LABELS.NOTESEDITOR:
          setNotesEditorRefresh(true);
          break;
        case PAGE_LABELS.CDSRULE:
          setCdsRuleRefresh(true);
          break;
        case PAGE_LABELS.NOTESMENUEDITOR:
          setNotesMenuEditorRefresh(true);
          break;
        case PAGE_LABELS.TERMINALCONFIG:
          setTerminalRefresh(true);
          break;
        case PAGE_LABELS.SUMMARYCONFTOOL:
          setSummaryConfigToolRefresh(true);
          break;
        default:
          break;
      }
      setDirtyOpen(false);
      setSelectedTabIndex(newIndex);
    } catch (err) {
      console.log(`err=${err}`);
    }
  }

  function handleCancel() {
    setDirtyOpen(false);
  }

  const show = (show: boolean) => {};

  return (
    <Box>
      <Tabs
        style={{
          minHeight: "0px",
          backgroundColor: "white",
        }}
        value={selectedTabIndex}
        onChange={handleChange}
        aria-label="registration pagescolor"
        TabIndicatorProps={{ style: { background: "#FFFFFF" } }}
      >
        {tabList.map((tab: string, index: number) => {
          return (
            <ConfigToolTab
              key={index}
              label={tab}
              index={index}
              {...TabProps(index)}
            />
          );
        })}
      </Tabs>
      {tabList.map((tab: string, index: number) => {
        return renderTabPanel(index, getComponent(tab));
      })}
      <ThemeProvider theme={EhrTheme}>
        <TerminalConfigConfirm
          open={dirtyOpen}
          show={show}
          text="You have unsaved data. Do you want to discard unsaved Data?"
          yesText="Discard Changes"
          noText="Cancel"
          onYes={handleDiscard}
          onNo={handleCancel}
          title="UNSAVED DATA"
        />
      </ThemeProvider>
    </Box>
  );
}
