.SummaryConfTool body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "<PERSON>xygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.SummaryConfTool code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.SummaryConfTool .labelStyle {
  text-align: right;
  line-height: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.SummaryConfTool input,
.SummaryConfTool configBtn {
  height: 40px;
  text-align: center;
  border: 1px solid #eeeeee;

  border-radius: 10px;
  -moz-border-radius: 10px;
}

.SummaryConfTool select {
  height: 40px;
  text-align: center;
  border: 1px solid #eeeeee;
  border-radius: 10px;
  -moz-border-radius: 10px;
  width: 100%;
}

.SummaryConfTool .checkboxInput {
  width: auto;
}

.SummaryConfTool .inputStyle {
  width: 100%;
  text-overflow: ellipsis;
}

.SummaryConfTool .readOnly {
  background: #eeeeee;
}

.SummaryConfTool .section {
  width: 100%;
  height: calc(100% - 25px);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.SummaryConfTool .titleDiv {
  width: 100%;
  background-color: #1890ff;
  color: #ffffff;
}

.SummaryConfTool .configBtn {
  width: 100%;
  height: 25px;
  background-color: #1890ff;
  color: #ffffff;
  margin-top: 5px;
  padding-top: 0px;
  border-radius: 10px;
  -moz-border-radius: 10px;
  cursor: default;
}

.SummaryConfTool .removeIcon {
  position: absolute;
  right: 17px;
  top: 10px;
  cursor: pointer;
  z-index: 999;
  display: none;
}

.SummaryConfTool .modify {
  position: absolute;
  right: 37px;
  top: 10px;
  cursor: pointer;
  z-index: 999;
  display: none;
}

.SummaryConfTool .react-grid-item {
  border: 1px solid #ececec;
  padding: 10px;
}

.SummaryConfTool .react-grid-item:hover .removeIcon {
  display: inline-block;
}

.SummaryConfTool .react-grid-item:hover .modify {
  display: inline-block;
}

.SummaryConfTool .react-resizable-handle {
  visibility: hidden;
}

.SummaryConfTool .title {
  padding-left: 10px;
}

.SummaryConfTool .react-grid-item:hover .react-resizable-handle {
  visibility: visible;
}

.SummaryConfTool .sectionCnt {
  flex: 5;
}

.SummaryConfTool .settingListRow {
  margin-top: 10px;
}

.SummaryConfTool .fileNameTitle {
  float: left;
  margin-top: -20px;
  margin-left: 10px;
  font-size: 16px;
  font-weight: 500;
}

.SummaryConfTool .cntModify {
  float: right;
  margin-top: -20px;
}

.SummaryConfTool .cntModify:hover,
.SummaryConfTool .rootModify:hover {
  cursor: pointer;
}

.SummaryConfTool .rootModify {
  font-size: 24px;
}

.SummaryConfTool .showSetting {
  display: block;
  -webkit-transition: all 5s ease-in 1s;
}
.SummaryConfTool .hideSetting {
  display: none;
  -webkit-transition: all 5s ease-out 1s;
}

.SummaryConfTool .hidePart {
  display: none;
}
.SummaryConfTool .infoMargin {
  margin-top: 15px;
}
