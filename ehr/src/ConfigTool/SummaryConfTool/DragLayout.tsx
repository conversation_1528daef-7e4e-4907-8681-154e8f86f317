import React, { PureComponent } from "react";
import { Layout, But<PERSON>, Modal } from "antd";

import { Row, Col } from "antd";

import Setting from "./Setting";
import SectionList from "./sectionList";

import { writeConf, readConf } from "./IO";
import SettingsIcon from "@mui/icons-material/Settings";
import {
  itemsHobj,
  itemsHobjParams,
  itemsAction,
  itemsConf,
  sliderwidget,
  printerwidget,
  slideAction,
  RS,
  DEFUALTS,
  SLIDER,
  PRINTER,
} from "./common";

const { Header, Content } = Layout;

const { confirm } = Modal;

export default class DragLayout extends PureComponent<any, any> {
  private id: number = 1;
  private dlt: any = {};
  private information: string = "";
  private square: any = [];
  private blankCount: number = 0;
  constructor(props: any) {
    super(props);
    this.state = {
      sectionConf: {
        selectedId: 0,
        curTitle: "",
        curWidgetType: "CHARTSTACK",
        curRHobj: itemsHobj["CHARTSTACK"][0],
        curRHobjP: itemsHobjParams["CHARTSTACK"],
        curAction: itemsAction["CHARTSTACK"],
        curConf: itemsConf["CHARTSTACK"],
      },

      rootData: {
        RR: {},
        Header: {},
        root: {},
      },

      widgets: [], //section list data
      //flag dialog
      editModalVisible: false,
      rootModalVisible: false,
      frameModalVisible: false,
      infoModalVisible: false,
      saveConfVisible: false,
      showSelectConfWin: false,
      sltRootType: "RR",

      headerTool: [], //header tool list data
      showSetting: true,

      //cps screen width and height
      cellH: DEFUALTS.cellH,
      cellW: DEFUALTS.cellW,
      //save config file to corresponding file
      filename: "Demo",
      //get all config files from config tool
      filesname: [],
      //select files to load
      loadFileName: "Demo_SummaryConfTool.conf",
      //set tag for config
      tab: DEFUALTS.tab,

      showFileName: "",

      frameIptValue: {
        tab: DEFUALTS.tab,
        cellH: DEFUALTS.cellH,
        cellW: DEFUALTS.cellW,
      },
    };
  }

  componentDidMount() {
    this.onLoadConf();
  }

  serverChange(type: string, evt: any) {
    if (type === "files") {
      let loadFileName = evt.target.value;
      let filename = evt.target.value.split("_SummaryConfTool.conf")[0];
      this.setState({
        loadFileName: loadFileName,
        filename: filename,
      });
    }
  }

  onRemoveItem(i: number, callback: any) {
    this.setState((prevState: any) => {
      return {
        widgets: prevState.widgets.filter(
          (item: any, index: number) => index !== Number(i)
        ),
      };
    }, callback);
  }
  getPosition() {
    // search a place to add new chart
    for (let i = 0; i < this.square.length; i++) {
      for (let j = 0; j < DEFUALTS.SCREENWIDTH; j++) {
        if (this.canAddNewWidget(i, j)) {
          return { x: j, y: i };
        }
      }
    }
    //Default position
    return { x: 0, y: this.square.length };
  }

  canAddNewWidget(startY: number, startX: number) {
    for (let i = startY; i < startY + DEFUALTS.widgetDefualtH; i++) {
      for (let j = startX; j < startX + DEFUALTS.widgetDefualtW; j++) {
        if (Number(this.square[i][j]) === 1) {
          return false;
        }
      }
    }
    return true;
  }

  addChart(type: any, id: number, action: any) {
    var tmpobj =
      action === "editWidgets"
        ? { x: this.dlt.x, y: this.dlt.y }
        : this.getPosition();
    const addItem = {
      x: tmpobj.x,
      y: tmpobj.y,
      w: action === "editWidgets" ? this.dlt.w : DEFUALTS.widgetDefualtW,
      h: action === "editWidgets" ? this.dlt.h : DEFUALTS.widgetDefualtH,
      i: new Date().getTime().toString(),
      title: this.state.sectionConf.curTitle
        ? this.state.sectionConf.curTitle
        : this.state.sectionConf.curWidgetType,
      widgetType:
        type === "normal" ? this.state.sectionConf.curWidgetType : type,
      tab: this.state.tab,
      rHobj: this.state.sectionConf.curRHobj,
      rHobjP: this.state.sectionConf.curRHobjP,
      action: this.state.sectionConf.curAction,
      conf: this.state.sectionConf.curConf,
      id: this.id++,
    };
    if (action === "editWidgets") {
      var widgets = this.state.widgets.concat();
      widgets.splice(id, 1, addItem);
      this.setState({
        widgets: widgets,
        sectionConf: {
          ...this.state.sectionConf,
          selectedId: this.state.widgets.length,
        },
      });
    } else {
      this.setState({
        widgets: this.state.widgets.concat({
          ...addItem,
        }),
        sectionConf: {
          ...this.state.sectionConf,
          selectedId: this.state.widgets.length,
        },
      });
    }
  }

  onSelectItem(i: number) {
    var tmpConf = Object.assign({}, this.state.sectionConf, {
      curTitle: this.state.widgets[i].title,
      curWidgetType: this.state.widgets[i].widgetType,
      selectedId: i,
      curRHobj: this.state.widgets[i].rHobj,
      curRHobjP: this.state.widgets[i].rHobjP,
      curAction: this.state.widgets[i].action,
      curConf: this.state.widgets[i].conf,
    });

    this.setState({
      sectionConf: tmpConf,
    });
  }

  onChangeCPSsitting(type: string, evt: any) {
    if (type === "H") {
      let cellH = evt.target.value;
      this.setState({ cellH: cellH });
    } else if (type === "W") {
      let cellW = evt.target.value;
      this.setState({ cellW: cellW });
    } else if (type === "filename") {
      let filename = evt.target.value;
      this.setState({ filename: filename });
    } else if (type === "tab") {
      let tab = evt.target.value;
      this.setState({ tab: tab });
    }
  }

  onChangeSettings(value: string, type: string) {
    var tmpConf: any = {};
    tmpConf[type] = value;
    //Test with hard connection;
    if (type === "curWidgetType") {
      tmpConf["curRHobj"] = itemsHobj[value][0];
      tmpConf["curRHobjP"] = itemsHobjParams[value];
      tmpConf["curAction"] = itemsAction[value];
      tmpConf["curConf"] = itemsConf[value];
      tmpConf["curTitle"] = value;
    }
    tmpConf = Object.assign({}, this.state.sectionConf, tmpConf);
    this.setState({
      sectionConf: tmpConf,
    });
  }

  horizentalGroup(colObj: any) {
    var needGroup = false;

    for (var col in colObj) {
      if (!colObj[col]) continue;
      let flag: number;
      for (let j in colObj) {
        //keep col is the smaller one;
        var colNum: number = Number(col);
        var jNum: number = Number(j);
        if (colNum > jNum) {
          var temp = colNum;
          colNum = jNum;
          jNum = temp;
        }
        flag = colObj[colNum].w + colNum;
        if (jNum !== colNum && jNum < flag) {
          needGroup = true;
          colObj[colNum].cols = colObj[colNum].cols.concat(colObj[jNum].cols);
          colObj[colNum].w = jNum - colNum + colObj[jNum].w;
          delete colObj[jNum];
        }
      }
    }

    if (!needGroup) {
      return colObj;
    } else {
      this.horizentalGroup(colObj);
    }
  }

  verticalGroup(colObj: any) {
    var needGroup = false;

    for (var col in colObj) {
      if (!colObj[col]) continue;
      let flag: number;
      for (let j in colObj) {
        //keep col is the smaller one;
        var colNum: number = Number(col);
        var jNum: number = Number(j);
        if (colNum > jNum) {
          var temp = colNum;
          colNum = jNum;
          jNum = temp;
        }
        flag = colObj[colNum].h + colNum;
        if (jNum !== colNum && jNum < flag) {
          needGroup = true;
          colObj[colNum].cols = colObj[colNum].cols.concat(colObj[jNum].cols);
          colObj[colNum].h = jNum - colNum + colObj[jNum].h;
          delete colObj[jNum];
        }
      }
    }

    if (!needGroup) {
      return colObj;
    } else {
      this.verticalGroup(colObj);
    }
  }

  canMerged(hbox: any, data: any) {
    for (var key in hbox) {
      if (
        Number(key) < Number(data.x) &&
        Number(key) + hbox[key].w > Number(data.x)
      ) {
        return key;
      }
    }
    return -1;
  }

  sortDESC(arr: any) {
    var index: number, tmp: any;
    for (var i = 0; i < arr.length; i++) {
      index = 0;
      for (var j = 0; j < arr.length - i; j++) {
        if (arr[index].x < arr[j].x) {
          index = j;
        } else if (
          Number(arr[index].x) === Number(arr[j].x) &&
          Number(arr[index].y) < Number(arr[j].y)
        ) {
          index = j;
        }
      }
      tmp = arr[index];
      arr[index] = arr[arr.length - 1 - i];
      arr[arr.length - 1 - i] = tmp;
    }
    return arr;
  }

  async saveConfig(winobj: any) {
    var widgets = this.addBlankHboxToCols();
    let colObj: any = {},
      k = 0,
      tmp: any,
      data = this.sortDESC(JSON.parse(JSON.stringify(widgets))),
      me = this,
      col: any;
    colObj.Hbox = {};
    for (var i = 0; i < data.length; i++) {
      data[i].tab = me.state.tab;
    }
    if (data.length === 0) {
      this.setConfigWin({
        type: "info",
        flag: true,
        information: `Please add chart before saving!`,
      });
      this.setConfigWin(winobj);
      return;
    }
    const operation = async () => {
      this.setConfigWin(winobj);
      let map = this.createMap(data);
      let group = {};
      this.verticalMap(map, 0, map[0].length - 1, 0, map.length - 1, group);
      console.table(group);
      for (k; k < data.length; k++) {
        if (k === 0) {
          colObj.Hbox[data[k].x] = {};
          colObj.Hbox[data[k].x].cols = [data[k]];
          colObj.Hbox[data[k].x].w = data[k].w;
        } else {
          if (colObj.Hbox.hasOwnProperty(data[k].x)) {
            colObj.Hbox[data[k].x].cols.push(data[k]);
            colObj.Hbox[data[k].x].w =
              colObj.Hbox[data[k].x].w > data[k].w
                ? colObj.Hbox[data[k].x].w
                : data[k].w;
          } else if (this.canMerged(colObj.Hbox, data[k]) !== -1) {
            tmp = this.canMerged(colObj.Hbox, data[k]);
            colObj.Hbox[tmp].cols.push(data[k]);
            colObj.Hbox[tmp].w =
              data[k].x - Number(tmp) + data[k].w > colObj.Hbox[tmp].w
                ? data[k].x - Number(tmp) + data[k].w
                : colObj.Hbox[tmp].w;
          } else {
            colObj.Hbox[data[k].x] = {};
            colObj.Hbox[data[k].x].cols = [data[k]];
            colObj.Hbox[data[k].x].w = data[k].w;
          }
        }
      }

      this.horizentalGroup(colObj.Hbox);

      for (col in colObj.Hbox) {
        var hobj = colObj.Hbox[col].cols,
          obj = colObj.Hbox;
        if (hobj.length > 1) {
          obj[col].Vbox = {};
          for (k = 0; k < hobj.length; k++) {
            if (k === 0) {
              obj[col].Vbox[hobj[k].y] = {};
              obj[col].Vbox[hobj[k].y].cols = [hobj[k]];
              obj[col].Vbox[hobj[k].y].h = hobj[k].h;
            } else {
              if (obj[col].Vbox.hasOwnProperty(hobj[k].y)) {
                obj[col].Vbox[hobj[k].y].cols.push(hobj[k]);
                obj[col].Vbox[hobj[k].y].h =
                  obj[col].Vbox[hobj[k].y].h > hobj[k].h
                    ? obj[col].Vbox[hobj[k].y].h
                    : hobj[k].h;
              } else {
                obj[col].Vbox[hobj[k].y] = {};
                obj[col].Vbox[hobj[k].y].cols = [hobj[k]];
                obj[col].Vbox[hobj[k].y].h = hobj[k].h;
              }
            }
          }
          delete obj[col].cols;
        }
      }

      for (col in colObj.Hbox) {
        var vbox = colObj.Hbox[col].Vbox;
        if (vbox) {
          this.verticalGroup(vbox);
        }
      }

      this.addHboxTocols(colObj.Hbox);

      let cnt = this.assembleConf(colObj),
        that = this;
      var dobj = {
        cnt: cnt,
        filename: `${this.state.filename}_SummaryConfTool.conf`,
      };
      let resultData = await Cci.util.Hobj.requestRecords({
        hobj: "summaryConfTool/writeConfFile",
        params: dobj,
      });
      if (resultData.length) {
        this.setState({
          showFileName: `${this.state.filename}_SummaryConfTool.conf`,
        });
        this.onLoadConf();
      } else {
        that.setConfigWin({
          type: "info",
          flag: true,
          information: "Error occurred!",
        });
      }
    };
    const { filename, filesname } = this.state;
    const exists = filesname.some(
      (name: string) => name.split("_SummaryConfTool.conf")[0] === filename
    );

    if (exists) {
      try {
        confirm({
          title: `${filename}_SummaryConfTool.conf already exists,\n Do you want to replace it?`,
          okText: "Yes",
          cancelText: "Cancel",
          onOk: () => {
            operation();
          },
        });
      } catch (error) {
        console.log("An error occurred:", error);
      }
    } else {
      operation();
    }
  }

  verticalMap(
    map: any,
    startIdx: number,
    endIdx: number,
    startIdy: number,
    endIdy: number,
    group: any
  ) {
    group.Vbox = {};
    console.table(map);
    let newStartY = startIdy,
      unDevideFlag = false,
      cols: any = [];
    for (let i = startIdy; i < endIdy - 1; i++) {
      for (let j = startIdx; j <= endIdx; j++) {
        if (map[i][j] === map[i + 1][j]) {
          unDevideFlag = true;
        }
        if (cols.indexOf(map[i][j]) === -1) cols.push(map[i][j]);
        if (!unDevideFlag && j === endIdx) {
          if (cols.length === 1) {
            group.Vbox[newStartY] = {
              cols: cols,
            };
          } else {
            this.horizentalMap(map, startIdx, endIdx, newStartY, i, group.Vbox);
          }
          newStartY = i + 1;
          unDevideFlag = false;
          cols = [];
          console.table(group);
        }
      }
      unDevideFlag = false;
    }
    if (newStartY !== startIdy) {
      this.horizentalMap(map, startIdx, endIdx, newStartY, endIdy, group.Vbox);
    }
    if (newStartY === startIdy && cols.length === 1) {
      group.Vbox[newStartY] = {
        cols: cols,
      };
      console.table(group);
    }
  }

  horizentalMap(
    map: any,
    startIdx: number,
    endIdx: number,
    startIdy: number,
    endIdy: number,
    group: any
  ) {
    group.Hbox = {};
    console.table(map);
    let unDevideFlag = false,
      cols: any = [],
      newStartX = startIdx;
    for (let i = startIdx; i < endIdx - 1; i++) {
      for (let j = startIdy; j <= endIdy; j++) {
        if (map[j][i] === map[j][i + 1]) {
          unDevideFlag = true;
        }
        if (cols.indexOf(map[j][i]) === -1) cols.push(map[j][i]);
        if (!unDevideFlag && i === endIdx) {
          if (cols.length === 1) {
            group.Hbox[newStartX] = {
              cols: cols,
            };
            console.table(group);
          } else {
            this.verticalMap(map, newStartX, i, startIdy, endIdy, group.Hbox);
          }
          newStartX = i + 1;
          unDevideFlag = false;
          cols = [];
        }
      }
      unDevideFlag = false;
    }
    if (newStartX !== startIdx) {
      this.verticalMap(map, newStartX, endIdx, startIdy, endIdy, group.Hbox);
    }
    if (newStartX === startIdx && cols.length === 1) {
      group.Hbox[newStartX] = {
        cols: cols,
      };
      console.table(group);
    }
  }

  createMap(data: any) {
    let len = this.square.length,
      depth = this.square[0].length;
    let map = new Array(len);
    //initialize map;
    for (let i = 0; i < len; i++) {
      map[i] = new Array(depth);
    }
    for (let i = 0; i < data.length; i++) {
      let x = data[i].x,
        y = data[i].y,
        w = data[i].w,
        h = data[i].h,
        id = data[i].id;
      for (let k = y; k < y + h; k++) {
        for (let j = x; j < x + w; j++) {
          map[k][j] = id;
        }
      }
    }
    console.table(map);
    return map;
  }

  addBlankHboxToCols() {
    var j: number,
      i: number,
      empty: any = [];
    for (j = 0; j < this.square.length; j++) {
      var temp: any = {},
        startFlag = true;
      for (i = 0; i < 12; i++) {
        if (!this.square[j][i]) {
          if (startFlag) {
            temp.x = i;
            startFlag = false;
            temp.w = 1;
            temp.h = 1;
            temp.y = j;
            temp.rs = "s";
            temp.id = "blank" + this.blankCount;
            temp.tab = this.state.tab;
          } else {
            temp.w++;
          }
        } else {
          if (JSON.stringify(temp) !== "{}") {
            empty.push(temp);
            this.blankCount++;
            temp = {};
          }
          startFlag = true;
        }
      }
      if (JSON.stringify(temp) !== "{}") {
        empty.push(temp);
        this.blankCount++;
      }
    }
    var tmpArr = this.mergeEmptys(empty);
    tmpArr = tmpArr.concat(this.state.widgets);
    return tmpArr;
  }

  sortDescY(arr: any) {
    var temp: any,
      indexArr: any = [],
      newArr: any = [];
    for (var i = 0; i < arr.length; i++) {
      for (var j = i + 1; j < arr.length; j++) {
        if (arr[i].y > arr[j].y) {
          temp = Object.assign({}, arr[i]);
          arr[i] = Object.assign({}, arr[j]);
          arr[j] = Object.assign({}, temp);
          temp = {};
        }
      }
      indexArr.push(arr[i].y);
    }

    for (var k = 0; k < indexArr.length; k++) {
      if (k === 0) {
        newArr.push(arr[k]);
      }
      if (k < indexArr.length - 1 && indexArr[k] !== indexArr[k + 1] - 1) {
        newArr[newArr.length - 1].h =
          indexArr[k] - newArr[newArr.length - 1].y + 1;
        newArr.push(arr[k + 1]);
      }
      if (k === indexArr.length - 1) {
        newArr[newArr.length - 1].h =
          indexArr[k] - newArr[newArr.length - 1].y + 1;
      }
    }
    return newArr;
  }

  mergeEmptys(empty: any) {
    var merged: any = [],
      curEmpty: any,
      tempArr: any = [],
      mergedIndex: any = [];
    for (var k = 0; k < empty.length; k++) {
      if (mergedIndex.indexOf(k) > -1) {
        continue;
      }

      curEmpty = empty[k];
      tempArr.push(curEmpty);
      mergedIndex.push(k);

      for (
        var i = k + 1;
        i < empty.length && mergedIndex.indexOf(i) === -1;
        i++
      ) {
        if (mergedIndex.indexOf(i) > -1) {
          continue;
        }

        if (curEmpty.x === empty[i].x && curEmpty.w === empty[i].w) {
          tempArr.push(empty[i]);
          mergedIndex.push(i);
        }
      }
      merged = merged.concat(this.sortDescY(tempArr));
      tempArr = [];
    }
    return merged;
  }

  addHboxTocols(colObj: any) {
    var cols: any, obj: any, str: string;
    for (var key in colObj) {
      obj = colObj[key];
      cols = obj.cols;
      if (cols) {
        obj.Hbox = {};
        for (var j = 0; j < cols.length; j++) {
          str = "h" + cols[j].x + "" + cols[j].y;
          obj.Hbox[str] = {};
          obj.Hbox[str].cols = [cols[j]];
          obj.Hbox[str].w = cols[j].w;
        }
        delete obj.cols;
      } else {
        this.addHboxTocols(obj);
      }
    }
  }

  async onLoadConf() {
    let data =
      (await Cci.util.Hobj.requestRecords({
        hobj: "summaryConfTool/listConfFile",
      })) || [];
    if (data.length && data[0]?.value) {
      var filesname = data[0].value.split("./");
      filesname.shift();
      if (this.state.filesname.length === 0) {
        this.setState({
          filesname: filesname,
          loadFileName: filesname[0],
          filename: filesname[0].split("_SummaryConfTool.conf")[0],
        });
      } else {
        this.setState({
          filesname,
        });
      }
    }
    return data;
  }

  async getAllConfFiles() {
    try {
      const data = await this.onLoadConf();
      if (data.length && data[0]?.value) {
        this.setConfigWin({ type: "selectConf", flag: true });
      } else {
        this.setConfigWin({
          type: "info",
          flag: true,
          information: "No SummaryScreen conf exists.",
        });
      }
    } catch (error) {
      this.setConfigWin({
        type: "info",
        flag: true,
        information: "Failed to load SummaryScreen conf list.",
      });
    }
  }

  async loadConfigFileFromServer() {
    this.setConfigWin({ type: "selectConf", flag: false });
    var obj = {
      filename: this.state.loadFileName.trim(),
    };
    let data = await Cci.util.Hobj.requestRecords({
      hobj: "summaryConfTool/readConfFile",
      params: obj,
    });
    if (data.length) {
      this.loadConfig(data[0].value);
      this.setState({ showFileName: this.state.loadFileName });
    } else {
      this.setConfigWin({
        type: "info",
        flag: true,
        information: `The file ${this.state.loadFileName} is not exsit! `,
      });
    }
  }

  loadConfig(cnt: any) {
    let confArr = readConf(cnt),
      i = 0,
      tmpConf: any = {},
      rootData: any,
      children: any = [],
      widgets: any = [],
      me = this;
    let confObj = me.convertArr(confArr);
    for (i; i < confArr.length; i++) {
      if (confArr[i].id.trim() === "root") {
        tmpConf.root = Object.assign({}, confArr[i]);
      } else if (confArr[i].id.trim() === "RR") {
        tmpConf.RR = Object.assign({}, confArr[i]);
        children = confArr[i].children.replace(/\[|]|"/g, "").split(",");
        confArr[i].w = "1";
        confArr[i].h = undefined;
        confArr[i].x = 0;
        confArr[i].y = 0;
        me.analyzeChildren(widgets, children, confObj, confArr[i]);
        me.setState({
          widgets: widgets,
        });
      } else if (
        confArr[i].id.trim() === "ACTIONS" ||
        confArr[i].id.trim() === "Header"
      ) {
        tmpConf.Header = Object.assign({}, confArr[i]);
      }
    }
    rootData = Object.assign({}, me.state.rootData, tmpConf);
    me.setState({
      rootData: rootData,
    });
  }

  analyzeChildren(widgets: any, cl: any, confObj: any, parent: any) {
    let k = 0;
    let children: any,
      conf: any = {};
    for (k; k < cl.length; k++) {
      conf = Object.assign({}, confObj[cl[k]]);
      if (conf.rs === "r") {
        children = conf.children.replace(/\[|]|"/g, "").split(",");
        conf = Object.assign({}, confObj[cl[k]]);
        if (parent.layout.trim() === "hbox") {
          conf.h = parent.h;
        } else if (parent.layout.trim() === "vbox") {
          if (conf.h && !conf.h.endsWith("%")) {
            conf.h = Math.floor(conf.h / this.state.cellH);
            conf.h =
              conf.h < DEFUALTS.widgetDefualtH
                ? DEFUALTS.widgetDefualtH
                : conf.h;
          } else {
            conf.h = DEFUALTS.widgetDefualtH;
          }
        }
        this.analyzeChildren(widgets, children, confObj, conf);
      } else {
        if (conf.widgetType === "SLIDER" || conf.widgetType === "button") {
          console.log("slider/button");
        } else if (!conf.widgetType) {
          console.log("useless widgets");
        } else {
          conf.id = this.id++;
          conf.i = new Date().getTime().toString() + conf.id;
          //width should be percent value, height should be number
          if (conf.w && conf.w.endsWith("%")) {
            conf.w = Math.round(
              (conf.w.replace(/%/g, "") / 100) * DEFUALTS.SCREENWIDTH
            );
            conf.w =
              conf.w < DEFUALTS.widgetDefualtW
                ? DEFUALTS.widgetDefualtW
                : conf.w;
          } else {
            conf.w = DEFUALTS.widgetDefualtW;
          }
          if (conf.h && !conf.h.endsWith("%")) {
            conf.h = Math.floor(conf.h / this.state.cellH);
            conf.h =
              conf.h < DEFUALTS.widgetDefualtH
                ? DEFUALTS.widgetDefualtH
                : conf.h;
          } else {
            conf.h = DEFUALTS.widgetDefualtH;
          }
          conf.x = Math.floor(conf.x);
          conf.y = Math.floor(conf.y);
          conf.tab = this.state.tab;
          widgets.push(conf);
        }
      }
    }
  }

  convertArr(arr: any) {
    let obj: any = {};
    for (var i = 0; i < arr.length; i++) {
      obj[arr[i].id] = Object.assign({}, arr[i]);
    }
    return obj;
  }

  assembleFrame(target: any, rPrt: any) {
    let rPrtWidget: any = { rs: "r", id: target };
    let children = "";
    if (target === "root") {
      rPrtWidget.action = JSON.stringify(slideAction);
      rPrtWidget.children = '["Header", "RS"]';
      rPrtWidget.widgetType = JSON.stringify(
        Object.assign({}, sliderwidget, printerwidget)
      );
      rPrtWidget.xywhConstant = "tab";
      rPrtWidget.layout = "vbox";
    } else if (target === "Header") {
      let headerTool: any = [];

      headerTool.push("SLIDER");
      children = children + SLIDER;

      headerTool.push("PRINT");
      children = children + PRINTER;

      if (headerTool.length > 0) {
        rPrtWidget.children = '["' + headerTool.join('","') + '"]';
      }
      rPrtWidget.xywhConstant = "xywh";
      rPrtWidget.x = this.state.rootData[target].x
        ? this.state.rootData[target].x
        : 0;
      rPrtWidget.y = this.state.rootData[target].y
        ? this.state.rootData[target].y
        : 0;
      rPrtWidget.w = this.state.rootData[target].w
        ? this.state.rootData[target].w
        : "100%";
      rPrtWidget.h = 40;
      rPrtWidget.layout = "hbox";
      rPrtWidget.widgetType = "";
    }
    return { rPrtWidget, children };
  }

  assembleRPartConf(obj: any, type: any, cnt: any, rRec: any, parentStep: any) {
    var key: any,
      colCntWidegt: any = { rs: "r" },
      rCols: any,
      cols: any,
      vbox: any,
      hbox: any;
    for (key in obj) {
      cols = obj[key].cols;
      vbox = obj[key].Vbox;
      hbox = obj[key].Hbox;
      rCols = [];
      colCntWidegt.id =
        type === "hbox" ? "RH" + parentStep + key : "RV" + parentStep + key;
      colCntWidegt.id = parentStep === 0 ? "RR" : colCntWidegt.id;
      colCntWidegt.x = obj[key].w;
      colCntWidegt.xywhConstant = "f";
      if (cols) {
        for (let i = 0; i < cols.length; i++) {
          rCols.push(cols[i].id);
          cols[i].w = (cols[i].w / DEFUALTS.SCREENWIDTH) * 100 + "%";
          cols[i].h = cols[i].h * this.state.cellH;
          rRec.push(writeConf(cols[i], "s"));
        }
        colCntWidegt.layout = "hbox";
        // every colomns style
        colCntWidegt.children = '["' + rCols.join('","') + '"]';

        cnt.push(writeConf(colCntWidegt, "r"));
      } else if (vbox) {
        for (let i in vbox) {
          rCols.push("RV" + parentStep + key + i);
        }
        colCntWidegt.layout = "vbox";
        colCntWidegt.children = '["' + rCols.join('","') + '"]';
        cnt.push(writeConf(colCntWidegt, "r"));
        this.assembleRPartConf(vbox, "vbox", cnt, rRec, parentStep + key);
      } else if (hbox) {
        for (let i in hbox) {
          rCols.push("RH" + parentStep + key + i);
        }
        colCntWidegt.layout = "hbox";
        colCntWidegt.children = '["' + rCols.join('","') + '"]';
        cnt.push(writeConf(colCntWidegt, "r"));
        this.assembleRPartConf(hbox, "hbox", cnt, rRec, parentStep + key);
      }
    }
  }

  assembleConf(obj: any) {
    let rRec: any = [],
      cntRec: any = [],
      rPrtWidget: any = {},
      confArr: any = [],
      conf: any,
      root: any;
    //this.onPrepareHeaderCnt();
    this.assembleRPartConf({ 0: obj }, "hbox", cntRec, rRec, 0);

    rPrtWidget = this.assembleFrame("Header", "");
    confArr.push(writeConf(rPrtWidget.rPrtWidget, "r"));
    confArr.push(rPrtWidget.children);

    rPrtWidget = this.assembleFrame("root", "");
    root = writeConf(rPrtWidget.rPrtWidget, "r");
    conf = [root]
      .concat(RS)
      .concat(confArr)
      .concat(cntRec)
      .concat(rRec)
      .join("\n");
    return conf;
  }

  setConfigWin(obj: any) {
    switch (obj.type) {
      case "frame":
        let frameModalVisible = obj.flag;
        let sltRootType = obj.rType;
        this.setState({
          frameModalVisible: frameModalVisible,
          tab: this.state.frameIptValue.tab,
          cellH: this.state.frameIptValue.cellH,
          cellW: this.state.frameIptValue.cellW,
        });
        this.setState({ sltRootType: sltRootType });
        break;
      case "info":
        let infoModalVisible = obj.flag;
        this.information = obj.information;
        this.setState({ infoModalVisible: infoModalVisible });
        break;
      case "saveconf":
        let saveConfVisible = obj.flag;
        this.setState({ saveConfVisible: saveConfVisible });
        break;
      case "selectConf":
        let showSelectConfWin = obj.flag;
        this.setState({ showSelectConfWin: showSelectConfWin });
        break;
      default:
        break;
    }
  }

  setEditWin(editModalVisible: any) {
    this.setState({ editModalVisible: editModalVisible });
  }

  clearSquare() {
    this.square = [];
  }

  setSquare(widgets: any) {
    this.clearSquare();
    for (var i in widgets) {
      var x = widgets[i].x,
        y = widgets[i].y,
        w = widgets[i].w,
        h = widgets[i].h;
      for (var j = y; j < y + h; j++) {
        for (var k = x; k < x + w; k++) {
          if (!this.square[j]) {
            this.square[j] = [];
          }
          this.square[j][k] = 1;
        }
      }
    }
  }

  onUpdateWidgets(lg: any) {
    let i = 0,
      widgets: any = [];
    for (i; i < lg.length; i++) {
      widgets.push(Object.assign({}, this.state.widgets[i], lg[i]));
    }
    this.setSquare(widgets);

    this.setState({
      widgets: widgets,
    });
  }

  onModifySection(i: number) {
    this.onSelectItem(i);
    this.setEditWin(true);
  }

  onShowCntDialog(rootType: any) {
    this.setState({
      sltRootType: rootType,
    });

    this.setConfigWin({ type: "frame", flag: true, rType: rootType });
  }

  onSaveEditDialog() {
    let id = this.state.sectionConf.selectedId;
    this.setEditWin(false);
    if (id > -1) {
      this.dlt.x = this.state.widgets[id].x;
      this.dlt.y = this.state.widgets[id].y;
      this.dlt.w = this.state.widgets[id].w;
      this.dlt.h = this.state.widgets[id].h;
      this.addChart("normal", id, "editWidgets");
    }
  }

  onPrepareHeaderCnt() {
    let tmpConf: any = {},
      rootData = {};

    let children: any = [];
    children.push("Header");

    children.push("RS");

    tmpConf.root = {
      widgetType: JSON.stringify(
        Object.assign({}, sliderwidget, printerwidget)
      ),
      id: "root",
      rs: "r",
      children: '["' + children.join('","') + '"]',
      xywhConstant: "tab",
      layout: "vbox",
      action: JSON.stringify(slideAction),
    };

    rootData = Object.assign({}, this.state.rootData, tmpConf);
    this.setState({
      rootData: rootData,
    });
  }

  onUpdateWidgetsCnt() {
    /*TODO*/
    console.log("reassign widgets data");
  }

  onUpdateHeaderCnt() {
    let headerTool: any = [],
      addItem: any;
    if (!this.refs.frameData) {
      return;
    }
    let refs: any = this.refs,
      frameData: any = refs.frameData,
      headCntData: any = frameData.state;
    if (headCntData.addSlider) {
      addItem = {
        x: 0,
        y: 0, // puts it at the bottom
        w: headCntData.addPrinter ? 80 : 98,
        h: 1,
        i: new Date().getTime().toString(),
        widgetType: "SLIDER",
      };
      headerTool.push(addItem);
    }

    if (headCntData.addPrinter) {
      addItem = {
        x: 82,
        y: 0, // puts it at the bottom
        w: 3,
        h: 1,
        i: new Date().getTime().toString() + "0",
        widgetType: "BUTTON",
      };
      headerTool.push(addItem);
    }

    this.setState({
      headerTool: headerTool,
    });
  }

  handleRootModify(rType: any) {
    this.setConfigWin({ type: "frame", flag: true, rType: rType });
  }

  onToggle() {
    var showSetting = !this.state.showSetting;

    this.setState({
      showSetting: showSetting,
    });
  }

  onSaveFrameDialog(obj: any) {
    this.setConfigWin(obj);
  }

  render() {
    let fileItems = this.state.filesname.map((file: any) => (
      <option key={file} value={file}>
        {" "}
        {file}{" "}
      </option>
    ));
    return (
      <Layout className="SummaryConfTool" style={{ flexDirection: "row" }}>
        <Layout
          className={this.state.showSetting ? "showSetting" : "hideSetting"}
          style={{
            overflow: "auto",
            height: "100vh",
            left: 0,
            backgroundColor: "#ffffff",
            border: "1px solid #CCCCCC",
            flex: 1,
          }}
        >
          <Header
            style={{
              backgroundColor: "#CCCCCC",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <h2>Setting</h2>
            <span
              className="rootModify"
              title="Set cps screen width and height"
            >
              <SettingsIcon
                onClick={this.handleRootModify.bind(this, "root")}
              />
            </span>
          </Header>
          <Setting
            config={this.state.sectionConf}
            onChangeSettings={this.onChangeSettings.bind(this)}
            onShowConfigWin={this.setConfigWin.bind(this)}
          />
          <Row style={{ marginTop: "150px" }}>
            <Col lg={{ span: 8 }}>
              <Button
                type="primary"
                style={{
                  marginRight: "7px",
                  marginTop: "5px",
                  marginLeft: "5px",
                  width: "90%",
                }}
                onClick={this.addChart.bind(this, "normal", -1)}
              >
                {" "}
                Add{" "}
              </Button>
            </Col>
            <Col lg={{ span: 8 }}>
              <Button
                type="primary"
                style={{
                  marginRight: "7px",
                  marginTop: "5px",
                  marginLeft: "5px",
                  width: "90%",
                }}
                onClick={this.setConfigWin.bind(this, {
                  type: "saveconf",
                  flag: true,
                })}
              >
                {" "}
                Save{" "}
              </Button>
            </Col>
            <Col lg={{ span: 8 }}>
              <Button
                type="primary"
                style={{
                  marginRight: "5px",
                  marginTop: "5px",
                  marginLeft: "5px",
                  width: "90%",
                }}
                onClick={this.getAllConfFiles.bind(this)}
              >
                {" "}
                Load Conf{" "}
              </Button>
            </Col>
          </Row>
        </Layout>
        <Layout
          onClick={this.onToggle.bind(this)}
          style={{
            overflow: "hidden",
            height: "100vh",
            left: "5px",
            color: "#ffffff",
            backgroundColor: "#7DBCEA",
            border: "1px solid #7DBCEA",
            fontSize: "large",
            flex: 0.05,
            textAlign: "center",
          }}
        >
          <Row style={{ marginTop: "0px" }}>
            <Col lg={{ span: 24 }}>
              <div
                style={{
                  writingMode: "vertical-rl",
                  height: "100%",
                  cursor: "pointer",
                }}
              >
                {this.state.showSetting ? "Hide Setting" : "Show Setting"}
              </div>
            </Col>
          </Row>
        </Layout>

        <Content style={{ flex: 3, flexDirection: "column" }}>
          <Layout style={{ overflow: "auto", height: "calc(100vh - 120px)" }}>
            <SectionList
              className="sectionCnt"
              list={this.state.widgets}
              onRemoveItem={this.onRemoveItem.bind(this)}
              onModifySection={this.onModifySection.bind(this)}
              onSelectItem={this.onSelectItem.bind(this)}
              selectedId={this.state.sectionConf.selectedId}
              showFileName={this.state.showFileName}
              updateWidgetList={this.onUpdateWidgets.bind(this)}
              onShowCntDialog={this.onShowCntDialog.bind(this)}
              sltRoot={this.state.sltRootType}
            />
          </Layout>
        </Content>
        <Modal
          className="SummaryConfTool"
          title={"Edit Section " + this.state.sectionConf.selectedId}
          style={{ top: 20 }}
          width="660px"
          open={this.state.editModalVisible}
          onOk={this.onSaveEditDialog.bind(this)}
          onCancel={() => this.setEditWin(false)}
        >
          <Setting
            config={this.state.sectionConf}
            editFlag={this.state.editModalVisible}
            onChangeSettings={this.onChangeSettings.bind(this)}
            onShowConfigWin={this.setConfigWin.bind(this)}
          />
        </Modal>
        <Modal
          className="SummaryConfTool"
          title="Set minimized width and height"
          style={{ top: 20 }}
          open={this.state.frameModalVisible}
          onOk={() => {
            this.setState({
              frameIptValue: {
                tab: this.state.tab,
                cellH: this.state.cellH,
                cellW: this.state.cellW,
              },
            });
            this.onSaveFrameDialog({
              type: "frame",
              flag: false,
              rType: this.state.sltRootType,
            });
          }}
          onCancel={() =>
            this.setConfigWin({
              type: "frame",
              flag: false,
              rType: this.state.sltRootType,
            })
          }
        >
          <Layout>
            <Row style={{ marginTop: "5px" }}>
              <Col lg={{ span: 5, offset: 1 }}>
                <div className="labelStyle">Tag</div>
              </Col>
              <Col lg={{ span: 15, offset: 1 }}>
                <input
                  className="inputStyle"
                  value={this.state.tab}
                  onChange={this.onChangeCPSsitting.bind(this, "tab")}
                />
              </Col>
            </Row>
            <Row style={{ marginTop: "5px" }}>
              <Col lg={{ span: 5, offset: 1 }}>
                <div className="labelStyle">Screen Width</div>
              </Col>
              <Col lg={{ span: 15, offset: 1 }}>
                <input
                  className="inputStyle"
                  value={this.state.cellW}
                  onChange={this.onChangeCPSsitting.bind(this, "W")}
                />
              </Col>
              <Col lg={{ span: 1, offset: 0 }}>
                <div className="labelStyle">px</div>
              </Col>
            </Row>
            <Row style={{ marginTop: "5px" }}>
              <Col lg={{ span: 5, offset: 1 }}>
                <div className="labelStyle">Screen Height</div>
              </Col>
              <Col lg={{ span: 15, offset: 1 }}>
                <input
                  className="inputStyle"
                  value={this.state.cellH}
                  onChange={this.onChangeCPSsitting.bind(this, "H")}
                />
              </Col>
              <Col lg={{ span: 1, offset: 0 }}>
                <div className="labelStyle">px</div>
              </Col>
            </Row>
          </Layout>
        </Modal>
        <Modal
          className="SummaryConfTool"
          title="Set saved config file name"
          style={{ top: 20 }}
          open={this.state.saveConfVisible}
          onOk={() => this.saveConfig({ type: "saveconf", flag: false })}
          onCancel={() => this.setConfigWin({ type: "saveconf", flag: false })}
          okButtonProps={{ disabled: !this.state.filename }}
        >
          <Layout>
            <Row style={{ marginTop: "5px" }}>
              <Col lg={{ span: 5, offset: 1 }}>
                <div className="labelStyle">Set Filename: </div>
              </Col>
              <Col lg={{ span: 15, offset: 1 }}>
                <input
                  className="inputStyle"
                  value={this.state.filename}
                  onChange={this.onChangeCPSsitting.bind(this, "filename")}
                />
              </Col>
            </Row>
          </Layout>
        </Modal>
        <Modal
          className="SummaryConfTool"
          title="Select config file"
          style={{ top: 20 }}
          open={this.state.showSelectConfWin}
          onOk={() => this.loadConfigFileFromServer()}
          onCancel={() =>
            this.setConfigWin({ type: "selectConf", flag: false })
          }
        >
          <Layout>
            <Row style={{ marginTop: "7px" }}>
              <Col lg={{ span: 8, offset: 1 }}>
                <div className="labelStyle">Select config file: </div>
              </Col>
              <Col lg={{ span: 13, offset: 1 }}>
                <div>
                  <select
                    value={this.state.loadFileName}
                    onChange={this.serverChange.bind(this, "files")}
                  >
                    {fileItems}
                  </select>
                </div>
              </Col>
            </Row>
          </Layout>
        </Modal>
        <Modal
          className="SummaryConfTool"
          title="Information"
          style={{ top: 20 }}
          open={this.state.infoModalVisible}
          onOk={() => this.setConfigWin({ type: "info", flag: false })}
          onCancel={() => this.setConfigWin({ type: "info", flag: false })}
          footer={[
            <Button
              key="OK"
              onClick={() => this.setConfigWin({ type: "info", flag: false })}
            >
              OK
            </Button>,
          ]}
        >
          <p>{this.information}</p>
        </Modal>
      </Layout>
    );
  }
}
