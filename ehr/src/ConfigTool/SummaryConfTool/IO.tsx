var propertyToIndex: any = {
  id: 0,
  rs: 1,
  children: 2,
  xywhConstant: 3,
  x: 4,
  y: 5,
  w: 6,
  h: 7,
  layout: 8,
  tab: 9,
  widgetType: 10,
  title: 12,
  rHobj: 13,
  rHobjP: 14,
  action: 19,
  conf: 23,
};

function parseWidgetCfg(widgetCfg: any) {
  var out: any = {};
  for (var key in propertyToIndex) {
    out[key] = widgetCfg[propertyToIndex[key]];
  }
  return out;
}

function encodeWidgetCfg(widgetCfg: any) {
  var out: any = [];
  for (var key in widgetCfg) {
    if (propertyToIndex[key] !== undefined) {
      out[propertyToIndex[key]] = widgetCfg[key];
    }
  }
  return out;
}

function readCsv(str: any, rowSep: any, colSep: any, comment: any) {
  return str
    .split(rowSep)
    .filter(function (line: any) {
      return !/^\s*$/.test(line);
    })
    .filter(function (line: any) {
      return !comment.test(line);
    })
    .reduce(function (table: any, line: any) {
      table.push(line.split(colSep));
      return table;
    }, []);
}

export const readConf = (str: any) => {
  return readCsv(str, "\n", "[]", /^\s*#/).map(parseWidgetCfg);
};

function writeCsv(table: any, rowSep: any, colSep: any) {
  return table
    .map(function (row: any) {
      return row.join(colSep);
    })
    .join(rowSep);
}

export const writeConf = (widget: any, rs: any) => {
  if (rs === "s") {
    widget.xywhConstant = "xywh";
    widget.rs = rs;
    widget.children = "";
  }

  return writeCsv([encodeWidgetCfg(widget)], "\n", "[]");
};
