"use client";
import yaml from "js-yaml";
import { ThemeProvider, styled } from "@mui/material/styles";
import EhrTheme from "../theme/theme";
import { FormEvent, useState, useEffect } from "react";
import { Responsive, WidthProvider } from "react-grid-layout";
import { v4 as uuid } from "uuid";
import { getWidgetType, parseConf } from "./util/GridWidgetData";
import ExtCmp from "../ExtComponent/components/ExtCmp";
import { showError } from "@cci-monorepo/common";

import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";

const ResponsiveGridLayout = WidthProvider(Responsive);

const rowHeight: number = 30;

type LayoutType = {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
};

type WidgetType = {
  id: string;
  config: string;
};

type Widget = {
  id: string;
  conf: any;
  layout: LayoutType;
};

type THeaderPros = {
  widgetTypes: WidgetType[];
  addWidgetType: (arg0: string) => void;
};

type TBodyPros = {
  widgets: Widget[];
  deleteWidget: (arg0: string) => void;
  collapseWidget: (arg0: string) => void;
  expandWidget: (arg0: string) => void;
  handleDragStop: (arg0: ReactGridLayout.Layout[]) => void;
};

const Root = styled("main")(({ theme }) => ({
  position: "relative",
  height: "100%",
  width: "100%",
  overflowY: "auto",
}));

export default function GridWidget() {
  let [widgetTypes, setWidgetTypes] = useState<any[]>([]);
  let [widgets, setWidgets] = useState([] as Widget[]);
  let [widgetsHeight, setWidgetsHeight] = useState<any[]>([]);

  useEffect(() => {
    getWidgetType().then((ret) => {
      if (ret) {
        const widget_type = yaml.load(ret);
        if (typeof widget_type === "object" && Array.isArray(widget_type)) {
          setWidgetTypes(widget_type);
        }
      }
    });
  }, []);

  const addWidgetType = (widgetType: string) => {
    let config = widgetType;
    parseConf(config).then((conf) => {
      if (conf) {
        Object.keys(conf).forEach((key) => {
          if (conf[key] && typeof conf[key] === "object") {
            conf[key] = JSON.stringify(conf[key]);
          }
        });
        let id = uuid(),
          loc = JSON.parse(conf.loc);

        let layout = {
          i: id,
          x: 0,
          y: 0,
          w: (parseInt(loc.u) * 12) / window.innerWidth,
          h: parseInt(loc.v) / rowHeight,
        };

        setWidgets((widgets) => [
          ...widgets,
          { id: id, conf: conf, layout: layout },
        ]);
        setWidgetsHeight((widgestHeight) => [
          ...widgestHeight,
          { id: id, height: layout.h },
        ]);
      } else {
        showError("Invalid widget configuration!", 500, () => {});
      }
    });
  };

  const deleteWidget = (id: string) => {
    let ws = [...widgets];
    ws = ws.filter((item) => item.id !== id);
    setWidgets(ws);
  };

  const collapseWidget = (id: string) => {
    let ws = [...widgets];
    for (let i = 0; i < ws.length; i++) {
      if (id === ws[i].id) {
        ws[i].layout = { ...ws[i].layout, h: 1 };
      }
    }
    setWidgets(ws);
  };

  const expandWidget = (id: string) => {
    let ws = [...widgets];
    for (let i = 0; i < ws.length; i++) {
      if (id === ws[i].id) {
        let widget: any = widgetsHeight.find((item: any) => {
          return item.id === id;
        });
        ws[i].layout = { ...ws[i].layout, h: widget.height };
      }
    }
    setWidgets(ws);
  };

  const handleDragStop = (layout: ReactGridLayout.Layout[]) => {
    let ws = [...widgets];
    for (let i = 0; i < ws.length; i++) {
      let curlayout: any = layout.find((item: any) => {
        return item.i === ws[i].id;
      });
      if (curlayout) {
        ws[i].layout = { ...curlayout };
      }
    }
    setWidgets(ws);
  };

  return (
    <ThemeProvider theme={EhrTheme}>
      <Root>
        <Header widgetTypes={widgetTypes} addWidgetType={addWidgetType} />
        <Body
          widgets={widgets}
          deleteWidget={deleteWidget}
          collapseWidget={collapseWidget}
          expandWidget={expandWidget}
          handleDragStop={handleDragStop}
        />
      </Root>
    </ThemeProvider>
  );
}

function Header({ widgetTypes, addWidgetType }: THeaderPros) {
  async function onSubmit(event: FormEvent<HTMLFormElement>) {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    addWidgetType(String(formData.get("widget-type")));
  }
  return (
    <form
      style={{ display: "flex", justifyContent: "center" }}
      onSubmit={onSubmit}
    >
      <div style={{ padding: 4 }}>
        <select name="widget-type">
          {widgetTypes.map((widgetType: WidgetType) => (
            <option value={widgetType.config} key={widgetType.id}>
              {widgetType.id}
            </option>
          ))}
        </select>
      </div>
      <button>Add</button>
    </form>
  );
}

function Body({
  widgets,
  deleteWidget,
  collapseWidget,
  expandWidget,
  handleDragStop,
}: TBodyPros) {
  console.log("widgets : " + JSON.stringify(widgets));

  const onDragStop = (layout: ReactGridLayout.Layout[]) => {
    handleDragStop(layout);
  };

  return (
    <ResponsiveGridLayout
      cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
      rowHeight={rowHeight}
      isResizable={false}
      onDragStop={onDragStop}
    >
      {widgets.map((widget) => (
        <div key={widget.id} data-grid={widget.layout}>
          <div
            style={{ height: `calc(100% - 5px)` }}
            onMouseDown={(e) => e.stopPropagation()}
          >
            <ExtCmp
              style={{ height: "100%" }}
              widgetId={widget.id}
              conf={widget.conf}
              applyCfg={{ flex: 1 }}
              deleteWidget={deleteWidget}
              collapseWidget={collapseWidget}
              expandWidget={expandWidget}
            />
          </div>
        </div>
      ))}
    </ResponsiveGridLayout>
  );
}
