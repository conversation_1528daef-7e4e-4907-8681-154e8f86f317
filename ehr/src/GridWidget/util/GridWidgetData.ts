export async function getWidgetType() {
  const result = await Cci.util.Hobj.requestRecords({
    hobj: "widgets/getwidgettype",
  });
  let widgets = "";
  if (
    Array.isArray(result) &&
    result.length > 0 &&
    Object.keys(result[0]).length > 0
  ) {
    widgets = result[0].widget_type;
  }
  return widgets;
}

export async function parseConf(config: string) {
  const result = await Cci.util.Hobj.requestRecords({
    hobj: "widgets/parseConf",
    params: {
      config: config,
    },
  });

  let conf = "";
  if (
    Array.isArray(result) &&
    result.length > 0 &&
    Object.keys(result[0]).length > 0
  ) {
    conf = result[0].conf;
  }
  if (conf) {
    return JSON.parse(conf);
  }
  return;
}
