import { styled } from "@mui/material/styles";
import React from "react";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import Grid from "@oe-src/medrec/hook/useSearchRx/component/grid";
import ShowComboDrugsButton from "@oe-src/medrec/hook/useSearchRx/component/ShowComboDrugsButton";
import {
  FORM_FIELD,
  SEARCH_FIELD,
} from "@oe-src/medrec/hook/useSearchRx/config/constant";
import { IControllerResult } from "../types/type";
import AddFreeText from "./AddFreeText";
import Popper from "./Popper";
import SearchField from "./SearchField";

const PopperBody = styled("div")({
  background: "white",
  boxShadow: "0px 4px 4px 0px #0000000D, 0px 15px 15px 0px #0000001A",
  display: "flex",
  flexDirection: "column",
  gap: 8,
  padding: 8,
  width: 633,
});

const ShowComboWrapper = styled("div")({
  display: "flex",
  justifyContent: "flex-end",
});

const MedSearch: React.FC<IControllerResult> = ({
  searchVal,
  includeCombo,
  meds = [],
  disabled,
  loadingMeds,
  onChange,
  onOpen,
  onClose,
  onChangeShowCombo,
  onSelectMed,
  searchRef,
  anchor,
  onAddFreeTextMed,
}) => {
  const deferredloadingMeds = React.useDeferredValue(loadingMeds);
  const gridColumns = React.useMemo(
    () => ({
      [SEARCH_FIELD.IS_FREQUENT]: true,
      [SEARCH_FIELD.NAME]: true,
      [SEARCH_FIELD.THERA_CLASS]: true,
    }),
    []
  );

  let body;
  if (
    loadingMeds ||
    deferredloadingMeds ||
    meds.length > 0 ||
    nullorblank(searchVal)
  )
    body = (
      <Grid
        columns={gridColumns}
        loading={deferredloadingMeds}
        rowData={meds as never[]}
        title=""
        onRowClicked={onSelectMed}
      />
    );
  else
    body = (
      <AddFreeText
        searchVal={ifnullblank(searchVal) as string}
        onClick={onAddFreeTextMed}
      />
    );

  return (
    <>
      <SearchField
        disabled={disabled}
        eRef={searchRef}
        value={ifnullblank(searchVal) as string}
        onChange={onChange}
        onClick={onOpen}
      />
      <Popper anchorEl={anchor} open={Boolean(anchor)} onClose={onClose}>
        <PopperBody>
          <ShowComboWrapper>
            <ShowComboDrugsButton
              checked={includeCombo}
              onChange={onChangeShowCombo}
            />
          </ShowComboWrapper>
          {body}
        </PopperBody>
      </Popper>
    </>
  );
};

export default MedSearch;
