import InputAdornment from "@mui/material/InputAdornment";
import { styled } from "@mui/material/styles";
import React from "react";
import { BaseTextField } from "@cci/mui-components";
import SearchIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Search.svg";

const TextField = styled(BaseTextField)({
  "& .MuiOutlinedInput-input": {
    height: 32,
  },
  "& .MuiOutlinedInput-root": {
    height: 32,
  },
  height: 32,
  width: 210,
});

interface IProps {
  value: string;
  disabled?: boolean;
  onChange: Function;
  onClick: Function;
  eRef: React.Ref<HTMLDivElement>;
}

const MedSearch: React.FC<IProps> = ({
  value,
  disabled,
  onChange,
  onClick,
  eRef,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.persist();
    onChange(e);
  };

  const handleClick = (e: React.MouseEvent) => {
    onClick();
  };

  return (
    <TextField
      disabled={disabled}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <img alt="search" src={SearchIcon} />
          </InputAdornment>
        ),
      }}
      inputProps={{ autoComplete: "off" }}
      placeholder="Add Medication"
      ref={eRef}
      value={value}
      onChange={handleChange}
      onClick={handleClick}
    />
  );
};

export default MedSearch;
