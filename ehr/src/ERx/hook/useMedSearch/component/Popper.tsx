import ClickAwayListener from "@mui/material/ClickAwayListener";
import MuiPopper from "@mui/material/Popper";
import React from "react";

interface IProps {
  open: boolean;
  onClose: (event: MouseEvent | TouchEvent) => void;
  anchorEl: HTMLDivElement | null;
  children: React.ReactNode;
}

const Popper: React.FC<IProps> = ({ open, onClose, ...other }) => {
  return (
    <ClickAwayListener onClickAway={onClose}>
      <MuiPopper
        open={open}
        placement="bottom-start"
        {...other}
        sx={{ position: "relative", zIndex: 1200 }}
      />
    </ClickAwayListener>
  );
};

export default Popper;
