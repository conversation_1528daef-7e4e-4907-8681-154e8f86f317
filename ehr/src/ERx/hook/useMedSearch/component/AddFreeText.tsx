import { styled } from "@mui/material/styles";
import React from "react";

const Wrapper = styled("div")({
  display: "flex",
  gap: 10,
  justifyContent: "center",
  padding: "8px 16px 16px 16px",
  width: "100%",
});

const Text = styled("span")({
  color: "#00000066",
  font: "normal 500 22px Raleway",
});

const Button = styled("button")({
  background: "none",
  border: "none",
  color: "#2B7DE1",
  cursor: "pointer",
  font: "normal 500 22px Raleway",
  padding: 0,
  textWrap: "nowrap",
});

interface IProps {
  searchVal: string;
  onClick: () => void;
}

const AddFreeText: React.FC<IProps> = ({ searchVal, onClick }) => {
  const handleClick = () => {
    onClick();
  };

  return (
    <Wrapper>
      <Text>{`There are no results for “${searchVal}”.`}</Text>
      <Button onClick={handleClick}>Add Free Text Medication</Button>
    </Wrapper>
  );
};

export default AddFreeText;
