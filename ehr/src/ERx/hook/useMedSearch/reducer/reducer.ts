import update from "immutability-helper";
import { IMed, IState } from "../types/type";

export const ACTIONS: Record<string, string> = {
  FIND_MEDS: "SET MEDS",
  TOGGLE_SHOW_COMBO: "Toggle Show Combo Option",
  UPDATE_LOADING: "Set Loading",
  UPDATE_SEARCH_STRING: "On change search value",
};

export const initState: IState = {
  includeCombo: true,
  loadingMeds: false,
  meds: [],
  searchVal: "",
};

interface IAction {
  type: string;
  payload?: IMed[];
  searchVal?: string;
  loading?: boolean;
}

export const reducer = (state: IState, action: IAction): IState => {
  switch (action.type) {
    case ACTIONS.UPDATE_SEARCH_STRING: {
      return update(state, { searchVal: { $set: action.searchVal ?? "" } });
    }
    case ACTIONS.TOGGLE_SHOW_COMBO: {
      return update(state, { includeCombo: { $apply: (o: boolean) => !o } });
    }
    case ACTIONS.UPDATE_LOADING: {
      return update(state, { loadingMeds: { $set: Boolean(action.loading) } });
    }
    case ACTIONS.FIND_MEDS: {
      return update(state, { meds: { $set: action.payload ?? [] } });
    }
    default:
      return state;
  }
};
