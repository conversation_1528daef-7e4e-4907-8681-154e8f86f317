import omit from "lodash/omit";
import React from "react";
import { nullorblank } from "@cci-monorepo/common";
import findMeds from "@oe-src/medrec/hook/useSearchRx/api/findMeds";
import { getFrequentERx } from "@oe-src/medrec/hook/useSearchRx/api/frequentERx";
import { ACTIONS, initState, reducer } from "../reducer/reducer";
import { IControllerResult, IMed } from "../types/type";

interface IProps {
  disabled?: boolean;
  onMedSelected: ({ data }: { data: any }) => void;
}

const useController = ({
  onMedSelected,
  disabled,
}: IProps): IControllerResult => {
  const [state, dispatch] = React.useReducer(reducer, initState);
  const [anchor, setAnchor] = React.useState<HTMLDivElement | null>(null);
  const searchRef = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    let didCancel = false;

    const updateMeds = (meds: IMed[] | null) => {
      dispatch({ payload: meds ?? [], type: ACTIONS.FIND_MEDS });
      dispatch({ loading: false, type: ACTIONS.UPDATE_LOADING });
    };

    const exec = async () => {
      dispatch({ loading: true, type: ACTIONS.UPDATE_LOADING });
      try {
        if (nullorblank(state.searchVal)) {
          const response = (await getFrequentERx()) as IMed[];
          if (!didCancel) updateMeds(response);
        } else {
          const response = (await findMeds({
            combo: state.includeCombo ? 1 : 0,
            lookup: state.searchVal,
          })) as IMed[];
          if (!didCancel) updateMeds(response);
        }
      } catch (e) {
        if (!didCancel) {
          console.error(e); // eslint-disable-line no-console
          updateMeds([]);
        }
      }
    };

    exec();
    return () => {
      didCancel = true;
    };
  }, [state.searchVal, state.includeCombo]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAnchor(searchRef.current);
    dispatch({ searchVal: e.target.value, type: ACTIONS.UPDATE_SEARCH_STRING });
  };

  const onChangeShowCombo = () => {
    dispatch({ type: ACTIONS.TOGGLE_SHOW_COMBO });
  };

  const onOpen = () => {
    setAnchor(searchRef.current);
  };

  const onClose = (e?: MouseEvent | TouchEvent) => {
    if (!searchRef.current?.contains(e?.target as Node)) {
      setAnchor(null);
    }
  };

  const onSelectMed = ({ data }: any) => {
    onClose();
    onMedSelected({
      data: omit(data, ["isFrequent", "staffid"]),
    });
  };

  const onAddFreeTextMed = () => {
    onMedSelected({
      data: { order: { isftxt: "1", name: state.searchVal } },
    });
    onClose();
  };

  return {
    ...state,
    anchor,
    disabled,
    onAddFreeTextMed,
    onChange,
    onChangeShowCombo,
    onClose,
    onOpen,
    onSelectMed,
    searchRef,
  };
};

export default useController;
