import React from "react";

export interface IMed {
  id: number;
  name: string;
  strength: string;
  form: string;
  route: string;
  frequency: string;
  duration: string;
  quantity: string;
  sig: string;
  isCombo: boolean;
  isFreeText: boolean;
}

export interface IState {
  searchVal: string;
  includeCombo: boolean;
  meds: IMed[];
  loadingMeds: boolean;
}

export interface IControllerResult extends Partial<IState> {
  disabled?: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onOpen: (event: MouseEvent | TouchEvent) => void;
  onClose: (event: MouseEvent | TouchEvent) => void;
  onChangeShowCombo: () => void;
  onSelectMed: (...args: any[]) => any;
  anchor: HTMLDivElement | null;
  searchRef: React.Ref<HTMLDivElement>;
  onAddFreeTextMed: () => void;
  meds: IMed[];
  searchVal: string;
}
