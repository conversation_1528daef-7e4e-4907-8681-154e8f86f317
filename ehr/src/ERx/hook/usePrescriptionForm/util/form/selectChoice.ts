import { nullorblank } from "@cci-monorepo/common";
import {
  FIELD,
  FILTER_FIELDS_ORDER_CHOICES,
  SELECTION_FIELDS_ORDER_CHOICES,
} from "../../config/form";
import { IFormData, IOrderChoice } from "../../type/form";

export const selectChoice = (
  data: IFormData,
  selectionOrderChoices: IOrderChoice[],
  error: Record<string, { severity?: string }>
) => {
  let choice: IOrderChoice | undefined | null = null;
  const filledAllReqForChoice = SELECTION_FIELDS_ORDER_CHOICES.every(
    (field) => !error[field]
  );
  if (filledAllReqForChoice) {
    choice = selectionOrderChoices.find((c) =>
      SELECTION_FIELDS_ORDER_CHOICES.every((field) => data[field] === c[field])
    );
    if (choice == null) {
      choice = selectionOrderChoices.find((c) =>
        FILTER_FIELDS_ORDER_CHOICES.filter(
          (field) => field !== FIELD.DOSE_VALUE
        ).every((field) => data[field] === c[field])
      );
    }
  }
  if (choice == null) return choice;
  else
    return Object.keys(choice).reduce((o, n) => {
      if (!nullorblank((choice ?? {})[n])) o[n] = (choice ?? {})[n];
      return o;
    }, {} as IOrderChoice);
};
