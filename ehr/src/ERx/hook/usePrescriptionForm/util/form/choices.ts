import isEmpty from "lodash/isEmpty";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import { FIELD, SELECTION_FIELDS_ORDER_CHOICES } from "../../config/form";
import {
  COMPLIANCE_OPTIONS,
  SOURCE_OPTIONS,
  STATUS_OPTIONS,
} from "../../config/option";
import { IForm, IFormData, IOrderChoice } from "../../type/form";

export const getSelectionChoices = (
  data: IFormData = {},
  orderChoices: IOrderChoice[] = []
) => {
  let choices = (orderChoices ?? []).filter((choice) =>
    SELECTION_FIELDS_ORDER_CHOICES.every(
      (field) =>
        nullorblank(data[field]) ||
        nullorblank(choice[field]) ||
        ifnullblank(data[field]) === ifnullblank(choice[field])
    )
  );
  if (isEmpty(choices)) {
    const excludeDoseValue = [...SELECTION_FIELDS_ORDER_CHOICES].filter(
      (field) => field !== FIELD.DOSE_VALUE
    );
    choices = (orderChoices ?? []).filter((choice) =>
      excludeDoseValue.every(
        (field) =>
          nullorblank(data[field]) ||
          nullorblank(choice[field]) ||
          ifnullblank(data[field]) === ifnullblank(choice[field])
      )
    );
  }
  return choices;
};

export const getDoseValueOptions = (
  selectionOrderChoices: IOrderChoice[] = []
) => {
  const valUnitMap = (selectionOrderChoices ?? []).reduce((o, n) => {
    if (nullorblank(n[FIELD.DOSE_VALUE]) || nullorblank(n[FIELD.DOSE_UNIT]))
      return o;
    o[`${n[FIELD.DOSE_VALUE]}::${n[FIELD.DOSE_UNIT]}`] = {
      unit: n[FIELD.DOSE_UNIT],
      value: n[FIELD.DOSE_VALUE],
    };
    return o;
  }, {});
  return Object.values(valUnitMap);
};

export const getDoseUnitOptions = (
  selectionOrderChoices: IOrderChoice[] = []
) => {
  const unitsMap = (selectionOrderChoices ?? []).reduce((o, n) => {
    const unitText = n[FIELD.DOSE_UNIT];
    if (nullorblank(unitText)) return o;
    o[n[FIELD.DOSE_UNIT]] = {
      display: unitText,
      value: unitText,
    };
    return o;
  }, {});
  return Object.values(unitsMap);
};

export const getRouteOptions = (selectionOrderChoices: IOrderChoice[] = []) => {
  const routeMap = (selectionOrderChoices ?? []).reduce((o, n) => {
    const routeText = n[FIELD.ROUTE];
    if (nullorblank(routeText)) return o;
    o[n[FIELD.ROUTE]] = {
      display: routeText,
      value: routeText,
    };
    return o;
  }, {});
  return Object.values(routeMap);
};

export const getFrequencyOptions = (
  selectionOrderChoices: IOrderChoice[] = []
) => {
  const freqMap = (selectionOrderChoices ?? []).reduce((o, n) => {
    const freqText = n[FIELD.FREQUENCY];
    if (nullorblank(freqText)) return o;
    o[n[FIELD.FREQUENCY]] = {
      display: freqText,
      value: freqText,
    };
    return o;
  }, {});
  return Object.values(freqMap);
};

export const getDoseageFormOptions = (
  selectionOrderChoices: IOrderChoice[] = []
) => {
  const formMap = (selectionOrderChoices ?? []).reduce((o, n) => {
    const formText = n[FIELD.DOSAGE_FORM];
    if (nullorblank(formText)) return o;
    o[n[FIELD.DOSAGE_FORM]] = {
      display: formText,
      value: formText,
    };
    return o;
  }, {});
  return Object.values(formMap);
};

export const getFieldChoices = (state: IForm) => {
  const selectionOrderChoices = state.others.selectionOrderChoices ?? [];
  const choices = state.others.choices ?? {};

  const doseValueOptions = getDoseValueOptions(selectionOrderChoices);

  const doseUnitOptions =
    getDoseUnitOptions(selectionOrderChoices).length !== 0
      ? getDoseUnitOptions(selectionOrderChoices)
      : choices[FIELD.DOSE_UNIT] ?? [];

  const routeOptions =
    getRouteOptions(selectionOrderChoices).length !== 0
      ? getRouteOptions(selectionOrderChoices)
      : choices[FIELD.ROUTE] ?? [];

  const frequencyOptions =
    getFrequencyOptions(selectionOrderChoices).length !== 0
      ? getFrequencyOptions(selectionOrderChoices)
      : choices[FIELD.FREQUENCY] ?? [];

  const dosageFormOptions = getDoseageFormOptions(selectionOrderChoices);
  const result = {
    option: {
      [FIELD.STATUS]: STATUS_OPTIONS,
      [FIELD.DOSE_VALUE]: doseValueOptions,
      [FIELD.DOSE_UNIT]: doseUnitOptions,
      [FIELD.ROUTE]: routeOptions,
      [FIELD.FREQUENCY]: frequencyOptions,
      [FIELD.COMPLIANCE]: COMPLIANCE_OPTIONS,
      [FIELD.DOSAGE_FORM]: dosageFormOptions,
      [FIELD.DURATION_UNIT]: choices[FIELD.DURATION_UNIT] ?? [],
      [FIELD.SOURCE]: SOURCE_OPTIONS,
    },
    suggestion: {
      [FIELD.DOSE_VALUE]: doseValueOptions.slice(0, 3),
      [FIELD.ROUTE]: routeOptions.slice(0, 3),
      [FIELD.FREQUENCY]: frequencyOptions.slice(0, 3),
    },
  };
  return result;
};

export default getSelectionChoices;
