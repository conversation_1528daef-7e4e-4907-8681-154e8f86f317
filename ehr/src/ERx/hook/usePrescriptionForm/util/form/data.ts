import { DATA_IN_ORDER_CHOICES, DATA_WITH_OPTIONS } from "../../config/form";
import { IOrderChoice } from "../../type/form";

export const setupDataFromOptions = (
  rawData: Record<string, any>,
  options: Record<string, any[]>
) => {
  const data = { ...rawData };

  DATA_WITH_OPTIONS.forEach((field) => {
    data[field] =
      options[field].findIndex((c) => c.value === data[field]) > -1
        ? data[field]
        : null;
  });

  return data;
};

export const setupDataFromOrderChoices = (
  rawData: Record<string, any>,
  orderChoices: IOrderChoice[] = []
) => {
  const data = { ...rawData };

  DATA_IN_ORDER_CHOICES.forEach((field) => {
    data[field] =
      orderChoices.findIndex((c) => c[field] === data[field]) > -1
        ? data[field]
        : null;
  });

  return data;
};
