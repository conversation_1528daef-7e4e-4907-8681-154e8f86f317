import cloneDeep from "lodash/cloneDeep";
import omit from "lodash/omit";
import { nullorblank } from "@cci-monorepo/common";
import { choices as getChoices } from "../../api/choices";
import { orderchoices as orderChoicesApi } from "../../api/orderchoices";
import { DISABLED_FIELDS as CCD_DISABLED_FIELDS } from "../../config/ccd";
import {
  DISABLED_FIELDS,
  EXCLUDE_FROM_DDAASIG,
  FIELD,
  FORM_TYPE,
} from "../../config/form";
import { COMPLIANCE_OPTION, STATUS_OPTION } from "../../config/option";
import { DISABLED_FIELDS as SS_DISABLED_FIELDS } from "../../config/surescript";
import { initState } from "../../reducer/form";
import { IOrderChoice } from "../../type/form";
import { formatObjectToString } from "../util";
import { getFieldChoices, getSelectionChoices } from "./choices";
import { setupDataFromOptions, setupDataFromOrderChoices } from "./data";
import { ddaasig } from "./ddaasig";
import { selectChoice } from "./selectChoice";
import { getError, isValid } from "./validation";

interface IProps {
  data: Record<string, any>;
  isInModifyMode: boolean;
}

export const setup = async ({ data, isInModifyMode }: IProps) => {
  let result = cloneDeep(initState);
  result.data = formatObjectToString(data);

  try {
    const ordMedId = data[FIELD.ORD_MED_ID];

    if (!nullorblank(ordMedId)) {
      let orderChoices = await orderChoicesApi({ ord_med_id: ordMedId });
      orderChoices = (orderChoices ?? []).map((choice: IOrderChoice) =>
        formatObjectToString(choice)
      );
      result.others.orderChoices = orderChoices;
      result.data = setupDataFromOrderChoices(
        result.data,
        result.others.orderChoices
      );
      const selectionOrderChoices = getSelectionChoices(
        result.data,
        result.others.orderChoices
      );
      result.others.selectionOrderChoices = selectionOrderChoices;
    }

    let choices = {};
    try {
      choices = await getChoices();
      result.others.choices = choices;
    } catch (e) {
      console.error(e);
    }

    result = {
      ...result,
      ...getFieldChoices(result),
    };
    if (!nullorblank(ordMedId)) {
      result.data = setupDataFromOptions(result.data, result.option);
    }

    // FREE-TEXT DOSE_VALUE
    if (!nullorblank(data[FIELD.DOSE_VALUE]))
      result.data[FIELD.DOSE_VALUE] = data[FIELD.DOSE_VALUE];
    if (nullorblank(result.data[FIELD.NAME]))
      result.data[FIELD.NAME] = result.data[FIELD.BASE_NAME];

    if (isInModifyMode) {
    } else {
      result.data = {
        ...result.data,
        [FIELD.STATUS]: STATUS_OPTION.ACTIVE,
        [FIELD.COMPLIANCE]: COMPLIANCE_OPTION.TAKING_AS_PRESCRIBED,
      };
      if (result.data.isftxt === "1") {
        result.data[FIELD.DOSE_UNIT] = "mg";
      }
    }

    result.error = getError(result.data, result.needValidation);

    const choice = selectChoice(
      result.data,
      result.others.selectionOrderChoices,
      result.error
    );
    if (choice != null) result.data = { ...result.data, ...choice };

    if (
      isValid(result.error) &&
      !nullorblank(result.data[FIELD.FULL_ORDER_ID]) &&
      !nullorblank(result.data[FIELD.ORD_MED_ID])
    ) {
      const response = await ddaasig({
        orderdata: result.data,
      });
      result.data = {
        ...result.data,
        ...omit(response.ddaasig, EXCLUDE_FROM_DDAASIG),
      };
    }

    switch (result.data[FIELD.SOURCE]) {
      case FORM_TYPE.SURESCRIPTS:
        result.disabled = SS_DISABLED_FIELDS;
        break;
      case FORM_TYPE.CCD:
        result.disabled = CCD_DISABLED_FIELDS;
        break;
      default:
        result.disabled = DISABLED_FIELDS;
        break;
    }

    if (result.data.isftxt !== "1") {
      result.disabled = {
        ...result.disabled,
        [FIELD.DOSAGE_FORM]: true,
        [FIELD.STRENGTH]: true,
      };
    }
  } catch (error) {
    console.error(error); // eslint-disable-line no-console
  }
  return result;
};
