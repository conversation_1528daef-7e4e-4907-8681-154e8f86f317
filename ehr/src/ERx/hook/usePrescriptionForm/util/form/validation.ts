import isEmpty from "lodash/isEmpty";
import isFunction from "lodash/isFunction";
import { nullorblank } from "@cci-monorepo/common";
import { FIELD } from "../../config/form";
import { IFormData } from "../../type/form";

export const isValidStartStopTime = ({
  starttime,
  stoptime,
}: {
  starttime: string | number;
  stoptime: string | number;
}) => {
  if (nullorblank(starttime) || nullorblank(stoptime)) return true;
  return Number(starttime) < Number(stoptime);
};

export const validationConfig = {
  [FIELD.NAME]: (data: IFormData) => !nullorblank(data[FIELD.NAME]),
  [FIELD.STATUS]: (data: IFormData) => !nullorblank(data[FIELD.STATUS]),
  // [FIELD.DOSE_VALUE]: (data: IFormData) => !nullorblank(data[FIELD.DOSE_VALUE]),
  // [FIELD.DOSE_UNIT]: (data: IFormData) => !nullorblank(data[FIELD.DOSE_UNIT]),
  // [FIELD.ADMIN_AMOUNT]: (data: IFormData) =>
  //   !nullorblank(data[FIELD.ADMIN_AMOUNT]),
  // [FIELD.ADMIN_UNIT]: (data: IFormData) => !nullorblank(data[FIELD.ADMIN_UNIT]),
  // [FIELD.ROUTE]: (data: IFormData) => !nullorblank(data[FIELD.ROUTE]),
  // [FIELD.FREQUENCY]: (data: IFormData) => !nullorblank(data[FIELD.FREQUENCY]),
  // [FIELD.DURATION_VALUE]: (data: IFormData) =>
  //   !(
  //     nullorblank(data[FIELD.DURATION_VALUE]) &&
  //     !nullorblank(data[FIELD.DURATION_UNIT])
  //   ),
  // [FIELD.DURATION_UNIT]: (data: IFormData) =>
  //   !(
  //     nullorblank(data[FIELD.DURATION_UNIT]) &&
  //     !nullorblank(data[FIELD.DURATION_VALUE])
  //   ),
  // [FIELD.PRESCRIPTION_MODE]: (data: IFormData) =>
  //   !nullorblank(data[FIELD.PRESCRIPTION_MODE]),
  // [FIELD.PREFERRED_PHARMACY]: (data: IFormData) =>
  //   !(
  //     isEmpty(data[FIELD.PREFERRED_PHARMACY]) &&
  //     !data[FIELD.NO_PREFERRED_PHARMACY]
  //   ),
};

export const getError = (
  data: IFormData,
  needValidation: Record<string, boolean>
) => {
  const a: Record<string, any> = {};
  const error = Object.entries({
    ...needValidation,
    // [FIELD.DURATION_VALUE]: true,
    // [FIELD.DURATION_UNIT]: true,
  }).reduce((o, [key, val]) => {
    if (
      val &&
      isFunction(validationConfig[key]) &&
      !validationConfig[key](data)
    ) {
      o[key] = { severity: "Exception" };
    } else {
      o[key] = null;
    }
    return o;
  }, a);
  return error;
};

export const isValid = (error: Record<string, { severity?: string }>) => {
  return !isEmpty(Object.values(error).filter((e) => Boolean(e)));
};
