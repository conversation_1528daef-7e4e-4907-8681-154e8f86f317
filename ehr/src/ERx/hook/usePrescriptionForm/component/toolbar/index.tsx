import { styled } from "@mui/material/styles";
import React from "react";
import { nullorblank } from "@cci-monorepo/common";
import Button from "../../../../component/form/Button";
import CcdIcon from "../../asset/CCD.svg";
import CheckIcon from "../../asset/icn_button/Check.svg";
import HomeMedIcon from "../../asset/Icn_HomeMedType.svg";
import SureScriptIcon from "../../asset/SureScript.svg";
import { FORM_TYPE } from "../../config/form";
import { IControllerResult } from "../../type/main";
import DescribeButton from "./DescribeButton";
import OrderHistoryButton from "./OrderHistoryButton";

const Wrapper = styled("div")({
  alignItems: "center",
  background: "#F2F2F2",
  boxShadow: "0px 4px 6px 0px #00000026",
  display: "flex",
  flexWrap: "nowrap",
  gap: 16,
  height: 62,
  overflow: "hidden",
  padding: "0 16px",
});

const Name = styled("div")({
  color: "#11181F",
  display: "inline-block",
  flex: 1,
  font: "normal 700 20px Helvetica",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
});

const RightStack = styled("div")({
  alignItems: "center",
  display: "flex",
  gap: 10,
});

const Toolbar: React.FC<IControllerResult> = ({
  readOnly,
  form,
  toolbarDisabled,
  onClose,
  onSave,
  onDescribeMed,
  onOrderHistory,
}) => {
  let iconSrc = HomeMedIcon;
  if (form?.data.source === FORM_TYPE.SURESCRIPTS) {
    iconSrc = SureScriptIcon;
  } else if (form?.data.source === FORM_TYPE.CCD) {
    iconSrc = CcdIcon;
  }

  return (
    <Wrapper>
      <img alt="icon" src={iconSrc} />
      <Name>{form?.data.name}</Name>
      <RightStack>
        <DescribeButton
          disabled={nullorblank(form?.data.nit)}
          onClick={() => {
            if (form?.data != null) {
              onDescribeMed(form.data);
            }
          }}
        />
        <OrderHistoryButton
          disabled={nullorblank(form?.data.nit)}
          onClick={() => {
            if (form?.data != null) {
              onOrderHistory(form.data);
            }
          }}
        />
        <Button
          color="secondary"
          disabled={toolbarDisabled.cancel}
          onClick={onClose}
        >
          Cancel
        </Button>
        {!readOnly && (
          <Button
            disabled={toolbarDisabled.save}
            startIcon={<img alt="Check" src={CheckIcon} />}
            onClick={onSave}
          >
            Save
          </Button>
        )}
      </RightStack>
    </Wrapper>
  );
};

export default Toolbar;
