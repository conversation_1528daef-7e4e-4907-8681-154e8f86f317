import { styled } from "@mui/material/styles";
import React from "react";
import Icon from "@cci-monorepo/oe-shared/src/assets/Icon_EditHistory.svg";
import DisabledIcon from "@cci-monorepo/oe-shared/src/assets/Icon_EditHistory_Disabled.svg";
import IconButton, {
  BUTTON_TYPE,
  IIconButtonProps,
} from "../../../../component/form/IconButton";

const StyledIconButton = styled(IconButton)({
  "& img": {
    height: 20,
    width: 20,
  },
});

type IProps = Omit<IIconButtonProps, "tooltip" | "buttonType">;

const OrderHistoryButton: React.FC<IProps> = ({ disabled, ...other }) => (
  <StyledIconButton
    buttonType={BUTTON_TYPE.OUTLINED}
    disabled={disabled}
    tooltip="Order History"
    {...other}
  >
    <img alt="order history icon" src={disabled ? DisabledIcon : Icon} />
  </StyledIconButton>
);

export default OrderHistoryButton;
