import { styled } from "@mui/material/styles";
import React from "react";
import IconInfo from "@cci-monorepo/oe-shared/src/assets/Icon_Info.svg";
import DisabledIconInfo from "@cci-monorepo/oe-shared/src/assets/Icon_Info_Disabled.svg";
import IconButton, {
  BUTTON_TYPE,
  IIconButtonProps,
} from "../../../../component/form/IconButton";

const StyledIconButton = styled(IconButton)({
  "& img": {
    height: 20,
    width: 20,
  },
});

type IProps = Omit<IIconButtonProps, "tooltip" | "buttonType">;

const DescribeButton: React.FC<IProps> = ({ disabled, ...other }) => (
  <StyledIconButton
    buttonType={BUTTON_TYPE.OUTLINED}
    disabled={disabled}
    tooltip="Describe Item"
    {...other}
  >
    <img alt="describe icon" src={disabled ? DisabledIconInfo : IconInfo} />
  </StyledIconButton>
);

export default DescribeButton;
