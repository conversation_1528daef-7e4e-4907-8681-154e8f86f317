import { styled } from "@mui/material/styles";
import React from "react";
import LoadingBackdrop from "../../../component/LoadingBackdrop";
import DescribeMed from "../../useDescribeMed/component";
import { IControllerResult } from "../type/main";
import Form from "./form";
import Toolbar from "./toolbar";

const Wrapper = styled("div")({
  display: "flex",
  flexDirection: "column",
  gap: 10,
  height: "100%",
  overflow: "hidden",
});

const App: React.FC<IControllerResult> = (props) => {
  return (
    <>
      <Wrapper>
        {props.setupLoading && <LoadingBackdrop open />}
        {!props.setupLoading && (
          <>
            <Toolbar {...props} />
            <Form {...props} />
          </>
        )}
      </Wrapper>
      {props.describeMedProps.open && (
        <DescribeMed {...props.describeMedProps} />
      )}
    </>
  );
};

export default App;
