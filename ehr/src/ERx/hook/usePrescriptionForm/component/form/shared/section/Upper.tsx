import { styled } from "@mui/material/styles";
import React from "react";
import { IFormProps } from "../../../../type/main";
import Compliance from "../formElement/Compliance";
import Dose from "../formElement/Dose";
import Frequency from "../formElement/Frequency";
import Indication from "../formElement/Indication";
import LastTaken from "../formElement/LastTaken";
import Name from "../formElement/Name";
import Route from "../formElement/Route";
import StartTime from "../formElement/StartTime";
import Status from "../formElement/Status";

const Wrapper = styled("div")({
  background: "#FFFFFF",
  display: "flex",
  flexDirection: "column",
  gap: 8,
});

const Upper: React.FC<IFormProps> = ({
  nameProps,
  statusProps,
  doseProps,
  routeProps,
  frequencyProps,
  indicationProps,
  starttimeProps,
  lastTakenProps,
  complianceProps,
}) => {
  return (
    <Wrapper>
      <Name {...nameProps} />
      <Status {...statusProps} />
      <Dose {...doseProps} />
      <Route {...routeProps} />
      <Frequency {...frequencyProps} />
      <Indication {...indicationProps} />
      <StartTime {...starttimeProps} />
      <LastTaken {...lastTakenProps} />
      <Compliance {...complianceProps} />
    </Wrapper>
  );
};

export default Upper;
