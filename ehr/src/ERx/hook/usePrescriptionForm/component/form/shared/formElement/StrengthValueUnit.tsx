import { styled } from "@mui/material/styles";
import React from "react";
import TextField from "@cci-monorepo/ERx/component/form/TextField";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP } from "../../../../config/style";
import { IFieldProps } from "../../../../type/form";
import Label from "../others/Label";

const Wrapper = styled("div")({
  "& .MuiTextField-root": {
    flex: 1,
  },
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const buildValueUnit = (value: string, unit: string) => {
  const arr = [];
  if (!nullorblank(value)) arr.push(value);
  if (!nullorblank(unit)) arr.push(unit);
  return arr.join(" ");
};

const Strength: React.FC<IFieldProps> = ({
  value,
  unit,
  changeFields,
  disabled,
  required,
  error,
  onChange,
}) => {
  const localValue = ifnullblank(value);
  const localUnit = ifnullblank(unit);
  const localValueUnit = React.useMemo(
    () => buildValueUnit(value, unit),
    [value, unit]
  );

  const [memoStr, setMemoStr] = React.useState<string>(localValueUnit);

  React.useEffect(() => {
    setMemoStr(buildValueUnit(localValue, localUnit));
  }, [localValue, localUnit]);

  const onValueChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setMemoStr(e.target.value);
    const strArr = ifnullblank(e.target.value).split(/(\s+)/);
    const newValue = ifnullblank(strArr[0]);
    const newUnit = ifnullblank(strArr[2]);
    onChange({
      [changeFields?.value ?? ""]: newValue,
      [changeFields?.unit ?? ""]: newUnit,
    });
  };

  return (
    <Wrapper>
      <Label required={required} value="Strength" />
      <TextField
        disabled={disabled}
        error={Boolean(error?.value)}
        value={memoStr}
        onChange={onValueChange}
      />
    </Wrapper>
  );
};

export default Strength;
