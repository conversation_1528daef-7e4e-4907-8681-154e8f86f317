import { styled } from "@mui/material/styles";
import React, { ReactNode } from "react";
import Chip from "@cci-monorepo/ERx/component/form/Chip";

const Wrapper = styled("div")({
  display: "flex",
  flexWrap: "wrap",
  gap: 8,
  overflow: "hidden",
});

interface IDefaultOption {
  display?: string;
  value?: string;
}

interface IProps<T> {
  dataArray?: T[];
  disabled?: boolean;
  getOptionLabel: (option: T | undefined) => string;
  isOptionEqualToValue?: (option: T, value: T | null) => boolean;
  onClick?: (option: T | null) => void;
  value: T | null;
}

const ChipGroup = <T extends IDefaultOption>({
  value,
  dataArray = [],
  getOptionLabel,
  isOptionEqualToValue = (option: T, value: T | null) => option === value,
  disabled = false,
  onClick = () => {},
}: IProps<T> & { children?: ReactNode }) => {
  const handleClick = (option: T) => {
    if (isOptionEqualToValue(option, value)) onClick(null);
    else onClick(option);
  };

  return (
    <Wrapper>
      {React.Children.toArray(
        dataArray.map((rec) => (
          <Chip
            data={isOptionEqualToValue(rec, value)}
            disabled={disabled}
            label={getOptionLabel(rec)}
            onDataChange={() => handleClick(rec)}
          />
        ))
      )}
    </Wrapper>
  );
};

export default ChipGroup;
