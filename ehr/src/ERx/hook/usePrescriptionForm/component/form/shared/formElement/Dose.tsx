import { styled } from "@mui/material/styles";
import React from "react";
import Combobox from "@cci-monorepo/ERx/component/form/Combobox";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP, LEFT_FIELD_WIDTH } from "../../../../config/style";
import { IFieldProps, IOption } from "../../../../type/form";
import ChipStack from "../others/ChipStack";
import Label from "../others/Label";

const Wrapper = styled("div")({
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const LeftFieldWrapper = styled("div")({
  display: "flex",
  gap: 24,
  width: LEFT_FIELD_WIDTH,
});

const StyledCombobox = styled(Combobox)({
  "& .MuiAutocomplete-endAdornment": {
    display: "none !important",
  },
  "& .MuiAutocomplete-inputRoot": {
    padding: "0px !important",
  },
});

const Unit = styled("span")({
  font: "normal 400 14px/32px Roboto",
});

const RightFieldWrapper = styled("div")({
  flex: 1,
  overflow: "hidden",
  paddingTop: 4,
});

const Dose: React.FC<IFieldProps> = ({
  value,
  unit,
  valueOptions,
  unitOptions,
  suggestions,
  changeFields,
  disabled,
  required,
  error,
  onChange,
  isFreeTextMedication,
}) => {
  const onInputValueChange = (
    e: InputEvent,
    nValue: string,
    reason: string
  ) => {
    if (reason !== "reset")
      onChange({
        [changeFields?.value ?? ""]: ifnullblank(nValue),
        [changeFields?.unit ?? ""]: unit,
      });
  };

  const onValueChange = (e: InputEvent, nValue: IOption, reason: string) => {
    onChange({
      [changeFields?.value ?? ""]: ifnullblank(nValue.value),
      [changeFields?.unit ?? ""]:
        reason === "clear" ? unit : ifnullblank(nValue.unit),
    });
  };

  const handleChipClick = (option: IOption | null) => {
    if (option == null)
      onChange({
        [changeFields?.value ?? ""]: "",
        [changeFields?.unit ?? ""]: unit,
      });
    else
      onChange({
        [changeFields?.value ?? ""]: option.value,
        [changeFields?.unit ?? ""]: option.unit,
      });
  };

  const handleDoseUnitChange = (e: InputEvent, nValue: IOption) => {
    onChange({ [changeFields?.unit ?? ""]: ifnullblank(nValue?.value) });
  };

  return (
    <Wrapper>
      <Label required={required} value="Dose" />
      <LeftFieldWrapper>
        <StyledCombobox
          clearIcon={null}
          disabled={disabled}
          error={Boolean(error?.value)}
          freeSolo
          getOptionLabel={(option: IOption) => option.value}
          inputValue={ifnullblank(value)}
          isOptionEqualToValue={(option: IOption, nValue: IOption) =>
            option.value === nValue.value && option.unit === nValue.unit
          }
          options={valueOptions}
          popupIcon={null}
          renderOption={(props: any, option: IOption) => (
            <li {...props}>
              {option.value} {option.unit}
            </li>
          )}
          value={nullorblank(value) ? null : { unit, value }}
          width={70}
          onChange={onValueChange}
          onInputChange={onInputValueChange}
        />
        <Combobox
          disabled={disabled}
          getOptionLabel={(option: IOption) => ifnullblank(option.value)}
          isOptionEqualToValue={(option: IOption, nValue: IOption) =>
            option.value === nValue.value
          }
          options={unitOptions ?? []}
          value={{ value: unit }}
          width={118}
          onChange={handleDoseUnitChange}
        />
      </LeftFieldWrapper>
      <RightFieldWrapper>
        <ChipStack<IOption>
          dataArray={suggestions}
          disabled={disabled}
          getOptionLabel={(rec) =>
            `${ifnullblank(rec?.value)} ${ifnullblank(rec?.unit)}`
          }
          isOptionEqualToValue={(rec, val) =>
            rec.value === val?.value && rec.unit === val.unit
          }
          value={{ unit, value }}
          onClick={handleChipClick}
        />
      </RightFieldWrapper>
    </Wrapper>
  );
};

export default Dose;
