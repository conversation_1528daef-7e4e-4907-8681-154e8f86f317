import { styled } from "@mui/material/styles";
import React from "react";
import { LABEL_WIDTH } from "../../../../config/style";

const Wrapper = styled("div")({
  display: "flex",
  font: "normal 600 14px Helvetica",
  width: LABEL_WIDTH,
});

const Text = styled("span")({
  display: "inline-block",
  overflow: "hidden",
  textOverflow: "ellipsis",
});

const RequiredSign = styled((props) => <span {...props}>*</span>)({
  color: "#CF4C35",
  font: "normal 700 20px Roboto",
});

interface IProps {
  value: string;
  required?: boolean;
  lineHeight?: string;
}

const Label: React.FC<IProps> = ({
  value = "",
  required = false,
  lineHeight = "32px",
  ...other
}) => (
  <Wrapper {...other}>
    <Text style={{ lineHeight }}>
      {value}
      {required && <RequiredSign />}
    </Text>
  </Wrapper>
);

export default Label;
