import { styled } from "@mui/material/styles";
import React from "react";
import { getTimeStr } from "@cci-monorepo/ERx/util/util";
import { ifnullblank } from "@cci-monorepo/common";
import { IFormProps } from "../../../../../type/main";

const Wrapper = styled("div")({
  display: "flex",
  gap: 16,
  padding: "8px 0px",
  width: "100%",
});

const Column = styled("div")({
  display: "flex",
  flex: 1,
  flexDirection: "column",
  gap: 16,
});

const RowWrapper = styled("div")({
  alignItems: "center",
  display: "flex",
  width: "100%",
});

const Label = styled("div")({
  color: "#000000B2",
  display: "inline-block",
  font: "normal 700 14px Roboto",
  overflow: "hidden",
  textOverflow: "ellipsis",
  width: 172,
});

const Value = styled("div")({
  color: "#000000B2",
  display: "inline-block",
  flex: 1,
  font: "normal 400 14px Roboto",
  overflow: "hidden",
  textOverflow: "ellipsis",
});

const Row: React.FC<{ label: string; value: string }> = ({ label, value }) => (
  <RowWrapper>
    <Label>{ifnullblank(label)}</Label>
    <Value>{ifnullblank(value)}</Value>
  </RowWrapper>
);

const Summary: React.FC<IFormProps> = ({
  dosageFormProps,
  strengthProps,
  durationProps,
  stoptimeProps,
  lastFillDateProps,
  quantityProps,
  refillsRemainProps,
  theraClassProps,
  instructionsProps,
  sourceProps,
}) => {
  return (
    <Wrapper>
      <Column>
        <Row label="Dosage Form:" value={dosageFormProps.value} />
        <Row label="Strength:" value={strengthProps.value} />
        <Row
          label="Duration:"
          value={`${ifnullblank(durationProps.value)} ${ifnullblank(durationProps.unit)}`}
        />
        <Row
          label="End Date:"
          value={getTimeStr(stoptimeProps.value, "MM/DD/YYYY")}
        />
        <Row
          label="Last Fill Date:"
          value={getTimeStr(lastFillDateProps.value, "MM/DD/YYYY")}
        />
      </Column>
      <Column>
        <Row label="Quantity:" value={quantityProps.value} />
        <Row label="Refills Remaining:" value={refillsRemainProps.value} />
        <Row label="Therapeutic Class:" value={theraClassProps.value} />
        <Row label="Source:" value={sourceProps.value} />
        <Row label="Patient Instructions:" value={instructionsProps.value} />
      </Column>
    </Wrapper>
  );
};

export default Summary;
