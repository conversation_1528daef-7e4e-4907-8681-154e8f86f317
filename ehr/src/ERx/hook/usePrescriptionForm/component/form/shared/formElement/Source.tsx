import { styled } from "@mui/material/styles";
import React from "react";
import { nullorblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP } from "../../../../config/style";
import { IFieldProps, IOption } from "../../../../type/form";
import ChipStack from "../others/ChipStack";
import Label from "../others/Label";

const Wrapper = styled("div")({
  alignItems: "center",
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const Source: React.FC<IFieldProps> = ({
  value,
  options,
  changeFields,
  required,
  disabled,
  onChange,
}) => {
  const handleChange = (o: IOption | null) => {
    onChange({ [changeFields?.value ?? ""]: o?.value ?? "" });
  };

  return (
    <Wrapper>
      <Label required={required} value="Source" />
      <ChipStack<IOption>
        dataArray={options}
        disabled={disabled}
        getOptionLabel={(o) => o?.display ?? o?.value ?? ""}
        isOptionEqualToValue={(o, v) => o.value === v?.value}
        value={nullorblank(value) ? null : { value }}
        onClick={handleChange}
      />
    </Wrapper>
  );
};

export default Source;
