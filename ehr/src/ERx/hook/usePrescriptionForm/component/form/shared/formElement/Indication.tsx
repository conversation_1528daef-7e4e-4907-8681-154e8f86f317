import { styled } from "@mui/material/styles";
import React from "react";
import TextField from "@cci-monorepo/ERx/component/form/TextField";
import { ifnullblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP } from "../../../../config/style";
import { IFieldProps } from "../../../../type/form";
import Label from "../others/Label";

const Wrapper = styled("div")({
  alignItems: "center",
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const StyledTextField = styled(TextField)({
  flex: 1,
});

const Indication: React.FC<IFieldProps> = ({
  value,
  changeFields,
  required,
  disabled,
  onChange,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ [changeFields?.value ?? ""]: ifnullblank(e.target.value) });
  };

  return (
    <Wrapper>
      <Label required={required} value="Indication" />
      <StyledTextField
        disabled={disabled}
        value={value ?? ""}
        onChange={handleChange}
      />
    </Wrapper>
  );
};

export default Indication;
