import { styled } from "@mui/material/styles";
import React from "react";
import Combobox from "@cci-monorepo/ERx/component/form/Combobox";
import TextField from "@cci-monorepo/ERx/component/form/TextField";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP, LEFT_FIELD_WIDTH } from "../../../../config/style";
import { IFieldProps, IOption } from "../../../../type/form";
import Label from "../others/Label";

const Wrapper = styled("div")({
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

interface IProps extends IFieldProps {
  wantTextField?: boolean;
}

const DosageForm: React.FC<IProps> = ({
  value,
  options,
  changeFields,
  disabled,
  required,
  error,
  wantTextField,
  onChange,
}) => {
  const onValueChange = (e: InputEvent, nValue: IOption) => {
    onChange({ [changeFields?.value ?? ""]: ifnullblank(nValue.value) });
  };

  const onInputValueChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    onChange({
      [changeFields?.value ?? ""]: ifnullblank(e.target.value),
    });
  };

  return (
    <Wrapper>
      <Label required={required} value="Dosage Form" />
      {wantTextField && (
        <TextField
          disabled={disabled}
          error={Boolean(error?.value)}
          value={ifnullblank(value)}
          onChange={onInputValueChange}
        />
      )}
      {!wantTextField && (
        <Combobox
          disabled={disabled}
          error={Boolean(error?.value)}
          getOptionLabel={(option: IOption) => ifnullblank(option.value)}
          isOptionEqualToValue={(option: IOption, nValue: IOption) =>
            option.value === nValue.value
          }
          options={options ?? []}
          value={nullorblank(value) ? null : { value }}
          width={LEFT_FIELD_WIDTH}
          onChange={onValueChange}
        />
      )}
    </Wrapper>
  );
};

export default DosageForm;
