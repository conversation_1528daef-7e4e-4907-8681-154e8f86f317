import { styled } from "@mui/material/styles";
import React from "react";
import Combobox from "@cci-monorepo/ERx/component/form/Combobox";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP, LEFT_FIELD_WIDTH } from "../../../../config/style";
import { IFieldProps, IOption } from "../../../../type/form";
import ChipStack from "../others/ChipStack";
import Label from "../others/Label";

const Wrapper = styled("div")({
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const RightFieldWrapper = styled("div")({
  flex: 1,
  overflow: "hidden",
  paddingTop: 4,
});

const Frequency: React.FC<IFieldProps> = ({
  value,
  options,
  suggestions,
  changeFields,
  disabled,
  required,
  error,
  onChange,
}) => {
  const onValueChange = (e: InputEvent, nValue: IOption) => {
    onChange({ [changeFields?.value ?? ""]: ifnullblank(nValue.value) });
  };

  const handleChipClick = (option: IOption | null) => {
    if (option == null)
      onChange({
        [changeFields?.value ?? ""]: "",
      });
    else
      onChange({
        [changeFields?.value ?? ""]: option.value,
      });
  };

  return (
    <Wrapper>
      <Label required={required} value="Frequency" />
      <Combobox
        disabled={disabled}
        error={Boolean(error?.value)}
        getOptionLabel={(option: IOption) => ifnullblank(option.value)}
        isOptionEqualToValue={(option: IOption, nValue: IOption) =>
          option.value === nValue.value
        }
        options={options ?? []}
        value={nullorblank(value) ? null : { value }}
        width={LEFT_FIELD_WIDTH}
        onChange={onValueChange}
      />
      <RightFieldWrapper>
        <ChipStack<IOption>
          dataArray={suggestions}
          disabled={disabled}
          getOptionLabel={(rec) =>
            ifnullblank(rec?.display, rec?.value) as string
          }
          isOptionEqualToValue={(rec, val) => rec.value === val?.value}
          value={{ value }}
          onClick={handleChipClick}
        />
      </RightFieldWrapper>
    </Wrapper>
  );
};

export default Frequency;
