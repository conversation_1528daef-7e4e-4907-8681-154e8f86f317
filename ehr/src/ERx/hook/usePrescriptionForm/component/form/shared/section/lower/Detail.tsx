import { styled } from "@mui/material/styles";
import React from "react";
import { IFormProps } from "../../../../../type/main";
import DosageForm from "../../formElement/DosageForm";
import Duration from "../../formElement/Duration";
import LastFillDate from "../../formElement/LastFillDate";
import PatientInstructions from "../../formElement/PatientInstructions";
import Quantity from "../../formElement/Quantity";
import RefillsRemain from "../../formElement/RefillsRemain";
import Source from "../../formElement/Source";
import StopTime from "../../formElement/StopTime";
import Strength from "../../formElement/Strength";
import TheraClass from "../../formElement/TheraClass";

const Wrapper = styled("div")({
  background: "#FFFFFF",
  display: "flex",
  flexDirection: "column",
  gap: 8,
});

const SplitColumnsWrapper = styled("div")({
  display: "flex",
  gap: 16,
});

const SplitColumn = styled("div")({
  flex: 1,
});

const Detail: React.FC<IFormProps> = ({
  dosageFormProps,
  strengthProps,
  durationProps,
  stoptimeProps,
  lastFillDateProps,
  quantityProps,
  refillsRemainProps,
  theraClassProps,
  instructionsProps,
  sourceProps,
}) => {
  return (
    <Wrapper>
      <SplitColumnsWrapper>
        <SplitColumn>
          <DosageForm {...dosageFormProps} />
        </SplitColumn>
        <SplitColumn>
          <Strength {...strengthProps} />
        </SplitColumn>
      </SplitColumnsWrapper>
      <Duration {...durationProps} />
      <StopTime {...stoptimeProps} />
      <LastFillDate {...lastFillDateProps} />
      <SplitColumnsWrapper>
        <SplitColumn>
          <Quantity {...quantityProps} />
        </SplitColumn>
        <SplitColumn>
          <RefillsRemain {...refillsRemainProps} />
        </SplitColumn>
      </SplitColumnsWrapper>
      <TheraClass {...theraClassProps} />
      <PatientInstructions {...instructionsProps} />
      <Source {...sourceProps} />
    </Wrapper>
  );
};

export default Detail;
