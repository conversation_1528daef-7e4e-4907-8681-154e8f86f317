import { styled } from "@mui/material/styles";
import React from "react";
import DateTimePicker from "@cci-monorepo/ERx/component/form/DateTimePicker";
import { LABEL_FIELD_GAP, LEFT_FIELD_WIDTH } from "../../../../config/style";
import { IFieldProps } from "../../../../type/form";
import Label from "../others/Label";

const Wrapper = styled("div")({
  display: "flex",
  gap: LABEL_FIELD_GAP,
});

const StopTime: React.FC<IFieldProps> = ({
  value,
  onChange,
  changeFields,
  disabled,
  required,
  error,
}) => {
  const valueField = changeFields?.value ?? "";
  const handleChange = React.useCallback(
    (newVal: number | string | null) => {
      onChange({ [valueField]: newVal });
    },
    [onChange, valueField]
  );

  return (
    <Wrapper>
      <Label required={required} value="End Date" />
      <DateTimePicker
        allowEmpty
        disabled={disabled}
        error={Boolean(error?.value)}
        fieldWidth={LEFT_FIELD_WIDTH}
        inputFormat="MM/DD/YYYY"
        isAllowedInput
        placeholder="__/__/____"
        slotProps={{
          desktopPaper: {
            sx: {
              "& .MuiMultiSectionDigitalClock-root": { display: "none" },
            },
          },
        }}
        value={value}
        onChange={handleChange}
      />
    </Wrapper>
  );
};

export default StopTime;
