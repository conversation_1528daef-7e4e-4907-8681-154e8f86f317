import { styled } from "@mui/material/styles";
import React from "react";
import TextField from "@cci-monorepo/ERx/component/form/TextField";
import { ifnullblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP } from "../../../../config/style";
import { IFieldProps } from "../../../../type/form";
import Label from "../others/Label";

const Wrapper = styled("div")({
  "& .MuiTextField-root": {
    flex: 1,
  },
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const RefillsRemain: React.FC<IFieldProps> = ({
  value,
  changeFields,
  disabled,
  required,
  error,
  onChange,
}) => {
  const onValueChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    onChange({
      [changeFields?.value ?? ""]: ifnullblank(e.target.value),
    });
  };

  return (
    <Wrapper>
      <Label required={required} value="Refills Remaining" />
      <TextField
        disabled={disabled}
        error={Boolean(error?.value)}
        value={ifnullblank(value)}
        onChange={onValueChange}
      />
    </Wrapper>
  );
};

export default RefillsRemain;
