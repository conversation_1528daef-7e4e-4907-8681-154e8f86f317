import { styled } from "@mui/material/styles";
import React from "react";
import { ifnullblank } from "@cci-monorepo/common";
import DateTimePicker from "@cci-monorepo/ERx/component/form/DateTimePicker";
import { Moment } from "moment";
import { MomentTz } from "@cci-monorepo/ERx/type/type";
import {
  LAST_TAKEN_OPTION,
  LAST_TAKEN_OPTIONS,
} from "../../../../config/option";
import { LABEL_FIELD_GAP, LEFT_FIELD_WIDTH } from "../../../../config/style";
import { IFieldProps, IOption } from "../../../../type/form";
import ChipStack from "../others/ChipStack";
import Label from "../others/Label";

const serverTZ = cci.cfg.serverTZ as string; // eslint-disable-line @typescript-eslint/no-unsafe-member-access
const today = ((moment() as MomentTz).tz(serverTZ) as Moment)
  .startOf("day")
  .unix();
const yesterday = ((moment() as MomentTz).tz(serverTZ) as Moment)
  .subtract(1, "days")
  .startOf("day")
  .unix();
const pastWeek = ((moment() as MomentTz).tz(serverTZ) as Moment)
  .subtract(7, "days")
  .startOf("day")
  .unix();

const Wrapper = styled("div")({
  display: "flex",
  gap: LABEL_FIELD_GAP,
});

const RightFieldWrapper = styled("div")({
  flex: 1,
  overflow: "hidden",
  paddingTop: 4,
});

const LastFillDate: React.FC<IFieldProps> = ({
  value,
  onChange,
  changeFields,
  disabled,
  required,
  error,
}) => {
  const chipValue = React.useMemo(() => {
    if (String(value) === String(today)) return LAST_TAKEN_OPTION.TODAY;
    else if (String(value) === String(yesterday))
      return LAST_TAKEN_OPTION.YESTERDAY;
    else if (String(value) === String(pastWeek))
      return LAST_TAKEN_OPTION.PAST_WEEK;
    return "";
  }, [value]);

  const valueField = changeFields?.value ?? "";
  const handleChange = React.useCallback(
    (newVal: number | string | null) => {
      onChange({ [valueField]: newVal });
    },
    [onChange, valueField]
  );

  const handleChipClick = (option: IOption | null) => {
    if (option == null)
      onChange({
        [changeFields?.value ?? ""]: null,
      });
    else {
      let newValue: number | null = null;
      switch (option.value) {
        case LAST_TAKEN_OPTION.TODAY:
          newValue = today;
          break;
        case LAST_TAKEN_OPTION.YESTERDAY:
          newValue = yesterday;
          break;
        case LAST_TAKEN_OPTION.PAST_WEEK:
          newValue = pastWeek;
          break;
        default:
          break;
      }
      onChange({
        [changeFields?.value ?? ""]: newValue,
      });
    }
  };

  return (
    <Wrapper>
      <Label required={required} value="Last Fill Date" />
      <DateTimePicker
        allowEmpty
        disabled={disabled}
        error={Boolean(error?.value)}
        fieldWidth={LEFT_FIELD_WIDTH}
        inputFormat="MM/DD/YYYY"
        isAllowedInput
        placeholder="__/__/____"
        slotProps={{
          desktopPaper: {
            sx: {
              "& .MuiMultiSectionDigitalClock-root": { display: "none" },
            },
          },
        }}
        value={value as number | null}
        onChange={handleChange}
      />
      <RightFieldWrapper>
        <ChipStack<IOption>
          dataArray={LAST_TAKEN_OPTIONS}
          disabled={disabled}
          getOptionLabel={(rec) => ifnullblank(rec?.display) as string}
          isOptionEqualToValue={(rec, val) => rec.value === val?.value}
          value={{ value: chipValue }}
          onClick={handleChipClick}
        />
      </RightFieldWrapper>
    </Wrapper>
  );
};

export default LastFillDate;
