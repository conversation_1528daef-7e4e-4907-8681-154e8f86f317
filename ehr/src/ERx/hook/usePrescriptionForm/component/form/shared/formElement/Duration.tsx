import { styled } from "@mui/material/styles";
import React from "react";
import Combobox from "@cci-monorepo/ERx/component/form/Combobox";
import TextField from "@cci-monorepo/ERx/component/form/TextField";
import { ifnullblank, nullorblank } from "@cci-monorepo/common";
import { LABEL_FIELD_GAP, LEFT_FIELD_WIDTH } from "../../../../config/style";
import { IFieldProps, IOption } from "../../../../type/form";
import Label from "../others/Label";

const Wrapper = styled("div")({
  display: "flex",
  gap: LABEL_FIELD_GAP,
  overflow: "hidden",
});

const LeftFieldWrapper = styled("div")({
  "& .MuiAutocomplete-root": {
    "& .MuiTextField-root": {
      width: "100%",
    },
    flex: 1,
  },
  display: "flex",
  gap: 16,
  overflow: "hidden",
  width: LEFT_FIELD_WIDTH,
});

const Duration: React.FC<IFieldProps> = ({
  value,
  unit,
  unitOptions,
  changeFields,
  disabled,
  required,
  error,
  onChange,
}) => {
  const onValueChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    onChange({
      [changeFields?.value ?? ""]: e.target.value,
    });
  };

  const onUnitChange = (e: InputEvent, nValue: IOption) => {
    onChange({ [changeFields?.unit ?? ""]: ifnullblank(nValue.value) });
  };

  return (
    <Wrapper>
      <Label required={required} value="Duration" />
      <LeftFieldWrapper>
        <TextField
          disabled={disabled}
          error={Boolean(error?.value)}
          style={{ width: 91 }}
          value={value}
          onChange={onValueChange}
        />
        <Combobox
          disabled={disabled}
          error={Boolean(error?.unit)}
          getOptionLabel={(option: IOption) => ifnullblank(option.value)}
          isOptionEqualToValue={(option: IOption, nValue: IOption) =>
            option.value === nValue.value
          }
          options={unitOptions ?? []}
          value={nullorblank(unit) ? null : { value: unit }}
          width={LEFT_FIELD_WIDTH}
          onChange={onUnitChange}
        />
      </LeftFieldWrapper>
    </Wrapper>
  );
};

export default Duration;
