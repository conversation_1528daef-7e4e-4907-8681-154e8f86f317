import React from "react";
import { IFormProps } from "../../../../../type/main";
import Detail from "./Detail";
import Summary from "./Summary";

interface IProps {
  isInSummaryFormMode: boolean;
  formProps: IFormProps;
}

const Lower: React.FC<IProps> = ({ formProps, isInSummaryFormMode }) => {
  if (isInSummaryFormMode) return <Summary {...formProps} />;
  return <Detail {...formProps} />;
};

export default Lower;
