import { styled } from "@mui/material/styles";
import React from "react";
import Button from "../../../../component/form/Button";
import { IControllerResult } from "../../type/main";
import Divider from "./shared/others/Divider";
import Lower from "./shared/section/lower";
import Upper from "./shared/section/Upper";

const Wrapper = styled("div")({
  display: "flex",
  flex: 1,
  flexDirection: "column",
  overflow: "auto",
  padding: "16px 32px",
  width: "100%",
});

const Form: React.FC<IControllerResult> = ({
  readOnly,
  formProps,
  isInSummaryFormMode,
  onToggleFormMode,
}) => {
  return (
    <Wrapper>
      <Upper {...formProps} />
      <Divider />
      <Lower
        formProps={formProps}
        isInSummaryFormMode={Boolean(isInSummaryFormMode)}
      />
      <Divider />
      {!readOnly && (
        <Button
          color="secondary"
          style={{ width: "fit-content" }}
          onClick={onToggleFormMode}
        >
          {isInSummaryFormMode ? "Edit Additional Details" : "Hide Fields"}
        </Button>
      )}
    </Wrapper>
  );
};

export default Form;
