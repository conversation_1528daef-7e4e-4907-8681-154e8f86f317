export interface IOption {
  value: string;
  display?: string;
  unit?: string;
}

export interface IFormData {
  name?: string;

  [x: string]: any;
}

export type IOrderChoice = Record<string, any>;

export type IChoices = Record<string, any[] | undefined>;

export interface IFieldProps {
  value: any;
  valueOptions?: any[];
  options?: any[];
  suggestions?: any[];
  changeFields?: Record<string, string>;
  required?: boolean;
  error?: Record<string, { severity?: string }>;
  disabled?: boolean;
  hidden?: boolean;
  onChange: (changes: Record<string, any>) => void;

  [x: string]: any;
}

export interface IForm {
  data: IFormData;
  disabled: Record<string, boolean>;
  error: Record<string, { severity?: string }>;
  option: Record<string, any[]>;
  suggestion: Record<string, any[] | undefined>;
  needValidation: Record<string, boolean>;
  others: {
    isInit: boolean;
    orderChoices: IOrderChoice[];
    selectionOrderChoices: IOrderChoice[];
    choices: IChoices;
  };
  loading: Record<string, boolean>;
}
