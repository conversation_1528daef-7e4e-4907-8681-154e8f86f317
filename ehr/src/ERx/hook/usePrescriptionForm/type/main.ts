import { IControllerResult as IDescribeMedProps } from "../../useDescribeMed/type/type";
import { IFieldProps, IForm, IFormData } from "./form";

export interface IState {
  open: boolean;
  setupLoading: boolean;
  form: IForm;
  isInSummaryFormMode: boolean;
}

export interface IFormProps {
  nameProps: IFieldProps;
  statusProps: IFieldProps;
  doseProps: IFieldProps;
  routeProps: IFieldProps;
  frequencyProps: IFieldProps;
  indicationProps: IFieldProps;
  starttimeProps: IFieldProps;
  lastTakenProps: IFieldProps;
  complianceProps: IFieldProps;
  dosageFormProps: IFieldProps;
  strengthProps: IFieldProps;
  durationProps: IFieldProps;
  stoptimeProps: IFieldProps;
  lastFillDateProps: IFieldProps;
  quantityProps: IFieldProps;
  refillsRemainProps: IFieldProps;
  theraClassProps: IFieldProps;
  instructionsProps: IFieldProps;
  sourceProps: IFieldProps;

  [x: string]: Record<string, any>;
}

export interface IControllerResult extends Partial<IState> {
  readOnly: boolean;
  onOpen: ({
    data,
    isInModifyMode,
    onSave,
  }: {
    data: IFormData;
    isInModifyMode: boolean;
    onSave: (data: IFormData) => Promise<void>;
  }) => void;
  onClose: () => void;
  onSave: () => void;
  onToggleFormMode: () => void;
  onDescribeMed: (med: IFormData) => void;
  onOrderHistory: (med: IFormData) => void;
  formProps: IFormProps;
  toolbarDisabled: { cancel?: boolean; save?: boolean };
  describeMedProps: IDescribeMedProps;
}
