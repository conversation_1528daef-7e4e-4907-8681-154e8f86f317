import { nullorblank } from "@cci-monorepo/common";
import { FIELD } from "../config/form";

export const ddaasig = async (params: any) => {
  const response = await Cci.util.Hobj.requestUnorderedRecords({
    dbs: [Cci.util.Patient.getDbpath()],
    hobj: "medrec/ordinfo/ddaasig",
    noBatch: true,
    params,
  });
  const doseError = JSON.parse(
    response.output_message?.[0]?.message ?? "[]"
  )?.[0];
  let ddaasigData = {
    ...(response.ddaasig?.[0] ?? {}),
    [FIELD.NAME]: response.ddaasig?.[0]?.[FIELD.ORDER_SIG],
  };
  ddaasigData = Object.keys(ddaasigData).reduce((o, n) => {
    if (nullorblank(ddaasigData[n])) return o;
    o[n] = ddaasigData[n];
    return o;
  }, {} as any);
  const res = {
    ddaasig: ddaasigData,
    error: {
      dose_info: doseError,
    },
  };
  return res;
};
