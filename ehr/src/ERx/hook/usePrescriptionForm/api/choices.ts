import { FIELD } from "../config/form";
// import { getPrefPhms } from "./getPrefPhms";

export const choices = async () => {
  // const prefPharms = getPrefPhms();
  const choices = Cci.util.Hobj.requestUnorderedRecords({
    hobj: "ccrx/choices",
    noBatch: true,
    params: { min: 1 },
  });
  const result = await Promise.all([choices]);
  return {
    [FIELD.DURATION_UNIT]: result[0].TimeUnits,
    [FIELD.DOSE_UNIT]: result[0].Dosing_Units,
    [FIELD.ROUTE]: result[0].RxRoutes,
    [FIELD.FREQUENCY]: result[0].Frequencies.map(
      (rec: { Display: string; value: string; [x: string]: string }) => ({
        ...rec,
        display: rec.Display,
      })
    ),
  };
};
