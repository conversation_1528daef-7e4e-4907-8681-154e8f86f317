import update from "immutability-helper";
import omit from "lodash/omit";
import {
  DISABLED_FIELDS,
  FILTER_FIELDS_ORDER_CHOICES,
  REQUIRED_FIELDS,
  SELECTION_FIELDS_ORDER_CHOICES,
} from "../../config/form";
import { IForm } from "../../type/form";
import getSelectionChoices, { getFieldChoices } from "../../util/form/choices";
import { selectChoice } from "../../util/form/selectChoice";
import { getError } from "../../util/form/validation";

interface IAction {
  type: string;

  [x: string]: any;
}

export const ACTIONS = {
  ON_CHANGE: "On Data Change",
  SETUP: "On Setup",
  UPDATE_CHOICE: "Update choice",
  UPDATE_DDAASIG_LOADING: "Update DDAASIG loading",
  UPDATE_ERROR: "Update Error",
  VALIDATION: "On Validation",
};

export const initState: IForm = {
  data: {},
  disabled: DISABLED_FIELDS,
  error: {},
  loading: { ddaasig: false },
  needValidation: REQUIRED_FIELDS,
  option: {},
  others: {
    choices: {},
    isInit: true,
    orderChoices: [],
    selectionOrderChoices: [],
  },
  suggestion: {},
};

export const reducer = (state: IForm, action: IAction): IForm => {
  switch (action.type) {
    case ACTIONS.SETUP: {
      return action.payload;
    }
    case ACTIONS.VALIDATION: {
      const error = getError(state.data, state.needValidation);
      return update(state, {
        error: { $merge: error },
      });
    }
    case ACTIONS.UPDATE_CHOICE: {
      const selectedChoice = selectChoice(
        state.data,
        state.others.selectionOrderChoices,
        state.error
      );
      return update(state, {
        data: {
          $merge: omit(selectedChoice ?? {}, SELECTION_FIELDS_ORDER_CHOICES),
        },
      });
    }
    case ACTIONS.UPDATE_ERROR: {
      return update(state, { error: { $merge: action.error } });
    }
    case ACTIONS.ON_CHANGE: {
      let res: IForm = state;
      const changes = action.changes ?? {};
      res = update(state, { data: { $merge: action.changes } });
      res = reducer(res, { type: ACTIONS.VALIDATION });

      const hasOrderChoicesFieldError = SELECTION_FIELDS_ORDER_CHOICES.some(
        (field) => Boolean(res.error[field])
      );
      const hasOrderChoicesFieldChange = SELECTION_FIELDS_ORDER_CHOICES.some(
        (field) => changes.hasOwnProperty(field)
      );
      const hasOrderChoicesFilterFieldChange = FILTER_FIELDS_ORDER_CHOICES.some(
        (field) => changes.hasOwnProperty(field)
      );
      if (hasOrderChoicesFilterFieldChange) {
        const selectionOrderChoices = getSelectionChoices(
          res.data,
          res.others.orderChoices
        );
        res = update(res, {
          others: { selectionOrderChoices: { $set: selectionOrderChoices } },
        });
        const fieldChoices = getFieldChoices(res);
        res = update(res, {
          option: { $set: fieldChoices.option },
          suggestion: { $set: fieldChoices.suggestion },
        });
      }
      if (!hasOrderChoicesFieldError && hasOrderChoicesFieldChange)
        res = reducer(res, { type: ACTIONS.UPDATE_CHOICE });
      return update(res, { others: { isInit: { $set: false } } });
    }
    case ACTIONS.UPDATE_DDAASIG_LOADING: {
      return update(state, { loading: { ddaasig: { $set: action.loading } } });
    }
    default:
      return state;
  }
};
