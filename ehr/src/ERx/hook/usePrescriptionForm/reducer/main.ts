import update from "immutability-helper";
import { IState } from "../type/main";
import {
  ACTIONS as FORM_ACTIONS,
  initState as formInitState,
  reducer as formReducer,
} from "./form";

export const ACTIONS = {
  CLOSE: "Close",
  OPEN: "Open",
  TOGGLE_FORM_MODE: "Toggle Form Mode",
  UPDATE_SETUP_LOADING: "UpdateL setup Loading",
  ...FORM_ACTIONS,
};

export const initState: IState = {
  form: formInitState,
  isInSummaryFormMode: true,
  open: false,
  setupLoading: false,
};

interface IAction {
  type: string;

  [x: string]: any;
}

export const reducer = (state: IState, action: IAction) => {
  switch (action.type) {
    case ACTIONS.OPEN: {
      return update(state, {
        form: { $set: formInitState },
        isInSummaryFormMode: { $set: true },
        open: { $set: true },
      });
    }
    case ACTIONS.CLOSE: {
      return update(state, {
        isInSummaryFormMode: { $set: true },
        open: { $set: false },
      });
    }
    case ACTIONS.UPDATE_SETUP_LOADING:
      return update(state, { setupLoading: { $set: action.loading } });
    case ACTIONS.TOGGLE_FORM_MODE:
      return update(state, {
        isInSummaryFormMode: { $apply: (value: boolean) => !value },
      });
    default:
      return update(state, {
        form: { $set: formReducer(state.form, action) },
      });
  }
};
