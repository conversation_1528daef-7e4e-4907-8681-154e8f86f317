import isEmpty from "lodash/isEmpty";
import omit from "lodash/omit";
import React from "react";
import { nullorblank } from "@cci-monorepo/common";
import useDescribeMed from "../../useDescribeMed/hook/useController";
import { ddaasig } from "../api/ddaasig";
import { DDAASIG_PARAMS, EXCLUDE_FROM_DDAASIG, FIELD } from "../config/form";
import { ACTIONS, initState, reducer } from "../reducer/main";
import { IFormData } from "../type/form";
import { IControllerResult } from "../type/main";
import { setup } from "../util/form";
import { isFreeTextMed } from "@cci-monorepo/Pld/hook/useMeds/util/util";

interface IProps {
  readOnly: boolean;
}

const useController = ({ readOnly }: IProps): IControllerResult => {
  const [state, dispatch] = React.useReducer(reducer, initState);
  const onSaveRef = React.useRef<((data: IFormData) => Promise<void>) | null>(
    null
  );
  const form = state.form;
  const data = form.data;
  const option = form.option;
  const suggestion = form.suggestion;
  const needValidation = form.needValidation;
  const error = form.error;
  const disabled = form.disabled;
  const others = form.others;
  const isFreeTextMedication = isFreeTextMed(data);

  const deferredDdaasigParams = React.useDeferredValue(
    JSON.stringify(
      DDAASIG_PARAMS.reduce(
        (o, n) => {
          o[n] = data[n];
          return o;
        },
        {} as Record<string, any>
      )
    )
  );
  const isInit = others.isInit;

  React.useEffect(() => {
    let didCancel = false;
    const params = JSON.parse(deferredDdaasigParams);
    const execute = async () => {
      if (
        isInit ||
        nullorblank(params[FIELD.FULL_ORDER_ID]) ||
        nullorblank(params[FIELD.ORD_MED_ID]) ||
        nullorblank(params[FIELD.DOSE_VALUE]) ||
        nullorblank(params[FIELD.DOSE_UNIT]) ||
        nullorblank(params[FIELD.ROUTE]) ||
        nullorblank(params[FIELD.FREQUENCY])
      )
        return () => {
          didCancel = true;
        };
      dispatch({ loading: true, type: ACTIONS.UPDATE_DDAASIG_LOADING });
      const payload = await ddaasig({
        orderdata: params,
      });
      if (!didCancel) {
        dispatch({
          changes: omit(payload.ddaasig, EXCLUDE_FROM_DDAASIG),
          type: ACTIONS.ON_CHANGE,
        });
        // dispatch({ type: ACTIONS.UPDATE_ERROR, error: payload.error });
        dispatch({
          loading: false,
          type: ACTIONS.UPDATE_DDAASIG_LOADING,
        });
      }
    };

    execute();
    return () => {
      didCancel = true;
    };
  }, [deferredDdaasigParams, dispatch, isInit]);

  React.useEffect(() => {
    if (isFreeTextMedication && !isInit) {
      if (!data[FIELD.BASE_NAME]) {
        const changes = { [FIELD.BASE_NAME]: data[FIELD.NAME] };
        dispatch({
          changes,
          type: ACTIONS.ON_CHANGE,
        });
      }

      // Generate name from template https://cciconnect.clinicomp.com/SoftZilla/attachment.cgi?id=99373&action=view
      // [tallman generic name] + [BRAND NAME] + [admin DOSE] + [route] + [frequency];
      const newName = `${data[FIELD.BASE_NAME] ?? ""} ${data[FIELD.DOSE_VALUE] ?? ""} ${data[FIELD.DOSE_UNIT] ?? ""} ${data[FIELD.ROUTE] ?? ""} ${data[FIELD.FREQUENCY] ?? ""}`;
      const nameChanges = { [FIELD.NAME]: newName };
      dispatch({
        changes: nameChanges,
        type: ACTIONS.ON_CHANGE,
      });
    }
  }, [deferredDdaasigParams, isInit]);

  const onOpen = React.useCallback(
    async ({
      data,
      isInModifyMode,
      onSave,
    }: {
      [x: string]: any;
      isInModifyMode: boolean;
      onSave: (data: IFormData) => Promise<void>;
    }) => {
      onSaveRef.current = onSave;
      dispatch({ type: ACTIONS.OPEN });
      dispatch({ loading: true, type: ACTIONS.UPDATE_SETUP_LOADING });
      const payload = await setup({ data, isInModifyMode });
      dispatch({ payload, type: ACTIONS.SETUP });
      dispatch({ loading: false, type: ACTIONS.UPDATE_SETUP_LOADING });
    },
    [dispatch, onSaveRef]
  );

  const onClose = () => {
    onSaveRef.current = null;
    dispatch({ type: ACTIONS.CLOSE });
  };

  const onChange = React.useCallback(
    (changes: Record<string, any>) => {
      dispatch({
        changes,
        type: ACTIONS.ON_CHANGE,
      });
    },
    [dispatch]
  );

  const handleSave = () => {
    if (onSaveRef.current) onSaveRef.current(data);
    onClose();
  };

  const onToggleFormMode = () => {
    dispatch({ type: ACTIONS.TOGGLE_FORM_MODE });
  };

  const describeMedProps = useDescribeMed();

  const onDescribeMed = (med: IFormData) => {
    describeMedProps.onOpen(med, 0);
  };

  const onOrderHistory = (med: IFormData) => {
    describeMedProps.onOpen(med, 1);
  };

  const formProps = {
    complianceProps: {
      changeFields: { value: FIELD.COMPLIANCE },
      disabled: disabled[FIELD.COMPLIANCE] || readOnly,
      error: { value: error[FIELD.COMPLIANCE] },
      onChange,
      options: option[FIELD.COMPLIANCE],
      required: needValidation[FIELD.COMPLIANCE],
      value: data[FIELD.COMPLIANCE],
    },
    dosageFormProps: {
      changeFields: { value: FIELD.DOSAGE_FORM },
      disabled: disabled[FIELD.DOSAGE_FORM] || readOnly,
      error: { value: error[FIELD.DOSAGE_FORM] },
      onChange,
      options: option[FIELD.DOSAGE_FORM],
      required: needValidation[FIELD.DOSAGE_FORM],
      value: data[FIELD.DOSAGE_FORM],
      wantTextField: data.isftxt === "1",
    },
    doseProps: {
      isFreeTextMedication,
      changeFields: { unit: FIELD.DOSE_UNIT, value: FIELD.DOSE_VALUE },
      disabled: disabled[FIELD.DOSE_VALUE] || readOnly,
      error: { unit: error[FIELD.DOSE_UNIT], value: error[FIELD.DOSE_VALUE] },
      onChange,
      required:
        needValidation[FIELD.DOSE_VALUE] || needValidation[FIELD.DOSE_UNIT],
      suggestions: suggestion[FIELD.DOSE_VALUE],
      unit: data[FIELD.DOSE_UNIT],
      unitOptions: option[FIELD.DOSE_UNIT],
      value: data[FIELD.DOSE_VALUE],
      valueOptions: option[FIELD.DOSE_VALUE],
    },
    durationProps: {
      changeFields: { unit: FIELD.DURATION_UNIT, value: FIELD.DURATION_VALUE },
      disabled: disabled[FIELD.DURATION_VALUE] || readOnly,
      error: {
        unit: error[FIELD.DURATION_UNIT],
        value: error[FIELD.DURATION_VALUE],
      },
      onChange,
      required: needValidation[FIELD.DURATION_VALUE],
      unit: data[FIELD.DURATION_UNIT],
      unitOptions: option[FIELD.DURATION_UNIT],
      value: data[FIELD.DURATION_VALUE],
    },
    frequencyProps: {
      changeFields: { value: FIELD.FREQUENCY },
      disabled: disabled[FIELD.FREQUENCY] || readOnly,
      error: { value: error[FIELD.FREQUENCY] },
      onChange,
      options: option[FIELD.FREQUENCY],
      required: needValidation[FIELD.FREQUENCY],
      suggestions: suggestion[FIELD.FREQUENCY],
      value: data[FIELD.FREQUENCY],
    },
    indicationProps: {
      changeFields: { value: FIELD.INDICATION },
      disabled: disabled[FIELD.INDICATION] || readOnly,
      error: { value: error[FIELD.INDICATION] },
      onChange,
      required: needValidation[FIELD.INDICATION],
      value: data[FIELD.INDICATION],
    },
    instructionsProps: {
      changeFields: { value: FIELD.INSTRUCTIONS },
      disabled: disabled[FIELD.INSTRUCTIONS] || readOnly,
      error: { value: error[FIELD.INSTRUCTIONS] },
      onChange,
      required: needValidation[FIELD.INSTRUCTIONS],
      value: data[FIELD.INSTRUCTIONS],
    },
    lastFillDateProps: {
      changeFields: { value: FIELD.LAST_FILL_DATE },
      disabled: disabled[FIELD.LAST_FILL_DATE] || readOnly,
      error: { value: error[FIELD.LAST_FILL_DATE] },
      onChange,
      required: needValidation[FIELD.LAST_FILL_DATE],
      value: data[FIELD.LAST_FILL_DATE],
    },
    lastTakenProps: {
      changeFields: { value: FIELD.LAST_TAKEN },
      disabled: disabled[FIELD.LAST_TAKEN] || readOnly,
      error: { value: error[FIELD.LAST_TAKEN] },
      onChange,
      required: needValidation[FIELD.LAST_TAKEN],
      value: data[FIELD.LAST_TAKEN],
    },
    nameProps: {
      changeFields: { value: FIELD.NAME },
      disabled: disabled[FIELD.NAME] || readOnly,
      error: { value: error[FIELD.NAME] },
      hidden: data.isftxt !== "1",
      onChange,
      required: needValidation[FIELD.NAME],
      value: data[FIELD.NAME],
    },
    quantityProps: {
      changeFields: { value: FIELD.QUANTITY },
      disabled: disabled[FIELD.QUANTITY] || readOnly,
      error: { value: error[FIELD.QUANTITY] },
      onChange,
      required: needValidation[FIELD.QUANTITY],
      value: data[FIELD.QUANTITY],
    },
    refillsRemainProps: {
      changeFields: { value: FIELD.REFILLS_REMAIN },
      disabled: disabled[FIELD.REFILLS_REMAIN] || readOnly,
      error: { value: error[FIELD.REFILLS_REMAIN] },
      onChange,
      required: needValidation[FIELD.REFILLS_REMAIN],
      value: data[FIELD.REFILLS_REMAIN],
    },
    routeProps: {
      changeFields: { value: FIELD.ROUTE },
      disabled: disabled[FIELD.ROUTE] || readOnly,
      error: { value: error[FIELD.ROUTE] },
      onChange,
      options: option[FIELD.ROUTE],
      required: needValidation[FIELD.ROUTE],
      suggestions: suggestion[FIELD.ROUTE],
      value: data[FIELD.ROUTE],
    },
    sourceProps: {
      changeFields: { value: FIELD.SOURCE },
      disabled: disabled[FIELD.SOURCE] || readOnly,
      error: { value: error[FIELD.SOURCE] },
      onChange,
      options: option[FIELD.SOURCE],
      required: needValidation[FIELD.SOURCE],
      value: data[FIELD.SOURCE],
    },
    starttimeProps: {
      changeFields: { value: FIELD.STARTTIME },
      disabled: disabled[FIELD.STARTTIME] || readOnly,
      error: { value: error[FIELD.STARTTIME] },
      onChange,
      required: needValidation[FIELD.STARTTIME],
      value: data[FIELD.STARTTIME],
    },
    statusProps: {
      changeFields: { value: FIELD.STATUS },
      disabled: disabled[FIELD.STATUS] || readOnly,
      error: { value: error[FIELD.STATUS] },
      onChange,
      options: option[FIELD.STATUS],
      required: needValidation[FIELD.STATUS],
      value: data[FIELD.STATUS],
    },
    stoptimeProps: {
      changeFields: { value: FIELD.STOPTIME },
      disabled: disabled[FIELD.STOPTIME] || readOnly,
      error: { value: error[FIELD.STOPTIME] },
      onChange,
      required: needValidation[FIELD.STOPTIME],
      value: data[FIELD.STOPTIME],
    },
    strengthProps: {
      changeFields: { value: FIELD.STRENGTH },
      disabled: disabled[FIELD.STRENGTH] || readOnly,
      error: { value: error[FIELD.STRENGTH] },
      onChange,
      required: needValidation[FIELD.STRENGTH],
      value: data[FIELD.STRENGTH],
    },
    theraClassProps: {
      changeFields: { value: FIELD.THERA_CLASS },
      disabled: disabled[FIELD.THERA_CLASS] || readOnly,
      error: { value: error[FIELD.THERA_CLASS] },
      onChange,
      required: needValidation[FIELD.THERA_CLASS],
      value: data[FIELD.THERA_CLASS],
    },
  };

  const hasError = React.useMemo(
    () => !isEmpty(Object.values(error).filter((e) => Boolean(e))),
    [error]
  );

  const toolbarDisabled = {
    save: hasError,
  };

  return {
    ...state,
    describeMedProps,
    formProps,
    onClose,
    onDescribeMed,
    onOpen,
    onOrderHistory,
    onSave: handleSave,
    onToggleFormMode,
    readOnly,
    toolbarDisabled,
  };
};

export default useController;
