import { getTimeStr } from "@cci-monorepo/ERx/util/util";

const transformData = (data: string[][] = []) => {
  const unixToDateFields = ["Last Taken", "Start Date", "Last Fill Date"];
  return data.map(([key, ...value]) => {
    if (unixToDateFields.includes(key)) {
      return [key, ...value.map((v) => getTimeStr(v, "MM/DD/YYYY"))];
    }
    return [key, ...value];
  });
};

export const getData = async (params: { nit: string }) => {
  const orderDetailsRequest = Cci.util.Hobj.requestUnorderedUnfilteredRecords({
    dbs: [Cci.util.Patient.getDbpath()],
    hobj: "pld/home_meds/describeitem",
    noBatch: true,
    params,
  });
  const historyRequest = Cci.util.Hobj.requestUnorderedUnfilteredRecords({
    dbs: [Cci.util.Patient.getDbpath()],
    hobj: "pld/home_meds/describeitem/history",
    noBatch: true,
    params,
  });
  const [orderDetailsResponse, historyResponse] = await Promise.allSettled([
    orderDetailsRequest,
    historyRequest,
  ]);
  const orderDetails =
    orderDetailsResponse.status === "fulfilled"
      ? orderDetailsResponse.value
      : { Home_Med_Details: {} };
  orderDetails.Home_Med_Details.data = transformData(
    orderDetails.Home_Med_Details.data
  );
  const history =
    historyResponse.status === "fulfilled"
      ? historyResponse.value
      : { Home_Med_Details: {} };
  history.Home_Med_Details.data = transformData(history.Home_Med_Details.data);
  history.Home_Med_Details.header[0] = "Order Field";
  return {
    details: {
      Order_Details: orderDetails.Home_Med_Details,
    },
    history: {
      Order_History: history.Home_Med_Details,
    },
  };
};
