import React from "react";
import { getData } from "../api/getData";
import { IControllerResult, IState } from "../type/type";

// Define the MedicationOrder type
export type MedicationOrder = {
  admin_amount: string;
  admin_unit: string;
  compliance: string;
  dose_unit: string;
  dose_value: string;
  frequency: string;
  full_order_id: string;
  instructions: string;
  isotc: string;
  lgtime: string;
  name: string;
  nit: string;
  ord_med_id: string;
  route: string;
  sig: string;
  status: string;
  preferred_pharmacy: Record<string, any>;
  dosage_form: null | string;
  duration_unit: null | string;
  med: string;
  med_supply: string;
  order_sig: string;
};

const useController = (): IControllerResult => {
  const [state, setState] = React.useState<IState>({
    defaultTab: 0,
    details: null,
    history: null,
    loading: false,
    name: "",
    open: false,
  });

  /**
   * Opens the controller with the specified parameters.
   *
   * @param {MedicationOrder} med - The medication order details.
   * @param {0 | 1} defaultTab - The default tab index to open.
   * @param {() => Promise<{ details: any; history: any }>} [customApi] - Optional custom API function to fetch data.
   */
  const onOpen = async (
    med: MedicationOrder,
    defaultTab: 0 | 1,
    customApi?: () => Promise<{ details: any; history: any }>
  ) => {
    setState((prev) => ({ ...prev, loading: true }));
    let data: { details: any; history: any };
    if (customApi) data = await customApi();
    else data = await getData({ nit: med.nit });
    setState((prev) => ({
      ...prev,
      defaultTab,
      details: data.details,
      history: data.history,
      loading: false,
      open: true,
      name: med.name,
    }));
  };

  const onClose = () => {
    setState((prev) => ({
      ...prev,
      details: null,
      history: null,
      loading: false,
      open: false,
    }));
  };

  return {
    ...state,
    onClose,
    onOpen,
  };
};

export default useController;
