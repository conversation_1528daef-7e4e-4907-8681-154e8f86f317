import { styled } from "@mui/material/styles";
import React from "react";
import DescribeOrderPanel from "@cci-monorepo/OE/describe/components/DescribeOrderPanel";
import "../../../../OE/describe/css/oe-describe.css";
import Dialog from "../../../component/dialog";
import BottomWrapper from "../../../component/dialog/BottomWrapper";
import Button from "../../../component/form/Button";
import { IControllerResult } from "../type/type";

const BodyWrapper = styled("div")({
  background: "#E3EAF5",
  display: "flex",
  flex: 1,
  flexDirection: "column",
  overflow: "hidden",
  padding: 24,
});

const PanelWrapper = styled("div")({
  "& .ag-center-cols-clipper": {
    width: "100%",
  },
  flex: 1,
  overflowX: "hidden",
  overflowY: "auto",
  width: "100%",
});

const DescribeItem: React.FC<IControllerResult> = ({
  open,
  onClose,
  name,
  details,
  history,
  defaultTab,
  loading,
}) => {
  return (
    <Dialog
      open={open}
      PaperProps={{
        style: {
          width: "75%",
        },
      }}
      title="Describe Item"
      onClose={onClose}
    >
      <BodyWrapper>
        <PanelWrapper>
          <DescribeOrderPanel
            defaultTab={defaultTab}
            history={history}
            name={name}
            open={!loading}
            orderData={details}
          />
        </PanelWrapper>
        <BottomWrapper>
          <Button onClick={onClose}>OK</Button>
        </BottomWrapper>
      </BodyWrapper>
    </Dialog>
  );
};

export default DescribeItem;
