import { MomentTz } from "@cci-monorepo/ERx/type/type";

const serverTZ = cci.cfg.serverTZ as string; // eslint-disable-line @typescript-eslint/no-unsafe-member-access

export const getTimeStr = (
  unixTime: string | number | undefined,
  format: string | undefined = "HH:mm MM/DD/YYYY"
) => {
  if (!unixTime) return "";
  const mom = (
    moment.unix(
      typeof unixTime === "string" ? Number(unixTime) : unixTime
    ) as MomentTz
  ).tz(serverTZ);
  if (mom.isValid()) return mom.format(format);
  return "";
};
