import { styled } from "@mui/material/styles";
import React from "react";
import { Chip, IChipProps } from "@cci/mui-components";

export const StyledChip = styled(Chip)({
  "&.MuiChip-root": {
    "& .MuiChip-label": {
      font: "normal 500 14px Roboto",
    },
  },
});

const ERxChip: React.FC<IChipProps> = ({
  small = true,
  icon = true,
  ...other
}) => <StyledChip icon={icon} small={small} {...other} />;

export default ERxChip;
