import { styled } from "@mui/material/styles";
import CciCombobox from "@oe-src/common/Combobox";

const Combobox = styled(CciCombobox)({
  "& .MuiInputBase-input.MuiOutlinedInput-input.Mui-disabled": {
    WebkitTextFillColor: "#000000",
    color: "#000000",
  },
  "& .MuiOutlinedInput-root": {
    "&.Mui-disabled": {
      background: "#EBEBEB",
    },
  },
  "&.MuiAutocomplete-root .MuiOutlinedInput-root .MuiAutocomplete-input": {
    height: 32,
  },
});

export default Combobox;
