import { styled } from "@mui/material/styles";
import debounce from "lodash/debounce";
import { Moment } from "moment";
import React from "react";
import { nullorblank } from "@cci-monorepo/common";
import CciDateTimePicker from "@cci-monorepo/OE/common/OEDateTimePicker";
import { MomentTz } from "@cci-monorepo/ERx/type/type";

const serverTZ = cci.cfg.serverTZ as string; // eslint-disable-line @typescript-eslint/no-unsafe-member-access

const StyledDateTimePicker = styled(CciDateTimePicker)({
  "& .MuiOutlinedInput-input": {
    font: "normal 400 16px/16px Helvetica",
    height: 32,
  },
});

const getMom = (value: string | number | undefined | null) =>
  nullorblank(value)
    ? null
    : ((moment.unix(Number(value)) as MomentTz).tz(serverTZ) as Moment);

export interface IProps {
  allowEmpty?: boolean;
  disabled?: boolean;
  error?: boolean;
  fieldWidth?: number;
  inputFormat?: string;
  isAllowedInput?: boolean;
  label?: string;
  onChange: (value: string | number | null) => void;
  placeholder?: string;
  slotProps?: Record<string, any>;
  value?: string | number | null;
  views?: string[];
}

const DateTimePicker: React.FC<IProps> = ({
  value = null,
  error = false,
  onChange,
  ...others
}) => {
  const [open, setOpen] = React.useState(false);

  // eslint-disable-next-line
  const debouncedOnChange = React.useCallback(debounce(onChange, 500), [
    onChange,
  ]);

  const onOpen = () => setOpen(true);

  const onClose = () => setOpen(false);

  const localMom = React.useMemo(() => getMom(value), [value]);

  const handleChange = (newMom: any) => {
    let newVal;
    if (newMom == null || !newMom.isValid()) newVal = null;
    else newVal = newMom.unix();
    debouncedOnChange(newVal);
  };

  return (
    <StyledDateTimePicker
      error={error ? "error" : undefined}
      mom={localMom}
      open={open}
      placeholder=""
      onChange={handleChange}
      onClose={onClose}
      onOpen={onOpen}
      {...others}
    />
  );
};

export default DateTimePicker;
