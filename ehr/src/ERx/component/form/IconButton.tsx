import { StyledEngineProvider } from "@mui/material/styles";
import React from "react";
import TooltipIconButton, {
  BUTTON_TYPE,
  TooltipIconButtonProps,
} from "@cci-monorepo/OE/common/TooltipIconButton";

export interface IIconButtonProps extends TooltipIconButtonProps {
  tooltip: string;
  onClick: (event: React.MouseEvent) => void;
}

const IconButton: React.FC<IIconButtonProps> = (props: IIconButtonProps) => (
  <StyledEngineProvider injectFirst>
    <TooltipIconButton {...props} />
  </StyledEngineProvider>
);

export { BUTTON_TYPE };
export default IconButton;
