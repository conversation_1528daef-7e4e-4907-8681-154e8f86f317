import { ButtonProps as MuiButtonProps } from "@mui/material/Button";
import { styled } from "@mui/material/styles";
import React from "react";
import { Button as CciButton } from "@cci/mui-components";

const StyledButton = styled(CciButton)({
  "&.MuiButtonBase-root.MuiButton-root.Mui-disabled": {
    border: "none",
  },
  padding: "0px 16px",
});

const Button: React.FC<MuiButtonProps> = ({ color, ...other }) => {
  return <StyledButton color={color ? color : "primary"} {...other} />;
};

export default Button;
