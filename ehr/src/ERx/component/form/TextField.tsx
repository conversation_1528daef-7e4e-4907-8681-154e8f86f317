import { styled } from "@mui/material/styles";
import { TextFieldProps } from "@mui/material/TextField";
import React from "react";
import { CciInputField } from "@cci/mui-components";

const StyledTextField = styled(CciInputField)({
  "& .MuiInputBase-input.MuiOutlinedInput-input.Mui-disabled": {
    WebkitTextFillColor: "#000000",
    color: "#000000",
  },
  "&.MuiFormControl-root.MuiTextField-root .MuiOutlinedInput-input": {
    height: 32,
  },
});

interface StyledTextFieldProps
  extends Omit<TextFieldProps, "variant" | "value"> {
  value: string;
}

const TextField: React.FC<StyledTextFieldProps> = (props) => (
  <StyledTextField {...props} />
);

export default TextField;
