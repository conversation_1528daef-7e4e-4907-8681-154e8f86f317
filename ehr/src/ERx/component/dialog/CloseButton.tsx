import { styled } from "@mui/material/styles";
import React from "react";
import CloseIcon from "../../asset/Icon_Close_Dialog.svg";

const StyleButton = styled("button")({
  background: "transparent",
  border: "none",
  cursor: "pointer",
  height: 18,
  padding: 0,
});

const CloseButton: React.FC<React.ButtonHTMLAttributes<HTMLButtonElement>> = (
  props
) => (
  <StyleButton {...props}>
    <img alt="close" src={CloseIcon} />
  </StyleButton>
);

export default CloseButton;
