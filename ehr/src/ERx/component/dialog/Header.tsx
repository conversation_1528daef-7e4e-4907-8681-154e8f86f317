import { styled } from "@mui/material/styles";
import React from "react";
import CloseButton from "./CloseButton";

const StyledHeader = styled("header")({
  alignItems: "center",
  backgroundColor: "#222B2E",
  color: "#FFFFFF",
  cursor: "move",
  display: "flex",
  font: "normal 700 16px Roboto",
  height: 28,
  overflow: "hidden",
  padding: "0 8px",
});

const ChildrenWrapper = styled("div")({
  alignItems: "center",
  display: "flex",
  flex: 1,
  flexWrap: "nowrap",
  overflow: "hidden",
});

const TextWrapper = styled("div")({
  display: "inline-block",
  lineHeight: "28px",
  overflow: "hidden",
  textOverflow: "ellipsis",
});

interface IHeaderComponentProps extends React.HTMLAttributes<HTMLHtmlElement> {
  onClose: () => void;
}

const Header: React.FC<IHeaderComponentProps> = ({
  children,
  onClose,
  ...rest
}) => (
  <StyledHeader {...rest}>
    <ChildrenWrapper>
      <TextWrapper>{children}</TextWrapper>
    </ChildrenWrapper>
    <CloseButton onClick={onClose} />
  </StyledHeader>
);

export default Header;
