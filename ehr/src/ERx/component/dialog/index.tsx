import MuiDialog, { DialogProps } from "@mui/material/Dialog";
import Paper, { PaperProps } from "@mui/material/Paper";
import { styled } from "@mui/material/styles";
import React from "react";
import Draggable from "react-draggable";
import Body from "./Body";
import Header from "./Header";

const headerId = "draggable-dialog-header";
const bodyClassName = "draggable-dialog-body";

const StyledPaper = styled(Paper)({
  boxShadow: "0px 0px 40px 0px #00000033, 0px -4px 20px 0px #00000026",
  maxWidth: "calc(100% - 32px) !important",
  overflow: "hidden !important",
});

const PaperComponent = (props: PaperProps) => {
  return (
    <Draggable cancel={`[class*="${bodyClassName}"]`} handle={`#${headerId}`}>
      <StyledPaper {...props} />
    </Draggable>
  );
};

interface IDialogProps extends DialogProps {
  open: boolean;
  title: string;
  onClose: () => void;
  children?: React.ReactNode;
  bodyStyle?: React.CSSProperties;
}

const Dialog: React.FC<IDialogProps> = ({
  open,
  title,
  onClose,
  children,
  bodyStyle,
  ...other
}) => {
  return (
    <MuiDialog
      aria-labelledby={headerId}
      open={open}
      PaperComponent={PaperComponent}
      slots={{ backdrop: () => null }}
      onClose={onClose}
      {...other}
    >
      <Header id={headerId} onClose={onClose}>
        {title}
      </Header>
      <Body className={bodyClassName} style={bodyStyle}>
        {children}
      </Body>
    </MuiDialog>
  );
};

export default Dialog;
