import Backdrop, { BackdropProps } from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";
import React from "react";

const LoadingBackdrop: React.FC<BackdropProps> = ({ open, ...other }) => (
  <Backdrop
    open={open}
    sx={{
      backdropFilter: "blur(1px)",
      background: "transparent",
      position: "absolute",
      zIndex: (theme) => theme.zIndex.drawer + 1,
    }}
    {...other}
  >
    <CircularProgress />
  </Backdrop>
);

export default LoadingBackdrop;
