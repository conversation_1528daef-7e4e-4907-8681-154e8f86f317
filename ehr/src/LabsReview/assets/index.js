import { ReactComponent as IconAbnormalOval } from "./Abnormal_Oval.svg";
import { ReactComponent as IconAbnormalRound } from "./Abnormal_Round.svg";
import { ReactComponent as IconCorrected } from "./Corrected_Icon.svg";
import { ReactComponent as IconCloseDialog } from "./Icon_Close_Dialog.svg";
import { ReactComponent as IconSearch } from "./Icon_Search.svg";
import { ReactComponent as IconLeftArrow } from "./Left_Arrow.svg";
import { ReactComponent as IconLeftArrowDisabled } from "./Left_Arrow_disabled.svg";
import { ReactComponent as IconNegativeOval } from "./Negative_Oval.svg";
import { ReactComponent as IconNegativeRound } from "./Negative_Round.svg";
import { ReactComponent as IconPositiveOval } from "./Positive_Oval.svg";
import { ReactComponent as IconPositiveRound } from "./Positive_Round.svg";
import { ReactComponent as IconRightArrow } from "./Right_Arrow.svg";
import { ReactComponent as IconRightArrowDisabled } from "./Right_Arrow_disabled.svg";
import { ReactComponent as IconSort } from "./Sort.svg";
import { ReactComponent as IconAddendum } from "./Addendum_Icon.svg";
import { ReactComponent as IconNew } from "./New_Icon.svg";
import { ReactComponent as IconCheckedGrey } from "./Checked_Grey_Icon.svg";
import { ReactComponent as IconCheckedGreen } from "./Checked_Green_Icon.svg";
import { ReactComponent as IconPrelim } from "./Prelim_Icon.svg";
import { ReactComponent as IconPreliminary } from "./Preliminary_Icon.svg";
import { ReactComponent as IconAbnormalLab } from "./AbnormalLab_Icon.svg";
import { ReactComponent as IconCriticalLab } from "./CriticalLab_Icon.svg";

export {
  IconAbnormalOval,
  IconAbnormalRound,
  IconCorrected,
  IconCloseDialog,
  IconSearch,
  IconLeftArrow,
  IconLeftArrowDisabled,
  IconNegativeOval,
  IconNegativeRound,
  IconPositiveOval,
  IconPositiveRound,
  IconRightArrow,
  IconRightArrowDisabled,
  IconSort,
  IconAddendum,
  IconNew,
  IconCheckedGrey,
  IconCheckedGreen,
  IconPrelim,
  IconPreliminary,
  IconAbnormalLab,
  IconCriticalLab,
};
