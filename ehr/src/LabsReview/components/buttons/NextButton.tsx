import * as React from "react";
import Button from "@mui/material/Button";
import { IconRightArrow, IconRightArrowDisabled } from "../../assets";
import { IButtonProps } from "../../type";

const NextButton: React.FunctionComponent<IButtonProps> = (
  props: IButtonProps
): JSX.Element => {
  return (
    <Button
      variant="outlined"
      endIcon={props.disabled ? <IconRightArrowDisabled /> : <IconRightArrow />}
      onClick={props.handleClick}
      disabled={props.disabled}
      style={{
        marginLeft: 15,
        textTransform: "none",
        font: "normal 700 14px Roboto",
        background: "#FFFFFF",
        border: props.disabled ? "1px solid #C0C0C0" : "1px solid #4B6EAF",
        width: "76px",
        height: "32px",
        padding: "4px, 6px, 4px, 8px",
        borderRadius: "4px",
        gap: "8px",
      }}
    >
      Next
    </Button>
  );
};

export default NextButton;
