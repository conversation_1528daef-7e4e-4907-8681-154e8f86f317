import * as React from "react";
import Button from "@mui/material/Button";
import { IButtonProps } from "../../type";

const PreviousBtn: React.FunctionComponent<IButtonProps> = (
  props: IButtonProps
): JSX.Element => {
  return (
    <Button
      variant="outlined"
      onClick={props.handleClick}
      style={{
        textTransform: "none",
        border: "1px solid #4B6EAF",
        font: "normal 700 14px Roboto",
        background: "#FFFFFF",
        height: "32px",
        padding: "8px",
        borderRadius: "4px",
        gap: "8px",
      }}
    >
      {props.text}
    </Button>
  );
};

export default PreviousBtn;
