import * as React from "react";
import Button from "@mui/material/Button";
import { IconLeftArrow, IconLeftArrowDisabled } from "../../assets";
import { IButtonProps } from "../../type";

const PrevButton: React.FunctionComponent<IButtonProps> = (
  props: IButtonProps
): JSX.Element => {
  return (
    <Button
      variant="outlined"
      startIcon={props.disabled ? <IconLeftArrowDisabled /> : <IconLeftArrow />}
      disabled={props.disabled}
      onClick={props.handleClick}
      style={{
        marginLeft: 15,
        textTransform: "none",
        font: "normal 700 14px Roboto",
        background: "#FFFFFF",
        border: props.disabled ? "1px solid #C0C0C0" : "1px solid #4B6EAF",
        width: "76px",
        height: "32px",
        padding: "4px, 6px, 4px, 8px",
        borderRadius: "4px",
        gap: "8px",
      }}
    >
      Prev
    </Button>
  );
};

export default PrevButton;
