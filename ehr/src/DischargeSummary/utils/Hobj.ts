import axios from "axios";

const responseTypes = {
  records: {
    adapt: function (tables: any[] | undefined) {
      if (tables === undefined) {
        return [];
      } else {
        return tables[0].records;
      }
    },
    responseFormat: "jsoncolvalue",
  },
  unorderedRecords: {
    adapt: function (tables: any) {
      return tables.reduce(function (unorderedRecords: any, table: any) {
        unorderedRecords[table.title] = table.records;
        return unorderedRecords;
      }, {});
    },
    responseFormat: "jsoncolvalue",
  },
  unorderedUnfilteredRecords: {
    adapt: function (tables: any) {
      return !tables.errorMsg && tables.length > 0
        ? tables.reduce(function (unorderedRecords: any, table: any) {
            var title = "data";
            if (table.title) title = table.title;
            unorderedRecords[title] = {
              type: table.type,
              header: table.fields,
              data: table.records.slice(1),
            };
            return unorderedRecords;
          }, {})
        : tables;
    },
    responseFormat: "jsonarray",
  },
  orderedRecords: {
    adapt: function (tables: any) {
      return tables.map(function (table: any) {
        return {
          title: table.title,
          records: table.records,
        };
      });
    },
    responseFormat: "jsoncolvalue",
  },
  table: {
    adapt: function (tables: any) {
      var table = tables[0];
      return {
        title: table.title,
        fields: table.fields,
        records: table.records.slice(1),
      };
    },
    responseFormat: "jsonarray",
  },
  tables: {
    adapt: function (tables: any) {
      return tables.map(function (table: any) {
        return {
          title: table.title,
          fields: table.fields,
          records: table.records.slice(1), //slice should be done server side
        };
      });
    },
    responseFormat: "jsonarray",
  },
};

const prefixColons = (obj: any) => {
  return Object.keys(obj).reduce(function (newObj: any, current) {
    newObj[":" + current] = obj[current];
    return newObj;
  }, {});
};

const copyTo = (
  dest: { [keyof: string]: any },
  source: { [keyof: string]: any },
  names: string[]
): { [keyof: string]: any } => {
  for (
    let name: string, i: number = 0, n: number = names ? names.length : 0;
    i < n;
    i++
  ) {
    name = names[i];
    if (source.hasOwnProperty(name)) {
      dest[name] = source[name];
    }
  }

  return dest;
};

const ArrayFindBy = (array: any, fn: any, scope: any) => {
  var i = 0,
    len = array.length;

  for (; i < len; i++) {
    if (fn.call(scope || array, array[i], i)) {
      return array[i];
    }
  }
  return null;
};

const adaptHobjResponse = (response: any) => {
  var someErrorTable = ArrayFindBy(
    response,
    function (table: any) {
      return "ErrorMsg" in table;
    },
    response
  );
  if (someErrorTable) {
    return {
      errorMsg: someErrorTable.ErrorMsg,
    };
  }

  if (!response[0]?.Header) {
    return {
      tables: [],
    };
  }

  const ret: any[] = response.map(function (table: any) {
    return {
      title: table.ResId,
      fields: table.Header,
      type: table.Type,
      records: table.Result || [],
    };
  });

  return ret;
};

const createHobjRequest = (request: { [keyof: string]: any }) => {
  var hobj = request.hobj,
    params = request.params || {},
    dbs = request.dbs || [],
    dbpath = request.dbpath || "",
    responseFormat = request.responseFormat,
    campus = request.campus || cci.cfg.campus;

  return copyTo(
    {
      type: "HOBJ",
      HOBJ: hobj,
      qargv: prefixColons(params),
      campus: campus,
      args: {
        editinfo: false,
        ropen: false,
        start: 0,
        end: 2147483647,
        SQLDB: false,
      },
      dbs: dbs,
      dbpath: dbpath,
      resultformat: responseFormat,
    },
    request,
    ["init", "query", "output"]
  );
};

const hobjRequest = async (request: any) => {
  const useWhitelist = request.useWhitelist;

  var theRequest = createHobjRequest(
    copyTo({}, request, [
      "hobj",
      "params",
      "dbs",
      "dbpath",
      "responseFormat",
      "init",
      "query",
      "output",
      "campus",
    ])
  );

  const url = useWhitelist
    ? cci.cfg.query.whitelistHobj.request
    : cci.cfg.query.hobj.request;
  let params = {
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
  };
  let data = JSON.stringify({ hobjRequest: JSON.stringify(theRequest) });
  let rawResponse;
  if (request.timeout) {
    rawResponse = await axios.post(url, data, {
      ...params,
      timeout: request.timeout,
    });
  } else {
    rawResponse = await axios.post(url, data, { ...params });
  }
  const content = await rawResponse!.data;
  return adaptHobjResponse(content);
};

const singleRequest = (request: any) => {
  const responseType = request.responseType;
  const adaptedRequest = {
    ...request,
    responseFormat: responseType.responseFormat,
  };
  return hobjRequest(adaptedRequest).then(responseType.adapt);
};

export const requestUnorderedUnfilteredRecords = (request: any) => {
  const adaptedRequest = {
    ...request,
    responseType: responseTypes.unorderedUnfilteredRecords,
  };
  return singleRequest(adaptedRequest);
};
