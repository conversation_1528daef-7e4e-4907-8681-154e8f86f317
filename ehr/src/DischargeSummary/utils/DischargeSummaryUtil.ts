import { isEmpty } from "lodash";
import { serverRequest } from "./DataApi";

const weekDay = ["Sun.", "Mon.", "Tue.", "Wed.", "Thu.", "Fri.", "Sat."];

enum ENCOUNTER_ACTIONS {
  ADMIT = "admit",
  DISCHARGE = "discharge",
  CHECKIN = "checkin",
  CHECKOUT = "checkout",
}

export const visitTypeMap: { [key: string]: string } = {
  Outpatient: "Office Visit",
  Inpatient: "Admission",
  Emergency: "ED Visit",
};

export const ENCOUNTER_ACTION_LABELS: Record<string, string> = {
  [ENCOUNTER_ACTIONS.ADMIT]: "ADMIT",
  [ENCOUNTER_ACTIONS.DISCHARGE]: "DISCH",
  [ENCOUNTER_ACTIONS.CHECKIN]: "CHKIN",
  [ENCOUNTER_ACTIONS.CHECKOUT]: "CHKOUT",
};

export const removeDuplicateRows = (allitems: any) => {
  let rows: any = [];
  allitems.forEach((element: any) => {
    if (
      !rows.some((row: any) => {
        return JSON.stringify(row) === JSON.stringify(element);
      })
    ) {
      rows.push(element);
    }
  });
  return rows;
};

export const nullorblank = (str: any) => {
  let toStr = str;
  if (typeof toStr === "number") toStr = str.toString();
  return isEmpty(toStr) || /^\s*$/.test(toStr);
};

const jsonParse = (str: any) => {
  try {
    return JSON.parse(str);
  } catch (e) {
    return str;
  }
};

export const parsedData = (data: any) => {
  return data.map((item: any) => {
    if (typeof item.recaction === "string") {
      item.recaction = jsonParse(item.recaction);
    }

    if (typeof item._data0 === "string") {
      item._data0 = jsonParse(item._data0);
    }

    if (typeof item._erxdata === "string") {
      item._erxdata = jsonParse(item._erxdata);
      if (typeof item._erxdata.preferred_pharmacy === "string") {
        item._erxdata.preferred_pharmacy = jsonParse(
          item._erxdata.preferred_pharmacy
        );
      }
    }
    return item;
  });
};

export type AnyDateType = Date | string | number | null | undefined;

export function toDateOrNull(date?: AnyDateType) {
  if (date == null) return null;
  let dateArg;
  if (typeof date === "number") {
    dateArg = date * 1000;
  } else if (typeof date === "string" && String(parseInt(date)) === date) {
    // If the date string represents a number (unix time)
    dateArg = parseInt(date) * 1000;
  } else {
    dateArg = date;
  }
  const asDate = new Date(dateArg);
  return isNaN(asDate.getTime()) ? null : asDate;
}

export function padTo2Digits(num: number) {
  return num.toString().padStart(2, "0");
}

function formatDate(date: AnyDateType, type: "short" | "long" = "long") {
  const asDate = toDateOrNull(date);
  if (asDate == null) return "";
  if (type === "long")
    return `${weekDay[asDate.getDay()]} ${padTo2Digits(asDate.getMonth() + 1)}/${padTo2Digits(
      asDate.getDate()
    )}/${asDate.getFullYear()}`;
  return asDate.toLocaleDateString();
}

export function formatDateTime(
  date: AnyDateType,
  type: "short" | "long" = "long",
  comma: boolean = false
) {
  return `${formatDate(date, type)}${comma ? "," : ""} ${formatTime(
    date,
    type
  )}`;
}

function formatTime(date: AnyDateType, type: "short" | "long" = "long") {
  const asDate = toDateOrNull(date);
  if (asDate == null) return "";
  if (type === "long")
    return padTo2Digits(asDate.getHours()) + padTo2Digits(asDate.getMinutes());
  return asDate.toLocaleTimeString();
}

export const getChoiceData = async (
  chc: string,
  setData: (data: any[]) => void
) => {
  const onSuccess = (rsp: any) => {
    let chc = rsp.choicelist ? rsp.choicelist : rsp;
    let data = chc.data as any[];
    setData(data);
  };

  const onFailure = (error: string) => {
    setData([]);
  };

  let params = { chc };
  serverRequest("getchoice2", params, onSuccess, onFailure);
};

export const convertLocation = (choices: any[], location: string) => {
  let displayLocation = "";
  const currentRow = (choices ?? []).find((choice) => choice[1] === location);
  if (currentRow) {
    displayLocation = `${currentRow[6]} - ${currentRow[1]}`;
  }
  return displayLocation;
};

export const convertEnctypeToCat = (
  encTypesCategory: any[],
  encTypes: string
) => {
  let encounterCategory: string = "";
  const encounterTypeRow = encTypesCategory.find((row) => row[0] === encTypes);
  if (encounterTypeRow) {
    encounterCategory = encounterTypeRow[1];
  }
  return encounterCategory;
};

export const findDispByName = (name: string) => {
  const result = cci.cfg.allcampuslist.find((item: any) => item.name === name);
  return result ? result.disp : "";
};

export const convertFacilityAbbrToName = (choices: any[], name: string) => {
  let facilityName = "";
  const currentRow = (choices ?? []).find((choice) => choice[3] === name);
  if (currentRow) {
    facilityName = currentRow[1];
  }
  return facilityName;
};

export const cmToFeetAndInches = (cm: any) => {
  const totalInches = cm * 0.393701;

  const feet = Math.floor(totalInches / 12);
  const inches = Math.round(totalInches % 12);

  return `${feet}'${inches}"`;
};

export const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }

  return rows;
};

export const formatMaterialMetaData = (responseData: any) => {
  return {
    ...responseData,
    regularTitle: responseData?.metadata?.regularTitle || "",
    metadata: {
      language: responseData?.metadata?.language || "",
    },
  };
};

const entryFields: any = [
  "name",
  "status",
  "comments",
  "onsetdate",
  "resolveddate",
  "snomedcode",
  "icd10code",
  "materialid",
];
export const diagnosesDBitemsMap: any = {
  status: "diag_status.MGDB",
  name: "diag_name.MGDB",
  onsetdate: "diag_onsetDT.MGDB",
  resolveddate: "diag_resolvedDT.MGDB",
  comments: "diag_annotation.MGDB",
  snomedcode: "diag_SNOMEDcode.MGDB",
  icd10code: "diag_icd10code.MGDB",
  probid: "prob_ID.MGDB",
  materialid: "Educational Materials",
};

export const AddittionalMaterialDBitemsMap: any = {
  materialid: "Educational Materials",
};
export const getSaveData = (category: any, rowData: any) => {
  let saveData: any = [];
  const curEntryFields =
    category === "Diagnoses" ? [...entryFields, "probid"] : ["materialid"];
  // Copy entry fields before sending to the server
  const newKey = Cci.RunTime.getEncounterInfo().admitkey;
  rowData.forEach((entry: any) => {
    Object.keys(entry).forEach((field) => {
      if (curEntryFields.includes(field)) {
        const curjit =
          category === "Diagnoses"
            ? diagnosesDBitemsMap[field]
            : AddittionalMaterialDBitemsMap[field];
        const curnit = entry["nit"];
        const existrow = saveData.find(
          (item: any) => item.jit === curjit && item.nit === curnit
        );
        if (!existrow && entry.data_source !== "VISTA") {
          saveData.push({
            data: entry[field],
            jit: curjit,
            nit: curnit,
            ks: Number(curnit) === -1 ? newKey : entry["key"],
          });
        }
      }
    });
  });
  return saveData;
};
