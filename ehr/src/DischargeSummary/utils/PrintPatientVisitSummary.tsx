import { printPatientVisitSummary } from "./DataApi";

export default function printPatientVisitSummaryData(
  params: any,
  landscape: boolean,
  showPrintError: null | Function,
  setShowPrintToast: (show: boolean) => void
) {
  Cci.RunTime.showLoadMask();
  if (Cci.RunTime.isWebsockPrintReady()) {
    Cci.util.RunTime.startPrintJob(
      "Reports",
      cci.cfg.query.dischargesummary.printPatientVisitSummary,
      params,
      {
        landscape: landscape,
        hideOptions: true,
        printDoneCallback: () => setShowPrintToast(true),
      }
    );
    return Promise.resolve();
  }

  return printPatientVisitSummary(params).then((rst) => {
    Cci.RunTime.hideLoadMask();
    var file, win;
    if (
      rst.success &&
      rst.success === true &&
      rst.result &&
      /.pdf/.test(rst.result)
    ) {
      file = rst.result;
      win = Cci.window.PdfPreviewWindow.getPreviewWindow({
        fileuri:
          cci.cfg.query.cps.preview + "?file=" + encodeURIComponent(file),
        requestURL:
          params.url || cci.cfg.query.dischargesummary.printPatientVisitSummary,
        printParams: params,
        landscape: landscape,
        hideOptions: true,
        printDoneCallback: () => setShowPrintToast(true),
      });
      win.show();
    } else {
      var msg = rst.errorMsg ? rst.errorMsg : "Unknown Error!";
      showPrintError && showPrintError(msg);
    }
  });
}
