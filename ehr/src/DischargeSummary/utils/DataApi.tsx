/**
 * DataAPI.tsx
 *
 * @author: CCI
 * @description: Data API to get Patient Visit Summary data from server */

import { postData, fetchData } from "@cci-monorepo/common";
import Config from "@cci-monorepo/config/Config";
import { requestUnorderedUnfilteredRecords } from "./Hobj";
import { formatMaterialMetaData } from "./DischargeSummaryUtil";

/**
 * Generic YCQL request
 * @params {string} hobj
 * @params {object} params
 * @params {callback} onSuccess
 * @params {callback} onFailure
 * @returns nothing
 */
export const serverRequest = async (
  hobj: string,
  params: any,
  onSuccess: (data: any) => void,
  onFailure: (error: any) => void
) => {
  let jsonData: any = {};
  const encounterInfo = Cci.RunTime.getEncounterInfo();
  try {
    if (hobj === "pld/problems") {
      let parsedMrn = Cci.util.Patient.getMrn().replace(/-/g, ""),
        lastChar = parsedMrn.charAt(parsedMrn.length - 1),
        patpath = "vol" + lastChar + "/" + parsedMrn;
      jsonData = await requestUnorderedUnfilteredRecords({
        hobj: hobj,
        noBatch: true,
        visitkey: encounterInfo.visitkey,
        staffid: Cci.util.Staff.getSid(),
        params: {
          ccitoken: Config.webtoken,
          endtime: encounterInfo.DischTime ? encounterInfo.DischTime : "-1",
          cursid: Cci.util.Staff.getSid(),
          curpatid: Cci.RunTime.getEncounterInfo().patid,
          patpath: patpath,
          ssn: params.ssn ? params.ssn : null,
          isreg: params.isreg ? params.isreg : null,
        },
        campus: params.campus,
        dbs: params.dbs,
      });
    } else {
      params.ccitoken = Config.webtoken;
      jsonData = await requestUnorderedUnfilteredRecords({
        hobj: hobj,
        noBatch: true,
        dbs: [Cci.util.Patient.getDbpath()],
        visitkey: encounterInfo.visitkey,
        staffid: Cci.util.Staff.getSid(),
        params: params,
      });
    }
    if (!jsonData.errorMsg) {
      if (onSuccess) {
        onSuccess(jsonData);
      }
    } else {
      if (onFailure) {
        onFailure(jsonData.errorMsg);
      }
    }
  } catch (error) {
    if (onFailure) {
      onFailure(error);
    } else {
      console.error(error);
    }
  }
  return jsonData;
};

export const printPatientVisitSummary = (params: object) => {
  const url: string = "DischargeSummary/printPatientVisitSummary";
  return postData(url, params);
};

export const getProblems = (
  params: any,
  onSuccess: (data: any) => void,
  onFailure: (error: any) => void
) => {
  return serverRequest("pld/problems", params, onSuccess, onFailure);
};

export const savePatInstructions = (params: object) => {
  const url: string = "DischargeSummary/savePatInstructions";
  return postData(url, params);
};

export const getPatInstructions = (params: object) => {
  const url: string = "DischargeSummary/getPatInstructions";
  return postData(url, params);
};

export const getPatVitals = (params: object) => {
  const url: string = "DischargeSummary/getPatVitals";
  return postData(url, params);
};

export const getTokenConfig = () => {
  const url: string = "DischargeSummary/getTokenConfig";
  return fetchData(url);
};

export const getUpcomingAppointments = (params: object) => {
  const url: string = "Ess/getAppointmentsByMrn";
  return postData(url, params);
};

export class WebMdApiError extends Error {
  constructor(message = "WebMd API request failed") {
    super(message);
    this.name = "WebMdApiError";
  }
}
let webMDToken: string | null = null;
let tokenExpiresAt: number | null = null;

export const getWebMDToken = async (): Promise<string | null> => {
  try {
    let ret = await getTokenConfig();
    if (!ret || !ret.success || !ret.data) {
      throw new WebMdApiError("Token config request failed");
    }

    const url = `https://identity.krames.com/connect/token`;
    const body = new URLSearchParams({
      client_id: ret.data.client_id,
      client_secret: ret.data.client_secret,
      grant_type: ret.data.grant_type,
      scope: ret.data.scope,
    });

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body,
    });

    if (!response.ok) throw new WebMdApiError("Token API response not ok");

    const data = await response.json();

    if (data?.access_token && data?.expires_in) {
      webMDToken = data.access_token;
      tokenExpiresAt = Date.now() + data.expires_in * 1000 - 5000;
      return webMDToken;
    } else {
      throw new WebMdApiError("Invalid token response structure");
    }
  } catch (e) {
    throw new WebMdApiError("Token request failed");
  }
};

export const fetchWithAuth = async (
  url: string,
  options: RequestInit
): Promise<Response> => {
  if (!webMDToken || !tokenExpiresAt || Date.now() >= tokenExpiresAt) {
    webMDToken = await getWebMDToken();
  }

  const fetchWithToken = async (token: string): Promise<Response> => {
    return await fetch(url, {
      ...options,
      headers: {
        ...(options.headers || {}),
        Authorization: `Bearer ${token}`,
      },
    });
  };

  let response = await fetchWithToken(webMDToken!);

  if (response.status === 401) {
    try {
      webMDToken = await getWebMDToken();
      response = await fetchWithToken(webMDToken!);
    } catch {
      throw new WebMdApiError("Re-authentication failed");
    }
  }
  if (response.status === 404) {
    throw new WebMdApiError("No education material to preview!");
  }

  if (!response.ok) {
    throw new WebMdApiError("Failed to load education material.");
  }

  return response;
};
export const searchWebMDTerms = async (itemName: string): Promise<any> => {
  const url = `https://api.krames.com/v3/content/search?terms=${encodeURIComponent(itemName)}&select=Title,ContentTypeID,ContentID&mediatype=Document`;
  const response = await fetchWithAuth(url, {
    method: "GET",
    headers: {
      "Accept-Language": "en",
      "Content-Type": "application/json",
    },
  });

  return await response.json();
};

export const getMaterialDetail = async (
  params: string,
  language: "en" | "es" = "en"
): Promise<any> => {
  const url = `https://api.krames.com/v3/content/${params}`;
  const response = await fetchWithAuth(url, {
    method: "GET",
    headers: {
      "Accept-Language": language,
      "Content-Type": "application/json",
    },
  });

  const data = await response.json();
  return formatMaterialMetaData(data);
};
export const saveMaterialInfo = async (params: any) => {
  return await Cci.util.Hobj.requestUnorderedRecords({
    hobj: "educationmaterial",
    init: "init.save.ycql",
    output: "output.save.ycql",
    params,
  });
};
export const getMaterialInfo = async (params: any) => {
  return await Cci.util.Hobj.requestUnorderedRecords({
    hobj: "educationmaterial",
    init: "init.ycql",
    output: "output.ycql",
    params,
  });
};
