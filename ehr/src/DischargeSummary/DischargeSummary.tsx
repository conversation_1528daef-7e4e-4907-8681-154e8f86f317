/**
 * DischargeSummary.tsx
 *
 * @description Main component for Discharge Summary module
 * <AUTHOR>
 */
import { ThemeProvider, styled } from "@mui/material";
import { Provider } from "jotai";
import EhrTheme from "../theme/theme";
import MainContent from "./MainContent";

const Root = styled("main")(({ theme }) => ({
  height: "100%",
  width: "100%",
  display: "flex",
  flexDirection: "column",
}));

const MainBox = styled("main")(({ theme }) => ({
  position: "relative", // Make the module fit in webframe Tab
  width: "100%",
  overflow: "auto",
  display: "flex",
  flexDirection: "row",
  flexGrow: 1,
  height: 0,
  padding: "15px 0px 0px 10px",
}));

export default function DischargeSummary() {
  return (
    <ThemeProvider theme={EhrTheme}>
      <Provider>
        <Root>
          <MainBox>
            <MainContent />
          </MainBox>
        </Root>
      </Provider>
    </ThemeProvider>
  );
}
