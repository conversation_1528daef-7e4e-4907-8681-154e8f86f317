/**
 * MainContent.tsx
 *
 * @description Main content for Discharge Summary module
 * <AUTHOR>
 */
import { useEffect, useState } from "react";
import { Box, Typography, Stack } from "@mui/material";
import {
  Button,
  Checkbox,
  MultilineInputField,
  CciToast,
  CciMsgDialog,
} from "@cci/mui-components";
import { Increase, Decrease, Bold } from "./icons";
import PrintPvs from "./components/PrintPvs";
import { savePatInstructions, getPatInstructions } from "./utils/DataApi";
import { PatientEducationMaterial } from "./components/educationMaterial";
import {
  openErrorDialogAtom,
  additionalMaterialListAtom,
  selectedAdditionalMaterial<PERSON>tom,
  diagnosis<PERSON>ist<PERSON>tom,
  selectedRowDiagnosisAtom,
  checkedPrintMaterialAtom,
  toastAtom,
  checkedDeclinedPrintVisitSummaryAtom,
} from "./context/EducationAtoms";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Todos } from "@cci-monorepo/AmbPatOverview/components/todos/Todos";
import { USERCONTEXT } from "@cci-monorepo/AmbPatOverview/components/common/Constants";
import { userContextAtom } from "@cci-monorepo/AmbPatOverview/context/CommonAtoms";

export default function MainContent() {
  const setUserContext = useSetAtom(userContextAtom);
  const [patInstructions, setPatInstructions] = useState<string>("");
  const [initPatInstructions, setInitPatInstructions] = useState<string>("");
  const [checkedPrintMaterial, setCheckedPrintMaterial] = useAtom(
    checkedPrintMaterialAtom
  );
  const [
    checkedDeclinedPrintVisitSummary,
    setCheckedDeclinedPrintVisitSummary,
  ] = useAtom(checkedDeclinedPrintVisitSummaryAtom);
  const diagnosisList = useAtomValue(diagnosisListAtom);
  const additionalMaterialList = useAtomValue(additionalMaterialListAtom);
  const setSelectedRowDiagnosis = useSetAtom(selectedRowDiagnosisAtom);
  const setSelectedAdditionalMaterial = useSetAtom(
    selectedAdditionalMaterialAtom
  );
  const [openErrorDialog, setOpenErrorDialog] = useAtom(openErrorDialogAtom);
  const [toast, setToast] = useAtom(toastAtom);

  const getPatInstructionsData = () => {
    getPatInstructions({
      encdbpath: Cci.util.Patient.getDbpath(),
    }).then((ret: any) => {
      if (ret.success) {
        setPatInstructions(ret?.data || "");
        setInitPatInstructions(ret?.data || "");
      }
    });
  };

  const onSave = () => {
    savePatInstructions({
      encdbpath: Cci.util.Patient.getDbpath(),
      patInstructions: patInstructions,
    }).then((ret: any) => {
      if (ret?.ExecOk !== false) {
        setToast({
          type: "success",
          text: "Patient Instructions Saved",
          open: true,
        });
        getPatInstructionsData();
      }
    });
  };
  useEffect(() => {
    if (Cci.util.Staff.getLicensetype() === "RN") {
      setUserContext(USERCONTEXT.NURSE);
    } else {
      setUserContext(USERCONTEXT.PROVIDER);
    }
    getPatInstructionsData();
  }, []);

  const checkIncludeMaterial = (checked: boolean) => {
    if (checked) {
      setSelectedAdditionalMaterial(
        additionalMaterialList
          .filter((item) => item?.materialContent?.id)
          .map((row) => row.id)
      );
      setSelectedRowDiagnosis(
        diagnosisList
          .filter((item) => item?.materialContent?.id)
          .map((row) => row?.id)
      );
    } else {
      setSelectedAdditionalMaterial([]);
      setSelectedRowDiagnosis([]);
    }
  };

  useEffect(() => {
    checkIncludeMaterial(checkedPrintMaterial);
  }, [checkedPrintMaterial]);

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "row",
        padding: "15px",
      }}
    >
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <PrintPvs patInstructions={initPatInstructions} />
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            marginTop: "10px",
          }}
        >
          <Checkbox
            color="success"
            checked={checkedDeclinedPrintVisitSummary}
            onChange={(e: any, checked: boolean) =>
              setCheckedDeclinedPrintVisitSummary(checked)
            }
          ></Checkbox>
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: "16px",
              color: "#000",
            }}
          >
            Patient Declined Printable Visit Summary
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            marginTop: "10px",
          }}
        >
          <Checkbox
            color="success"
            checked={checkedPrintMaterial}
            onChange={(e: any, checked: boolean) =>
              setCheckedPrintMaterial(checked)
            }
          />
          <Typography
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: "16px",
              color: "#000",
            }}
          >
            Include Education Material
          </Typography>
        </Box>
        <Box sx={{ width: "760px" }}>
          <PatientEducationMaterial />
          <Typography
            sx={{
              color: "#000",
              fontFamily: "Roboto",
              fontSize: "18px",
              fontWeight: "700",
              marginTop: "20px",
            }}
          >
            Patient Instructions
          </Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              width: "760px",
              marginTop: "15px",
            }}
          >
            <Stack direction="row" spacing={1}>
              <Increase />
              <Decrease />
              <Bold />
            </Stack>
            <Button
              color="secondary"
              disabled={initPatInstructions === patInstructions}
              onClick={onSave}
            >
              Save
            </Button>
          </Box>
          <MultilineInputField
            sx={{ marginTop: "10px" }}
            data-testid="patient-instructions-notes-field"
            id="patientInstructions-notes"
            rows={5}
            dataType="text"
            data={patInstructions}
            setData={(data: string) => {
              setPatInstructions(data);
            }}
          />
        </Box>
      </Box>
      <Box
        sx={{
          flex: 1,
        }}
      >
        <Typography
          sx={{
            color: "#000",
            fontFamily: "Roboto",
            fontSize: "18px",
            fontWeight: "700",
            padding: "7px 4px",
            marginBottom: "8px",
          }}
        >
          Close Encounter
        </Typography>
        <Todos hasSignButton={true} />
      </Box>
      <CciToast
        type={toast.type}
        text={toast.text}
        open={toast.open}
        setOpen={(open: any) =>
          setToast({
            ...toast,
            open: open,
          })
        }
        anchorOrigin={{ horizontal: "center", vertical: "bottom" }}
        autoHideDuration={3000}
        alertSx={{ width: "max-content" }}
      />
      <CciMsgDialog
        title={openErrorDialog.title}
        text={openErrorDialog.text}
        open={openErrorDialog.open}
        disableEnforceFocus
        setOpen={(open: any) =>
          setOpenErrorDialog({
            ...openErrorDialog,
            open: open,
          })
        }
        sx={{
          ".MuiDialogTitle-root": {
            padding: "16px 24px",
          },
          ".MuiDialogActions-root": {
            padding: "10px 24px 20px",
          },
        }}
        type="Error"
      />
    </Box>
  );
}
