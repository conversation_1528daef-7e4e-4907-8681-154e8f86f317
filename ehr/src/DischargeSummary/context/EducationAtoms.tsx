import { atom } from "jotai";

export const diagnosisListAtom = atom<any[]>([]);
export const additionalMaterialListAtom = atom<any[]>([]);
export const selectedRowDiagnosisAtom = atom<any[]>([]);
export const selectedAdditionalMaterialAtom = atom<any[]>([]);
export const drawerOpenAtom = atom<boolean>(false);
export const materialContentAtom = atom<any>(null);

export const previewRowAtom = atom<any>(null);
export const searchValueAtom = atom<string>("");

export const loadingAtom = atom<boolean>(false);
export const checkedPrintMaterialAtom = atom<boolean>(true);
export const checkedDeclinedPrintVisitSummaryAtom = atom<boolean>(false);
export const openErrorDialogAtom = atom<{
  text: string;
  open: boolean;
  title?: string;
}>({
  title: "Error",
  text: "",
  open: false,
});

export const toastAtom = atom<{
  type: "error" | "success" | "warning";
  text: string;
  open: boolean;
}>({
  type: "success",
  text: "",
  open: false,
});

export const refreshAtom = atom<{
  diagnosisFlag: boolean;
  additionalFlag: boolean;
}>({ diagnosisFlag: false, additionalFlag: false });
