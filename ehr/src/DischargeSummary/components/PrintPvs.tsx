import { useEffect, useState, useCallback, SetStateAction } from "react";
import { Button, Box, Typography } from "@mui/material";
import { CciToast } from "@cci/mui-components";
import dayjs from "dayjs";
import { postData, convertResultToObjectArray } from "@cci-monorepo/common";
import printPatientVisitSummaryData from "../utils/PrintPatientVisitSummary";
import {
  getProblems,
  getPatVitals,
  getUpcomingAppointments,
  serverRequest,
} from "../utils/DataApi";
import {
  nullorblank,
  parsedData,
  visitTypeMap,
  ENCOUNTER_ACTION_LABELS,
  toDateOrNull,
  formatDateTime,
  getChoiceData,
  convertLocation,
  convertEnctypeToCat,
  removeDuplicateRows,
  findDispByName,
  convertFacilityAbbrToName,
  cmToFeetAndInches,
} from "../utils/DischargeSummaryUtil";
import { useAtomValue } from "jotai";
import {
  additionalMaterialList<PERSON>tom,
  checkedDeclinedPrintVisitSummaryAtom,
  checkedPrintMaterialAtom,
  diagnosisList<PERSON>tom,
  selectedAdditionalMaterialAtom,
  selectedRowDiagnosisAtom,
} from "../context/EducationAtoms";
import { AlcoholInfoIcon } from "@cci-monorepo/Pld/components/socialHistory/alcohol/assets";

interface PrintPvsProps {
  patInstructions: string;
}

export default function PrintPvs({ patInstructions }: PrintPvsProps) {
  const [problemsList, setProblemsList] = useState<any[]>([]);
  const [hl7Data, setHl7Data] = useState<any>({});
  const [immuDataList, setImmuDataList] = useState<any[]>([]);
  const [regId, setRegId] = useState<string>("");
  const [patientDemoInfo, setPatientDemoInfo] = useState<any>({});
  const [vitalsInfo, setVitalsInfo] = useState<any>({});
  const [diagnosisList, setDiagnosisList] = useState<any[]>([]);
  const [proceduresList, setProceduresList] = useState<any[]>([]);
  const [upcomingAppointments, setUpcomingAppointments] = useState<any[]>([]);
  const [referralOrders, setReferralOrders] = useState<any[]>([]);
  const [diagnosticTestOrders, setDiagnosticTestOrders] = useState<any[]>([]);
  const [stopOrders, setStopOrders] = useState<any[]>([]);
  const [curAtiveOrders, setCurAtiveOrders] = useState<any[]>([]);
  const [newOrders, setNewOrders] = useState<any[]>([]);
  const [visitDetails, setVisitDetails] = useState<any>({});
  const [encTypesCategory, setEncTypesCategory] = useState<any[]>([]);
  const [facility, setFacility] = useState<any[]>([]);
  const [showPrintToast, setShowPrintToast] = useState(false);
  const [showErrorToast, setShowErrorToast] = useState(false);
  const [errorToastText, setErrorToastText] = useState("");

  const checkedPrintMaterial = useAtomValue(checkedPrintMaterialAtom);
  const checkedDeclinedPrintVisitSummary = useAtomValue(
    checkedDeclinedPrintVisitSummaryAtom
  );
  const additionalMaterialList = useAtomValue(additionalMaterialListAtom);
  const diagnosisMaterialList = useAtomValue(diagnosisListAtom);
  const selectedRowDiagnosisIds = useAtomValue(selectedRowDiagnosisAtom);
  const selectedAdditionalMaterialIds = useAtomValue(
    selectedAdditionalMaterialAtom
  );

  const getProblemsData = () => {
    const onFailure = (error: string) => {
      setProblemsList([]);
      setDiagnosisList([]);
    };
    const dbpaths = [
      { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
    ];
    let promises = dbpaths.map((item: any) => {
      const params = { campus: item.campus, dbs: item.dbpath };
      return getProblems(params, () => {}, onFailure);
    });
    Promise.all(promises).then((ret: any) => {
      if (ret && Array.isArray(ret) && ret.length > 0) {
        let problemsData = ret[0].problems_data?.data;
        let diagnosisData = ret[0].diagnosis_data?.data;

        if (problemsData?.length > 0) {
          problemsData = removeDuplicateRows(problemsData);
          problemsData = problemsData.filter(
            (record: any) => record[5] === "Active"
          );
          const problemsList = problemsData.map((item: any) => {
            const [problemName, problemOnsetDate, problemResolvedDate] =
              item.slice(7, 10);
            return {
              problemName: problemName || "",
              problemOnsetDate: problemOnsetDate || "",
              problemResolvedDate: problemResolvedDate || "",
            };
          });

          setProblemsList(problemsList);
        }
        if (diagnosisData?.length > 0) {
          diagnosisData = removeDuplicateRows(diagnosisData);
          diagnosisData = diagnosisData.filter(
            (record: any) => record[5] === "Active"
          );
          const diagnosisList = diagnosisData.map((item: any) => {
            return {
              diagnosisName: item[7] || "",
            };
          });
          setDiagnosisList(diagnosisList);
        }
      }
    });
  };

  const getProceduresData = () => {
    const onFailure = (error: any) => {
      setProceduresList([]);
    };
    const dbpaths = [
      { campus: cci.cfg.campus, dbpath: Cci.util.Patient.getDbpath() },
    ];
    let promises = dbpaths.map((item: any, index) => {
      const params = { campus: item.campus, dbs: item.dbpath };
      return serverRequest("pld/procedure", params, () => {}, onFailure);
    });
    Promise.all(promises).then((ret: any) => {
      if (ret && Array.isArray(ret) && ret.length > 0) {
        let proceduresData = ret[0].data?.data;

        if (proceduresData?.length > 0) {
          proceduresData = removeDuplicateRows(proceduresData);
          proceduresData = proceduresData.filter(
            (record: any) => record[1] === "Active"
          );
          let proceduresList = proceduresData.map((item: any) => {
            return {
              procedureName: item[2] || "",
            };
          });
          setProceduresList(proceduresList);
        }
      }
    });
  };

  const formatDate = (d: any) => {
    return d ? dayjs(d, "YYYYMMDD").format("MM/DD/YYYY") : "n/a";
  };

  const parseHL7Data = (d: any) => {
    const fcs = d["forecast"] ?? [];

    const fc_rows = fcs.map((item: any) => ({
      vaccineType: item.vaccineType ?? "n/a",
      nextDoseDate: formatDate(item.nextDoseDate),
    }));

    return fc_rows;
  };

  const formatPat = (p: any) => {
    p["pat_addr"] =
      (p["streetAddr"] ? p["streetAddr"] + "," : "") +
      (p["cityAddr"] ? p["cityAddr"] + "," : "") +
      (p["stateAddr"] ? p["stateAddr"] + "," : "") +
      (p["zipCode"] ? p["zipCode"] + "," : "") +
      (p["country"] ? p["country"] : "");
    let pids: any = [];
    if (p["SR"]) {
      pids.push("SR:" + p["SR"]);
      p["pid"] = p["SR"];
    }
    if (p["MR"]) {
      pids.push("MR:" + p["MR"]);
      if (!p["pid"]) p["pid"] = p["MR"];
    }
    p["pids"] = pids.join(";");
    p["age"] = p["pat_bod"]
      ? dayjs().diff(dayjs(p["pat_bod"], "YYYYMMDD"), "year")
      : "";
    p["pat_bod"] = p["pat_bod"]
      ? dayjs(p["pat_bod"], "YYYYMMDD").format("DD MMM YYYY")
      : "";
  };

  const getForecastInfo = (pat: any) => {
    const onIISSuccess = (result: any) => {
      if (
        result &&
        result.data &&
        result.data.data &&
        result.data.data[0] &&
        result.data.data[0][0]
      ) {
        let jObj = JSON.parse(result.data.data[0]);
        setHl7Data(jObj);
      }
    };

    const onIISFailed = (error: string) => {
      console.error(error);
    };
    const params = {
      hl7type: "Z44",
      pat: JSON.stringify(pat),
    };
    serverRequest(
      "pld/immunizations/forecast",
      params,
      onIISSuccess,
      onIISFailed
    );
  };

  const getForecastData = () => {
    const onIISSuccess = (result: any) => {
      if (
        result &&
        result.data &&
        result.data.data &&
        result.data.data[0] &&
        result.data.data[0][0]
      ) {
        let jObj = JSON.parse(result.data.data[0]);
        if (jObj && jObj["type"]) {
          switch (jObj["type"]) {
            case "Z31":
              let pats = jObj["pid"];
              let availPats: any = [];
              if (pats && pats.length > 0) {
                pats.forEach((p: any) => {
                  formatPat(p);
                  availPats.push(p);
                });
                let idx = availPats.findIndex(
                  (i: any) => i.SR === Cci.util.Patient.getMrn()
                );
                getForecastInfo(availPats[idx]);
              }
              break;
            case "Z32":
              const params = {
                hl7type: "Z44",
                dbp: Cci.util.Patient.getDbpath(),
              };
              serverRequest(
                "pld/immunizations/forecast",
                params,
                onIISSuccess,
                onIISFailed
              );
              break;
            case "Z42":
              setHl7Data(jObj);
              break;
          }
        }
      }
    };
    const onIISFailed = (error: string) => {
      console.error(error);
    };
    const params = {
      hl7type: "Z34",
      dbp: Cci.util.Patient.getDbpath(),
    };

    serverRequest(
      "pld/immunizations/forecast",
      params,
      onIISSuccess,
      onIISFailed
    );
  };

  const getEncTypesCatData = async () => {
    const chc = "encounter_types_category";
    getChoiceData(chc, setEncTypesCategory);
  };

  const getFacilityData = async () => {
    const chc = "facility";
    getChoiceData(chc, setFacility);
  };

  const getEncounterData = () => {
    const dbpath = Cci.util.Patient.getDbpath();
    const encounterId = dbpath.substring(
      dbpath.lastIndexOf("/p") + 2,
      dbpath.length
    );
    postData("registration/getencounter", { regid: regId })
      .then((result: any) => {
        const admissionDetailList = convertResultToObjectArray(
          result?.[3]?.Result
        );
        const admissionDetails = admissionDetailList
          .filter(
            (detail: any) =>
              detail.encounterId === encounterId && detail.unit !== "REGUNIT"
          )
          .sort((a, b) => {
            if (a.encounterADTDT > b.encounterADTDT) return 1;
            else if (a.encounterADTDT < b.encounterADTDT) return -1;
            else return 0;
          });

        const getActionDetail = (label: string) => {
          const actionDetails = admissionDetails.filter((detail) =>
            detail.encounterADTType
              ?.toLowerCase()
              ?.startsWith(label.toLowerCase())
          );
          return actionDetails[actionDetails.length - 1];
        };

        const admitDetail = getActionDetail(ENCOUNTER_ACTION_LABELS.admit);
        const admitTime = formatDateTime(
          toDateOrNull(admitDetail?.encounterADTDT)
        );
        const dischargeDetail = getActionDetail(
          ENCOUNTER_ACTION_LABELS.discharge
        );
        const dischargeTime = formatDateTime(
          toDateOrNull(dischargeDetail?.encounterADTDT)
        );
        const checkinDetail = getActionDetail(ENCOUNTER_ACTION_LABELS.checkin);
        const checkinTime = formatDateTime(
          toDateOrNull(checkinDetail?.encounterADTDT)
        );
        const checkoutDetail = getActionDetail(
          ENCOUNTER_ACTION_LABELS.checkout
        );
        const checkoutTime = formatDateTime(
          toDateOrNull(checkoutDetail?.encounterADTDT)
        );

        const encDetailList = convertResultToObjectArray(result?.[0]?.Result);
        const currentEncDetail = encDetailList.filter(
          (detail: any) => detail.encounterDbPath === dbpath
        );
        if (currentEncDetail && currentEncDetail.length > 0) {
          const encounterCategory = convertEnctypeToCat(
            encTypesCategory,
            currentEncDetail[0].encounterType
          );
          const displayLocation = convertLocation(
            facility,
            currentEncDetail[0].encounterLocation
          );
          const visitDetails: any = {
            visitType: visitTypeMap[encounterCategory] ?? "",
            visitTime:
              encounterCategory === "Inpatient" ||
              encounterCategory === "Emergency"
                ? `${admitTime} - ${dischargeTime}`
                : `${checkinTime} - ${checkoutTime}`,
            clinician:
              encounterCategory === "Inpatient" ||
              encounterCategory === "Emergency"
                ? currentEncDetail[0].encounterAttdProvider || ""
                : currentEncDetail[0].encounterApptProvider || "",
            location: displayLocation,
          };
          setVisitDetails(visitDetails);
        }
      })
      .catch(() => {
        setVisitDetails({});
      });
  };

  const getRegId = () => {
    const params = { dbpath: Cci.util.Patient.getDbpath() };
    const onSuccess = (result: any) => {
      if (result && result.data) {
        const { data: rows } = result.data;
        if (rows && rows.length > 0) {
          const regId = rows[0]?.[0];
          setRegId(regId);
        }
      }
    };
    const onFailure = (error: string) => {
      setRegId("");
    };
    serverRequest("registration/getencounter", params, onSuccess, onFailure);
  };

  const getPatientDemoData = useCallback(() => {
    if (!regId) {
      return;
    }
    postData("registration/getpatientdemoinfo", {
      regid: regId,
      needheaderinfo: 0,
    })
      .then((result: any) => {
        const demoInfo = convertResultToObjectArray(result?.[0]?.Result);
        if (demoInfo && demoInfo.length > 0) {
          const patDemoInfo = demoInfo[0];
          setPatientDemoInfo({
            fullname: `${patDemoInfo.lastName || ""}, ${patDemoInfo.firstName || ""}`,
            dob: patDemoInfo.dob,
            mrn: patDemoInfo.mrn,
            phone: patDemoInfo.primaryPhone,
            email: patDemoInfo.email,
            address: `${patDemoInfo.address1 || ""} ${patDemoInfo.city || ""}, ${patDemoInfo.state || ""}, ${patDemoInfo.zip || ""}`,
          });
        }
      })
      .catch(() => {
        setPatientDemoInfo({});
      });
  }, [regId]);

  const getVitalsData = () => {
    getPatVitals({
      encdbpath: Cci.util.Patient.getDbpath(),
    })
      .then((ret: any) => {
        if (ret && ret.success) {
          let vitalsInfo = {
            bloodPressure: ret.bloodPressure ?? "",
            bmi: ret.bmi ?? "",
            heartRate: ret.heartRate ?? "",
            height: cmToFeetAndInches(ret.height) ?? "",
            pain: ret.pain ?? "",
            temp: `${ret.tempC || ""}°C(${ret.tempF || ""}°F)`,
            weightLbs: `${ret.weightLbs || ""} Ibs`,
            visitReason: ret.visitReason ?? "",
          };
          setVitalsInfo(vitalsInfo);
        } else {
          setVitalsInfo({});
        }
      })
      .catch(() => {
        setVitalsInfo({});
      });
  };

  const getUpcomingAppointmentsData = () => {
    getUpcomingAppointments({
      mrn: Cci.util.Patient.getMrn(),
      startDate: Date.now(),
    })
      .then((ret: any) => {
        if (
          ret.success &&
          ret.data &&
          Array.isArray(ret.data) &&
          ret.data.length > 0
        ) {
          const upcomingAppointments = ret.data.map((item: any) => {
            const formattedDate = Ext.Date.format(
              Ess.Util.newDate(item.startTime),
              "D. m/d/Y"
            );
            const formattedStartTime = Ext.Date.format(
              Ess.Util.newDate(item.startTime),
              "Hi"
            );
            const formattedEndTime = Ext.Date.format(
              Ess.Util.newDate(item.endTime),
              "Hi"
            );
            const formattedDateTime = `${formattedDate} · ${formattedStartTime} - ${formattedEndTime}`;
            const resources = JSON.parse(item.resources || "{}");
            const [campus, clinic] = item.facility?.split("_");
            const facilityName = findDispByName(campus);
            const clinicName = convertFacilityAbbrToName(facility, clinic);
            return {
              apptType: item.apptType ?? "",
              appTime: formattedDateTime,
              clinician: resources?.[0]?.name ?? "",
              location: facilityName + " - " + clinicName,
            };
          });
          setUpcomingAppointments(upcomingAppointments);
        } else {
          setUpcomingAppointments([]);
        }
      })
      .catch(() => {
        setUpcomingAppointments([]);
      });
  };

  const getExistingOrders = async () => {
    const params = {
      env: Cci.Env.getType(),
      sid: Cci.util.Staff.getSid().toString(),
    };
    const cpoe2Orders: { [key: string]: { [category: string]: any[] } } = {};
    let referralOrders: any = [];
    let diagnosticTestOrders: any = [];
    let labOrders: any = [];
    let imagingOrders: any = [];

    try {
      const cpoe2Response = await Cci.util.Hobj.requestUnorderedRecords({
        dbs: [Cci.util.Patient.getDbpath()],
        hobj: "cpoe2/getorders",
        noBatch: true,
        params,
      });
      Object.entries(cpoe2Response).forEach(([key, catData = [] as any[]]) => {
        const orderCategoryMapping: { [key: string]: any[] } = {};
        (catData as any[]).forEach((orderData) => {
          const { category, subcat } = orderData;
          if (!nullorblank(category) && !nullorblank(subcat)) {
            if (orderCategoryMapping[category] == null)
              orderCategoryMapping[category] = [];
            orderCategoryMapping[category].push(orderData);
          }
        });
        cpoe2Orders[key] = orderCategoryMapping;
      });

      if (
        cpoe2Orders.oe2_Consults &&
        Array.isArray(cpoe2Orders.oe2_Consults.Referral)
      ) {
        referralOrders = cpoe2Orders.oe2_Consults.Referral.map(
          (referralOrder) => {
            return {
              referralOrderName: referralOrder.name,
            };
          }
        );
        setReferralOrders(referralOrders);
      }
      if (cpoe2Orders.oe2_LAB && Array.isArray(cpoe2Orders.oe2_LAB.Lab)) {
        labOrders = cpoe2Orders.oe2_LAB.Lab.map((labOrder) => {
          return {
            diagnosticTestOrderName: `${labOrder.category}: ${labOrder.name}`,
          };
        });
      }
      if (cpoe2Orders.oe2_RAD && Array.isArray(cpoe2Orders.oe2_RAD.Imaging)) {
        imagingOrders = cpoe2Orders.oe2_RAD.Imaging.map((labOrder) => {
          return {
            diagnosticTestOrderName: `${labOrder.category}: ${labOrder.name}`,
          };
        });
      }
      diagnosticTestOrders = [...labOrders, ...imagingOrders];
      setDiagnosticTestOrders(diagnosticTestOrders);
    } catch (error) {
      setReferralOrders([]);
      setDiagnosticTestOrders([]);
    }
  };

  const getDmrOrders = async () => {
    let stopOrders = [];
    let continueOrders = [];
    let adjustedOrders = [];
    let newOrders = [];
    try {
      const response = await Cci.util.Hobj.requestUnorderedRecords({
        dbs: [Cci.util.Patient.getDbpath()],
        hobj: "medrec/dmr",
        noBatch: true,
        params: {
          env: Cci.Env.getType(),
        },
      });
      if (response && response.dmr) {
        const dmrOrders = parsedData(response.dmr);

        const newOrdersData = dmrOrders.filter(
          (order: any) =>
            (order.medsrc !== "HOMEMED" && order.recaction.Rx === 1) ||
            order.medsrc === "NEW"
        );
        newOrders = newOrdersData.map((order: any) => {
          const preferred_pharmacy = order?._erxdata?.preferred_pharmacy ?? "";
          const frequency = order?._erxdata?.frequency ?? "";
          const route = order?._erxdata?.route ?? "";

          return {
            orderName: order?.recname ?? "",
            howToTake: `${frequency} ${route}`,
            reasonToTake: order?._erxdata?.indication ?? "",
            pharmacyName: preferred_pharmacy?.name ?? "",
            pharmacyAddress: `${preferred_pharmacy?.addr1 ? preferred_pharmacy.addr1 + "," : ""} ${preferred_pharmacy?.city || ""} ${preferred_pharmacy?.state || ""} ${preferred_pharmacy?.zip || ""}`,
            pharmacyFax: preferred_pharmacy?.fax
              ? "Fax " + preferred_pharmacy.fax
              : "",
            pharmacyPhone: preferred_pharmacy?.phone
              ? "Phone " + preferred_pharmacy.phone
              : "",
          };
        });
        setNewOrders(newOrders);

        const adjustedOrdersData = dmrOrders.filter(
          (order: any) => order.medsrc === "HOMEMED" && order.recaction.Rx === 1
        );
        adjustedOrders = adjustedOrdersData.map((order: any) => {
          return {
            type: "ADJUSTED",
            orderName: order?.recname ?? "",
            howToTake: `${order?._erxdata?.frequency || ""} ${order?._erxdata?.route || ""}`,
            reasonToTake: order?._erxdata?.indication ?? "",
          };
        });

        const continueOrdersData = dmrOrders.filter(
          (order: any) => order.recaction.Resume === 1
        );
        continueOrders = continueOrdersData.map((order: any) => {
          return {
            type: "CONTINUE",
            orderName: order?.recname ? order?.recname : order?.name ?? "",
            howToTake: `${order?._data0?.frequency || ""} ${order?._data0?.route || ""}`,
            reasonToTake: order?._data0?.indication ?? "",
          };
        });
        const curAtiveOrders = [...continueOrders, ...adjustedOrders];
        setCurAtiveOrders(curAtiveOrders);

        const stopOrdersData = dmrOrders.filter(
          (order: any) =>
            order.medsrc === "HOMEMED" && order.recaction.Stop === 1
        );
        stopOrders = stopOrdersData.map((order: any) => {
          return {
            orderName: order?.recname ? order?.recname : order?.name ?? "",
            howToTake: `${order?._data0?.frequency || ""} ${order?._data0?.route || ""}`,
            reasonToTake: order?._data0?.indication ?? "",
          };
        });
        setStopOrders(stopOrders);
      }
    } catch (error) {
      setNewOrders([]);
      setCurAtiveOrders([]);
      setStopOrders([]);
    }
  };

  useEffect(() => {
    getProblemsData();
    getForecastData();
    getVitalsData();
    getProceduresData();
    getFacilityData();
    getExistingOrders();
    getDmrOrders();
    getEncTypesCatData();
    getRegId();
  }, []);

  useEffect(() => {
    if (facility.length > 0) {
      getUpcomingAppointmentsData();
    }
  }, [facility]);

  useEffect(() => {
    if (regId) {
      getPatientDemoData();
      if (encTypesCategory.length > 0 && facility.length > 0) {
        getEncounterData();
      }
    }
  }, [regId, encTypesCategory, facility]);

  useEffect(() => {
    setImmuDataList(parseHL7Data(hl7Data));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hl7Data]);

  const handlePrintPvs = () => {
    let patientVisitSummaryData: any = {};

    if (!checkedDeclinedPrintVisitSummary) {
      patientVisitSummaryData = {
        problemsList: problemsList,
        immuDataList: immuDataList,
        patientDemoInfo: patientDemoInfo,
        patInstructions: patInstructions,
        vitalsInfo: vitalsInfo,
        diagnosisList: diagnosisList,
        proceduresList: proceduresList,
        upcomingAppointments: upcomingAppointments,
        referralOrders: referralOrders,
        diagnosticTestOrders: diagnosticTestOrders,
        stopOrders: stopOrders,
        curAtiveOrders: curAtiveOrders,
        newOrders: newOrders,
        visitDetails: visitDetails,
      };
    }
    if (checkedPrintMaterial) {
      const materialList = [
        ...diagnosisMaterialList.filter((item) =>
          selectedRowDiagnosisIds.includes(item.id)
        ),
        ...additionalMaterialList.filter((item) =>
          selectedAdditionalMaterialIds.includes(item.id)
        ),
      ].map((item) => item.materialContent);

      if (materialList.length) {
        patientVisitSummaryData.materialList = materialList;
      }
    }

    if (Object.keys(patientVisitSummaryData).length === 0) {
      setShowErrorToast(true);
      setErrorToastText("No selected content to print!");
      return;
    }

    printPatientVisitSummaryData(
      {
        patientVisitSummaryData: JSON.stringify(patientVisitSummaryData),
      },
      true,
      (msg: SetStateAction<string>) => {
        setErrorToastText(msg);
        setShowErrorToast(true);
      },
      setShowPrintToast
    );
  };

  return (
    <>
      <Box display={"flex"} alignItems={"center"}>
        <Button color="primary" variant="contained" onClick={handlePrintPvs}>
          Print Patient Visit Summary
        </Button>
        <AlcoholInfoIcon style={{ margin: "0 8px 0 32px" }} />
        <Typography sx={{ font: "normal 400 16px Roboto" }}>
          Visit Summary and Education Material will automatically be sent to the
          Patient Portal
        </Typography>
      </Box>
      <CciToast
        centerText={false}
        autoHideDuration={3000}
        type="success"
        text={"Patient Visit Summary printed."}
        open={showPrintToast}
        setOpen={setShowPrintToast}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        alertSx={{ width: "330px" }}
      />
      <CciToast
        type="error"
        text={errorToastText}
        open={showErrorToast}
        setOpen={setShowErrorToast}
        anchorOrigin={{ horizontal: "center", vertical: "bottom" }}
        autoHideDuration={5000}
      />
    </>
  );
}
