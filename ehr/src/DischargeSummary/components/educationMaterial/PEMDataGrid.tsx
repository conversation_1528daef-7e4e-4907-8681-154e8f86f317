import { FunctionComponent } from "react";
import { CciCard } from "@cci/mui-components";
import {
  DataGridPro,
  gridClasses,
  GridColDef,
  gridPageSelector,
  useGridApiContext,
  useGridSelector,
  gridPaginationModelSelector,
  GridRowSelectionModel,
  GridCallbackDetails,
} from "@mui/x-data-grid-pro";
import { Box, styled } from "@mui/material";
import { CciTablePagination } from "@cci-monorepo/common/mui-components/src/export";
import { Typography } from "@mui/material";

type TableProps = {
  title: string;
  rows: any[];
  columns: GridColDef[];
  readonly: boolean;
  rowSelectionModel: GridRowSelectionModel;
  handleRowSelectionModelChange: (
    rowSelectionModel: GridRowSelectionModel,
    details: GridCallbackDetails
  ) => void;
  actionHeader?: JSX.Element;
  [key: string]: any;
};

const ROWS_PER_PAGE = 5;

const StyledDataGrid = styled(DataGridPro)({
  borderBottomLeftRadius: "8px",
  borderBottomRightRadius: "8px",
  borderBottom: "none",
  background: "white",
  "& .MuiDataGrid-column:focus-within, .MuiDataGrid-cell:focus-within": {
    outline: "none",
  },
  "& .MuiDataGrid-columnHeaders": {
    background: "#f2f2f2",
    borderRadius: "0px",
    fontSize: "15px",
    fontFamily: "Roboto",
    color: "black",
    "& .MuiDataGrid-columnHeaderTitle": {
      fontWeight: "700",
    },
    "& .MuiDataGrid-columnHeader": {
      "&:focus": {
        outline: "none",
      },
      "&:hover": {
        outline: "none",
      },
    },
  },
  "& .MuiDataGrid-cell": {
    fontSize: "15px",
    fontWeight: "400",
    borderBottom: "none",
    "&:focus": {
      outline: "none",
    },
  },
  "&.MuiDataGrid-root .MuiDataGrid-row": {
    borderBottom: "1px solid rgba(224, 224, 224, 1)",
  },
  [`& .${gridClasses.row}.even`]: {
    backgroundColor: "#F7F7F7 !important",
  },
  [`& .${gridClasses.row}.odd`]: {
    backgroundColor: "#FFF !important",
  },
  "& .MuiDataGrid-footerContainer": {
    borderTop: "none",
    minHeight: "40px",
  },
  "& .MuiDataGrid-virtualScroller": {
    marginBottom: 0,
  },
  "& .MuiDataGrid-main": {
    borderBottom: "none",
  },
  "& .MuiDataGrid-checkboxInput": {
    "&.Mui-checked": {
      color: "rgb(49, 148, 54)",
    },
  },
});
const PEMDateGrid: FunctionComponent<TableProps> = ({
  title,
  rows,
  columns,
  readonly,
  rowSelectionModel,
  handleRowSelectionModelChange,
  actionHeader,
  ...props
}) => {
  const Pagination = () => {
    const apiRef = useGridApiContext();
    const page = useGridSelector(apiRef, gridPageSelector);
    const paginationModel = useGridSelector(
      apiRef,
      gridPaginationModelSelector
    );
    const computedRowTotal = apiRef.current.getRowsCount();
    const computedRowsPerPage = paginationModel.pageSize
      ? paginationModel.pageSize
      : 0;

    return (
      <CciTablePagination
        rowTotal={computedRowTotal}
        rowsPerPage={computedRowsPerPage}
        page={page}
        handleChangePage={(event, value) => apiRef.current.setPage(value)}
        refresh={() => {
          apiRef.current.setPage(page);
        }}
      />
    );
  };

  return (
    <Box sx={{ marginTop: "16px" }}>
      <CciCard
        title={title}
        actionHeader={actionHeader}
        cardHeaderStyle={{ padding: "8px" }}
        content={
          <StyledDataGrid
            rows={rows}
            columns={columns}
            getRowId={(row) => row.id}
            columnHeaderHeight={32}
            rowHeight={40}
            disableColumnReorder
            disableColumnResize
            disableColumnMenu
            checkboxSelection={!readonly}
            disableRowSelectionOnClick
            rowSelectionModel={rowSelectionModel}
            onRowSelectionModelChange={handleRowSelectionModelChange}
            getRowClassName={(params) =>
              params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even"
            }
            hideFooterSelectedRowCount
            hideFooter={rows.length <= ROWS_PER_PAGE}
            pagination={rows.length > ROWS_PER_PAGE}
            slots={{
              pagination: Pagination,
              noRowsOverlay: () => (
                <Typography sx={{ padding: "12px", font: "400 15px Roboto" }}>
                  {title === "Diagnoses"
                    ? "No Diagnoses added"
                    : "No additional materials added"}
                </Typography>
              ),
            }}
            initialState={{
              pagination: { paginationModel: { pageSize: ROWS_PER_PAGE } },
            }}
            autoHeight
            sx={{
              "--DataGrid-overlayHeight": "42px",
            }}
            {...props}
          />
        }
        noPadding
        excludeBorder
      />
    </Box>
  );
};

export default PEMDateGrid;
