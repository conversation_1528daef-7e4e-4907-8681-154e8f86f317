import {
  drawerOpenAtom,
  loading<PERSON>tom,
  materialContentAtom,
  openErrorDialog<PERSON>tom,
  previewRowAtom,
  additionalMaterialListAtom,
  diagnosisListAtom,
  searchValueAtom,
  refreshAtom,
  toastAtom,
} from "@cci-monorepo/DischargeSummary/context/EducationAtoms";
import {
  Box,
  CircularProgress,
  Drawer,
  Stack,
  Typography,
} from "@mui/material";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { MaterialList } from "./MaterialList";
import { Button } from "@cci-monorepo/common/mui-components/src/export";
import { MaterialDetail } from "./MaterialDetail";
import { PEMAutoComplete } from "./PEMAutoComplete";
import { useEffect, useState } from "react";
import {
  getMaterialDetail,
  saveMaterialInfo,
  searchWebMDTerms,
  serverRequest,
  WebMdApiError,
} from "@cci-monorepo/DischargeSummary/utils/DataApi";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { getSaveData } from "@cci-monorepo/DischargeSummary/utils/DischargeSummaryUtil";

export const MaterialDrawer = () => {
  const additionalMaterialList = useAtomValue(additionalMaterialListAtom);
  const diagnosisList = useAtomValue(diagnosisListAtom);
  const [drawerOpen, setDrawerOpen] = useAtom(drawerOpenAtom);
  const [materialContent, setMaterialContent] = useAtom(materialContentAtom);
  const [previewRow, setPreviewRow] = useAtom(previewRowAtom);
  const [searchValue, setSearchValue] = useAtom(searchValueAtom);
  const [materialList, setMaterialList] = useState<any[]>([]);
  const [loading, setLoading] = useAtom(loadingAtom);
  const setRefresh = useSetAtom(refreshAtom);
  const setToast = useSetAtom(toastAtom);
  const setOpenErrorDialog = useSetAtom(openErrorDialogAtom);
  const handleDrawerCancel = () => {
    setDrawerOpen(false);
  };

  useEffect(() => {
    if (!drawerOpen) {
      setPreviewRow(null);
      setMaterialContent(null);
      setMaterialList([]);
      setSearchValue("");
    }
  }, [drawerOpen]);
  const handleSelectMaterial = (item: any) => {
    setMaterialContent(item);
  };

  useEffect(() => {
    let isActive = true;
    const loadSearchList = async () => {
      if (!searchValue?.trim()) {
        setMaterialList([]);
        return;
      }

      setLoading(true);
      setMaterialList([]);

      try {
        const searchResult = await searchWebMDTerms(searchValue);
        const data = searchResult?.data ?? [];

        if (!data.length) return;

        const validItems = data
          .filter((item: any) => item.ContentTypeID && item.ContentID)
          .slice(0, 5);

        const mergedList = await Promise.all(
          validItems.map(async (item: any) => {
            try {
              const detail = await getMaterialDetail(
                `${item.ContentTypeID}-${item.ContentID}`
              );
              return { ...item, ...(detail || {}) };
            } catch {
              return null;
            }
          })
        );

        if (isActive) {
          setMaterialList(mergedList.filter(Boolean));
        }
      } catch (error) {
        if (error instanceof WebMdApiError) {
          openErrorDialog();
        }
      } finally {
        setLoading(false);
      }
    };

    loadSearchList();
    return () => {
      isActive = false;
      setLoading(false);
    };
  }, [searchValue]);

  useEffect(() => {
    if (!previewRow?.materialContent?.id) {
      setSearchValue(previewRow?.name || "");
    }
  }, [previewRow?.name]);

  const handleChangeSelect = (value: any) => {
    setSearchValue(value);
    setMaterialContent(null);
  };

  const updateDiagnosis = () => {
    const saveData = diagnosisList.map((item) =>
      item.nit === previewRow.nit
        ? { ...item, materialid: materialContent.id }
        : item
    );

    const onSuccess = (result: any) => {
      if (result) {
        setRefresh((prev) => ({
          ...prev,
          diagnosisFlag: !prev.diagnosisFlag,
        }));
      }
    };

    const onFailure = (error: string) => {
      setToast({
        type: "error",
        text: `System has failed to update Diagnosis: ${error}`,
        open: true,
      });
    };

    let datasArr: any[] = getSaveData("Diagnoses", saveData);
    const params = {
      group: "pld_diagnoses",
      appname: "UNLOCK",
      fsdata: datasArr,
      perm: "R",
    };

    serverRequest("DBI/savemgdb", params, onSuccess, onFailure);
  };
  const updateAdditionalMaterial = () => {
    const onSuccess = (result: any) => {
      if (result) {
        setRefresh((prev) => ({
          ...prev,
          additionalFlag: !prev.additionalFlag,
        }));
      }
    };

    const onFailure = (error: string) => {
      setToast({
        type: "error",
        text: `System has failed to update Diagnosis: ${error}`,
        open: true,
      });
    };

    const idlist: any[] = additionalMaterialList
      .map((item) => item.materialid)
      .filter((id) => id !== previewRow?.materialContent?.id);

    const datasArr: any[] = Array.from(
      new Set([materialContent.id, ...idlist])
    );

    const data = [{ jit: "6634", nit: 0, ks: "admitkey", data: datasArr }];
    const params = {
      appname: "UNLOCK",
      dbpath: Cci.Patient.getDbpath(),
      fsdata: Ext.encode(data),
      perm: "E",
      type: "DEMO",
    };

    serverRequest("DBI/savedata", params, onSuccess, onFailure);
  };

  const saveMaterial = async () => {
    try {
      if (!materialContent) return;
      setLoading(true);
      const params = {
        materialinfo: [
          {
            id: materialContent.id,
            content: JSON.stringify(materialContent),
          },
        ],
      };
      await saveMaterialInfo(params);
      if (previewRow?.materialType === "diagnosis") {
        updateDiagnosis();
      } else {
        updateAdditionalMaterial();
      }
      handleDrawerCancel();
      setToast({
        type: "success",
        text: `Patient Education Material ${!previewRow?.materialContent?.id ? "added" : "updated"}.`,
        open: true,
      });
    } catch (error) {
      setToast({
        type: "error",
        text: `System has failed to save: ${error}`,
        open: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const openErrorDialog = () => {
    setOpenErrorDialog({
      title: "Resource Not Available",
      text: "The education material resource is not available at this time. Please try again later.",
      open: true,
    });
    setDrawerOpen(false);
  };

  return (
    <Drawer
      sx={{
        "& .MuiDrawer-paper": {
          width: 850,
          backgroundColor: "#F7F7F7",
          padding: "0px 24px",
          borderRadius: "15px 0px 0px 15px",
          boxSizing: "border-box",
          boxShadow:
            "0px -4px 20px 0px rgba(0, 0, 0, 0.15), 0px 0px 40px 0px rgba(0, 0, 0, 0.20)",
        },
      }}
      variant="temporary"
      anchor="right"
      disableEnforceFocus
      open={drawerOpen}
      onClose={handleDrawerCancel}
    >
      <Box padding="16px 0" display={"flex"}>
        {!previewRow?.icd10code && <ArrowRightIcon sx={{ fontSize: "30px" }} />}
        <Typography
          sx={{
            font: "normal 700 24px Roboto",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {previewRow?.icd10code && previewRow?.name
            ? `${previewRow.icd10code} - ${previewRow.name}`
            : previewRow?.name ||
              previewRow?.Title ||
              "Add Patient Education Material"}
        </Typography>
      </Box>
      <Box
        sx={{
          background: "#fff",
          width: "100%",
          height: "calc(100vh - 120px)",
          borderRadius: "20px",
          padding: "16px",
        }}
      >
        {(searchValue || !materialContent?.id) && (
          <PEMAutoComplete
            id={"PEMAutoComplete"}
            value={searchValue}
            onDataChange={handleChangeSelect}
            openErrorDialog={openErrorDialog}
          />
        )}
        {!materialContent ? (
          <MaterialList
            handleSelectMaterial={handleSelectMaterial}
            materials={materialList}
          />
        ) : (
          <MaterialDetail
            hasback={materialList.length > 0}
            openErrorDialog={openErrorDialog}
          />
        )}
      </Box>

      <Stack
        direction="row"
        justifyContent="flex-end"
        spacing={1}
        marginTop="10px"
      >
        <Button color="secondary" onClick={handleDrawerCancel}>
          Cancel
        </Button>
        <Button
          color="primary"
          disabled={
            loading ||
            (previewRow?.materialContent?.id
              ? previewRow.materialContent.id === materialContent.id
              : !materialContent)
          }
          onClick={saveMaterial}
        >
          {previewRow?.materialContent?.id ? "Save" : "Add"}
        </Button>
      </Stack>
      {loading && (
        <CircularProgress
          size={30}
          sx={{
            position: "absolute",
            left: "50%",
            top: "50%",
            zIndex: 2,
          }}
        />
      )}
    </Drawer>
  );
};
