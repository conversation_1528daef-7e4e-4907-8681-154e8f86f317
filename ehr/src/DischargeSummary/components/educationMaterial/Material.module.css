/* Material.module.css */
.material {
  font-family: Roboto;
}
.material .list {
  height: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 4;
  -webkit-line-clamp: 4;
  overflow: hidden;
  text-overflow: ellipsis;
}
.material .list img {
  display: none;
}

.material h1 {
  color: #e74c39;
  font: normal normal 700 24px Roboto;
  line-height: 24px;
  margin: 0 0 10px;
}

.material .hideH1 h1 {
  display: none;
}

.material h2 {
  color: #0a2a64;
  font: normal normal 500 18px Roboto;
  margin: 8px 0;
}

.material p {
  font: normal normal 400 15px Roboto;
  margin: 0;
}

.material .highlight {
  background-color: #fffd54;
}
