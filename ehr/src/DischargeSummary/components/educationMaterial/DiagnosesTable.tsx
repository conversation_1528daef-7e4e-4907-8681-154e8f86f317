import { Box, Stack, Typography } from "@mui/material";
import PEMDateGrid from "./PEMDataGrid";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { useEffect, useRef } from "react";
import {
  getMaterialDetail,
  getProblems,
  saveMaterialInfo,
  searchWebMDTerms,
  serverRequest,
} from "@cci-monorepo/DischargeSummary/utils/DataApi";
import {
  convertArrayToObject,
  getSaveData,
  removeDuplicateRows,
} from "@cci-monorepo/DischargeSummary/utils/DischargeSummaryUtil";
import {
  drawerOpenAtom,
  previewRowAtom,
  diagnosisListAtom,
  selectedRowDiagnosisAtom,
  materialContentAtom,
  checkedPrintMaterialAtom,
  refreshAtom,
} from "@cci-monorepo/DischargeSummary/context/EducationAtoms";
import { Popup, WarningIcon } from "@cci-monorepo/DischargeSummary/icons";
import { isJson } from "@cci-monorepo/common";

export const DiagnosesTable = () => {
  const setDrawerOpen = useSetAtom(drawerOpenAtom);
  const setPreviewRow = useSetAtom(previewRowAtom);
  const setMaterialContent = useSetAtom(materialContentAtom);
  const [diagnosisList, setDiagnosisList] = useAtom(diagnosisListAtom);
  const [rowSelectionModel, setRowSelectionModel] = useAtom(
    selectedRowDiagnosisAtom
  );
  const refresh = useAtomValue(refreshAtom);
  const checkedPrintMaterial = useAtomValue(checkedPrintMaterialAtom);

  const isLoadedRef = useRef(false);
  const handleRowSelectionModelChange = (rowSelectionModel: any) => {
    setRowSelectionModel(rowSelectionModel);
  };
  const getProblemsData = async () => {
    try {
      const onFailure = () => {
        setDiagnosisList([]);
      };

      const result = await getProblems(
        {
          campus: cci.cfg.campus,
          dbs: Cci.util.Patient.getDbpath(),
        },
        () => {},
        onFailure
      );

      const rawData = result?.diagnosis_data?.data ?? [];
      if (!rawData.length) {
        setDiagnosisList([]);
        return;
      }

      let diagnosisData = convertArrayToObject(result.diagnosis_data) ?? [];

      diagnosisData = removeDuplicateRows(diagnosisData).filter(
        (record: any) => record.status === "Active"
      );

      const diagnosisList = diagnosisData.map((item: any) => ({
        ...item,
        id: item.nit,
        materialContent: isJson(item.materialcontent)
          ? JSON.parse(item.materialcontent)
          : null,
      }));
      setDiagnosisList(diagnosisList);
      if (!isLoadedRef.current) {
        autoSaveMaterial(diagnosisList);
      }
    } catch (error) {
      setDiagnosisList([]);
    }
  };

  const autoSaveMaterial = async (diagnosisList: any) => {
    const enrichedList = await Promise.all(
      diagnosisList.map(async (item: any) => {
        try {
          if (item?.materialContent?.id) return item;

          const webMDResult = await searchWebMDTerms(item.name);
          const matchedData = webMDResult?.data?.[0];
          const isTitleMatch =
            matchedData?.Title.toLowerCase() === item.name.toLowerCase();
          if (!isTitleMatch) return item;

          const contentKey = `${matchedData.ContentTypeID}-${matchedData.ContentID}`;
          const materialDetail = await getMaterialDetail(contentKey);

          return {
            ...item,
            ...(isTitleMatch && {
              materialContent: {
                ...matchedData,
                ...materialDetail,
              },
              materialid: materialDetail.id,
            }),
            needSaveMaterial: true,
          };
        } catch (e) {
          return item;
        }
      })
    );

    if (enrichedList.some((item) => item.needSaveMaterial)) {
      const materialParams = enrichedList
        .filter((item) => item.materialContent?.id && item.needSaveMaterial)
        .map((item) => ({
          id: item.materialContent.id,
          content: JSON.stringify(item.materialContent),
        }));
      saveMaterialInfo({ materialinfo: materialParams });

      let datasArr: any[] = getSaveData("Diagnoses", enrichedList);
      const params = {
        group: "pld_diagnoses",
        appname: "UNLOCK",
        fsdata: datasArr,
        perm: "R",
      };
      serverRequest(
        "DBI/savemgdb",
        params,
        () => {},
        () => {}
      );
    }
    setDiagnosisList(enrichedList);
    isLoadedRef.current = true;
  };

  useEffect(() => {
    getProblemsData();
  }, [refresh.diagnosisFlag]);

  useEffect(() => {
    if (!checkedPrintMaterial || !diagnosisList.length) return;
    const selectedIds = diagnosisList.filter(
      (item) => item?.materialContent?.id
    );
    if (selectedIds.length) {
      setRowSelectionModel(selectedIds.map((item) => item.nit));
    }
  }, [diagnosisList]);

  const DIAGNOSES_COLUMNS: any[] = [
    {
      field: "icd10code",
      headerName: "ICD-10 Code",
      width: 110,
    },
    {
      field: "name",
      headerName: "Diagnosis",
      flex: 1,
      renderCell: (params: any) => (
        <Box
          display="flex"
          alignItems="center"
          title={params.value}
          sx={{
            minWidth: 0,
          }}
        >
          {!params.row?.materialContent?.id && (
            <WarningIcon
              style={{
                width: 24,
                marginRight: "8px",
                flexShrink: 0,
              }}
            />
          )}
          <Typography
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: "action",
      headerName: "Preview",
      sortable: false,
      width: 80,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => (
        <Popup
          style={{ cursor: "pointer" }}
          onClick={async () => {
            setDrawerOpen(true);
            setPreviewRow({ ...params.row, materialType: "diagnosis" });
            if (params.row?.materialContent?.id) {
              setMaterialContent(params.row.materialContent);
            }
          }}
        />
      ),
    },
  ];

  return (
    <Box>
      {diagnosisList.some((item) => !item?.materialContent?.id) && (
        <Stack
          sx={{
            marginTop: "16px",
            flexDirection: "row",
            alignItems: "flex-start",
          }}
        >
          <WarningIcon
            style={{
              width: 24,
              marginRight: "16px",
              flexShrink: 0,
            }}
          />
          <Typography sx={{ font: "italic 400 15px Roboto" }}>
            Patient Education Material for at least one of the added diagnoses
            were not found. Please manually edit the missing patient education
            materials.
          </Typography>
        </Stack>
      )}
      <PEMDateGrid
        title="Diagnoses"
        rows={diagnosisList}
        columns={DIAGNOSES_COLUMNS}
        readonly={false}
        rowSelectionModel={rowSelectionModel}
        handleRowSelectionModelChange={handleRowSelectionModelChange}
        isRowSelectable={(params: any) => {
          return params.row?.materialContent?.id;
        }}
      />
    </Box>
  );
};
