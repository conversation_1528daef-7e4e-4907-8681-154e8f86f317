import { useEffect, useState } from "react";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { getMaterialDetail } from "@cci-monorepo/DischargeSummary/utils/DataApi";
import { Box, CircularProgress, Drawer, Typography } from "@mui/material";
import { MaterialTemplate } from "./MaterialTemplate";
import styles from "./Material.module.css";

export default function MaterialDrawerWrapper({ screenParams }: any) {
  const [drawerOpen, setDrawerOpen] = useState<boolean>(true);
  const [materialContent, setMaterialContent] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!drawerOpen && screenParams?.closedCallback) {
      screenParams.closedCallback();
    }
  }, [drawerOpen]);

  useEffect(() => {
    if (screenParams?.contentId && screenParams?.language) {
      getDetail(screenParams.contentId, screenParams.language);
    }
  }, [screenParams?.contentId, screenParams?.language]);

  const getDetail = async (contentId: any, language: any) => {
    try {
      setLoading(true);
      const result = await getMaterialDetail(contentId, language);
      setMaterialContent(result);
    } catch (error: any) {
      if (screenParams?.errorCallback) {
        screenParams.errorCallback(
          error?.message || "Failed to load education material."
        );
      }
      setDrawerOpen(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      sx={{
        "& .MuiDrawer-paper": {
          width: 850,
          height: "calc(100vh - 46px)",
          top: "46px",
          backgroundColor: "#F7F7F7",
          padding: "0px 24px",
          borderRadius: "15px 0px 0px 15px",
          boxSizing: "border-box",
          boxShadow:
            "0px -4px 20px 0px rgba(0, 0, 0, 0.15), 0px 0px 40px 0px rgba(0, 0, 0, 0.20)",
        },
      }}
      variant="temporary"
      anchor="right"
      disableEnforceFocus
      open={drawerOpen}
      onClose={() => setDrawerOpen(false)}
    >
      <Box padding="16px 0" display={"flex"}>
        <ArrowRightIcon
          sx={{ fontSize: "30px", cursor: "pointer" }}
          onClick={() => setDrawerOpen(false)}
        />
        <Typography
          sx={{
            font: "normal 700 24px Roboto",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {materialContent?.regularTitle}
        </Typography>
      </Box>
      <Box
        sx={{
          background: "#fff",
          width: "100%",
          height: "calc(100vh - 120px)",
          borderRadius: "20px",
          padding: "16px",
          overflow: "auto",
        }}
        className={styles.material}
      >
        {!loading && materialContent?.id && (
          <MaterialTemplate type="notes" materialContent={materialContent} />
        )}
      </Box>

      {loading && (
        <CircularProgress
          size={30}
          sx={{
            position: "absolute",
            left: "50%",
            top: "50%",
            zIndex: 2,
          }}
        />
      )}
    </Drawer>
  );
}
