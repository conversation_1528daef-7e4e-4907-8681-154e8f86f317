import { Box, Typography } from "@mui/material";
import PEMDateGrid from "./PEMDataGrid";
import { Button } from "@cci-monorepo/common/mui-components/src/export";
import { Popup } from "@cci-monorepo/DischargeSummary/icons";
import {
  drawerOpenAtom,
  materialContentAtom,
  previewRowAtom,
  additionalMaterialListAtom,
  selectedAdditionalMaterialAtom,
  refreshAtom,
  checkedPrintMaterialAtom,
} from "@cci-monorepo/DischargeSummary/context/EducationAtoms";
import { useAtom, useSetAtom } from "jotai";
import { useEffect } from "react";
import {
  getMaterialInfo,
  serverRequest,
} from "@cci-monorepo/DischargeSummary/utils/DataApi";
import { isJson } from "@cci-monorepo/common";

export const AdditionalTable = () => {
  const setDrawerOpen = useSetAtom(drawerOpenAtom);
  const setPreviewRow = useSetAtom(previewRowAtom);
  const setAdditionalMaterialList = useSetAtom(additionalMaterialListAtom);
  const setMaterialContent = useSetAtom(materialContentAtom);
  const [additionalMaterialList] = useAtom(additionalMaterialListAtom);
  const [rowSelectionModel, setRowSelectionModel] = useAtom(
    selectedAdditionalMaterialAtom
  );
  const [refresh] = useAtom(refreshAtom);
  const [checkedPrintMaterial] = useAtom(checkedPrintMaterialAtom);

  const ADDITIONAL_COLUMNS: any[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      renderCell: ({ row }: any) => (
        <Typography
          title={row?.materialContent?.Title}
          sx={{
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {row?.materialContent?.Title || ""}
        </Typography>
      ),
    },
    {
      field: "action",
      headerName: "Preview",
      sortable: false,
      width: 80,
      align: "center",
      headerAlign: "center",
      renderCell: ({ row }: any) => (
        <Popup
          style={{ cursor: "pointer" }}
          onClick={() => {
            setDrawerOpen(true);
            setPreviewRow({
              ...row,
              materialType: "additional",
              name: row?.materialContent?.Title,
            });
            setMaterialContent(row.materialContent);
          }}
        />
      ),
    },
  ];

  const addMaterial = () => {
    setDrawerOpen(true);
  };

  const getAdditionalData = () => {
    const params = { dbpath: Cci.util.Patient.getDbpath() };

    const onSuccess = async (result: any) => {
      try {
        const materialidsIdx = result?.data?.header.indexOf("materialids");
        const idsStr = result?.data?.data?.[0]?.[materialidsIdx];
        if (!isJson(idsStr)) return;

        const materialids = JSON.parse(idsStr);
        const info = await getMaterialInfo({ materialids });

        const list = info.materialContent.map((item: any) => ({
          ...item,
          id: item.materialid,
          materialContent: JSON.parse(item.content),
        }));

        setAdditionalMaterialList(list);
      } catch (e) {
        console.error("Failed to parse additional materials:", e);
        setAdditionalMaterialList([]);
      }
    };

    const onFailure = (error: string) => {
      console.error("Failed to fetch additional materials:", error);
    };

    serverRequest(
      "educationmaterial/additionalmaterial",
      params,
      onSuccess,
      onFailure
    );
  };

  useEffect(() => {
    getAdditionalData();
  }, [refresh.additionalFlag]);

  useEffect(() => {
    if (!checkedPrintMaterial || additionalMaterialList.length === 0) return;

    const selectedIds = additionalMaterialList.filter(
      (item) => item?.materialContent?.id
    );
    if (selectedIds.length) {
      setRowSelectionModel(selectedIds.map((item) => item.id));
    }
  }, [additionalMaterialList]);

  return (
    <Box>
      <PEMDateGrid
        title="Additional Education Material"
        actionHeader={
          <Button
            color="secondary"
            sx={{ marginRight: "16px", font: "normal 700 14px Roboto" }}
            onClick={addMaterial}
          >
            Add Education Material
          </Button>
        }
        rows={additionalMaterialList}
        columns={ADDITIONAL_COLUMNS}
        readonly={false}
        rowSelectionModel={rowSelectionModel}
        handleRowSelectionModelChange={setRowSelectionModel}
      />
    </Box>
  );
};
