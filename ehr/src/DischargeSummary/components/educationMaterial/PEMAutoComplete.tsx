import React, { useState, useEffect, useRef } from "react";
import { Autocomplete, Popper, PopperProps, TextField } from "@mui/material";
import { SearchIcon } from "@cci-monorepo/common";
import { IconDropDownArrow } from "@cci-monorepo/CareTeam/icons";
import { searchWebMDTerms } from "@cci-monorepo/DischargeSummary/utils/DataApi";
import { debounce } from "lodash";

const minCharactersCount: number = 3;
const StyledPopper = (props: PopperProps) => {
  return (
    <Popper
      placement={props.placement ?? "bottom-start"}
      {...props}
      sx={{
        borderRadius: "4px",
        "& .MuiAutocomplete-listbox": {
          "& .MuiAutocomplete-option": {
            height: 28,
            "&:hover": {
              backgroundColor: "#F7EEC1",
            },
          },
          '& .MuiAutocomplete-option[aria-selected="true"]': {
            backgroundColor: "#F7EEC1 !important",
            "&:hover": {
              backgroundColor: "#F7EEC1",
            },
          },
        },
      }}
    />
  );
};

export const PEMAutoComplete = React.forwardRef(
  (
    { id, value, onDataChange, inputHeight = "32px", openErrorDialog }: any,
    ref: any
  ) => {
    const [inputValue, setInputValue] = useState(value || "");
    const [options, setOptions] = useState<string[]>([]);
    const [open, setOpen] = useState(false);

    useEffect(() => {
      setInputValue(value);
    }, [value]);

    const fetchOptions = async (newValue: string) => {
      if (newValue.trim().length >= minCharactersCount) {
        try {
          const result = await searchWebMDTerms(newValue);
          const entryFields: string[] = Array.from(
            new Set(result?.data?.map((item: any) => item.Title))
          );
          setOptions([...entryFields, `Use "${newValue}"`]);
        } catch (e) {
          openErrorDialog();
        }
      } else {
        setOptions([]);
      }
    };

    const debouncedFetch = useRef(
      debounce((val: string) => fetchOptions(val), 500)
    ).current;

    const handleInputChange = (
      event: React.ChangeEvent<{}>,
      newInputValue: string
    ) => {
      if (!newInputValue.startsWith("Use")) {
        setInputValue(newInputValue);
        debouncedFetch(newInputValue);
      }
    };

    const handleOptionSelect = (
      event: React.ChangeEvent<{}>,
      newValue: string
    ) => {
      let selectedValue = newValue;
      if (newValue.startsWith("Use")) {
        selectedValue = newValue.replace(/Use "|"/g, "");
      }
      onDataChange(selectedValue);
    };

    return (
      <Autocomplete
        id={id}
        key={id}
        sx={{ width: "100%", height: inputHeight, marginBottom: "16px" }}
        options={options}
        freeSolo
        open={open}
        loadingText={
          inputValue?.length >= minCharactersCount
            ? open && options.length === 0
              ? "Loading..."
              : "No Matching Options"
            : `Enter ${minCharactersCount} characters to search`
        }
        PopperComponent={StyledPopper}
        loading={open && options.length === 0}
        onClose={() => setOpen(false)}
        onOpen={() => setOpen(true)}
        disableClearable
        inputValue={inputValue}
        onInputChange={handleInputChange}
        onChange={handleOptionSelect}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder="Search by keyword"
            InputProps={{
              ...params.InputProps,
              startAdornment: <SearchIcon />,
              endAdornment: <IconDropDownArrow />,
              style: { height: inputHeight },
            }}
            sx={{
              "& .MuiInputBase-root": {
                backgroundColor: "#FFFFFF",
                borderRadius: "4px",
                fontSize: "14px",
                fieldset: {
                  whiteSpace: "nowrap",
                  border: "1px solid",
                  borderColor: "#B1B1B1",
                },
                "&.Mui-focused fieldset": {
                  border: "1px solid #6599FF",
                },
                "&.Mui-disabled fieldset": {
                  border: "1px solid #B1B1B1",
                },
                "&.Mui-disabled.Mui-error fieldset": {
                  border: "1px solid #CF4C35",
                },
                "&.Mui-disabled": {
                  color: "inherit",
                  backgroundColor: "#e9e9e9",
                },
              },
            }}
            variant="outlined"
          />
        )}
      />
    );
  }
);
