import {
  loadingAtom,
  searchValueAtom,
} from "@cci-monorepo/DischargeSummary/context/EducationAtoms";
import { Box, Stack, Typography } from "@mui/material";
import { useAtomValue } from "jotai";
import { Chevron } from "@cci-monorepo/DischargeSummary/icons";
import { MaterialTemplate } from "./MaterialTemplate";

interface MaterialListProps {
  materials: any[];
  handleSelectMaterial: (item: any) => void;
}

export const MaterialList = ({
  materials,
  handleSelectMaterial,
}: MaterialListProps) => {
  const searchValue = useAtomValue(searchValueAtom);
  const loading = useAtomValue(loadingAtom);

  return (
    <>
      <Stack
        gap={2}
        sx={{
          height: "calc(100% - 48px)",
          overflow: "auto",
        }}
      >
        {searchValue && !loading && materials?.[0]?.Title !== searchValue && (
          <Typography
            sx={{
              font: "italic 400 15px Roboto",
            }}
          >
            No results found for "{searchValue}"
          </Typography>
        )}
        {!!materials.length && (
          <Typography sx={{ font: "normal 700 18px Roboto" }}>
            Suggested Material
          </Typography>
        )}
        {!searchValue && (
          <Typography
            sx={{
              height: "100%",
              font: "normal 400 24px Releway",
              color: "rgba(0, 0, 0, 0.60)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            Please enter a search query
          </Typography>
        )}
        {materials.map((item, index) => (
          <Stack
            key={index}
            direction="row"
            gap={2}
            alignItems="center"
            justifyContent={"space-between"}
            sx={{
              border: "1px solid #CBCACA",
              borderRadius: "8px",
              padding: "16px",
            }}
          >
            {item?.id && (
              <MaterialTemplate type="list" materialContent={item} />
            )}
            <Box
              sx={{ cursor: "pointer" }}
              onClick={() => handleSelectMaterial(item)}
            >
              <Chevron />
            </Box>
          </Stack>
        ))}
      </Stack>
    </>
  );
};
