import { Box, Typography } from "@mui/material";
import { DiagnosesTable } from "./DiagnosesTable";
import { AdditionalTable } from "./AdditionalTable";
import { MaterialDrawer } from "./MaterialDrawer";
import { useEffect } from "react";
import { getWebMDToken } from "@cci-monorepo/DischargeSummary/utils/DataApi";
export const PatientEducationMaterial = () => {
  useEffect(() => {
    getWebMDToken();
  }, []);
  return (
    <Box>
      <Typography
        sx={{
          color: "#000",
          font: "normal 700 18px Roboto",
          marginTop: "20px",
        }}
      >
        Patient Education Material
      </Typography>
      <DiagnosesTable />
      <AdditionalTable />
      <MaterialDrawer />
    </Box>
  );
};
