import { <PERSON>, Stack, SxProps, Theme, Typography } from "@mui/material";
import { Chevron } from "@cci-monorepo/DischargeSummary/icons";
import styles from "./Material.module.css";
import { useAtomValue } from "jotai";
import { searchValueAtom } from "@cci-monorepo/DischargeSummary/context/EducationAtoms";

interface MaterialTemplateProps {
  type: "list" | "detail" | "notes";
  materialContent: any;
}
const highlightWord = (html: string, searchTerms: string): string => {
  if (!searchTerms.trim()) return html;

  const keywords = searchTerms
    .split(/\s+/)
    .filter(Boolean)
    .map((word) => {
      return `\\b${word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`;
    });

  const regex = new RegExp(`(<[^>]+>)|(${keywords.join("|")})`, "gi");

  return html.replace(regex, (match, p1, p2) => {
    if (p1) return p1;
    return `<span class=${styles.highlight}>${p2}</span>`;
  });
};

export const MaterialTemplate = ({
  type,
  materialContent,
}: MaterialTemplateProps) => {
  const searchValue = useAtomValue(searchValueAtom);

  return (
    <div className={styles.material}>
      {type === "list" ? (
        <Box>
          <div
            className={styles.list}
            dangerouslySetInnerHTML={{
              __html: highlightWord(materialContent.body, searchValue),
            }}
          ></div>
        </Box>
      ) : (
        <Box>
          <div
            className={`${type === "detail" ? styles.hideH1 : ""}`}
            dangerouslySetInnerHTML={{
              __html: materialContent.body,
            }}
          ></div>
        </Box>
      )}
    </div>
  );
};
