import { useAtom, useAtomValue } from "jotai";
import styles from "./Material.module.css";
import {
  loadingAtom,
  materialContentAtom,
  searchValueAtom,
} from "@cci-monorepo/DischargeSummary/context/EducationAtoms";
import { Box, Stack } from "@mui/material";
import { Chevron } from "@cci-monorepo/DischargeSummary/icons";
import { SingleDropdownMenu } from "@cci-monorepo/common/mui-components/src/export";
import { useState } from "react";
import {
  getMaterialDetail,
  WebMdApiError,
} from "@cci-monorepo/DischargeSummary/utils/DataApi";
import { MaterialTemplate } from "./MaterialTemplate";

const LANGUAGE_TO_CODE: any = {
  English: "en",
  Spanish: "es",
};

interface MaterialDetailProps {
  openErrorDialog: () => void;
  hasback?: boolean;
}

export const MaterialDetail = ({
  hasback,
  openErrorDialog,
}: MaterialDetailProps) => {
  const [materialContent, setMaterialContent] = useAtom(materialContentAtom);
  const searchValue = useAtomValue(searchValueAtom);
  const [loading, setLoading] = useAtom(loadingAtom);
  const [languageOptions] = useState<any[]>([
    {
      label: "English",
      customLabel: `English${materialContent.metadata.language === "English" ? " (default)" : ""}`,
      id: 1,
    },
    {
      label: "Spanish",
      customLabel: `Spanish${materialContent.metadata.language === "Spanish" ? " (default)" : ""}`,
      id: 2,
    },
  ]);

  const [language, setLanguage] = useState<any>(
    languageOptions.find((item) => item.customLabel.includes("default"))
  );

  const getH1Title = (htmlContent: string) => {
    return htmlContent?.match(/<h1[^>]*>(.*?)<\/h1>/i)?.[1] || "";
  };

  const handleLanguageChange = async (value: any) => {
    try {
      setLanguage(value);
      setLoading(true);
      const result = await getMaterialDetail(
        materialContent.id,
        LANGUAGE_TO_CODE[value.label]
      );

      setMaterialContent({ ...materialContent, ...result });
    } catch (error) {
      if (error instanceof WebMdApiError) {
        openErrorDialog();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        height:
          searchValue || !materialContent?.id ? "calc(100% - 48px)" : "100%",
        overflow: "auto",
      }}
    >
      <Stack
        marginBottom="16px"
        direction="row"
        alignItems={"center"}
        justifyContent={"space-between"}
        className={styles.material}
      >
        <Box display={"flex"} alignItems={"center"}>
          {hasback && (
            <Chevron
              style={{
                transform: "rotate(180deg)",
                cursor: "pointer",
                marginRight: "16px",
              }}
              onClick={() => setMaterialContent(null)}
            />
          )}
          <h1 style={{ margin: 0 }}>{getH1Title(materialContent.body)}</h1>
        </Box>

        <SingleDropdownMenu
          data-testid="discharge-summary-material-language-dropdown"
          sx={{ width: "170px", marginRight: "8px" }}
          value={language}
          onSelectionChange={handleLanguageChange}
          disableClearable
          comboBoxChoices={languageOptions}
        />
      </Stack>
      {!loading && materialContent?.id && (
        <MaterialTemplate type="detail" materialContent={materialContent} />
      )}
    </Box>
  );
};
