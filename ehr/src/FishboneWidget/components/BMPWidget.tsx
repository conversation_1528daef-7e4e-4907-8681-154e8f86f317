import { Box } from "@mui/material";
import { Stage, Layer } from "react-konva";
import { Html } from "react-konva-utils";

import TailGroup from "./TailGroup";
import { FbTextField } from "./common/FbTextField";

export default function BMPWidget(props: any) {
  const { editable, data, onChange } = props;
  const startX = 20,
    startY = 20,
    gap = 70,
    verticalGap = gap * (2 / 3);

  let columns: number = 1;
  if (data && data.component) {
    data.component.forEach((item: any) => {
      var index = Number(item.id.substr(3).split(".")[1]);
      if (index > columns) columns = index;
    });
  }

  return (
    <Box
      sx={{
        position: "relative",
        marginTop: "15px",
      }}
    >
      <Stage width={500} height={250}>
        <Layer>
          <TailGroup x={startX} y={startY} gap={gap} columns={columns} />
          <Html
            groupProps={{
              x: startX,
              y: startY,
            }}
            divProps={{
              style: {
                position: "absolute",
                top: 0,
                left: 0,
              },
            }}
          >
            {data && data.TAIL && (
              <FbTextField
                edit={editable}
                top={verticalGap - 12}
                left={gap * columns + gap * 0.5}
                data={data.TAIL}
                onChangeValue={(v: any) => {
                  const newData = {
                    ...data,
                    TAIL: { ...data.TAIL, value: v },
                  };
                  onChange && onChange(newData);
                }}
              />
            )}
            {data &&
              data.component &&
              data.component.map((item: any, index: number) => {
                var position = item.id.substr(3).split(".");
                var row = Number(position[0]) - 1,
                  column = Number(position[1]) - 1;
                return (
                  <FbTextField
                    key={index}
                    edit={editable}
                    top={verticalGap * (row + 0.5) - 12}
                    left={gap * (column + 0.5) - 25}
                    data={item ? item : {}}
                    onChangeValue={(v: any) => {
                      const newData = { ...data };
                      newData.component[index] = {
                        ...data.component[index],
                        value: v,
                      };
                      onChange && onChange(newData);
                    }}
                  />
                );
              })}
          </Html>
        </Layer>
      </Stage>
    </Box>
  );
}
