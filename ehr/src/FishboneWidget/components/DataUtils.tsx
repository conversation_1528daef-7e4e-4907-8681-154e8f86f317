/**
 * DataUtils.tsx
 *
 * @description Data utils for fishbone widget
 */

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

export const parseMainData = (data: any) => {
  let mainData: any = {};
  if (data) {
    const rows = convertArrayToObject(data);
    rows.forEach((row: any) => {
      if (row.id === "title") {
        mainData.title = row.name;
      } else if (row.id !== "widget") {
        var tooltipInfo = JSON.parse(row.json_val);
        delete row.json_val;
        var mergedJson = Object.assign({}, row, tooltipInfo);
        if (row.id === "TAIL" || row.id === "HEADER") {
          mainData[row.id] = mergedJson;
        } else {
          if (!mainData.component) mainData.component = [];
          mainData.component.push(mergedJson);
        }
      }
    });
  }
  return mainData;
};

export const getTopIndex = (data: any) => {
  let index = -1;
  if (data && data.component && data.component.length > 0) {
    index = data.component.findIndex((row: any) => String(row.id) === "COL1.1");
  }
  return index;
};

export const getBottomIndex = (data: any) => {
  let index = -1;
  if (data && data.component && data.component.length > 0) {
    index = data.component.findIndex((row: any) => String(row.id) === "COL2.1");
  }
  return index;
};

export const getWidgetType = (data: any) => {
  let value: number = 0;
  if (data) {
    if (data.TAIL && data.HEADER) {
      if ("value" in data.HEADER) value += 1;
      if ("value" in data.TAIL) value += 1;
      if (Array.isArray(data.component)) {
        data.component.forEach((component: any) => {
          if ("value" in component) value += 1;
        });
      }
      if (value > 0) return "CBC";
    } else if (data.TAIL || data.HEADER) {
      if ("value" in data.TAIL) value += 1;
      if (Array.isArray(data.component)) {
        data.component.forEach((component: any) => {
          if ("value" in component) value += 1;
        });
      }
      if (value > 0) return "BMP";
    } else {
      if (Array.isArray(data.component)) {
        data.component.forEach((component: any) => {
          if ("value" in component) value += 1;
        });
      }
      if (value > 0) return "ABG";
    }
  }
  return "";
};
