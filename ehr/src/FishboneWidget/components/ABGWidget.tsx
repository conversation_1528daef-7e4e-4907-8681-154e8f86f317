import { Box } from "@mui/material";
import { Layer, Line, Stage } from "react-konva";
import { FbTextField } from "./common/FbTextField";

export default function ABGWidget(props: any) {
  const perWidth = 80;
  const { editable, data, onChange } = props;
  return (
    data &&
    data.component &&
    data.component.length > 0 && (
      <Box
        sx={{
          position: "relative",
          marginTop: "30px",
          marginLeft: "30px",
        }}
      >
        <Stage width={perWidth * data.component.length} height={60}>
          <Layer>
            {data.component.map((element: any, index: number) => {
              return index !== data.component.length - 1 ? (
                <Line
                  key={"layer-line-" + index}
                  linecap="round"
                  linejoin="round"
                  stroke="#aeaeae"
                  strokwidth={0.5}
                  points={[perWidth * index + 50, 60, perWidth * index + 80, 0]}
                />
              ) : (
                <Line key={"layer-line-" + index} display="hidden"></Line>
              );
            })}
          </Layer>
        </Stage>
        {data.component.map((element: any, index: number) => {
          return (
            <FbTextField
              key={"fbtextfield-" + index}
              edit={editable}
              left={perWidth * index}
              top={18}
              data={element}
              onChangeValue={(value: string) => {
                const newData = { ...data };
                newData.component[index] = {
                  ...data.component[index],
                  value: value,
                };
                onChange && onChange(newData);
              }}
            />
          );
        })}
      </Box>
    )
  );
}
