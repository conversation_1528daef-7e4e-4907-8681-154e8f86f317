import React from "react";
import { Group, Line } from "react-konva";

const TailGroup = (props: any) => {
  const { x, y, gap, columns } = props;
  const verticalGap = gap * (2 / 3);
  const points1 = [0, verticalGap, gap * columns, verticalGap];
  const points2 = [
    gap * columns + gap,
    0,
    gap * columns,
    verticalGap,
    gap * columns + gap,
    verticalGap * 2,
  ];
  let verticalLinesArray: any[] = [];
  for (var i = 0; i < columns - 1; i++) {
    const points = [gap * (i + 1), 0, gap * (i + 1), verticalGap * 2];
    verticalLinesArray.push(points);
  }

  return (
    <Group x={x} y={y}>
      <Line points={points1} stroke="black" strokeWidth={0.5} />
      <Line points={points2} stroke="black" strokeWidth={0.5} />
      {verticalLinesArray.map((item: any, index: number) => {
        return (
          <Line
            key={"vertical-line-" + index}
            points={item}
            stroke="black"
            strokeWidth={0.5}
          />
        );
      })}
    </Group>
  );
};

export default TailGroup;
