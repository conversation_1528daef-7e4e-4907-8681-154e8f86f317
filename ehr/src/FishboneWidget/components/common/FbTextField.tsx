import * as React from "react";

import { Box, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import Tooltip, { TooltipProps, tooltipClasses } from "@mui/material/Tooltip";
import { nullorblank } from "@cci-monorepo/common";
import { BaseTextField } from "./BaseTextField";

interface TextFieldProps {
  edit: boolean;
  left: number;
  top: number;
  onChangeValue: (value: string) => void;
  data: any;
}
const regexbe = /^\s*(-*\d+\.*\d*)\s*-\s*(-*\d+\.*\d*)$/g;
const regexb = /^\s*(-*\d+\.*\d*)\s*-\s*$/g;
const regexe = /^\s*-\s*(-*\d+\.*\d*)$/g;

const getMax = (normalrange: string) => {
  let ret = { val: 0, inc: true };
  let nlrt = { val: null, inc: false };
  if (normalrange !== undefined && normalrange !== null) {
    let result = Array.from(normalrange.matchAll(regexbe));
    if (result.length > 0) {
      ret["val"] = parseFloat(result[0][2].trim());
      return ret;
    }
    result = Array.from(normalrange.matchAll(regexb));
    if (result.length > 0) {
      return nlrt;
    }
    result = Array.from(normalrange.matchAll(regexe));
    if (result.length > 0) {
      ret["val"] = parseFloat(result[0][1].trim());
      return ret;
    }
    let tmp = normalrange.split("<=");
    if (tmp.length > 1) {
      ret["val"] = parseFloat(tmp[1].trim());
      return ret;
    }
    tmp = normalrange.split("<");
    if (tmp.length > 1) {
      ret["val"] = parseFloat(tmp[1].trim());
      ret["inc"] = false;
      return ret;
    }
    return nlrt;
  } else {
    return nlrt;
  }
};

const getMin = (normalrange: string) => {
  let ret = { val: 0, inc: true };
  let nlrt = { val: null, inc: false };
  if (normalrange !== undefined && normalrange !== null) {
    let result = Array.from(normalrange.matchAll(regexbe));
    if (result.length > 0) {
      ret["val"] = parseFloat(result[0][1].trim());
      return ret;
    }
    result = Array.from(normalrange.matchAll(regexb));
    if (result.length > 0) {
      ret["val"] = parseFloat(result[0][1].trim());
      return ret;
    }
    result = Array.from(normalrange.matchAll(regexe));
    if (result.length > 0) {
      return nlrt;
    }
    let tmp = normalrange.split(">=");
    if (tmp.length > 1) {
      ret["val"] = parseFloat(tmp[1].trim());
      return ret;
    }
    tmp = normalrange.split(">");
    if (tmp.length > 1) {
      ret["val"] = parseFloat(tmp[1].trim());
      ret["inc"] = false;
      return ret;
    }
    return nlrt;
  } else {
    return nlrt;
  }
};

const getError = (
  number: number,
  max: any,
  xinc: boolean,
  min: any,
  ninc: boolean
) => {
  if (max === null && min === null) {
    return false;
  }
  if (number) {
    if (max !== null && min !== null) {
      if (xinc && ninc) return number > max || number < min;
      if (!xinc && !ninc) return number >= max || number <= min;
      if (!xinc) return number >= max || number < min;
      if (!ninc) return number > max || number <= min;
    }
    if (max === null) {
      if (ninc) return number < min;
      return number <= min;
    }
    if (min === null) {
      if (xinc) return number > max;
      return number >= max;
    }
  } else {
    return false;
  }
};

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "white",
    boxShadow: "1px 1px 2px 2px lightgray",
  },
}));

export const FbTextField = (props: TextFieldProps) => {
  const { edit, left, top, onChangeValue, data } = props;
  const max = getMax(data.range);
  const min = getMin(data.range);
  const [error, setError] = React.useState(
    getError(data.value, max.val, max.inc, min.val, min.inc)
  );
  const val = data && data.value && !nullorblank(data.value) ? data.value : "";
  const [dataValue, setDataValue] = React.useState(val);
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDataValue(event.target.value);
    let number = parseInt(event.target.value);
    setError(getError(number, max.val, max.inc, min.val, min.inc));
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    onChangeValue(event.target.value);
  };

  return (
    data.value && (
      <StyledTooltip
        title={
          !edit && (
            <>
              <Typography
                sx={{
                  fontSize: "14px",
                  fontWeight: "bold",
                  marginLeft: "5px",
                  marginTop: "5px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  color: "black",
                }}
              >
                {data.key}
              </Typography>
              <Typography
                sx={{
                  fontSize: "14px",
                  fontWeight: "bold",
                  marginLeft: "5px",
                  marginTop: "2px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  color: "black",
                }}
              >
                {data.dispname +
                  (data.value ? " - " + data.value + " " + data.unit : "")}
              </Typography>
              <Box
                sx={{
                  backgroundColor: "#666666",
                  height: "1px",
                  width: "260px",
                  marginLeft: "5px",
                  marginRight: "2px",
                  marginTop: "5px",
                }}
              />
              <Box
                sx={{
                  display: "flex",
                  marginLeft: "5px",
                  marginRight: "5px",
                  marginTop: "2px",
                  verticalAlign: "center",
                }}
              >
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    color: "black",
                  }}
                >
                  Specimen:
                </Typography>
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    fontWeight: "bold",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    color: "black",
                  }}
                >
                  {data.specimen}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  marginLeft: "5px",
                  marginRight: "5px",
                  marginTop: "2px",
                  verticalAlign: "center",
                }}
              >
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    color: "black",
                  }}
                >
                  Normal Range:
                </Typography>
                <Typography
                  sx={{
                    fontSize: "15px",
                    width: "130px",
                    fontWeight: "bold",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    color: "black",
                  }}
                >
                  {data.range}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  marginLeft: "5px",
                  marginRight: "5px",
                  marginTop: "2px",
                  verticalAlign: "center",
                }}
              >
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    color: "black",
                  }}
                >
                  Collection Time:
                </Typography>
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    fontWeight: "bold",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    color: "black",
                  }}
                >
                  {data.collecttime}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  marginLeft: "5px",
                  marginRight: "5px",
                  marginTop: "2px",
                  marginBottom: "5px",
                  verticalAlign: "center",
                }}
              >
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    color: "black",
                  }}
                >
                  Collected by:
                </Typography>
                <Typography
                  sx={{
                    fontSize: "14px",
                    width: "130px",
                    fontWeight: "bold",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    color: "black",
                  }}
                >
                  {data.collectby}
                </Typography>
              </Box>
            </>
          )
        }
      >
        <BaseTextField
          sx={{
            position: "absolute",
            left: left,
            top: top,
            width: "50px",
            height: "25px",
            zIndex: "2",
          }}
          id={data.key ? data.key : ""}
          error={error}
          disabled={!edit}
          value={dataValue}
          onChange={handleChange}
          onBlur={handleBlur}
        />
      </StyledTooltip>
    )
  );
};
