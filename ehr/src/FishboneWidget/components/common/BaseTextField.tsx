import TextField from "@mui/material/TextField";
import { styled } from "@mui/material/styles";

export const BaseTextField = styled(TextField, {
  shouldForwardProp: (prop) => prop !== "height",
})((props: any) => ({
  background: "white",
  font: "normal 400 15px Roboto",
  width: 50,
  borderRadius: 2,
  "& .MuiInputBase-root": {
    fontSize: "inherit",
    fontFamily: "inherit",
    padding: "0px 4px",
  },
  "& .MuiOutlinedInput-input": {
    color: props.error ? "red" : "black",
    overflow: "hidden",
    textOverflow: "ellipsis",
    font: "inherit",
    height: "25px",
    padding: "0px 0px 0px 0px",
    "&::placeholder": {
      color: "#a9a9a9",
      opacity: 1,
    },
  },
  "& .MuiFormLabel-root": {
    color: "#A9A9A9",
  },
  "& .Mui-disabled": {
    background: "#EBEBEB",
    color: "#B1B1B1",
  },
  "& .MuiInputBase-input.Mui-disabled": {
    WebkitTextFillColor: props.error ? "red" : "black",
  },
  "& .MuiOutlinedInput-notchedOutline": {
    borderRadius: 2,
  },
  "& .MuiOutlinedInput-root": {
    "&.Mui-focused fieldset, &:hover .MuiOutlinedInput-notchedOutline": {
      border: "1px solid #a9a9a9",
    },
    "&.Mui-error": {
      "& .MuiOutlinedInput-notchedOutline": {
        border: "1px solid #a9a9a9",
      },
    },
  },
  "& .MuiOutlinedInput-inputMarginDense": {
    padding: 0,
  },
}));
