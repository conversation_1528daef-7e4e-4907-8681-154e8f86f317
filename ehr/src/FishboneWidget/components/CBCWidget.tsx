import { Box } from "@mui/material";
import { Stage, Layer, Line } from "react-konva";
import { FbTextField } from "./common/FbTextField";
import { getBottomIndex, getTopIndex } from "./DataUtils";

export default function CBCWidget(props: any) {
  const height = 120;
  const startX = 15;
  const perWidth = 70;
  const perHeight = 60;
  const fieldHeight = 25;
  const fieldWidth = 50;
  const { editable, data, onChange } = props;

  const LineTop = () => {
    return (
      <Line
        linecap="round"
        linejoin="round"
        stroke="#aeaeae"
        strokwidth={0.5}
        points={[
          startX,
          0,
          startX + perWidth,
          perHeight,
          startX + perWidth * 2,
          perHeight,
          startX + perWidth * 3,
          0,
        ]}
      />
    );
  };

  const LineBottom = () => {
    return (
      <Line
        linecap="round"
        linejoin="round"
        stroke="#aeaeae"
        strokwidth={0.5}
        points={[
          startX,
          height,
          startX + perWidth,
          perHeight,
          startX + perWidth * 2,
          perHeight,
          startX + perWidth * 3,
          height,
        ]}
      />
    );
  };

  return (
    data && (
      <Box
        sx={{
          position: "relative",
          marginTop: "15px",
          marginLeft: "30px",
        }}
      >
        <Stage width={300} height={height}>
          <Layer>
            <LineTop />
            <LineBottom />
          </Layer>
        </Stage>
        {data.HEADER && (
          <FbTextField
            edit={editable}
            left={startX - 15}
            top={perHeight - fieldHeight / 2}
            data={data.HEADER}
            onChangeValue={(value: string) => {
              const newData = { ...data };
              newData.HEADER = {
                ...data.HEADER,
                value: value,
              };
              onChange && onChange(newData);
            }}
          />
        )}
        {getTopIndex(data) !== -1 && (
          <FbTextField
            edit={editable}
            left={startX + perWidth + (perWidth - fieldWidth) / 2}
            top={perHeight - fieldHeight - 20}
            data={data.component[getTopIndex(data)]}
            onChangeValue={(value: string) => {
              const newData = { ...data };
              newData.component[getTopIndex(data)] = {
                ...data.component[getTopIndex(data)],
                value: value,
              };
              onChange && onChange(newData);
            }}
          />
        )}
        {data.TAIL && (
          <FbTextField
            edit={editable}
            left={startX + perWidth * 3 + 15 - fieldWidth}
            top={perHeight - fieldHeight / 2}
            data={data.TAIL}
            onChangeValue={(value: string) => {
              const newData = { ...data };
              newData.TAIL = {
                ...data.TAIL,
                value: value,
              };
              onChange && onChange(newData);
            }}
          />
        )}
        {getBottomIndex(data) !== -1 && (
          <FbTextField
            edit={editable}
            left={startX + perWidth + (perWidth - fieldWidth) / 2}
            top={perHeight + 20}
            data={data.component[getBottomIndex(data)]}
            onChangeValue={(value: string) => {
              const newData = { ...data };
              newData.component[getBottomIndex(data)] = {
                ...data.component[getBottomIndex(data)],
                value: value,
              };
              onChange && onChange(newData);
            }}
          />
        )}
      </Box>
    )
  );
}
