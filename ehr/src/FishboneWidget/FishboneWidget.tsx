import * as React from "react";

import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";

import BMPWidget from "./components/BMPWidget";
import { getFishboneData } from "./util/DataAPI";
import { getWidgetType, parseMainData } from "./components/DataUtils";
import CBCWidget from "./components/CBCWidget";
import ABGWidget from "./components/ABGWidget";
import type { ScreenComponentProps } from "@cci-monorepo/common/utils/screenUtil";

const Root = styled("main")(({ theme }) => ({
  position: "relative", // Make it fit in webframe Tab
  height: "100%",
  width: "100%",
}));

export default function FishboneWidget(props: ScreenComponentProps) {
  const dbpath = Cci?.Patient?.getDbpath();
  const { fbconf } = (props.screenParams as unknown as {
    fbconf: any[];
  }) || { fbconf: [] };
  const [checked, setChecked] = React.useState(false);
  const [displayData, setDisplayData] = React.useState<any[]>([]);
  var type: string = "";

  React.useEffect(() => {
    const onSuccess = (result: any) => {
      if (result && result.length > 0) {
        let dataArr: any[] = [];
        result.forEach((item: any) => {
          let data = parseMainData(item.data);
          dataArr.push(data);
        });
        setDisplayData(dataArr);
      }
    };
    const onFailure = (error: string) => {
      console.error("Failed to get lab fishbone data.", error);
      // TODO open dialog here
    };
    // Get lab fishbone data
    if (fbconf && fbconf.length > 0) {
      let promises = fbconf.map((item: any) => {
        return getFishboneData(item, () => {}, onFailure);
      });
      Promise.all(promises).then((ret: any) => {
        onSuccess(ret);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };

  const getWidth = (type: string) => {
    switch (type) {
      case "CBC":
        return 305;
      case "BMP":
        return 355;
      case "ABG":
        return 480;
      default:
        return 0;
    }
  };

  return (
    <Root>
      {dbpath && dbpath.trim() ? (
        <Box
          sx={{
            position: "relative",
            height: "100%",
            width: "100%",
          }}
        >
          <FormControlLabel
            sx={{ marginLeft: "15px", display: "none" }}
            control={<Switch checked={checked} onChange={handleChange} />}
            label="Edit"
            labelPlacement="start"
          />
          <Box
            display="flex"
            sx={{
              width: "100%",
            }}
          >
            <Grid container spacing={2}>
              {displayData.map(
                (item: any, index: number) =>
                  (type = getWidgetType(item)) !== "" && (
                    <Grid key={index} item sx={{ width: getWidth(type) }}>
                      <Box display="flex" flexDirection="column">
                        <Typography sx={{ marginLeft: "30px" }} variant="h6">
                          {item?.title}
                        </Typography>
                        {type === "CBC" && (
                          <CBCWidget
                            editable={checked}
                            data={item}
                            onChange={(v: any) => {
                              let tmpData = displayData;
                              tmpData[index] = v;
                              setDisplayData([...tmpData]);
                            }}
                          />
                        )}
                        {type === "BMP" && (
                          <BMPWidget
                            editable={checked}
                            data={item}
                            onChange={(v: any) => {
                              let tmpData = displayData;
                              tmpData[index] = v;
                              setDisplayData([...tmpData]);
                            }}
                          />
                        )}
                        {type === "ABG" && (
                          <ABGWidget
                            editable={checked}
                            data={item}
                            onChange={(v: any) => {
                              let tmpData = displayData;
                              tmpData[index] = v;
                              setDisplayData([...tmpData]);
                            }}
                          />
                        )}
                      </Box>
                    </Grid>
                  )
              )}
            </Grid>
          </Box>
        </Box>
      ) : (
        <h3>Please select a patient.</h3>
      )}
    </Root>
  );
}
