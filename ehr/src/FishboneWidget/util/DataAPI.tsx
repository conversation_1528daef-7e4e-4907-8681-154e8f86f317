/**
 * DataAPI.tsx
 *
 * @author: RnD
 * @description: Common data API to server
 */
import { serverRequest } from "./DataRequests";

/**
 * Get fishbone data
 * @params {object} params
 * @params {callback} onSuccess
 * @params {callback} onFailure
 * @returns nothing
 */
export const getFishboneData = (
  params: any,
  onSuccess: (data: any) => void,
  onFailure: (error: string) => void
) => {
  return serverRequest(
    "labfishbone/getdata",
    { conf: params },
    onSuccess,
    onFailure
  );
};
