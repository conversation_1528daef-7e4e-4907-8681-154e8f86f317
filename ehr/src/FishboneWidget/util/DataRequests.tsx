/**
 * DataRequests.tsx
 *
 * @author: RnD
 * @description Base data request API to the server
 */

import { requestUnorderedUnfilteredRecords } from "./Hobj";

/**
 * Generic YCQL request
 * @params {string} hobj
 * @params {object} params
 * @params {callback} onSuccess
 * @params {callback} onFailure
 * @returns nothing
 */
export const serverRequest = async (
  hobj: string,
  params: any,
  onSuccess: (data: any) => void,
  onFailure: (error: any) => void
) => {
  let jsonData: any = {};
  const encounterInfo = Cci.RunTime.getEncounterInfo();
  try {
    params.dbpath = Cci.util.Patient.getDbpath();
    jsonData = await requestUnorderedUnfilteredRecords({
      hobj: hobj,
      noBatch: true,
      dbs: [Cci.util.Patient.getDbpath()],
      visitkey: encounterInfo.visitkey,
      staffid: Cci.util.Staff.getSid(),
      params: params,
    });
    if (!jsonData.errorMsg) {
      if (onSuccess) {
        onSuccess(jsonData);
      }
    } else {
      if (onFailure) {
        onFailure(jsonData.errorMsg);
      }
    }
  } catch (error) {
    if (onFailure) {
      onFailure(error);
    } else {
      console.error(error);
    }
  }
  return jsonData;
};
