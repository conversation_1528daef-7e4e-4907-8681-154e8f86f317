// @ts-nocheck

import { useHobjsQuery } from "./useHobjsQuery";

/**
 * Get Order Category/Sub-category/Order-type list
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} extra, optional, the optional parameters for dataParseFn
 */
export const useOrderCategoryQuery = (
  options: any,
  dataParseFn: any,
  extra = null
) => {
  const hobj = "cpoe2/browseCategories";
  const queryKey = {
    hobj: hobj,
    params: options,
    extra,
  };

  return useHobjsQuery(queryKey, dataParseFn, extra);
};

/**
 * Help function to parse raw category data from server to array data
 * Raw data format:
 *   ["CatID", "cat", "subcat", "chc", "role", "display", "type", "mask", "sdname", "coredef", "nitref", "display", "icon"]
 * @param {Array} categoryData raw data from server
 * @returns objects of category data with category as key
 */
export const parseOrderCategory = (categoryData: any) => {
  let categories = {};
  const rawdata = categoryData?.categories?.data;
  if (rawdata) {
    for (let i = 0; i < rawdata.length; i++) {
      const oneData = rawdata[i];

      if (!categories[oneData[1]]) {
        categories[oneData[1]] = {
          key: oneData[1],
          label: oneData[1],
          children: [],
          catid: oneData[0],
          cat: oneData[1],
          subcat: "",
          chc: oneData[3],
          type: oneData[6],
          mask: oneData[7],
          selectedOrderIds: [],
        };
      }

      categories[oneData[1]].children.push({
        key: oneData[1] + "_" + oneData[2],
        label: oneData[2],
        catid: oneData[0],
        cat: oneData[1],
        subcat: oneData[2],
        chc: oneData[3],
        type: oneData[6],
        mask: oneData[7],
        selectedOrderIds: [],
      });
    }
  }

  return categories;
};
