// @ts-nocheck
import { useMutation } from "@tanstack/react-query";
import { tabs as NonMedAppTypes } from "../Constants";

/**
 * Wrapper for react-query useMutation to save  create/edit Non-Medication data.
 * @param {string} dataType required, for this hook, the type of data, 'set', or 'group'
 * @param {object} data required, for mutationFn the real data
 * @returns `MutationResult` The function and variables associated with saving data
 */
export const useSaveNonMedMutation = () => {
  return useMutation({
    mutationFn: (data: any) => {
      const saveData = getSaveData(data);
      const hobjRequest = {
        hobj: "cpoe2/nonmed/savedata",
        params: saveData,
      };
      return Cci.util.Hobj.requestUnorderedUnfilteredRecords(hobjRequest);
    },
  });
};

const getDataType = (tabType: string) => {
  let dataType: string = "";
  switch (tabType) {
    case NonMedAppTypes.LABS:
      dataType = "lab";
      break;

    case NonMedAppTypes.RADIOLOGY:
      dataType = "radiology";
      break;

    case NonMedAppTypes.THERAPIES:
      dataType = "therapies";
      break;

    case NonMedAppTypes.OTHER:
      dataType = "other";
      break;

    default:
      console.error("Unsupported tab type", tabType);
  }
  return dataType;
};

const getNewData = (data: any) => {
  const dataType = getDataType(data.tabtype);

  const tmpData = data.data;
  let newData = {};
  if (data.islab) {
    newData = {
      Env: ".",
      ID: "",
      ICode: "",
      NAME: tmpData.name,
      CatID: tmpData.catid,
      Priority: "",
      AOEQuestions: "",
      Chargeable: "",
      CDM: "",
      CPT: "",
      Scheduleable: "",
      DupCheck: "",
      SpecimenType: "",
      SpecimenSource: "",
      CollectionMethod: "",
      Defaults: {
        cat: tmpData.cat,
        subcat: tmpData.subcat,
        order_type: tmpData.order_type,
        chc: tmpData.chc,
        mask: tmpData.mask,
        Status: "Build In-Progress",
      },
    };
  } else {
    newData = {
      env: ".",
      chc: tmpData.chc,
      cat: tmpData.cat,
      subcat: tmpData.subcat,
      order_type: tmpData.order_type,
      subtype: "",
      catid: tmpData.catid,
      mask: tmpData.mask,
      name: tmpData.name,
      orderid: "",
      frequency: "",
      defaults: {
        Status: "Build In-Progress",
      },
      flag: "",
    };
  }

  const saveData = {
    isnew: true,
    islab: data.islab,
    nonmedtype: dataType,
    data: JSON.stringify(newData),
  };

  return saveData;
};

const getOrderQuestions = (env: string, ccid: string, questionData: any) => {
  let questions = null;
  if (questionData) {
    questions = [];
    questionData.forEach((q) => {
      questions.push({
        env: env,
        ccid: ccid,
        question_id: q.question_id,
        question: q.Question,
        response_type: q["Response Type"],
        chc_name: q["Choice List"],
        dbi: q["DBI"],
        is_required: q["Required"],
      });
    });
  }
  return questions;
};

const getOneLabData = (data: any) => {
  let oneData = {};
  let dataDefaults = {};
  let questions = null;

  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case "env":
        oneData["Env"] = value == "Default" ? "." : value;
        break;
      case "catid":
        oneData["CatID"] = value;
        break;
      case "name":
        oneData["NAME"] = value;
        break;
      case "cdm":
        oneData["CDM"] = value;
        break;
      case "cpt":
        oneData["CPT"] = value;
        break;
      case "ID":
        oneData[key] = value;
        break;
      case "AOEQuestions":
        questions = getOrderQuestions(
          data["env"] == "Default" ? "." : data["env"],
          data["ID"],
          value
        );
        break;
      case "DupCheck":
      case "SpecimenType":
      case "SpecimenSource":
        oneData[key] = value;
        dataDefaults[key] = value;
        break;
      case "Default_Priority":
        oneData["Priority"] = value;
        dataDefaults["Default_Priority"] = value;
        break;
      case "scheduleable":
        oneData["Scheduleable"] = value;
        dataDefaults["Scheduleable"] = value;
        break;
      case "Chargeable":
        oneData["Chargeable"] = value;
        dataDefaults["Chargeable"] = value;
        break;
      case "ICode":
      case "CollectionMethod":
        oneData[key] = value;
        dataDefaults[key] = value;
        break;
      case "defaults":
      case "detailData":
      case "order_display_name":
      case "envpath":
        break;
      default:
        dataDefaults[key] = value;
        break;
    }
  }
  oneData["Defaults"] = dataDefaults;
  if (questions) {
    oneData["questions"] = questions;
  }
  return oneData;
};

const getOneNonMedData = (data: any) => {
  let oneData = {};
  let dataDefaults = {};
  let questions = null;

  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case "env":
        oneData[key] = value == "Default" ? "." : value;
        break;
      case "chc":
      case "cat":
      case "subcat":
      case "order_type":
      case "subtype":
      case "catid":
      case "mask":
      case "name":
      case "flag":
        oneData[key] = value;
        break;
      case "ccid":
        oneData["orderid"] = value;
      case "defaults":
      case "detailData":
      case "order_display_name":
      case "envpath":
        break;
      case "frequency":
        /**
         * frequency separate column in nonmed db is not used,
         * instead, frequency in OE is pulled from defaults json element.
         */
        oneData[key] = "";
        dataDefaults[key] = value;
        break;
      case "Order_QnA":
        questions = getOrderQuestions(
          data["env"] == "Default" ? "." : data["env"],
          data["orderid"],
          value
        );
        break;
      default:
        dataDefaults[key] = value;
        break;
    }
  }
  oneData["defaults"] = dataDefaults;
  if (questions) {
    oneData["questions"] = questions;
  }
  return oneData;
};

/**
 * Convert one edit data to saveable data
 */
const getOneEditData = (data: any) => {
  const tmpData = data.data;
  let editData = {};
  if (data.islab) {
    editData = getOneLabData(tmpData);
  } else {
    editData = getOneNonMedData(tmpData);
  }

  return editData;
};

const getDuplicateData = (data: any) => {
  const dataType = getDataType(data.tabtype);
  let oneData = getOneEditData(data);

  // only global env can be dup'ed
  if (data.islab) {
    oneData["Env"] = ".";
  } else {
    oneData["env"] = ".";
  }

  const editData = {
    isnew: true,
    islab: data.islab,
    nonmedtype: dataType,
    data: JSON.stringify(oneData),
  };

  return editData;
};

const getInactiveData = (data: any) => {
  const dataType = getDataType(data.tabtype);
  const editData = {
    islab: data.islab,
    nonmedtype: dataType,
    data: JSON.stringify(getOneEditData(data)),
  };

  return editData;
};

const getEditData = (data: any) => {
  const dataType = getDataType(data.tabtype);
  const isArrayData = Array.isArray(data);
  let tmpData;
  if (isArrayData) {
    tmpData = [];
    data.forEach((item) => {
      tmpData.push(getOneEditData(item));
    });
  } else {
    tmpData = getOneEditData(data);
  }
  const editData = {
    islab: data.islab,
    nonmedtype: dataType,
    data: JSON.stringify(tmpData),
  };

  return editData;
};

/**
 * Help function convert Non-Med data to saveable object.
 * NonMed database columns: (env,chc,cat,subcat,order_type,subtype,catid,mask,name, orderid, frequency, defaults,flag)
 * Lab database columns: (Env,ID,ICode,LOINC,NAME,CATID,Priority,AOEQuestions,Chargeable,CDM,CPT,Scheduleable,DupCheck,SpecimenType,SpecimenSource,CollectionMethod,Defaults)
 */
export const getSaveData = (data: any) => {
  const savedata = data.isnew
    ? getNewData(data)
    : data.isdup
      ? getDuplicateData(data)
      : getEditData(data);

  return savedata;
};
