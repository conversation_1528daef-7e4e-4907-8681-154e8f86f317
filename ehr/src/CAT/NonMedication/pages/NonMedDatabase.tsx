import React, { useEffect, useReducer } from "react";
import { Box } from "@mui/material";
import ActiveStatusTabs from "@cci-monorepo/Psat/md/components/ActiveStatusTabs";
import {
  mdInitState,
  mdStateReducer,
} from "@cci-monorepo/Psat/md/context/MdStore";
import PropTypes from "prop-types";
import ErrorDialog from "@cci-monorepo/Psat/common/layouts/AdminLayoutA/components/dialogs/ErrorDialog";
import UniversalTabs, {
  TabConfig,
} from "@cci-monorepo/common/mui-components/src/components/tabs/UniversalTabs";
import TopAdminTabs from "@cci-monorepo/common/mui-components/src/components/tabs/Admin/TopAdminTabs";
import { MDProvider } from "@cci-monorepo/Psat/md/context/MdContext";
import { CciMainTab } from "@cci/mui-components";
import { tabs } from "@cci-monorepo/CAT/NonMedication/Constants";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { queryClientDefaultOptions } from "@cci-monorepo/common";
import { ColumnSettingsProvider } from "@psat-src/common/layouts/AdminLayoutA/context/GeneralColumnsContext"; // Initialize queryClient for QueryClientProvider

// Initialize queryClient for QueryClientProvider
const queryClient = new QueryClient(queryClientDefaultOptions);

// export const tabs = {
//   AllOrders: "NMD:All Orders",
//   Labs: "NMD:Labs",
//   Cardiac: "NMD:Cardiac Procedures & Radiology",
//   Consults: "NMD: Consults & Referrals",
//   // SINGLE: 0,
//   // CUSTOMMED: 1,
//   // ORDERABLE: 2,
//   // COMPOUND: 3,
// };

// const tabMapping = {
//   Single: tabs.SINGLE,
//   Custom: tabs.CUSTOMMED,
//   Orderable: tabs.ORDERABLE,
//   Compounding: tabs.COMPOUND,
// };

const loadedTabIndices = [true, false, /*false,*/ false, false];

function MdTool({ show = true, ...props }) {
  // const [activeTab, setActiveTab] = React.useState(0);
  // const [jumpTab, setJumpTab] = React.useState(null);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [dialogData, setDialogData] = React.useState({});

  // MDStore is the state across all MD tabs.
  // topRec is set in the store to be displayed for auto-tab switching triggered by
  // duplicate med rec action in Single Med to Custom Med Tab.
  const [mdState, mdDispatch] = useReducer(mdStateReducer, mdInitState);

  const handleChange = (
    event: React.SyntheticEvent | null,
    newValue: number
  ) => {
    loadedTabIndices[newValue] = true;
  };

  // /**
  //  *
  //  * @param {*} rxid
  //  * @return type: Single, Orderable, Compounding, Unknown
  //  */
  // async function getRxIDType(rxid: string) {
  //   try {
  //     let jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
  //       hobj: "ccrx/getrxidtype",
  //       params: { rxid: rxid },
  //       noBatch: true,
  //     });

  //     if (jsonData.RxID_Type.data.length === 0) {
  //       return {
  //         success: false,
  //         message: "Unexpected return for RxID: " + rxid,
  //       };
  //     }

  //     const type = jsonData.RxID_Type.data[0][0];

  //     return type === "Unknown"
  //       ? {
  //           success: false,
  //           type: type,
  //           message: "Cannot Determine the Type for RxID: " + rxid,
  //         }
  //       : { success: true, type: type, message: null };
  //   } catch (Err: any) {
  //     return { success: false, message: Err.message };
  //   }
  // }

  type ResponseType = {
    success: boolean;
    type?: string;
    message: string | null;
  };

  useEffect(() => {
    // if (props.jumpObj && props.jumpObj.rxid) {
    //   getRxIDType(props.jumpObj.rxid).then((data: ResponseType) => {
    //     if (data.message == null && data.type !== undefined) {
    //       const tabIdx = tabMapping[data.type as keyof typeof tabMapping];
    //       handleChange(null, tabIdx);
    //       // setJumpTab(tabIdx);
    //     } else {
    //       setDialogOpen(true);
    //       setDialogData({ errorMsg: data.message });
    //     }
    //   });
    // }
  }, [props.jumpObj]);

  const tabData: TabConfig[] = [
    {
      id: tabs.LABS,
      label: "Labs",
      style: { zIndex: "5" },
      content: (
        <ActiveStatusTabs
          appType={tabs.LABS}
          show={true}
          switchMDTab={handleChange}
          onStatusChange={props.onStatusChange}
          jumpObj={props.jumpObj}
          recordItemName="Non-Medication"
          recordToolbarPlaceholder="Search Non-Medication Database"
        />
      ),
    },
    {
      id: tabs.RADIOLOGY,
      label: "Radiology",
      style: { zIndex: "4" },
      content: (
        <ActiveStatusTabs
          appType={tabs.RADIOLOGY}
          show={true}
          switchMDTab={handleChange}
          onStatusChange={props.onStatusChange}
          jumpObj={props.jumpObj}
          recordItemName="Non-Medication"
          recordToolbarPlaceholder="Search Non-Medication Database"
        />
      ),
    },
    {
      id: tabs.THERAPIES,
      label: "Therapies (PT/OT/SLP)",
      style: { zIndex: "3" },
      content: (
        <ActiveStatusTabs
          appType={tabs.THERAPIES}
          show={true}
          switchMDTab={handleChange}
          onStatusChange={props.onStatusChange}
          jumpObj={props.jumpObj}
          recordItemName="Non-Medication"
          recordToolbarPlaceholder="Search Non-Medication Database"
        />
      ),
    },
    {
      id: tabs.CONSULTS,
      label: "Consults & Referrals",
      style: { zIndex: "2" },
      content: (
        <ActiveStatusTabs
          appType={tabs.CONSULTS}
          show={true}
          switchMDTab={handleChange}
          onStatusChange={props.onStatusChange}
          jumpObj={props.jumpObj}
          recordItemName="Non-Medication"
          recordToolbarPlaceholder="Search Non-Medication Database"
        />
      ),
    },
    {
      id: tabs.OTHER,
      label: "Other Orders",
      style: { zIndex: "1" },
      content: (
        <ActiveStatusTabs
          appType={tabs.OTHER}
          show={true}
          switchMDTab={handleChange}
          onStatusChange={props.onStatusChange}
          jumpObj={props.jumpObj}
          recordItemName="Non-Medication"
          recordToolbarPlaceholder="Search Non-Medication Database"
        />
      ),
    },
  ];

  return (
    <QueryClientProvider client={queryClient}>
      <Box
        style={{
          height: "100%",
          display: show ? "flex" : "none",
          flexDirection: "column",
        }}
      >
        <ErrorDialog
          data={dialogData}
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
        />
        <MDProvider value={{ mdState, mdDispatch }}>
          <ColumnSettingsProvider pageType={"nonmeddatabase"}>
            <UniversalTabs
              tabs={tabData}
              TabsComponents={TopAdminTabs}
              TabComponent={CciMainTab}
              sx={{
                backgroundColor: "#637e83",
                "& .MuiTabs-indicator": {
                  opacity: "0",
                },
              }}
            />
          </ColumnSettingsProvider>
        </MDProvider>
      </Box>
    </QueryClientProvider>
  );
}

MdTool.propTypes = {
  show: PropTypes.bool,
};

export default MdTool;
