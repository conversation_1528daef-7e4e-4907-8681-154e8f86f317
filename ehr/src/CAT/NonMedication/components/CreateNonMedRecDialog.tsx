// @ts-nocheck
import React, { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import { Box, Typography, Stack } from "@mui/material";
import {
  Button as CciButton,
  <PERSON>ciDialog,
  CciRequiredFieldMarker,
  InputField28,
} from "@cci/mui-components";
import Combobox from "@oe-src/common/Combobox";
import {
  parseOrderCategory,
  useOrderCategoryQuery,
} from "../../hooks/useOrderCategoryQuery";
import { useSaveNonMedMutation } from "../hooks/useNonMedMutation";
import { tabs as NonMedAppTypes, NONMED_DIALOG_TYPES } from "../Constants";

const StyledLabel = styled(Typography)(() => ({
  fontSize: 15,
  fontWeight: 700,
  fontFamily: "Helvetica",
  color: "#11181F",
  minWidth: "150px",
  marginRight: "4px",
  textWrap: "nowrap",
  display: "flex",
  alignItems: "center",
}));

const StyledBox = styled(Box)(() => ({
  display: "flex",
}));

const FlexedBox = styled(Box)(() => ({
  flex: 1,
}));

const StyledCombobox = styled(Combobox)({
  width: "216px",
  "& .MuiOutlinedInput-root": {
    height: "28px",
  },
});

const defaultCategory = {
  catid: "",
  cat: "",
  subcat: "",
  order_type: "",
};

type CreateNonMedRecDialogProps = {
  selectedRecordData?: any;
  nonMedTab: string;
  nonMedDialogType: string;
  open: boolean;
  onClose: any;
};

const CreateNonMedRecDialog = ({
  selectedRecordData,
  nonMedTab,
  nonMedDialogType,
  open,
  onClose,
}: CreateNonMedRecDialogProps) => {
  const initialSelectCategoryValue = selectedRecordData?.srcData
    ? {
        ...selectedRecordData?.srcData,
        order_type: selectedRecordData.srcData.order_type,
      }
    : defaultCategory;

  const initialRecordName = selectedRecordData?.srcData?.name
    ? `Dup ${selectedRecordData?.srcData?.name}`
    : "";

  const [recordName, setRecordName] = useState(initialRecordName);
  const [selectedCategory, setSelectedCategory] = useState(
    initialSelectCategoryValue
  );

  const [categoryOptions, setCategoryOptions] = useState([]);
  const [subCategoryOptions, setSubCategoryOptions] = useState([]);

  const dialogPrefix =
    nonMedDialogType === NONMED_DIALOG_TYPES.CREATENMD
      ? "Create"
      : nonMedDialogType === NONMED_DIALOG_TYPES.DUPLICATENMD
        ? "Duplicate"
        : "";

  const getCategoryList = (allCategoriesData) => {
    return Object.values(allCategoriesData).map((category) => category);
  };

  const getDefaultCategory = (allCategoriesData) => {
    const appType = nonMedTab;
    const allCategories = Object.values(allCategoriesData);
    switch (appType) {
      case NonMedAppTypes.LABS:
        return allCategories.filter((category) => category.key === "Lab")?.[0];
      case NonMedAppTypes.RADIOLOGY:
        return allCategories.filter(
          (category) => category.key === "Imaging"
        )?.[0];
      case NonMedAppTypes.THERAPIES:
        return allCategories.filter(
          (category) => category.key === "Therapy"
        )?.[0];
      case NonMedAppTypes.CONSULTS:
        return allCategories.filter(
          (category) => category.key === "Consults"
        )?.[0];
    }
  };

  const getSubCategoryList = (
    categoryToGetSubCategoriesFrom,
    allCategoriesData
  ) => {
    let subCatList = [];
    if (categoryToGetSubCategoriesFrom) {
      subCatList = allCategoriesData?.[categoryToGetSubCategoriesFrom]?.children
        ? allCategoriesData?.[categoryToGetSubCategoriesFrom]?.children
        : [];
    }
    return subCatList;
  };

  const handleClose = (event, reason) => {
    if (reason && reason === "backdropClick") return;
    setRecordName("");
    onClose();
  };

  const createNonMedMutate = useSaveNonMedMutation();
  const handleCreate = () => {
    const newRecord = {
      isnew: true,
      islab: selectedCategory.cat === "Lab" ? true : false,
      tabtype: nonMedTab,
      data: {
        name: recordName,
        catid: selectedCategory.catid,
        cat: selectedCategory.cat,
        subcat: selectedCategory.subcat,
        order_type: selectedCategory.order_type,
        chc: selectedCategory.chc,
        mask: selectedCategory.mask,
      },
    };
    createNonMedMutate.mutate(newRecord);
    setRecordName("");
    onClose();
  };

  const handleDuplicate = () => {
    const duplicateRecord = {
      isdup: true,
      islab: selectedCategory.cat === "Lab" ? true : false,
      tabtype: nonMedTab,
      data: {
        ...selectedRecordData.srcData,
        name: recordName,
        catid: selectedCategory.catid,
        cat: selectedCategory.cat,
        subcat: selectedCategory.subcat,
        order_type: selectedCategory.order_type,
        chc: selectedCategory.chc,
        mask: selectedCategory.mask,
      },
    };
    if (duplicateRecord.data.defaults === null) {
      duplicateRecord.data.defaults = {};
    }

    createNonMedMutate.mutate(duplicateRecord);
    setRecordName("");
    onClose();
  };

  const handleNameInput = (event) => {
    const name = event.target.value;
    setRecordName(name);
  };

  const handleCategoryChange = (categoryObject: any | null) => {
    const newSelectedCategory = {
      cat: categoryObject.cat,
      catid: categoryObject.catid,
      subcat: categoryObject.subcat ? categoryObject.subcat : "",
      order_type: categoryObject.order_type,
      chc: categoryObject.chc,
      mask: categoryObject.mask,
    };
    setSelectedCategory(newSelectedCategory);
  };

  const createNonMedRecDialogContent = () => {
    return (
      <Stack sx={{ p: "8px", pt: "32px" }} spacing="16px">
        <Typography sx={{ fontSize: 15 }}>
          To <b>{dialogPrefix}</b> a Non-Medication Record, specify the
          following:
        </Typography>
        <StyledBox>
          <StyledLabel>
            Order Name:
            <CciRequiredFieldMarker />
          </StyledLabel>
          <InputField28
            value={recordName}
            onChange={handleNameInput}
            sx={{ width: "216px" }}
          />
        </StyledBox>
        <StyledBox>
          <StyledLabel>
            Order Category:
            <CciRequiredFieldMarker />
          </StyledLabel>
          <FlexedBox>
            <StyledCombobox
              data-testid={"create-non-med-record-category-combobox-id"}
              value={
                categoryOptions.find((c) => c.cat === selectedCategory.cat) ?? {
                  cat: selectedCategory.cat,
                }
              }
              options={categoryOptions}
              placeholder={"Category"}
              onChange={(_, newVal) => {
                handleCategoryChange(newVal);
              }}
              isOptionEqualToValue={(option, value) => option.cat === value.cat}
              getOptionLabel={(option) => option.cat || ""}
              disableClearable
            />
          </FlexedBox>
        </StyledBox>
        <StyledBox>
          <StyledLabel>
            Order Subcategory:
            <CciRequiredFieldMarker />
          </StyledLabel>
          <FlexedBox>
            <StyledCombobox
              data-testid={"create-non-med-record-sub-category-combobox-id"}
              value={
                subCategoryOptions.find(
                  (sc) => sc.subcat === selectedCategory.subcat
                ) ?? { subcat: selectedCategory.subcat ?? "" }
              }
              options={subCategoryOptions}
              placeholder={"Subcategory"}
              onChange={(_, newVal) => {
                handleCategoryChange(newVal);
              }}
              isOptionEqualToValue={(option, value) =>
                option.subcat === value.subcat
              }
              getOptionLabel={(option) => option.subcat}
              disableClearable
            />
          </FlexedBox>
        </StyledBox>
      </Stack>
    );
  };

  const createNonMedRecDialogButtons = () => {
    return (
      <CciButton
        color="primary"
        onClick={() => {
          nonMedDialogType === NONMED_DIALOG_TYPES.DUPLICATENMD
            ? handleDuplicate()
            : handleCreate();
        }}
        disabled={
          recordName &&
          selectedCategory &&
          selectedCategory.cat &&
          selectedCategory.subcat
            ? false
            : true
        }
      >
        OK
      </CciButton>
    );
  };

  // Get category data from server
  const categoryData = useOrderCategoryQuery({}, parseOrderCategory);

  useEffect(() => {
    const allCategoriesData = categoryData.data;
    setCategoryOptions(getCategoryList(allCategoriesData));
    setSubCategoryOptions(
      getSubCategoryList(selectedCategory.cat, allCategoriesData)
    );
    if (
      !selectedCategory?.cat &&
      nonMedDialogType === NONMED_DIALOG_TYPES.CREATENMD
    ) {
      const newDefaultCategory = getDefaultCategory(allCategoriesData);
      if (newDefaultCategory?.cat) handleCategoryChange(newDefaultCategory);
    }
  }, [categoryData?.data, selectedCategory]);

  return (
    <CciDialog
      data-testid="create-non-med-record-dialog-id"
      title={`${dialogPrefix} Non-Medication Record`}
      onClose={handleClose}
      setOpen={handleClose}
      open={open}
      content={createNonMedRecDialogContent()}
      buttons={createNonMedRecDialogButtons()}
      draggable
      sx={{
        "& .MuiDialog-paper": {
          width: "450px",
          boxShadow: "3px",
        },
        "& .MuiModal-backdrop": {
          backgroundColor: "rgba(0, 0, 0, 0.25)",
        },
      }}
    />
  );
};

export default CreateNonMedRecDialog;
