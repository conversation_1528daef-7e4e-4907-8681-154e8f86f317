// @ts-nocheck
import React, { useEffect, useState } from "react";
import { styled } from "@mui/material/styles";
import { Box, Typography, Stack } from "@mui/material";
import { Button as CciButton, CciDialog } from "@cci/mui-components";
import Combobox from "@oe-src/common/Combobox";
import { tabs as NonMedAppTypes, NONMED_DIALOG_TYPES } from "../Constants";

const StyledLabel = styled(Typography)(() => ({
  fontSize: 14,
  fontWeight: 700,
  fontFamily: "Helvetica",
  color: "#11181F",
  minWidth: "150px",
  marginRight: "4px",
  textWrap: "nowrap",
  display: "flex",
  alignItems: "center",
}));

const StyledBox = styled(Box)(() => ({
  display: "flex",
  paddingLeft: "24px",
}));

const FlexedBox = styled(Box)(() => ({
  flex: 1,
}));

const StyledCombobox = styled(Combobox)({
  width: "216px",
  "& .MuiOutlinedInput-root": {
    height: "28px",
  },
});

interface INonMedEnvData {
  srcData: any;
  recName: string;
  actionType: string;
  [key: string]: any;
}

interface INonMedEnvDialog {
  selectedRecordData: INonMedEnvData;
  nonMedTab: string;
  nonMedDialogType: string;
  open: boolean;
  onClose: any;
  rowDispatch?: any;
  gridRef?: any;
  remoteMgr?: any;
  excludedEnvs?: string[];
}

const NonMedEnvDialog = (props: INonMedEnvDialog) => {
  const {
    selectedRecordData,
    nonMedTab,
    nonMedDialogType,
    open,
    onClose,
    rowDispatch,
    remoteMgr,
    gridRef,
    excludedEnvs,
  } = props;

  const [selectedEnv, setSelectedEnv] = useState("");

  const { srcData, recName, actionType } = selectedRecordData;

  const isAdd: boolean = actionType === "add";
  const isRemove: boolean = actionType === "remove";
  const title: string = isAdd
    ? "Add Environment"
    : isRemove
      ? "Remove Environment"
      : "Update Environment";

  // Get env list
  const getNonMedEnvList = () => {
    const envlist = Cci.Env.getEnvListNoDup();
    let tmpEnvList = [...envlist];
    const rowModels = gridRef.current.getRowModels();

    const ccid = srcData.ccid;
    rowModels.forEach(function (rowData, rowKey) {
      if (rowKey.startsWith(ccid)) {
        tmpEnvList = tmpEnvList.filter((item) => item.envname !== rowData.env);
      }
    });

    return tmpEnvList;
  };

  const handleClose = (event, reason) => {
    if (reason && reason === "backdropClick") return;
    onClose();
  };

  const handleOk = () => {
    if (isAdd) handleAddEnv();
    else if (isRemove) handleRemoveEnv();

    onClose();
  };

  const handleAddEnv = () => {
    let newData = { ...srcData };
    newData.env = selectedEnv;
    newData.envpath = newData.ccid + "Default/" + newData.env;
    const isLab = newData?.cat === "Lab" ? true : false;

    const newEnvInfo = {
      isadd: true,
      islab: isLab,
      ccid: newData.ccid,
      env: newData.env,
    };

    // add Env into database
    remoteMgr.updateNonMedRecordEnv(newEnvInfo);

    // add Env into the grid
    if (gridRef && gridRef.current) {
      const rowModels = gridRef.current.getRowModels();

      // find the index for default env
      const rootEnvKey = newData.ccid + "Default";
      const rowKeys = Array.from(rowModels.keys());
      const tmpIndex = rowKeys.indexOf(rootEnvKey);

      // insert into after tmpIndex
      let allRowsArray = Array.from(rowModels.values());
      allRowsArray.splice(tmpIndex, 0, newData);

      rowDispatch({
        type: "SET_ROW_DATA",
        payload: allRowsArray,
      });
    }
    onClose();
  };

  const handleRemoveEnv = () => {
    if (!srcData) {
      return;
    }

    const newEnvInfo = {
      isadd: false,
      islab: srcData?.cat === "Lab" ? true : false,
      ccid: srcData.ccid,
      env: srcData.env,
    };

    // remove the data from server
    remoteMgr.updateNonMedRecordEnv(newEnvInfo);

    // remove data from the grid
    const removeData = [{ ...srcData }];
    let newRows = gridRef.current.getRowModels();
    let tmpRowId;
    let count = 0;
    removeData.forEach((tmpRow) => {
      count = 0;
      tmpRowId = tmpRow.ccid + tmpRow.env;
      if (newRows.has(tmpRowId)) {
        newRows.delete(tmpRowId);
        count++;
      }
    });

    if (count > 0) {
      rowDispatch({
        type: "SET_ROW_DATA",
        payload: Array.from(newRows.values()),
      });
    }
  };

  const handleEnvChange = (env: string) => {
    setSelectedEnv(env.envname);
  };

  const nonMedEnvDialogContent = () => {
    return (
      <Stack sx={{ p: "8px", pt: "32px" }} spacing="16px">
        {isAdd && (
          <Typography sx={{ fontSize: 15 }}>
            To add an <b>Environment</b> to this non-medication record
          </Typography>
        )}
        {isRemove && (
          <Typography sx={{ fontSize: 15 }}>
            You are about to remove the{" "}
            <b>{selectedRecordData.srcData.env} Environment</b> from the
            following non-medication record:
          </Typography>
        )}
        <Box sx={{ mt: 10 }} />
        <StyledBox>
          <StyledLabel>CCID</StyledLabel> {selectedRecordData.srcData.ccid}
        </StyledBox>
        <StyledBox>
          <StyledLabel>Record Name</StyledLabel> {selectedRecordData.recName}
        </StyledBox>
        {isAdd && (
          <Box>
            <Typography sx={{ fontSize: 15 }}>
              Specify the following:
            </Typography>
            <StyledBox>
              <StyledLabel>Environment:</StyledLabel>
              <FlexedBox>
                <StyledCombobox
                  data-testid={"add-non-med-env-combobox-id"}
                  value={Option.envname}
                  options={getNonMedEnvList()}
                  placeholder={"Environment"}
                  onChange={(_, newVal) => {
                    handleEnvChange(newVal);
                  }}
                  getOptionLabel={(option) => option.envname}
                  disableClearable
                />
              </FlexedBox>
            </StyledBox>
          </Box>
        )}
      </Stack>
    );
  };

  const nonMedEnvDialogButtons = () => {
    return (
      <>
        <CciButton sx={{ mr: 1 }} color="primary" onClick={handleClose}>
          Cancel
        </CciButton>
        <CciButton
          color="primary"
          onClick={handleOk}
          disabled={isAdd && !(selectedEnv?.length > 0)}
        >
          OK
        </CciButton>
      </>
    );
  };

  return (
    <CciDialog
      data-testid="non-med-env-dialog-id"
      title={title}
      onClose={handleClose}
      setOpen={handleClose}
      open={open}
      content={nonMedEnvDialogContent()}
      buttons={nonMedEnvDialogButtons()}
      draggable
      sx={{
        "& .MuiDialog-paper": {
          width: "450px",
          boxShadow: "3px",
        },
        "& .MuiModal-backdrop": {
          backgroundColor: "rgba(0, 0, 0, 0.25)",
        },
      }}
    />
  );
};

export default NonMedEnvDialog;
