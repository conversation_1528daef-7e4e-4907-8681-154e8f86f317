import { NONMED_DIALOG_TYPES } from "@cci-monorepo/CAT/NonMedication/Constants";
import BaseBizLogic from "@cci-monorepo/Psat/md/service/BaseBizLogic";

export default class NonMedicationBizLogic extends BaseBizLogic {
  constructor(appType) {
    super({
      appType,
      duplicateDialogType: NONMED_DIALOG_TYPES.DUPLICATENMD,
      recordName: "name",
      tableStructKey: "nonmeds",
    });
  }

  isColSortable(tableStruct, field) {
    return false;
  }

  supportOverrideContext() {
    return false;
  }

  getEditableConfig(_, thisCol) {
    if (
      thisCol === "ccid" ||
      thisCol === "catid" ||
      thisCol === "name" ||
      thisCol === "order_type" ||
      thisCol === "cat" ||
      thisCol === "subcat" ||
      thisCol === "order_display_name" ||
      thisCol === "inactive_date" ||
      thisCol === "Active_Status" ||
      thisCol === "env" ||
      thisCol === "envpath"
    ) {
      return false;
    }
    if (thisCol === this.recordName) {
      return this.isEditable;
    }
    return this.isEditable;
  }

  wantColTooltip(tableStruct, field) {
    if (tableStruct[this.tableStructKey].hasOwnProperty("header")) {
      return tableStruct[this.tableStructKey].header.includes(field);
    } else {
      return tableStruct[this.tableStructKey].includes(field);
    }
  }
}
