import BaseRemote from "@cci-monorepo/Psat/md/service/BaseRemote";
import { getSaveData } from "../hooks/useNonMedMutation";
import { tabs as nonMedicationAppTypes } from "@cci-monorepo/CAT/NonMedication/Constants";

export default class NonMedicationRemote extends BaseRemote {
  static nonmedtypes = {
    [nonMedicationAppTypes.LABS]: "lab",
    [nonMedicationAppTypes.RADIOLOGY]: "radiology",
    [nonMedicationAppTypes.THERAPIES]: "therapy",
    [nonMedicationAppTypes.CONSULTS]: "consult",
    [nonMedicationAppTypes.OTHER]: "other",
  };

  static instances = {};

  static getInstance(tabName) {
    if (!NonMedicationRemote.instances[tabName]) {
      NonMedicationRemote.instances[tabName] = new NonMedicationRemote(tabName);
    }
    return NonMedicationRemote.instances[tabName];
  }

  tabName = "";

  constructor(tabName) {
    super();
    this.hoPath = "cpoe2/nonmed";
    this.tabName = tabName;
  }

  parseColumnDef(col, colTypeDef, rawData) {
    let columnDefs = [];
    let colDefMap = {};
    let colNameMap = {};

    colTypeDef.data?.forEach((colDef) => {
      // get column Display value
      colNameMap[colDef[0]] = rawData?.nonmeds_map?.data.find(
        (item) => item[0] === colDef[0]
      )?.[1];
    });

    for (let i = 0; i < colTypeDef.data?.length; i++) {
      colDefMap[colTypeDef.data?.[i]?.[0]] = JSON.parse(
        colTypeDef.data?.[i]?.[1]
      );
    }

    for (let i = 0; i < col.length; i++) {
      let thisCol = col[i];

      // get column type, "Text" if non defined
      const colType =
        rawData?.nonmeds_map?.data.find((item) => item[0] === thisCol)?.[2] ??
        "Text";
      const colObj = this.ColumnFactory(colType);
      colObj.createColumnDef(
        i,
        thisCol,
        colType,
        colDefMap,
        columnDefs,
        rawData,
        this.appLogic,
        colNameMap
      );
    }
    return { columnDefs, colNameMap };
  }

  colToServerTableName(col, serverTableStructure) {
    if (serverTableStructure.allnonmeds.includes(col)) {
      return "allnonmeds";
    }
    throw new Error("Unknown column: " + col);
  }

  async getColChcDefs() {
    let jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
      hobj: this.hoPath,
      params: {
        nonmedtype: NonMedicationRemote.nonmedtypes[this.tabName],
      },
      noBatch: true,
    });

    const colTypeDef = jsonData.coldefs;

    const nonMeds = jsonData.nonmeds;
    const col = nonMeds.header;

    const saveStructure = {
      allnonmeds: jsonData.nonmeds.header,
    };

    const serverTableStructure = {
      allnonmeds: jsonData.nonmeds.header,
    };

    try {
      const { columnDefs, colNameMap } = this.parseColumnDef(
        col,
        colTypeDef,
        jsonData
      );
      return { columnDefs, saveStructure, serverTableStructure, colNameMap };
    } catch (err) {
      throw new Error(err);
    }
  }

  async getMedData(lookup, agGridParams, state, inactive, topRecs) {
    let params = {
      instock: this.GET_INSTOCK_ONLY,
      inactive: inactive === true ? 1 : 0,
      nonmedtype: NonMedicationRemote.nonmedtypes[this.tabName],
    };
    if (lookup !== undefined && lookup !== null && lookup !== "")
      params.lookup = lookup;

    if (agGridParams) {
      const request = agGridParams.request;
      params.start = request.startRow;
      params.pagesize = request.endRow - request.startRow;
      if (request.sortModel && request.sortModel.length > 0) {
        let sortParam = "";
        request.sortModel.forEach((s) => {
          sortParam += s.field + " COLLATE NOCASE " + s.sort + ",";
        });
        params.sort = sortParam.substring(0, sortParam.length - 1);
      }
      if (request.filterModel) {
        const filter = this.buildFilterParam(
          agGridParams.api,
          request.filterModel,
          state.tableStruct
        );
        if (filter.length > 0) {
          params.filter = filter;
        }
      }
      // If there is topRecs in the requests, backend return # rows - topRec.length.
      // It means there is less real record returned and therefore, subsequence startRow is shifted up
      // when loading 2nd to nth pages because the topRec is always at the top of the 1st page
      //const topRecCnt = agGridParams.parentNode.gridOptionsWrapper.gridOptions.context.topRecCount;
      const topRecCnt = state.topRecCount;
      if (topRecCnt > 0 && params.start !== 0) {
        params.start -= topRecCnt; // This topRecs count should use the HO returned one from previous getMeds call.
        //params.pagesize -= topRecs.length;
      }
    }
    // Only request the top recs for page 0
    if (topRecs && topRecs.length > 0 && params.start === 0) {
      params.topId = topRecs.toString();
    }

    try {
      let jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: this.hoPath,
        params: params,
        noBatch: true,
      });

      const allnonmeds = jsonData.nonmeds;
      const col = allnonmeds.header;

      const colTypeDef = jsonData.coldefs;

      // @todo restore/review pagination logic
      // let toprecscnt = parseInt(jsonData.toprecscnt.data[0][0]);
      let toprecscnt = 0; //isNaN(toprecscnt) ? 0 : toprecscnt;
      const dataTimestamp = 0; //jsonData.datats.data[0][0];

      let { columnDefs } = this.parseColumnDef(col, colTypeDef, jsonData);
      const rowData = allnonmeds.data.map((row) => {
        let r = {};
        r.detailData = {};
        for (let i = 0; i < columnDefs.length; i++) {
          let aCol = columnDefs[i];
          const colType = aCol.customProp.colType;
          const colObj = this.ColumnFactory(colType);
          colObj.buildRowData(row, r, aCol /*, tableData*/);
        }
        return r;
      });
      return {
        columnDefs,
        rowData,
        toprecscnt /*, roundingData*/,
        dataTimestamp,
      };
    } catch (error) {
      console.error(error);
      throw new Error("Error in get allnonmeds");
    }
  }

  async getColumnSetValues(agColApi, col, currentFilter, state, inactive) {
    // Find which server table the col is in
    const colDef = agColApi.getColumn(col);
    const colObj = colDef.colObj;
    const colProps = colDef.customProp.colProps;
    const tableName = this.colToServerTableName(col, state.tableStruct);

    let params = {
      instock: this.GET_INSTOCK_ONLY,
      inactive: inactive === true ? 1 : 0,
      colDistinctValueSubject: [
        {
          tname: tableName,
          colname: col,
        },
      ],
      filter: this.buildFilterParam(agColApi, currentFilter, state.tableStruct),
      nonmedtype: NonMedicationRemote.nonmedtypes[this.tabName],
    };
    if (state.searchText) params.lookup = state.searchText;

    try {
      let jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: this.hoPath,
        params: params,
        noBatch: true,
      });

      if (jsonData && jsonData.distinctvals) {
        let { displayValues, display2RawValuesMap } = colObj.buildColumnFilter(
          jsonData.distinctvals.data,
          colProps,
          colDef.chcObj
        );
        colDef.customProp.display2RawValuesMap = display2RawValuesMap;
        return { displayValues, display2RawValuesMap };
      }
    } catch (err) {
      throw new Error("get column set value error: " + col);
    }
    return {};
  }

  async saveRecords(payload, saveStruct, columnDefs) {
    let finalPayload = this.buildSavePayload(
      payload,
      saveStruct,
      columnDefs
    )?.allnonmeds;

    for (const currItem of finalPayload) {
      const item = { ...currItem };
      let tabtype = "";
      if (item.tabtype) {
        tabtype = item.tabtype;
        delete item.tabtype;
      } else {
        tabtype = item.cat === "Lab" ? "Lab" : "Other";
      }

      const newRecord = {
        tabtype,
      };

      if (item.cat === "Lab") {
        item.ID = item.ccid;
        newRecord.islab = true;
      } else {
        item.orderid = item.ccid;
        newRecord.islab = false;
      }

      newRecord.data = {
        ...item,
      };

      const saveData = getSaveData(newRecord);

      try {
        let jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
          hobj: "cpoe2/nonmed/savedata",
          params: saveData,
        });

        // return { success: true, message: jsonData?.data?.data?.[0]?.[0] };
      } catch (Err) {
        return { success: false, message: Err.message };
      }
    }
    return { success: true, message: "Success" };
  }

  async inactivateRec(payload) {
    if (!payload) return;

    const updateData = {
      islab: payload.cat === "Lab",
      isactive: payload.active,
      ccid: payload.ccid,
    };

    try {
      let jsonData = await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: "cpoe2/nonmed/updatestatus",
        params: updateData,
      });

      return { success: true, message: "Update successfully." };
    } catch (Err) {
      return { success: false, message: Err.message };
    }
  }

  // Post-processing cleanup
  // RxInfo entries should have unique RxID; remove duplicate rxinfo rows due to possible multiple contexts per rxid
  preSaveTransform(final) {
    // let cleanRxInfo = [];
    // final.rxinfo.forEach((e) => {
    //   if (cleanRxInfo.find((x) => x.RxID === e.RxID)) {
    //     return;
    //   } else {
    //     cleanRxInfo.push(e);
    //   }
    // });
    // final.rxinfo = cleanRxInfo;
    // delete final.custommeds;
    return final;
  }

  // @todo create logic
  async createNewRecord(payload, saveStruct, columnDefs) {
    // let localSaveStruct = cloneDeep(saveStruct);
    // const newPayload = this.setDefaultValues(payload);
    // // Since Single Med does not have the knowledge of what columns is in custom med table,
    // // we can only hardcode.
    // localSaveStruct.custommeds = [
    //   "RxID",
    //   "CustomMed_Name",
    //   "Strength",
    //   "Form",
    //   "Route",
    //   "Formulary",
    // ];
    // return CustomMedRemote.getInstance().createNewRecord(
    //   newPayload,
    //   localSaveStruct,
    //   columnDefs
    // );
  }

  // Add/Remove one record from Non-Med data based on Env
  async updateNonMedRecordEnv(record) {
    if (!record) return;
    const updateData = {
      isadd: record.isadd,
      islab: record.islab,
      ccid: record.ccid,
      env: record.env,
    };
    try {
      await Cci.util.Hobj.requestUnorderedUnfilteredRecords({
        hobj: "cpoe2/nonmed/updateenv",
        params: updateData,
      });

      return { success: true, message: "Remove successfully." };
    } catch (Err) {
      return { success: false, message: Err.message };
    }
  }
}
