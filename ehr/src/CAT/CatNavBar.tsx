import React from "react";
import {
  AppBar,
  Box,
  Toolbar,
  Typography,
  TypographyProps,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import MenuIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Hamburger_Menu.svg";
import CliniCompLogo from "@cci-monorepo/oe-shared/src/assets/Logo_CliniComp_CC.svg";
import MinimizeIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Minimize.svg";
import MaximizeIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Maximize.svg";
import CloseIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Close_Application.svg";

type TActiveCatTitle = TypographyProps & {
  isActive?: boolean;
};

const StyledCatTitle: React.FC<TActiveCatTitle> = styled(Typography)(() => ({
  padding: 0,
  marginLeft: 70,
  textTransform: "none",
  color: "#606060",
  fontSize: "16px",
  fontWeight: "bold",
  background: "transparent",
  cursor: "pointer",
  "&:hover": {
    color: "#3C6CBB",
    background: "transparent",
  },
  "&:focus": {
    border: "none",
    boxShadow: "none",
  },
}));

export const ActiveCatTitle: React.FC<TActiveCatTitle> = styled(
  StyledCatTitle,
  {
    shouldForwardProp: (prop) => prop !== "isActive",
  }
)((props) => ({
  ...(props.isActive && {
    color: "#3C6CBB",
    borderBottom: "3px solid #3C6CBB",
    borderRadius: 0,
    "&:focus": {
      borderBottom: "3px solid #3C6CBB",
    },
  }),
}));

const ImageBox = styled(Box)(() => ({
  width: "25px",
  height: "28px",
}));

type TCatNavBarProps = {
  title?: string;
  subHeader?: string;
  modules: string[];
  chilren?: React.ReactNode;
  moduleValue: number;
  handleModuleChange: (index: number) => void;
};

export const CatNavBar = ({
  title = "Clinical Admin Tools",
  subHeader = "ORDER ENTRY",
  modules,
  chilren = null,
  moduleValue,
  handleModuleChange,
}: TCatNavBarProps) => {
  return (
    <AppBar
      data-testid="clinical-admin-tools-nav-bar-id"
      position="static"
      sx={{ background: "#EDEDED" }}
    >
      <Box
        alignContent={"center"}
        sx={{
          display: "flex",
          justifyContent: "space-between",
          flexDirection: "row",
          height: "46px",
          background: "#3C4B50",
        }}
      >
        <Box>
          <Typography sx={{ font: "normal 700 20px Roboto", p: "10px" }}>
            {title}
          </Typography>
        </Box>
        <Box>
          <Box
            component="img"
            src={CliniCompLogo}
            alt="clini-comp-logo"
            sx={{ cursor: "pointer" }}
          />
        </Box>
        <Box
          gap="20px"
          sx={{ display: "flex", flexDirection: "row", p: "10px" }}
        >
          <Box>
            <ImageBox
              component="img"
              // @ts-ignore
              src={MinimizeIcon}
              alt="minimize-icon"
              sx={{ paddingTop: "12px" }}
            />
          </Box>
          <Box>
            {/*// @ts-ignore*/}
            <ImageBox component="img" src={MaximizeIcon} alt="maximize-icon" />
          </Box>
          <Box>
            {/*// @ts-ignore*/}
            <ImageBox component="img" src={CloseIcon} alt="close-icon" />
          </Box>
        </Box>
      </Box>
      <Toolbar sx={{ height: "46px" }}>
        <Box
          component="img"
          src={MenuIcon}
          alt="menu-icon"
          sx={{
            width: "22px",
            height: "28px",
            cursor: "pointer",
            paddingBottom: "3px",
          }}
        />
        <Box
          justifyContent="center"
          justifyItems={"center"}
          sx={{ display: "flex", flexDirection: "row" }}
        >
          <Typography
            sx={{ color: "#000000", fontSize: 16, fontWeight: 700, pl: "15px" }}
          >
            {subHeader}
          </Typography>
          {modules?.map((module, index) => {
            return (
              <ActiveCatTitle
                key={index}
                isActive={moduleValue === index}
                onClick={() => handleModuleChange(index)}
              >
                {module}
              </ActiveCatTitle>
            );
          })}
          {chilren}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default CatNavBar;
