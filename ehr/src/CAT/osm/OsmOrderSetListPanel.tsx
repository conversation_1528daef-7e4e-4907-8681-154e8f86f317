// @ts-nocheck
import React, { useState } from "react";
import { v4 as uuid } from "uuid";
import { cloneDeep } from "lodash";
import OsmSearchToolbar from "./components/OsmSearchToolbar";
import OsmDataGrid from "./components/OsmDataGrid";
import {
  getOsmDataGridColumns,
  parseOrderGroupsData,
  parseOrderSetsData,
  parseUserSetsData,
} from "./util/OsmDataGridHelper";
import { parseVersionData } from "./util/OsmVersionDataHelper";
import {
  GRID_TREE_DATA_GROUPING_FIELD,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import {
  useOrderGroupListQuery,
  useOrderSetListQuery,
  useUserSetListQuery,
} from "./hooks/useOrderSetQuery";
import { useOsmContext } from "./contexts/OsmContext";
import {
  OsmAction,
  OsmToolType,
  OsmType,
  OrderSetStatus,
} from "./OsmConstants";
import { useGroupVersionQuery } from "./hooks/useOrderSetQuery";
import { OsmWarningDialog } from "./components/OsmWarningDialog";

import {
  isEditPermission,
  isOSMAdminPermission,
} from "@cci-monorepo/CAT/common/Permissions";
import { TimelineDot, timelineDotClasses } from "@mui/lab";
import { useOsmPageContext } from "./contexts/OsmPageContext";

type TFilter = {
  [key: string]: any;
};

type TOsmOrderSetListPanelProps = {
  searchType: OsmType;
};

/**
 * The component for searching Order Sets or Order Set Groups
 * @params {Object} searchType The type of search, OsmSearchSet or OsmSearchGroup
 */
const OsmOrderSetListPanel = ({ searchType }: TOsmOrderSetListPanelProps) => {
  const { osmState, osmDispatch, allDataRef } = useOsmContext();
  const [open, setOpen] = useState(false);
  const [preVersionData, setPreVersionData] = useState({});
  const [latestVersionData, setLatestVersionData] = useState({});
  const { updateTabPage } = useOsmPageContext();

  const [filter, setFilter] = useState<TFilter>({});
  const apiRef = useGridApiRef();
  const dataType =
    searchType === OsmType.OsmSearchGroup ? OsmToolType.Group : OsmToolType.Set;
  const dataTestId =
    dataType === "group"
      ? "osm-tool-search-orderset-group-id"
      : "osm-tool-search-orderset-set-id";

  const handleEditRow = (row: any, addVersion) => {
    osmDispatch({
      type:
        dataType === OsmToolType.Set
          ? OsmAction.EditExistSet
          : OsmAction.EditExistGroup,
      name:
        dataType === OsmToolType.Set
          ? row.orderSetName
          : addVersion
            ? row.name + " (v" + row.version + ")"
            : row.name,
      data: row,
    });
  };

  const HandleCancel = () => {
    setOpen(false);
  };

  const HandlePreviousVersion = () => {
    let tid = "osm-group-" + preVersionData.id + "-id";
    updateTabPage({ data: preVersionData }, tid);
    handleEditRow(preVersionData, true);
  };

  const HandleLatestVersion = () => {
    handleEditRow(latestVersionData);
  };

  const handleDoubleClick = (row: any, event: any) => {
    if (!event.ctrlKey) {
      event.defaultMuiPrevented = true;
    }
    if (dataType === OsmToolType.Set) {
      handleEditRow(row);
    } else {
      let node = apiRef.current.getRowNode(row.id);
      if (node.type === "leaf") {
        handleEditRow(row);
      } else {
        let cid = node?.children[0];
        let cRow = apiRef.current.getRow(cid);
        setLatestVersionData(cRow);
        setPreVersionData(row);
        setOpen(true);
      }
    }
  };

  const filterWithSearchStr = {
    ...filter,
    searchStr: osmState.searchStr.length >= 3 ? osmState.searchStr : "",
  };

  const columns = getOsmDataGridColumns(
    dataType,
    handleEditRow,
    osmState.searchStr,
    isEditPermission
  );

  const [orderSetRows, setsData] =
    useOrderSetListQuery({}, parseOrderSetsData, filterWithSearchStr)?.data ??
    [];

  const userSetsData = useUserSetListQuery(
    { showsetversion: true },
    parseUserSetsData,
    filterWithSearchStr
  );

  const [orderGroupRows, groupsData, ordersData] =
    useOrderGroupListQuery({}, parseOrderGroupsData, filterWithSearchStr)
      .data ?? [];

  const versionsList =
    useGroupVersionQuery({}, parseVersionData, null).data ?? [];

  if (setsData) {
    allDataRef.current["sets"] = setsData;
  }

  if (userSetsData && userSetsData?.data) {
    allDataRef.current["userSets"] = userSetsData.data;
  }

  if (groupsData && ordersData) {
    allDataRef.current["groups"] = groupsData;
    allDataRef.current["orders"] = ordersData;
  }

  let rows =
    dataType === OsmToolType.Set
      ? orderSetRows
        ? orderSetRows
        : []
      : orderGroupRows
        ? orderGroupRows
        : [];

  // Merge Master Sets to show the tree
  if (isOSMAdminPermission) {
    // Add unique ID as User Set ID can be same as Master Set ID
    // @ts-ignore
    const userSetsWithUniqueID = userSetsData?.data?.map((userSet) => {
      return { ...userSet, userSetId: userSet.id, id: uuid() };
    });
    if (dataType === OsmToolType.Set) {
      rows = rows.map((row: any) => {
        let sets = [{ ...row, path: [row.id] }];
        const userSets = userSetsWithUniqueID.filter(
          (userSet: any) => userSet.masterSetId === row.id
        );
        userSets?.forEach((userSet: any) => {
          sets.push({ ...userSet, path: [row.id, userSet.id] });
        });
        return sets;
      });

      rows = rows.flat(1);
    } else {
      rows = rows.map((row: any) => {
        var i = 0;
        const dataId = row.id;
        const [major, minor] = row.version.split(".");
        //let prow, crow;
        let sets = [];
        if (row.status === OrderSetStatus.Draft && major >= 1) {
          for (i = 0; i < versionsList.length; i++) {
            let ver = versionsList[i];
            const [verMajor, verMinor] = ver.version.split(".");
            if (
              ver.id === row.id &&
              ver.version !== row.version &&
              parseInt(verMajor) === parseInt(major) - 1
            ) {
              if (ver.status === OrderSetStatus.Active) {
                let prow: any = { ...ver, group_id: ver.id, path: [ver.id] };
                let crow: any = {
                  ...row,
                  id: row.id + row.version,
                  orderGroupName: row.orderGroupName + " (Latest Version)",
                  group_id: row.id,
                  path: [ver.id, row.version],
                };
                crow.preActiveVersion = prow;
                prow.latestVersion = crow;
                sets.push(prow);
                sets.push(crow);
                break;
              }
            }
          }
          if (i === versionsList.length)
            sets.push({ ...row, group_id: row.id, path: [row.id] });
        } else {
          sets.push({ ...row, group_id: row.id, path: [row.id] });
        }
        return sets;
      });
      rows = rows.flat(1);
    }
  }

  const groupingColDef = {
    headerName: "",
    valueGetter() {
      return "";
    },
    hideDescendantCount: true,
  };

  return (
    <>
      <OsmSearchToolbar
        searchType={searchType}
        setFilter={setFilter}
        autoFocus
      />
      <OsmDataGrid
        apiRef={apiRef}
        data-testid={dataTestId}
        columnVisibilityModel={
          dataType === OsmToolType.Set
            ? {
                data: false,
                [GRID_TREE_DATA_GROUPING_FIELD]: false,
                updatedById: false,
                orderGroupName: false,
              }
            : {
                data: false,
                [GRID_TREE_DATA_GROUPING_FIELD]: false,
                updatedById: false,
                orderSetName: false,
              }
        }
        rows={rows}
        showCellVerticalBorder={true}
        hideFooter={true}
        columnHeaderHeight={30}
        columns={columns}
        pageSize={50}
        rowsPerPageOptions={[50]}
        onCellDoubleClick={(params: any, event: any) =>
          handleDoubleClick(params.row, event)
        }
        // groups data section
        treeData={isOSMAdminPermission}
        rowGroupingColumnMode={"single"}
        getTreeDataPath={(row: any) => row.path}
        groupingColDef={groupingColDef}
        defaultGroupingExpansionDepth={0}
        sx={{
          ".MuiDataGrid-columnHeaders": {
            borderRadius: 0,
          },
          ".MuiDataGrid-columnHeaderTitle": {
            font: "normal 700 14px Roboto",
          },
          ".MuiTypography-root": {
            fontSize: "15px",
          },
          "& .MuiDataGrid-treeDataGroupingCellToggle": {
            height: "30px",
            width: "30px",
          },
          borderRadius: 0,
          marginLeft: 3,
          marginRight: 3,
          marginBottom: 3,
          height: "75vh",
        }}
      />
      {open && (
        <OsmWarningDialog
          open={open}
          name={preVersionData.name}
          onClose={HandleCancel}
          onOpenPreviousVersion={HandlePreviousVersion}
          onOpenLatestVersion={HandleLatestVersion}
        ></OsmWarningDialog>
      )}
    </>
  );
};

export default OsmOrderSetListPanel;
