import PropTypes from "prop-types";

export type TOsmDataType = {
  environments?: {
    [key: string]: any;
  };
  groupIds?: {
    group_id: string;
    pos: string;
    selected: string;
    selections: object;
    [key: string]: any;
  }[];
  id: string;
  type: string;
  instruction: string;
  location: string;
  name?: string;
  orderSetName: string;
  retireComment: string;
  retireReason: string;
  searchTerms: string;
  status: string;
  synonyms: string;
  updateDate: string;
  updatedBy: string;
  updatedById: string;
  version: string;
  versionNote: string;
  weblinks: {
    display: string;
    url: string;
  }[];
  [key: string]: any;
};

export const OsmDataType = PropTypes.shape({
  environments: PropTypes.shape({
    dod: PropTypes.arrayOf(
      PropTypes.shape({
        display: PropTypes.string,
        name: PropTypes.string,
      })
    ),
  }),
  groupIds: PropTypes.arrayOf(
    PropTypes.shape({
      group_id: PropTypes.string,
      pos: PropTypes.string,
      selected: PropTypes.string,
      selections: PropTypes.object,
    })
  ),
  id: PropTypes.string,
  instruction: PropTypes.string,
  location: PropTypes.string,
  name: PropTypes.string,
  orderSetName: PropTypes.string,
  retireComment: PropTypes.string,
  retireReason: PropTypes.string,
  searchTerms: PropTypes.string,
  status: PropTypes.string,
  synonyms: PropTypes.string,
  updateDate: PropTypes.string,
  updatedBy: PropTypes.string,
  updatedById: PropTypes.string,
  version: PropTypes.string,
  versionNote: PropTypes.string,
  weblinks: PropTypes.arrayOf(
    PropTypes.shape({
      display: PropTypes.string,
      url: PropTypes.string,
    })
  ),
});
