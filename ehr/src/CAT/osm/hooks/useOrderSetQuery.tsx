/**
 * Custom hook to get/set OrderSet(s)/Group(s) data through react-query
 */
import { useOsmQuery } from "./useOsmQuery";

/**
 * Get existing OrderSet list from master_sets table in cpoe2/ordersets/ordersets.db
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} [extra], optional, the optional parameters for dataParseFn
 */
export const useOrderSetListQuery = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const hobj = "cpoe2/ordersets/getList";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get existing UserOrderSet list from user_sets table in cpoe2/ordersets/ordersets.db
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} extra, optional, the optional parameters for dataParseFn
 */
export const useUserSetListQuery = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const hobj = "cpoe2/ordersets/getUserSetsList";
  const queryKey = {
    hobj: hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get existing Order Group list from groups table in cpoe2/ordersets/ordersets.db
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} [extra], optional, the optional parameters for dataParseFn
 */
export const useOrderGroupListQuery = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const hobj = "cpoe2/ordersets/getGroupList";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get all versions of given Order Set/Group list from cpoe2/ordersets/ordersets.db
 * @param {Object} options the paramaters passed to server
 *  - dateType: set/group
 *  - dataId: set_id/group_id
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} [extra], optional, the optional parameters for dataParseFn
 */
export const useVersionQuery = (options: any, dataParseFn: any, extra: any) => {
  const hobj = "cpoe2/ordersets/getVersions";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get orders belonging to a specific Order Set from cpoe2/ordersets/ordersets.db
 * @param {Object} options the parameters passed to server
 *  - order_set_type: Type of order set (master/user)
 *  - order_set_id: ID of the order set
 * @param {Function} dataParseFn optional, the function to parse data
 * @param {any} [extra] optional, the optional parameters for dataParseFn
 */
export const useOrderSetOrdersQuery = (
  options: { order_set_type: string; order_set_id: string },
  dataParseFn?: any,
  extra?: any
) => {
  const hobj = "cpoe2/ordersets/getOrdersOfOrderSet";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

export const useGroupVersionQuery = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const hobj = "cpoe2/ordersets/getGroupVersions";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};
