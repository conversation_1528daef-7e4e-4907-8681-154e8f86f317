import { useOsmQuery } from "./useOsmQuery";

/**
 * Check Order Set/Group name for duplications
 * @param {Object} options required parameters for useQuery
 * @param {any} [extra] optional, the optional parameters for dataParseFn
 */
export const useCheckNameQuery = (options: any, extra: any = null) => {
  const hobj = "cpoe2/ordersets/checkName";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  const parseCheckName = (rawData: any) => {
    return rawData?.data?.data?.[0]?.[0] ? rawData.data.data[0][0] : 0;
  };
  return useOsmQuery(queryKey, parseCheckName, extra);
};

/**
 * Get configured environments for Order Set
 * @param {Object} options required parameters for useQuery
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} [extra], optional, the optional parameters for dataParseFn
 */
export const useGetEnvsQuery = (
  options: any,
  dataParseFn: any,
  extra: any = null
) => {
  const hobj = "cpoe2/ordersets/getEnvs";
  const queryKey = {
    hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};
