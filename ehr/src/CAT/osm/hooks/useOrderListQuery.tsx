/**
 * Custom hook to get/set Order data through react-query
 */
import { useOsmQuery } from "./useOsmQuery";

// @ts-ignore

/**
 * Get Order Category/Sub-category list
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} extra, optional, the optional parameters for dataParseFn
 */
export const useCategoryQuery = (
  options: any,
  dataParseFn: any,
  extra = null
) => {
  const hobj = "cpoe2/browseCategories";
  const queryKey = {
    hobj: hobj,
    params: options,
    extra,
  };

  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get data from cpoe2/searchChoices
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} extra, optional, the optional parameters for dataParseFn
 */
export const useSearchChoices = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const hobj = "cpoe2/searchChoices";
  const queryKey = {
    hobj: hobj,
    params: options,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get list of user's Orders from cpoe2/myorders/myorders.db
 * @param {Object} options the paramaters passed to server
 * @param {Object} dataParseFn optional, the function to parse data
 */
export const useMyOrderListQuery = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const hobj = "cpoe2/myorders/getData";
  let params = options ? options : {};
  params["staffid"] = Cci.util.Staff.getSid();
  if (!params["lookup"]) {
    params["lookup"] = "";
  }

  const queryKey = {
    hobj: hobj,
    params: params,
    extra,
  };
  return useOsmQuery(queryKey, dataParseFn, extra);
};

/**
 * Get list Orders for given Order category/sub-category for Order Set Management Tool.
 * Add default values for required paramenters if needed.
 */
export const useOrderListByCategory = (
  options: any,
  dataParseFn: any,
  extra: any
) => {
  const cat = !options.cat
    ? ""
    : options.cat === "ALL_ORDERS"
      ? "All Orders"
      : options.cat;
  const isAllOrders = options?.cat === "ALL_ORDERS" && !extra?.searchstr;
  const params = {
    ...options,
    unit: "ALL_UNITS",
    hospWide: isAllOrders ? 0 : 1,
    cat: extra?.searchstr ? "" : cat,
    browseChc: " ",
    lookup: extra?.searchstr,
  };
  const callFunc = isAllOrders ? useMyOrderListQuery : useSearchChoices;

  return callFunc(params, dataParseFn, extra);
};
