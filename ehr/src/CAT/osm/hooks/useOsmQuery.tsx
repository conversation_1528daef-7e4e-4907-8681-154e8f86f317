/**
 * Custom hook to get data through react-query
 */
import { useQuery } from "@tanstack/react-query";

// @ts-ignore

/**
 * Call useQuery (v3) to get data
 * @param {Object} queryKey required paramaters for useQuery
 * @param {Object} dataParseFn optional, the function to parse data
 * @param {any} [extra], optional, the optional parameters for dataParseFn
 */
export const useOsmQuery = (queryKey: any, dataParseFn: any, extra: any) => {
  return useQuery({
    queryKey: [queryKey],

    queryFn: async ({ queryKey: [hobjRequest] }) => {
      const records =
        await Cci.util.Hobj.requestUnorderedUnfilteredRecords(hobjRequest);
      return dataParseFn ? dataParseFn(records, extra) : records;
    },

    placeholderData: [],
    enabled: queryKey.params?.isValid !== false,
  });
};
