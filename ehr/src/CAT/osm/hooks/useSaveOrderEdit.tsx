import { useContext } from "react";
import { OEContext } from "@oe-src/context/OEContext";
import { getSummaryText } from "@oe-entry/util/SummaryTextBuilder";
import { prepareSubmitPayload } from "@oe-entry/components/NewOrdersPanel/useSave";

function useSaveOrderEdit({
  orderSetType,
  orderSetId,
  getEditedOrders,
  handleApplyClose,
  updateOrders,
}: any) {
  // @ts-ignore
  const { masterCfgData, dosingUnitMapping } = useContext(OEContext);

  const handleSave = () => {
    let newOrders;
    if (getEditedOrders) {
      newOrders = getEditedOrders();
      if ((newOrders ?? []).length < 1) return;
    }

    let orderPayloads: any[] = [];
    newOrders.forEach((orderToSave: any) => {
      if (orderToSave.order_id) {
        let orderIdPayload = orderToSave.order_id;
        let orderPayload: any = prepareSubmitPayload(
          orderToSave,
          masterCfgData
        );

        if (orderPayload["_action"]) {
          delete orderPayload["_action"];
        }

        orderPayload["summary"] = getSummaryText(
          orderToSave,
          dosingUnitMapping
        );
        //Some fields that were missing on payload prepare that are still in DB:
        if (
          orderPayload.hasOwnProperty("order_id") &&
          !orderPayload.hasOwnProperty("unique_id")
        ) {
          orderPayload["unique_id"] = orderPayload["order_id"];
        }

        if (
          orderPayload.hasOwnProperty("subtype") &&
          !orderPayload.hasOwnProperty("order_type")
        ) {
          orderPayload["order_type"] = orderPayload["subtype"];
        }

        orderPayloads.push({
          order_data: JSON.stringify(orderPayload),
          order_id: orderIdPayload,
        });
      }
    });

    if (orderPayloads.length === 0) return;

    const params = {
      orders: JSON.stringify(orderPayloads),
      order_set_type: orderSetType,
      order_set_id: orderSetId,
      sid: Cci.util.Staff.getSid(),
      env: Cci.util.Env.getEnvName(),
    };

    Cci.util.Hobj.requestUnorderedUnfilteredRecords({
      hobj: "cpoe2/ordersets/saveMultiOrders",
      noBatch: true,
      dbs: [Cci.util.Patient.getDbpath()],
      params: params,
    }).then(
      function (response: any) {
        if (response) {
          if (updateOrders) {
            updateOrders(convertArrayToObject(response.TempTable));
          }
          handleApplyClose();
        }
      },
      function (error: any) {
        console.log(error);
      }
    );
  };
  return {
    handleSave,
  };
}

const convertArrayToObject = (dataDefs: any) => {
  let rows: any = [];

  if (
    dataDefs &&
    dataDefs.header &&
    dataDefs.data &&
    dataDefs.data.length > 0
  ) {
    const headers = dataDefs.header;
    const data = dataDefs.data;

    // Pair up key:value (data[2].Result[0] stores keys, and the rest stores data)
    const dataKeyValue = data.map((cfgObj: any, index: number) =>
      headers.map((key: string, idx: number) => ({
        [key]: cfgObj[idx],
      }))
    );
    // Convert each row array into an object
    rows = dataKeyValue.map((rowArray: Array<any>, idx: number) =>
      rowArray.reduce((r: string, c: string) => Object.assign(r, c), {})
    );
  }
  return rows;
};

export default useSaveOrderEdit;
