import { useMutation } from "@tanstack/react-query";

// @ts-ignore

// OsmMutation Preliminary data type.
type TDataOsm = {
  set: {
    id?: string;
  };
  group: {
    group_id?: string;
  };
};

type TOsmMutation = {
  error: {
    message: string;
  };
  isError: boolean;
  isSuccess: boolean;
  [key: string]: any;
};

/**
 * Wrapper for react-query useMutation to save Set/Group to the database.
 * @param {string} dataType required, for this hook, the type of data, 'set', or 'group'
 * @param {object} data required, for mutationFn the real data
 * @returns `MutationResult` The function and variables associated with saving Set/Group data.
 */
export const useOsmSaveMutation = () => {
  const getHobj = (isNew: any, dataType: any) => {
    let hobj = "";
    if (dataType === "set") {
      hobj = isNew ? "cpoe2/ordersets/newSet" : "cpoe2/ordersets/updateSet";
    } else {
      hobj = isNew ? "cpoe2/ordersets/newGroup" : "cpoe2/ordersets/updateGroup";
    }
    return hobj;
  };

  return useMutation({
    mutationFn: (data: TDataOsm) => {
      const hobjRequest = {
        hobj: getHobj(
          data.set?.id === "-1" || data.group?.group_id === "-1",
          data["set"] && "set"
        ),
        params: data,
      };
      return Cci.util.Hobj.requestUnorderedUnfilteredRecords(hobjRequest);
    },
  }) as TOsmMutation;
};

export const useOsmUpdateDataMutation = () => {
  const hobj = "cpoe2/ordersets/updateData";
  return useMutation({
    mutationFn: (data) => {
      const hobjRequest = {
        hobj: hobj,
        params: data,
      };
      return Cci.util.Hobj.requestUnorderedUnfilteredRecords(hobjRequest);
    },
  }) as TOsmMutation;
};
