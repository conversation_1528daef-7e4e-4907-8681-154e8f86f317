import React, { useEffect, useRef, useState } from "react";
import { Box } from "@mui/material";
import { useSet<PERSON>tom, useAtomValue } from "jotai";
import OsmOrderSetToolbar from "./components/OsmOrderSetToolbar";
import OsmOrderSetNamePanel from "./components/OsmOrderSetNamePanel";
import OsmOrderSetDetailPanel from "./components/OsmOrderSetDetailPanel";
import OsmSettingPanel from "./components/OsmSettingPanel";
import {
  OrderSetStatus,
  OsmAction,
  OsmToolType,
  OsmType,
} from "./OsmConstants";
import { useOsmContext } from "./contexts/OsmContext";
import { useOsmPageContext } from "./contexts/OsmPageContext";
import {
  getNextVersion,
  getPageData,
  getGroupSavedData,
} from "./util/OsmSaveDataHelper";
import CatDialog from "../common/CatDialog";
import BrowseOrderGroupsPanel from "./components/BrowseOrderGroupsPanel";
import BrowseOrdersPanel from "./components/BrowseOrdersPanel";
import RetireInfoPanel from "./components/RetireInfoPanel";
import PublishInfoPanel from "./components/PublishInfoPanel";
import ConvertToDraftInfoPanel from "./components/ConvertToDraftInfoPanel";
import VersioningPanel from "./components/VersioningPanel";
import {
  DialogVariants,
  useAlert,
} from "@cci-monorepo/Psat/common/layouts/AdminLayoutA/context/AlertContext";
import {
  CatSuccessNotification,
  InitCatNotification,
  TCatNotification,
} from "../common/CatNotification";
import {
  useOsmSaveMutation,
  useOsmUpdateDataMutation,
} from "./hooks/useOsmMutation";
import { TOsmDataType } from "./OsmPropTypes";
import AffectedOrderSetGroupPanel from "./components/AffectedOrderSetGroupPanel";
import { printOsm } from "./util/printOsm";
import { exportToPdf } from "./util/exportOsm";
import { isEditPermission } from "@cci-monorepo/CAT/common/Permissions";
import { useOrderSetOrdersQuery } from "./hooks/useOrderSetQuery";
import { parseOrderData } from "./util/OsmDataGridHelper";
import {
  osmUndoArrayAtom,
  osmCurrentIndexAtom,
  setClearCurrentUndoAtom,
  osmUpdateAtom,
  osmSetUndoAtom,
} from "./contexts/OsmAtom";

type TOsmOrderSetPanel = {
  dataType: OsmType;
  data: TOsmDataType;
  status: OrderSetStatus;
  tabid: string;
  name?: string;
  isNew?: boolean;
  // could be removed
  closable?: boolean;
  label?: string;
};

export type UpdateDataType = {
  dataType?: OsmToolType;
  status: OrderSetStatus;
  name?: string;
  version?: any;
  id?: string;
  dataId?: string;
  retireReason?: string;
  retireComment?: string;
  publishNotify?: string;
  [key: string]: any;
};
export enum OsmDialogAction {
  retire = "retire",
  convert = "convert",
  publish = "publish",
}
/**
 * Hold one Order Set/Group, either New or Existing Set
 *  - dataType: OsmType
 *  - data?: set/group data, default is undefined
 */
const OsmOrderSetPanel: React.FC<TOsmOrderSetPanel> = (props) => {
  const { dataType, data, status, isNew, tabid } = props;
  const [browseOpen, setBrowseOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const { osmState, osmDispatch, allDataRef } = useOsmContext();
  const [editError, setEditError] = useState(false);
  const [versionHistory, setVersionHistory] = useState(false);
  const [toastState, setToastState] = useState(InitCatNotification);
  const undoSaved = useAtomValue(osmUndoArrayAtom);
  const currentIndex = useAtomValue(osmCurrentIndexAtom);
  const setClearUndo = useSetAtom(setClearCurrentUndoAtom);
  const osmNeedUpdate = useAtomValue(osmUpdateAtom);
  const setOmsUndo = useSetAtom(osmSetUndoAtom);

  const {
    removeCurrentPage,
    updateCurrentPage,
    updatePageStatus,
    updateTabPage,
  } = useOsmPageContext();
  const [, setPrintError] = useState(null);

  const createAlert = useAlert();

  const nameInputRef = useRef<HTMLInputElement>(null);

  const typeName =
    dataType === OsmType.OsmExistSet || dataType === OsmType.OsmNewSet
      ? OsmToolType.Set
      : OsmToolType.Group;

  const dialogSx =
    osmState.action === OsmAction.BrowseOrders ||
    osmState.action === OsmAction.OrderGroups
      ? {
          "& .MuiDialogContent-root": {
            marginTop: "0px",
            padding: "0px",
          },
          "& .MuiDialog-paper": {
            background: "#F2F2F2",
          },
        }
      : {};

  const getPopupTitle = (action: OsmAction) => {
    let title = "";
    switch (action) {
      case OsmAction.BrowseOrders:
        title = "Browse Orders";
        break;
      case OsmAction.OrderGroups:
        title = "Browse Order Groups";
        break;
      case OsmAction.RetireSet:
      case OsmAction.RetireGroup:
        title = "Retire";
        break;
      case OsmAction.ConvertToDraft:
        title = "Convert to Draft";
        break;
      case OsmAction.OpenAffectedOrderGroupSetDialog:
        title = "Affected Groups and Sets";
        break;
      case OsmAction.PublishSet:
      case OsmAction.PublishGroup:
        title = "End User Notifications";
        break;
      default:
        break;
    }
    return title;
  };

  const handleCloseToast = () => {
    setToastState(InitCatNotification);
  };

  /**
   * Handle show popup window.
   *  - Orders Browsing Window
   *  - Groups Browsing Window
   *  - Retire information Window
   *  - Convert-to-Draft confirm Window
   */
  const handleOpenPopup = (actionType: any, target: any) => {
    switch (actionType) {
      case OsmAction.Unknown:
        setBrowseOpen(false);
        setAnchorEl(null);
        break;
      case OsmAction.BrowseOrders:
      case OsmAction.OrderGroups:
      case OsmAction.OpenAffectedOrderGroupSetDialog:
        target && setAnchorEl(target);
        setBrowseOpen(true);
        break;
      case OsmAction.PublishGroup:
        if (data.updateType === "New Revision") {
          osmDispatch({
            type: OsmAction.DoPublish,
            data: data,
          });
          setBrowseOpen(false);
          setAnchorEl(null);

          let newData = { ...data };
          newData["notification"] = osmState.data?.notification;
          newData.status = OrderSetStatus.Published;
          newData.updateType = "Published";
          // @ts-ignore
          handleUpdateData(newData, `Order Group published`);
        } else {
          setBrowseOpen(true);
        }
        break;
      default:
        setBrowseOpen(true);
    }
  };

  const handleBrowseClose = () => {
    setBrowseOpen(false);
    setAnchorEl(null);
  };

  // save success and close the tab
  const handleSaveSuccess = () => {
    let data: any = {};
    if (typeName === OsmToolType.Group) {
      data = getGroupSavedData(saveDataMutate);
    }
    saveDataMutate.reset();
    if (tabid) {
      setClearUndo(tabid);
      updatePageStatus(tabid, { dirty: false, isNew: false });
    }
    if (osmState.action === OsmAction.SaveAndClose) {
      createAlert({ variant: DialogVariants.INFO, msg: "Save success!" });
      removeCurrentPage();
    } else if (osmState.action === OsmAction.Save) {
      setOmsUndo(tabid, data);
      const catNotify = {
        open: true,
        msg:
          typeName === OsmToolType.Set
            ? "Order Set Saved"
            : "Order Group Saved",
      } as TCatNotification;

      setToastState(catNotify);
      if (data && Object.keys(data).length > 0) {
        osmDispatch({
          type: OsmAction.UpdateSave,
          data: data,
        });
      }
    }
  };

  // save error, keep the tab open
  const handleSaveError = () => {
    createAlert({
      variant: DialogVariants.INFO,
      msg: saveDataMutate.error.message,
    });
  };

  const saveDataMutate = useOsmSaveMutation();
  const updateDataMutation = useOsmUpdateDataMutation();

  const handleSave = () => {
    let tmpData = { ...data };
    if (!tmpData.name && data.name) {
      tmpData.name = data.name;
    }

    const saveData = getPageData(tmpData, typeName, allDataRef, isNew);

    saveDataMutate.mutate(saveData);
  };

  /**
   * Handle update data
   * @param newData {any} new data
   * @param toastMsg {string | null} optional, toast message
   */
  const handleUpdateData = (
    newData: UpdateDataType,
    toastMsg: null | string = null,
    hasNewActive?: boolean
  ) => {
    let updateData: UpdateDataType = {
      dataId: newData.preActiveVersion ? newData.group_id : newData.id,
      dataType: typeName,
      status: newData.status,
      version: newData.version,
      updateType: newData.updateType,
    };

    if (newData.status === OrderSetStatus.Draft) {
      updateData.version = getNextVersion(newData.version, false); // draft consider minor version
    } else if (newData.status === OrderSetStatus.Review) {
      updateData.version = getNextVersion(newData.version, false); // review consider minor version
    } else if (newData.status === OrderSetStatus.Active) {
      updateData.version = getNextVersion(newData.version, true); // active consider major version
    } else if (newData.status === OrderSetStatus.Published) {
      updateData.version = getNextVersion(newData.version, true); // publish consider major version
    }
    if (newData.status === OrderSetStatus.Retired) {
      updateData.version = getNextVersion(newData.version, false);
      updateData.retireReason = newData.retireReason;
      updateData.retireComment = newData.retireComment;
    } else {
      updateData.retireReason = "";
      updateData.retireComment = "";
    }
    updateData.updateTime = Cci.util.DateTime.getCurrentUnixtime();
    updateDataMutation.mutate(updateData);
    if (hasNewActive && typeName === OsmToolType.Group) {
      let tid = "osm-group-" + newData.id + "-id";
      updateTabPage(
        {
          data: {
            ...newData,
            updateDate: Cci.util.DateTime.serverSecsToTimeStr(
              updateData.updateTime,
              "HH:mm DD MMM YYYY"
            ),
            version: updateData.version,
          },
        },
        tid
      );
    } else {
      updateCurrentPage({
        data: {
          ...newData,
          updateDate: Cci.util.DateTime.serverSecsToTimeStr(
            updateData.updateTime,
            "HH:mm DD MMM YYYY"
          ),
          version: updateData.version,
        },
      });
    }
    if (toastMsg) {
      const catNotify = {
        open: true,
        msg: toastMsg,
      } as TCatNotification;

      setToastState(catNotify);
      if (typeName === OsmToolType.Group) {
        osmDispatch({
          type: OsmAction.UpdateSave,
          data: {
            ...newData,
            updateDate: Cci.util.DateTime.serverSecsToTimeStr(
              updateData.updateTime,
              "HH:mm DD MMM YYYY"
            ),
            version: updateData.version,
          },
        });
      }
    }
  };

  const handlePrint = () => {
    let printData = { ...data };
    printData.type = typeName;
    // @ts-ignore
    printOsm(printData, setPrintError);
  };

  const handleExportToPdf = () => {
    let exportData = { ...data };
    exportData.type = typeName;
    exportToPdf(exportData);
  };

  // Handle dialog ok button click
  const handleOk = (okstr: OsmDialogAction, okData: any) => {
    if (okstr === OsmDialogAction.retire) {
      osmDispatch({
        type: OsmAction.DoRetire,
        data: okData,
      });
    } else if (okstr === OsmDialogAction.convert) {
      osmDispatch({
        type: OsmAction.DoConvertToDraft,
      });
    } else if (okstr === OsmDialogAction.publish) {
      osmDispatch({
        type: OsmAction.DoPublish,
        data: okData,
      });
    }
    setBrowseOpen(false);
  };

  const [orders] = useOrderSetOrdersQuery(
    { order_set_type: "master", order_set_id: data.id },
    parseOrderData
  ).data;

  if (orders) {
    allDataRef.current["masterOrders"][data.id] = orders;
  }

  useEffect(() => {
    let newData: any;
    let toastMsg = "Order " + (typeName === OsmToolType.Set ? "Set" : "Group");
    switch (osmState.action) {
      case OsmAction.SaveSet:
      case OsmAction.Save:
      case OsmAction.SaveAndClose:
        handleSave();
        break;

      // case OsmAction.ActivateOrderSet:
      //   newData = { ...data };
      //   newData.status = OrderSetStatus.Active;
      //   // @ts-ignore
      //   handleUpdateData(newData, `${toastMsg} Activated`);
      //   break;
      case OsmAction.ActivateOrderGroup:
        newData = { ...data };
        newData.status = OrderSetStatus.Active;
        newData.updateType = "Published";

        if (newData.preActiveVersion) {
          let pdata = newData.preActiveVersion;
          pdata.status = OrderSetStatus.Retired;
          pdata.updateType = "Retired";
          handleUpdateData(pdata, "", true);
          setTimeout(() => {
            // @ts-ignore
            handleUpdateData(newData, `${toastMsg} Published`);
          }, 20);
        } else {
          // @ts-ignore
          handleUpdateData(newData, `${toastMsg} Published`);
        }
        break;

      case OsmAction.ReviewSet:
        newData = { ...data };
        newData.status = OrderSetStatus.Review;
        // @ts-ignore
        handleUpdateData(newData, `${toastMsg} In Review`);
        break;

      case OsmAction.DoPublish:
        newData = { ...data };
        newData["notification"] = osmState.data?.notification;
        newData.status = OrderSetStatus.Published;
        newData.updateType = "Published";
        // @ts-ignore
        handleUpdateData(newData, `${toastMsg} published`);
        break;

      case OsmAction.DoRetire:
        newData = { ...data };
        newData["retireReason"] = osmState.data?.retireReason;
        newData["retireComment"] = osmState.data?.retireComment;
        newData.status = OrderSetStatus.Retired;
        newData.updateType = "Retired";
        // @ts-ignore
        handleUpdateData(newData, `${toastMsg} Retired`);
        break;

      case OsmAction.DoConvertToDraft:
        newData = { ...data };
        newData.status = OrderSetStatus.Draft;
        // @ts-ignore
        handleUpdateData(newData, `${toastMsg} Update to Draft Status`);
        break;
      case OsmAction.CreateNewVersion:
        newData = { ...data };
        newData.status = OrderSetStatus.Draft;
        newData.updateType = "New Revision";
        // @ts-ignore
        handleUpdateData(newData, `New Version Created`);
        break;
      case OsmAction.Print:
        handlePrint();
        break;
      case OsmAction.ExportToPdf:
        handleExportToPdf();
        break;
      case OsmAction.Undo:
      case OsmAction.Redo:
        updateCurrentPage({
          data: undoSaved[tabid][currentIndex[tabid].current],
        });
        break;
      default:
        break;
    }
  }, [osmState.action, osmNeedUpdate]); // eslint-disable-line

  useEffect(() => {
    if (isNew && nameInputRef.current) {
      setTimeout(() => {
        if (nameInputRef.current) {
          nameInputRef.current.focus();
        }
      }, 100);
    }
  }, [isNew]);

  useEffect(() => {
    if (tabid) {
      updatePageStatus(tabid, { dirty: false });
    }
  }, [editError, tabid]); // eslint-disable-line

  return (
    <>
      {saveDataMutate.isError && handleSaveError()}
      {saveDataMutate.isSuccess && handleSaveSuccess()}
      <Box sx={{ background: "#FFFFFF" }}>
        <Box sx={{ pr: "12px" }}>
          <OsmOrderSetToolbar
            toolType={typeName}
            status={status}
            handleOpenPopup={handleOpenPopup}
            editError={editError}
            data={data}
            versionHistory={versionHistory}
            setVersionHistory={setVersionHistory}
            tabId={tabid}
          />
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            pl: "16px",
            height: "100%",
          }}
        >
          {versionHistory && (
            <VersioningPanel
              dataType={typeName}
              dataId={data.group_id ? data.group_id : data.id}
              currentVersion={data?.version}
              tabId={tabid}
            />
          )}
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              flexGrow: 1,
              width: 0,
              height: "75vh",
              pl: versionHistory ? "12px" : "20px",
            }}
          >
            <OsmOrderSetNamePanel
              {...props}
              disabled={isEditPermission === false}
              setEditError={setEditError}
              inVersionHistory={versionHistory}
              version={data?.version}
              dataType={typeName}
              ref={nameInputRef}
              tabId={tabid}
            />
            <OsmOrderSetDetailPanel
              {...props}
              disabled={isEditPermission === false}
              editError={editError}
              dataType={typeName}
              data={data}
              inVersionHistory={versionHistory}
            />
          </Box>
          <Box
            sx={{
              pl: "16px",
              pr: "20px",
            }}
          >
            <OsmSettingPanel
              disabled={isEditPermission === false}
              settingType={typeName}
              sx={{ height: "75vh", ml: 1 }}
              data={data}
              inVersionHistory={versionHistory}
              handleOpenPopup={handleOpenPopup}
            />
          </Box>
        </Box>
      </Box>
      <CatDialog
        open={browseOpen}
        onClose={handleBrowseClose}
        title={getPopupTitle(osmState.action)}
        anchorEl={anchorEl}
        sx={dialogSx}
      >
        <>
          {osmState.action === OsmAction.BrowseOrders && (
            <BrowseOrdersPanel onClose={handleBrowseClose} currentData={data} />
          )}
          {osmState.action === OsmAction.OrderGroups && (
            <BrowseOrderGroupsPanel
              onClose={handleBrowseClose}
              currentData={data}
              dataType={typeName}
            />
          )}
          {osmState.action === OsmAction.PublishSet && (
            <PublishInfoPanel
              onClose={handleBrowseClose}
              onOk={handleOk}
              publishData={data}
              publishType={OsmToolType.Set}
            />
          )}
          {osmState.action === OsmAction.PublishGroup && (
            <PublishInfoPanel
              onClose={handleBrowseClose}
              onOk={handleOk}
              publishData={data}
              publishType={OsmToolType.Group}
            />
          )}
          {osmState.action === OsmAction.RetireSet && (
            <RetireInfoPanel
              onClose={handleBrowseClose}
              onOk={handleOk}
              retireName={data.name}
              retireType={OsmToolType.Set}
            />
          )}
          {osmState.action === OsmAction.RetireGroup && (
            <RetireInfoPanel
              onClose={handleBrowseClose}
              onOk={handleOk}
              retireName={data.name}
              retireType={OsmToolType.Group}
              retireData={data}
            />
          )}
          {osmState.action === OsmAction.ConvertToDraft && (
            <ConvertToDraftInfoPanel
              onClose={handleBrowseClose}
              onOk={handleOk}
              convertName={data.name}
              convertType={typeName}
            />
          )}
          {osmState.action === OsmAction.OpenAffectedOrderGroupSetDialog && (
            <AffectedOrderSetGroupPanel
              onClose={handleBrowseClose}
              data={data}
            />
          )}
        </>
      </CatDialog>
      <CatSuccessNotification
        open={toastState.open}
        handleClose={handleCloseToast}
        msg={toastState.msg}
      />
    </>
  );
};

export default OsmOrderSetPanel;
