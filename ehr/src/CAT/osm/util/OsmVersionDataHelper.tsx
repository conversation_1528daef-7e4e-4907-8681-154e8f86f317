import { parseSetEnv } from "./OsmDataGridHelper";
// @todo - temporary data type.
type TTmpData = {
  id: string;
  name: string;
  orderGroupName?: string;
  orderSetName?: string;
  synonyms: string;
  status: string;
  environments: string;
  location: string;
  weblinks: string;
  instruction: string;
  updateDate: string;
  searchTerms: string[];
  updatedById: string;
  updatedBy: string;
  groupIds: string[];
  retireReason: string;
  retireComment: string;
  version: string;
  versionNote: string;
  isLatest?: boolean;
  isSelected?: boolean;
  isRequired?: boolean;
  selectionType?: string;
  subIds?: string[];
  defaultExpanded?: boolean;
  updateType?: string;
  specifyLocations?: boolean;
};

type TDataType = string | "set" | "group";
/**
 * Parse version data from server.
 * @prop {object} version data from server
 */
export const parseVersionData = (versionData: any) => {
  let data: any[] = [];
  let dataType: TDataType = "";
  if (
    versionData &&
    versionData.data_type &&
    versionData.data_type.data &&
    versionData.data_type.data[0]
  ) {
    dataType = versionData.data_type.data[0][0];
  }
  if (versionData && versionData.versions && versionData.versions.data) {
    versionData.versions.data.forEach((oneData: any, index: any) => {
      let tmpData = {} as TTmpData;
      if (dataType === "set") {
        const linkArr = JSON.parse(oneData[5] ? oneData[5] : []);
        const envData = parseSetEnv(oneData[4]);

        tmpData = {
          id: oneData[0],
          name: oneData[1],
          orderSetName: oneData[1],
          synonyms: oneData[2],
          status: oneData[3],
          environments: envData.envs,
          location: envData.location,
          weblinks: linkArr,
          instruction: oneData[6],
          updateDate: oneData[7]
            ? Cci.util.DateTime.serverSecsToTimeStr(
                oneData[7],
                "HH:mm DD MMM YYYY"
              )
            : "",
          searchTerms: oneData[8],
          updatedById: oneData[9],
          updatedBy: oneData[17],
          groupIds: JSON.parse(oneData[10]),
          retireReason: oneData[11],
          retireComment: oneData[12],
          version: oneData[13],
          versionNote: oneData[14],
        } as TTmpData;
      } else if (dataType === "group") {
        const envData = parseSetEnv(oneData[12]);
        tmpData = {
          id: oneData[0],
          name: oneData[1],
          orderGroupName: oneData[1],
          synonyms: oneData[2],
          status: oneData[3],
          instruction: oneData[4],
          isSelected: oneData[5],
          isRequired: oneData[6],
          searchTerms: oneData[7],
          selectionType: oneData[8],
          updatedBy: oneData[21],
          subIds: oneData[9] && JSON.parse(oneData[9]),
          updateDate: oneData[10]
            ? Cci.util.DateTime.serverSecsToTimeStr(
                oneData[10],
                "HH:mm DD MMM YYYY"
              )
            : "",
          environments: envData.envs,
          location: envData.location,
          specifyLocations: envData.specifyLocations,
          retireReason: oneData[13],
          retireComment: oneData[14],
          version: oneData[15],
          versionNote: oneData[16],
          defaultExpanded: oneData[17],
          updateType: oneData[18],
        } as TTmpData;
      }
      if (index === 0) {
        tmpData["isLatest"] = true;
      }
      if (tmpData) {
        data.push(tmpData);
      }
    });
  }

  return data;
};

export const byUpdateDateAsc = (a: any, b: any) => {
  const date1 = new Date(`${a.updateDate}`);
  const date2 = new Date(`${b.updateDate}`);

  if (date1 > date2) {
    return -1;
  } else if (date1 < date2) {
    return 1;
  } else {
    return 0;
  }
};
