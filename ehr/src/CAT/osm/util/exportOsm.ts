import { format } from "date-fns";
// @ts-ignore
// @ts-ignore

const saveToLocal = async (blob: any, fileName: any) => {
  const url = window.URL.createObjectURL(blob);

  const fileLink = document.createElement("a");
  fileLink.download = fileName;
  fileLink.href = url;
  fileLink.click();
  setTimeout(() => {
    URL.revokeObjectURL(url);
  });
};

const saveFile = async (fileuri: any, fileName: any) => {
  const fileRes = await fetch(fileuri);
  const blob = await fileRes.blob();
  await saveToLocal(blob, fileName);
};

export function exportToPdf(exportData: any) {
  var params = {
    hobj: "print/osmprint",
    dataid: exportData.id,
    datatype: exportData.type,
    envs: exportData.environments,
    repCfgFile: "osmRep.json",
  };

  function successCB(pdfName: any) {
    let file = Array.isArray(pdfName)
      ? pdfName[0]
      : typeof pdfName === "string"
        ? pdfName
        : "";
    if (file.length > 0 && /.pdf\s*$/.test(file) && cci.cfg) {
      Cci.util.RunTime.hideLoadMask();
      file = file.trim();
      const fileuri =
        cci.cfg.query.cps.preview + "?file=" + encodeURIComponent(file);

      let now = format(new Date(), "yyyy-MMM-dd");
      let fileName = exportData.name + "-" + now + ".pdf";
      saveFile(fileuri, fileName);
    } else {
      failureCB();
    }
  }

  type TResponse = {
    success?: boolean;
    data: {
      result: string;
    }[];
    responseText: string;
  };

  function failureCB(msg = "") {
    Cci.util.RunTime.hideLoadMask();
    console.log(msg);
  }

  Cci.util.RunTime.showLoadMask();

  Cci.RunTime.getSimpleData(
    params,
    function (result: TResponse) {
      if (result.success) {
        successCB([result.data[0].result]);
      } else {
        failureCB("Export to PDF Error! " + result.data[0].result);
      }
    },
    function (result: TResponse) {
      failureCB(
        "Export to PDF Error! " + JSON.parse(result.responseText).ErrMsg
      );
    }
  );
}
