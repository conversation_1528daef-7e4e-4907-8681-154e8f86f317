// @ts-nocheck
import React from "react";
import { Box, IconButton, Link, Stack } from "@mui/material";
import { GridActionsCellItem, useGridApiContext } from "@mui/x-data-grid-pro";
import HighlightedText from "../../common/HighlightedText";
import ChipOSM from "@cci-monorepo/CAT/osm/components/ui/ChipOSM";
import { OrderSetLocation, OrderSetStatus } from "../OsmConstants";
import IconTreeCollapsedPlus from "@cci-monorepo/oe-shared/src/assets/tree/Icn_Elbow_Expanded.svg";
import IconTreeExpanded from "@cci-monorepo/oe-shared/src/assets/tree/Icn_Elbow.svg";
import IconTreeElbow from "@cci-monorepo/oe-shared/src/assets/tree/Icn_Elbow_branch.svg";
import IconTreeElbowEnd from "@cci-monorepo/oe-shared/src/assets/tree/Icn_Elbow-end.svg";
import IconTreeCollapsed from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import EditIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Edit.svg";
import OpenIcon from "@cci-monorepo/Pld/components/implants/assets/Group.svg";
import BlueArrowDown from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import BlueArrowRight from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Right.svg";

/**
 * Custom collapse tree expand button.
 *
 * @param props
 * @constructor
 */
const CustomCollapseTreeExpandButton = (props) => {
  const apiRef = useGridApiContext();
  const rowNode = apiRef.current.getRowNode(props.id);
  const getAllRowIds = apiRef.current.getAllRowIds();
  const nextRowId = getAllRowIds[getAllRowIds.indexOf(props.id) + 1];
  // Check if the next row is a child row or null.
  const isLastChildRowinGroup =
    nextRowId && apiRef.current.getRowNode(nextRowId).depth < rowNode.depth;

  const [expanded, setExpanded] = React.useState(
    rowNode?.childrenExpanded || false
  );

  if (rowNode.type === "leaf") {
    if (rowNode.depth === 0) {
      return null;
    } else {
      return (
        <Box
          sx={{
            height: "24px",
            width: "36px",
            p: 0,
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        />
      );
    }
  } else {
    const handleToggle = (e) => {
      const rowNode = apiRef.current.getRowNode(props.id);

      if (!rowNode) {
        return null;
      }

      if (expanded) {
        apiRef.current.setRowChildrenExpansion(props.id, false);
      } else {
        apiRef.current.setRowChildrenExpansion(props.id, true);
      }

      setExpanded(!expanded);
    };

    return (
      <Box
        sx={{
          height: "24px",
          width: "24px",
          p: 0,
          alignContent: "center",
        }}
      >
        <IconButton
          size="small"
          disableRipple
          onClick={handleToggle}
          onDoubleClick={(e) => {
            // To stop double click event propagation to avoid row selection to edit mode.
            e.stopPropagation();
            return;
          }}
        >
          <img
            alt={"open or close arrow"}
            src={expanded ? BlueArrowDown : BlueArrowRight}
          />
        </IconButton>
      </Box>
    );
  }
};

// Current campus
const currentCampus = cci?.cfg?.campus as string;

const monthNames: Record<number, string> = {
  1: "Jan",
  2: "Feb",
  3: "Mar",
  4: "Apr",
  5: "May",
  6: "Jun",
  7: "Jul",
  8: "Aug",
  9: "Sep",
  10: "Oct",
  11: "Nov",
  12: "Dec",
};

/**
 * Format date string to "HH:MM DD MMM YYYY"
 * @param dateString
 */
function formatDate(dateString: string) {
  const date = new Date(dateString);

  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const day = date.getDate();

  const month = monthNames[date.getMonth() + 1];
  const year = date.getFullYear();

  return `${hours}:${minutes} ${day} ${month} ${year}`;
}

/**
 * Hold Orders data from server.
 * Foramt:  order_id ==> order_data
 */
export let orders = {};

/**
 * Get data grid column headers.
 * @returns [] of columns
 * @param type can be 'set' or 'group'
 * @param handleEditRow
 * @param searchStr
 * @param isEditPermission bool, true if edit permission granted
 */
export const getOsmDataGridColumns = (
  type: any,
  handleEditRow: any,
  searchStr: any,
  isEditPermission: any
) => {
  return type === "set"
    ? [
        {
          field: "action",
          headerName: "",
          width: 60,
          sortable: false,
          disableColumnMenu: true,
          type: "actions",
          align: "center",
          getActions: (params: any) => [
            <GridActionsCellItem
              label={"actions-link-" + params.id}
              sx={{
                cursor: "pointer",
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
              icon={
                <IconButton className={"osmActionIconButton"}>
                  <img
                    src={
                      params.row?.status === OrderSetStatus.Draft
                        ? EditIcon
                        : OpenIcon
                    }
                    alt={"osmActionIconButton"}
                  />
                </IconButton>
              }
              onClick={() => {
                if (handleEditRow) {
                  handleEditRow(params.row);
                }
              }}
            />,
          ],
        },
        {
          field: "orderSetName",
          width: 468,
          flex: 1,
          headerName: "Order Set Name",
          editable: false,
          renderCell: (params: any) => (
            <Stack
              direction={"row"}
              gap={2}
              sx={{
                alignItems: "center",
              }}
            >
              <CustomCollapseTreeExpandButton id={params.id} />
              {(params.row?.status === OrderSetStatus.Draft ||
                params.row?.status === OrderSetStatus.Review) && (
                <ChipOSM label="Draft" size="small" sx={{ height: 20 }} />
              )}
              {params.row?.status === OrderSetStatus.Retired && (
                <ChipOSM label="Retired" size="small" sx={{ height: 20 }} />
              )}
              <HighlightedText
                text={params.value}
                substring={searchStr}
                isItalic={params.row?.status === OrderSetStatus.Retired}
              />
            </Stack>
          ),
        },
        {
          field: "updatedById",
          headerName: "Staff ID",
          width: 150,
          editable: false,
        },
        {
          field: "version",
          headerName: "Version",
          width: 90,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText text={params.value} substring={searchStr} />
          ),
        },
        {
          field: "updatedBy",
          headerName: "Last Updated By",
          width: 174,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText text={params.value} substring={searchStr} />
          ),
        },
        {
          field: "updateDate",
          headerName: "Last Updated",
          width: 156,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              isItalic={params.row?.status === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "id",
          headerName: "Order Set ID",
          width: 130,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              isItalic={params.row?.status === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "environments",
          headerName: "Environments",
          width: 200,
          editable: false,
          flex: 1,
          renderCell: (params: any) => {
            let textContent = "";
            if (typeof params.value === "string") {
              textContent = params.value;
            } else if (params.value?.[currentCampus]?.display) {
              textContent = params.value[currentCampus].display;
            } else if (Array.isArray(params.value?.[currentCampus])) {
              textContent = params.value[currentCampus]
                .map(({ display }: any) => display)
                .join(",");
            }
            return (
              <HighlightedText
                text={textContent}
                isItalic={params.row?.status === OrderSetStatus.Retired}
              />
            );
          },
        },
        {
          field: "status",
          headerName: "Status",
          sortable: false,
          width: 116,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              isItalic={params.value === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "data",
          headerName: "Data",
          sortable: false,
        },
      ]
    : [
        {
          field: "action",
          headerName: "",
          width: 60,
          sortable: false,
          disableColumnMenu: true,
          type: "actions",
          align: "center",
          getActions: (params: any) => [
            <GridActionsCellItem
              label={"actions-link-" + params.id}
              sx={{
                cursor: "pointer",
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
              icon={
                <IconButton className={"osmActionIconButton"}>
                  <img
                    src={
                      params.row?.status === OrderSetStatus.Draft
                        ? EditIcon
                        : OpenIcon
                    }
                    alt={"osmActionIconButton"}
                  />
                </IconButton>
              }
              onClick={() => {
                if (handleEditRow) {
                  handleEditRow(params.row);
                }
              }}
            />,
          ],
        },
        {
          field: "orderGroupName",
          width: 468,
          flex: 1,
          headerName: "Order Group Name",
          editable: false,
          renderCell: (params: any) => (
            <Stack
              direction={"row"}
              gap={2}
              sx={{
                alignItems: "center",
              }}
            >
              <CustomCollapseTreeExpandButton id={params.id} />
              {params.row?.status === OrderSetStatus.Draft && (
                <ChipOSM
                  label="Draft"
                  size="small"
                  sx={{ height: 20, mr: 2 }}
                />
              )}
              {params.row?.status === OrderSetStatus.Retired && (
                <ChipOSM
                  label="Retired"
                  size="small"
                  sx={{ height: 20, mr: 2 }}
                />
              )}
              <HighlightedText
                text={params.value}
                substring={searchStr}
                isItalic={params.row?.status === OrderSetStatus.Retired}
              />
            </Stack>
          ),
        },
        {
          field: "updatedBy",
          headerName: "Last Updated By",
          width: 174,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              substring={searchStr}
              isItalic={params.row?.status === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "version",
          headerName: "Version",
          width: 90,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              substring={searchStr}
              isItalic={params.row?.status === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "group_id",
          headerName: "Order Group ID",
          width: 130,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              substring={searchStr}
              isItalic={params.row?.status === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "updateDate",
          headerName: "Last Updated",
          width: 468,
          flex: 1,
          editable: false,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              substring={searchStr}
              isItalic={params.row?.status === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "status",
          headerName: "Status",
          sortable: false,
          width: 116,
          renderCell: (params: any) => (
            <HighlightedText
              text={params.value}
              substring={searchStr}
              isItalic={params.value === OrderSetStatus.Retired}
            />
          ),
        },
        {
          field: "data",
          headerName: "Data",
          sortable: false,
        },
      ];
};

/**
 * Support status string and staffid
 * @param {String} filter original filter string
 * @returns {String} actual filter string
 */
// @todo OrderSetStatus
const getFilterStr = (filter: OrderSetStatus) => {
  return filter === OrderSetStatus.Draft ||
    filter === OrderSetStatus.Active ||
    filter === OrderSetStatus.Retired ||
    isNaN(filter)
    ? filter
    : undefined;
};

type TEnvObj = {
  location: string;
  specifyLocations: boolean;
  envs: Record<string, any> | string;
};

/**
 * Parse Set env data, it can be regular string or json string
 * @prop {string} rawEnvData string for env, it can be rgular string or json string
 * @return {object} envObj
 *  - location string hospital/ambulatory, so far, it can be hospitalAndAmbulatory, hospitalOnly and ambulatoryOnly
 *  - envs object
 *     * campus: { name, display }
 */
export const parseSetEnv = (rawEnvData: string | TEnvObj) => {
  const defaultLocation = OrderSetLocation.HospitalAndAmbulatory;

  let envObj = {
    location: defaultLocation,
    specifyLocations: false,
    envs: {},
  } as TEnvObj;
  if (!rawEnvData) {
    return envObj;
  }
  if (typeof rawEnvData === "string") {
    try {
      envObj = JSON.parse(rawEnvData);
    } catch (e) {
      envObj.envs = rawEnvData;
    }
  } else if (typeof rawEnvData === "object") {
    envObj = rawEnvData;
  }

  if (!envObj.location) {
    envObj.location = defaultLocation;
  }

  if (envObj.specifyLocations === undefined) {
    envObj.specifyLocations = false;
  }

  if (typeof envObj.envs === "string") {
    const tmpEnvName = envObj.envs;
    envObj.envs = {};
    if (currentCampus) {
      envObj.envs[currentCampus] = [
        {
          name: tmpEnvName,
          display: tmpEnvName,
        },
      ];
    }
  }

  return envObj;
};

/**
 * Convert Order Set data from server data to grid set data
 * set row header
 *   ["master_set_id", "name", "synonyms", "status", "env", "weblinks", "instructions", "last_modified_time", "search_terms", "stored_by", "group_ids", "retire_reason", "retire_comment", "version", "version_note", "__rid__", "__ts__", "favorited", "suggested", "num_order", "staffname", "group_details"]
 *   [ 0,               1,      2,          3,        4,     5,          6,              7,                    8,              9,           10,          11,              12,               13,        14,             15,        16,       17,          18,          19,          20,          21            ]
 * @param {Object} orderSetsData
 * @param {Object} filter optional, filter object, may contain filter.status, filter.staffid or filter.searchStr
 * @returns [] of row data and object of sets
 */
export const parseOrderSetsData = (orderSetsData: any, filter: any) => {
  let data: any = [];
  type SetsType = Record<string, any>;

  let sets: SetsType = {};

  const statusStr = getFilterStr(filter?.status);

  if (orderSetsData && orderSetsData.sets && orderSetsData.sets.data) {
    const showdata = orderSetsData.sets.data.filter((d: any) => {
      let searchTerms = [];
      if (d[8]) {
        try {
          searchTerms = JSON.parse(d[8]);
        } catch (e) {
          console.log("Incorrect search terms for the Set: ", d);
        }
      }
      let isFound = true;
      if (statusStr) {
        isFound = isFound && d[3] === statusStr;
      }
      if (filter?.staffid) {
        isFound = isFound && d[9] === filter?.staffid.toString();
      }
      if (filter?.searchStr !== "") {
        // filter by "name", "synonyms" or "search terms"
        isFound =
          isFound &&
          (d[1]?.toLowerCase().includes(filter?.searchStr) ||
            d[2]?.toLowerCase().includes(filter?.searchStr) ||
            searchTerms.find((searchTerm: any) =>
              searchTerm.toLowerCase().includes(filter?.searchStr)
            ));
      }
      return isFound;
    });

    showdata.forEach((oneData: Record<number, any>) => {
      const linkArr = JSON.parse(oneData[5] ? oneData[5] : []);
      const envData = parseSetEnv(oneData[4]);
      let tmpData = {
        id: oneData[0],
        name: oneData[1],
        orderSetName: oneData[1],
        synonyms: oneData[2],
        searchTerms: oneData[8],
        // Potentially replace 'string' with OrderSetStatus
        status: oneData[3],
        environments: envData.envs,
        location: envData.location,
        specifyLocations: envData.specifyLocations,
        weblinks: linkArr,
        instruction: oneData[6],
        updateDate: oneData[7]
          ? Cci.util.DateTime.serverSecsToTimeStr(
              oneData[7],
              "HH:mm DD MMM YYYY"
            )
          : "",
        updatedById: oneData[9],
        updatedBy: oneData[20],
        groupIds: JSON.parse(oneData[10]),
        retireReason: oneData[11],
        retireComment: oneData[12],
        version: oneData[13],
        versionNote: oneData[14],
      };
      data.push(tmpData);

      sets[oneData[0]] = tmpData;
    });
  }
  return [data, sets];
};

type TUserSetsData = {
  data: {
    data: any[];
    header: any[];
  };
  [key: string]: any;
};

/**
 * Convert User Set data from server data to grid set data based on response header
 * @param {Object} userSetsData
 * @param {Object} filter optional, filter object, may contain filter.status, filter.staffid or filter.searchStr
 * @returns [] of row data and object of sets
 */

export const parseUserSetsData = (userSetsData: TUserSetsData, filter: any) => {
  const statusStr = getFilterStr(filter?.status);
  let userSets: any = [];

  if (userSetsData?.data?.data && userSetsData?.data?.header) {
    userSetsData.data.data.forEach((set: any) => {
      const tempSetObject = {} as Record<string, any>;
      userSetsData.data.header.forEach((field: any, i: any) => {
        tempSetObject[field] = set[i];
      });

      const linkArr = JSON.parse(
        tempSetObject.weblinks ? tempSetObject.weblinks : []
      );

      const envData = parseSetEnv(tempSetObject.env);

      const setObject = {
        id: tempSetObject.user_set_id,
        masterSetId: tempSetObject.master_set_id,
        userId: tempSetObject.user_id,
        name: tempSetObject.name,
        orderSetName: tempSetObject.name,
        synonyms: tempSetObject.synonyms,
        searchTerms: tempSetObject?.search_terms,
        status: tempSetObject.status,
        environments: envData.envs,
        location: envData.location,
        weblinks: linkArr,
        instruction: tempSetObject.instructions,
        updateDate: tempSetObject.last_modified_time
          ? Cci.util.DateTime.serverSecsToTimeStr(
              tempSetObject.last_modified_time,
              "HH:mm DD MMM YYYY"
            )
          : "",
        updatedById: tempSetObject.user_id,
        updatedBy: tempSetObject.staffname,
        groupIds: JSON.parse(tempSetObject.group_ids),
        isFavorite: tempSetObject.is_favorite,
        isSuggested: tempSetObject.is_suggested,
        version: tempSetObject.version,
      };
      userSets.push(setObject);
    });
  }

  userSets = userSets.filter((set: any) => {
    let searchTerms = [] as string[];
    if (set.searchTerms) {
      searchTerms = JSON.parse(set?.searchTerms);
    }

    let isFound = true;

    if (statusStr) {
      isFound = isFound && set?.status === statusStr;
    }
    if (filter?.staffid) {
      isFound = isFound && set?.userId === filter?.staffid.toString();
    }
    if (filter?.searchStr !== "") {
      // filter by "name", "synonyms" or "search terms"
      isFound =
        isFound &&
        (set.name.toLowerCase().includes(filter.searchStr) ||
          set.synonyms.toLowerCase().includes(filter.searchStr) ||
          searchTerms.find((searchTerm) =>
            searchTerm.toLowerCase().includes(filter.searchStr)
          ));
    }
    return isFound;
  });
  return userSets;
};

const getOrderGroups = (showdata: any) => {
  let groups: GroupType = {};
  let datas: any = [];
  showdata.forEach((oneData: DataType) => {
    const envData = parseSetEnv(oneData[12]);
    let tmpData = {
      id: oneData[0],
      name: oneData[1],
      orderGroupName: oneData[1],
      synonyms: oneData[2],
      status: oneData[3],
      environments: envData.envs,
      location: envData.location,
      specifyLocations: envData.specifyLocations,
      instruction: oneData[4],
      isSelected: oneData[5],
      isRequired: oneData[6],
      searchTerms: oneData[7],
      selectionType: oneData[8],
      updatedBy: oneData[19],
      subIds: oneData[9] && JSON.parse(oneData[9]),
      updateDate: oneData[10]
        ? Cci.util.DateTime.serverSecsToTimeStr(
            oneData[10],
            "HH:mm DD MMM YYYY"
          )
        : "",
      retireReason: oneData[13],
      retireComment: oneData[14],
      version: oneData[15],
      versionNote: oneData[16],
      defaultExpanded: oneData[17],
      updateType: oneData[18],
    };
    datas.push(tmpData);
    groups[oneData[0]] = tmpData;
  });
  return [groups, datas];
};

/**
 * Convert Order Group data from server data to grid group data.
 * The returned Group data has two REsId: "Groups" and "Orders"
 * Groups row header:
 *   ['group_id', 'group_name', 'synonyms', 'group_status', 'instruction', 'is_selected', 'is_required', 'search_terms', 'selection_type', 'sub_ids', 'last_modified_time', 'stored_by', 'env', 'retire_reason', 'retire_comment', 'version', 'version_note', 'staffname']
 *   [ 0,          1,            2,          3,              4,             5,             6,             7,              8,                9,         10,                   11,          12,    13,              14,               15,        16,             17        ]
 * Order row header:
 *   ["order_id", "order_data"]
 *   [0,          1           ]
 * @param {Object} orderGroupsData
 * @param filter
 * @returns [] of row data and objects of groups and orders
 */
export const parseOrderGroupsData = (orderGroupsData: any, filter: any) => {
  type OrdersType = Record<string, any>;
  type GroupType = Record<string, any>;
  type DataType = any[];

  if (!orderGroupsData) return [];

  let data: any = [];
  let groups: GroupType = {};
  let orders: OrdersType = {};

  // Hold Orders data from server as order_id ==> order_data
  if (orderGroupsData.Orders && orderGroupsData.Orders.data) {
    orderGroupsData.Orders.data.forEach((order: any) => {
      const tmpId = order[0];
      let tmpData = JSON.parse(order[1]);
      tmpData["orderid"] = tmpId;

      orders[tmpId] = tmpData;
    });
  }

  const statusStr = getFilterStr(filter?.status);

  if (orderGroupsData.Groups && orderGroupsData.Groups.data) {
    const showdata = orderGroupsData.Groups.data.filter((d: any) => {
      let searchTerms = [];
      if (d[7]) {
        try {
          searchTerms = JSON.parse(d[7]);
        } catch (e) {
          console.log("Incorrect search terms for the Group: ", d);
        }
      }
      let isFound = true;
      if (statusStr) {
        isFound = isFound && d[3] === statusStr;
      }
      if (filter?.staffid) {
        isFound = isFound && d[11] === filter?.staffid.toString();
      }
      if (filter?.searchStr !== "") {
        // filter by "name", "Last Updated By", "Version", "Group ID", "S", or "search terms"
        isFound =
          isFound &&
          (d[1]?.toLowerCase().includes(filter?.searchStr) ||
            d[19]?.toLowerCase().includes(filter?.searchStr) ||
            d[15]?.toLowerCase().includes(filter?.searchStr) ||
            d[0]?.toLowerCase().includes(filter?.searchStr) ||
            d[3]?.toLowerCase().includes(filter?.searchStr) ||
            (d[10] &&
              Cci.util.DateTime.serverSecsToTimeStr(d[10], "HH:mm DD MMM YYYY")
                .toLowerCase()
                .includes(filter?.searchStr)) ||
            searchTerms.find((searchTerm) =>
              searchTerm.toLowerCase().includes(filter?.searchStr)
            ));
      }
      return isFound;
    });
    [, data] = getOrderGroups(showdata);
    [groups] = getOrderGroups(orderGroupsData.Groups.data);
  }
  return [data, groups, orders];
};

/**
 * Convert Order data from server data to grid order data.
 * Order row header:
 *   ["order_set_id", "order_id", "order_data"]
 *   [0,          1,          2           ]
 * @param {Object} orderData
 * @returns [] of object of orders
 */
export const parseOrderData = (orderData: any) => {
  type OrdersType = Record<string, any>;

  if (!orderData) return [];

  let orders: OrdersType = {};

  // Hold Orders data from server as order_id ==> order_data
  if (orderData.Orders && orderData.Orders.data) {
    orderData.Orders.data.forEach((order: any) => {
      const tmpId = order[1];
      let tmpData = JSON.parse(order[2]);
      tmpData["orderid"] = tmpId;

      orders[tmpId] = tmpData;
    });
  }

  return [orders];
};
