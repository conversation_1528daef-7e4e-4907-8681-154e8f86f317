// @ts-ignore

// @ts-ignore

// @ts-ignore

export type TPrintDataOsm = {
  id: string;
  type: string;
  environments: string;
};

type TErrorStateSetterProps = { title?: string; message?: string } | null;

export function printOsm(
  printData: TPrintDataOsm,
  errorStateSetter: (props: TErrorStateSetterProps) => void
) {
  var params = {
    hobj: "print/osmprint",
    dataid: printData.id,
    datatype: printData.type,
    envs: printData.environments,
    repCfgFile: "osmRep.json",
  };

  function successCB(pdfName: string | string[]) {
    let file = Array.isArray(pdfName)
      ? pdfName[0]
      : typeof pdfName === "string"
        ? pdfName
        : "";

    if (file.length > 0 && /.pdf\s*$/.test(file)) {
      Cci.util.RunTime.hideLoadMask();
      file = file.trim();

      const fileuri =
        cci.cfg.query.cps.preview + "?file=" + encodeURIComponent(file);
      var win = Ext.create("Cci.window.PdfPreviewWindow", {
        fileuri: fileuri,
        requestURL: cci.cfg.query.cps.getSimpleData as string,
        printParams: params,
        hideOptions: true,
        formatRst: true,
      });
      win.show();
      errorStateSetter(null);
    } else {
      failureCB();
    }
  }

  function failureCB(msg = "") {
    Cci.util.RunTime.hideLoadMask();
    errorStateSetter({
      title: "Print Preview",
      message: msg || "Failed to print due to timeout. File may be too large.",
    });
  }

  Cci.util.RunTime.showLoadMask();
  if (Cci.RunTime.isWebsockPrintReady() && cci.cfg) {
    Cci.util.RunTime.startPrintJob(
      "Preview",
      cci.cfg.query.cps.getSimpleData,
      params,
      { hideOptions: true, formatRst: true },
      successCB,
      failureCB
    );
  } else {
    Cci.RunTime.getSimpleData(
      params,
      function (result: { success: boolean; data: { result: string }[] }) {
        if (result.success) {
          successCB([result.data[0].result]);
        } else {
          failureCB("Print Error! " + result.data[0].result);
        }
      },
      function (result: { responseText: string }) {
        failureCB("Print Error! " + JSON.parse(result.responseText).ErrMsg);
      }
    );
  }
}
