/**
 * Help functions to generate save data
 */
import { OrderSetLocation, OrderSetStatus } from "../OsmConstants";
import { parseSetEnv } from "../util/OsmDataGridHelper";
// @ts-ignore

export type TOsmSaveDataHelper = {
  id: string;
  name: string;
  status: string;
  env: {
    // OrderSetLocation
    location: any;
    envs: any;
  };
  synonyms: string;
  instructions: string;
  weblinks: string;
  group_ids: string[];
  search_terms: string[];
  stored_by: string;
  version: string;
  groups?: string;
};

/**
 * Get all data for a page for saving
 * @prop {object} data current page data
 * @prop {string} typeName the data type, "set" or "group"
 * @prop {object} allDataRef the reference of other data, such as "groups", "orders"
 * @return {object} saveData
 */
export const getPageData = (
  data: any,
  typeName: any,
  allDataRef: any,
  isNew: any,
  isAutoSave?: any
) => {
  let tmpData = { ...data };

  // do simple data-checking
  if (!tmpData || !tmpData.name || (!tmpData.groupIds && !tmpData.subIds)) {
    return undefined;
  }

  let orders: any = [];
  let groups: any = [];

  // Save groups in the Set
  if (tmpData.groupIds && Array.isArray(tmpData.groupIds)) {
    let groups: any = [];
    tmpData.groupIds.forEach((groupId: any) => {
      if (allDataRef?.current.groups[groupId]) {
        groups.push(allDataRef.current.groups[groupId]);
      }
    });
    typeName === "set"
      ? (tmpData.groups = groups)
      : (tmpData.subgroups = groups);
  }

  // Save Orders and Sub-groups in the Group
  if (tmpData.subIds && Array.isArray(tmpData.subIds)) {
    tmpData.subIds.forEach((subId: any) => {
      if (subId.orderid) {
        if (allDataRef?.current.orders[subId.orderid]) {
          orders.push(allDataRef.current.orders[subId.orderid]);
        }
      } else if (subId.group_id) {
        if (allDataRef?.current.groups[subId.group_id]) {
          groups.push(allDataRef.current.groups[subId.group_id]);
        }
      }
    });
  }

  if (orders && orders.length > 0) {
    tmpData.orders = orders;
  }

  if (groups && groups.length > 0) {
    typeName === "set"
      ? (tmpData.groups = groups)
      : (tmpData.subgroups = groups);
  }

  return typeName === "set"
    ? getSetSaveData(tmpData)
    : getGroupSaveData(tmpData, isNew, isAutoSave);
};

/**
 * Get next version based on given version.
 * @param {string} version current version
 * @param {boolean} isMajor update major version
 * @return {string} nextVersion
 */
export const getNextVersion = (
  version: string,
  isMajor: boolean = false
): string => {
  if (!version) return "0.1";

  const [major, minor] = version.split(".");
  if (isMajor) {
    return (+major + 1).toString() + ".0";
  }

  return major + "." + (+minor + 1).toString();
};

/**
 * Convert given Order Group data object to object for saving Order Group
 * @param {Object} group group data object
 * @return
 *  - groupId:   required, the group_id, -1 for no id, i.e. new Group
 *  - groupName: required, the name of the Group, used for checking duplication
 *  - groupData: required, group data in json string
 *     * groups: array of groups in json string format
 *     * orders: array of orders in json string format
 *  - version:   optional, the version of the group, default is empty, i.e. current version
 *
 * groupData in json string format
 *  - groups: array of groups in json string format
 *     * group_id: group id, for new group, it is '-1'
 *     * group_name: name of the group
 *     * synonyms: comma delimited string ?
 *     * group_status: string
 *     * instruction: string
 *     * is_selected: 0 or 1
 *     * is_required: 0 or 1
 *     * search_terms: json string (fe "['Base'],['Generic']")
 *     * selection_type: 1 or othere number ?
 *     * store_by: staff_id
 *     * sub_group_ids: pipe-delimited group ids
 *     * order_ids: array of order ids
 *        - order_id
 *        - selectd: 0 or 1
 *  - orders: optional, when there are new Orders or Orders get modified, array of orders in json string format
 *     * order_id: string
 *     * order_data: json string
 *  - subgroups: optional, when there are new subgroup or subgroups get modified, array of groups in json string format
 */
export const getGroupSaveData = (group: any, isNew: any, isAutoSave?: any) => {
  if (!group || !group.name) {
    return {};
  }

  const groupId = isNew
    ? "-1"
    : group.group_id
      ? group.group_id
      : group.id
        ? group.id
        : "-1";
  const groupName = group.name;

  const groupVersion = getNextVersion(group.version);
  const envData = {
    location: group.location
      ? group.location
      : OrderSetLocation.HospitalAndAmbulatory,
    specifyLocations: group.specifyLocations ? group.specifyLocations : false,
    envs: group.environments,
  };
  const groupData = {
    group_id: groupId,
    group_name: groupName,
    synonyms: group.synonyms ? group.synonyms : "",
    group_status: group.status ? group.status : OrderSetStatus.Draft,
    env: envData,
    instruction: group.instruction ? group.instruction : "",
    is_selected: group.is_selected === undefined ? 0 : group.is_selected,
    is_required: group.is_required === undefined ? 0 : group.is_required,
    search_terms: group?.searchTerms ?? "",
    selection_type:
      group.selectionType || group.selectionType === 0
        ? group.selectionType
        : 3,
    sub_ids: group.subIds ? group.subIds : "",
    stored_by: Cci.util.Staff.getSid(),
    version: groupVersion,
    version_note: group.versionNote,
    default_expanded: group.defaultExpanded,
    update_type:
      groupId === "-1"
        ? "Created Draft"
        : isAutoSave
          ? "Modified (Autosave)"
          : "Modified",
  };

  // let saveGroupData = {
  //     groupId: groupId,
  //     groupName: groupName,
  //     groupData: groupData,
  //     groupVersion: groupVersion,
  // }

  let saveGroupData = { ...groupData } as Record<string, any>;

  if (group.orders) {
    saveGroupData["orders"] = [];
    group.orders.forEach((order: { id: any }) => {
      saveGroupData["orders"].push({
        id: order.id,
        data: JSON.stringify(order),
      });
    });
  }
  // if (group.subgroups) {
  //     saveGroupData['subgroups'] = group.subgroups;
  // }

  return { group: saveGroupData };
};

/**
 * Convert given Order Set data object to object for saving Order Set
 */
export const getSetSaveData = (set: any) => {
  if (!set || !set.name) {
    return {};
  }

  const setId = set.id ? set.id : "-1";
  const setName = set.name;

  const setVersion = getNextVersion(set.version);
  const envData = {
    location: set.location
      ? set.location
      : OrderSetLocation.HospitalAndAmbulatory,
    specifyLocations: set.specifyLocations ? set.specifyLocations : false,
    envs: set.environments,
  };

  const setData: TOsmSaveDataHelper = {
    id: setId,
    name: setName,
    status: set.status ? set.status : OrderSetStatus.Draft,
    env: envData,
    synonyms: set.synonyms,
    instructions: set.instruction ? set.instruction : "",
    weblinks: set.weblinks,
    group_ids: set.groupIds,
    search_terms: set?.searchTerms ?? "",
    stored_by: Cci.util.Staff.getSid(),
    version: setVersion,
  };

  let saveSetData = { ...setData };

  if (set.groups) {
    saveSetData["groups"] = set.groups;
  }

  return { set: saveSetData };
};

export const getGroupSavedData = (saveDataMutate: any) => {
  let data = {};
  const oneData: any = saveDataMutate?.data?.newGroup?.data[0]
    ? saveDataMutate?.data?.newGroup?.data[0]
    : saveDataMutate?.data?.updateGroup?.data[0]
      ? saveDataMutate?.data?.updateGroup?.data[0]
      : [];
  if (oneData && oneData.length > 0) {
    const envData = parseSetEnv(oneData[12]);
    data = {
      id: oneData[0],
      group_id: oneData[0],
      name: oneData[1],
      orderGroupName: oneData[1],
      synonyms: oneData[2],
      status: oneData[3],
      instruction: oneData[4],
      isSelected: oneData[5],
      isRequired: oneData[6],
      searchTerms: oneData[7],
      selectionType: oneData[8],
      updatedBy: oneData[19],
      subIds: oneData[9] && JSON.parse(oneData[9]),
      updateDate: oneData[10]
        ? Cci.util.DateTime.serverSecsToTimeStr(oneData[10], "HH:mm MM/DD/YYYY")
        : "",
      environments: envData.envs,
      location: envData.location,
      specifyLocations: envData.specifyLocations,
      retireReason: oneData[13],
      retireComment: oneData[14],
      version: oneData[15],
      versionNote: oneData[16],
      defaultExpanded: oneData[17],
      updateType: oneData[18],
    };
  }
  return data;
};
