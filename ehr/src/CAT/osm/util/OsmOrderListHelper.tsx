import HighlightedText from "../../common/HighlightedText";
import { cloneDeep } from "lodash";

export type TOrderListDataRows = {
  id: string;
  orderName: string;
  subcat: string;
  frequency: string;
  // @todo porentical misspring.
  "frequency:"?: string;
  comment: string;
  cat: string;
  data: string;
  therapeuticClass?: string;
  dose?: string;
  route?: string;
};

export type TorderListData = {};

export type TArgs = {
  cat: "Medications" | "ALL_ORDERS" | string;
  searchstr: string;
};
type TOrderListData =
  | {
      columns: any[];
      rows: TOrderListDataRows[];
    }
  | undefined;

type TmyOrderListData = {
  Rx?: {
    header: any[];
    data: any[];
  };
  frequentOrders?: {
    header: any[];
    data: any[];
  };
};
const idColumn = {
  field: "id",
  headerName: "id",
  width: 10,
  sortable: false,
};

const therapeuticColumn = {
  field: "therapeuticClass",
  headerName: "Therapeutic Class",
  width: 158,
  sortable: true,
};

const categoryColumn = {
  field: "cat",
  headerName: "Category",
  width: 158,
  sortable: true,
};

const subCategoryColumn = {
  field: "subcat",
  headerName: "SubCategory",
  width: 158,
  sortable: true,
};

const doseColumn = {
  field: "dose",
  headerName: "Dose",
  width: 140,
  sortable: true,
};

const routeColumn = {
  field: "route",
  headerName: "Route",
  width: 158,
  sortable: true,
};

const frequencyColumn = {
  field: "freqency",
  headerName: "Freq",
  width: 152,
  sortable: true,
};

const commentColumn = {
  field: "comment",
  headerName: "Comment",
  width: 160,
  flex: 1,
  sortable: true,
};

/**
 * Get columns based on given category and sub-category
 */
const getColumnsByCategory = (options: TArgs) => {
  const { cat, searchstr } = options;

  let columns = [] as {
    field: string;
    headerName: string;
    width: number;
    sortable: boolean;
  }[];

  const nameColumn = {
    field: "orderName",
    headerName: "Name",
    width: 320,
    sortable: true,
    renderCell: (params: { value: string }) => (
      <HighlightedText text={params.value} substring={searchstr} />
    ),
  };

  switch (cat) {
    case "Medications":
    case "ALL_ORDERS":
      columns = [
        idColumn,
        nameColumn,
        therapeuticColumn,
        categoryColumn,
        subCategoryColumn,
        doseColumn,
        routeColumn,
        frequencyColumn,
        commentColumn,
      ];
      break;
    default:
      columns = [
        idColumn,
        nameColumn,
        categoryColumn,
        subCategoryColumn,
        frequencyColumn,
        commentColumn,
      ];
      break;
  }

  return columns;
};

/**
 * Parse raw data from ordersets/getList to DataGrid data.
 */
export const parseOrderListData = (
  orderListData: TmyOrderListData,
  args: TArgs
) => {
  if (args?.cat === "ALL_ORDERS" && !args?.searchstr) {
    return parseMyOrderListData(orderListData, args);
  }

  let data = undefined as TOrderListData | undefined;

  const rawHeader = orderListData?.Rx?.header ?? undefined;
  const rawData = orderListData?.Rx?.data ?? undefined;
  const filterStr = args?.searchstr?.length > 2 ? args.searchstr : "";

  if (rawData && rawData.length > 0) {
    data = {
      columns: [],
      rows: [],
    };
    data.columns = getColumnsByCategory(args);
    // @todo duplication with line 209.
    const showData = filterStr
      ? rawData.filter((d: string) => {
          return d[2].toLowerCase().includes(filterStr.toLowerCase());
        })
      : rawData;

    if (showData) {
      for (let i = 0; i < showData.length; i++) {
        const oneData = showData[i];
        let tmpData = {
          id: oneData[0],
          orderName: oneData[2],
          subcat: oneData[5],
          frequency: oneData[9]?.Frequency,
          comment: "",
          cat: oneData[4],
          data: oneData,
          dataHeader: rawHeader,
        } as TOrderListDataRows;
        if (args?.cat === "Medications") {
          const moreInfo = JSON.parse(oneData[6]);
          tmpData["therapeuticClass"] = oneData[3];
          tmpData["dose"] = moreInfo?.Dose;
          tmpData["route"] = moreInfo?.Route;
          // @todo potencial bug.
          tmpData["frequency:"] = moreInfo?.Frequency;
        }
        data.rows.push(tmpData);
      }
    }
    if (data.rows.length < 1) {
      data = undefined;
    }
  }

  return data;
};

export const getMappedResData = (data?: any[], header?: any[]) => {
  const mappedData: any[] = [];
  data?.forEach((arr: any) => {
    let mapping: any = {};
    arr.forEach((val: any, idx: any) => {
      if (header && header.length > 0) {
        var key = cloneDeep(header[idx]);
        mapping[key] = val;
      }
    });
    mappedData.push(mapping);
  });
  return mappedData;
};

export const parseMyOrderList = (props: any) => {
  let data = undefined as TOrderListData | undefined;
  if (props && (props.favoriteOrders || props.frequentOrders)) {
    const favOrderIdSet = new Set();
    const favOrderRows: any[] = getMappedResData(
      props.favoriteOrders?.data,
      props.favoriteOrders?.header
    ).map((ord) => ({ ...ord, isFav: "1" }));

    favOrderRows.forEach((ord) => {
      favOrderIdSet.add(ord.rxid);
    });
    const unfilteredFreqOrderRows = getMappedResData(
      props.frequentOrders?.data,
      props.frequentOrders?.header
    ).map((ord) => ({ ...ord, isFreq: "1" }));
    const freqOrderRows = unfilteredFreqOrderRows.filter(
      (ord) => !favOrderIdSet.has(ord.rxid)
    );
    favOrderRows.forEach((ord) => {
      if (unfilteredFreqOrderRows.find((row) => row.rxid === ord.rxid)) {
        ord.isFreq = "1";
      }
    });

    const rawData = [...favOrderRows, ...freqOrderRows];

    const filterStr =
      props.args?.searchstr?.length > 2 ? props.args.searchstr : "";

    if (rawData && rawData.length > 0) {
      data = {
        columns: [],
        rows: [],
      };
      // data.columns = getColumnsByCategory(props.args);

      const showData = filterStr
        ? rawData.filter((d: string) => {
            return d[2].includes(filterStr);
          })
        : rawData;
      if (showData) {
        for (let i = 0; i < showData?.length; i++) {
          const oneData = showData[i];
          const moreInfo = JSON.parse(oneData.defaults);
          let tmpData = {
            id: oneData.rxid,
            orderName: oneData.name,
            subcat: oneData.subcat,
            comment: "",
            cat: oneData.catid,
            data: oneData,
            frequency: moreInfo.frequency
              ? moreInfo.frequency
              : moreInfo.Frequency,
            therapeuticClass: "",
            dose: "",
            isFreq: oneData.isFreq,
            isFav: oneData.isFav,
          } as TOrderListDataRows;

          tmpData["therapeuticClass"] = oneData.thera_class;
          tmpData["dose"] = moreInfo?.Dose;
          tmpData["route"] = moreInfo?.Route;

          data.rows.push(tmpData);
        }
      }
      if (data.rows.length < 1) {
        data = undefined;
      }
    }
  }
  return data;
};

/**
 * Parse raw data from myorders/getData to DataGrid data.
 * Raw data format:
 *   ['rxid', 'brand_name', 'name', 'order_display_name', 'thera_class', 'suffix', 'category', 'subcat', 'defaults', 'catid', 'subtype', 'synonyms', 'formulary', 'source', 'staffid', 'count', 'id']
 */
export const parseMyOrderListData = (
  myOrderListData: TmyOrderListData,
  args: TArgs
) => {
  let data = undefined as TOrderListData | undefined;

  const rawHeader = myOrderListData?.frequentOrders?.header ?? undefined;
  const rawData = myOrderListData?.frequentOrders?.data ?? undefined;
  const filterStr = args?.searchstr?.length > 2 ? args.searchstr : "";

  if (rawData && rawData.length > 0) {
    data = {
      columns: [],
      rows: [],
    };
    data.columns = getColumnsByCategory(args);

    const showData = filterStr
      ? rawData.filter((d: string) => {
          return d[2].includes(filterStr);
        })
      : rawData;
    if (showData) {
      for (let i = 0; i < showData?.length; i++) {
        const oneData = showData[i];
        let tmpData = {
          id: oneData[0],
          orderName: oneData[2],
          subcat: oneData[7],
          frequency: oneData[8]?.Frequency,
          comment: "",
          cat: oneData[6],
          data: oneData,
          dataHeader: rawHeader,
          therapeuticClass: "",
          dose: "",
        } as TOrderListDataRows;

        const moreInfo = JSON.parse(oneData[8]);
        tmpData["therapeuticClass"] = oneData[3];
        tmpData["dose"] = moreInfo?.Dose;
        tmpData["route"] = moreInfo?.Route;

        data.rows.push(tmpData);
      }
    }
    if (data.rows.length < 1) {
      data = undefined;
    }
  }

  return data;
};

export const getOrderData = (
  allDataRef: any,
  setId: string,
  orderId: string
) => {
  // Check if we have master-specific order data
  if (allDataRef.current?.["masterOrders"]?.[setId]?.[orderId]) {
    return allDataRef.current["masterOrders"][setId][orderId];
  }

  // Fallback to standard orders
  return allDataRef.current["orders"][orderId];
};
