// @ts-nocheck
import React, {
  useCallback,
  useContext,
  useMemo,
  useState,
  useEffect,
} from "react";
import OsmInstructionPanel from "./OsmInstructionPanel";
import OsmWeblinkPanel from "./OsmWeblinkPanel";
import { TGroup, useOsmContext } from "../contexts/OsmContext";
import { OsmAction } from "../OsmConstants";
import { Divider, Stack } from "@mui/material";
import { DataGridPro, GRID_REORDER_COL_DEF } from "@mui/x-data-grid-pro";
import OsmSetDataRenderer from "../common/OsmSetDataRenderer";
import useForm from "@oe-src/entry/hooks/useForm";
import { OEContext, transformOrderData } from "@oe-src/context/OEContext";
import OERecord from "@oe-src/model/RecordModel";
import { postProcessOrder } from "@oe-entry/components/OrderSetsPanel/OrderSets.store";
import { getOrderInfo } from "@oe-entry/util/PayloadSetup";
import { cloneDeep } from "lodash";
import usePayload from "@oe-src/entry/components/OrderDetailsWindow/usePayload";
import OrderDetailsWindow from "./OrderDetailsWindow";
import { getOrderData } from "../util/OsmOrderListHelper";
import OsmAccordion from "@cci-monorepo/CAT/osm/common/OsmAccordion";
import { useAtom } from "jotai";
import { osmOrderSetExpandedAtom } from "../contexts/OsmAtom";
type OsmSetDataPanelProps = {
  set: {
    id: string;
    groupIds: {
      group_id: string;
      selections?: object;
      isRequired?: string;
      isSelected: string;
      selectionType: string;
    }[];
    weblinks: string[];
    instruction: string;
    status?: string;
  };
  inVersionHistory: boolean;
  data: any;
  disabled: boolean;
  isNew?: boolean;
  status?: string;
};

const OsmSetDataPanel = (props: OsmSetDataPanelProps) => {
  const { set, inVersionHistory, disabled } = props;
  const { osmDispatch, allDataRef } = useOsmContext();
  const {
    // @ts-ignore
    choices,
    // @ts-ignore
    cpoeChoices,
    // @ts-ignore
    masterCfgData,
    // @ts-ignore
    roundingLogic,
    // @ts-ignore
    patientWeights,
    // @ts-ignore
    dosingUnitMapping,
  } = useContext(OEContext);

  // Destructure only the values we need
  const [
    windowOpen,
    setWindowOpen,
    dirty,
    setDirty,
    formId,
    form,
    formData,
    openOrderForm,
    ,
    ,
    ,
    ,
    setFormId,
  ] = useForm();

  const [editingOrderId, setEditingOrderId] = useState("");
  const [editedOrders, setEditedOrders] = useState([]);

  // Add order edit handler
  const handleOrderEdit = useCallback(
    async (order: any) => {
      const orderPostProcessing = postProcessOrder(order);
      const orderClone = cloneDeep({
        ...orderPostProcessing,
        order_data: JSON.stringify(order),
      });
      const record = new OERecord(transformOrderData(orderClone));

      const orderState: any = await getOrderInfo(
        record,
        choices,
        cpoeChoices,
        masterCfgData,
        roundingLogic,
        patientWeights,
        dosingUnitMapping
      );
      const formNodeId = orderState.order_id;
      const openFormParams = {
        data: {
          payload: {
            ...orderState,
            ...orderClone,
            is_dirty: false,
            nodeId: formNodeId,
          },
        },
      };
      // @ts-ignore
      openOrderForm(openFormParams, formNodeId);
    },
    [
      choices,
      cpoeChoices,
      masterCfgData,
      roundingLogic,
      patientWeights,
      dosingUnitMapping,
      openOrderForm,
    ]
  );

  const [currentPayload, onChange] = usePayload(
    formData,
    null,
    setDirty,
    // @ts-ignore
    formId,
    null
  );
  currentPayload.ordersetOrder = true;

  const [osmOrderSetExpanded, setOsmOrderSetExpanded] = useAtom(
    osmOrderSetExpandedAtom
  );

  const handleOrderSetExpandChange = () => {
    setOsmOrderSetExpanded(!osmOrderSetExpanded);
  };
  const groupData = useMemo(() => {
    let groupData: any[] = [];
    if (
      set.groupIds &&
      Array.isArray(set.groupIds) &&
      allDataRef?.current?.groups
    ) {
      set.groupIds.forEach((gid) => {
        let aGroup = {} as any;
        if (allDataRef.current.groups[gid.group_id]) {
          aGroup["group"] = allDataRef.current.groups[gid.group_id];
          aGroup["group"]["selections"] = gid.selections ? gid.selections : {};
          groupData.push(aGroup);
        }
      });
    }
    return groupData;
    // eslint-disable-next-line
  }, [set, inVersionHistory]);

  const displayOrders = useMemo(() => {
    let displayOrders: string[] = [];

    function parseOrderIds(groudId: string, parentId: string = "") {
      if (allDataRef.current.groups[groudId]) {
        let group = allDataRef.current.groups[groudId];
        if (group.subIds && Array.isArray(group.subIds)) {
          group.subIds.forEach((subId) => {
            if (subId.orderid) {
              let parentGid = parentId ? parentId + "_" : "";
              displayOrders.push(parentGid + groudId + "_" + subId.orderid);
            } else if (subId.group_id) {
              parseOrderIds(subId.group_id, groudId);
            }
          });
        }
      }
    }

    if (
      set.groupIds &&
      Array.isArray(set.groupIds) &&
      allDataRef?.current?.groups
    ) {
      set.groupIds.forEach((gid) => {
        parseOrderIds(gid.group_id);
      });
    }
    return displayOrders;
    // eslint-disable-next-line
  }, [set, inVersionHistory]);

  useEffect(() => {
    osmDispatch({
      type: OsmAction.UpdateDisplayOrders,
      data: { displayOrders: displayOrders },
    });
  }, [displayOrders]);

  const removeGroup = (data: any) => {
    const newGroupIds = set.groupIds.filter(
      (set: any) => set.group_id !== data.id
    );
    osmDispatch({
      type: OsmAction.RemoveGroups,
      data: { groupIds: newGroupIds },
    });
  };

  const dragRow = (params: any) => {
    const movingIdx = params.oldIndex;
    const overIdx = params.targetIndex;
    const rowNeedsToMove = movingIdx !== overIdx;
    if (rowNeedsToMove && !inVersionHistory) {
      const newGroupIdList = [...set.groupIds];
      const dragItemContent = newGroupIdList[movingIdx];
      newGroupIdList.splice(movingIdx, 1);
      newGroupIdList.splice(overIdx, 0, dragItemContent);
      osmDispatch({
        type: OsmAction.AddGroups,
        data: { groupIds: newGroupIdList },
      });
    }
  };

  const handleFormCycle = (direction: string) => {
    if (!editingOrderId) return;
    let offset = direction === "forward" ? 1 : -1;
    let curIndex = displayOrders.indexOf(editingOrderId),
      newIndex = curIndex + offset;
    if (newIndex < 0) {
      newIndex = displayOrders.length - 1;
    } else if (newIndex === displayOrders.length) {
      newIndex = 0;
    }
    let newOrderId = displayOrders[newIndex],
      oidIdx = newOrderId.indexOf("oid"),
      oid = newOrderId.substring(oidIdx);

    let orderModified: any = editedOrders.find(
      (item: any) => item.orderid === oid
    );

    if (orderModified) {
      const openFormParams = {
        data: {
          payload: {
            ...orderModified,
            is_dirty: false,
          },
        },
      };
      // @ts-ignore
      openOrderForm(openFormParams, oid);
    } else {
      const newOrder = getOrderData(allDataRef, set.id, oid);
      handleOrderEdit(newOrder);
    }
    setEditingOrderId(newOrderId);
  };

  const handleCloseOrderDetailsWin = (opened: boolean) => {
    // @ts-ignore
    setWindowOpen(opened);

    if (opened === false) {
      setEditingOrderId("");
      // @ts-ignore
      setFormId(null);
    }
  };

  const columns = React.useMemo(
    () => [
      {
        ...GRID_REORDER_COL_DEF,
        minWidth: 40,
        maxWidth: 40,
      },
      {
        field: "group",
        minWidth: 100,
        flex: 1,
        headerName: "",
        sortable: false,
        renderCell: (params: any) => {
          return <OsmSetDataRenderer {...params} />;
        },
        cellRendererParams: {
          props,
          removeGroup,
          editingOrderId,
          setEditingOrderId,
          handleOrderEdit,
        },
      },
    ],
    // eslint-disable-next-line
    [set, inVersionHistory, editingOrderId]
  );

  return (
    <Stack
      sx={{
        gap: 1,
      }}
    >
      {set.weblinks && set.weblinks.length > 0 && (
        <>
          <OsmWeblinkPanel
            {...props}
            set={set}
            weblinks={set.weblinks}
            inVersionHistory={props.inVersionHistory}
          />
          <Divider
            sx={{
              height: "1px",
              color: "#B9B9B9",
              m: "0 4px ",
            }}
          />
        </>
      )}
      <OsmInstructionPanel
        {...props}
        isTop={true}
        setOrGroupStatus={
          !set.status || props.isNew ? props.status : set.status
        }
        inVersionHistory={props.inVersionHistory}
        data={set}
      />
      <OsmAccordion
        title="Order Set"
        expanded={osmOrderSetExpanded}
        onChange={handleOrderSetExpandChange}
      >
        <DataGridPro
          sx={{
            "&.MuiDataGrid-root": {
              border: "transparent",
            },
            "& .MuiDataGrid-row": {
              "&:hover, &.Mui-hovered": {
                backgroundColor: "transparent",
              },
            },
            "& .MuiDataGrid-cell": {
              borderBottom: 0,
              padding: "2px 0",
              "& .MuiDataGrid-rowReorderCell--draggable": {
                zIndex: 1000,
                color: "#6d6d6d",
                alignSelf: "flex-start",
                padding: "16px 0",
              },
              "&:focus-within": {
                outline: "none",
              },
            },
            "& .MuiDataGrid-columnHeaders": {
              borderColor: "transparent",
            },
          }}
          autoHeight
          columns={columns}
          rows={groupData}
          getRowId={(row) => row["group"]["id"]}
          getRowHeight={() => "auto"}
          isRowSelectable={() => false}
          columnHeaderHeight={0}
          disableColumnResize={true}
          disableColumnMenu
          hideFooter
          rowReordering={!inVersionHistory && disabled === false}
          onRowOrderChange={dragRow}
        />
        {windowOpen && (
          <OrderDetailsWindow
            orderSetId={set.id}
            windowOpen={windowOpen}
            setWindowOpen={handleCloseOrderDetailsWin}
            form={form}
            formData={formData}
            currentPayload={currentPayload}
            onChange={onChange}
            dirty={dirty}
            setDirty={setDirty}
            handleFormCycle={handleFormCycle}
            editedOrders={editedOrders}
            setEditedOrders={setEditedOrders}
          />
        )}
      </OsmAccordion>
    </Stack>
  );
};

export default OsmSetDataPanel;
