import React from "react";
import { Paper } from "@mui/material";
import OsmEmptyDataMsgBox from "../common/OsmEmptyDataMsgBox";
import { OsmTopGroupDataPanel } from "./OsmGroupDataPanel";
import OsmSetDataPanel from "./OsmSetDataPanel";

const OsmSetDetailPanel = (props: any) => {
  const { data, inVersionHistory } = props;
  const emptyMsg = [
    "Begin typing to search for an Order Group",
    "or click Browse.",
    " ",
    "(Add an Order Group to save)",
  ];
  return (
    <>
      {data ? (
        <OsmSetDataPanel
          {...props}
          set={data}
          inVersionHistory={inVersionHistory}
        />
      ) : (
        <OsmEmptyDataMsgBox messages={emptyMsg} />
      )}
    </>
  );
};

const OsmGroupDetailPanel = (props: any) => {
  const { data, name, inVersionHistory, editError } = props;
  const emptyMsg = [
    "Begin typing to search for an order",
    "or click browse",
    " ",
  ];
  const msg = "(Add an order or order group to save)";

  return (
    <>
      {data && data.name && (!editError || data?.subIds?.length > 0) ? (
        <OsmTopGroupDataPanel
          {...props}
          group={data}
          name={name}
          inVersionHistory={inVersionHistory}
          emptyMsg={emptyMsg}
          additionalMsg={msg}
        />
      ) : (
        <></>
      )}
    </>
  );
};

const OsmOrderSetDetailPanel = (props: any) => {
  const { dataType } = props;

  return (
    <Paper
      sx={{
        overflow: "auto",
        height: "79vh",
        pr: "16px",
        boxShadow: "none",
        border: "none",
      }}
    >
      {dataType === "set" && <OsmSetDetailPanel {...props} />}
      {dataType === "group" && <OsmGroupDetailPanel {...props} />}
    </Paper>
  );
};

export default OsmOrderSetDetailPanel;
