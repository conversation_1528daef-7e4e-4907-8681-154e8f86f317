// @ts-nocheck
import React, { useEffect, useRef, useState } from "react";
import { <PERSON>, <PERSON>, Stack, TextField, Typography } from "@mui/material";
import OsmDataPanel from "./OsmDataPanel";
import { useOsmContext } from "../contexts/OsmContext";
import { OsmAction } from "../OsmConstants";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import {
  Draggable,
  DragDropContext,
  Droppable,
  OnDragEndResponder,
} from "react-beautiful-dnd";
import makeStyles from "@mui/styles/makeStyles";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import OsmAccordion from "@cci-monorepo/CAT/osm/common/OsmAccordion";
import { useAtom, useSetAtom, useAtomValue } from "jotai";
import {
  osmWeblinksExpandedAtom,
  osmWeblinkRemovedAtom,
} from "../contexts/OsmAtom";

const useStyles = makeStyles((theme) => ({
  draggingItem: {
    background: "rgb(235,235,235)",
  },
}));

/**
 * Validate the given url string.
 * @param {string} url
 * @returns {boolean} true if it is valid url, otherwise false
 */
const validateUrl = (url: string) => {
  if (!url || url.length < 1) {
    return false;
  }
  const pattern = new RegExp(
    "^(https|http)://" + // protocol
      "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
      "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR IP (v4) address
      "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
      "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
      "(\\#[-a-z\\d_]*)?$", // fragment locator
    "i"
  );
  return pattern.test(url);
};

type TWeblinkEditPanelProps = {
  weblink: any;
  updateEdit: any;
  setEditMode: (key: boolean) => void;
  updateWeblink: any;
  onRemove?: (data: any) => void;
  inVersionHistory: boolean;
  numInEdit: number;
  setNumInEdit: any;
  numWeblinks: number;
  pos: number;
  disabled?: boolean;
};

/**
 * Edit one weblink data
 */
const WeblinkEditPanel = (props: TWeblinkEditPanelProps) => {
  const {
    weblink,
    setEditMode,
    updateWeblink,
    onRemove,
    inVersionHistory,
    numWeblinks,
    numInEdit,
    setNumInEdit,
    pos,
    disabled,
  } = props;
  const [editLink, setEditLink] = useState(weblink);
  const linkRef0 = useRef({ ...weblink });
  const linkRef = useRef(weblink);

  const labelSx = {
    fontSize: "15px",
    fontWeight: 600,
    margin: "4px",
    whiteSpace: "nowrap",
    display: "flex",
    justifyContent: "center",
    flexDirection: "column",
  };

  const labelSx_OnError = {
    fontSize: "15px",
    fontWeight: 600,
    margin: "10px 4px 4px 4px",
    whiteSpace: "nowrap",
  };

  const dragIndicatorIconSx = {
    marginTop: "2px",
    marginLeft: "4px",
    display: "flex",
    justifyContent: "center",
    flexDirection: "column",
  };
  const dragIndicatorIconSx_OnError = {
    marginTop: "2px",
    marginLeft: "4px",
    margin: "10px 4px 4px 4px",
  };

  const editTextFieldSx = {
    width: "560px",
    background: "#FFFFFF",
    "&::placeholder": {
      fontSize: 16,
      fontWeight: "bold",
    },
    "& .MuiOutlinedInput-root": {
      fontSize: 16,
      fontWeight: 400,
      padding: "2px",
    },
    "&.MuiTextField-root": {
      margin: "4px",
    },
    ".MuiOutlinedInput-input": {
      paddingTop: "2px",
      paddingBottom: "2px",
      paddingLeft: "8px",
    },
  };

  const handleClickAway = (event: MouseEvent | TouchEvent) => {
    setEditMode(false);
    const tmpNum = numInEdit - 1;
    setNumInEdit(tmpNum);
  };

  const handleLinkNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDisplay = event.target.value;
    const newLink = { ...editLink, display: newDisplay };
    setEditLink(newLink);
    linkRef.current.display = newDisplay;
  };

  const handleLinkUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = event.target.value;
    const newLink = { ...editLink, url: newUrl };
    setEditLink(newLink);
    linkRef.current.url = newUrl;
  };

  useEffect(() => {
    return () => {
      if (
        !linkRef.current.isNew &&
        (linkRef.current.display?.length < 1 ||
          !validateUrl(linkRef.current.url))
      ) {
        updateWeblink(linkRef0.current, pos); // eslint-disable-line
      } else {
        updateWeblink(linkRef.current, pos); // eslint-disable-line
      }
    };
  }, []); // eslint-disable-line

  return (
    <ClickAwayListener mouseEvent="onMouseDown" onClickAway={handleClickAway}>
      <div>
        <Box>
          <OsmDataPanel
            removable={!inVersionHistory && disabled === false}
            onRemove={onRemove}
            removeData={props.pos}
          >
            <>
              {editLink?.display?.length < 1 || !validateUrl(editLink?.url) ? (
                <Typography sx={labelSx_OnError}>Display Text</Typography>
              ) : (
                <Typography sx={labelSx}>Display Text</Typography>
              )}
            </>

            <TextField
              sx={editTextFieldSx}
              autoFocus={true}
              placeholder={"Enter Display Text"}
              value={editLink?.display}
              onChange={handleLinkNameChange}
              error={editLink?.display?.length < 1 ? true : false}
              helperText={
                editLink?.display?.length < 1 ? "Please enter weblink name" : ""
              }
            />
            {editLink?.display?.length < 1 || !validateUrl(editLink?.url) ? (
              <Typography sx={labelSx_OnError}>Weblink</Typography>
            ) : (
              <Typography sx={labelSx}>Weblink</Typography>
            )}
            <TextField
              sx={editTextFieldSx}
              placeholder={"Paste Hyperlink"}
              value={editLink?.url}
              onChange={handleLinkUrlChange}
              error={validateUrl(editLink?.url) ? false : true}
              helperText={validateUrl(editLink?.url) ? "" : "Not a valid URL"}
            />
          </OsmDataPanel>
        </Box>
      </div>
    </ClickAwayListener>
  );
};

type TWeblinkViewPanelProps = {
  weblink: any;
  setEditMode: (key: boolean) => void;
  onRemove?: (data: any) => void;
  inVersionHistory: boolean;
  numInEdit: number;
  setNumInEdit: any;
  numWeblinks: number;
  disabled?: boolean;
  pos: number;
  updateWeblink: any;
};

/**
 * Display one information
 */
const WeblinkViewPanel = (props: TWeblinkViewPanelProps) => {
  const {
    weblink,
    setEditMode,
    onRemove,
    inVersionHistory,
    numInEdit,
    setNumInEdit,
    numWeblinks,
    disabled,
    pos,
  } = props;

  const handleClick = (event: React.SyntheticEvent) => {
    if (!inVersionHistory && disabled === false) {
      setEditMode && setEditMode(true);
      const tmpNum = numInEdit + 1;
      setNumInEdit(tmpNum);
    }
  };

  const setRealLink = (url: string) => {
    let realUrl = url;
    if (realUrl.startsWith("/")) {
      realUrl = "https://" + window.location.host + url;
    } else if (!realUrl.startsWith("http")) {
      realUrl = "https://" + url;
    }

    return realUrl;
  };
  return (
    <Box>
      <OsmDataPanel
        onClick={handleClick}
        removable={!inVersionHistory && disabled === false}
        onRemove={onRemove}
        removeData={props.pos}
      >
        {numWeblinks > 1 && (
          <Box
            sx={{
              marginTop: "2px",
              marginLeft: "4px",
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            <DragIndicatorIcon
              sx={{ color: "rgba(109,109,109,1)" }}
              fontSize="medium"
            />
          </Box>
        )}
        <Link
          href={setRealLink(weblink.url)}
          target="_blank"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <Typography
            sx={{
              fontSize: 16,
              fontWeight: 400,
              ml: 2,
              height: 30,
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            {weblink.display}
          </Typography>
        </Link>
      </OsmDataPanel>
    </Box>
  );
};

type TOneWeblinkPanelProps = {
  weblink: {
    display: string;
    url: string;
    isNew: boolean;
  };
  updateWeblink: any;
  onRemove: any;
  inVersionHistory: boolean;
  numWeblinks: number;
  numInEdit: number;
  setNumInEdit: any;
  pos: number;
  disabled?: boolean;
};

/**
 * For one Weblink.
 * It can have 2 modes: view-mode and edit-mode
 * @param {object} weblink
 *  - display: string
 *  - url: string
 */
const OneWeblinkPanel = (props: TOneWeblinkPanelProps) => {
  const {
    weblink,
    updateWeblink,
    onRemove,
    inVersionHistory,
    numWeblinks,
    numInEdit,
    setNumInEdit,
    pos,
    disabled,
  } = props;
  const [editMode, setEditMode] = useState(false);
  const [osmWeblinkRemoved, setOsmWeblinkRemoved] = useAtom(
    osmWeblinkRemovedAtom
  );

  // if is new link, set it to editMode
  useEffect(() => {
    if (weblink.isNew) {
      setEditMode(true);
    }
  }, []); // eslint-disable-line

  useEffect(() => {
    if (osmWeblinkRemoved === pos) {
      setEditMode(false);
      setOsmWeblinkRemoved(-1);
    }
  }, [osmWeblinkRemoved]); // eslint-disable-line

  return (
    <>
      {editMode && disabled === false ? (
        <WeblinkEditPanel
          {...props}
          weblink={weblink}
          updateWeblink={updateWeblink}
          updateEdit={updateWeblink}
          setEditMode={setEditMode}
          onRemove={onRemove}
          inVersionHistory={inVersionHistory}
          numWeblinks={numWeblinks}
          numInEdit={numInEdit}
          setNumInEdit={setNumInEdit}
          pos={pos}
        />
      ) : (
        <WeblinkViewPanel
          {...props}
          weblink={weblink}
          updateWeblink={updateWeblink}
          setEditMode={setEditMode}
          onRemove={onRemove}
          inVersionHistory={inVersionHistory}
          numWeblinks={numWeblinks}
          numInEdit={numInEdit}
          setNumInEdit={setNumInEdit}
          pos={pos}
        />
      )}
    </>
  );
};

type TOsmWeblinkPanelProps = {
  set: any;
  weblinks: any;
  inVersionHistory: boolean;
  data: any;
};

/**
 * Weblink Panel for Order Set Management.
 * It is dedicated for web link in OrderSet panel.
 * Each OrderSet, can have multiple {OsmWeblinkPanel}, but each panel only one line.
 * It appears above instruction panel.
 */
const OsmWeblinkPanel = (props: TOsmWeblinkPanelProps) => {
  const { set, weblinks, inVersionHistory } = props;
  const [numInEdit, setNumInEdit] = useState(0);
  const { osmDispatch } = useOsmContext();
  const [osmWeblinksExpanded, setOsmWeblinksExpanded] = useAtom(
    osmWeblinksExpandedAtom
  );
  const [osmWeblinkRemoved, setOsmWeblinkRemoved] = useAtom(
    osmWeblinkRemovedAtom
  );
  const removeIndexRef = React.useRef(-1);

  const onDragEnd = ({ destination, source }) => {
    if (!destination) return;
    const items = Array.from(weblinks);
    const [reorderedItem] = items.splice(source.index, 1);
    items.splice(destination.index, 0, reorderedItem);
    osmDispatch({
      type: OsmAction.SortWeblink,
      data: { ...props.data, weblinks: items },
    });
  };

  const handleWeblinkExpandChange = () => {
    setOsmWeblinksExpanded(!osmWeblinksExpanded);
  };

  const removeWeblink = (linkIndex: any) => {
    let currLinks = [...weblinks];
    if (linkIndex < 0 || linkIndex >= currLinks.length) return;
    currLinks.splice(linkIndex, 1);
    removeIndexRef.current = linkIndex;
    setOsmWeblinkRemoved(linkIndex);
    osmDispatch({
      type: OsmAction.RemoveWeblink,
      data: { ...props.data, weblinks: currLinks },
    });
  };

  const updateWeblink = (newLink: any, pos: any) => {
    if (removeIndexRef.current === pos) {
      removeIndexRef.current = -1;
      return;
    }
    if (!weblinks[pos]) return;

    let isValid = true;
    if (!newLink.display) {
      isValid = false;
    } else {
      isValid = validateUrl(newLink.url);
    }

    if (isValid) {
      let newLinks = [...weblinks];
      let tmpLink = { ...newLink };
      delete tmpLink["isNew"];
      newLinks[pos] = tmpLink;
      osmDispatch({
        type: OsmAction.UpdateWeblink,
        data: {
          ...props.data,
          weblinks: newLinks,
          name: set.name,
          orderSetName: set.orderSetName,
        },
      });
    } else {
      if (newLink.isNew) {
        removeWeblink(pos);
      }
    }
  };
  const classes = useStyles();
  return (
    <OsmAccordion
      title="Order Set Weblinks:"
      expanded={osmWeblinksExpanded}
      onChange={handleWeblinkExpandChange}
    >
      <Stack
        data-testid="osm-weblinks-panel-id"
        sx={{
          pl: 0,
          pr: 4,
          background: "#FFFFFF",
          gap: 1,
        }}
      >
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="droppable-list">
            {(provided: any) => (
              <div ref={provided.innerRef} {...provided.droppableProps}>
                {weblinks &&
                  weblinks.map((link: any, index: any) => {
                    return (
                      <>
                        <Draggable
                          draggableId={"Item" + "_" + index}
                          index={index}
                        >
                          {(draggableProvided: any, snapshot: any) => (
                            <div
                              ref={draggableProvided.innerRef}
                              {...draggableProvided.draggableProps}
                              {...draggableProvided.dragHandleProps}
                              className={
                                snapshot.isDragging ? classes.draggingItem : ""
                              }
                            >
                              <OneWeblinkPanel
                                {...props}
                                key={index}
                                pos={index}
                                weblink={link}
                                numWeblinks={weblinks.length}
                                onRemove={removeWeblink}
                                updateWeblink={updateWeblink}
                                inVersionHistory={inVersionHistory}
                                numInEdit={numInEdit}
                                setNumInEdit={setNumInEdit}
                              />
                            </div>
                          )}
                        </Draggable>
                      </>
                    );
                  })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </Stack>
    </OsmAccordion>
  );
};

export default OsmWeblinkPanel;
