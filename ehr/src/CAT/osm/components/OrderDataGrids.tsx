import React, { useState } from "react";
import Box from "@mui/material/Box";
import { cloneDeep } from "lodash";
import { NameCellRenderer } from "./NameCellRender";
import { DataGridPro as DataGrid, GridRowParams } from "@mui/x-data-grid-pro";
import TooltipIconButton, {
  BUTTON_TYPE,
} from "@oe-src/common/TooltipIconButton";
import BlueArrowDown from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import BlueArrowRight from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Right.svg";
import sortDescIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Up_Small.svg";
import sortAscIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down_Small.svg";
import { useOsmContext } from "../contexts/OsmContext";
import { Order } from "./BrowseOrdersPanel";
import { OsmAction } from "../OsmConstants";

export const OrderDataGrids = (props: any) => {
  const { collapsed, currentData, handleClose, searchVal, freqFavOrders } =
    props;
  const [orderCollapsed, setOrderCollapsed] = useState(false);
  const { osmDispatch, allDataRef } = useOsmContext();
  const sx = {
    datagrid: {
      "&.MuiDataGrid-root": {
        borderRadius: 0,
        "& .MuiDataGrid-cell:focus, & .MuiDataGrid-columnHeader:focus, & .MuiDataGrid-columnHeaderDraggableContainer":
          {
            outline: "none",
          },
        border: "none",
      },
      "& .MuiDataGrid-columnHeader": {
        outline: "none !important",
        "&:hover": {
          "& .MuiDataGrid-iconButtonContainer": {
            visibility: "visible",
          },
        },
      },
      "& .MuiDataGrid-columnHeaders": {
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        top: 32,
        position: "sticky",
        zIndex: 2,
        background: "#D8DCE3",
        "& .MuiDataGrid-columnHeaderTitleContainer": {
          "& .MuiDataGrid-columnHeaderTitle": {
            fontWeight: 700,
            fontSize: 15,
          },
          justifyContent: "space-between",
        },
      },
      "& .MuiDataGrid-main": {
        overflow: "visible",
      },
      "& .MuiDataGrid-cell": {
        fontWeight: 400,
        fontSize: 15,
      },
    },
    header: {
      background: "#E8EFF7 !important",
      position: "sticky",
      display: "flex",
      top: "0px",
      zIndex: 10,
      color: "#396EA7 !important",
      fontWeight: 700,
      fontSize: "16px",
      "& .quick-search-orders-collapsed": {
        height: 13,
        width: 12,
      },
      "& .quick-search-orders-expanded": {
        height: 8,
        width: 12,
      },
      "& p": {
        lineHeight: "25px",
      },
    },
  };

  const getColDefs = () => {
    const headers = [
      "orderName",
      "therapeuticClass",
      "cat",
      "dose",
      "route",
      "frequency",
      "comment",
      "isFreq",
      "isFav",
    ];

    return headers.map((header) => {
      const def: any = {
        field: header,
        sortable: true,
      };
      switch (header) {
        case "isFreq":
        case "isFav":
          def.hide = true;
          break;
        case "orderName":
          def.renderCell = NameCellRenderer;
          def.minWidth = 518;
          def.headerName = "Name";
          def.searchVal = searchVal;
          break;
        case "therapeuticClass":
          def.headerName = "Therapeutic Class";
          break;
        case "cat":
          def.headerName = "Category";
          break;
        case "dose":
          def.headerName = "Dose";
          break;
        case "route":
          def.headerName = "Route";
          break;
        case "frequency":
          def.headerName = "Freq";
          break;
        case "comment":
          def.headerName = "Comment";
          def.flex = 1;
          break;
        default:
      }
      def.width = 200;
      return def;
    });
  };

  const addToGlobalOrders = (order: any) => {
    if (!order) return;

    if (!order.data) {
      allDataRef.current.orders[order.id] = { ...order, orderid: order.id };
    }

    const data = order.data;
    let newOrder: Order = {
      id: "",
      brand_name: "",
      name: "",
      orderNme: "",
      order_display_name: "",
    };
    if (order?.cat === "Medications") {
      newOrder = {
        id: data.id,
        brand_name: data.brand_name,
        name: data.name,
        orderNme: data.anem,
        order_display_name: data.order_display_name,
        thera_class: data.thera_class,
        suffix: data.suffix,
        category: data.category,
        subcat: data.subcat,
        defaults: data.defaults,
        catid: data.catid,
        order_type: data.order_type,
        subtype: data.subtype,
        formulary: data.formulary,
        source: data.source,
        staffid: data.staffid,
        // count: data[15],
        rxid: data?.rxid ?? data.id,
      };
    } else {
      newOrder = {
        id: data.id,
        brand_name: data.brand_name,
        name: data.name,
        orderNme: data.name,
        order_display_name: data.order_display_name,
        category: data.category,
        subcat: data.subcat,
        defaults: data.defaults,
        order_type: data.order_type,
        subtype: data.subtype,
        formulary: data.formulary,
        source: data.source,
        catid: data.catid,
      };
    }
    allDataRef.current.orders[order.id] = { ...newOrder, orderid: newOrder.id };
  };

  const onRowClicked = (params: GridRowParams) => {
    let newSubIds: any[] = currentData?.subIds
      ? cloneDeep(currentData.subIds)
      : [];
    if (!newSubIds.some((item: any) => item.orderid === params.row.id)) {
      newSubIds.push({
        orderid: params.row.id,
        selected: 0,
      });
      if (allDataRef?.current.orders) {
        addToGlobalOrders(params.row);
      }
    }

    osmDispatch({
      type: OsmAction.AddOrders,
      data: { subIds: newSubIds },
    });
    handleClose && handleClose();
  };
  // eslint-disable-next-line react-hooks/exhaustive-deps

  const colDefs = React.useMemo(
    () => (getColDefs() ?? []).filter((col) => !col.hide),
    [getColDefs()]
  );

  return (
    <Box
      data-testid={"quick-search-order-data-grids-id"}
      className={`quick-search-order-data-grids`}
      sx={{
        background: "white",
        display: collapsed ? "none" : "flex",
        flexDirection: "column",
        overflow: "auto",
      }}
    >
      <Box
        key={"My Orders"}
        sx={{
          display: freqFavOrders && freqFavOrders.length > 0 ? "block" : "none",
        }}
      >
        <Box sx={sx.header}>
          <TooltipIconButton
            buttonType={BUTTON_TYPE.TRANSPARENT}
            onClick={() => setOrderCollapsed(!orderCollapsed)}
            tooltip={""}
          >
            <img
              className={
                orderCollapsed
                  ? "quick-search-orders-collapsed"
                  : "quick-search-orders-expanded"
              }
              src={orderCollapsed ? BlueArrowRight : BlueArrowDown}
              alt="arrow"
            />
          </TooltipIconButton>
          <p>{"My Orders"}</p>
        </Box>
        <Box
          sx={{
            background: "#D8DCE3",
            display: orderCollapsed ? "none" : "block",
          }}
        >
          <DataGrid
            hideFooter={true}
            disableRowSelectionOnClick={true}
            onRowClick={onRowClicked}
            autoHeight={true}
            autoPageSize={true}
            sortingOrder={["asc", "desc"]}
            rows={freqFavOrders}
            getRowId={(row) => row.id}
            slots={{
              columnSortedAscendingIcon: () => (
                <img src={sortAscIcon} alt="ascending" />
              ),
              columnSortedDescendingIcon: () => (
                <img src={sortDescIcon} alt="descending" />
              ),
              columnUnsortedIcon: () => (
                <img src={sortAscIcon} alt="unsorted" />
              ),
            }}
            rowHeight={24}
            disableColumnMenu
            disableColumnReorder
            columns={colDefs}
            columnHeaderHeight={32}
          />
        </Box>
      </Box>
    </Box>
  );
};
