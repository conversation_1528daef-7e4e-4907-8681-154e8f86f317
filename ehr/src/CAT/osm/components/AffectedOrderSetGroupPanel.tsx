import React from "react";
import {
  Box,
  Divider,
  List,
  ListItem,
  Stack,
  Toolbar,
  Typography,
} from "@mui/material";
import { OsmCciButton } from "../common/OsmButtons";
import { useOsmContext } from "../contexts/OsmContext";

type TAffectedSetsValue = {
  name: string;
  groupIds: string[];
};

type AffectedOrderSetGroupPanelProps = {
  data: {
    id: string;
    name?: string;
    [key: string]: any;
  };
  onClose: () => void;
};

/**
 * Affected Groups and Sets Dialog
 * Displays affected order sets and groups when saving order group
 * @param {Object} props required parameters
 * @param {Object} props.data - Order Set/Group data
 * @param { function() : void } props.onClose - Callback function to close modal
 */
const AffectedOrderSetGroupPanel = ({
  data,
  onClose,
}: AffectedOrderSetGroupPanelProps) => {
  const {
    allDataRef: { current },
  } = useOsmContext();
  const { sets, groups } = current;

  const affectedSets = Object.entries(sets)
    .map(([key, value]) => {
      const { name, groupIds } = value as TAffectedSetsValue;
      const setAffected =
        groupIds.filter((group: any) => group.group_id === data.id).length > 0;
      return setAffected ? { key, name } : null;
    })
    .filter((sets) => !!sets);

  const affectedGroups = Object.entries(groups)
    .map(([key, value]) => {
      const { subIds, orderGroupName } = value;
      const groupAffected =
        subIds.filter((subGroups: any) => subGroups.group_id === data.id)
          .length > 0;
      return groupAffected ? { key, orderGroupName } : null;
    })
    .filter((groups) => !!groups);

  const handleClose = () => {
    onClose();
  };

  return (
    <Stack
      data-testid={"osm-affected-set-group-dialog-id"}
      sx={{ width: 600, maxHeight: 670 }}
    >
      <Typography variant="body1">
        Publishing changes to this order group will modify the following Order
        Sets and Order Groups
      </Typography>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          paddingBottom: "16px",
          overflowY: "scroll",
        }}
      >
        <Typography
          variant="body1"
          style={{ fontWeight: "bold", paddingTop: "16px" }}
        >{`(${affectedSets.length}) Order Sets`}</Typography>
        <List
          sx={{ listStyleType: "disc", pl: 4 }}
          disablePadding={true}
          dense={true}
        >
          {/*// @ts-ignore*/}
          {affectedSets.map(({ key, name }) => (
            <ListItem
              sx={{ display: "list-item" }}
              disablePadding={true}
              key={key}
            >
              <Typography variant="body1">{name}</Typography>
            </ListItem>
          ))}
        </List>
        <Typography
          variant="body1"
          style={{ fontWeight: "bold", paddingTop: "16px" }}
        >{`(${affectedGroups.length}) Order Groups`}</Typography>
        <List
          sx={{ listStyleType: "disc", pl: 4 }}
          disablePadding={true}
          dense={true}
        >
          {affectedGroups &&
            affectedGroups.map((affectedGroupsItem) => {
              if (affectedGroupsItem === null) {
                return null;
              }
              const { key, orderGroupName } = affectedGroupsItem;

              return (
                <ListItem
                  sx={{ display: "list-item" }}
                  disablePadding={true}
                  key={key}
                >
                  <Typography variant="body1">{orderGroupName}</Typography>
                </ListItem>
              );
            })}
        </List>
      </Box>
      <Divider
        sx={{
          height: "24px",
          borderColor: "#FFFFFF",
          borderBottomWidth: "3px",
        }}
      />
      <Toolbar sx={{ justifyContent: "flex-end" }}>
        {/*// @ts-ignore */}
        <OsmCciButton buttonName={"Close"} onClick={handleClose} />
      </Toolbar>
    </Stack>
  );
};

export default AffectedOrderSetGroupPanel;
