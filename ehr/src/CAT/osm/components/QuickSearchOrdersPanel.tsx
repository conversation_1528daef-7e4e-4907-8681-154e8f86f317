import React, { useState } from "react";
import Popper from "@mui/material/Popper";
import { FiltersBar } from "./FiltersBar";
import { useOrderListByCategory } from "../hooks/useOrderListQuery";
import { parseMyOrderList } from "../util/OsmOrderListHelper";
import { OrdersPanel } from "./OrdersPanel";

export const QuickSearchOrdersPanel = (props: any) => {
  const { handleClose, searchVal, anchorEl } = props;
  const [medFilter, setMedFilter] = useState(false);
  const [nonMedFilter, setNonMedFilter] = useState(false);

  const ref = React.useRef(null);

  let filterType = "Both";
  if ((medFilter && nonMedFilter) || (!medFilter && !nonMedFilter)) {
    filterType = "Both";
  } else {
    filterType = medFilter ? "Medications" : "Non-Meds";
  }

  const orderQueryOptions = {
    cat: "ALL_ORDERS",
    subcat: "",
    browsechc: "",
  };

  const searchOrderData = useOrderListByCategory(
    orderQueryOptions,
    parseMyOrderList,
    { cat: "ALL_ORDERS" }
  );
  let freqFavOrders: any[] = searchOrderData?.data?.rows;
  if (filterType !== "Both") {
    if (medFilter) {
      freqFavOrders = freqFavOrders?.filter((ord: any) => ord.cat === "MED");
    } else {
      freqFavOrders = freqFavOrders.filter((ord: any) => ord.cat !== "MED");
    }
  }

  if (searchVal) {
    freqFavOrders = freqFavOrders?.filter((ord: any) =>
      ord.orderName?.toLowerCase().includes(searchVal.toLowerCase())
    );
  }

  const numOrders = searchOrderData?.data?.rows?.length;

  return (
    <Popper
      keepMounted
      anchorEl={anchorEl}
      open={props.open}
      placement="bottom-start"
      style={{
        zIndex: 2,
        boxShadow:
          "0px 4px 20px rgba(0, 0, 0, 0.25), 0px 0px 4px rgba(0, 0, 0, 0.2)",
      }}
      modifiers={[
        {
          name: "preventOverflow",
          options: {
            flipVariations: false,
          },
        },
      ]}
      ref={ref}
      tabIndex={-1}
    >
      <FiltersBar
        nonMedFilter={nonMedFilter}
        medFilter={medFilter}
        setMedFilter={setMedFilter}
        setNonMedFilter={setNonMedFilter}
      />
      <div
        style={{
          background: "white",
          padding: "8px 16px 16px 16px",
          width: "80vw",
          maxHeight: "68vh",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {numOrders > 0 && (
          <OrdersPanel
            freqFavOrders={freqFavOrders}
            currentData={props.currentData}
            handleClose={handleClose}
            searchVal={searchVal}
            openOrderForm={props.openOrderForm}
          />
        )}
      </div>
    </Popper>
  );
};
