import OEChip from "@oe-src/common/OEChip";

export const FiltersBar = (props: any) => {
  const { medFilter, setMedFilter, nonMedFilter, setNonMedFilter } = props;
  const onBtnClick = (filterType: any) => {
    if (filterType === "Medications") {
      setMedFilter(!medFilter);
    }
    if (filterType === "Non-Meds") {
      setNonMedFilter(!nonMedFilter);
    }
  };
  return (
    <div
      style={{
        padding: "8px 8px 8px 15px",
        display: "flex",
        width: "80vw",
        background: "white",
        alignItems: "center",
        columnGap: 8,
      }}
    >
      <div style={{ display: "flex", columnGap: 8 }}>
        <OEChip
          label="Medications"
          selected={medFilter}
          onClick={() => onBtnClick("Medications")}
          style={{ height: 32, margin: 0 }}
        />
        <OEChip
          label="Non-Meds"
          selected={nonMedFilter}
          onClick={() => onBtnClick("Non-Meds")}
          style={{ height: 32, margin: 0 }}
        />
      </div>
    </div>
  );
};
