// @ts-nocheck
import { useState } from "react";
import Stack from "@mui/material/Stack";
import { OsmCciButton, OsmWhiteButton } from "../common/OsmButtons";
import {
  useOrderListByCategory,
  useCategoryQuery,
} from "../hooks/useOrderListQuery";
import { parseOrderListData } from "../util/OsmOrderListHelper";
import { OsmAction, defaultAllOrderCategoryName } from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";

import OrderCategoryPanel, { initSelectedCategory } from "./OrderCategoryPanel";
import OsmOrderListPanel from "./OsmOrderListPanel";
import { cloneDeep } from "lodash";
export type TOrder = {
  id: string;
  name: string;
  category: string;
  subcategory: string;
  chc: string;
  orderid: string;
};
type TBrowseOrdersPanelProps = {
  onClose: any;
  currentData: any;
};
export type Order = {
  id: string;
  brand_name: string;
  name: string;
  orderNme: string;
  order_display_name: string;
  thera_class?: string;
  suffix?: string;
  category?: string;
  subcat?: string;
  defaults?: string;
  catid?: string;
  order_type?: string;
  subtype?: string;
  formulary?: string;
  source?: string;
  staffid?: string;
  rxid?: string;
};

type TCategotyType = {
  key: string;
  label: string;
  children: TCategotyType[];
  cat: string;
  subcat: string;
  chc: string;
  selectedOrderIds: string[];
  [key: string]: any;
};
/**
 * Help function to parse raw category data from server to tree data
 * Raw data format:
 *   ["CatID", "cat", "subcat", "chc", "role", "type", "mask", "sdname", "coredef", "nitref", "display", "icon"]
 * @param {Array} categoryData raw data from server
 * @returns array of category data with tree-structure
 */
const parseCategory = (categoryData: any) => {
  let categories = {} as TCategotyType;
  const rawdata = (categoryData.data ?? categoryData.categories)?.data;
  if (rawdata) {
    for (let i = 0; i < rawdata.length; i++) {
      const oneData = rawdata[i];

      if (!categories[oneData[1]]) {
        categories[oneData[1]] = {
          key: oneData[1],
          label: oneData[1],
          children: [],
          cat: oneData[1],
          subcat: "",
          chc: oneData[3],
          selectedOrderIds: [],
        } as TCategotyType;
      }

      const curCatCount = rawdata.filter(
        (item: any) => item.cat === oneData[1]
      ).length;
      if (!(oneData[1] === oneData[2] && curCatCount <= 1)) {
        categories[oneData[1]].children.push({
          key: oneData[1] + "_" + oneData[2],
          label: oneData[2],
          cat: oneData[1],
          subcat: oneData[2],
          chc: oneData[3],
          selectedOrderIds: [],
        });
      }
    }
  }

  // Adjust the category list, put Medication at first
  let catArray = [];
  Object.values(categories).forEach((category) => {
    if (category.key === "Medications") {
      catArray.unshift(category);
    } else {
      catArray.push(category);
    }
  });

  // Add All_ORDERS category at beginning
  catArray.unshift({
    key: defaultAllOrderCategoryName,
    label: "All Orders",
    cat: defaultAllOrderCategoryName,
    subcat: "",
    chc: "",
    selectedOrderIds: [],
  });

  return catArray;
};
/**
 * The container for browsing and selecting Orders.
 * @prop onClose func close the popup dialog
 * @prop currentData
 */
const BrowseOrdersPanel = ({
  onClose,
  currentData,
}: TBrowseOrdersPanelProps) => {
  const [addedOrders, setAddedOrders] = useState<TOrder[]>([]);
  const [selectedOrderIds, setSelectedOrderIds] = useState<Record<string, any>>(
    {}
  );
  const [selectedCategory, setSelectedCategory] =
    useState(initSelectedCategory);

  const { osmDispatch, allDataRef } = useOsmContext();
  const [orderToAdded, setOrderToAdded] = useState<any>({});
  /**
   * Convert Order data from Browse Orders to Order Detail data,
   * then add the Order to global Orders.
   * @param order Order data from Browse Orders
   */
  const addToGlobalOrders = (order: any) => {
    if (!order) return;

    if (!order.data) {
      allDataRef.current.orders[order.id] = { ...order, orderid: order.id };
      return;
    }

    const header = order.dataHeader;
    const data = order.data;

    let newOrder: object = {};
    data.forEach((d: any, i: number) => {
      newOrder[header[i]] = d;
    });

    if (!newOrder["rxid"]) {
      newOrder["rxid"] = newOrder["id"];
    }
    if (!newOrder["order_display_name"]) {
      newOrder["order_display_name"] = newOrder["name"];
    }

    allDataRef.current.orders[order.id] = {
      ...newOrder,
      orderid: newOrder["id"],
    };
  };

  const handleAdd = () => {
    // add new orderIds to currentData.subIds, and put new orders to allDataRef.current.orders
    let newSubIds = currentData?.subIds ? cloneDeep(currentData?.subIds) : [];
    addedOrders.forEach((order, index) => {
      // add to newOrerIds array if not exist
      if (!newSubIds.some((item: any) => item.orderid === order.id)) {
        newSubIds.push({
          orderid: order.id,
          selected: 0,
        });

        if (allDataRef?.current.orders) {
          addToGlobalOrders(order);
        }
      }
    });

    osmDispatch({
      type: OsmAction.AddOrders,
      data: { subIds: newSubIds },
    });
    onClose && onClose();
  };

  const handleCancelAll = () => {
    onClose && onClose();
  };
  const SetToAddedOrders = (orders: any[]) => {
    const addord: any = {};
    orders.forEach((conf: any) => {
      const key = `${conf.cat}:${conf.subcat}`;
      if (!addord.hasOwnProperty(key)) {
        addord[key] = [];
        addord[key].push(conf);
      } else {
        addord[key].push(conf);
      }
    });
    setOrderToAdded(addord);
  };

  const handleSelectOrders = (orders: any, orderIds: any) => {
    let tmps: any = [];

    // remove old Orders for current selected category/sub-category
    const currentOrderIds = selectedOrderIds[selectedCategory.key];
    if (currentOrderIds && currentOrderIds.length > 0) {
      addedOrders.forEach((order) => {
        const found = currentOrderIds.some((oneId: any) => order.id === oneId);
        if (!found) {
          tmps.push(order);
        }
      });
    } else {
      tmps = [...addedOrders];
    }

    // add newly orders
    orders.forEach((order: any) => {
      tmps.push(order);
    });

    setAddedOrders(tmps);
    SetToAddedOrders(tmps);
    // update selected ids array for category/sub-category
    const tmpSel = selectedOrderIds as {
      [key: string]: any;
    };

    // it is sub-category, so need to update its category list first
    if (selectedCategory.subcat) {
      let tmpIds: any = [];
      if (tmpSel[selectedCategory.key] && tmpSel[selectedCategory.cat]) {
        tmpSel[selectedCategory.cat].forEach((selId: any) => {
          const found2 = tmpSel[selectedCategory.key].some(
            (oneId: any) => selId === oneId
          );
          if (!found2) {
            tmpIds.push(selId);
          }
        });

        tmpSel[selectedCategory.cat] = [...tmpIds, ...orderIds];
      }
    }

    tmpSel[selectedCategory.key] = orderIds;
    setSelectedOrderIds(tmpSel);
  };

  const onCategorySelect = (category: any) => {
    setSelectedCategory(category);
  };

  const orderQueryOptions = {
    cat: selectedCategory.cat,
    subcat: selectedCategory.subcat,
    browsechc: selectedCategory.chc,
  };

  const searchOrderData = useOrderListByCategory(
    orderQueryOptions,
    parseOrderListData,
    selectedCategory
  );
  // Get category data from server
  const categoryData = useCategoryQuery({}, parseCategory);

  // @ts-ignore
  return (
    <Stack>
      <Stack
        direction="row"
        gap="8px"
        sx={{
          px: "8px",
          pt: "8px",
          background: "#F2F2F2",
          width: "1632px",
          maxHeight: "780px",
        }}
      >
        <OrderCategoryPanel
          onCategorySelect={onCategorySelect}
          categoryData={categoryData}
          ordersToAdd={orderToAdded}
        />
        <OsmOrderListPanel
          data={searchOrderData?.data}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          onOrderSelection={handleSelectOrders}
          selectedOrderIds={selectedOrderIds[selectedCategory.key]}
        />
      </Stack>
      <Stack
        gap="8px"
        direction="row"
        sx={{
          px: "8px",
          py: "12px",
          height: "56px",
          background: "#F2F2F2",
          justifyContent: "right",
        }}
      >
        <OsmWhiteButton buttonName={"Cancel All"} onClick={handleCancelAll} />
        <OsmCciButton
          buttonName={
            addedOrders.length > 0 ? "Add (" + addedOrders.length + ")" : "Add"
          }
          onClick={handleAdd}
          disabled={addedOrders.length > 0 ? false : true}
        />
      </Stack>
    </Stack>
  );
};

export default BrowseOrdersPanel;
