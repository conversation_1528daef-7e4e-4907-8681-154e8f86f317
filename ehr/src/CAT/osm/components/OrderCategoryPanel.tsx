import React from "react";
import { Box, Typography, Stack } from "@mui/material";
import { cloneDeep } from "lodash";
import OsmTreeView from "../common/OsmTreeView";
import { defaultAllOrderCategoryName } from "../OsmConstants";

export const initSelectedCategory = {
  key: defaultAllOrderCategoryName,
  cat: defaultAllOrderCategoryName,
  subcat: "",
  chc: "",
  selectedOrderIds: [],
  searchstr: "",
};
/**
 * Show Order category/sub-category as tree list
 * It is used in part of Browse Orders window.
 * @prop onCategorySelect function to handle selection of a category/sub-category.
 * @prop noAllOrders boolean optional, to hide "All Orders" box
 */
const OrderCategoryPanel = (props: any) => {
  const { onCategorySelect, categoryData, ordersToAdd } = props;
  const [catData, setCatData] = React.useState([]);
  /**
   * Get a category data based on category and sub-category
   * @param {array} categories all categories
   * @param {string} cat category name
   * @param {string} subcat sub-category name
   */
  const getOneCategoryData = (categories: any, cat: any, subcat: any) => {
    let oneData = null;
    if (categories) {
      categories.forEach((category: any) => {
        if (category?.cat === cat) {
          if (subcat && category.children) {
            category.children.forEach((subcategory: any) => {
              if (subcategory.subcat === subcat) {
                oneData = subcategory;
              }
            });
          } else {
            oneData = category;
          }
        }
      });
    }
    return oneData;
  };

  const handleCategoryClick = (event: any, value: any) => {
    if (value === defaultAllOrderCategoryName) {
      onCategorySelect({
        cat: defaultAllOrderCategoryName,
        subcat: "",
        chc: "",
      });
    } else {
      const [cat, subcat] = value.split("_");
      onCategorySelect(getOneCategoryData(categoryData?.data, cat, subcat));
    }
  };

  const updateCat = () => {
    let cateData = categoryData?.data ? cloneDeep(categoryData.data) : [];
    if (cateData && cateData.length > 0) {
      cateData.forEach((node: any) => {
        if (node.cat !== defaultAllOrderCategoryName) {
          const parent = node;
          parent.children.forEach((child: any) => {
            const key = `${child.cat}:${child.subcat}`;
            const childCount = ordersToAdd[key]?.length;
            const childLabel = child.subcat;
            if (childCount > 0) {
              child.label = ` ${childLabel} (${childCount})`;
            } else {
              child.label = `${childLabel}`;
            }
          });
        }
      });
    }
    setCatData(cateData);
  };
  React.useEffect(() => {
    updateCat();
  }, [categoryData, ordersToAdd]);

  return (
    <Stack
      sx={{
        background: "#FFFFFF",
        overflow: "auto",
        width: "200px",
        pl: 1,
      }}
    >
      <Box sx={{ height: "22px", background: "#5A7493" }}>
        <Typography
          sx={{
            px: "4px",
            color: "#FFFFFF",
            fontSize: 16,
            fontWeight: 700,
            height: "22px",
          }}
        >
          Categories
        </Typography>
      </Box>
      <OsmTreeView
        treeData={catData}
        onNodeSelect={handleCategoryClick}
        defaultSelected={defaultAllOrderCategoryName}
      />
    </Stack>
  );
};

export default OrderCategoryPanel;
