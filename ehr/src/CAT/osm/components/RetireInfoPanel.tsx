import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import QuestionIcon from "@cci-monorepo/oe-shared/src/assets/osm/icn_dialog_question.svg";
import { OsmCciButton, OsmWhiteButton } from "../common/OsmButtons";
import OsmSelect from "../common/OsmSelect";
import { OsmDialogAction } from "@cci-monorepo/CAT/osm/OsmOrderSetPanel";
import { CciRequiredFieldMarker as RequiredFieldMarker } from "@cci/mui-components";
import { useOsmContext } from "../contexts/OsmContext";
import AffectedDataPanel, { getAffectedData } from "./AffectedDataPanel";

const RetireInfoPanelWidth = 660;

const reasonOptions = [
  { value: 1, display: "Drug shortage" },
  { value: 2, display: "Update to clinical practice guidelines" },
  { value: 3, display: "Update to hospital protocol" },
  { value: 4, display: "Low usage" },
  { value: 5, display: "No longer applicable" },
];

type TokData = {
  retireReason: string;
  retireComment: string;
};

type TRetireInfoPanelProps = {
  retireType: string;
  retireName?: string;
  retireData?: any;
  onOk: (action: OsmDialogAction, data: TokData) => void;
  onClose: () => void;
};

/**
 * The Retire confirm dialog
 * @prop {string} retireType 'set' or 'group'
 * @prop {string} retireName the name of the Set/Group to be retired
 */
const RetireInfoPanel = (props: TRetireInfoPanelProps) => {
  const { retireType, retireName, onOk, onClose, retireData } = props;
  const [retireReasonSelect, setRetireReasonSelect] = useState<number>(0);
  const [retireComment, setRetireComment] = useState("");

  const {
    allDataRef: { current },
  } = useOsmContext();
  const allSets = current?.sets;
  const allGroups = current?.groups;

  let affectedData: any;
  if (retireType === "group" && retireData) {
    affectedData = getAffectedData(retireData, allSets, allGroups);
  }

  const handleCancel = () => {
    onClose && onClose();
  };

  const handleRetire = () => {
    const okData = {
      retireReason: reasonOptions[retireReasonSelect]?.display,
      retireComment: retireComment,
    };
    onOk && onOk(OsmDialogAction.retire, okData);
  };

  const handleReasonChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setRetireReasonSelect(Number(event.target.value));
  };

  const handleCommentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRetireComment(event.target.value);
  };

  return (
    <Stack
      data-testid={"osm-retire-dialog-id"}
      sx={{ width: RetireInfoPanelWidth }}
    >
      <Box sx={{ display: "flex" }}>
        <img src={QuestionIcon} alt="osm-retire-dialog-info-icon" />
        <Box sx={{ ml: 1 }}>
          <Typography variant="body1" style={{ marginLeft: 10, paddingTop: 3 }}>
            Are you sure you want to retire <b>{retireName}</b> order{" "}
            {retireType}?
          </Typography>
          {retireType === "group" && (
            <Typography variant="body1" sx={{ ml: 1.5, pt: 1.5 }}>
              {affectedData?.sets || affectedData?.groups ? (
                <span>
                  Retiring this Order Group will modify the following Order
                  Groups and Order Sets.
                </span>
              ) : (
                <span>
                  Retiring this Order Group will not affect any other Order
                  Groups or Other Sets.
                </span>
              )}
            </Typography>
          )}
        </Box>
      </Box>
      {(affectedData?.sets || affectedData?.groups) && (
        <AffectedDataPanel {...affectedData} />
      )}
      <Typography
        style={{ fontSize: "14px", fontWeight: "bold", marginTop: 16 }}
      >
        Reason for Retiring <RequiredFieldMarker />
      </Typography>
      <OsmSelect
        // @ts-ignore TS(2322): Type '{ value: number; name: string; onChange: (e:... Remove this comment to see the full error message
        value={retireReasonSelect}
        name="retireReason"
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
          handleReasonChange(e);
        }}
        options={reasonOptions}
      />
      <Typography
        style={{ fontSize: "14px", fontWeight: "bold", marginTop: 36 }}
      >
        Additional Comments
      </Typography>
      <TextField
        multiline={true}
        minRows={2}
        sx={{
          background: "#FFFFFF",
          "&::placeholder": {
            fontSize: 16,
            fontWeight: "bold",
          },
        }}
        onChange={handleCommentChange}
        placeholder={"Additional Comments"}
      />
      <Divider
        sx={{
          height: "24px",
          borderColor: "#FFFFFF",
          borderBottomWidth: "3px",
        }}
      />
      <Toolbar sx={{ justifyContent: "flex-end" }}>
        <OsmWhiteButton buttonName={"Cancel"} onClick={handleCancel} />
        <OsmCciButton
          buttonName={"Retire"}
          onClick={handleRetire}
          disabled={!retireReasonSelect}
          sx={{ ml: 2 }}
        />
      </Toolbar>
    </Stack>
  );
};

export default RetireInfoPanel;
