import React, { useEffect, useState } from "react";
import { Box, Stack, Typography } from "@mui/material";
import { cloneDeep } from "lodash";
import OsmSelect from "../common/OsmSelect";
import CatSwitch from "../../common/CatSwitch";
import { CatIconChip } from "../../common/CatChip";
import CheckedMark from "@cci-monorepo/oe-shared/src/assets/radio_only_checked.svg";
import { StyledLabel } from "./OsmDetailSettingPanel";
import { useGetEnvsQuery } from "../hooks/useOsmUtilQuery";
import {
  OrderSetLocation,
  OrderSetLocationDataFormatName,
  OsmAction,
} from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";
import { OsmToolType } from "../OsmConstants";

const defaultAvailability = {
  hospitalAndAmbulatory: {
    value: OrderSetLocation.HospitalAndAmbulatory,
    display: "Hospital and Ambulatory",
    campuses: [],
  },
  hospital: {
    value: OrderSetLocation.HospitalOnly,
    display: "Hospital Only",
    campuses: [],
  },
  ambulatory: {
    value: OrderSetLocation.AmbulatoryOnly,
    display: "Ambulatory Only",
    campuses: [],
  },
};

type LocationOption = {
  value: string;
  display: string;
  campuses: Campus[];
};
type Campus = {
  name: string;
  display: string;
  envs: Environment[];
};
type Environment = {
  name: string;
  display: string;
  disabled?: boolean;
};

type TOneAvailableCampusProps = {
  inVersionHistory: any;
  selectCampusEnvData: any;
  campus: Campus;
  handleEnvChipClick: any;
  handleSpecificCampusSwitch: any;
  disabled?: boolean;
};

const OneAvailableCampus = (props: TOneAvailableCampusProps) => {
  const {
    inVersionHistory,
    selectCampusEnvData,
    campus,
    handleEnvChipClick,
    handleSpecificCampusSwitch,
    disabled,
  } = props;
  const { name, display, envs } = campus;

  const [campusSwitch, setCampusSwitch] = useState(true);

  const handleCampusSwitch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checkStatus = e.currentTarget.checked;
    handleSpecificCampusSwitch(name, checkStatus);
    setCampusSwitch(checkStatus);
  };

  return (
    <Stack>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          pt: 2,
          justifyContent: "space-between",
        }}
      >
        <StyledLabel>{display}</StyledLabel>
        <CatSwitch
          disabled={disabled}
          checkedIcon={<img src={CheckedMark} alt="check-mark-icon" />}
          onChange={handleCampusSwitch}
          checked={campusSwitch}
        />
      </Box>
      <Box
        sx={{
          display: campusSwitch ? "flex" : "none",
          flexDirection: "row",
          flexWrap: "wrap",
          width: "100%",
          pt: 1,
        }}
      >
        {envs.map((env, index) => {
          return (
            <Box key={index} sx={{ pr: 1, pb: 1 }}>
              <CatIconChip
                label={env.display}
                selected={selectCampusEnvData?.some(
                  (obj: any) => obj["name"] === env.name
                )}
                disabled={inVersionHistory || disabled || env.disabled}
                onClick={() => handleEnvChipClick(name, env)}
              />
            </Box>
          );
        })}
      </Box>
    </Stack>
  );
};

/**
 * Display campuses for available Location.
 */
const AvailableLocation = (props: any) => {
  const {
    selectEnvData,
    inVersionHistory,
    locationData,
    handleEnvChipClick,
    handleSpecificCampusSwitch,
    disabled,
  } = props;
  const campuses = locationData?.campuses || [];

  return (
    <>
      {
        // eslint-disable-next-line array-callback-return
        campuses.map((campus: Campus, index: any) => {
          if (campus.envs) {
            return (
              <OneAvailableCampus
                disabled={disabled}
                key={index}
                inVersionHistory={inVersionHistory}
                selectCampusEnvData={selectEnvData?.[campus.name]}
                campus={campus}
                handleEnvChipClick={handleEnvChipClick}
                handleSpecificCampusSwitch={handleSpecificCampusSwitch}
              />
            );
          }
        })
      }
    </>
  );
};

/**
 * This is a sub-panel for Order Set Management Setting panel.
 * It uses on setting Location with its campuses in an Order Set Setting.
 * @prop {object} data the Set data
 */
const OsmLocationDetailsPanel = (props: any) => {
  const { data, settingType, inVersionHistory, disabled } = props;
  const [preEnvData, setPreEnvData] = useState();
  const selectLocation = data?.location
    ? data.location
    : OrderSetLocation.HospitalAndAmbulatory;
  const selectSpecifyLocations = data?.specifyLocations
    ? data.specifyLocations
    : false;
  const selectEnvData = data?.environments;

  const [location, setLocation] = useState(selectLocation);
  const [specifySwitch, setSpecifySwitch] = useState(selectSpecifyLocations);

  const { osmDispatch } = useOsmContext();

  const handleLocationChange = (event: any) => {
    const newLocation = event.target.value;
    osmDispatch({
      type: OsmAction.UpdateData,
      data: { ...data, location: newLocation },
    });
    setLocation(newLocation);
  };

  const getLocationCampusData = () => {
    let tmpEnvData: any = {};
    if (
      location === OrderSetLocation.AmbulatoryOnly &&
      locationOptions?.data?.[OrderSetLocationDataFormatName.Ambulatory]
        ?.campuses
    ) {
      const selectedCampuses =
        locationOptions?.data?.[OrderSetLocationDataFormatName.Ambulatory]
          ?.campuses;
      for (const campus of selectedCampuses) {
        tmpEnvData[campus.name] = campus?.envs;
      }
    } else if (
      location === OrderSetLocation.HospitalOnly &&
      locationOptions?.data?.[OrderSetLocationDataFormatName.Hospital]?.campuses
    ) {
      const selectedCampuses =
        locationOptions?.data?.[OrderSetLocationDataFormatName.Hospital]
          ?.campuses;
      for (const campus of selectedCampuses) {
        tmpEnvData[campus.name] = campus?.envs;
      }
    } else if (
      location === OrderSetLocation.HospitalAndAmbulatory &&
      locationOptions?.data?.[
        OrderSetLocationDataFormatName.HospitalAndAmbulatory
      ]?.campuses
    ) {
      const selectedCampuses =
        locationOptions?.data?.[
          OrderSetLocationDataFormatName.HospitalAndAmbulatory
        ]?.campuses;
      for (const campus of selectedCampuses) {
        tmpEnvData[campus.name] = campus?.envs;
      }
    }
    return tmpEnvData;
  };

  // Handle Specific Campus switch
  const handleSpecificCampusSwitch = (
    campusName: string,
    campusSwitchStatus: boolean
  ) => {
    let tmpEnvData: any = { ...data.environments };

    if (campusSwitchStatus === false) {
      setPreEnvData(cloneDeep(tmpEnvData));
      if (tmpEnvData.hasOwnProperty(campusName)) {
        delete tmpEnvData[campusName];
      }
    } else {
      if (!tmpEnvData.hasOwnProperty(campusName)) {
        const tmpCampusData = getLocationCampusData();
        tmpEnvData[campusName] = preEnvData?.[campusName]
          ? preEnvData?.[campusName]
          : tmpCampusData?.[campusName];
      }
    }
    osmDispatch({
      type: OsmAction.UpdateData,
      data: {
        ...data,
        environments: tmpEnvData,
      },
    });
  };

  // Handle Specify Locations switch
  const handleSpecifyLocationSwitch = () => {
    let tmpEnvData: any = {};
    if (!specifySwitch === true) {
      tmpEnvData = getLocationCampusData();
    }
    osmDispatch({
      type: OsmAction.UpdateData,
      data: {
        ...data,
        environments: tmpEnvData,
        specifyLocations: !specifySwitch,
      },
    });
    setSpecifySwitch(!specifySwitch);
  };

  const handleEnvChipClick = (campusName: any, env: any) => {
    let tmpEnvData = { ...data.environments };

    if (!tmpEnvData[campusName]) {
      tmpEnvData[campusName] = [];
    }
    const existEnv = tmpEnvData[campusName].find(
      (elem: any) => elem.name === env.name
    );
    if (existEnv) {
      tmpEnvData[campusName] = tmpEnvData[campusName].filter(
        (elem: any) => elem.name !== env.name
      );
    } else {
      tmpEnvData[campusName].push({
        name: env.name,
        display: env.display,
      });
    }
    osmDispatch({
      type: OsmAction.UpdateData,
      data: { ...data, environments: tmpEnvData },
    });
  };

  type TtmpCamps = {
    name: string;
    envs: {
      name: string;
      display: string;
    }[];
    [key: string]: any;
  };

  const parseOneAvailability = (availName: any, availData: any) => {
    let tmpCamps = {} as TtmpCamps;
    // eslint-disable-next-line array-callback-return
    availData.map((data: any) => {
      const campus = data[0];
      const campusName = data[1];
      const env = data[2];
      const envName = data[3];

      if (!tmpCamps[campus]) {
        tmpCamps[campus] = {
          name: campus,
          display: campusName,
          envs: [],
        };
      }

      tmpCamps[campus].envs.push({
        name: env,
        display: envName,
      });
    });

    const tmpKeys = Object.keys(tmpCamps);
    let tmpData: any = [];
    tmpKeys.forEach((oneKey) => {
      tmpData.push(tmpCamps[oneKey]);
    });

    return tmpData;
  };

  const parseEnvs = (rawData: any) => {
    let tmpAvailability = { ...defaultAvailability };
    if (rawData?.[OrderSetLocationDataFormatName.Hospital]?.data) {
      tmpAvailability.hospital.campuses = parseOneAvailability(
        OrderSetLocationDataFormatName.Hospital,
        rawData.hospital.data
      );
    }

    if (rawData?.[OrderSetLocationDataFormatName.Ambulatory]?.data) {
      tmpAvailability.ambulatory.campuses = parseOneAvailability(
        OrderSetLocationDataFormatName.Ambulatory,
        rawData.ambulatory.data
      );
    }

    if (rawData?.[OrderSetLocationDataFormatName.HospitalAndAmbulatory]?.data) {
      tmpAvailability.hospitalAndAmbulatory.campuses = parseOneAvailability(
        OrderSetLocationDataFormatName.HospitalAndAmbulatory,
        rawData.hospitalAndAmbulatory.data
      );
    }

    return tmpAvailability;
  };

  const locationOptions = useGetEnvsQuery({}, parseEnvs);

  const insertDisabledLocationOptions = (
    enabledLocation: LocationOption,
    disabledLocation: LocationOption
  ) => {
    disabledLocation?.campuses?.forEach((campus: Campus) => {
      const selectedCampus = enabledLocation?.campuses?.find(
        (c) => c.name === campus.name
      );
      campus?.envs?.forEach((env: Environment) => {
        if (
          !selectedCampus?.envs?.find(
            (e: Environment) => e.name === env.name
          ) &&
          selectedCampus?.envs
        ) {
          // Show disabled environments as disabled chips
          env.disabled = true;
          selectedCampus.envs.push(env);
        }
      });
    });
  };

  const formatLocationOptions = (
    enabledLocationName: string,
    disabledLocationNames: string[]
  ) => {
    const locationOptionsCopy = JSON.parse(
      JSON.stringify(locationOptions?.data)
    );
    const enabledLocation: LocationOption =
      locationOptionsCopy?.[enabledLocationName];
    for (const disabledLocationName of disabledLocationNames) {
      const disabledLocation: LocationOption =
        locationOptionsCopy?.[disabledLocationName];
      insertDisabledLocationOptions(enabledLocation, disabledLocation);
    }
    return enabledLocation;
  };

  const locationData = () => {
    if (
      location === OrderSetLocation.AmbulatoryOnly &&
      locationOptions?.data?.[OrderSetLocationDataFormatName.Ambulatory]
    ) {
      return formatLocationOptions(OrderSetLocationDataFormatName.Ambulatory, [
        OrderSetLocationDataFormatName.Hospital,
        OrderSetLocationDataFormatName.HospitalAndAmbulatory,
      ]);
    } else if (
      location === OrderSetLocation.HospitalOnly &&
      locationOptions?.data?.[OrderSetLocationDataFormatName.Hospital]
    ) {
      return formatLocationOptions(OrderSetLocationDataFormatName.Hospital, [
        OrderSetLocationDataFormatName.Ambulatory,
        OrderSetLocationDataFormatName.HospitalAndAmbulatory,
      ]);
    } else if (
      location === OrderSetLocation.HospitalAndAmbulatory &&
      locationOptions?.data?.[
        OrderSetLocationDataFormatName.HospitalAndAmbulatory
      ]
    ) {
      return formatLocationOptions(
        OrderSetLocationDataFormatName.HospitalAndAmbulatory,
        [
          OrderSetLocationDataFormatName.Hospital,
          OrderSetLocationDataFormatName.Ambulatory,
        ]
      );
    }
    return {};
  };

  useEffect(() => {
    setLocation(selectLocation);
  }, [selectLocation]);

  return (
    <Stack sx={{ pb: 2 }}>
      <StyledLabel>
        {settingType === OsmToolType.Set
          ? "Set Availability"
          : "Group Availability"}
      </StyledLabel>
      {settingType === OsmToolType.Group && (
        <Typography
          sx={{
            font: "normal 400 12px Roboto",
            color: "#7C7C7C",
          }}
        >
          Adjusts selection mode:
        </Typography>
      )}
      <OsmSelect
        // @ts-ignore TS(2322): Type '{ value: any; name: string; disabled: any; o... Remove this comment to see the full error message
        value={Object.keys(locationOptions?.data).length > 0 ? location : ""}
        name="availableLocation"
        disabled={inVersionHistory || disabled}
        onChange={(e: React.MouseEvent<HTMLElement>) => {
          handleLocationChange(e);
        }}
        style={{ width: "324px", height: "32px", fontSize: "15px" }}
        options={
          locationOptions?.data ? Object.values(locationOptions.data) : []
        }
      ></OsmSelect>
      <Box
        sx={{
          height: 40,
          display: "flex",
          flexDirection: "row",
          pt: 4,
          justifyContent: "space-between",
        }}
      >
        <StyledLabel>Specify Locations</StyledLabel>
        <CatSwitch
          disabled={disabled}
          checked={specifySwitch}
          checkedIcon={<img src={CheckedMark} alt="check-mark-icon" />}
          onChange={handleSpecifyLocationSwitch}
        />
      </Box>
      <Box sx={{ width: "275px" }}>
        <Typography
          sx={{
            font: "normal 400 12px Roboto",
            color: "#7C7C7C",
          }}
        >
          Order Set will be available in all Hospital Locations and Environments
          unless otherwise specified.
        </Typography>
      </Box>
      {specifySwitch && (
        <AvailableLocation
          disabled={disabled}
          selectEnvData={selectEnvData}
          inVersionHistory={inVersionHistory}
          locationData={locationData()}
          handleEnvChipClick={handleEnvChipClick}
          handleSpecificCampusSwitch={handleSpecificCampusSwitch}
        />
      )}
    </Stack>
  );
};

export default OsmLocationDetailsPanel;
