import { Box, Typography } from "@mui/material";
import { TOsmDataType } from "../OsmPropTypes";

type TOneVersionPanel = {
  versionData: TOsmDataType;
  onSelect: (versionData: any) => void;
  selectedVersion: boolean;
  currentVersion: string;
};

/**
 * One version of Set/Group data.
 * @prop {object} version
 * @prop {func} onSelect
 */
const OneVersionPanel = (props: TOneVersionPanel) => {
  const { versionData, onSelect, selectedVersion, currentVersion } = props;
  return (
    <Box
      onClick={() => onSelect(versionData)}
      sx={{
        py: 2,
        px: 4,
        background: selectedVersion ? "#F9EEBC" : "#FFFFFF",
        borderBottom: "1px solid rgba(244, 244, 244, 1)",
      }}
    >
      {currentVersion === versionData.version ? (
        <Typography
          sx={{
            color: "#4B6EAF",
            fontSize: 15,
            fontWeight: 500,
          }}
        >
          Version {versionData.version} (Current){" "}
          {versionData.status && "(" + versionData.status + ")"}{" "}
        </Typography>
      ) : (
        <Typography sx={{ color: "#4B6EAF", fontSize: 15, fontWeight: 500 }}>
          Version {versionData.version}
        </Typography>
      )}
      <Typography sx={{ fontSize: 15, fontWeight: 700 }}>
        {versionData.updateType ? versionData.updateType : versionData.status} -{" "}
        {versionData.updatedBy}
      </Typography>
      {versionData.status === "Retired" && (
        <div>
          <Typography sx={{ fontSize: 15 }}>
            <Box sx={{ fontWeight: 420 }}>Reason for Retiring</Box> -{" "}
            {versionData.retireReason}
          </Typography>
          <Typography>Retire Comment: {versionData.retireComment}</Typography>
        </div>
      )}
      <Typography>
        {Cci.util.DateTime.serverSecsToTimeStr(
          Cci.util.DateTime.timeStrToServerSecs(
            versionData.updateDate,
            "HH:mm DD MMM YYYY"
          ),
          "MM/DD/YYYY HH:mm"
        )}
      </Typography>
      <Typography>{versionData.versionNote}</Typography>
    </Box>
  );
};

export default OneVersionPanel;
