import React, { useState } from "react";
import { isCreateNewPermission } from "@cci-monorepo/CAT/common/Permissions";

import OsmToolbar from "../common/OsmToolbar";
import { OsmCciButton } from "../common/OsmButtons";
import { OsmVerticalDivider } from "../common/OsmDividers";
import { OsmSearchField } from "../common/OsmSearchField";
import {
  OrderSetStatus,
  OsmAction,
  OsmToolType,
  OsmType,
} from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";
import {
  DialogVariants,
  useAlert,
} from "@cci-monorepo/Psat/common/layouts/AdminLayoutA/context/AlertContext";
import { ChipChoice } from "@cci/mui-components";

/**
 * The toolbar for search panel (Set/Group) in Order Set Management Tool
 * @param {Object} props
 *  - searchType: the type of search component
 */
const OsmSearchToolbar = (props: any) => {
  const { searchType, setFilter } = props;
  const [selectedStatusBtn, setSelectedStatusBtn] = useState(
    OsmAction.AllOrders
  );

  const { osmDispatch, osmState } = useOsmContext();
  const createAlert = useAlert();

  const typeName =
    searchType === OsmType.OsmSearchSet ? OsmToolType.Set : OsmToolType.Group;
  const typeNameLabel = searchType === OsmType.OsmSearchSet ? "Sets" : "Groups";

  const chipsLabelsGroup = [
    {
      label: `All Order ${typeNameLabel}`,
      id: 0,
      value: OsmAction.AllOrders,
    },
    {
      label: `My Order ${typeNameLabel}`,
      id: 1,
      value: OsmAction.MyOrders,
    },
    {
      label: `Drafts`,
      id: 2,
      value: OsmAction.Drafts,
    },
    {
      label: `Publish`,
      id: 3,
      value: OsmAction.Publish,
    },
    {
      label: `Retired`,
      id: 4,
      value: OsmAction.Retired,
    },
  ];

  const onSearchFilter = (event: any) => {
    let searchStr = "";
    searchStr = event.target.value.toLowerCase();
    osmDispatch({
      type:
        typeName === OsmToolType.Set
          ? OsmAction.SearchSetList
          : OsmAction.SearchGroupList,
      searchStr: searchStr,
    });
  };

  const handleButtonClick = (btnType: any) => {
    switch (btnType) {
      case OsmAction.CreateNewSet:
        break;
      case OsmAction.CreateNewGroup:
        break;
      case OsmAction.AllOrders:
        setFilter({ status: "" });
        setSelectedStatusBtn(btnType);
        break;
      case OsmAction.MyOrders:
        setFilter({ staffid: Cci.util.Staff.getSid() });
        setSelectedStatusBtn(btnType);
        break;
      case OsmAction.Publish:
        setFilter({ status: OrderSetStatus.Published });
        setSelectedStatusBtn(btnType);
        break;
      case OsmAction.Drafts:
        setFilter({ status: OrderSetStatus.Draft });
        setSelectedStatusBtn(btnType);
        break;
      case OsmAction.Retired:
        setFilter({ status: OrderSetStatus.Retired });
        setSelectedStatusBtn(btnType);
        break;

      default:
        createAlert({
          variant: DialogVariants.INFO,
          msg: "Not implemented yet.",
        });
        return;
    }
    osmDispatch({
      type: btnType,
    });
  };

  return (
    <OsmToolbar sx={{ width: "100%", gap: 2 }}>
      <OsmSearchField
        placeHolder={"Search Order " + typeNameLabel}
        onChange={onSearchFilter}
        value={osmState.searchStr}
        autoFocus={props.autoFocus ? props.autoFocus : false}
      />
      <OsmVerticalDivider />
      <ChipChoice
        labels={chipsLabelsGroup.map((val) => val.label)}
        size="medium"
        selected={
          chipsLabelsGroup.filter((val) => val.value === selectedStatusBtn)[0]
            .id
        }
        setSelected={(val) => {
          handleButtonClick(chipsLabelsGroup[val].value);
        }}
        sx={{
          height: "30px",
          padding: "2px 12px 2px 6px",
          "& .MuiFormControlLabel-label": {
            fontSize: "16px!important",
            padding: "0!important",
          },
        }}
      />
      {isCreateNewPermission && <OsmVerticalDivider />}
      {isCreateNewPermission && (
        <>
          <OsmCciButton
            buttonName={"Create New Order " + typeNameLabel}
            onClick={(e: React.MouseEvent<HTMLElement>) =>
              handleButtonClick(
                typeName === OsmToolType.Set
                  ? OsmAction.CreateNewSet
                  : OsmAction.CreateNewGroup
              )
            }
          ></OsmCciButton>
        </>
      )}
    </OsmToolbar>
  );
};

export default OsmSearchToolbar;
