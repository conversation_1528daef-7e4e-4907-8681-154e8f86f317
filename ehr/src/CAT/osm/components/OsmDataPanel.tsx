// @ts-nocheck
import React, { useState } from "react";
import { styled } from "@mui/material/styles";
import { Box } from "@mui/material";
import RemoveIcon from "@cci-monorepo/oe-shared/src/assets/osm/Icn_Remove.svg";

const StyledDataBox = styled(Box)(({ draggable, removable }) => ({
  display: "flex",
  flexDirection: "row",
  boxShadow: removable
    ? "0px 0px 4px 0px rgba(0, 0, 0, 0.25)"
    : "0px 0px 0px 0px rgba(0, 0, 0, 0.25)",
  borderRadius: "8px",
  margin: draggable ? "8px 2px 8px -38px" : "8px 2px",
  padding: "2px",
}));

type TOsmDataPanel = {
  removable?: boolean;
  onClick?: (data: any) => void;
  onRemove?: (data: any) => void;
  removeData?: any;
  draggable?: boolean;
  sx?: any;
  disabled?: boolean;
  children: React.ReactNode;
};

const OSMRemoveIcon = ({ showRemoveIcon, handleRemove }) => {
  if (!showRemoveIcon) {
    return null;
  }

  return (
    <img
      src={RemoveIcon}
      alt={"osm-remove-icon"}
      // @ts-ignore TS(2322): Type '(event: any, data: any) => void' is not assi... Remove this comment to see the full error message
      onClick={handleRemove}
      style={{ width: "26px", height: "26px", cursor: "pointer" }}
    />
  );
};

/**
 * Basic Data panel for Order Set Management Tool.
 * @todo candidate for storybook
 * It provides default styles, plus removable
 * If it is removable, it shows a trash icon at right when move over.
 * @prop {boolean} removable optional, default is false, the panel is removable or not, if it is true, need onRemove function.
 * @prop {func} onRemove optional, default is undefined, if isRemovable is true, it does removes the panel.
 * @prop {object} removeData optional, default is undefined, the data related remove action.
 * @prop {boolean} draggable optional, default is false, the component is draggable or not
 */
const OsmDataPanel = (props: TOsmDataPanel) => {
  const {
    removable,
    onRemove,
    removeData,
    draggable,
    sx,
    disabled,
    ...others
  } = props;

  const [showRemoveIcon, setShowRemoveIcon] = useState(false);

  const handleMouseEnter = () => {
    removable && setShowRemoveIcon(true);
  };
  const handleMouseLeave = () => {
    removable && setShowRemoveIcon(false);
  };

  const handleRemove = (event: any, data: any) => {
    event.stopPropagation();
    onRemove && onRemove(removeData && removeData);
  };

  return (
    <StyledDataBox
      {...others}
      sx={{
        display: "flex",
        flexGrow: 1,
        alignItems: "center",
        gap: 2,
        ...sx,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      draggable={draggable && disabled === false}
      removable={!(removable === undefined || removable === false)}
    >
      <Box sx={{ display: "flex", flexGrow: 1 }}>{props.children}</Box>
      <Box sx={{ width: "26px", height: "26px", alignSelf: "start" }}>
        <OSMRemoveIcon
          showRemoveIcon={showRemoveIcon}
          handleRemove={handleRemove}
        />
      </Box>
    </StyledDataBox>
  );
};

export default OsmDataPanel;
