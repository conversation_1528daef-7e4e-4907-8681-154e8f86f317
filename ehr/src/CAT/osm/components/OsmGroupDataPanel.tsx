import { useMemo } from "react";
import {
  Box,
  Checkbox,
  FormControl,
  Stack,
  Typography,
  Radio,
} from "@mui/material";
import OsmDataPanel from "./OsmDataPanel";
import OsmOrderDataPanel from "./OsmOrderDataPanel";
import OsmInstructionPanel from "./OsmInstructionPanel";
import { useOsmContext } from "../contexts/OsmContext";
import { GroupOrderSelectionType, OsmAction } from "../OsmConstants";
import { DataGridPro, GRID_REORDER_COL_DEF } from "@mui/x-data-grid-pro";
import OsmGroupDataRenderer from "../common/OsmGroupDataRenderer";
import { getOrderData } from "../util/OsmOrderListHelper";
import OsmEmptyDataMsgBox from "../common/OsmEmptyDataMsgBox";

/**
 * Help function to check Order selections in a Group.
 * @prop {object} subIds in the Group
 * @prop {object} selections
 * @prop {string} selectionMode
 * @return {number} the status number
 *  - 0: invalid
 *  - 1: satified requirement
 *  - 2: not satified
 */
const checkOrderSelection = (
  set: any,
  group: any,
  selections: any,
  selectionMode: any
) => {
  let status = 1;
  let selected = 0;
  let subIds = [];
  if (group && group.subIds) {
    subIds = Array.isArray(group.subIds) ? group.subIds : [];
  }
  let newSelections = selections;
  if (set && set.groupIds) {
    let newSetGroupIds = [...set.groupIds];
    if (Object.keys(newSelections).length === 0) {
      newSetGroupIds.forEach((setGroupId: any) => {
        if (setGroupId.group_id === group.id) {
          newSelections = setGroupId.selections;
        }
      });
    }
  }
  subIds.forEach((subId: any) => {
    if (
      subId.orderid &&
      newSelections?.[subId.orderid] &&
      newSelections?.[subId.orderid] === "1"
    ) {
      selected++;
    }
    if (
      subId.group_id &&
      newSelections?.[subId.group_id] &&
      newSelections?.[subId.group_id].selections &&
      newSelections?.[subId.group_id].selections["groupSelected"] === "1"
    ) {
      selected++;
    }
  });

  if (parseInt(selectionMode) === GroupOrderSelectionType.SelectOne) {
    if (selected > 1) {
      status = 0;
    } else if (selected < 1) {
      status = 2;
    }
  } else if (parseInt(selectionMode) === GroupOrderSelectionType.MinimalOne) {
    if (selected < 1) {
      status = 2;
    }
  } else if (
    parseInt(selectionMode) === GroupOrderSelectionType.SelectRequired
  ) {
    if (selected !== 1) {
      status = 2;
    }
  }

  return status;
};

/**
 * Show message based on Group selection mode if needed
 * @prop {string} msg text message
 * @prop {boolean} satified optional, the selection of Orders satified the Mode requirement, default is false
 */
export const SelectionModeMsg = (props: any) => {
  const { msg, satified } = props;
  return (
    <Typography
      sx={{
        pl: 9,
        fontSize: 16,
        fontWeight: 500,
        opacity: 0.5,
        color: satified ? "#000000" : "#CF4C35",
      }}
    >
      {msg}
    </Typography>
  );
};

const initGroupIds = (allGroup: any, setGroupIds: any[]) => {
  setGroupIds.forEach((setGroupId: any) => {
    if (setGroupId.group_id) {
      if (!setGroupId.selections) {
        setGroupId.selections = {};
      }

      const func = (top: boolean, groupId: string | null) => {
        const index = top ? setGroupId.group_id : groupId;
        const root = allGroup[index];
        root.subIds.forEach((s: any) => {
          if (s.group_id) {
            if (!setGroupId.selections[s.group_id]) {
              setGroupId.selections[s.group_id] = {};
            }
            if (!setGroupId.selections[s.group_id]["selections"]) {
              setGroupId.selections[s.group_id]["selections"] = {};
            }
            if (
              !setGroupId.selections[s.group_id]["selections"]["groupSelected"]
            ) {
              setGroupId.selections[s.group_id]["selections"]["groupSelected"] =
                "0";
            }
            const subGroup = allGroup[s.group_id];
            subGroup.subIds.forEach((e: any) => {
              if (e.orderid) {
                if (
                  !setGroupId.selections[s.group_id]["selections"][e.orderid]
                ) {
                  setGroupId.selections[s.group_id]["selections"][e.orderid] =
                    "0";
                }
              }
            });
            func(false, s.group_id);
          } else if (s.orderid) {
            if (top && !setGroupId.selections[s.orderid]) {
              setGroupId.selections[s.orderid] = "0";
            }
          }
        });
      };
      func(true, null);
    }
  });
};

type OneGroupDataPanelProps = {
  group: any;
  parentGroupIds?: any[];
  selections?: any;
  selectable?: any;
  set?: any;
  isLeaf?: boolean;
  disabled?: boolean;
  handleOrderEdit?: (order: any) => void;
  editingOrderId?: string;
  setEditingOrderId?: (orderId: string) => void;
};

/**
 * Show one group data.
 * @todo candidate for Storybook
 */
export const OneGroupDataPanel = (props: OneGroupDataPanelProps) => {
  const {
    group,
    parentGroupIds = [],
    selections,
    selectable = false,
    set,
    isLeaf = false,
    disabled = false,
    handleOrderEdit,
    editingOrderId,
    setEditingOrderId,
  } = props;

  const { subIds, selectionType } = group;
  const { osmDispatch, allDataRef } = useOsmContext();
  const allGroups = allDataRef?.current?.groups;

  const groupEmpty = (mySelections: any): boolean => {
    let empty: boolean = true;
    for (const [key, value] of Object.entries(mySelections)) {
      if (key !== "groupSelected") {
        if (value === "1") {
          empty = false;
        }
      }
    }
    return empty;
  };

  const uncheckChildGroups = (
    parentGId: any,
    groupId: any,
    oneGroupId: any
  ) => {
    if (!oneGroupId) {
      return;
    }
    const parentGroup = allDataRef.current.groups[parentGId];
    oneGroupId.selections[parentGId].isSelected = "0";
    if (!oneGroupId.selections[parentGId].selections) {
      oneGroupId.selections[parentGId].selections = {};
    }
    oneGroupId.selections[parentGId].selections["groupSelected"] = "0";
    for (let i = 0; i < parentGroup.subIds.length; i++) {
      if (parentGroup.subIds[i].orderid) {
        oneGroupId.selections[parentGId].selections[
          parentGroup.subIds[i].orderid
        ] = "0";
      } else if (parentGroup.subIds[i].group_id) {
        if (parentGroup.subIds[i].group_id !== groupId) {
          uncheckChildGroups(
            parentGroup.subIds[i].group_id,
            groupId,
            oneGroupId
          );
        }
      }
    }
  };

  const uncheckOtherChildGroups = (
    parentGId: any,
    groupId: any,
    oneGroupId: any
  ) => {
    if (!oneGroupId) {
      return;
    }
    const parentGroup = allDataRef.current.groups[parentGId];
    for (let i = 0; i < parentGroup.subIds.length; i++) {
      if (parentGroup.subIds[i].orderid) {
        oneGroupId.selections[parentGId].selections[
          parentGroup.subIds[i].orderid
        ] = "0";
      } else if (parentGroup.subIds[i].group_id) {
        if (parentGroup.subIds[i].group_id !== groupId) {
          oneGroupId.selections[parentGroup.subIds[i].group_id].isSelected =
            "0";
          if (
            !oneGroupId.selections[parentGroup.subIds[i].group_id].selections
          ) {
            oneGroupId.selections[parentGroup.subIds[i].group_id].selections =
              {};
          }
          oneGroupId.selections[parentGroup.subIds[i].group_id].selections[
            "groupSelected"
          ] = "0";
          uncheckChildGroups(
            parentGroup.subIds[i].group_id,
            groupId,
            oneGroupId
          );
        }
      }
    }
  };
  const setOrderSelected = (
    oneGroupId: any,
    groupId: string,
    checked: any,
    orderId: string
  ) => {
    let empty = true;
    if (oneGroupId.group_id !== groupId) {
      empty = groupEmpty(oneGroupId.selections?.[groupId].selections);
      oneGroupId.selections[groupId].selections.groupSelected = empty
        ? "0"
        : "1";
    }
    let avoidGroupListIds: string[] = [groupId];
    if (parentGroupIds?.length > 0) {
      for (let i = parentGroupIds?.length - 1; i >= 0; --i) {
        const pId = parentGroupIds[i];
        const myGroup = allDataRef.current.groups[pId];
        const isTopGroup = oneGroupId.group_id === pId;
        const mySelections = isTopGroup
          ? oneGroupId.selections
          : oneGroupId.selections?.[pId].selections;
        let pEmpty = true;
        switch (parseInt(myGroup.selectionType)) {
          case GroupOrderSelectionType.SelectOne:
            pEmpty = false;
            for (let i = 0; i < myGroup.subIds.length; i++) {
              let ord = myGroup.subIds[i];
              if (ord.orderid) {
                if (groupId !== pId && ord.orderid !== orderId) {
                  mySelections[ord.orderid] = "0";
                } else {
                  mySelections[ord.orderid] = "1";
                }
              } else if (ord.group_id) {
                if (avoidGroupListIds.indexOf(ord.group_id) === -1) {
                  oneGroupId.selections[ord.group_id].isSelected = "0";
                  if (!oneGroupId.selections[ord.group_id].selections) {
                    oneGroupId.selections[ord.group_id].selections = {};
                  }
                  oneGroupId.selections[ord.group_id].selections[
                    "groupSelected"
                  ] = "0";
                  uncheckOtherChildGroups(ord.group_id, groupId, oneGroupId);
                }
              }
            }
            mySelections["groupSelected"] = "1";
            break;
          case GroupOrderSelectionType.MinimalOne:
          case GroupOrderSelectionType.Multiple:
            const pGroup = allGroups[pId];
            pGroup.subIds.forEach((e: any) => {
              if (e.group_id && e.group_id !== groupId) {
                if (
                  oneGroupId.selections[e.group_id]?.selections?.[
                    "groupSelected"
                  ] === "1"
                ) {
                  pEmpty = false;
                }
              }
            });
            if (isTopGroup) {
              oneGroupId.selections["groupSelected"] =
                empty && pEmpty ? "0" : "1";
            } else {
              oneGroupId.selections[pId].selections["groupSelected"] =
                empty && pEmpty ? "0" : "1";
            }
            break;
        }
        avoidGroupListIds = [...avoidGroupListIds, pId];
      }
    } else {
      const myGroup = allDataRef.current.groups[groupId];
      const mySelections = oneGroupId.selections;
      let pEmpty = true;
      switch (parseInt(myGroup.selectionType)) {
        case GroupOrderSelectionType.SelectOne:
          pEmpty = false;
          for (let i = 0; i < myGroup.subIds.length; i++) {
            let ord = myGroup.subIds[i];
            if (ord.orderid) {
              if (ord.orderid !== orderId) {
                mySelections[ord.orderid] = "0";
              } else {
                mySelections[ord.orderid] = "1";
              }
            } else if (ord.group_id) {
              uncheckChildGroups(ord.group_id, groupId, oneGroupId);
            }
          }
          mySelections["groupSelected"] = "1";
          break;
        case GroupOrderSelectionType.MinimalOne:
        case GroupOrderSelectionType.Multiple:
          const pGroup = allGroups[groupId];
          pGroup.subIds.forEach((e: any) => {
            if (e.group_id && e.group_id !== groupId) {
              if (
                oneGroupId.selections[e.group_id]?.selections?.[
                  "groupSelected"
                ] === "1"
              ) {
                pEmpty = false;
              }
            }
          });
          oneGroupId.selections["groupSelected"] = pEmpty && pEmpty ? "0" : "1";
          break;
      }
    }
  };

  /**
   * Handle user select/unselect an Order in a Group in a Set
   * @param orderId the target order id
   * @param checked true/false
   */
  const onOrderSelect = (orderId: any, checked: any) => {
    if (!set) return;
    let newGroupId = group.id;
    let myGroup = allDataRef.current.groups[newGroupId];
    if (!myGroup.subIds) {
      myGroup.subIds = [];
    }
    let newSetGroupIds = [...set.groupIds];
    if (parentGroupIds && parentGroupIds.length > 0) {
      newSetGroupIds.forEach((oneGroupId) => {
        if (oneGroupId.group_id === parentGroupIds[0]) {
          if (!oneGroupId.selections) oneGroupId.selections = {};
          if (!oneGroupId.selections[newGroupId]) {
            oneGroupId.selections[newGroupId] = {
              selections: {},
            };
          }
          switch (parseInt(myGroup.selectionType)) {
            case GroupOrderSelectionType.SelectOne:
              for (let i = 0; i < myGroup.subIds.length; i++) {
                let ord = myGroup.subIds[i];
                if (ord.orderid) {
                  if (orderId === ord.orderid) {
                    oneGroupId.selections[newGroupId].selections[ord.orderid] =
                      "1";
                  } else {
                    oneGroupId.selections[newGroupId].selections[ord.orderid] =
                      "0";
                  }
                } else if (ord.group_id) {
                  let newGroup = allDataRef.current.groups[ord.group_id];
                  if (oneGroupId.selections[ord.group_id]) {
                    oneGroupId.selections[ord.group_id].isSelected = "0";
                    for (let i = 0; i < newGroup.subIds.length; i++) {
                      let ord2 = newGroup.subIds[i];
                      if (ord2.orderid) {
                        oneGroupId.selections[ord.group_id].selections[
                          ord2.orderid
                        ] = "0";
                      }
                    }
                    oneGroupId.selections[ord.group_id].selections[
                      "groupSelected"
                    ] = "0";
                  }
                }
              }
              break;
            case GroupOrderSelectionType.MinimalOne:
            case GroupOrderSelectionType.Multiple:
              oneGroupId.selections[newGroupId].selections[orderId] = checked
                ? "1"
                : "0";
          }
          setOrderSelected(oneGroupId, newGroupId, checked, orderId);
        }
      });
    } else {
      newSetGroupIds.forEach((oneGroupId) => {
        if (oneGroupId.group_id === newGroupId) {
          if (!oneGroupId.selections) oneGroupId.selections = {};
          switch (parseInt(myGroup.selectionType)) {
            case GroupOrderSelectionType.SelectOne:
              for (let i = 0; i < myGroup.subIds.length; i++) {
                let ord = myGroup.subIds[i];
                if (ord.orderid) {
                  if (orderId === ord.orderid) {
                    oneGroupId.selections[ord.orderid] = "1";
                  } else {
                    oneGroupId.selections[ord.orderid] = "0";
                  }
                } else if (ord.group_id) {
                  let newGroup = allDataRef.current.groups[ord.group_id];
                  if (oneGroupId.selections[ord.group_id]) {
                    oneGroupId.selections[ord.group_id].isSelected = "0";
                    for (let i = 0; i < newGroup.subIds.length; i++) {
                      let ord2 = newGroup.subIds[i];
                      if (ord2.orderid) {
                        oneGroupId.selections[ord.group_id].selections[
                          ord2.orderid
                        ] = "0";
                      }
                    }
                    oneGroupId.selections[ord.group_id].selections[
                      "groupSelected"
                    ] = "0";
                  }
                }
              }
              break;
            case GroupOrderSelectionType.MinimalOne:
            case GroupOrderSelectionType.Multiple:
              oneGroupId.selections[orderId] = checked ? "1" : "0";
          }
          setOrderSelected(oneGroupId, newGroupId, checked, orderId);
        }
      });
    }
    osmDispatch({
      type: OsmAction.SetGroupIds,
      data: { groupIds: newSetGroupIds },
    });
  };

  const parentGroupId =
    parentGroupIds && parentGroupIds.length > 0 ? parentGroupIds[0] + "_" : "";

  return (
    <FormControl>
      {/*// @ts-ignore*/}
      <Stack sx={{ gap: 1, padding: "0px" }}>
        {// eslint-disable-next-line
        subIds?.map((subId: any, index: any) => {
          if (subId.orderid) {
            const order = getOrderData(allDataRef, set?.id, subId.orderid);
            const uOrderId = parentGroupId + group.id + "_" + order?.orderid;
            // @todo - this logic worked incorrectly. there is no no selections in OsmOrderDataPanel.
            return (
              <OsmOrderDataPanel
                key={index}
                disabled={disabled}
                order={order}
                groupId={group.id}
                selectionType={selectionType}
                selectable={selectable}
                onOrderSelectChange={onOrderSelect}
                selected={selections?.[subId.orderid] === "1"}
                onOrderEdit={handleOrderEdit}
                editingOrderId={editingOrderId}
                setEditingOrderId={setEditingOrderId}
                uOrderId={uOrderId}
              />
            );
          } else if (subId.group_id) {
            const subgrp = allGroups[subId.group_id];
            const newParentGroupIds = parentGroupIds
              ? [...parentGroupIds, group.id]
              : [group.id];
            let newSelections = {};

            const newParentGroupIdSet = new Set(newParentGroupIds);
            if (newParentGroupIdSet.size !== newParentGroupIds.length) {
              return <></>;
            }

            if (set) {
              set.groupIds.forEach((gid: any) => {
                if (gid.selections?.[subId.group_id]?.selections) {
                  newSelections = gid.selections[subId.group_id].selections;
                  return;
                }
              });
            }

            return (
              <OsmGroupDataPanel
                {...props}
                disabled={disabled}
                key={index}
                group={subgrp}
                parentGroupIds={newParentGroupIds}
                selections={newSelections}
                isLeaf={subgrp.subIds ? false : true}
                selectable={selectable}
                set={set}
                selectionType={subgrp.selectionType}
                parentSelectionType={group.selectionType}
              />
            );
          }
        })}
      </Stack>
    </FormControl>
  );
};

/**
 * Display one Order Group data with removeable and draggable options
 * @todo candidate for Storybook
 */
const OsmGroupDataPanel = (props: any) => {
  const {
    group,
    parentGroupIds,
    selectionType,
    parentSelectionType,
    selections,
    isLeaf,
    selectable,
    set,
    inVersionHistory,
    disabled,
    ...others
  } = props;
  const { osmDispatch, allDataRef } = useOsmContext();

  const allGroups = allDataRef?.current?.groups;

  let NewSelections = selections;

  if (set && set.groupIds) {
    let newSetGroupIds = [...set.groupIds];
    initGroupIds(allGroups, newSetGroupIds);
    if (Object.keys(NewSelections).length === 0) {
      //a new group is just added to orderset and has not been saved.
      newSetGroupIds.forEach((setGroupId: any) => {
        if (setGroupId.group_id === group.id) {
          NewSelections = setGroupId.selections;
        }
      });
    }
  }
  const isGroupSelected = () => {
    let flag = "0";
    if (set) {
      if (parentGroupIds && parentGroupIds.length > 0) {
        set.groupIds.forEach((oneGroupId: any) => {
          if (oneGroupId.group_id === parentGroupIds[0]) {
            flag =
              oneGroupId?.selections?.[group.id]?.selections?.["groupSelected"];
          }
        });
      } else {
        flag = set.groupIds[0].selections.groupSelected;
      }
    }
    return flag === "1";
  };

  const groupChecked = isGroupSelected();

  const setGroupSelected = (oneGroupId: any, groupId: string) => {
    let selections = oneGroupId.selections?.[groupId]?.selections;
    const currentGroup = allGroups[groupId];
    if (!selections || !currentGroup) {
      return;
    }
    if (selections["groupSelected"] === "0") {
      uncheckAllChildGroups(groupId, oneGroupId);
    }
    // skip root, loop parent
    for (let i = parentGroupIds?.length - 1; i > 0; --i) {
      const pId = parentGroupIds[i];
      let pEmpty = true;
      const pGroup = allGroups[pId];
      pGroup.subIds.forEach((g: any) => {
        if (g.group_id) {
          if (
            oneGroupId.selections[g.group_id]?.selections?.["groupSelected"] ===
            "1"
          ) {
            pEmpty = false;
          }
        }
      });
      oneGroupId.selections[pId].selections["groupSelected"] = pEmpty
        ? "0"
        : "1";
    }

    // loop children
    currentGroup.subIds.forEach((g: any) => {
      if (g.group_id) {
        oneGroupId.selections[g.group_id].selections["groupSelected"] =
          selections["groupSelected"];
        setGroupSelected(oneGroupId, g.group_id);
      }
    });
  };

  const uncheckAllChildGroups = (groupId: any, oneGroupId: any) => {
    if (!oneGroupId) {
      return;
    }
    const myGroup = allDataRef.current.groups[groupId];
    oneGroupId.isSelected = "0";
    if (!oneGroupId.selections) {
      oneGroupId.selections = {};
    }
    for (let i = 0; i < myGroup.subIds.length; i++) {
      if (myGroup.subIds[i].orderid) {
        oneGroupId.selections[groupId].selections[myGroup.subIds[i].orderid] =
          "0";
      } else if (myGroup.subIds[i].group_id) {
        if (oneGroupId.selections[myGroup.subIds[i].group_id]) {
          oneGroupId.selections[myGroup.subIds[i].group_id].isSelected = "0";
          if (oneGroupId.selections[myGroup.subIds[i].group_id].selections) {
            oneGroupId.selections[
              myGroup.subIds[i].group_id
            ].selections.groupSelected = "0";
          }
        }
        uncheckAllChildGroups(myGroup.subIds[i].group_id, oneGroupId);
      }
    }
  };
  const onGroupRadionClick = (e: any, groupid: any) => {
    let newSetGroupIds = [...set.groupIds];
    if (parentGroupIds && parentGroupIds.length > 0) {
      let parentGroup =
        allDataRef.current.groups[parentGroupIds[parentGroupIds.length - 1]];
      if (!parentGroup.subIds) {
        parentGroup.subIds = [];
      }
      newSetGroupIds.forEach((oneGroupId) => {
        let parentSetGroup: any;
        if (oneGroupId.group_id === parentGroupIds[0]) {
          for (let i = parentGroupIds.length - 1; i >= 0; i--) {
            if (oneGroupId.group_id === parentGroupIds[i]) {
              parentSetGroup = oneGroupId;
            } else {
              parentSetGroup = oneGroupId.selections[parentGroupIds[i]];
            }
            if (!oneGroupId.selections) oneGroupId.selections = {};
            if (!oneGroupId.selections[groupid]) {
              oneGroupId.selections[groupid] = {
                selections: {},
              };
            }
            parentSetGroup.isSelected = "1";
            parentSetGroup.selections["groupSelected"] = "1";
            for (let i = 0; i < parentGroup.subIds.length; i++) {
              let ord = parentGroup.subIds[i];
              if (ord.orderid) {
                parentSetGroup.selections[ord.orderid] = "0";
              } else if (ord.group_id) {
                if (groupid === ord.group_id) {
                  oneGroupId.selections[ord.group_id].isSelected = "1";
                  oneGroupId.selections[ord.group_id].selections[
                    "groupSelected"
                  ] = "1";
                } else {
                  oneGroupId.selections[ord.group_id].isSelected = "0";
                  oneGroupId.selections[ord.group_id].selections[
                    "groupSelected"
                  ] = "0";
                  uncheckAllChildGroups(ord.group_id, oneGroupId);
                }
              }
            }
          }
        }
        setGroupSelected(oneGroupId, groupid);
      });
    }
    osmDispatch({
      type: OsmAction.UpdateData,
      data: { groupIds: newSetGroupIds },
    });
  };
  const onGroupSelect = (e: any, groupid: any) => {
    const checked = e.target.checked;

    let newSetGroupIds = [...set.groupIds];
    if (parentGroupIds && parentGroupIds.length > 0) {
      newSetGroupIds.forEach((oneGroupId) => {
        if (oneGroupId.group_id === parentGroupIds[0]) {
          if (!oneGroupId.selections) oneGroupId.selections = {};
          if (!oneGroupId.selections[groupid]) {
            oneGroupId.selections[groupid] = {
              selections: {},
            };
          }
          oneGroupId.selections[groupid].selections["groupSelected"] = checked
            ? "1"
            : "0";
        }
        setGroupSelected(oneGroupId, groupid);
      });
    }
    osmDispatch({
      type: OsmAction.UpdateData,
      data: { groupIds: newSetGroupIds },
    });
  };

  const inSetGroupNameSx = {
    fontSize: 16,
    fontWeight: 600,
    color: "#396EA7",
  };

  const subGroupNameSx = {
    fontSize: 14,
    fontWeight: 400,
    color: "#000000",
  };

  const satified = checkOrderSelection(
    set,
    group,
    selections,
    group?.selectionType
  );
  const selectStyle = {
    alignSelf: "start",
    marginLeft: "12px",
  };

  return (
    <OsmDataPanel
      {...others}
      //SZ29371: not allow remove children groups
      removable={
        !inVersionHistory &&
        disabled === false &&
        (!parentGroupIds || parentGroupIds.length === 0)
      }
      removeData={group}
      draggable={props.draggable && !isLeaf && disabled === false}
      disabled={disabled}
      sx={
        isLeaf
          ? { border: "none", marginLeft: 0, p: "8px 32px 32px 28px" }
          : props.draggable && !isLeaf && disabled === false
            ? { p: "8px 32px 32px 58px" }
            : { p: "8px 32px 32px 28px" }
      }
    >
      <Stack sx={{ width: "100%" }}>
        <Stack
          direction="row"
          sx={{
            fontSize: 16,
            fontWeight: 600,
            gap: 6,
            alignItems: "center",
          }}
        >
          {/*sync with ehr/src/CAT/osm/components/OsmOrderDataPanel.tsx:75*/}
          {set && !isLeaf ? (
            <Box />
          ) : parseInt(parentSelectionType) !==
              GroupOrderSelectionType.SelectOne &&
            parseInt(parentSelectionType) !==
              GroupOrderSelectionType.SelectRequired ? (
            <Checkbox
              checked={groupChecked}
              disabled={!selectable || disabled}
              onChange={(e) => onGroupSelect(e, group.id)}
              sx={{
                p: 0,
                m: 0,
                borderRadius: 0,
                "&.Mui-disabled": {
                  background: "#D6D6D6",
                },
              }}
            />
          ) : (
            <Radio
              name={group.id + "-group-radio-button"}
              //value={group.isSelected}
              value={group.id}
              disabled={!selectable || disabled}
              checked={groupChecked}
              onChange={(e) => onGroupRadionClick(e, group.id)}
              sx={{
                p: 0,
                m: 0,
              }}
            />
          )}
          <Typography sx={set ? inSetGroupNameSx : subGroupNameSx}>
            {group.name}
          </Typography>
        </Stack>
        <Stack sx={{ gap: 1, paddingLeft: "35px" }}>
          {group.instruction && (
            <OsmInstructionPanel
              {...props}
              isTop={false}
              setOrGroupStatus={props.status}
              editMode={false}
              data={{ instruction: group.instruction }}
            />
          )}
          {parseInt(selectionType) === GroupOrderSelectionType.SelectOne && (
            <SelectionModeMsg msg="Select One" satified={satified === 1} />
          )}
          {parseInt(selectionType) === GroupOrderSelectionType.MinimalOne && (
            <SelectionModeMsg
              msg="Select a minimum of one"
              satified={satified === 1}
            />
          )}
          {parseInt(group.selectionType) ===
            GroupOrderSelectionType.SelectRequired && (
            <SelectionModeMsg
              msg="Selection Required"
              satified={satified === 1}
            />
          )}
          {group.subIds && (
            <OneGroupDataPanel
              {...props}
              group={group}
              parentGroupIds={parentGroupIds}
              selections={NewSelections}
              isLeaf={isLeaf}
              selectable={set}
              set={set}
            />
          )}
        </Stack>
      </Stack>
    </OsmDataPanel>
  );
};

/**
 * Display top group data
 */
export const OsmTopGroupDataPanel = (props: any) => {
  const { group, inVersionHistory, disabled, emptyMsg, additionalMsg } = props;
  const { osmDispatch, allDataRef } = useOsmContext();

  const allOrders = allDataRef?.current?.orders;
  const allGroups = allDataRef?.current?.groups;

  const satified = checkOrderSelection(
    null,
    group.subIds,
    {},
    group.selectionType
  );
  let index = 0;

  const NoRowsOverlay = () => {
    return <div></div>;
  };

  const removeOrder = (data: any) => {
    const newSubIds = group.subIds.filter(
      (subId: any) => !subId.orderid || subId.orderid !== data.orderid
    );
    osmDispatch({
      type: OsmAction.RemoveOrders,
      data: { subIds: newSubIds },
    });
  };

  const removeGroup = (data: any) => {
    const newSubIds = group.subIds.filter(
      (subId: any) => !subId.group_id || subId.group_id !== data.id
    );
    osmDispatch({
      type: OsmAction.RemoveGroups,
      data: { subIds: newSubIds },
    });
  };

  const dragRow = (params: any) => {
    const movingIdx = params.oldIndex;
    const overIdx = params.targetIndex;
    const rowNeedsToMove = movingIdx !== overIdx;
    if (rowNeedsToMove && !inVersionHistory) {
      const newSubIds = [...group.subIds];
      const dragItemContent = newSubIds[movingIdx];
      newSubIds.splice(movingIdx, 1);
      newSubIds.splice(overIdx, 0, dragItemContent);
      osmDispatch({
        type: OsmAction.AddGroups,
        data: { subIds: newSubIds },
      });
    }
  };

  const columns = useMemo(
    () => [
      {
        ...GRID_REORDER_COL_DEF,
        minWidth: 40,
        maxWidth: 40,
      },
      {
        field: "orderid",
        minWidth: 100,
        flex: 1,
        headerName: "",
        sortable: false,
        renderCell: (params: any) => {
          return <OsmGroupDataRenderer {...params} />;
        },
        cellRendererParams: {
          props,
          removeGroup,
          removeOrder,
          allOrders,
          allGroups,
          group,
        },
      },
    ],
    // eslint-disable-next-line
    [group, inVersionHistory, allOrders, allGroups]
  );

  return (
    <Box data-testid={"osm-top-group-" + group.id} sx={{ overflow: "auto" }}>
      <OsmInstructionPanel
        isTop={true}
        setOrGroupStatus={props.status}
        disabled={disabled}
        inVersionHistory={inVersionHistory}
        data={group}
      />
      {parseInt(group.selectionType) === GroupOrderSelectionType.SelectOne && (
        <SelectionModeMsg msg={"Select One"} satified={satified === 1} />
      )}
      {parseInt(group.selectionType) === GroupOrderSelectionType.MinimalOne && (
        <SelectionModeMsg
          msg={"Select a minimum of one"}
          satified={satified === 1}
        />
      )}
      {parseInt(group.selectionType) ===
        GroupOrderSelectionType.SelectRequired && (
        <SelectionModeMsg msg="Selection Required" satified={satified === 1} />
      )}
      {group?.subIds && Array.isArray(group.subIds) ? (
        <DataGridPro
          sx={{
            "&.MuiDataGrid-root": {
              border: "transparent",
            },
            "& .MuiDataGrid-row": {
              "&:hover, &.Mui-hovered": {
                backgroundColor: "transparent",
              },
            },
            "& .MuiDataGrid-cell": {
              borderBottom: 0,
              padding: "2px 0",
              "& .MuiDataGrid-rowReorderCell--draggable": {
                zIndex: 1000,
                color: "#6d6d6d",
                alignSelf: "flex-start",
                padding: "16px 0",
              },
              "&:focus-within": {
                outline: "none",
              },
            },
            "& .MuiDataGrid-columnHeaders": {
              borderColor: "transparent",
            },
          }}
          autoHeight
          columns={columns}
          rows={group?.subIds ? group.subIds : []}
          getRowId={(row: any) =>
            row["orderid"] ? row["orderid"] : row["group_id"] + "" + index++
          }
          getRowHeight={() => "auto"}
          isRowSelectable={() => false}
          columnHeaderHeight={0}
          disableColumnResize={true}
          disableColumnMenu
          hideFooter
          rowReordering={!inVersionHistory && disabled === false}
          onRowOrderChange={dragRow}
          slots={{
            noRowsOverlay: NoRowsOverlay,
          }}
        />
      ) : (
        <OsmEmptyDataMsgBox messages={emptyMsg} additionalMsg={additionalMsg} />
      )}
    </Box>
  );
};

export default OsmGroupDataPanel;
