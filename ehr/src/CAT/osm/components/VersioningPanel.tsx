import { useEffect, useRef } from "react";
import { Box, Stack, Typography } from "@mui/material";
import { useVersionQuery } from "../hooks/useOrderSetQuery";
import {
  byUpdateDateAsc,
  parseVersionData,
} from "../util/OsmVersionDataHelper";
import { useOsmContext } from "../contexts/OsmContext";
import { OsmAction, OsmToolType } from "../OsmConstants";
import OneVersionPanel from "./OneVersionPanel";
import PropTypes from "prop-types";
import { useOsmPageContext } from "../contexts/OsmPageContext";

const stackCss = {
  width: 300,
  height: "79vh",
  mx: 1,
  overflowY: "auto",
  backgroundColor: "#FFFFFF",
};

type TVersioningPanelProps = {
  dataType: OsmToolType;
  dataId: string;
  currentVersion: string;
  tabId: string;
};

/**
 * Versioning Panel to display Order Set/Group Versions.
 */
const VersioningPanel = (props: TVersioningPanelProps) => {
  const { dataType, dataId, currentVersion, tabId } = props;
  const { pageStatusRef } = useOsmPageContext();
  const { osmDispatch } = useOsmContext();

  const onSelect = (versionData: any) => {
    osmDispatch({
      type: OsmAction.ShowVersionPage,
      name:
        dataType === OsmToolType.Set
          ? versionData.orderSetName
          : versionData.orderGroupName,
      data: versionData,
    });
  };

  const versionsList = useVersionQuery(
    { dataType, dataId },
    parseVersionData,
    null
  ).data.sort(byUpdateDateAsc);

  const versionListDataRef = useRef([]);

  useEffect(() => {
    if (versionsList) {
      versionListDataRef.current = versionsList;
    }
    return () => {
      const [currentVersionDataRef] = versionListDataRef.current || null;
      if (currentVersionDataRef && pageStatusRef?.current?.[tabId]) {
        // @todo sync with TOsmDataType
        const currentData = currentVersionDataRef as {
          orderSetName: string;
          orderGroupName: string;
        };
        osmDispatch({
          type: OsmAction.Unknown,
          // @ts-ignore
          name:
            dataType === OsmToolType.Set
              ? currentData.orderSetName
              : currentData.orderGroupName,
          data: currentData,
        });
      }
    };
  }, [versionsList]); // eslint-disable-line

  return (
    <Stack sx={stackCss}>
      <Box sx={{ maxHeight: "56px", py: 4, px: 4, background: "#E8EFF7" }}>
        <Typography sx={{ fontSize: 18, fontWeight: 700 }}>
          Version History
        </Typography>
      </Box>
      {versionsList.map((versionData: any, index: any) => (
        <OneVersionPanel
          key={index}
          versionData={versionData}
          onSelect={() => onSelect(versionData)}
          selectedVersion={currentVersion === versionData.version}
          currentVersion={currentVersion}
        />
      ))}
    </Stack>
  );
};

VersioningPanel.propTypes = {
  dataType: PropTypes.oneOf([OsmToolType.Set, OsmToolType.Group]),
  dataId: PropTypes.string,
  currentVersion: PropTypes.string,
};

export default VersioningPanel;
