import React, { useCallback, useEffect, useState } from "react";
import { Box, InputAdornment, TextField, Typography } from "@mui/material";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { styled } from "@mui/material/styles";
import OrderSetsIcon from "@cci-monorepo/oe-shared/src/assets/osm/icn_Order_Sets.svg";
import OrderGroupsIcon from "@cci-monorepo/oe-shared/src/assets/osm/icn_draft_Order_Group.svg";
import ChipOSM from "@cci-monorepo/CAT/osm/components/ui/ChipOSM";
import ResetIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Reset.svg";
import PencilIcon from "@cci-monorepo/oe-shared/src/assets/Icn_Edit.svg";
import WarningIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Warning_Large.svg";
import { useCheckNameQuery } from "../hooks/useOsmUtilQuery";
import { useOsmContext } from "../contexts/OsmContext";
import { useOsmPageContext } from "../contexts/OsmPageContext";
import { OrderSetStatus, OsmAction, OsmToolType } from "../OsmConstants";
import { TOsmDataType } from "@cci-monorepo/CAT/osm/OsmPropTypes";
import { useDebounce } from "../../../Pld/components/common/DataUtils";

const maxNameLength = 128;
const minNameLength = 3;

const StyledBox = styled(Box)(() => ({
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  gap: "8px",
  padding: "8px 16px",
}));

export type TNameQueryOptions = {
  name: string;
  ignoreCase: string;
  isValid: boolean;
};

type TOsmOrderSetNamePanelProps = {
  data: TOsmDataType;
  disabled: boolean;
  name?: string;
  isNew?: boolean;
  status: string;
  setEditError: (error: boolean) => void;
  inVersionHistory: boolean;
  version: string;
  dataType: string;
  tabId: string;
};

/**
 * Order Set/Group name panel. It may include
 *  - icon area: Order icon and status icon (Draft/Retired)
 *  - name area: the Order Set/Group name field
 *  - edit icon: icon button to enable edit name
 *  - change info: such as "Unpublished Chageds"
 *  - publish time: label of publish time, "Published 12:05 2/5/2025"
 *
 * @param props
 * @param ref
 */
const OsmOrderSetNamePanel = (
  {
    data,
    disabled,
    name,
    isNew = false,
    status,
    setEditError,
    inVersionHistory,
    version,
    dataType,
    tabId,
  }: TOsmOrderSetNamePanelProps,
  ref: any
) => {
  const { osmDispatch } = useOsmContext();
  const { updatePageStatus } = useOsmPageContext();
  const [editMode, setEditMode] = useState(false);
  const [currentName, setCurrentName] = useState<string | undefined>("");

  const onHandleEditMode = () => {
    if ((isNew && nameCounter > 0) || (editMode && nameCounter > 0)) return;
    setEditMode(!editMode);
  };
  const preparedName = currentName?.trim() || "";
  const nameQueryOptions: TNameQueryOptions = {
    name: preparedName,
    ignoreCase: "1",
    isValid: (editMode || isNew) && preparedName.length >= minNameLength,
  };

  const nameCounter = useCheckNameQuery(nameQueryOptions)?.data;

  const error =
    dataType === OsmToolType.Set
      ? (isNew || editMode) &&
        (nameCounter > 0 || preparedName.length < minNameLength)
      : (isNew || editMode) &&
        nameCounter > 0 &&
        preparedName !== data.orderGroupName;

  useEffect(() => {
    updatePageStatus(tabId, { error });
    setEditError(error);
  }, [error, setEditError, tabId, updatePageStatus, setEditMode]);

  useEffect(() => {
    setCurrentName(name);
  }, [name]);

  const handleNameChange = React.useCallback(
    (val: string) => {
      osmDispatch({
        type: OsmAction.UpdateData,
        data: { ...data, name: val },
      });
    },
    [osmDispatch]
  );

  const debouncedChange = useDebounce(() => {
    handleNameChange(currentName ? currentName : "");
  }, 300);

  const onHandleNameChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const name = event.target.value;
      setCurrentName(name);
      debouncedChange();
    },
    [data]
  );

  const backgroundColor = () => {
    let bgColor;

    if (inVersionHistory) {
      switch (status) {
        case OrderSetStatus.Retired:
          bgColor = "#FDF9E4";
          break;

        case OrderSetStatus.Draft:
          bgColor = "#F9E7E8";
          break;

        default:
          bgColor = "#E8EFF7";
      }
    } else {
      switch (status) {
        case OrderSetStatus.Retired:
          bgColor = "#FDF9E4";
          break;

        default:
          bgColor = "#E8EFF7";
      }
    }
    return bgColor;
  };

  const nameBackgroundColor = () => {
    if (!currentName || editMode || isNew) return "#FFFFFF";
    backgroundColor();
  };

  const handleClickAway = () => {
    if (error) return;
    setEditMode(false);
  };

  return (
    <ClickAwayListener mouseEvent="onMouseDown" onClickAway={handleClickAway}>
      <Box sx={{ backgroundColor, display: "flex", flexDirection: "column" }}>
        <StyledBox>
          {dataType === OsmToolType.Set ? (
            <img src={OrderSetsIcon} alt="Order Set Icon" />
          ) : (
            <img src={OrderGroupsIcon} alt="Order Group Icon" />
          )}
          {(status === OrderSetStatus.Draft ||
            status === OrderSetStatus.Review ||
            status === OrderSetStatus.Retired) && (
            <ChipOSM
              label={status === OrderSetStatus.Review ? "Draft" : status}
              size="small"
            />
          )}
          <TextField
            value={
              inVersionHistory && version
                ? `(Version ${version}) ${currentName}`
                : status === OrderSetStatus.Retired && !editMode
                  ? currentName + " (Retired)"
                  : currentName
            }
            inputRef={ref}
            error={error}
            placeholder={`Enter Order ${
              dataType === OsmToolType.Set ? "Set" : "Group"
            } Name`}
            sx={{
              background: nameBackgroundColor(),
              border: isNew || editMode ? "1px" : "none",
              borderRadius: isNew || editMode ? "4px" : "0",
              pl: "none",
              width: "50%",
              "& .MuiOutlinedInput-input": {
                padding: 2,
                height: 24,
                fontSize: 18,
                fontWeight: 500,
              },
              "& fieldset": { border: currentName ? "none" : "" },
              "& .MuiInputAdornment-root": {
                visibility: editMode || isNew ? "visible" : "hidden",
              },
              ...(disabled === false && {
                "&:hover .MuiOutlinedInput-input": {
                  background: isNew || editMode ? "#FFFFFF" : "#0000001A",
                  border: "1px",
                  borderRadius: "4px",
                },
                "&:hover .MuiInputAdornment-root": {
                  visibility: "visible",
                  cursor: "pointer",
                },
                "& .MuiOutlinedInput-root": {
                  "&.Mui-error": {
                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "1px solid #cf4c35",
                    },
                  },
                },
              }),
            }}
            onChange={onHandleNameChange}
            autoFocus={true}
            inputProps={{
              maxLength: maxNameLength,
              readOnly: !editMode && !isNew,
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment onClick={onHandleEditMode} position="end">
                  {!editMode && !isNew && (
                    <img
                      style={{ height: "24px", width: "24px" }}
                      src={PencilIcon}
                      alt="Edit Name Icon"
                    />
                  )}
                  {(editMode || isNew) && (
                    <img
                      style={{ height: "24px", width: "24px" }}
                      src={ResetIcon}
                      alt="Reset Name Icon"
                    />
                  )}
                </InputAdornment>
              ),
            }}
          />
          <Box
            sx={{
              fontSize: 14,
              width: "50%",
              gap: 4,
              display: "flex",
              alignItems: "center",
              justifyContent: "end",
            }}
          >
            {!isNew &&
              editMode &&
              dataType === OsmToolType.Set &&
              (status === OrderSetStatus.Review ||
                status === OrderSetStatus.Draft) && (
                <>
                  <img
                    style={{ height: "24px", width: "24px" }}
                    src={WarningIcon}
                    alt="Warning Icon"
                  />
                  <span>Unpublished Changes</span>
                </>
              )}
            <Box sx={{ display: "flex", flexDirection: "column" }}>
              {dataType === OsmToolType.Group && data?.id && (
                <span
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginRight: "10px",
                    justifyContent: "start",
                  }}
                >
                  OGM ID:{" "}
                  <span style={{ fontWeight: 600, marginLeft: "10px" }}>
                    {data.group_id}
                  </span>
                </span>
              )}
              {dataType === OsmToolType.Set && data?.id && (
                <span
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginRight: "10px",
                    justifyContent: "start",
                  }}
                >
                  OSM ID:{" "}
                  <span style={{ fontWeight: 600, marginLeft: "10px" }}>
                    {data.id}
                  </span>
                </span>
              )}
              {status === OrderSetStatus.Published ||
              status === OrderSetStatus.Retired ? (
                <Box
                  sx={{
                    gap: 1,
                    display: "flex",
                    visibility: `${
                      status === OrderSetStatus.Published ||
                      status === OrderSetStatus.Retired
                        ? "visible"
                        : "hidden"
                    }`,
                  }}
                >
                  <span>
                    {status === OrderSetStatus.Published
                      ? "Published: "
                      : "Retired: "}
                  </span>
                  <span style={{ fontWeight: 600 }}>
                    {Cci.util.DateTime.serverSecsToTimeStr(
                      Cci.util.DateTime.timeStrToServerSecs(
                        data?.updateDate,
                        "HH:mm DD MMM YYYY"
                      ),
                      "HH:mm MM/DD/YYYY"
                    )}
                  </span>
                  <span style={{ fontWeight: 600 }}>
                    {`(Version ${version})`}
                  </span>
                </Box>
              ) : (
                version && (
                  <Box
                    sx={{
                      gap: 1,
                      display: "flex",
                    }}
                  >
                    <span>{"Draft:"}</span>
                    <span style={{ marginLeft: "10px", fontWeight: 600 }}>
                      {`(Version ${version})`}
                    </span>
                  </Box>
                )
              )}
            </Box>
          </Box>
        </StyledBox>
        {error && (
          <Typography
            sx={{
              color: "red",
              paddingLeft:
                status === OrderSetStatus.Draft ||
                status === OrderSetStatus.Retired
                  ? "120px"
                  : "48px",
              paddingBottom: "8px",
            }}
            variant="caption"
            gutterBottom
          >
            {dataType === OsmToolType.Set
              ? `An Order Group or Order Set with the same name already exists.
            Please choose another name. `
              : `Duplicate Order Group name , Please choose another name.`}
          </Typography>
        )}
      </Box>
    </ClickAwayListener>
  );
};

const ForwardOsmOrderSetNamePanel = React.forwardRef(OsmOrderSetNamePanel);

export default ForwardOsmOrderSetNamePanel;
