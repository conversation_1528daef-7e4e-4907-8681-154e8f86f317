import React, { useState } from "react";
import { styled } from "@mui/material/styles";
import { Box, Stack, TextField, Typography } from "@mui/material";
import OsmSearchTerms from "../common/OsmSearchTerms";
import OsmSelect from "../common/OsmSelect";
import CatSwitch from "../../common/CatSwitch";
import CheckedMark from "@cci-monorepo/oe-shared/src/assets/radio_only_checked.svg";
import {
  OrderSetStatus,
  OsmAction,
  GroupOrderSelectionType,
  OsmToolType,
} from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";
import { CciRequiredFieldMarker as RequiredFieldMarker } from "@cci/mui-components";
import { useDebounce } from "../../../Pld/components/common/DataUtils";
export const StyledLabel = styled(Typography)(() => ({
  fontSize: 14,
  fontWeight: 500,
  color: "#11181F",
}));

const StyledBox = styled(Box)(() => ({
  height: "auto",
  width: "100%",
  padding: "4px",
  border: "1px solid #E1E1E1",
  borderRadius: "4px",
  marginBottom: "8px",
}));

// selection_type in groups table
const groupSelectionModeOptions = [
  {
    value: GroupOrderSelectionType.Multiple,
    display: "Multi Select",
  },
  {
    value: GroupOrderSelectionType.MinimalOne,
    display: "Multi Select (Selection Required)",
  },
  {
    value: GroupOrderSelectionType.SelectOne,
    display: "Single Select",
  },
  {
    value: GroupOrderSelectionType.SelectRequired,
    display: "Single Select (Selection Required)",
  },
];

type OsmDetailSettingPanelProps = {
  settingType: string;
  data: any;
  inVersionHistory: boolean;
  disabled: boolean;
};

const OsmDetailSettingPanel = (props: OsmDetailSettingPanelProps) => {
  const { settingType, data, inVersionHistory, disabled } = props;
  const selectType =
    data?.selectionType || data.selectionType === 0 ? data.selectionType : 3;
  const [selectionMode, setSelectionMode] = useState(selectType);
  const defaultExpand = data?.defaultExpanded === "1" ? true : false;
  const [defaultExpanded, setDefaultExplanded] = useState(defaultExpand);
  const searchTermsParsed =
    data?.searchTerms && JSON.parse(data.searchTerms)
      ? JSON.parse(data.searchTerms)
      : [];
  const [searchTerms, setSearchTerms] = useState(searchTermsParsed);
  const [versionNote, setVersionNote] = useState(data.versionNote);
  const [inReview, setInReview] = useState(
    data.status == OrderSetStatus.Review
  );

  const { osmDispatch } = useOsmContext();

  React.useEffect(() => {
    const selectType =
      data?.selectionType || data.selectionType === 0 ? data.selectionType : 3;
    setSelectionMode(selectType);
    const defaultExpand = data?.defaultExpanded === "1" ? true : false;
    setDefaultExplanded(defaultExpand);
    const searchTermsParsed =
      data?.searchTerms && JSON.parse(data.searchTerms)
        ? JSON.parse(data.searchTerms)
        : [];
    setSearchTerms(searchTermsParsed);
    setVersionNote(data.versionNote);
  }, [data]);

  const handleSelectionModeChange = (event: any) => {
    const newSelectionType = event.target.value;
    osmDispatch({
      type: OsmAction.UpdateData,
      data: { ...data, selectionType: newSelectionType },
    });
    setSelectionMode(newSelectionType);
  };

  const handleSwitch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checkStatus = e.currentTarget.checked;
    osmDispatch({
      type: OsmAction.UpdateSearchTerm,
      data: { ...data, defaultExpanded: checkStatus ? "1" : "0" },
    });
    setDefaultExplanded(checkStatus);
  };

  const handleChange = (values: any) => {
    const newSearchTerms = JSON.stringify(values);
    osmDispatch({
      type: OsmAction.UpdateSearchTerm,
      data: { ...data, searchTerms: newSearchTerms },
    });
    setSearchTerms(values);
  };

  const debouncedChange = useDebounce(() => {
    osmDispatch({
      type: OsmAction.UpdateData,
      data: { ...data, versionNote: versionNote },
    });
  }, 500);

  const handleVersionNotes = (e: any) => {
    const val = e.target.value;
    setVersionNote(val);
    debouncedChange();
  };

  const handleInReview = (event: any) => {
    const inReviewStatus = event.currentTarget.checked;
    setInReview(inReviewStatus);
    if (inReviewStatus) {
      osmDispatch({
        type: OsmAction.ReviewSet,
        data: data,
      });
    } else {
      osmDispatch({
        type: OsmAction.DoConvertToDraft,
        data: data,
      });
    }
  };

  return (
    <Stack sx={{ pb: 2 }}>
      {settingType === OsmToolType.Set &&
        (data?.status === OrderSetStatus.Draft ||
          data?.status === OrderSetStatus.Review) && (
          <StyledBox>
            <StyledLabel>In Review</StyledLabel>
            <Typography
              sx={{
                marginTop: "16px",
                font: "normal 400 12px Roboto",
                color: "#7C7C7C",
                marginBottom: "16px",
              }}
            >
              Converts document to read-only until content has been approved.
            </Typography>
            <CatSwitch
              disabled={
                inVersionHistory ||
                (data?.status != OrderSetStatus.Draft &&
                  data?.status != OrderSetStatus.Review)
              }
              checkedIcon={<img src={CheckedMark} alt="check-mark-icon" />}
              onChange={handleInReview}
              checked={data?.status === OrderSetStatus.Review && inReview}
            />
          </StyledBox>
        )}
      {settingType === OsmToolType.Group && (
        <StyledBox>
          <Box sx={{ display: "flex", flexDirection: "row", height: 40 }}>
            <StyledLabel>Selection Mode</StyledLabel>
            <RequiredFieldMarker />
          </Box>
          <Typography
            sx={{
              font: "normal 400 12px Roboto",
              color: "#7C7C7C",
              marginBottom: "5px",
            }}
          >
            Adjusts selection mode:
          </Typography>
          <OsmSelect
            // @ts-ignore TS(2322): Type '{ value: any; name: string; onChange: (e: an... Remove this comment to see the full error message
            value={selectionMode}
            name="availableSelectionMode"
            onChange={(e: React.MouseEvent<HTMLElement>) => {
              handleSelectionModeChange(e);
            }}
            style={{
              width: 324,
              height: 32,
              fontSize: "14px",
            }}
            options={groupSelectionModeOptions}
            disabled={inVersionHistory || disabled}
          />
          <Stack
            direction="column"
            alignItems="left"
            sx={{
              height: "auto",
              display: "flex",
              flexDirection: "column",
              pt: 4,
            }}
          >
            <StyledLabel>Expanded by Default</StyledLabel>
            <Typography
              sx={{
                marginTop: "16px",
                font: "normal 400 12px Roboto",
                color: "#7C7C7C",
                marginBottom: "16px",
              }}
            >
              Order group is expanded when no orders are pre-selected.
            </Typography>
            <CatSwitch
              disabled={disabled}
              checkedIcon={<img src={CheckedMark} alt="check-mark-icon" />}
              onChange={handleSwitch}
              checked={defaultExpanded}
            />
          </Stack>
        </StyledBox>
      )}
      <StyledBox>
        <StyledLabel style={{ paddingTop: "0" }}>
          Additional Search Terms
        </StyledLabel>
        <Typography
          sx={{
            marginTop: "16px",
            font: "normal 400 12px Roboto",
            color: "#7C7C7C",
            marginBottom: "16px",
          }}
        >
          Entered strings are used to search for this order set in Order Entry.
          Minimum 3 charcters.
        </Typography>
        <OsmSearchTerms
          disabled={disabled}
          placeHolder={"Enter synonym, abbreviation, or term"}
          onChange={handleChange}
          terms={searchTerms}
        />
      </StyledBox>
      <StyledBox>
        <StyledLabel>Version Notes</StyledLabel>
        <TextField
          disabled={disabled}
          value={versionNote}
          variant="outlined"
          placeholder="Notifies users of this Order Set"
          multiline={true}
          rows={4}
          sx={{ width: "100%" }}
          onChange={handleVersionNotes}
        />
      </StyledBox>
    </Stack>
  );
};

export default OsmDetailSettingPanel;
