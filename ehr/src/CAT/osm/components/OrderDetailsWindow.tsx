import { cloneElement, useCallback, useState } from "react";
import { CciDialog } from "@cci/mui-components";
import OrderDetailsToolbar from "./OrderDetailsToolbar";

const OrderDetailsWindow = ({
  orderSetId,
  windowOpen,
  setWindowOpen,
  form,
  formData,
  currentPayload,
  dirty,
  setDirty,
  onChange,
  handleFormCycle,
  editedOrders,
  setEditedOrders,
}: any) => {
  const [cancelChangesDialogOpen, setCancelChangesDialogOpen] = useState(false);

  const handleOrderDetailsClose = () => {
    if (dirty && !cancelChangesDialogOpen) {
      setCancelChangesDialogOpen(true);
    } else {
      handleClose();
    }
  };

  const handleClose = useCallback(() => {
    setWindowOpen(false);
    setDirty(false);
    setEditedOrders([]);
    setCancelChangesDialogOpen(false);
  }, [setWindowOpen, setDirty, setEditedOrders, setCancelChangesDialogOpen]);

  const orderDetailsDialogContent = () => {
    return (
      <>
        <OrderDetailsToolbar
          orderSetId={orderSetId}
          currentPayload={formData}
          dirty={dirty}
          editedOrders={editedOrders}
          setEditedOrders={setEditedOrders}
          handleFormCycle={handleFormCycle}
          cancelChangesDialogOpen={cancelChangesDialogOpen}
          setCancelChangesDialogOpen={setCancelChangesDialogOpen}
          handleOrderDetailsClose={handleOrderDetailsClose}
          handleClose={handleClose}
        />
        {/* The toolbar position is absolute to remain fixed while scrolling.
        We need this dummy toolbar to avoid the toolbar overlapping
        with the form content. */}
        <OrderDetailsToolbar currentPayload={currentPayload} hidden={true} />
        {/* window open is needed to reset the form payload on window close*/}
        {form ? (
          cloneElement(form, {
            dirty,
            currentPayload,
            onChange,
          })
        ) : (
          <></>
        )}
      </>
    );
  };

  return (
    <CciDialog
      open={windowOpen}
      setOpen={handleOrderDetailsClose}
      title={
        "Order Details - " +
        (formData.current?.name ? formData.current?.name : "")
      }
      content={orderDetailsDialogContent()}
      removehrLine
      draggable
      disableEnforceFocus
      sx={{
        left: "auto",
        bottom: "auto",
        "& .MuiDialog-paper": {
          width: "800px",
          minHeight: "90vh",
          maxHeight: "90vh",
          overflowY: "auto",
          backgroundColor: "#F2F2F2",
          boxShadow:
            "0px -4px 20px 0px rgba(0, 0, 0, 0.15), 0px 0px 40px 0px rgba(0, 0, 0, 0.20)",
        },
        "& .MuiModal-backdrop": {
          backgroundColor: "rgba(0, 0, 0, 0.25)",
        },
        "& .MuiDialogActions-root": { display: "none" },
        "& .MuiDialogContent-root": { padding: 0, overflowY: "scroll" },
      }}
    />
  );
};

export default OrderDetailsWindow;
