import React from "react";
import { Box, Typography, Link } from "@mui/material";
import { useAtom, useSetAtom, useAtomValue } from "jotai";
import OsmToolbar from "../common/OsmToolbar";
import { OsmCciButton, OsmWhiteButton } from "../common/OsmButtons";
import { OsmWhiteMenuButton } from "../common/OsmMenuButton";
import { OrderSetStatus, OsmAction, OsmToolType } from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";
import { useOsmPageContext } from "../contexts/OsmPageContext";
import { OsmSearchField } from "../common/OsmSearchField";
import {
  osmWeblinksExpandedAtom,
  osmCurrentIndexAtom,
  osmUndoArrayAtom,
  osmUpdateAtom,
} from "../contexts/OsmAtom";
import WarningIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Warning_Large.svg";
import { ReactComponent as OrderIcon } from "@cci-monorepo/oe-shared/src/assets/osm/Icn_Orders.svg";
import { ReactComponent as OrderGroupIcon } from "@cci-monorepo/oe-shared/src/assets/osm/icn_Order_Group.svg";

import {
  DialogVariants,
  useAlert,
} from "@cci-monorepo/Psat/common/layouts/AdminLayoutA/context/AlertContext";
import {
  isChangeStatusPermission,
  isCreateNewPermission,
  isEditPermission,
} from "@cci-monorepo/CAT/common/Permissions";

/**
 * The toolbar for New/Edit panel (Set/Group) in Order Set Management Tool
 * @param {Object} props
 *  - toolType: the type of data component: "set" or "group"
 *  - status: the status of Set/Group, "Draft", "Review", "Published", "Retired"
 */
const OsmOrderSetToolbar = (props: any) => {
  const {
    toolType,
    status,
    handleOpenPopup,
    editError,
    data,
    versionHistory,
    setVersionHistory,
    tabId,
  } = props;
  const qsPopperAnchorRef = React.useRef();
  const { pageStatusRef } = useOsmPageContext();
  const { osmDispatch } = useOsmContext();
  const { removeCurrentPage } = useOsmPageContext();
  const createAlert = useAlert();
  const setOsmWeblinksExpanded = useSetAtom(osmWeblinksExpandedAtom);
  const [openQS, setOpenQS] = React.useState(false);
  const [currentIndex, setOsmCurrentIndex] = useAtom(osmCurrentIndexAtom);
  const osmUndoArray = useAtomValue(osmUndoArrayAtom);
  const [osmUpdate, setNeedUpdate] = useAtom(osmUpdateAtom);
  const [searchVal, setSearchValue] = React.useState("");
  const weblinks = data?.weblinks;
  const hasId = data?.id;
  let noData = false;
  if (toolType === OsmToolType.Set) {
    noData = !data?.groupIds || data.groupIds.length < 1;
  } else if (toolType === OsmToolType.Group) {
    noData = !data?.subIds || data.subIds.length < 1;
  }

  const handleButtonClick = (event: any, btnType: OsmAction) => {
    switch (btnType) {
      case OsmAction.BrowseOrders:
      case OsmAction.OrderGroups:
      case OsmAction.RetireSet:
      case OsmAction.RetireGroup:
      case OsmAction.ConvertToDraft:
      case OsmAction.PublishSet:
      case OsmAction.PublishGroup:
        handleOpenPopup(btnType);
        osmDispatch({
          type: btnType,
        });
        break;

      case OsmAction.Cancel:
        osmDispatch({
          type: OsmAction.RemoveTab,
        });
        removeCurrentPage();
        break;

      case OsmAction.SaveSet:
        osmDispatch({
          type: btnType,
        });
        break;
      case OsmAction.Save:
        osmDispatch({
          type: btnType,
        });
        break;
      case OsmAction.SaveAndClose:
        osmDispatch({
          type: btnType,
        });
        break;

      // case OsmAction.ActivateOrderSet:
      case OsmAction.ActivateOrderGroup:
        osmDispatch({
          type: btnType,
        });
        break;
      case OsmAction.PublishSet:
      case OsmAction.PublishGroup:
        osmDispatch({
          type: btnType,
        });
        break;

      case OsmAction.VersionHistory:
        setVersionHistory(true);
        break;

      case OsmAction.AddWeblink:
        const tmpLinks = data?.weblinks ?? [];
        setOsmWeblinksExpanded(true);
        osmDispatch({
          type: btnType,
          data: {
            weblinks: [...tmpLinks, { isNew: true, display: "", url: "" }],
          },
        });
        break;

      case OsmAction.DuplicateSet:
        osmDispatch({
          type: btnType,
          name: data.name,
          data: {
            ...data,
            id: undefined,
            name: undefined,
            orderSetName: undefined,
            status: OrderSetStatus.Draft,
            version: undefined,
          },
        });
        break;
      case OsmAction.DuplicateGroup:
        osmDispatch({
          type: btnType,
          name: data.name,
          data: {
            ...data,
            id: undefined,
            name: undefined,
            orderSetName: undefined,
            status: OrderSetStatus.Draft,
            version: undefined,
          },
        });
        break;

      case OsmAction.CreateNewVersion:
        osmDispatch({
          type: btnType,
          data: {
            ...data,
            preActiveVersion: data,
            status: OrderSetStatus.Draft,
            version: undefined,
          },
        });
        break;

      case OsmAction.Print:
      case OsmAction.ExportToPdf:
        setNeedUpdate(!osmUpdate);
        osmDispatch({
          type: btnType,
        });
        break;
      case OsmAction.Undo:
        currentIndex[tabId].current = currentIndex[tabId].current + 1;
        setOsmCurrentIndex(currentIndex);
        setNeedUpdate(!osmUpdate);
        osmDispatch({
          type: btnType,
        });
        break;
      case OsmAction.Redo:
        currentIndex[tabId].current = currentIndex[tabId].current - 1;
        setOsmCurrentIndex(currentIndex);
        setNeedUpdate(!osmUpdate);
        osmDispatch({
          type: btnType,
        });
        break;

      default:
        createAlert({
          variant: DialogVariants.INFO,
          msg: "Not implemented yet.",
        });
    }
  };

  const handleCloseVersionHistory = () => {
    setVersionHistory(false);
  };

  const handleViewLatestVersion = () => {
    osmDispatch({
      type: OsmAction.EditExistGroup,
      name: data.latestVersion.name,
      data: data.latestVersion,
    });
  };

  const onOpenQuickSearch = React.useCallback(() => {
    setOpenQS(true);
  }, [setOpenQS]);

  const onCloseQuickSearch = React.useCallback(() => {
    setOpenQS(false);
  }, [setOpenQS]);

  const onSearchFilter = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
  };

  return (
    <>
      {versionHistory && (
        <OsmToolbar sx={{ display: "flex", flexGrow: 1, minWidth: 1445 }}>
          <OsmCciButton
            buttonName={"Close Version History"}
            onClick={handleCloseVersionHistory}
          />
        </OsmToolbar>
      )}
      {!versionHistory && (
        <OsmToolbar sx={{ display: "flex", flexGrow: 1, minWidth: 1445 }}>
          <Box
            sx={{
              display: "flex",
              flexGrow: 1,
              pl: "14px",
            }}
          >
            {isEditPermission && (
              <Box sx={{ display: "flex", flexGrow: 1 }}>
                {toolType === "group" && status === OrderSetStatus.Draft && (
                  <Box
                    sx={{
                      display: "flex",
                      pl: "10px",
                      direction: "row",
                      alignItems: "center",
                    }}
                    ref={qsPopperAnchorRef}
                  >
                    <OsmSearchField
                      placeHolder={"Search to Add Orders"}
                      currentData={data}
                      disabled={false}
                      open={openQS}
                      onClose={onCloseQuickSearch}
                      onOpen={onOpenQuickSearch}
                      onChange={onSearchFilter}
                      value={searchVal}
                      popperAnchorRef={qsPopperAnchorRef}
                    />
                    <OsmWhiteButton
                      buttonName={"Browse"}
                      startIcon={<OrderIcon />}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.BrowseOrders)
                      }
                      disabled={status === OrderSetStatus.Retired}
                    />
                  </Box>
                )}
                {status === OrderSetStatus.Draft ||
                status === OrderSetStatus.Review ? (
                  <>
                    <OsmWhiteButton
                      buttonName={"Order Groups"}
                      startIcon={<OrderGroupIcon />}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.OrderGroups)
                      }
                    />
                    {toolType === OsmToolType.Set && (
                      <>
                        <OsmWhiteButton
                          buttonName={"Add Weblink"}
                          onClick={(e: React.MouseEvent<HTMLElement>) =>
                            handleButtonClick(e, OsmAction.AddWeblink)
                          }
                          disabled={
                            (status === weblinks) !== undefined &&
                            Array.isArray(weblinks) &&
                            weblinks.length >= 3
                          }
                        />
                      </>
                    )}
                    <OsmWhiteButton
                      buttonName={"Undo"}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.Undo)
                      }
                      disabled={
                        !currentIndex.hasOwnProperty(tabId) ||
                        currentIndex[tabId]?.current ===
                          osmUndoArray[tabId]?.length - 1
                      }
                    />
                    <OsmWhiteButton
                      buttonName={"Redo"}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.Redo)
                      }
                      disabled={
                        !currentIndex.hasOwnProperty(tabId) ||
                        currentIndex[tabId]?.current === 0
                      }
                    />
                    <OsmWhiteMenuButton
                      buttonName={"More... "}
                      menuOptions={[
                        {
                          name: "Version History",
                          type: OsmAction.VersionHistory,
                          disabled: !hasId,
                        },
                        {
                          name:
                            toolType === OsmToolType.Set
                              ? "Retire Set"
                              : "Retire Group",
                          type:
                            toolType === OsmToolType.Set
                              ? OsmAction.RetireSet
                              : OsmAction.RetireGroup,
                          hidden:
                            status !== OrderSetStatus.Draft ||
                            status !== OrderSetStatus.Review ||
                            isChangeStatusPermission === false,
                          disabled: editError,
                        },
                        /* wait for detail PRD
                        {
                          name: "Export to .docx",
                          type: OsmAction.ExportToDocx,
                          disabled: editError,
                        },
                        */
                        {
                          name: "Export to .pdf",
                          type: OsmAction.ExportToPdf,
                          disabled: editError,
                        },
                        /* wait for detail PRD
                        {
                          name: "Export to .xlsx",
                          type: OsmAction.ExportToXlsx,
                          disabled: editError,
                        },
                        */
                        {
                          name: "Print",
                          type: OsmAction.Print,
                          disabled: editError,
                        },
                      ]}
                      menuHandler={handleButtonClick}
                      sx={{
                        "& .MuiButton-icon": {
                          pr: 2,
                        },
                      }}
                    />
                  </>
                ) : (
                  <>
                    <OsmWhiteButton
                      buttonName={"Version History"}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.VersionHistory)
                      }
                      disabled={!hasId}
                    />
                    {isCreateNewPermission === true &&
                      status !== OrderSetStatus.Retired && (
                        <OsmWhiteButton
                          buttonName={
                            toolType === OsmToolType.Set
                              ? "Duplicate Set"
                              : "Duplicate Group"
                          }
                          onClick={(e: React.MouseEvent<HTMLElement>) =>
                            toolType === OsmToolType.Set
                              ? handleButtonClick(e, OsmAction.DuplicateSet)
                              : handleButtonClick(e, OsmAction.DuplicateGroup)
                          }
                          disabled={editError || !hasId}
                        />
                      )}
                    <OsmWhiteButton
                      buttonName={"Export"}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.ExportToPdf)
                      }
                      disabled={editError}
                    />
                    <OsmWhiteButton
                      buttonName={"Print"}
                      onClick={(e: React.MouseEvent<HTMLElement>) =>
                        handleButtonClick(e, OsmAction.Print)
                      }
                      disabled={editError}
                    />
                    {isChangeStatusPermission &&
                      status != OrderSetStatus.Retired && (
                        <OsmWhiteButton
                          buttonName={"Retire"}
                          onClick={(e: React.MouseEvent<HTMLElement>) =>
                            handleButtonClick(
                              e,
                              toolType === OsmToolType.Set
                                ? OsmAction.RetireSet
                                : OsmAction.RetireGroup
                            )
                          }
                          disabled={editError}
                        />
                      )}
                    {data?.latestVersion && (
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <img
                          src={WarningIcon}
                          alt="icon"
                          style={{
                            marginLeft: 16,
                            marginRight: 4,
                            height: "25px",
                            width: "25px",
                          }}
                        />
                        <Typography
                          sx={{
                            font: "normal 700 14px Roboto",
                            color: "#000",
                            marginRight: "10px",
                          }}
                        >
                          A new version of this order group is currently in
                          Draft Mode -
                        </Typography>
                        <Link
                          component="button"
                          variant="body2"
                          onClick={handleViewLatestVersion}
                        >
                          <Typography
                            sx={{
                              font: "normal 700 14px Roboto",
                            }}
                          >
                            View Latest Version
                          </Typography>
                        </Link>
                      </Box>
                    )}
                  </>
                )}
              </Box>
            )}
          </Box>
          <Box sx={{ display: "flex", ml: 4 }}>
            {status === OrderSetStatus.Draft ||
            status === OrderSetStatus.Review ? (
              <>
                <OsmWhiteButton
                  buttonName={isChangeStatusPermission ? "Cancel" : "Close"}
                  onClick={(e: React.MouseEvent<HTMLElement>) =>
                    handleButtonClick(e, OsmAction.Cancel)
                  }
                />
                {isChangeStatusPermission && (
                  <OsmWhiteButton
                    buttonName={"Save"}
                    onClick={(e: React.MouseEvent<HTMLElement>) =>
                      handleButtonClick(e, OsmAction.Save)
                    }
                    disabled={
                      editError ||
                      noData ||
                      !pageStatusRef.current[tabId]?.dirty
                    }
                    style={{ display: "inherit" }}
                  />
                )}
                {isChangeStatusPermission && (
                  <OsmWhiteButton
                    buttonName={"Save and Close"}
                    onClick={(e: React.MouseEvent<HTMLElement>) =>
                      handleButtonClick(e, OsmAction.SaveAndClose)
                    }
                    disabled={
                      editError ||
                      noData ||
                      !pageStatusRef.current[tabId]?.dirty
                    }
                    style={{
                      display:
                        status === OrderSetStatus.Retired ? "none" : "inherit",
                    }}
                  />
                )}
                {isChangeStatusPermission && (
                  <OsmCciButton
                    buttonName={
                      toolType === OsmToolType.Set
                        ? "Publish Set"
                        : "Publish Group"
                    }
                    onClick={(e: React.MouseEvent<HTMLElement>) =>
                      handleButtonClick(
                        e,
                        toolType === OsmToolType.Set
                          ? OsmAction.PublishSet
                          : OsmAction.PublishGroup
                      )
                    }
                    disabled={editError || !hasId || noData}
                    style={{
                      display:
                        toolType === OsmToolType.Set &&
                        status === OrderSetStatus.Draft
                          ? "none"
                          : "inherit",
                    }}
                  />
                )}
              </>
            ) : status === OrderSetStatus.Published ? (
              <>
                {!data?.latestVersion && (
                  <OsmWhiteButton
                    buttonName={"Create New Version"}
                    onClick={(e: React.MouseEvent<HTMLElement>) =>
                      handleButtonClick(e, OsmAction.CreateNewVersion)
                    }
                  />
                )}
                <OsmWhiteButton
                  buttonName={"Close"}
                  onClick={(e: React.MouseEvent<HTMLElement>) =>
                    handleButtonClick(e, OsmAction.Cancel)
                  }
                />
              </>
            ) : (
              <>
                <OsmWhiteButton
                  buttonName={"Cancel"}
                  onClick={(e: React.MouseEvent<HTMLElement>) =>
                    handleButtonClick(e, OsmAction.Cancel)
                  }
                />
                <OsmCciButton
                  buttonName={"Convert to Draft"}
                  onClick={(e: React.MouseEvent<HTMLElement>) =>
                    handleButtonClick(e, OsmAction.ConvertToDraft)
                  }
                  disabled={editError}
                />
              </>
            )}
          </Box>
        </OsmToolbar>
      )}
    </>
  );
};

export default OsmOrderSetToolbar;
