import React from "react";
import { Stack } from "@mui/material";
import { OsmSearchField } from "../common/OsmSearchField";
import OsmDataGrid from "./OsmDataGrid";
import OsmEmptyDataMsgBox from "../common/OsmEmptyDataMsgBox";

type TOsmOrderListPanelProps = {
  data: any;
  selectedCategory: any;
  setSelectedCategory: any;
  onOrderSelection: any;
  selectedOrderIds?: string[];
};

/**
 * Show list of Orders (in MUI datagrid) with search field and filter buttons.
 * @prop data Order list in MUI datagrid format
 * @selectedCategory currently selected category/sub-category
 * @onOrderSelection function for updating selected Order data
 */
const OsmOrderListPanel = (props: TOsmOrderListPanelProps) => {
  const {
    data,
    selectedCategory,
    setSelectedCategory,
    onOrderSelection,
    selectedOrderIds,
  } = props;

  const onSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedCategory({ ...selectedCategory, searchstr: event.target.value });
  };

  const emptyMsg = [
    "Begin typing to search for an order,",
    "or select an order category.",
  ];

  const columns = data?.columns ?? [];
  const rows = data?.rows ?? [];

  /**
   * Update selectedOrderIds for given category/sub-category, and
   * update selected Orders for "Add Orders".
   * @param {Array} orderIds
   */
  const handleOrderSelection = (orderIds: any) => {
    // update Order data
    const selectedIDs = new Set(orderIds);
    const selectedRows = rows.filter((row: any) => selectedIDs.has(row.id));
    onOrderSelection(selectedRows, orderIds);
  };

  return (
    <Stack gap="8px">
      <OsmSearchField
        placeholder="Search All Orders"
        onChange={onSearch}
        value={selectedCategory?.searchstr ? selectedCategory.searchstr : ""}
        sx={{ width: "1408px" }}
        // TODO: Change width of Search to 1200 when we add Medications and Non-Meds buttons
      />
      {!data && <OsmEmptyDataMsgBox messages={emptyMsg} />}
      {data && (
        <OsmDataGrid
          data-testid={"osm-order-list-grid-id"}
          checkboxSelection
          onRowSelectionModelChange={handleOrderSelection}
          rowSelectionModel={selectedOrderIds}
          columnVisibilityModel={{
            id: false,
            data: false,
            cat: selectedCategory?.cat === "ALL_ORDERS" ? true : false,
            subcat:
              selectedCategory?.subcat && selectedCategory.subcat.length > 0
                ? false
                : true,
          }}
          columns={columns}
          rows={rows}
          hideFooter={true}
          rowHeight={24}
          columnHeaderHeight={24}
          sx={{
            background: "#FFFFFF",
            height: "736px",
          }}
        />
      )}
    </Stack>
  );
};

export default OsmOrderListPanel;
