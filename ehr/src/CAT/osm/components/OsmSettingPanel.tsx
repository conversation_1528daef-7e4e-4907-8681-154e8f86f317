import React, { useState } from "react";
import { Box, Link, Paper, Typography, Divider } from "@mui/material";
import { OsmAction, OsmToolType } from "../OsmConstants";
import OsmSettingHeaderPanel from "./OsmSettingHeaderPanel";
import OsmGeneralDetailsSettingsPanel from "./OsmGeneralDetailsSettingsPanel";
import OsmGeneralLocationSettingsPanel from "./OsmGeneralLocationSettingsPanel";
import { useOsmContext } from "../contexts/OsmContext";

import {
  isOSMAdminPermission,
  isOSMManagerPermission,
} from "@cci-monorepo/CAT/common/Permissions";

// From UI Design
const SettingPanelWidth = 388;
const SettingPanelHeight = 878;
const ExpandSettingPanelWidth = SettingPanelWidth;
const CollapseSettingPanelWidth = 50;

/**
 * The component for Setting properties of Order Set/Group
 * @param {Object} props
 *  - settingType: the type of setting: 'set' or 'group'
 */
const OsmSettingPanel = (props: any) => {
  const {
    settingType,
    sx,
    data,
    inVersionHistory,
    handleOpenPopup,
    ...others
  } = props;
  const { osmDispatch } = useOsmContext();

  const [expand, setExpand] = useState(true);

  const handleButtonClick = () => {
    handleOpenPopup(OsmAction.OpenAffectedOrderGroupSetDialog);
    osmDispatch({
      type: OsmAction.OpenAffectedOrderGroupSetDialog,
    });
  };

  return (
    <Paper
      sx={{
        width: expand ? ExpandSettingPanelWidth : CollapseSettingPanelWidth,
        height: SettingPanelHeight,
        background: expand ? "#FFFFFF" : "#E8EFF7",
        overflowY: "auto",
        overflowX: "none",
        borderRadius: 0,
        border: "none",
        boxShadow: "none",
        ".MuiPaper-root": { border: "none", boxShadow: "none" },
        ...sx,
      }}
      {...others}
    >
      <OsmSettingHeaderPanel expand={expand} setExpand={setExpand} />
      {expand && (
        <Box>
          <OsmGeneralDetailsSettingsPanel
            {...others}
            settingType={settingType}
            data={data}
            inVersionHistory={inVersionHistory}
          />
          <OsmGeneralLocationSettingsPanel
            {...others}
            settingType={settingType}
            data={data}
            inVersionHistory={inVersionHistory}
          />
          {(isOSMAdminPermission || isOSMManagerPermission) && (
            <Box
              sx={{
                px: 4,
                display: "flex",
                flexDirection: "column",
                gap: "16px",
              }}
            >
              {settingType === OsmToolType.Group && (
                <Link
                  component="button"
                  underline="hover"
                  onClick={handleButtonClick}
                >
                  <Typography align="left" variant="h6">
                    Affected Order Sets and Groups
                  </Typography>
                </Link>
              )}
            </Box>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default OsmSettingPanel;
