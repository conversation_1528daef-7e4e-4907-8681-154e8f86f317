import React, { useState } from "react";
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  Toolbar,
  Typography,
} from "@mui/material";
import OsmSearchField from "../common/OsmSearchField";
import { OsmCciButton, OsmWhiteButton } from "../common/OsmButtons";
import ListElbowIcon from "@cci-monorepo/oe-shared/src/assets/osm/Icn_Elbow.svg";
import dialogErrorIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Error.svg";
import HighlightedText from "../../common/HighlightedText";
import { useOrderGroupListQuery } from "../hooks/useOrderSetQuery";
import { parseOrderGroupsData } from "../util/OsmDataGridHelper";

import EmptyDataMsgBox from "../common/OsmEmptyDataMsgBox";

import {
  GroupOrderSelectionType,
  OsmAction,
  OsmToolType,
} from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";
import { OneGroupDataPanel, SelectionModeMsg } from "./OsmGroupDataPanel";
import OsmInstructionPanel from "./OsmInstructionPanel";

const defaultGroupListWidth = 388;

/**
 * The detail information for a group data.
 * It contains top toolbar with the group name and 2 buttons, and
 * a detail information panel for the  group.
 */
const OrderGroupDetail = (props: any) => {
  const {
    data,
    addedGroups,
    setAddedGroups,
    selectNextGroup,
    currentData,
    dataType,
  } = props;
  const { allDataRef } = useOsmContext();
  const allGroups = allDataRef?.current?.groups;
  const emptyMsg = [
    "Begin typing to search for an order group,",
    "or select an order group.",
  ];

  const currentGroupId =
    currentData?.orderGroupName && currentData?.id && currentData.id;
  const currentSubGroupIds = currentData?.subIds
    ? currentData?.subIds
    : currentData?.groupIds
      ? currentData.groupIds
      : [];

  const getGroupsLevel = (data: any, level: number = 0) => {
    let max = 1;
    level++;
    if (data && data.subIds) {
      var i = 0;
      for (i = 0; i < data.subIds.length; i++) {
        const subId = data.subIds[i];
        if (subId.group_id) {
          const subgrp = allGroups[subId.group_id];
          let n: any = getGroupsLevel(subgrp, level);
          if (max < n) max = n;
        } else {
          if (max < level) max = level;
        }
      }
      return max;
    }
  };

  const IsRecursiveGroup = (data: any) => {
    let existFlag: any = false;
    if (data && data.id === currentData.group_id) {
      existFlag = true;
    } else {
      if (data && data.subIds) {
        for (var i = 0; i < data.subIds.length; i++) {
          let item = data.subIds[i];
          if (item.group_id) {
            if (item.group_id === currentData.group_id) {
              existFlag = true;
              break;
            } else {
              const subgrp = allGroups[item.group_id];
              existFlag = IsRecursiveGroup(subgrp);
              if (existFlag) break;
            }
          }
        }
      }
    }
    return existFlag;
  };

  const handleAddedGroup = (data: any) => {
    const groupId = data.id;
    if (
      addedGroups.includes(addedGroups.find((g: any) => g.group_id === data.id))
    ) {
      setAddedGroups(
        addedGroups.filter((grp: any) => {
          return groupId !== grp.group_id;
        })
      );
    } else {
      setAddedGroups([
        {
          group_id: data.id,
          isSelected: data.isSelected,
          isRequired: data.isRequired,
        },
        ...addedGroups,
      ]);
    }
  };

  const num: any = getGroupsLevel(data, 0);

  return (
    <>
      {!data && <EmptyDataMsgBox messages={emptyMsg} />}
      {data && (
        <Stack
          sx={{
            flexDirection: "column",
            width: "100%",
            background: "#E8EFF7",
          }}
        >
          <Stack
            direction={"row"}
            alignItems={"center"}
            sx={{ flexGrow: 1, ml: 4 }}
          >
            <Typography
              sx={{
                flexGrow: 1,
                fontSize: 18,
                fontWeight: 700,
                color: "#11181F",
              }}
            >
              {data.orderGroupName}
            </Typography>
            {dataType === OsmToolType.Set ? (
              <Stack
                direction={"row"}
                sx={{
                  gap: 2,
                  p: 2,
                }}
              >
                <OsmCciButton
                  buttonName={
                    addedGroups.includes(
                      addedGroups.find((g: any) => g.group_id === data.id)
                    )
                      ? "Exclude Order Group"
                      : "Include Order Group"
                  }
                  onClick={() => handleAddedGroup(data)}
                  disabled={
                    data?.id === currentGroupId ||
                    currentSubGroupIds.some(
                      (group: any) => data.id === group.group_id
                    )
                  }
                />
                <OsmWhiteButton
                  buttonName={"Continue Browsing"}
                  onClick={selectNextGroup}
                />
              </Stack>
            ) : (
              <Stack
                direction={"row"}
                sx={{
                  gap: 2,
                  p: 2,
                }}
              >
                {data?.id === currentGroupId ||
                currentSubGroupIds.some(
                  (group: any) => data.id === group.group_id
                ) ||
                IsRecursiveGroup(data) ? (
                  <Box
                    sx={{
                      fontSize: "15px",
                      fontWeight: 700,
                      display: "flex",
                      flexDirection: "row",
                    }}
                  >
                    <img
                      src={dialogErrorIcon}
                      style={{ width: 24, height: 24 }}
                    ></img>{" "}
                    <div
                      style={{
                        alignItems: "center",
                        paddingTop: "4px",
                        marginLeft: "8px",
                      }}
                    >
                      {" "}
                      Order group already exists in this order group
                    </div>
                  </Box>
                ) : num > 4 ? (
                  <Box
                    sx={{
                      fontSize: "15px",
                      fontWeight: 700,
                      display: "flex",
                      flexDirection: "row",
                    }}
                  >
                    <img
                      src={dialogErrorIcon}
                      style={{ width: 24, height: 24 }}
                    ></img>{" "}
                    <div
                      style={{
                        alignItems: "center",
                        paddingTop: "4px",
                        marginLeft: "8px",
                      }}
                    >
                      {" "}
                      Up to 5 order groups can be nested
                    </div>
                  </Box>
                ) : (
                  <OsmCciButton
                    buttonName={
                      addedGroups.includes(
                        addedGroups.find((g: any) => g.group_id === data.id)
                      )
                        ? "Exclude Order Group"
                        : "Include Order Group"
                    }
                    onClick={() => handleAddedGroup(data)}
                  />
                )}
              </Stack>
            )}
          </Stack>
          <Box
            sx={{
              background: "#FFFFFF",
              width: "100%",
              height: "100%",
              alignItems: "center",
              pt: 4,
              pl: 4,
              overflow: "auto",
            }}
          >
            <Typography
              style={{
                flexGrow: 1,
                fontSize: 16,
                fontWeight: 700,
                color: "#396EA7",
              }}
            >
              {data.orderGroupName}
            </Typography>
            {data.instruction && (
              <OsmInstructionPanel
                isTop={true}
                setOrGroupStatus={props.status}
                showonly={true}
                data={{ instruction: data.instruction }}
              />
            )}
            {parseInt(data.selectionType) ===
              GroupOrderSelectionType.SelectOne && (
              <SelectionModeMsg msg="Select One" satified={true} />
            )}
            {parseInt(data.selectionType) ===
              GroupOrderSelectionType.MinimalOne && (
              <SelectionModeMsg msg="Select a minimum of one" />
            )}
            {parseInt(data.selectionType) ===
              GroupOrderSelectionType.SelectRequired && (
              <SelectionModeMsg msg="Selection required " />
            )}
            <OneGroupDataPanel
              group={{ subIds: data.subIds, selectionType: data.selectionType }}
            />
          </Box>
        </Stack>
      )}
    </>
  );
};

/**
 * List of Order Groups with a Search Field.
 */
const OrderGroupList = (props: any) => {
  const { groupData, setOneGroup, selectedId, setSelectedId } = props;
  const [filter, setFilter] = useState("");

  const handleGroupItemClick = (event: React.MouseEvent, item: any) => {
    setSelectedId(item.id);
    setOneGroup(item);
  };

  const onSearchFilter = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(event.target.value.toLowerCase());
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        width: defaultGroupListWidth,
        maxWidth: defaultGroupListWidth,
        background: "#FFFFFF",
        overflowY: "auto",
        overflowX: "hidden",
        mr: 1,
      }}
    >
      <OsmSearchField
        sx={{ m: 1 }}
        onChange={onSearchFilter}
        placeholder={"Search Order Groups"}
      />
      <Typography
        sx={{
          height: 22,
          background: "#5A7493",
          color: "#FFFFFF",
          fontSize: 16,
          fontWeight: 700,
          mr: 1,
          ml: 1,
          pl: 1,
        }}
      >
        Order Groups
      </Typography>
      <Box sx={{ width: "100%" }}>
        <List
          component="nav"
          aria-label="order-set-management-order-group-list"
        >
          {groupData
            ?.filter((group: any) => {
              return group.orderGroupName.toLowerCase().includes(filter);
            })
            .map((item: any) => {
              const groupName = item.orderGroupName;
              return (
                <ListItem disablePadding key={item.id}>
                  <ListItemButton
                    disableRipple
                    onClick={(e) => handleGroupItemClick(e, item)}
                    selected={item.id === selectedId}
                    sx={{
                      padding: 0,
                      "&.Mui-selected": {
                        backgroundColor: "#FEC341",
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <img
                        src={ListElbowIcon}
                        alt={"list-elbow-icon"}
                        style={{ width: 24, height: 24 }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      id={groupName}
                      primary={
                        <HighlightedText text={groupName} substring={filter} />
                      }
                      sx={{ rt: 0, mb: 0 }}
                    />
                  </ListItemButton>
                </ListItem>
              );
            })}
        </List>
      </Box>
    </Box>
  );
};

const OrderGroupBottomToolBar = ({
  onCancel,
  onAddGroup,
  addedGroups,
}: any) => {
  return (
    <Toolbar
      sx={{
        height: 56,
        background: "#F2F2F2",
        justifyContent: "right",
        gap: 2,
      }}
    >
      <OsmWhiteButton buttonName={"Cancel All"} onClick={onCancel} />
      <OsmCciButton
        buttonName={
          addedGroups.length > 0
            ? "Add (" + addedGroups.length + ") Group"
            : "Add Group"
        }
        onClick={onAddGroup}
        disabled={addedGroups.length <= 0}
      />
    </Toolbar>
  );
};

/**
 * The main component of Browse Order Group.
 * It contains a SearchField, Group list, Group Detail, and bottom toolbar.
 */
const BrowseOrderGroupsPanel = (props: any) => {
  const { onClose, currentData, dataType } = props;
  const [oneGroup, setOneGroup] = useState(null);
  const [addedGroups, setAddedGroups] = useState([]);
  const [selectedId, setSelectedId] = useState("");
  const { osmDispatch } = useOsmContext();

  const selectNextGroup = () => {
    if (groupData && groupData.data && groupData.data[0] && selectedId) {
      const currIdx = groupData.data[0].findIndex(
        (group: any) => group.id === selectedId
      );
      const newIdx =
        currIdx > -1 && currIdx < groupData.data[0].length - 1
          ? Number(currIdx) + 1
          : "0";
      setSelectedId(groupData.data[0][newIdx]?.id);
      setOneGroup(groupData.data[0][newIdx]);
    }
  };

  const handleAddGroup = () => {
    let newGroupIds = [];
    let newData = undefined;

    if (currentData?.subIds) {
      newGroupIds = currentData.subIds;
      newData = { subIds: [...newGroupIds, ...addedGroups] };
    } else if (currentData?.groupIds) {
      newGroupIds = currentData.groupIds;
      newData = { groupIds: [...newGroupIds, ...addedGroups] };
    } else if (dataType === OsmToolType.Group) {
      newData = { subIds: [...addedGroups] };
    } else if (dataType === OsmToolType.Set) {
      newData = { groupIds: [...addedGroups] };
    }

    if (newData) {
      osmDispatch({
        type: OsmAction.AddGroups,
        data: {
          ...newData,
          name: currentData?.name,
          orderSetName: currentData?.orderSetName,
        },
      });
    }

    onClose && onClose();
  };

  const handleCancel = () => {
    onClose && onClose();
  };

  const groupData = useOrderGroupListQuery({}, parseOrderGroupsData, {
    searchStr: "",
  });

  return (
    <Box sx={{ display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          width: 1500,
          height: 600,
          padding: "8px 8px 0px 8px",
          background: "#F2F2F2",
        }}
      >
        <OrderGroupList
          groupData={groupData && groupData.data && groupData.data[0]}
          setOneGroup={setOneGroup}
          selectedId={selectedId}
          setSelectedId={setSelectedId}
        />
        <OrderGroupDetail
          data={oneGroup}
          addedGroups={addedGroups}
          setAddedGroups={setAddedGroups}
          selectNextGroup={selectNextGroup}
          currentData={currentData}
          dataType={dataType}
        />
      </Box>
      <OrderGroupBottomToolBar
        onCancel={handleCancel}
        onAddGroup={handleAddGroup}
        addedGroups={addedGroups}
      />
    </Box>
  );
};

export default BrowseOrderGroupsPanel;
