import React from "react";
import { <PERSON>, Divider, Stack, Toolbar, Typography } from "@mui/material";
import QuestionIcon from "@cci-monorepo/oe-shared/src/assets/osm/icn_dialog_question.svg";
import { OsmCciButton, OsmWhiteButton } from "../common/OsmButtons";

const convertDialogWidth = 450;

const ConvertToDraftInfoPanel = (props: any) => {
  const { convertType, convertName, onOk, onClose } = props;

  const handleCancel = () => {
    onClose && onClose();
  };

  const handleConvert = () => {
    onOk && onOk("convert");
  };

  return (
    <Stack
      data-testid={"osm-convert-to-draft-dialog-id"}
      sx={{ width: convertDialogWidth }}
    >
      <Box sx={{ display: "flex" }}>
        <Box style={{ display: "flex" }}>
          <img src={QuestionIcon} alt="osm-convert-to-draft-dialog-info-icon" />
        </Box>
        <Box sx={{ ml: 1 }}>
          <Typography variant="body1" style={{ marginLeft: 10, paddingTop: 3 }}>
            Are you sure you want to convert
          </Typography>
          <Typography variant="body1" style={{ marginLeft: 10, paddingTop: 3 }}>
            retired {convertType} <b>{convertName}</b> to draft?
          </Typography>
        </Box>
      </Box>
      <Divider
        sx={{
          height: "24px",
          borderColor: "#FFFFFF",
          borderBottomWidth: "3px",
        }}
      />
      <Toolbar sx={{ justifyContent: "flex-end" }}>
        <OsmWhiteButton buttonName={"Cancel"} onClick={handleCancel} />
        <OsmCciButton buttonName={"Convert to Draft"} onClick={handleConvert} />
      </Toolbar>
    </Stack>
  );
};

export default ConvertToDraftInfoPanel;
