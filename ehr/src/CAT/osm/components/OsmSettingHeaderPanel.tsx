import React from "react";
import { Box, Typography } from "@mui/material";
import LeftArrow from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Left.svg";
import RightArrow from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Right.svg";
import SettingIcon from "@cci-monorepo/oe-shared/src/assets/osm/Icon_Setting_Gear.svg";

/**
 * The header panel for Order Set/Group Setting in OSM.
 * It handles the panel expand/collapse.
 */

const OsmSettingHeaderPanel = (props: any) => {
  const { expand, setExpand } = props;

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        boxShadow: "none",
        alignItems: "center",
        gap: 2,
        p: 4,
        background: "#E8EFF7",
      }}
    >
      {expand ? (
        <img
          src={RightArrow}
          alt="right-arrow"
          onClick={() => {
            setExpand(!expand);
          }}
        />
      ) : (
        <img
          src={LeftArrow}
          alt="left-arrow"
          onClick={() => {
            setExpand(!expand);
          }}
        />
      )}
      {expand && (
        <>
          <img src={SettingIcon} alt="icon-setting" />
          <Typography
            sx={{
              fontSize: 18,
              fontWeight: "700",
              lineHeight: 1,
              color: "#11181F",
            }}
          >
            Settings
          </Typography>
        </>
      )}
    </Box>
  );
};

export default OsmSettingHeaderPanel;
