import { Typography } from "@mui/material";
import WarningIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Warning_Large.svg";
import { Box } from "@mui/material";
import { Button as CciButton, CciDialog } from "@cci/mui-components";

export const OsmWarningDialog = (props: any) => {
  const buttons = () => (
    <>
      {props.onClose && (
        <CciButton color="secondary" onClick={props.onClose}>
          Cancel
        </CciButton>
      )}
      {props.onOpenPreviousVersion && (
        <CciButton color="secondary" onClick={props.onOpenPreviousVersion}>
          Open Previous Version
        </CciButton>
      )}
      {props.onOpenLatestVersion && (
        <CciButton color="primary" onClick={props.onOpenLatestVersion}>
          Open Latest Version
        </CciButton>
      )}
    </>
  );

  const contents = () => (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-start",
        lineHeight: "25px",
      }}
    >
      <img src={WarningIcon} alt="icon" style={{ marginRight: 15 }} />
      <Typography>
        A new version of <b>{props.name}</b> is currently in Draft Mode.
      </Typography>
    </Box>
  );

  return (
    <CciDialog
      title={"Newer Version Available"}
      open={props.open}
      setOpen={props.onClose}
      handleClose={props.onClose}
      contentStyle={{
        padding: "24px",
      }}
      draggable
      content={contents()}
      buttonStyle={{
        display: "flex",
        flexDirection: "row",
        gap: 1,
      }}
      buttons={buttons()}
      sx={{
        "& .MuiModal-backdrop": {
          backgroundColor: "rgba(0, 0, 0, 0.25);",
        },
      }}
    />
  );
};
