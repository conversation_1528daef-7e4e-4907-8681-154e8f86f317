import { useState } from "react";
import Box from "@mui/material/Box";
import WhiteArrowDown from "@cci-monorepo/oe-shared/src/assets/Icon_White_Arrow_Down.svg";
import WhiteArrowRight from "@cci-monorepo/oe-shared/src/assets/Icon_White_Arrow_Right.svg";
import IconButton from "@mui/material/IconButton";
import OrdersIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Orders.svg";
import { OrderDataGrids } from "./OrderDataGrids";

/**
 * Component that displays all the order search data.
 */
export const OrdersPanel = (props: any) => {
  const [collapsed, setCollapsed] = useState(false);
  const numOrders = props.freqFavOrders.length;

  return (
    <div
      className={"quick-search-orders-panel"}
      style={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        overflow: "auto",
      }}
    >
      <div
        style={{
          background: "#5A7493",
          display: "flex",
          width: "100%",
          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
          marginBottom: 8,
        }}
      >
        <IconButton
          size="small"
          style={{ width: 30, height: 30 }}
          onClick={() => setCollapsed(!collapsed)}
        >
          <img src={collapsed ? WhiteArrowRight : WhiteArrowDown} alt="arrow" />
        </IconButton>
        <span
          style={{
            backgroundColor: "#E0E8F2",
            borderRadius: 10,
            textAlign: "center",
            lineHeight: "16px",
            padding: "0px 8px",
            marginTop: "6px",
            height: 16,
            color: "#000000",
            font: "Bold 14px Helvetica",
          }}
        >
          {numOrders}
        </span>
        <img
          src={OrdersIcon}
          alt="orders-icon"
          style={{ width: 24, height: 24, margin: "2px 9px" }}
        />
        <p style={{ color: "white", font: "Bold 18px Helvetica" }}>Orders</p>
      </div>
      <Box sx={{ height: "100vh" }}>
        <OrderDataGrids
          handleClose={props.handleClose}
          freqFavOrders={props.freqFavOrders}
          collapsed={collapsed}
          searchVal={props.searchVal}
          currentData={props.currentData}
        />
      </Box>
    </div>
  );
};
