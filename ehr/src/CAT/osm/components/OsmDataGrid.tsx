// @ts-nocheck
import React from "react";
import { alpha, styled } from "@mui/material/styles";
import { Box } from "@mui/material";
import {
  DataGridPro,
  DataGridProProps,
  gridClasses,
} from "@mui/x-data-grid-pro";

const ODD_OPACITY = 0.2;

const StyledOsmDataGrid = styled(DataGridPro)(({ theme }) => ({
  [`& .${gridClasses.row}.even`]: {
    "&:hover, &.Mui-hovered": {
      backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
    },
  },
  [`& .${gridClasses.row}.odd`]: {
    backgroundColor: theme.palette.grey[200],
    "&:hover, &.Mui-hovered": {
      backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
    },
  },
  [`& .${gridClasses.columnHeader}, & .${gridClasses.cell}`]: {
    outline: "transparent",
  },
  [`& .${gridClasses.columnHeader}:focus-within, & .${gridClasses.cell}:focus-within`]:
    {
      outline: "none",
    },
  ".MuiDataGrid-columnHeaders": {
    background: "#D8DCE3",
    position: "sticky",
    zIndex: 2,
  },
  ".MuiDataGrid-columnHeaderTitle": {
    fontWeight: "bold",
    fontSize: "16px",
    color: "black",
  },
  "& .MuiDataGrid-virtualScroller": {
    marginTop: "0 !important",
  },
  ".MuiTypography-root": {
    fontSize: 16,
  },
  ".MuiDataGrid-cellContent": {
    fontSize: 14,
  },
  height: "78vh",
}));

type OsmDataGridProps = DataGridProProps & {
  width?: string;
  height?: string;
};

/**
 * Datagrid for OrderSet Management.
 * It is wrapper of MUI DataGridPro.
 */
const OsmDataGrid = ({
  width,
  height,
  sx,
  rowHeight,
  columnHeaderHeight,
  ...others
}: OsmDataGridProps) => {
  const gridWidth = width ? width : "100%";
  const gridHeight = height ? height : "100%";
  const dataTestId = others["data-testid"]
    ? others["data-testid"]
    : "osm-data-grid";
  return (
    <Box
      data-testid={dataTestId}
      sx={{
        width: gridWidth,
        height: gridHeight,
        backgroundColor: "white",
        overflow: "auto",
      }}
    >
      <StyledOsmDataGrid
        rowHeight={rowHeight ?? 38}
        columnHeaderHeight={columnHeaderHeight}
        sx={sx}
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0 ? "even" : "odd"
        }
        {...others}
      />
    </Box>
  );
};

export default OsmDataGrid;
