import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Divider,
} from "@mui/material";
import OsmLocationDetailsPanel from "./OsmLocationDetailsPanel";
import * as PropTypes from "prop-types";
import DownArrow from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";

const OsmGeneralLocationSettingsPanel = (props: any) => {
  const { disabled, settingType, data, inVersionHistory } = props;
  return (
    <Accordion square={true} disableGutters={true} defaultExpanded={true}>
      <AccordionSummary
        expandIcon={<img src={DownArrow} alt="icon-warning" />}
        aria-controls="order-setting-location-content"
        style={{ backgroundColor: "#F7F7F7", paddingLeft: "16px" }}
        sx={{
          display: "flex",
          flexDirection: "row-reverse",
          background: "#F7F7F7",
          width: "100%",
          ".MuiAccordionSummary-content": { m: 0 },
        }}
      >
        <Typography
          sx={{
            pl: 2,
            m: 0,
            fontSize: 16,
            fontWeight: "500",
            color: "#11181F",
          }}
        >
          Location settings
        </Typography>
      </AccordionSummary>
      <AccordionDetails
        sx={{
          p: 4,
          margin: "16px 2px 16px 16px",
          padding: 4,
          border: "1px solid #E1E1E1",
          borderRadius: "4px",
        }}
      >
        <OsmLocationDetailsPanel
          disabled={disabled}
          settingType={settingType}
          data={data}
          inVersionHistory={inVersionHistory}
        />
        <Divider></Divider>
      </AccordionDetails>
    </Accordion>
  );
};

OsmGeneralLocationSettingsPanel.propTypes = {
  disabled: PropTypes.bool,
  data: PropTypes.any,
  inVersionHistory: PropTypes.any,
};

export default OsmGeneralLocationSettingsPanel;
