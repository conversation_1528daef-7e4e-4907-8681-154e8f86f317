import React, { useState } from "react";
import { styled } from "@mui/material/styles";

import {
  Box,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";

const StyledTableCell = styled(TableCell)(({}) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#D8DCE3",
    fontSize: "15px",
    fontWeight: 700,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: "15px",
    fontWeight: 400,
  },
}));

const StyledTableRow = styled(TableRow)(({}) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: "white",
  },
  "&:nth-of-type(even)": {
    backgroundColor: "#F7F7F7",
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const AffectedDataPanel = (props: any) => {
  let numSets = 0;
  let numGroups = 0;
  if (props.sets) {
    numSets = props.sets.length;
  }
  if (props.groups) {
    numGroups = props.groups.length;
  }

  return (
    <Box sx={{ mt: 4, mb: 2 }}>
      {numSets > 0 && (
        <>
          <Typography
            variant="body1"
            sx={{ fontSize: "16px", fontWeight: 700 }}
          >
            ({numSets}) Order Sets
          </Typography>
          <TableContainer component={Paper} sx={{ maxHeight: "300px" }}>
            <Table
              size="small"
              aria-label="retire-group-affected-order-set-table"
            >
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell>Order Set Name</StyledTableCell>
                  <StyledTableCell align="right">Order Set ID</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {props.sets.map((row: any) => (
                  <StyledTableRow key={row.key}>
                    <StyledTableCell>{row.name}</StyledTableCell>
                    <StyledTableCell align="right">{row.key}</StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
      {numGroups > 0 && (
        <>
          <Typography
            variant="body1"
            sx={{ fontSize: "16px", fontWeight: 700, mt: 4 }}
          >
            ({numGroups}) Order Groups
          </Typography>
          <TableContainer component={Paper} sx={{ maxHeight: "300px" }}>
            <Table
              size="small"
              aria-label="retire-group-affected-order-group-table"
            >
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell>Order Group Name</StyledTableCell>
                  <StyledTableCell align="right">
                    Order Group ID
                  </StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {props.groups.map((row: any) => (
                  <StyledTableRow key={row.key}>
                    <StyledTableCell>{row.orderGroupName}</StyledTableCell>
                    <StyledTableCell align="right">{row.key}</StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
      {(numSets > 0 || numGroups > 0) && (
        <Divider
          sx={{
            height: "24px",
            borderBottomWidth: "3px",
          }}
        />
      )}
    </Box>
  );
};

export default AffectedDataPanel;

/**
 * Get list of Order Sets which contains the given group
 * @param (object) data -- the group data
 * @return array of Order Sets
 */
export const getAffectedOrderSets = (data: any, allSets: object) => {
  let sets: any;
  if (data && allSets) {
    sets = Object.entries(allSets)
      .map(([key, value]) => {
        const name = value.name;
        const groupIds = value.groupIds;
        const setAffected =
          groupIds.filter((group: any) => group.group_id === data.id).length >
          0;
        return setAffected ? { key, name } : null;
      })
      .filter((allSets) => !!allSets);
  }
  return sets;
};

/**
 * Get list of Order Groups which contains the given group
 * @param (object) data -- the group data
 * @return array of Order Groups
 */
export const getAffectedOrderGroups = (data: any, allGroups: object) => {
  let groups: any;
  if (data && allGroups) {
    groups = Object.entries(allGroups)
      .map(([key, value]) => {
        const subIds = value.subIds;
        const orderGroupName = value.orderGroupName;
        const groupAffected =
          subIds.filter((subGroups: any) => subGroups.group_id === data.id)
            .length > 0;
        return groupAffected ? { key, orderGroupName } : null;
      })
      .filter((allGroups) => !!allGroups);
  }
  return groups;
};

/**
 * Get list of Order Sets and Order Groups whuch contains the given group
 * @param (object) group -- the group data
 * @return object of set array and group array
 */
export const getAffectedData = (group: any, allSets: any, allGroups: any) => {
  let sets: any;
  let groups: any;

  if (group) {
    sets = getAffectedOrderSets(group, allSets);
    groups = getAffectedOrderGroups(group, allGroups);
  }

  return {
    sets: sets,
    groups: groups,
  };
};
