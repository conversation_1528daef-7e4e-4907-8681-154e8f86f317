// @ts-nocheck
import React from "react";
import { Box, Checkbox, Radio, Stack, Typography } from "@mui/material";
import OsmDataPanel from "./OsmDataPanel";
import { GroupOrderSelectionType } from "../OsmConstants";

type TOsmOrderDataPanel = {
  order: any;
  groupId: string;
  selectionType: string;
  selectable: boolean;
  onOrderSelectChange: (orderId: string, checked: boolean) => void;
  selected?: boolean;
  disabled: boolean;
  onOrderEdit?: (order: any) => void;
  editingOrderId?: string;
  setEditingOrderId?: (orderId: string) => void;
  uOrderId?: string;
};

/**
 * Display one Order in a group.
 * @todo candidate for Storybook.
 * @prop {object} order the Order data
 *  - name: the name of the Order
 *  - summary: summary of the Order
 *  - selected: optional, the Order is selected or not, default is false
 * @prop {string} groupId the parent Group id
 * @prop {boolean} singleSelect can only select one Order in the current Group
 * @prop {func} onOrderSelectChange the function to handle checked/unchecked for Radio/Checkbox
 */
const OsmOrderDataPanel = (props: TOsmOrderDataPanel) => {
  const {
    order,
    groupId,
    selectionType,
    selectable,
    onOrderSelectChange,
    selected,
    disabled,
    onOrderEdit,
    editingOrderId,
    setEditingOrderId,
    uOrderId,
  } = props;

  const handleCheckboxClick = (e: any, orderid: any) => {
    onOrderSelectChange && onOrderSelectChange(orderid, e.target.checked);
  };

  const handleRadioClick = (e: any, orderid: any) => {
    onOrderSelectChange && onOrderSelectChange(orderid, e.target.checked);
  };

  const handleDoubleClick = () => {
    if (!disabled && onOrderEdit) {
      onOrderEdit(order);
      setEditingOrderId && setEditingOrderId(uOrderId);
    }
  };

  const selectStyle = {
    alignSelf: "start",
  };

  const labelStyle = {
    fontSize: "15px",
    fontWeight: 400,
    alignSelf: "start",
  };

  return (
    <Stack
      direction={"row"}
      sx={{
        alignItems: "center",
        gap: 6,
        padding: "0 0 0 19px",
        cursor: disabled ? "default" : "pointer",
        backgroundColor: editingOrderId === uOrderId ? "#F9EEBC" : "#FFFFFF",
      }}
      onDoubleClick={handleDoubleClick}
    >
      {parseInt(selectionType) === GroupOrderSelectionType.SelectOne ||
      parseInt(selectionType) === GroupOrderSelectionType.SelectRequired ? (
        <Box sx={selectStyle}>
          <Radio
            name={groupId + "-order-radio-button"}
            value={order.orderid}
            disabled={!selectable || disabled}
            onChange={(e) => handleRadioClick(e, order.orderid)}
            checked={selected}
            sx={{
              p: 0,
              m: 0,
            }}
          />
        </Box>
      ) : (
        <Box sx={selectStyle}>
          {/*Sync with ehr/src/CAT/osm/components/OsmGroupDataPanel.tsx:340*/}
          <Checkbox
            disabled={!selectable || disabled}
            onChange={(e) => handleCheckboxClick(e, order.orderid)}
            checked={selected}
            sx={{
              p: 0,
              m: 0,
              borderRadius: 0,
              "&.Mui-disabled": {
                background: "#D6D6D6",
              },
            }}
          />
        </Box>
      )}
      <Typography
        sx={{
          ...labelStyle,
          minWidth: 360,
          width: 360,
          color: (theme) =>
            disabled ? theme.palette.text.disabled : theme.palette.text.primary,
          p: 0,
        }}
      >
        {order?.name || order?.orderName}
      </Typography>
      <Typography
        sx={{
          ...labelStyle,
          color: (theme) =>
            disabled ? theme.palette.text.disabled : theme.palette.text.primary,
        }}
      >
        {order?.summary}
      </Typography>
    </Stack>
  );
};

type TOsmTopOrderDataPanel = {
  order: any;
  onRemove: (data: any) => void;
  inVersionHistory: boolean;
  groupId: string;
  selectionType: string;
  onOrderSelectChange: (orderId: string, checked: boolean) => void;
  disabled: boolean;
  onOrderEdit?: (order: any) => void;
};

/**
 * Display one Order data at top level, i.e. in top Group
 */
export const OsmTopOrderDataPanel = (props: TOsmTopOrderDataPanel) => {
  const {
    order,
    onRemove,
    inVersionHistory,
    groupId,
    selectionType,
    onOrderSelectChange,
    disabled,
    onOrderEdit,
    ...others
  } = props;
  return (
    <OsmDataPanel
      {...others}
      removable={!inVersionHistory && disabled === false}
      onRemove={onRemove}
      removeData={order}
      draggable={props.draggable && disabled === false}
      disabled={disabled}
      sx={{
        p: "8px",
      }}
    >
      <Box sx={{ pl: props.draggable && disabled === false ? "62px" : "32px" }}>
        <OsmOrderDataPanel
          {...others}
          disabled={disabled}
          order={order}
          groupId={groupId}
          selectionType={selectionType}
          selectable={false}
          onOrderSelectChange={onOrderSelectChange}
          onOrderEdit={onOrderEdit}
        />
      </Box>
    </OsmDataPanel>
  );
};

export default OsmOrderDataPanel;
