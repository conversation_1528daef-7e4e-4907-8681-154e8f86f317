import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from "@mui/material";
import { OsmToolType } from "../OsmConstants";
import OsmDetailSettingPanel from "./OsmDetailSettingPanel";
import DownArrow from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import * as PropTypes from "prop-types";

const OsmGeneralDetailsSettingsPanel = (props: any) => {
  return (
    <Accordion square={true} disableGutters={true} defaultExpanded={true}>
      <AccordionSummary
        expandIcon={<img src={DownArrow} alt="icon-warning" />}
        aria-controls="order-setting-detail-content"
        style={{ backgroundColor: "#F7F7F7", paddingLeft: "16px" }}
        sx={{
          display: "flex",
          flexDirection: "row-reverse",
          background: "#F7F7F7",
          width: "100%",
          ".MuiAccordionSummary-content": { m: 0 },
        }}
      >
        <Typography
          sx={{
            pl: 2,
            m: 0,
            fontSize: 16,
            fontWeight: "500",
            color: "#11181F",
          }}
        >
          {props.settingType === OsmToolType.Set
            ? "Order Set Details"
            : "Order Group Details"}
        </Typography>
      </AccordionSummary>
      <AccordionDetails sx={{ padding: "16px 2px 16px 16px" }}>
        <OsmDetailSettingPanel
          {...props}
          settingType={props.settingType}
          data={props.data}
          inVersionHistory={props.inVersionHistory}
        />
      </AccordionDetails>
    </Accordion>
  );
};

OsmGeneralDetailsSettingsPanel.propTypes = {
  settingType: PropTypes.any,
  data: PropTypes.any,
  inVersionHistory: PropTypes.any,
};

export default OsmGeneralDetailsSettingsPanel;
