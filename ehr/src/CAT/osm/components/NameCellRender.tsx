import FavoritesIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Favorites_Star.svg";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import FrequentIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Frequent_Order.svg";
import NonFormularyIcon from "@cci-monorepo/oe-shared/src/assets/Icon_NonFormulary.svg";
import HighlightedText from "../../common/HighlightedText";

export const NameCellRenderer = (props: any) => {
  const isFavOrder = props.row.isFav === "1";

  return (
    <Box
      sx={{
        marginTop: "-3px",
        "& .MuiIconButton-root": {
          width: 24,
          height: 24,
          padding: 0,
          backgroundColor: "transparent",
        },
        overflow: "hidden",
        display: "flex",
      }}
    >
      {isFavOrder ? (
        <IconButton style={{ display: "block" }} size="large">
          <img src={FavoritesIcon} style={{ width: 20 }} />
        </IconButton>
      ) : (
        <IconButton
          style={{
            display: "block",
          }}
          size="large"
        >
          <img src={FrequentIcon} alt="frequent" style={{ width: 18 }} />
        </IconButton>
      )}
      <span
        style={{
          marginLeft: 32,
          height: 20,
          marginTop: 2,
          lineHeight: "20px",
          display: "inline-block",
          textOverflow: "ellipsis",
          overflow: " hidden",
          whiteSpace: "nowrap",
        }}
      >
        {props.row.category?.split(":")[0] === "Medications" &&
          props.row.formulary === "No" && (
            <img
              src={NonFormularyIcon}
              style={{ width: 24, marginRight: 4 }}
              alt="non-formulary-icon"
            />
          )}
        <HighlightedText
          text={props.value}
          substring={props.colDef.searchVal ? props.colDef.searchVal : ""}
        />
      </span>
    </Box>
  );
};
