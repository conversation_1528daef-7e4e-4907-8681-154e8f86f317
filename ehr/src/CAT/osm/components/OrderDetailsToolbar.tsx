// @ts-nocheck
import { useContext } from "react";
import { Box, IconButton } from "@mui/material";
import {
  But<PERSON>,
  Divider,
  LeftIconButton,
  RightIconButton,
  FollowTooltip as CciTooltip,
} from "@cci/mui-components";
import { useOsmContext } from "../contexts/OsmContext";
import { OEContext } from "@oe-src/context/OEContext";
import OrderStatusToolbar from "@oe-entry/components/OrderDetailsWindow/OrderStatusToolbar";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import RevertIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Revert.svg";
import CancelChangesDialog from "@oe-entry/components/OrderDetailsWindow/CancelChangesDialog";
import useSaveOrderEdit from "../hooks/useSaveOrderEdit";

const ResetTooltipMsg = "Reset Order to Default Values";

function OrderDetailsToolbar({
  orderSetId,
  currentPayload,
  dirty,
  editedOrders,
  setEditedOrders,
  handleFormCycle,
  cancelChangesDialogOpen,
  setCancelChangesDialogOpen,
  handleOrderDetailsClose,
  handleClose,
  hidden = false,
}: any) {
  const { allDataRef } = useOsmContext();
  // @ts-ignore
  const { masterCfgData } = useContext(OEContext);

  const orderState = currentPayload?.current ?? {};

  const onFormCycle = (direction: string) => {
    if (dirty) {
      let newOrders = Array.from(editedOrders);
      newOrders = newOrders.filter(
        (item: any) => item.order_id !== orderState.order_id
      );
      newOrders.push(orderState);
      setEditedOrders(newOrders);
    }
    handleFormCycle(direction);
  };

  const getEditedOrders = () => {
    let newOrders = Array.from(editedOrders);
    newOrders = newOrders.filter(
      (item: any) => item.order_id !== orderState.order_id
    );
    newOrders.push(orderState);
    return newOrders;
  };

  const updateOrders = (savedOrders: any) => {
    if ((savedOrders ?? []).length === 0) return;

    savedOrders.forEach((item: any) => {
      let set_id = item.order_set_id,
        order_id = item.order_id,
        order_data = item.order_data ? JSON.parse(item.order_data) : "";
      if (set_id && order_id && order_data) {
        order_data["orderid"] = order_id;
        allDataRef.current["masterOrders"][set_id][order_id] = order_data;
      }
    });
  };

  const { handleSave } = useSaveOrderEdit({
    orderSetType: "master",
    orderSetId: orderSetId,
    getEditedOrders,
    handleApplyClose: handleClose,
    updateOrders: updateOrders,
  });

  return (
    <OrderStatusToolbar
      style={{ flex: 1 }}
      orderState={currentPayload.current}
      masterCfgData={masterCfgData}
      hidden={hidden}
      rightComponent={
        <Box>
          <Box
            sx={{
              display: "flex",
              minWidth: "295px",
              gap: "10px",
              justifyContent: "end",
              marginRight: "8px",
              "& .MuiButton-root": {
                font: "normal 700 14px Roboto",
                "&:focus": {
                  outline: "none",
                  boxShadow: "none",
                },
                "&:hover:focus": {
                  outline: "none",
                  boxShadow: "none",
                },
              },
            }}
          >
            <Box
              sx={{
                display: "flex",
                border: 1,
                height: "32px",
                width: "32px",
                borderColor: "primary.main",
                borderRadius: "6px",
                backgroundColor: "white",
                justifyContent: "center",
              }}
            >
              <CciTooltip title={ResetTooltipMsg}>
                <IconButton onClick={handleOrderDetailsClose} color="secondary">
                  <img
                    src={RevertIcon}
                    alt="Reset Icon"
                    style={{
                      height: "24px",
                      width: "24px",
                    }}
                  />
                </IconButton>
              </CciTooltip>
            </Box>
            <Divider sx={{ height: "auto", width: "3px", margin: "0px" }} />
            <LeftIconButton
              onClick={() => onFormCycle("backward")}
              color="secondary"
              icon={<KeyboardArrowLeftIcon />}
            >
              Prev
            </LeftIconButton>
            <RightIconButton
              onClick={() => onFormCycle("forward")}
              color="primary"
              icon={<KeyboardArrowRightIcon />}
            >
              Next
            </RightIconButton>
            <Button onClick={handleSave} color="secondary" disabled={!dirty}>
              Done
            </Button>
          </Box>
          {cancelChangesDialogOpen && !hidden && (
            <CancelChangesDialog
              open={cancelChangesDialogOpen}
              handleClose={handleClose}
              handleContinue={() => setCancelChangesDialogOpen(false)}
            />
          )}
        </Box>
      }
    />
  );
}

export default OrderDetailsToolbar;
