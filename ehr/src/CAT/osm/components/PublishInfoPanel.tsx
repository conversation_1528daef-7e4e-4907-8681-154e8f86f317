import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import { OsmCciButton, OsmWhiteButton } from "../common/OsmButtons";
import { OsmDialogAction } from "@cci-monorepo/CAT/osm/OsmOrderSetPanel";
import { useOsmContext } from "../contexts/OsmContext";
import AffectedDataPanel, { getAffectedData } from "./AffectedDataPanel";
import WarningIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Warning_Large.svg";

type TokData = {
  notification?: string;
};

type TPublishInfoPanelProps = {
  publishData?: any;
  publishType: string;
  onOk: (action: OsmDialogAction, data: TokData) => void;
  onClose: () => void;
};

/**
 * The Publish Order Set Notification dialog
 */
const PublishInfoPanel = (props: TPublishInfoPanelProps) => {
  const { publishData, publishType, onOk, onClose } = props;

  const [publishNotification, setPublishNotification] = useState("");

  const msg =
    "This Order " +
    publishType +
    " has been recently modified with the following changes";

  const {
    allDataRef: { current },
  } = useOsmContext();
  const allSets = current?.sets;
  const allGroups = current?.groups;

  let affectedData: any;
  if (publishType === "group" && publishData) {
    affectedData = getAffectedData(publishData, allSets, allGroups);
  }

  const handleCancel = () => {
    onClose && onClose();
  };

  const handlePublish = () => {
    let okData = { ...publishData };
    if (publishNotification) {
      okData["publishNotify"] = publishNotification;
    }
    onOk && onOk(OsmDialogAction.publish, okData);
  };

  const handleNotificationChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPublishNotification(event.target.value);
  };

  return (
    <Stack data-testid={"osm-publish-order-dialog-id"}>
      {publishType === "group" && (
        <Box>
          <Box sx={{ display: "flex" }}>
            <img src={WarningIcon} alt="osm-publish-dialog-info-icon" />
            <Box sx={{ ml: 1 }}>
              <Typography variant="body1" sx={{ ml: 1, pt: 1 }}>
                Publish this Order Group will modify the following
                <br />
                Order Sets and Order Groups.
              </Typography>
            </Box>
          </Box>
          <Typography variant="body1" sx={{ ml: 1.5, pt: 1.5 }}>
            {(affectedData?.sets || affectedData?.groups) && (
              <AffectedDataPanel {...affectedData} />
            )}
          </Typography>
        </Box>
      )}
      <Typography variant="body1" sx={{ ml: 1, pt: 1 }}>
        Add text to notify users of modifications in this version.
      </Typography>
      <Box sx={{ ml: 0, mb: 2 }} />
      <Typography
        style={{ fontSize: "14px", fontWeight: "bold", marginTop: 36 }}
      >
        {msg}
      </Typography>
      <TextField
        multiline={true}
        minRows={2}
        sx={{
          background: "#FFFFFF",
          "&::placeholder": {
            fontSize: 16,
            fontWeight: "bold",
          },
        }}
        onChange={handleNotificationChange}
        placeholder={"Enter end user notes"}
      />
      <Divider
        sx={{
          height: "24px",
          borderColor: "#FFFFFF",
          borderBottomWidth: "3px",
        }}
      />
      <Toolbar sx={{ justifyContent: "flex-end" }}>
        <OsmWhiteButton buttonName={"Cancel"} onClick={handleCancel} />
        <OsmCciButton
          buttonName={
            publishNotification ? "Publish" : "Publish Without Notifications"
          }
          onClick={handlePublish}
          sx={{ ml: 2 }}
        />
      </Toolbar>
    </Stack>
  );
};

export default PublishInfoPanel;
