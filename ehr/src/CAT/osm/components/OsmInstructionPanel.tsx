import React, { useEffect, useState } from "react";
import {
  Box,
  Stack,
  TextField,
  TextFieldProps,
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  IconButton,
} from "@mui/material";

import { styled } from "@mui/material/styles";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { OsmAction } from "../OsmConstants";
import { useOsmContext } from "../contexts/OsmContext";
import OsmDataPanel from "./OsmDataPanel";
import BlueArrowDown from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import BlueArrowRight from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Right.svg";
import { OrderSetStatus } from "../OsmConstants";

/**
 * Help function to convert instruction string to instruction object (if it is json string) with
 * @param {string} rawData
 * @return {object}
 */
export const getInstruction = (rawData: any) => {
  if (!rawData) return undefined;

  let tmpData = rawData?.instruction;
  let tmpInstr = {
    instruction: "",
    styles: {},
  };

  let tmpInstruction;
  try {
    tmpInstruction = JSON.parse(tmpData);
  } catch (e) {
    tmpInstruction = tmpData;
    tmpInstr.styles = {
      textColor: "black",
      textFontStyle: "",
      textFontWeight: "",
    };
  }

  if (
    tmpInstruction &&
    Object.prototype.toString.call(tmpInstruction) === "[object Object]" &&
    !Array.isArray(tmpInstruction)
  ) {
    if (
      tmpInstruction.hasOwnProperty("instruction") &&
      Object.prototype.toString.call(tmpInstruction.instruction) ===
        "[object Object]"
    ) {
      tmpInstr = { ...tmpInstr, ...tmpInstruction.instruction };
    } else {
      tmpInstr = { ...tmpInstr, ...tmpInstruction };
    }
  } else {
    tmpInstr.instruction = tmpInstruction;
  }
  return tmpInstr;
};

/**
 * Helper that returns the color based on a key
 * @param {string} key - color key
 * @returns {string} - final color
 */
const getTextColor = (key: any) => {
  const colorMap = {
    black: "#00000094",
    purple: "purple",
  } as Record<string, string>;

  if (!key) return colorMap["black"];

  return colorMap.hasOwnProperty(key) ? colorMap[key] : colorMap["black"];
};

type TInstructionTextFieldProps = TextFieldProps & {
  textcolor: string;
  textfontstyle: string;
  textfontweight: string;
};

const ExtendedTextField: React.FC<TInstructionTextFieldProps> = (props) => {
  const { textcolor, textfontstyle, textfontweight, ...otherProps } = props;
  return <TextField {...otherProps} />;
};

const StyledInstructionTextField = styled(ExtendedTextField)(
  ({ textcolor, textfontstyle, textfontweight }) => ({
    width: "100%",
    background: "#FFFFFF",
    "&::placeholder": {
      fontSize: 16,
      fontWeight: "bold",
    },
    "& .MuiOutlinedInput-root": {
      fontSize: 16,
      fontWeight: 500,
      padding: "5px 10px",
    },
    ".MuiOutlinedInput-input": {
      color: textcolor,
      fontStyle: textfontstyle,
      fontWeight: textfontweight,
    },
  })
);

/**
 * Tool box for Instruction Editing.
 *
 * Has Styled and Color boxes for Bold, Italic, Black and Purple.
 *
 * @prop {func} handleUpdateStyle function to update text style and color
 * @prop {string} textstyle string of text style, it can be empty string or 'bold' or 'italic'
 * @prop {string} textcolor string of text color, it can be 'black' or 'purple'
 */
const InstructionEditToolBox = (props: any) => {
  const { handleUpdateStyle, textstyle, textcolor } = props;
  const [textStyle, setTextStyle] = useState(textstyle);
  const [textColor, setTextColor] = useState(textcolor);
  const toolBoxSx = {
    width: "32px",
    height: "32px",
    border: "1px solid",
    borderRadius: "4px",
  };

  const styleBoxSx = {
    ...toolBoxSx,
    display: "flex",
    fontSize: "16px",
    color: "#426EB6",
    justifyContent: "center",
    alignItems: "center",
  };

  const boxLabelSx = {
    height: "34px",
    fontSize: "14px",
    fontWeight: 500,
    display: "flex",
    alignItems: "center",
    ml: 3,
    mr: 3,
  };

  const handleBoxClick = (boxName: any) => {
    if (boxName === "bold" || boxName === "italic") {
      const tmpStyle = boxName === textStyle ? "" : boxName;
      handleUpdateStyle({ textstyle: tmpStyle, textcolor: boxName });
      setTextStyle(tmpStyle);
    } else if (boxName === "black" || boxName === "purple") {
      handleUpdateStyle({ textstyle: textStyle, textcolor: boxName });
      setTextColor(boxName);
    }
  };

  return (
    <Stack direction={"row"} alignContent={"center"} sx={{ ml: 4, mr: 8 }}>
      <Typography sx={boxLabelSx}>Style</Typography>
      <Box
        onClick={() => handleBoxClick("bold")}
        sx={{
          fontWeight: 600,
          ...styleBoxSx,
          background: textStyle === "bold" ? "#DEE9F8" : "#FFFFFF",
          borderRadius: "4px 0 0 4px",
        }}
      >
        B
      </Box>
      <Box
        onClick={() => handleBoxClick("italic")}
        sx={{
          fontStyle: "italic",
          ...styleBoxSx,
          background: textStyle === "italic" ? "#DEE9F8" : "#FFFFFF",
          borderLeft: "none",
          borderRadius: "0 4px 4px 0",
        }}
      >
        i
      </Box>
      <Typography sx={boxLabelSx}>Color</Typography>
      <Stack direction={"row"} alignContent={"center"} gap={2}>
        <Box
          onClick={() => handleBoxClick("black")}
          sx={{
            background: "#00000094",
            ...toolBoxSx,
            border: textColor === "black" ? "4px solid #FEC341" : "",
            borderRadius: "4px",
          }}
        />
        <Box
          onClick={() => handleBoxClick("purple")}
          sx={{
            background: "#652F8F",
            ...toolBoxSx,
            border: textColor === "purple" ? "4px solid #FEC341" : "",
          }}
        />
      </Stack>
    </Stack>
  );
};

/**
 * Instruction Edit-mode panel.
 * @todo candidate for Storybook.
 */
const OsmInstructionEditPanel = (props: any) => {
  const {
    instruction,
    updateEdit,
    setEditMode,
    inVersionHistory,
    instructionObj,
    setInstructionObj,
    onRemove,
    disabled,
    isTop,
    setOrGroupStatus,
    ...others
  } = props;

  const [editStr, setEditStr] = useState(instruction);

  const handleClickAway = () => {
    if (isTop) {
      updateEdit(editStr, instructionObj?.styles);
    } else {
      setInstructionObj({ ...instructionObj, instruction: editStr });
    }
    setEditMode(false);
  };

  const handleUpdateStyle = (styles: any) => {
    let newStyles = { ...instructionObj?.styles };
    if (styles.textstyle === "bold") {
      newStyles["textFontWeight"] = "bold";
      newStyles["textFontStyle"] = "normal";
    } else if (styles.textstyle === "italic") {
      newStyles["textFontWeight"] = "normal";
      newStyles["textFontStyle"] = "italic";
    } else {
      newStyles["textFontWeight"] = "normal";
      newStyles["textFontStyle"] = "normal";
    }

    if (styles.textcolor === "black" || styles.textcolor === "purple") {
      newStyles["textColor"] = styles.textcolor;
    }

    setInstructionObj({ ...instructionObj, styles: newStyles });
  };

  const handleEditChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEditStr(event.target.value);
  };

  return (
    <ClickAwayListener mouseEvent="onMouseDown" onClickAway={handleClickAway}>
      <Box sx={{ width: "100%" }}>
        <OsmDataPanel
          sx={{
            display: "flex",
            flexGrow: 1,
            gap: 2,
            boxShadow: "none",
            pl: 8,
          }}
          onRemove={() => {
            setEditStr("");
            onRemove();
          }}
          removable={
            isTop &&
            setOrGroupStatus === OrderSetStatus.Draft &&
            !inVersionHistory &&
            disabled === false
          }
          {...others}
        >
          <StyledInstructionTextField
            autoFocus={true}
            multiline={true}
            placeholder={"Enter Instruction"}
            value={editStr}
            onChange={handleEditChange}
            onFocus={(e) =>
              e.currentTarget.setSelectionRange(
                e.currentTarget.value.length,
                e.currentTarget.value.length
              )
            }
            textcolor={getTextColor(instructionObj?.styles?.textColor)}
            textfontweight={instructionObj?.styles?.textFontWeight}
            textfontstyle={instructionObj?.styles?.textFontStyle}
          />
          <InstructionEditToolBox
            handleUpdateStyle={handleUpdateStyle}
            textcolor={instructionObj?.styles?.textColor || "black"}
            textstyle={
              instructionObj?.styles?.textFontStyle === "italic"
                ? "italic"
                : instructionObj?.styles?.textFontWeight
            }
          />
        </OsmDataPanel>
      </Box>
    </ClickAwayListener>
  );
};

/**
 * Instruction View-mode panel.
 * @todo candidate for Storybook.
 */
const OsmInstructionViewPanel = (props: any) => {
  const {
    instruction,
    onRemove,
    setEditMode,
    inVersionHistory,
    instructionStyle,
    disabled,
    isTop,
    isNew,
    setOrGroupStatus,
    ...others
  } = props;

  const handleClick = () => {
    !inVersionHistory &&
      setEditMode &&
      setOrGroupStatus === OrderSetStatus.Draft &&
      setEditMode(true);
  };

  return (
    <OsmDataPanel
      {...others}
      onClick={handleClick}
      sx={{ display: "flex", flexGrow: 1, gap: 2, boxShadow: "none", pl: 8 }}
      removable={
        (isTop || isNew) &&
        setOrGroupStatus === OrderSetStatus.Draft &&
        !inVersionHistory &&
        disabled === false
      }
      onRemove={onRemove}
    >
      <StyledInstructionTextField
        multiline={true}
        InputProps={{
          disabled:
            disabled ||
            !((isTop || isNew) && setOrGroupStatus === OrderSetStatus.Draft),
        }}
        placeholder={"Enter Instruction"}
        value={instruction}
        textcolor={getTextColor(instructionStyle?.textColor)}
        textfontweight={instructionStyle?.textFontWeight}
        textfontstyle={instructionStyle?.textFontStyle}
      />
    </OsmDataPanel>
  );
};

/**
 * Show Instruction only.
 * @todo candidate for Storybook.
 * Used for Instruction in non-top-group
 */
const OsmInstructionShowPanel = (props: any) => {
  const {
    instruction,
    instructionStyle,
    isTop,
    isNew,
    setOrGroupStatus,
    disabled,
  } = props;

  return (
    <Box sx={{ display: "flex", flexGrow: 1, boxShadow: "none", pl: 8 }}>
      <StyledInstructionTextField
        multiline={true}
        InputProps={{
          disabled:
            disabled ||
            !((isTop || isNew) && setOrGroupStatus === OrderSetStatus.Draft),
        }}
        value={instruction}
        textcolor={instructionStyle?.textColor}
        textfontweight={instructionStyle?.textFontWeight}
        textfontstyle={instructionStyle?.textFontStyle}
      />
    </Box>
  );
};

/**
 * Instruction Panel for Order Set Management.
 * @todo candidate for Storybook.
 *
 * It is dedicated for instructions and can be in Order Set and Order Group.
 * It can appear beneath a category or Weblink component, and above any order and Group.
 * It can expand in height if the text runs into a second line when there is enough text.
 * Features
 *  - Each Order Set/Group can have only one Instruction, if exists, the "Add Instruction" button is disabled.
 *  - It has two mode: display mode and edit mode. To switch between those two modes
 *     * the user can click on an order intruction line item to switch to eidt mode.
 *     * the user can click anywhere outside the edit-mode instruction to apply changes and switch back to text mode.
 *  - It can be deleted by hovering over when in display mode and clicking trash icon.
 *
 * @prop {string|object} instruction
 * @prop {boolean} editMode optional, default is false.
 * @prop {boolean} showonly optional, just show Instruction text, default is false.
 */
const OsmInstructionPanel = (props: any) => {
  const [editMode, setEditMode] = useState(false);
  const { osmState, osmDispatch } = useOsmContext();
  const [instructionObj, setInstructionObj] = useState(
    getInstruction(props.data)
  );
  const [accordionOpen, setAccordionOpen] = useState(true);

  useEffect(() => {
    const tmpInstructionObj = getInstruction(props.data);
    setInstructionObj(tmpInstructionObj);
  }, [props.data]);

  if (!instructionObj || instructionObj?.instruction === null) return <></>;

  const removeInstruction = () => {
    osmDispatch({
      type: OsmAction.RemoveInstruction,
      data: { ...props.data, instruction: "" },
    });
  };

  const updateEdit = (newInstruction: any, newStyles: any) => {
    osmDispatch({
      type: OsmAction.UpdateInstruction,
      data: {
        ...osmState.data,
        instruction: JSON.stringify({
          instruction: newInstruction,
          styles: newStyles,
        }),
      },
    });
  };

  // @todo simplify the above return statement with early returns approach.
  return instructionObj?.instruction || props.isTop ? (
    <Accordion
      square={true}
      disableGutters={true}
      expanded={accordionOpen}
      sx={{
        boxShadow: "0px 2px 1px -1px rgba(0,0,0,0.2)",
        "&::before": {
          content: "none",
          display: "none",
        },
      }}
    >
      <AccordionSummary
        aria-controls="order-setting-detail-content"
        sx={{
          display: "flex",
          flexDirection: "row-reverse",
          background: "#FFFFFF",
          paddingLeft: "0px",
          width: "100%",
          minHeight: "24px",
          marginTop: "12px",
          marginBottom: accordionOpen ? "0px" : "12px",
          ".MuiAccordionSummary-content": { m: 0 },
        }}
        onClick={() => setAccordionOpen(!accordionOpen)}
      >
        <Box
          sx={{
            height: "24px",
            width: "24px",
            p: 0,
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <IconButton size="small" disableRipple>
            <img
              src={accordionOpen ? BlueArrowDown : BlueArrowRight}
              alt="arrow"
            />
          </IconButton>
        </Box>
        <Typography
          sx={{
            pl: 1,
            m: 0,
            fontSize: 16,
            fontWeight: "500",
            color: "#11181F",
          }}
        >
          Instruction
        </Typography>
      </AccordionSummary>
      <AccordionDetails sx={{ pb: 4, pl: 0, pr: 0 }}>
        <OsmDataPanel sx={{ display: "flex", flexGrow: 1, gap: 2 }}>
          {props.showonly ? (
            <OsmInstructionShowPanel
              {...props}
              instruction={instructionObj?.instruction}
              instructionStyle={instructionObj?.styles}
            />
          ) : editMode && props.disabled === false && props.isTop ? (
            <OsmInstructionEditPanel
              {...props}
              onRemove={removeInstruction}
              updateEdit={updateEdit}
              setEditMode={setEditMode}
              instruction={instructionObj?.instruction}
              setInstructionObj={setInstructionObj}
              instructionObj={instructionObj}
            />
          ) : (
            <OsmInstructionViewPanel
              {...props}
              onRemove={removeInstruction}
              setEditMode={setEditMode}
              instruction={instructionObj?.instruction}
              instructionStyle={instructionObj?.styles}
            />
          )}
        </OsmDataPanel>
      </AccordionDetails>
    </Accordion>
  ) : (
    <Box sx={{ mt: 1, mb: 1 }}></Box>
  );
};

export default OsmInstructionPanel;
