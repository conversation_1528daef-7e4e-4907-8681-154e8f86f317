/**
 * OmsReducer.js
 *
 * Handle states of OSM actions
 */

import { OsmType, OsmAction } from "../OsmConstants";
import { TOSMState } from "./OsmContext";

// initial reducer state
export const initOsmState: TOSMState = {
  osmType: OsmType.OsmSearchSet, // updated by switching OSM sub-tab
  searchStr: "", // updated by search field changing
  action: OsmAction.Unknown, // updated by tool buttons
  name: "", // the name of an Order Set or Order Group
};

/**
 * OsmReducer
 * @param {*} state OsmState
 * @param {*} action OsmAction
 * @returns OsmState
 */
const OsmReducer = (state: TOSMState, action: TOSMState) => {
  let newState = { ...state };
  let actionType = action.type;

  newState.name = "";
  newState.searchStr = "";
  newState.action = actionType;

  switch (actionType) {
    case OsmAction.SwitchTab:
      newState.osmType = action.tabName;
      break;

    case OsmAction.RemoveTab:
      newState.data = undefined;
      break;

    case OsmAction.SearchSetList:
    case OsmAction.SearchGroupList:
      newState.searchStr = action.searchStr;
      break;

    case OsmAction.DuplicateSet:
      newState.name = action.name;
      newState.data = action.data;
      break;

    case OsmAction.CreateNewSet:
    case OsmAction.CreateNewGroup:
      newState.data = undefined;
      break;

    case OsmAction.DuplicateGroup:
      newState.name = action.name;
      newState.data = action.data;
      break;

    case OsmAction.EditExistSet:
    case OsmAction.EditExistGroup:
      newState.name = action.name;
      newState.data = action.data;
      break;

    case OsmAction.AddGroups:
    case OsmAction.AddOrders:
    case OsmAction.AddWeblink:
    case OsmAction.DoRetire:
    case OsmAction.DoPublish:
    case OsmAction.RemoveGroups:
    case OsmAction.RemoveInstruction:
    case OsmAction.RemoveOrders:
    case OsmAction.RemoveWeblink:
    case OsmAction.SortWeblink:
    case OsmAction.SetGroupIds:
    case OsmAction.ShowVersionPage:
    case OsmAction.UpdateData:
    case OsmAction.UpdateSearchTerm:
    case OsmAction.UpdateDisplayOrders:
    case OsmAction.CreateNewVersion:
    case OsmAction.UpdateSave:
    case OsmAction.Redo:
    case OsmAction.Undo:
      newState.data = { ...newState.data, ...action.data };
      break;

    case OsmAction.UpdateInstruction:
    case OsmAction.UpdateWeblink:
      newState.name = action.name;
      newState.data = action.data;
      break;

    case OsmAction.SaveAndClose:
      break;

    default:
      break;
  }

  return newState;
};

export default OsmReducer;
