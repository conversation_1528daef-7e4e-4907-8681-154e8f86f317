/**
 * OsmContext.js
 *
 * Wrap OsmReducer to handle Order Set Management actions
 */
import React, { useContext, useReducer } from "react";
import OsmReducer, { initOsmState } from "./OsmReducer";
import { OsmAction } from "@cci-monorepo/CAT/osm/OsmConstants";

// create osmContext with initial states
export const OsmContext = React.createContext(initOsmState);

export type TSubGroup = {
  group_id: string;
  orderGroupName: string;
  subIds: any;
  [key: string]: any;
};

export type TGroup = {
  group_id: string;
  orderGroupName: string;
  subIds: TSubGroup[];
  [key: string]: any;
};

export type TOSMState = {
  name: string;
  data?: {
    name?: string;
    instruction?: any;
    [key: string]: any;
  };
  action: OsmAction;
  [key: string]: any;
};

export type TOsmContext = {
  osmState: TOSMState;
  // osmDispatch: React.Dispatch<TOSMState>;
  osmDispatch: React.Dispatch<any>;
  allDataRef: {
    current: {
      sets: any;
      userSets: any;
      orders: any;
      groups: Record<string, TGroup>;
      masterOrders: any;
    };
  };
};

// create OsmContextProvider with give initial values
export const OsmContextProvider = ({ children }: any) => {
  const [osmState, osmDispatch] = useReducer(OsmReducer, initOsmState);

  const allDataRef = React.useRef({ masterOrders: {} });

  const values: TOsmContext = {
    osmState,
    osmDispatch,
    // @ts-ignore
    allDataRef,
  };
  // @ts-ignore
  return <OsmContext.Provider value={values}>{children}</OsmContext.Provider>;
};

// create hook for easy use
export const useOsmContext = () => {
  // @ts-ignore
  const context = useContext<TOsmContext>(OsmContext);

  if (context === undefined) {
    throw new Error("useOsmContext must be used within OsmContextProvider");
  }

  return context;
};
