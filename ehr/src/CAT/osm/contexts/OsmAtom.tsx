import { atom } from "jotai";
import { isEqual, uniqWith } from "lodash";

export const osmWeblinksExpandedAtom = atom(true);
export const osmWeblinkRemovedAtom = atom(-1);
export const osmOrderSetExpandedAtom = atom(true);
export const osmUpdateAtom = atom(false);
export const osmUndoArrayAtom = atom<any>({});
export const osmCurrentIndexAtom = atom<any>({});

export const osmSetUndoAtom = atom(null, (get, set, tabid: any, data: any) => {
  let undoArray: any[] = get(osmUndoArrayAtom);
  let currentUndo = get(osmCurrentIndexAtom);
  if (!currentUndo.hasOwnProperty(tabid) || currentUndo[tabid].current === 0) {
    if (!undoArray.hasOwnProperty(tabid)) {
      undoArray[tabid] = [];
      undoArray[tabid].push(data);
    } else {
      undoArray[tabid].unshift(data);
      if (undoArray.length > 11) {
        undoArray[tabid] = undoArray[tabid].slice(0, 11);
      }
    }
  } else {
    undoArray[tabid] = [
      data,
      ...undoArray[tabid].slice(currentUndo[tabid].current),
    ];
  }
  currentUndo[tabid] = { current: 0 };
  set(osmCurrentIndexAtom, currentUndo);
  undoArray[tabid] = uniqWith(undoArray[tabid], isEqual);
  set(osmUndoArrayAtom, undoArray);
});

export const setClearCurrentUndoAtom = atom(null, (get, set, tabid: any) => {
  let undoArray: any[] = get(osmUndoArrayAtom);
  let currentUndo = get(osmCurrentIndexAtom);
  undoArray[tabid] = [];
  delete undoArray[tabid];
  currentUndo[tabid] = { current: 0 };
  delete currentUndo[tabid];
  set(osmUndoArrayAtom, undoArray);
  set(osmCurrentIndexAtom, currentUndo);
});
