/**
 * OsmPageContext.tsx
 * @description The context for Order Set Management tabs (page)
 */
import React, { useState, useRef } from "react";
import { useSetAtom } from "jotai";
import { OrderSetStatus, OsmType } from "../OsmConstants";
import { TOsmDataType } from "@cci-monorepo/CAT/osm/OsmPropTypes";
import { setClearCurrentUndoAtom } from "./OsmAtom";

export const defaultMaxNumTab = 9;
export type TOsmPageListStateTabListData = {
  id?: string;
  name?: string;
  orderIds?: string[];
  groupIds?: string[];
  [key: string]: any;
};

export type TOsmPageListStateTabList = {
  id?: string;
  tabid: string;
  label: string;
  type: OsmType;
  data: TOsmDataType;
  name?: string;
  closable?: boolean;
  isNew?: boolean;
  status?: OrderSetStatus;
};

export type TOsmPageListState = {
  tabList: TOsmPageListStateTabList[];
  selectedTab: number;
  maxNumTab: number;
};

export type TOsmPageContext = {
  osmPageState: TOsmPageListState;
  setOsmPageState: (data: TOsmPageListState) => void;
  addOsmPage: (page: TOsmPageListStateTabList) => void;
  removeOsmPage: (page: TOsmPageListStateTabList) => void;
  removeCurrentPage: () => void;
  switchOsmPageTo: (index: number) => void;
  updateTabPage: (info: any, tabid: any) => void;
  updateCurrentPage: (info: any, updateIsNew?: any) => void; // You might want to replace 'any' with a more specific type
  pageStatusRef: any;
  updatePageStatus: (tabid: string, status: any) => void; // You might want to replace 'any' with a more specific type
};

const initOsmPageListState: TOsmPageListState = {
  tabList: [
    {
      tabid: "osm-search-order-sets",
      label: "Search Order Sets",
      type: OsmType.OsmSearchSet,
      // @ts-ignore
      data: {},
    },
    {
      tabid: "osm-search-order-sets",
      label: "Search Order Groups",
      type: OsmType.OsmSearchGroup,
      // @ts-ignore
      data: {},
    },
  ],
  selectedTab: 0,
  maxNumTab: defaultMaxNumTab,
};

// Create OsmPageContext with default two propertie values: "osmPageState" and "setOsmPageState"
export const OsmPageContext = React.createContext<TOsmPageContext>({
  osmPageState: initOsmPageListState,
  setOsmPageState: () => {},
  addOsmPage: () => {},
  removeOsmPage: () => {},
  removeCurrentPage: () => {},
  switchOsmPageTo: () => {},
  updateTabPage: () => {},
  updateCurrentPage: () => {},
  pageStatusRef: {},
  updatePageStatus: () => {},
});

// Wrap Context.Provider to give initial values
export const OsmPageContextProvider = ({ children }: any) => {
  const [osmPageState, setOsmPageState] = useState({
    ...initOsmPageListState,
  });

  const setClearUndo = useSetAtom(setClearCurrentUndoAtom);
  const pageStatusRef = useRef<Record<string, string>>({});

  const setTabLabel = (info: any, selectedTab: any, addVersion?: any) => {
    if (info?.data?.name) {
      return info.data.status === OrderSetStatus.Active &&
        info.data.latestVersion
        ? info.data.name + " (v" + info.data.version + ")"
        : info.data.status === OrderSetStatus.Retired
          ? info.data.name + " (Retired)"
          : info.data.name;
    }
    if (
      selectedTab.type === OsmType.OsmNewGroup &&
      selectedTab.label.trim().length === 0
    ) {
      return "New Order Group";
    }
    if (
      selectedTab.type === OsmType.OsmNewSet &&
      selectedTab.label.trim().length === 0
    ) {
      return "New Order Set";
    }

    return selectedTab.label;
  };

  /**
   * If the given page exists, switch to the page,
   * otherwise add the page to the last of osmPages.
   * @param {Object} page
   */
  const addOsmPage = (page: TOsmPageListStateTabList) => {
    let newIndex = -1;
    osmPageState.tabList.forEach((thePage, index) => {
      if (thePage.tabid === page.tabid) {
        newIndex = index;
        switchOsmPageTo(index);
        return false;
      }
    });

    if (newIndex >= 0) {
      return;
    }

    newIndex = osmPageState.tabList.length;
    const newTabLIst = [...osmPageState.tabList, page];
    setOsmPageState({
      ...osmPageState,
      tabList: newTabLIst,
      selectedTab: newIndex,
    });
  };

  const removeOsmPage = (page: TOsmPageListStateTabList) => {
    setClearUndo(page.tabid);
    const newPages = osmPageState.tabList.filter(
      (tab) => tab?.tabid !== page.tabid
    );

    if (pageStatusRef.current[page.tabid]) {
      delete pageStatusRef.current[page.tabid];
    }

    let selTab = 0;
    if (
      page.type === OsmType.OsmNewGroup ||
      page.type === OsmType.OsmExistGroup
    )
      selTab = 1;
    setOsmPageState({
      ...osmPageState,
      tabList: newPages,
      selectedTab: selTab,
    });
  };

  const removeCurrentPage = () => {
    removeOsmPage(osmPageState.tabList[osmPageState.selectedTab]);
  };

  const switchOsmPageTo = (index: any) => {
    setOsmPageState({
      ...osmPageState,
      selectedTab: index,
    });
  };

  //Update information
  const updateTabPage = (info: any, tabid: any) => {
    let newTabList = [...osmPageState.tabList];
    let utab: any = {};
    newTabList.forEach((thePage, index) => {
      if (thePage.tabid === tabid) {
        utab = thePage;
        return false;
      }
    });
    //const utab = newTabList[tabid];

    if (utab) {
      utab.data = {
        ...utab.data,
        ...info.data,
      };
      utab.label = setTabLabel(info, utab, true);
      setOsmPageState({
        ...osmPageState,
        tabList: newTabList,
      });
    }
  };
  // Update information in current page
  const updateCurrentPage = (info: any, updateIsNew = false) => {
    let newTabList = [...osmPageState.tabList];
    const selectedTab = newTabList[osmPageState.selectedTab];
    if (updateIsNew && selectedTab.isNew) {
      selectedTab.isNew = false;
      if (selectedTab.tabid === "osm-new-order-group-id") {
        selectedTab.tabid = "osm-group-" + info.data.id + "-id";
      } else if (selectedTab.tabid === "osm-new-order-set-id") {
        selectedTab.tabid = "osm-set-" + info.data.id + "-id";
      }
    }
    if (!selectedTab.data) {
      // @ts-ignore
      newTabList[osmPageState.selectedTab].data = {};
    }

    selectedTab.data = {
      ...selectedTab.data,
      ...info.data,
    };

    selectedTab.label = setTabLabel(info, selectedTab);

    if (info?.data?.orderIds) {
      // @ts-ignore
      selectedTab.data.orderIds = info.data.orderIds;
    }

    if (info?.data?.groupIds) {
      if (!selectedTab.data.groupIds) {
        selectedTab.data.groupIds = [];
      }
      selectedTab.data.groupIds = info.data.groupIds;
    }

    setOsmPageState({
      ...osmPageState,
      tabList: newTabList,
    });
  };

  /**
   * Updata a tab status, i.e. dirt, error, ...
   * @param {string} tabid
   * @param {object} status { statusName: statusValue }
   */
  const updatePageStatus = (tabid: string, status: any) => {
    if (!tabid) return;

    pageStatusRef.current[tabid] = {
      // @ts-ignore TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
      ...pageStatusRef.current[tabid],
      ...status,
    };
  };

  const values = {
    osmPageState,
    setOsmPageState,
    addOsmPage,
    removeOsmPage,
    removeCurrentPage,
    switchOsmPageTo,
    updateCurrentPage,
    updateTabPage,
    pageStatusRef,
    updatePageStatus,
  };

  return (
    <OsmPageContext.Provider value={values}>{children}</OsmPageContext.Provider>
  );
};

/**
 * useOsmPageContext
 * @description Get the OsmPageContext from React's useContext
 * @returns useOsmPageContext
 */
export function useOsmPageContext() {
  const context = React.useContext(OsmPageContext);
  if (context === undefined) {
    throw new Error(
      "useOsmPageContext is used under an OsmPageContextProvider"
    );
  }
  return context;
}
