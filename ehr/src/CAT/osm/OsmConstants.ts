/**
 * Base constants for Order Set management Tool
 */

/**
 * The sub-module types for Order Set Management
 */
export enum OsmType {
  Unknown = "unknown",
  OsmSearchSet = "osmSearchSet", // Search Order Sets
  OsmSearchGroup = "osmSearchGroup", // Search Order Groups
  OsmExistSet = "osmExistSet", // Existing Order set
  OsmExistGroup = "osmExistGroup", // Existing Order group
  OsmNewSet = "osmNewSet", // New Order Set
  OsmNewGroup = "osmNewGroup", // New Order Group
  // OsmAdmin= 'osmAdmin',               // Generic Admission
}

/**
 * List of supported OSM actions
 *
 *  - switch OSM sub-module tab
 *  - search string change
 *  - button click
 */
export enum OsmAction {
  ActivateOrderGroup = "activateOrderGroup",
  // ActivateOrderSet = "activateOrderSet",
  Active = "active",
  AddGroups = "addGroups",
  AddHyperlink = "addHyperlink",
  AddOrders = "addOrders",
  AddWeblink = "addWeblink",
  AllOrders = "allOrders",
  BrowseOrders = "browseOrders",
  Cancel = "cancel",
  ConvertToDraft = "convertToDraft",
  CreateNewGroup = "createNewGroup", // "Create New Order Groups" button in "Search Order Groups" tab
  CreateNewSet = "createNewSet", // "Create New Order Sets" button in "Search Order Sets" tab
  DoConvertToDraft = "doConvertToDraft",
  DoRetire = "doRetire",
  DoPublish = "doPublish",
  Drafts = "drafts",
  DuplicateSet = "duplicateSet",
  DuplicateGroup = "duplicateGroup",
  EditExistGroup = "editExistGroup", // double click a Group row in "Search Order Groups" tab
  EditExistSet = "editExistSet", // double click a Set row in "Search Order Sets" tab
  ExportToDocx = "exportToDocx",
  ExportToPdf = "exportToPdf",
  ExportToXlsx = "exportToXlsx",
  MyOrders = "myOrders",
  OpenAffectedOrderGroupSetDialog = "openAffectedOrderSetGroupDialog",
  OrderGroups = "orderGroups",
  Print = "print",
  Publish = "Publish", // filter chip in Search Order Sets/Groups
  PublishChanges = "publishChanges",
  PublishGroup = "publishGroup",
  PublishSet = "publishSet",
  Redo = "redo",
  RemoveGroups = "removeGroups",
  RemoveInstruction = "removeInstruction",
  RemoveOrders = "removeOrders",
  RemoveTab = "removeTab",
  RemoveWeblink = "removeWeblink",
  RetireGroup = "retireGroup",
  RetireSet = "retireSet",
  Retired = "retired",
  ReviewSet = "reviewSet",
  SaveAndClose = "saveAndClose",
  SaveGroup = "saveGroup",
  Save = "save",
  SaveSet = "saveSet",
  SearchGroup = "searchGroup", // SearchField in a Group tab
  SearchGroupList = "searchGroupList", // SearchField in "Search Order Groups" tab
  SearchOrder = "searchOrder", // SearchField in a Set tab
  SearchSetList = "searchSetList", // SearchField in "Search Order Sets" tab
  SetGroupIds = "setGroupIds",
  ShowVersionPage = "showVersionPage",
  SortWeblink = "sortWeblink",
  SwitchTab = "switchTab",
  Undo = "undo",
  Unknown = "unknown",
  UpdateData = "updateData",
  UpdateInstruction = "updateInstruction",
  UpdateSearchTerm = "updateSearchTerm",
  UpdateWeblink = "updateWeblink",
  VersionHistory = "versionHistory",
  UpdateDisplayOrders = "updateDisplayOrders",
  UpdateSave = "updateSave",
  CreateNewVersion = "createNewVersion",
}

/**
 * The status of Order Set.
 */
export enum OrderSetStatus {
  Draft = "Draft",
  Review = "Review",
  Active = "Active", // obsolete in SZ27314
  Published = "Published",
  Retired = "Retired",
}

/**
 * Selection types for Orders in a group.
 */
export enum GroupOrderSelectionType {
  SelectRequired = 0, // select one required
  SelectOne = 1, // select only one
  MinimalOne = 2, // select at least one, can be more
  Multiple = 3, // select 0 or multiple
}

/**
 * Order Set Locations
 */
export enum OrderSetLocation {
  HospitalAndAmbulatory = "hospitalAndAmbulatory",
  HospitalOnly = "hospitalOnly",
  AmbulatoryOnly = "ambulatoryOnly",
}

/**
 * Order Set Locations Data Format Names
 */
export enum OrderSetLocationDataFormatName {
  HospitalAndAmbulatory = "hospitalAndAmbulatory",
  Hospital = "hospital",
  Ambulatory = "ambulatory",
}

export enum OsmToolType {
  Set = "set",
  Group = "group",
}

export const defaultAllOrderCategoryName = "ALL_ORDERS";
