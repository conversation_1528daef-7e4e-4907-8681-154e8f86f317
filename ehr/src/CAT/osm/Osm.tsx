import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { queryClientDefaultOptions } from "@cci-monorepo/common";
import { OsmPageContextProvider } from "./contexts/OsmPageContext";
import { OsmContextProvider } from "./contexts/OsmContext";
import OsmContainer from "./OsmContainer";

// Initialize queryClient for QueryClientProvider
const queryClient = new QueryClient(queryClientDefaultOptions);

/**
 * Order Set Management Tool entry
 */
const Oms = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <OsmPageContextProvider>
        <OsmContextProvider>
          <OsmContainer />
        </OsmContextProvider>
      </OsmPageContextProvider>
    </QueryClientProvider>
  );
};

export default Oms;
