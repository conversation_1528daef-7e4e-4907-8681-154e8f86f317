import React, { useState, useEffect } from "react";
import { Chip, FormControl, TextField, InputAdornment } from "@mui/material";
import SearchIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Search.svg";
import { styled } from "@mui/material/styles";

const StyledChip = styled(Chip)(({ theme }) => ({
  "&.MuiChip-root": {
    fontSize: 14,
    color: "#11181F",
    height: 32,
    backgroundColor: "#fafafa",
    border: "1px solid #b1b1b1",
    borderRadius: 4,
    margin: "0 2px",
  },
}));

/**
 * Styles the additional search term input field and container which displays the terms
 */
const StyledFormControl = styled(FormControl)(({ theme }) => ({
  "&.MuiFormControl-root": {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    width: "100%",
    flexWrap: "wrap",
    flexDirection: "row",
    border: "1px solid lightgray",
    padding: 4,
    marginBottom: 12,
    borderRadius: "4px",
    "&> div.container": {
      gap: "2px",
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
    },
    "& > div.container > span": {
      backgroundColor: "gray",
      padding: "1px 3px",
      borderRadius: "4px",
    },
    "&:hover": {
      border: "1px solid black",
    },
  },
}));

export const OsmSearchTerms = ({
  placeHolder,
  onChange,
  terms,
  disabled,
}: any) => {
  const [values, setValues] = useState(terms || []);
  const [currentValue, setCurrentValue] = useState("");
  /**
   * Handle any change to values in textfield.
   *
   * @todo - refactor after full mui and typescript migration.
   *
   * @param {*} event
   * @param {*} values
   * @returns {void}
   */
  const handleKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    if (e.key === "Enter") {
      const val = (e.target as HTMLInputElement).value.trim();
      if (/[a-zA-Z0-9]/.test(val) && val.length >= 3 && !values.includes(val)) {
        setValues((oldState: any) => [...oldState, val]);
        setCurrentValue("");
        if (onChange) {
          onChange([...values, val]);
        }
      }
    }
    // handle backspace key
    if (e.key === "Backspace") {
      if ((e.target as HTMLInputElement).value.length === 0) {
        let arr = [...values];
        arr.pop();
        setValues(arr);
        if (onChange) {
          onChange(arr);
        }
      }
    }
  };

  useEffect(() => {
    setValues(terms);
  }, [terms]); // eslint-disable-line

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentValue(e.target.value);
  };

  const handleDelete = (item: any, index: any) => {
    let arr = [...values];
    arr.splice(index, 1);
    setValues(arr);
    if (onChange) {
      onChange(arr);
    }
  };

  return (
    <StyledFormControl>
      <div className={"container"}>
        {values.map((item: any, index: any) => (
          <StyledChip
            disabled={disabled}
            size="small"
            onDelete={() => handleDelete(item, index)}
            label={item}
            key={`chip-${index}`}
          />
        ))}
      </div>
      <TextField
        disabled={disabled}
        value={currentValue}
        variant="standard"
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={(event) => {
          // on lost focus
          // @ts-ignore
          handleKeyDown({
            ...event,
            key: "Enter",
          } as React.KeyboardEvent<HTMLElement>);
        }}
        placeholder={values.length === 0 ? placeHolder : ""}
        InputProps={{
          disableUnderline: true,
          startAdornment:
            values.length === 0 ? (
              <InputAdornment position="start">
                <img src={SearchIcon} alt={"Search"} />
              </InputAdornment>
            ) : (
              <></>
            ),
        }}
        sx={{
          width: values.length >= 1 ? 150 : 275,
          padding: 0,
        }}
      />
    </StyledFormControl>
  );
};

export default OsmSearchTerms;
