import OsmGroupDataPanel from "../components/OsmGroupDataPanel";

const OsmSetDataRenderer = (params: any) => {
  const {
    props,
    removeGroup,
    editingOrderId,
    setEditingOrderId,
    handleOrderEdit,
  } = params.colDef.cellRendererParams;

  return (
    <OsmGroupDataPanel
      {...props}
      group={params.row.group}
      parentGroupIds={[]}
      selections={params.row.group.selections}
      onRemove={removeGroup}
      inVersionHistory={props.inVersionHistory}
      removable={!props.inVersionHistory}
      draggable={!props.inVersionHistory}
      set={props.set}
      editingOrderId={editingOrderId}
      setEditingOrderId={setEditingOrderId}
      handleOrderEdit={handleOrderEdit}
      selectionType={params.row.group.selectionType}
    />
  );
};

export default OsmSetDataRenderer;
