import React from "react";
import { styled } from "@mui/material/styles";
import { TreeItem, TreeView } from "@mui/x-tree-view";
import CollapseIcon from "@cci-monorepo/oe-shared/src/assets/Icn_Elbow_Collapse.svg";
import ExpandIcon from "@cci-monorepo/oe-shared/src/assets/Icn_Elbow_Expand.svg";
import NestedIcon from "@cci-monorepo/oe-shared/src/assets/Icn_Elbow_Nested.svg";
import HomeIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Home_Star.svg";

const StyledTreeItem = styled(TreeItem)(({ theme, label }) => ({
  ".Mui-selected": {
    background: "#FEC341",
  },
  "&.MuiTreeItem-root > .MuiTreeItem-content": {
    // @ts-ignore @todo Add better ts check here
    height: label.length > 17 && label.includes(" ") ? 48 : 24,
    ":hover": {
      background: "#F6E596",
    },
  },
  "& .MuiTypography-root": {
    fontSize: label === "All Orders" ? 16 : 14,
    textWrap: "nowrap",
  },
}));

/**
 * Build tree structure recursively.
 * Each treeItem
 *  - key
 *  - nodeId
 *  - label
 *  - children
 * @props treeItems an array of tree items
 */
const getTreeItemsFromData = (treeItems: any) => {
  return treeItems?.map((treeItemData: any) => {
    let children = undefined;
    if (treeItemData.children && treeItemData.children.length > 0) {
      children = getTreeItemsFromData(treeItemData.children);
    }
    return (
      <StyledTreeItem
        key={treeItemData.key}
        nodeId={treeItemData.key}
        label={treeItemData.label}
        children={children}
        // @ts-ignore @todo - will be migrated to cci/mui-components
        data={treeItemData.data}
        icon={
          treeItemData.label === "All Orders" && (
            <img src={HomeIcon} alt="icon-tree-nested" />
          )
        }
      />
    );
  });
};

/**
 * A treeview comonent for OSM data.
 * It is wrap of @mui/lab/TreeView.
 * TODO: update the component when @mui/lab/TreeView is released and available.
 * @prop treeData {Array} required, an object array of data
 */
const OsmTreeView = (props: any) => {
  const { treeData, ...others } = props;

  return (
    <TreeView
      aria-label="osm-tree-view"
      defaultCollapseIcon={<img src={CollapseIcon} alt="icon-tree-collapse" />}
      defaultExpandIcon={<img src={ExpandIcon} alt="icon-tree-expand" />}
      defaultEndIcon={<img src={NestedIcon} alt="icon-tree-nested" />}
      style={{ paddingLeft: 4 }}
      {...others}
    >
      {getTreeItemsFromData(treeData)}
    </TreeView>
  );
};

export default OsmTreeView;
