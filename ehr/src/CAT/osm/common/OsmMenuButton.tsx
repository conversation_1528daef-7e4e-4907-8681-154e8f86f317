import React from "react";
import { OsmWhiteButton } from "./OsmButtons";
import { Menu, MenuItem } from "@mui/material";
import DownArrow from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import { SxProps, Theme } from "@mui/material/styles";

type OsmWhiteMenuButtonProps = {
  buttonName: string;
  menuOptions: any[];
  menuHandler: (event: React.MouseEvent, type: any) => void;
  sx?: SxProps<Theme>;
};

/**
 * Button with sub-menus and down arrow
 * @prop menuOptions array of list of sub-menu object
 *  - name display name
 *  - type menu type
 *  - hidden true to hide the menu
 *  - disabled true to disable the menu
 * @prop menuHandler function to handle menu click
 */
export const OsmWhiteMenuButton = (props: OsmWhiteMenuButtonProps) => {
  const { menuOptions, menuHandler, ...others } = props;
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleButtonClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClick = (event: React.MouseEvent, type: any) => {
    setAnchorEl(null);
    menuHandler && menuHandler(event, type);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <OsmWhiteButton
        id="osm-more-button"
        variant="contained"
        aria-controls={open ? "osm-more-button-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        endIcon={<img src={DownArrow} alt="down-arrow" />}
        onClick={handleButtonClick}
        {...others}
      />

      <Menu
        id="osm-more-button-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{
          "& .MuiPaper-root": {
            borderRadius: 0,
          },
          "& .MuiMenu-list": {
            px: 0,
            py: 1,
            display: "flex",
            flexDirection: "column",
          },
        }}
      >
        {menuOptions
          ?.filter((option: any) => !option.hidden)
          .map((option: any) => (
            <MenuItem
              key={option.type}
              onClick={(event) => handleMenuClick(event, option.type)}
              disabled={option.disabled}
              sx={{
                "&.MuiButtonBase-root": {
                  display: "flex",
                  justifyContent: "start",
                  px: 4,
                  py: 1,
                  alignItems: "center",
                },
                "&:hover": {
                  backgroundColor: "#F7EEC1",
                },
              }}
            >
              {option.name}
            </MenuItem>
          ))}
      </Menu>
    </>
  );
};
