import React, { useState } from "react";
import { Box } from "@mui/material";
import RemoveIcon from "@cci-monorepo/oe-shared/src/assets/osm/Icn_Remove.svg";

/**
 * A Panel with remove icon on right.
 * The remove-icon will show when mouse over the box,
 * and when click the remove-icon, it calls onRemove.
 * @prop {object} data the data to be removed
 * @prop {func} onRemove the remove function
 * @prop {boolean} readonly if true, no remove icon
 */
const OsmPanelWithRemover = (props: any) => {
  const { data, onRemove, readonly } = props;
  const [showRemoveIcon, setShowRemoveIcon] = useState(false);

  const handleMouseEnter = () => {
    !readonly && setShowRemoveIcon(true);
  };
  const handleMouseLeave = () => setShowRemoveIcon(false);

  const handleRemove = (event: any, data: any) => {
    onRemove && onRemove(data);
  };

  return (
    <Box
      sx={{ display: "flex", flexGrow: 1 }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <Box sx={{ display: "flex", flexGrow: 1 }}>{props.children}</Box>
      {showRemoveIcon && (
        <img
          src={RemoveIcon}
          alt={"osm-remove-icon"}
          onClick={(e) => handleRemove(e, data)}
          style={{ paddingRight: "8px" }}
        />
      )}
    </Box>
  );
};

export default OsmPanelWithRemover;
