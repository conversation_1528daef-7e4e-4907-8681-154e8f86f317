import React from "react";
import { Button } from "@cci/mui-components";

type TOsmCciButtonProps = {
  buttonName: string;
  style?: React.CSSProperties;
  onClick: (e?: any) => void;
  disabled?: boolean;
  [key: string]: any;
};
// @todo reuse Button from cci/mui-components
export const OsmCciButton = ({
  buttonName,
  style,
  ...others
}: TOsmCciButtonProps) => {
  return (
    <Button
      style={{
        height: "32px",
        fontSize: "14px",
        ...style,
      }}
      {...others}
    >
      {buttonName}
    </Button>
  );
};

type TOsmWhiteButtonProps = {
  buttonName?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  onClick: (e: React.MouseEvent<HTMLElement>) => void;
  [key: string]: any;
};
// @todo reuse Button from cci/mui-components
export const OsmWhiteButton = ({
  buttonName,
  style,
  disabled,
  ...others
}: TOsmWhiteButtonProps) => {
  return (
    // @ts-ignore @todo - will be migrated to cci/mui-components
    <Button
      disabled={disabled}
      style={{
        height: "32px",
        fontSize: "14px",
        background: "#FFFFFF",
        color: disabled ? "#B0AFAF" : "#4B6EAF",
        border: disabled ? "1px solid #C0C0C0" : "1px solid #4B6EAF",
        ...style,
      }}
      {...others}
    >
      {buttonName}
    </Button>
  );
};

type TOsmCciOvalButtonProps = {
  buttonName: string;
  selected: boolean;
  style?: React.CSSProperties;
  others: any;
};

export const OsmCciOvalButton = ({
  buttonName,
  selected,
  style,
  ...others
}: TOsmCciOvalButtonProps) => {
  return (
    <Button
      style={{
        height: "32px",
        fontSize: "14px",
        ...style,
      }}
      active={selected}
      {...others}
    >
      {buttonName}
    </Button>
  );
};

// TODO: add icon at left of button label
// Buttons with icons and label
type TOsmIconButtonProps = {
  buttonName: string;
  style?: React.CSSProperties;
  others: any;
};

export const OsmIconButton = ({
  buttonName,
  style,
  ...others
}: TOsmIconButtonProps) => {
  return (
    // @ts-ignore @todo - will be migrated to cci/mui-components
    <Button
      style={{
        height: "32px",
        fontSize: "14px",
        ...style,
      }}
      {...others}
    >
      {buttonName}
    </Button>
  );
};
