import React from "react";
import { Box } from "@mui/material";
import Accordion, { AccordionProps } from "@mui/material/Accordion";
import Typography, { TypographyProps } from "@mui/material/Typography";
import { styled, Theme } from "@mui/material/styles";
import AccordionSummary, {
  AccordionSummaryProps,
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails, {
  AccordionDetailsProps,
} from "@mui/material/AccordionDetails";
import BlueArrowDown from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Down.svg";
import BlueArrowRight from "@cci-monorepo/oe-shared/src/assets/Icon_Blue_Arrow_Right.svg";

const StyledTypography = styled(Typography)<TypographyProps>(
  ({ theme }: { theme: Theme }) => ({
    color: "#212121",
    fontFamily: "Roboto",
    fontSize: "16px",
    fontWeight: 500,
    lineHeight: "normal",
    letterSpacing: "0em",
    textAlign: "left",
    marginLeft: "10px",
    marginRight: "10px",
  })
);

type OsmAccordionProps = AccordionProps & {
  title?: string;
  children?: React.ReactNode;
};

const StyledAccordion = styled(Accordion)<OsmAccordionProps>(
  ({ theme }: { theme: Theme }) => ({
    marginTop: 0,
    boxShadow: "none",
    border: "none",
    borderTopLeftRadius: "0px",
    borderTopRightRadius: "0px",
    "&.MuiPaper-root.MuiAccordion-root::before": {
      display: "none",
    },
  })
);

const StyledAccordionSummary = styled(AccordionSummary)<AccordionSummaryProps>(
  ({ theme }) => ({
    height: "40px",
    padding: "0 32px 0 0",
    minHeight: "40px",
    border: "none",
    flexDirection: "row-reverse",
    "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
      transform: "rotate(0deg)",
    },
    "& .MuiAccordionSummary-content": {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
    },
  })
);

const StyledAccordionDetails = styled((props: AccordionDetailsProps) => (
  <MuiAccordionDetails {...props} />
))(({ theme }) => ({
  padding: 0,
}));

const OsmAccordion: React.FC<OsmAccordionProps> = ({
  title,
  children,
  expanded,
  ...otherProps
}) => {
  return (
    <StyledAccordion disableGutters={true} expanded={expanded} {...otherProps}>
      <StyledAccordionSummary
        expandIcon={
          <img
            src={!expanded ? BlueArrowRight : BlueArrowDown}
            alt="right-arrow"
          />
        }
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
          }}
        >
          <StyledTypography variant="inherit">{title}</StyledTypography>
        </Box>
      </StyledAccordionSummary>
      <StyledAccordionDetails>
        {expanded ? children : null}
      </StyledAccordionDetails>
    </StyledAccordion>
  );
};

export default OsmAccordion;
