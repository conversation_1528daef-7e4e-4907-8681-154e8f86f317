import OsmGroupDataPanel from "../components/OsmGroupDataPanel";
import { OsmTopOrderDataPanel } from "../components/OsmOrderDataPanel";

const OsmGroupDataRenderer = (params: any) => {
  const { props, removeGroup, removeOrder, allOrders, allGroups, group } =
    params.colDef.cellRendererParams;
  const subId = params.row;

  return (
    <>
      {subId.orderid && allOrders[subId.orderid] && (
        <OsmTopOrderDataPanel
          {...props}
          order={allOrders[subId.orderid]}
          onRemove={removeOrder}
          draggable={!props.inVersionHistory}
          groupId={group.id}
          selectionType={group.selectionType}
        />
      )}
      {subId.group_id && allGroups[subId.group_id] && (
        <OsmGroupDataPanel
          {...props}
          group={allGroups[subId.group_id]}
          removable={!props.inVersionHistory}
          onRemove={removeGroup}
          draggable={!props.inVersionHistory}
          selectable={false}
          selectionType={allGroups[subId.group_id].selectionType}
          parentSelectionType={group.selectionType}
        />
      )}
    </>
  );
};

export default OsmGroupDataRenderer;
