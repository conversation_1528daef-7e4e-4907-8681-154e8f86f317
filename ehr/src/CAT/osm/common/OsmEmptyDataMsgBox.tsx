import { Box, Typography } from "@mui/material";

/**
 * Show message at background when there is no data for the component.
 * @prop message - string or array of strings
 */
const OsmEmptyDataMsgBox = (props: any) => {
  const { messages, additionalMsg, sx } = props;
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        pt: 8,
        width: "100%",
        height: "100%",
        background: "#FFFFFF",
      }}
    >
      {typeof messages == "string" ? (
        <Typography
          sx={{ fontSize: 22, fontWeight: 500, opacity: "44%", ...sx }}
        >
          {messages}
        </Typography>
      ) : (
        <>
          {messages.map((msg: any, index: any) => {
            return (
              <Typography
                key={index}
                sx={{
                  fontFamily: "Raleway",
                  fontSize: 24,
                  fontWeight: 400,
                  opacity: "44%",
                  ...sx,
                }}
              >
                {msg}
              </Typography>
            );
          })}
          {additionalMsg && (
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontSize: 16,
                fontWeight: 400,
                opacity: "44%",
                ...sx,
              }}
            >
              {additionalMsg}
            </Typography>
          )}
        </>
      )}
    </Box>
  );
};

export default OsmEmptyDataMsgBox;
