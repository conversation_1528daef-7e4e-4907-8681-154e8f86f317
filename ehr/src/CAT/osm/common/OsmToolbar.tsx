import React from "react";
import { styled } from "@mui/material/styles";
import { AppBar, Toolbar } from "@mui/material";

const StyledOsmAppBar = styled(AppBar)(({ theme }) => ({
  background: "white",
  height: "auto",
  boxShadow: "none",
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  "& .MuiButton-root": {
    marginLeft: "10px",
  },
}));

const OsmToolbar = ({ children, sx, ...others }: any) => {
  return (
    <StyledOsmAppBar position="static">
      <Toolbar sx={{ ...sx }} {...others}>
        {children}
      </Toolbar>
    </StyledOsmAppBar>
  );
};

export default OsmToolbar;
