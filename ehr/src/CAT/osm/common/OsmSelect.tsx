import React from "react";
import { styled } from "@mui/material/styles";
import { InputBase, MenuItem, Select, SelectProps } from "@mui/material";

const StyledInputBase = styled(InputBase)(() => ({
  " .MuiInputBase-input": {
    paddingLeft: 10,
    fontSize: 14,
  },
}));

const StyledMenuItem = styled(MenuItem)(() => ({
  "&.MuiButtonBase-root": {
    display: "flex",
    justifyContent: "flex-start",
    paddingLeft: 10,
    fontSize: 14,
  },
}));

const StyledSelect = styled(Select)(() => ({
  "&.MuiInputBase-root": {
    background: "#FFFFFF",
    display: "flex",
  },
  "&.MuiButtonBase-root": {
    display: "flex",
  },
  borderRadius: "4px",
  border: "1px solid #D3D3D3",
  "& :hover": {
    border: "1px solid #77B2DF",
  },
}));

/**
 * A simple wrap of MUI Select component for Order Set Management Tools styling.
 * @prop {object} props
 *  - options -- options for Select
 * @prop {React.forwardRef} ref
 */
const OsmSelect = React.forwardRef<SelectProps<number>>((props, ref) => {
  return (
    <StyledSelect input={<StyledInputBase />} {...props} ref={ref}>
      {/*// @ts-ignore @todo - will be migrated to cci/mui-components.*/}
      {props?.options.map((option: any) => (
        <StyledMenuItem value={option?.value} key={option?.value}>
          {option?.display}
        </StyledMenuItem>
      ))}
    </StyledSelect>
  );
});

export default OsmSelect;
