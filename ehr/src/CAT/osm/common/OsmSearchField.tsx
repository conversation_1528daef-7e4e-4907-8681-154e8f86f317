import { useRef } from "react";
import { InputAdornment, TextField, Box } from "@mui/material";
import SearchIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Search.svg";
import { QuickSearchOrdersPanel } from "../components/QuickSearchOrdersPanel";
import ClickAwayListener from "@mui/material/ClickAwayListener";

const defaultSearchFieldWidth = 287;

export const OsmSearchField = (props: any) => {
  const { placeHolder, onSearch, sx, onOpen, value, ...others } = props;
  const placeHolderText = placeHolder ? placeHolder : "Search";
  const inputRef = useRef<HTMLElement>(null);
  if (inputRef?.current && props.autoFocus) {
    inputRef.current?.focus();
  }
  const onSearchKeyPress = (e: any) => {
    if (e && e.key === "Enter") {
      e.preventDefault();
      const value = e.target.value;
      onSearch && onSearch(value);
    }
  };

  return props.onClose ? (
    <ClickAwayListener onClickAway={props.onClose}>
      <Box>
        <TextField
          type="search"
          placeholder={placeHolderText}
          InputProps={{
            fontSize: 14,
            background: "red",
            padding: 0,
            opacity: "1",
            startAdornment: (
              <InputAdornment position="start">
                <img src={SearchIcon} alt={"Search"} />
              </InputAdornment>
            ),
          }}
          onClick={onOpen ? onOpen : null}
          sx={{
            width: defaultSearchFieldWidth,
            fontSize: 14,
            padding: 0,
            "& .MuiInputBase-input": {
              padding: 1,
              fontSize: 14,
            },
            ...sx,
          }}
          value={value}
          {...others}
        />
        {props.popperAnchorRef?.current != null && props.open && (
          <QuickSearchOrdersPanel
            currentData={props.currentData}
            open={props.open}
            handleClose={props.onClose}
            anchorEl={props.popperAnchorRef.current}
            searchVal={value}
          />
        )}
      </Box>
    </ClickAwayListener>
  ) : (
    <TextField
      type="search"
      inputRef={inputRef}
      placeholder={placeHolderText}
      InputProps={{
        fontSize: 14,
        background: "red",
        padding: 0,
        opacity: "1",
        startAdornment: (
          <InputAdornment position="start">
            <img src={SearchIcon} alt={"Search"} />
          </InputAdornment>
        ),
      }}
      sx={{
        width: defaultSearchFieldWidth,
        fontSize: 14,
        padding: 0,
        "& .MuiInputBase-input": {
          padding: 1,
          fontSize: 14,
        },
        ...sx,
      }}
      onKeyPress={onSearchKeyPress}
      value={value}
      {...others}
      autoFocus
    />
  );
};

export default OsmSearchField;
