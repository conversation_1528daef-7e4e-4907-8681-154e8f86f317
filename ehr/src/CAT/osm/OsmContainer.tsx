import React, { SyntheticEvent, useEffect, useRef } from "react";
import { useSet<PERSON>tom } from "jotai";
import Box from "@mui/material/Box";
import TopAdminTabs from "@cci-monorepo/common/mui-components/src/components/tabs/Admin/TopAdminTabs";
import { CciMainTab } from "@cci/mui-components";
import OsmOrderSetListPanel from "./OsmOrderSetListPanel";
import OsmOrderSetPanel from "./OsmOrderSetPanel";
import OrderSetsIcon from "@cci-monorepo/oe-shared/src/assets/osm/icn_Order_Sets.svg";
import OrderGroupIcon from "@cci-monorepo/oe-shared/src/assets/osm/icn_Order_Group.svg";
import Tooltip from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import { osmSetUndoAtom } from "./contexts/OsmAtom";

import {
  OrderSetStatus,
  OsmAction,
  OsmToolType,
  OsmType,
} from "./OsmConstants";
import {
  TOsmPageListStateTabList,
  useOsmPageContext,
} from "./contexts/OsmPageContext";
import { useOsmContext } from "./contexts/OsmContext";

import { useOsmSaveMutation } from "./hooks/useOsmMutation";
import { getPageData } from "./util/OsmSaveDataHelper";

import {
  DialogVariants,
  useAlert,
} from "@cci-monorepo/Psat/common/layouts/AdminLayoutA/context/AlertContext";
/**
 * Constant that configures how long a user can be inactive in this container before the page will autosave
 * Might be replaced with a masterconfig setting in future
 */
const AUTOSAVE_AFTER_MS = 900000;

const StyledTooltip = styled("span")({
  fontWeight: "bold",
  font: "normal 400 15px Roboto",
  color: "#F0F0F0",
  height: "auto",
  padding: "8px, 4px, 8px, 4px",
});

const OsmTooltipLabel = (name: any) => {
  let labelStr = name;
  if (labelStr.length > 25) {
    labelStr = name.substr(0, 25) + "...";
  }
  return (
    <Tooltip title={<StyledTooltip>{name}</StyledTooltip>}>
      <span>{labelStr}</span>
    </Tooltip>
  );
};

/**
 * Order Set Management Tool main component.
 * It contains 2 types of components:
 *  - OsmOrderSetListPanel: the component for searching Order Set or Order Group
 *  - OsmOrderSetPanel:     the component for creating new Order/editing exist Set or New Order Group
 */
const OsmContainer = () => {
  const {
    osmPageState,
    addOsmPage,
    removeOsmPage,
    switchOsmPageTo,
    updateCurrentPage,
    pageStatusRef,
    updatePageStatus,
  } = useOsmPageContext();

  const { selectedTab, tabList, maxNumTab } = osmPageState;

  const { osmState, osmDispatch, allDataRef } = useOsmContext();
  const setOmsUndo = useSetAtom(osmSetUndoAtom);

  const createAlert = useAlert();

  const inactivityRef = useRef<number | null>(null);
  const pageRef = useRef<TOsmPageListStateTabList[]>();

  const saveDataMutate = useOsmSaveMutation();

  const handleTabChange = (
    event: React.SyntheticEvent<Element, Event>,
    newValue: any
  ) => {
    event.stopPropagation();
    switchOsmPageTo(newValue);
    osmDispatch({
      type: OsmAction.Unknown,
    });
  };

  const handleTabClose = (event: any, tab: any) => {
    event.stopPropagation();
    if (pageStatusRef.current[tab.tabid]) {
      handleAutoSave([tab]);
      setTimeout(function () {
        osmDispatch({
          type: OsmAction.RemoveTab,
        });
        removeOsmPage(tab);
      }, 100);
    } else {
      osmDispatch({
        type: OsmAction.RemoveTab,
      });
      removeOsmPage(tab);
    }
  };

  type Ttmpdata = {
    id?: string;
    name?: string;
    [key: string]: any;
  };
  const handleAddPage = () => {
    const tmpdata = osmState.data ? osmState.data : ({} as Ttmpdata);
    const name =
      tmpdata.status === OrderSetStatus.Retired
        ? osmState.name + " (Retired)"
        : osmState.name;
    const tmpid = tmpdata.id ? tmpdata.id : name;
    let tabid = "";
    switch (osmState.action) {
      case OsmAction.CreateNewSet:
        tabid = "osm-new-order-set-id";
        break;
      case OsmAction.DuplicateSet:
        tabid = "osm-duplicate-set-" + tmpid + "-id";
        break;
      case OsmAction.CreateNewGroup:
        tabid = "osm-new-order-group-id";
        break;
      case OsmAction.DuplicateGroup:
        tabid = "osm-duplicate-group-" + tmpid + "-id";
        break;
      case OsmAction.EditExistSet:
        tabid = "osm-set-" + tmpid + "-id";
        break;
      case OsmAction.EditExistGroup:
        tabid = "osm-group-" + tmpid + "-id";
        break;
      default:
        break;
    }

    if (tabList.length >= maxNumTab) {
      let tabOpend = false;
      tabList.forEach((_page) => {
        if (_page.tabid === tabid) {
          tabOpend = true;
        }
      });
      if (!tabOpend) {
        createAlert({
          variant: DialogVariants.INFO,
          msg: "Reach maximal pages.",
        });
        return;
      }
    }

    let newPage;
    switch (osmState.action) {
      case OsmAction.CreateNewSet:
        newPage = {
          tabid: tabid,
          label: "New Order Set",
          type: OsmType.OsmNewSet,
          closable: true,
          isNew: true,
          status: OrderSetStatus.Draft,
          data: { ...tmpdata, name: "" },
        } as TOsmPageListStateTabList;
        break;

      case OsmAction.DuplicateSet:
        newPage = {
          tabid: tabid,
          label: "Copy of " + name,
          type: OsmType.OsmNewSet,
          closable: true,
          isNew: true,
          status: OrderSetStatus.Draft,
          data: { ...tmpdata, name: `Copy of ${name}` },
        } as TOsmPageListStateTabList;
        break;

      case OsmAction.CreateNewGroup:
        newPage = {
          tabid: tabid,
          label: "New Order Group",
          type: OsmType.OsmNewGroup,
          closable: true,
          isNew: true,
          status: OrderSetStatus.Draft,
          data: { ...tmpdata, name: "" },
        } as TOsmPageListStateTabList;
        break;

      case OsmAction.DuplicateGroup:
        newPage = {
          tabid: tabid,
          label: "Copy of " + name,
          type: OsmType.OsmNewGroup,
          closable: true,
          isNew: true,
          status: OrderSetStatus.Draft,
          data: { ...tmpdata, name: `Copy of ${name}` },
        } as TOsmPageListStateTabList;
        break;

      case OsmAction.EditExistSet:
        newPage = {
          tabid: tabid,
          label: name,
          type: OsmType.OsmExistSet,
          closable: true,
          data: tmpdata,
        } as TOsmPageListStateTabList;
        break;

      case OsmAction.EditExistGroup:
        newPage = {
          tabid: tabid,
          label: name,
          type: OsmType.OsmExistGroup,
          closable: true,
          data: tmpdata,
        } as TOsmPageListStateTabList;
        break;

      default:
        break;
    }

    if (newPage) {
      setOmsUndo(tabid, newPage.data);
      addOsmPage(newPage);
      if (
        osmState.action === OsmAction.DuplicateGroup ||
        osmState.action === OsmAction.DuplicateSet
      )
        osmDispatch({
          type: OsmAction.UpdateData,
          data: { ...newPage.data },
        });
    }
  };

  const handleUpdatePage = (needUpdateStatus = false, updateIsNew = false) => {
    updateCurrentPage(
      {
        data: osmState.data,
      },
      updateIsNew
    );
    if (needUpdateStatus) {
      updatePageStatus(tabList[selectedTab].tabid, { dirty: true });
      setOmsUndo(tabList[selectedTab].tabid, osmState.data);
    }
    osmDispatch({
      type: OsmAction.Unknown,
    });
  };

  const renderPanel = () => {
    const page = tabList[selectedTab];
    const { type: dataType } = page;

    const { name } = page?.data;

    let status: OrderSetStatus = OrderSetStatus.Draft;
    if (page?.data.status in OrderSetStatus) {
      status = page.data.status as OrderSetStatus;
    }

    switch (dataType) {
      case OsmType.OsmSearchSet:
      case OsmType.OsmSearchGroup:
        return <OsmOrderSetListPanel searchType={dataType} />;

      case OsmType.OsmExistSet:
      case OsmType.OsmExistGroup:
      case OsmType.OsmNewSet:
      case OsmType.OsmNewGroup:
        return (
          <OsmOrderSetPanel
            dataType={dataType}
            name={name}
            {...page}
            status={status}
          />
        );

      default:
        return <h2>Non-supported Component.</h2>;
    }
  };

  type TPages = {
    tabid: string;
    label: string;
    type: string;
    data: any;
  }[];

  /**
   * Handle auto save page data
   * @param {Array} pages array of page data
   */
  const handleAutoSave = (pages: TPages | null = null) => {
    const savePages = pages ? pages : (pageRef.current || []).slice(2);
    savePages.forEach((pageData: any) => {
      if (
        pageStatusRef.current[pageData.tabid]?.dirty &&
        !pageStatusRef.current[pageData.tabid]?.error
      ) {
        const dataType =
          pageData.type === OsmType.OsmExistSet ||
          pageData.type === OsmType.OsmNewSet
            ? OsmToolType.Set
            : OsmToolType.Group;
        const saveData = getPageData(
          pageData.data,
          dataType,
          allDataRef,
          pageData?.isNew,
          true
        );
        if (saveData) {
          saveDataMutate.mutate(saveData);
          pageStatusRef.current[pageData.tabid].dirty = false;
        }
      }
    });
  };

  useEffect(() => {
    switch (osmState.action) {
      case OsmAction.CreateNewSet:
      case OsmAction.DuplicateSet:
      case OsmAction.CreateNewGroup:
      case OsmAction.DuplicateGroup:
      case OsmAction.EditExistSet:
      case OsmAction.EditExistGroup:
        handleAddPage();
        break;
      case OsmAction.AddGroups:
      case OsmAction.AddOrders:
      case OsmAction.AddWeblink:
      case OsmAction.UpdateWeblink:
      case OsmAction.UpdateInstruction:
      case OsmAction.UpdateData:
      case OsmAction.RemoveGroups:
      case OsmAction.RemoveOrders:
      case OsmAction.RemoveWeblink:
      case OsmAction.SortWeblink:
      case OsmAction.UpdateSearchTerm:
      case OsmAction.RemoveInstruction:
        handleUpdatePage(true);
        break;
      case OsmAction.ShowVersionPage:
      case OsmAction.SetGroupIds:
        handleUpdatePage();
        break;
      case OsmAction.UpdateSave:
        handleUpdatePage(false, true);
        break;
      default:
        break;
    }
  }, [osmState.action, osmState.name]); // eslint-disable-line

  useEffect(() => {
    pageRef.current = tabList;
  }, [tabList]);

  useEffect(() => {
    pageRef.current = tabList;
    window.onbeforeunload = () => {
      handleAutoSave();
      return undefined;
    };

    return () => {
      handleAutoSave();
      window.onbeforeunload = null;
    };
  }, []); // eslint-disable-line

  const resetAutosaveTimeoutTrigger = () => {
    if (inactivityRef.current) {
      clearTimeout(inactivityRef.current);
      inactivityRef.current = window.setTimeout(
        handleAutoSave,
        AUTOSAVE_AFTER_MS
      );
    }
  };

  useEffect(() => {
    inactivityRef.current = window.setTimeout(
      handleAutoSave,
      AUTOSAVE_AFTER_MS
    );

    document.addEventListener("mousemove", resetAutosaveTimeoutTrigger);
    document.addEventListener("keydown", resetAutosaveTimeoutTrigger);

    return () => {
      document.removeEventListener("mousemove", resetAutosaveTimeoutTrigger);
      document.removeEventListener("keydown", resetAutosaveTimeoutTrigger);
      if (inactivityRef.current) {
        clearTimeout(inactivityRef.current);
      }
    };
  }, []); // eslint-disable-line

  return (
    <Box
      data-testid="osm-main-container-id"
      sx={{ height: "100vh", display: "flex", flexDirection: "column" }}
    >
      <Box sx={{ backgroundColor: "#637e83" }}>
        <TopAdminTabs
          value={selectedTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            height: "28px",
            "& .MuiTabs-indicator": {
              display: "none",
            },
          }}
        >
          {tabList.map((tab, index) => {
            return tab.closable ? (
              <CciMainTab
                label={OsmTooltipLabel(tab.label)}
                type={tab.type}
                tabid={tab.tabid}
                key={index}
                icon={
                  tab.type === OsmType.OsmExistSet
                    ? OrderSetsIcon
                    : OrderGroupIcon
                }
                onCloseTab={(e: SyntheticEvent) => {
                  handleTabClose(e, tab);
                }}
              />
            ) : (
              <CciMainTab label={OsmTooltipLabel(tab.label)} key={index} />
            );
          })}
        </TopAdminTabs>
      </Box>
      {renderPanel()}
    </Box>
  );
};

export default OsmContainer;
