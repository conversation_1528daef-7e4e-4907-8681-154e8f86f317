import React, { useState } from "react";
import { CssBaseline, ThemeProvider } from "@mui/material";
import { osmTheme } from "./common/CatTheme";
import CatNavBar from "./CatNavBar";
import Osm from "./osm";
import { AlertProvider } from "@cci-monorepo/Psat/common/layouts/AdminLayoutA/context/AlertContext";
import NonMedDatabase from "@cci-monorepo/CAT/NonMedication";
import { ChoicesProvider } from "@cci-monorepo/Psat/context/ChoicesContext";
import { theme } from "../Psat/common/PsatTheme";
import { OEContextProvider } from "@oe-src/context/OEContext";
import { EntryContext } from "@oe-src/entry";

const modules = ["Non-Medication Database", "Order Set Management"];

const defaultModule = 0;

/**
 * The module for CPOE-related Clinical Admin Tools
 */
const CAT = () => {
  const [moduleValue, setModuleValue] = useState<number>(defaultModule);

  // Create minimal Entry context value
  const entryContextValue = {
    openDCDialog: () => {},
    setDCTitle: () => {},
    setDCAction: () => {},
  };

  const handleModuleChange = (value: number) => {
    setModuleValue(value);
  };

  return (
    <ThemeProvider theme={osmTheme}>
      <AlertProvider>
        <ChoicesProvider>
          <OEContextProvider>
            <EntryContext.Provider value={entryContextValue}>
              <CssBaseline />
              <CatNavBar
                modules={modules}
                moduleValue={moduleValue}
                handleModuleChange={handleModuleChange}
              />
              {moduleValue === 0 && (
                <ThemeProvider theme={theme}>
                  <NonMedDatabase onStatusChange={() => {}} />
                  {/* onStatusChange may use in case of handeling dirty status */}
                </ThemeProvider>
              )}
              {moduleValue === 1 && <Osm />}
            </EntryContext.Provider>
          </OEContextProvider>
        </ChoicesProvider>
      </AlertProvider>
    </ThemeProvider>
  );
};

export default CAT;
