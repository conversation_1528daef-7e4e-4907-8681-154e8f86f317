const permissions = cci.cfg.perms.split(" ");

// permission name convension as in Sfaff Control Tool
// #231
export const isOSMAdminPermission = permissions.includes("231:E");
// #232
export const isOSMManagerPermission = permissions.includes("232:E");
// #233
export const isOSMReviewerPermisssion = permissions.includes("233:E");
// #93
export const isOrderSetMoverPermission = permissions.includes("93:E");

// complex permissions
export const isCreateNewPermission =
  isOSMAdminPermission || isOSMManagerPermission;
export const isEditPermission = isCreateNewPermission;
export const isChangeStatusPermission =
  isOrderSetMoverPermission && isCreateNewPermission;
