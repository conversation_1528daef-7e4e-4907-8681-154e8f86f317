import React from "react";
import { styled } from "@mui/material/styles";
import { Switch } from "@mui/material";

// @ts-ignore @todo - will be migrated to cci/mui-components
const StyledSwitch = styled(Switch)(({ theme, width = 50 }) => ({
  padding: 0,
  height: 24,
  width: width,
  borderRadius: 15,
  background: "#CBCACA 0% 0% no-repeat padding-box",
  "& .Mui-disabled": {
    opacity: 0.5,
  },
  "& .MuiSwitch-switchBase": {
    top: "3px",
    left: "3px",
    color: "#fafafa",
    width: "18px",
    height: "18px",
    borderRadius: "15px",
    zIndex: 1,
    position: "absolute",
    background: "#FFFFFF 0% 0% no-repeat padding-box",
    transition:
      "left 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
    "&:hover": {
      background: "#FFFFFF 0% 0% no-repeat padding-box",
    },
  },
  "&.MuiSwitch-root .Mui-checked + .MuiSwitch-track": {
    background: "#479852 0% 0% no-repeat padding-box",
    opacity: 1,
  },
  "& .MuiSwitch-switchBase.Mui-disabled + .MuiSwitch-track": {
    opacity: 0.5,
  },
  "& .MuiSwitch-switchBase.Mui-checked": {
    transform: "translateX(25px)",
  },
}));

const CatSwitch = (props: any) => {
  return <StyledSwitch {...props} />;
};

const StyledLabelSwitch = styled(CatSwitch)(({ theme, label = "" }) => ({
  width: label.length * 5 + 60,
  "& .MuiSwitch-switchBase": {
    "&:before": {
      content: '"' + label + '"',
      paddingLeft: 80,
      fontSize: 12,
      width: "auto",
      textWrap: "nowrap",
    },
  },
}));

/**
 * The Switch component with Label inside.
 * TODO: add label into Switch component with checked/unchecked status and leave circle image on the left
 */
export const CatLabelSwitch = (props: any) => {
  return <StyledLabelSwitch {...props} />;
};

export default CatSwitch;
