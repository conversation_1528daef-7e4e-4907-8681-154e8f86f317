import { styled } from "@mui/material/styles";
import Chip, { ChipProps } from "@mui/material/Chip";
import IconRadio from "@cci-monorepo/oe-shared/src/assets/Radio_Unselected_Small.svg";
import IconCheckedRadio from "@cci-monorepo/oe-shared/src/assets/radio_only_checked.svg";

interface ICciChipProps extends ChipProps {
  /**
   * CCI. Custom selected attribute. to show 'IconCheckedRadio' or 'IconRadio'.
   */
  selected: boolean;
}

const StyledChip = styled(Chip)({
  "&.MuiChip-root": {
    height: 24,
    borderRadius: "18px",
    padding: "0px 8px",
    font: "normal 500 14px Roboto",
    minWidth: 64,
  },
  "& .MuiChip-label": {
    font: "inherit",
    color: "inherit",
    padding: 0,
  },
  "&.MuiChip-filled": {
    background: "#319436",
    border: "1px solid #319436",
    color: "white",
  },
  "&.MuiChip-outlined": {
    background: "white",
    border: "1px solid #B1B1B1",
    color: "#000000",
  },
  "&.Mui-disabled": {
    color: "#7C7C7C",
    border: "1px solid #D6D6D6",
    background: "#D6D6D6",
  },
});

/**
 * Simple wrap mui Chip component
 * @param {object} param
 */
const CatChip = (props: ICciChipProps) => {
  const { selected, ...other } = props;
  return <StyledChip {...other} variant={selected ? undefined : "outlined"} />;
};

export default CatChip;

const StyledCatChip = styled(CatChip)({
  "&.MuiChip-root": {
    borderRadius: "24px",
    padding: "0px 6px",
    "& .MuiChip-icon": {
      margin: "0px 4px 0px 0px",
      width: 20,
    },
  },
});

/**
 * CatChip with default icon
 * @param {*} props
 */
export const CatIconChip = (props: ICciChipProps) => {
  const selected = props?.disabled ? false : props?.selected;
  return (
    <StyledCatChip
      icon={
        <img src={selected ? IconCheckedRadio : IconRadio} alt="chip-icon" />
      }
      {...props}
      selected={selected}
    />
  );
};
