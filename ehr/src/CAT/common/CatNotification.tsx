import React from "react";
import { Box, Snackbar } from "@mui/material";
import CloseIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Close_Simple.svg";
import SuccessIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Success_Small.svg";
import ErrorIcon from "@cci-monorepo/oe-shared/src/assets/Icon_error.png";
import InfoIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Info.svg";
import WarningIcon from "@cci-monorepo/oe-shared/src/assets/Icon_Dialog_Warning_Large.svg";

const defaultDuration = 2000; // 2 seconds
const defaultSuccessBkColor = "#E9F5E9";

export type TCatNotification = {
  variant?: string;
  open: boolean;
  msg: string;
};

export const InitCatNotification: TCatNotification = {
  variant: "",
  open: false,
  msg: "",
};

/**
 * Common  notification toast
 * @param {object} props
 *  - open {boolean}, the toast shows up or not, default is false
 *  - handleClose, {function}, handle closing the toast
 *  - msg {string}, the message, default is empty
 *  - duration {int}, auto hide toast after given milli-seconds, default is 2000 (2 seconds)
 *  - bkcolor {sstring}, the toast background color, default is '#E9F5E9'
 *  - anchor {object}, where to show the toast, default is {vertical: 'bottom', horizontal: 'center'}, the possible value
 *     { horizontal: 'center' | 'left' | 'right', vertical: 'bottom' | 'top' }
 *  - variant {string}, the variant of the toast, it can be 'success', 'error', 'warn', 'info', default is 'info'
 *  - sx {object} sx prop for message
 */
const CatNotification = (props: any) => {
  const { variant, open, handleClose, msg, duration, bkcolor, anchor, sx } =
    props;

  const backColor = bkcolor ? bkcolor : defaultSuccessBkColor;
  let icon = "";
  switch (variant) {
    case "success":
      icon = SuccessIcon;
      break;
    case "error":
      icon = ErrorIcon;
      break;
    case "warning":
      icon = WarningIcon;
      break;
    case "info":
      icon = InfoIcon;
      break;
    default:
      icon = InfoIcon;
      break;
  }

  const autoHideDuration = duration ? duration : defaultDuration;
  const anchorOrigin = anchor
    ? anchor
    : { vertical: "bottom", horizontal: "center" };

  const onClose = (e: any, reason: any) => {
    if (reason === "clickaway") {
      return;
    }
    handleClose();
  };

  const action = (
    <React.Fragment>
      <Box onClick={handleClose} sx={{ mr: 2 }}>
        {/*// @ts-ignore @todo - will be migrated to cci/mui-components*/}
        <img src={CloseIcon} alt={"toast-close-icon"} onClick={onClose} />
      </Box>
    </React.Fragment>
  );

  return (
    <Snackbar
      open={open}
      onClose={onClose}
      autoHideDuration={autoHideDuration}
      action={action}
      anchorOrigin={anchorOrigin}
      message={
        <Box
          data-testid="osm-snackbar-id"
          sx={{
            display: "flex",
            alignItems: "center",
            color: "black",
            font: "Bold 14px Helvetica",
            ...sx,
          }}
        >
          <img
            src={icon}
            alt="cat-notification-icon"
            style={{ marginRight: 10 }}
          />
          {msg}
        </Box>
      }
      ContentProps={{
        sx: {
          background: backColor,
        },
      }}
    />
  );
};

export const CatSuccessNotification = (props: any) => {
  return <CatNotification variant="success" {...props} />;
};

export default CatNotification;
