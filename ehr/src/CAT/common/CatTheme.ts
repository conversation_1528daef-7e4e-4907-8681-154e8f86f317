import { createTheme, responsiveFontSizes } from "@mui/material/styles";

/**
 * Basic theme for all Clinical Admin Tools modules
 */
const baseTheme = createTheme({
  palette: {
    common: {
      white: "#FFFFFF",
      black: "#000000",
    },
    primary: {
      main: "#3F8AE0",
    },
    secondary: {
      main: "#C0C0C0",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    fontSize: 14,
  },
  spacing: 4, // aka 4px
});

let catTheme = createTheme(baseTheme, {
  components: {
    MuiRadio: {
      styleOverrides: {
        root: {
          "&.Mui-disabled": {
            color: baseTheme.palette.grey[500],
            backgroundColor: baseTheme.palette.grey[300],
            height: 13,
            width: 13,
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          "&.Mui-disabled": {
            color: baseTheme.palette.grey[500],
            backgroundColor: baseTheme.palette.grey[300],
            height: 15,
            width: 15,
          },
        },
      },
    },
  },
  typography: {
    subtitle2: {
      fontSize: "14px",
    },
  },
});

// apply responsive font size
catTheme = responsiveFontSizes(catTheme);

/**
 * Order Set Management Tool theme
 */
export const osmTheme = createTheme(catTheme);

export default catTheme;
