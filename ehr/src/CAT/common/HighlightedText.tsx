import React from "react";
import { Box, Typography } from "@mui/material";
import PropTypes from "prop-types";

type HighlightedTextProps = {
  text: string;
  substring: string;
  highlightColor?: string;
  theRef?: React.ForwardedRef<HTMLDivElement>;
  parseParts?: boolean;
  isItalic?: boolean;
  [key: string]: any;
};

/**
 * Highlight the given substring.
 * @prop text -- the given text
 * @prop substring -- the given substring
 * @prop highlightColor -- optional, the color string, default is "#FFFD54"
 */
const HighlightedText = ({
  text,
  substring,
  highlightColor = "#FFFD54",
  theRef,
  parseParts,
  isItalic,
  ...other
}: HighlightedTextProps) => {
  const substringParts = parseParts
    ? substring
        .split(/[!@#$%&*()+=[\]\\'./{}":<>?~]/g)
        .filter((s: any) => s.length)
    : [substring];
  let parts;

  try {
    parts = substring.length
      ? text.split(new RegExp(`(${substringParts.join("|")})`, "gi"))
      : [text];
  } catch (e) {
    parts = [text];
  }

  const substringPartsLower = substringParts.map((s: string) =>
    s?.toLowerCase()
  );

  return (
    <Box ref={theRef}>
      {parts.map((part, i) =>
        substringPartsLower.includes(part?.toLowerCase()) ? (
          <Typography
            key={i}
            display="inline"
            sx={{
              bgcolor: highlightColor,
              "&.MuiTypography-root": {
                fontSize: "14px",
              },
              fontStyle: isItalic === true ? "italic" : "normal",
            }}
            {...other}
          >
            {part}
          </Typography>
        ) : (
          <Typography
            key={i}
            display="inline"
            sx={{
              "&.MuiTypography-root": {
                fontSize: "14px",
              },
              fontStyle: isItalic === true ? "italic" : "normal",
            }}
            {...other}
          >
            {part}
          </Typography>
        )
      )}
    </Box>
  );
};

export default HighlightedText;

HighlightedText.propTypes = {
  text: PropTypes.string,
  substring: PropTypes.string,
};
