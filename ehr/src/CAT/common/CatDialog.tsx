import React from "react";
import { styled, SxProps, Theme } from "@mui/material/styles";
import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
  Paper,
  Popover,
} from "@mui/material";

import CloseSvg from "@cci-monorepo/oe-shared/src/assets/Icon_Close_Dialog.svg";
import PropTypes from "prop-types";
import Draggable from "react-draggable";

const StyledDialog = styled(Dialog)(({ theme, maxWidth = "none" }) => ({
  "& .MuiDialog-paper": {
    background: "#E3EAF5 0% 0% no-repeat",
    maxWidth: maxWidth,
  },
  "& .MuiDialogContent-root": {
    marginTop: "20px",
    padding: "0px 24px 0px 24px",
    border: "0px",
    overflow: "hidden",
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(2),
  },
  "& .MuiSvgIcon-root": {
    width: "17px",
    height: "17px",
  },
  "& .MuiIconButton-root": {
    padding: 0,
  },
}));

type TCatDialogTitleProps = {
  children: React.ReactNode;
  onClose: () => void;
  sx?: SxProps<Theme>;
  [key: string]: any;
};

const CatDialogTitle = (props: TCatDialogTitleProps) => {
  const { children, onClose, sx, ...other } = props;

  return (
    <DialogTitle
      sx={{
        m: 0,
        p: 2,
        height: "28px",
        backgroundColor: "#222B2E",
        opacity: "1",
        ...sx,
      }}
      {...other}
    >
      <Typography sx={{ color: "#FFFFFF", marginTop: "-4px" }}>
        {children}
      </Typography>
      {onClose && (
        <Box
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 3,
            background: "none",
            color: "white",
            cursor: "pointer",
            opacity: "1",
            width: 18,
            height: 18,
            "&:focus": {
              borderRadius: "4px",
              border: "2px solid",
              borderColor: "#6599FF",
            },
          }}
        >
          <img alt="close" src={CloseSvg} />
        </Box>
      )}
    </DialogTitle>
  );
};

type TCatDialogProps = {
  open: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  draggable?: boolean;
  anchorEl?: any;
  usePopover?: boolean;
  [key: string]: any;
};

const CatDialog = (props: TCatDialogProps) => {
  const { children, open, onClose, title, draggable, anchorEl, ...others } =
    props;

  const handleClose = (
    event: React.MouseEvent<HTMLElement>,
    reason: string
  ) => {
    if (reason && reason === "backdropClick") return;

    onClose && onClose();
  };

  const PaperComponent = (props: any) => (
    <Draggable
      handle="#cat-popup-dialog"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );

  if (anchorEl) {
    return <CatPopoverDialog {...props} />;
  }

  return (
    <StyledDialog
      id="cat-popup-dialog"
      aria-labelledby="cat-popup-dialog"
      open={open}
      onClose={handleClose}
      title={title}
      // @todo migrate to slots https://next.mui.com/material-ui/migration/migrating-from-deprecated-apis/#autocomplete
      // @ts-ignore
      PaperComponent={draggable && PaperComponent}
      {...others}
    >
      <CatDialogTitle
        // @ts-ignore
        onClose={handleClose}
        sx={{
          cursor: draggable ? "move" : "arrow",
          "& .MuiTypography-root": {
            fontSize: "16px",
            fontWeight: "700",
          },
        }}
      >
        {title}
      </CatDialogTitle>
      <DialogContent>{children}</DialogContent>
    </StyledDialog>
  );
};

export const CatPopoverDialog = (props: any) => {
  const {
    children,
    open,
    onClose,
    title,
    draggable,
    anchorEl,
    usePopover,
    ...others
  } = props;

  return (
    <Popover
      open={open}
      onClose={onClose}
      anchorEl={anchorEl}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      {...others}
    >
      <CatDialogTitle onClose={onClose}>{title}</CatDialogTitle>
      {children}
    </Popover>
  );
};

export default CatDialog;

CatDialog.propTypes = {
  open: PropTypes.bool, // whether the dialog is open
  onClose: PropTypes.func, // callback for dialog close
  title: PropTypes.string, // title of the dialog
  children: PropTypes.element, // the children of DialogContent
  draggable: PropTypes.bool, // optional, the dialog is draggable or not, default is false
  anchorEl: PropTypes.any, // the point the dialog attaches to if anchor exists
};
