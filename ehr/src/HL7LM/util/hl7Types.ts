export interface HL7Node {
  name: string;
  value: string;
  description?: string;
  dataType?: string;
  dispValue?: string;
  children?: HL7Node[];
  type:
    | "Message"
    | "Segment"
    | "Field"
    | "Repetition"
    | "Component"
    | "SubComponent";
}
export interface HL7Attribute {
  segment_num: string;
  seq: string;
  len: string;
  dt: string;
  opt: string;
  element_name: string;
}
export interface SegmentNames {
  segment_name: string;
  description: string;
}

export interface HL7Resource {
  hl7Attribute: HL7Attribute[];
  segmentNames: SegmentNames[];
}
