import { HL7Node, HL7Resource } from "./hl7Types";
export class HL7Parser {
  private segmentDelimiter = "\r";
  private fieldDelimiter = "|";
  private componentDelimiter = "^";
  private repDelimiter = "~";
  private escapeCharacter = "\\";
  private subcomponentDelimiter = "&";

  public parse(message: string, resource: HL7Resource | undefined): HL7Node {
    const hl7Attribute = resource?.hl7Attribute;
    const segmentNames = resource?.segmentNames;
    if (!message.startsWith("MSH")) {
      throw new Error("Invalid HL7 message: Must start with 'MSH'");
    }

    const mshParts = message.split(this.fieldDelimiter);
    if (mshParts.length < 2) {
      throw new Error("Invalid HL7 message: Missing encoding characters");
    }

    // Parse encoding characters from MSH-2
    const encodingChars = mshParts[1];
    if (encodingChars.length >= 4) {
      this.componentDelimiter = encodingChars[0];
      this.repDelimiter = encodingChars[1];
      this.escapeCharacter = encodingChars[2];
      this.subcomponentDelimiter = encodingChars[3];
    }

    const rootNode: HL7Node = {
      name: "HL7 Message",
      value: message,
      type: "Message",
      children: [],
    };

    const segments = message.split(this.segmentDelimiter);
    for (const segment of segments) {
      if (!segment.trim()) continue;

      const segmentNode: HL7Node = {
        name: segment.substring(0, 3), // e.g., "MSH", "PID"
        value: segment,
        type: "Segment",
        description:
          segmentNames?.find(
            (item) => item.segment_name === segment.substring(0, 3)
          )?.description || "UNKNOWN",
        children: [],
      };

      const fields = segment.split(this.fieldDelimiter);
      for (let i = 1; i < fields.length; i++) {
        // Skip MSH-1 (field delimiter)
        if (i === fields.length - 1 && fields[i] === "") continue;

        if (segmentNode.name === "MSH" && i === 1) {
          segmentNode.children?.push({
            name: "MSH-1",
            value: fields[i],
            type: "Field",
            description:
              hl7Attribute?.find((item) => item.segment_num === `MSH-1`)
                ?.element_name || "UNKNOWN",
            dataType:
              hl7Attribute?.find((item) => item.segment_num === `MSH-1`)?.dt ||
              "UNKNOWN",
            children: [],
          });
          continue;
        }

        const fieldValue = this.unescapeValue(fields[i]);
        const repeats = fieldValue.split(this.repDelimiter);

        for (let r = 0; r < repeats.length; r++) {
          const repeatNode: HL7Node = {
            name: `${segmentNode.name}-${i}${repeats.length > 1 ? `[${r + 1}]` : ""}`,
            value: repeats[r],
            type: "Field",
            description:
              hl7Attribute?.find(
                (item) =>
                  item.segment_num ===
                  `${segmentNode.name}-${i}${repeats.length > 1 ? `[${r + 1}]` : ""}`
              )?.element_name || "UNKNOWN",
            dataType:
              hl7Attribute?.find(
                (item) =>
                  item.segment_num ===
                  `${segmentNode.name}-${i}${repeats.length > 1 ? `[${r + 1}]` : ""}`
              )?.dt || "UNKNOWN",
            children: [],
          };

          if (repeats[r].includes(this.componentDelimiter)) {
            const components = repeats[r].split(this.componentDelimiter);
            for (let j = 0; j < components.length; j++) {
              const componentNode: HL7Node = {
                name: `${repeatNode.name}-${j + 1}`,
                value: components[j],
                type: "Component",
                children: [],
              };

              if (components[j].includes(this.subcomponentDelimiter)) {
                const subcomponents = components[j].split(
                  this.subcomponentDelimiter
                );
                for (let k = 0; k < subcomponents.length; k++) {
                  const subcomponentNode: HL7Node = {
                    name: `${componentNode.name}-${k + 1}`,
                    value: subcomponents[k],
                    type: "SubComponent",
                    children: [],
                  };
                  componentNode.children?.push(subcomponentNode);
                }
              }

              repeatNode.children?.push(componentNode);
            }
          }

          segmentNode.children?.push(repeatNode);
        }
      }

      rootNode.children?.push(segmentNode);
    }

    return rootNode;
  }

  /**
   * Unescape HL7 escape sequences (e.g., \F\ -> |, \E\ -> \).
   */
  private unescapeValue(value: string): string {
    return value.replace(/\\([FRSET\\])/g, (match, code) => {
      switch (code) {
        case "F":
          return this.fieldDelimiter;
        case "S":
          return this.subcomponentDelimiter;
        case "R":
          return this.repDelimiter;
        case "E":
          return this.escapeCharacter;
        case "T":
          return this.componentDelimiter;
        case "\\":
          return "\\";
        default:
          return match; // Unknown escape sequence, leave as-is
      }
    });
  }
}
