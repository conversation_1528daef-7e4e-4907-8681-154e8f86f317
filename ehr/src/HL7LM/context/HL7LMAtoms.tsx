import { atom } from "jotai";

export const openErrorDialogAtom = atom({
  message: "",
  open: false,
});

export const propExpandedAtom = atom(true);

export const flipPropAtom = atom(null, (get, set) => {
  let propExpanded = get(propExpandedAtom);
  set(propExpandedAtom, !propExpanded);
});

export const drawerExpandedAtom = atom(true);

export const flipDrawerAtom = atom(null, (get, set) => {
  let drawerExpanded = get(drawerExpandedAtom);
  set(drawerExpandedAtom, !drawerExpanded);
});

export const panelExpandedAtom = atom(false);

export const flipPanelAtom = atom(null, (get, set) => {
  let panelExpanded = get(panelExpandedAtom);
  set(panelExpandedAtom, !panelExpanded);
});
