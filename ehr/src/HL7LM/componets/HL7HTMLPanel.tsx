import { Box, Typography } from "@mui/material";
import { propExpandedAtom } from "@cci-monorepo/HL7LM/context/HL7LMAtoms";
import { useAtomValue } from "jotai";
interface HL7MessageList {
  msg: string;
}
export default function HL7MessageList(props: HL7MessageList) {
  const propExpanded = useAtomValue(propExpandedAtom);
  return (
    <Box
      sx={{
        width: "100%",
        height: propExpanded ? "50%" : "100%",
        border: "1px solid #8db2e3",
        borderTop: "none",
        mb: "5px",
        transition: "height 0.3s ease",
      }}
    >
      <Typography
        sx={{
          backgroundColor: "#d2e0f2",
          height: "22px",
          pl: "4px",
          color: "#15428b",
          font: "bold 11px tahoma,arial,verdana,sans-serif",
          display: "flex",
          alignItems: "center",
        }}
      >
        HL7HTMLPanel
      </Typography>
      <Box
        sx={{
          height: "calc(100% - 22px)",
          padding: "4px",
          wordBreak: "break-word",
          overflow: "auto",
        }}
      >
        {props.msg}
      </Box>
    </Box>
  );
}
