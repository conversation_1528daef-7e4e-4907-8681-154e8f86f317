import { Typography, Box } from "@mui/material";
interface props {
  title: string;
}
export default function HL7Title(props: props) {
  return (
    <Box
      sx={{
        width: "100%",
        padding: "1px 0 2px 0",
        backgroundColor: "#d2e0f2",
        border: "1px solid #8db2e3",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "22px",
          mt: 0.2,
          backgroundColor: "#d2e0f2",
          borderBottom: "1px solid #8db2e3",
        }}
      >
        <Typography
          sx={{
            width: "115px",
            height: "22px",
            padding: "2px",
            ml: "2px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#DEECFD",
            color: "#15428b",
            font: "bold 11px tahoma,arial,verdana,sans-serif",
            border: "1px solid #8db2e3",
            borderBottom: "1px solid #DEECFD",
            borderTopLeftRadius: "5px",
            borderTopRightRadius: "5px",
          }}
        >
          {props.title}
        </Typography>
      </Box>
    </Box>
  );
}
