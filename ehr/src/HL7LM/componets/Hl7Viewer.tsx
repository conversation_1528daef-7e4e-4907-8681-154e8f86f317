import { useEffect, useState } from "react";
import { HL7Parser } from "../util/hl7Parser";
import { HL7Node, HL7Resource } from "../util/hl7Types";
import { TreeView, TreeItem } from "@mui/x-tree-view";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import segmentIcon from "../images/segment_16x16.gif";
import componentIcon from "../images/component_16x16.gif";
import subcomponentIcon from "../images/subcomponent_16x16.gif";
import fieldIcon from "../images/field_16x16.gif";
import messageIcon from "../images/plugin.gif";
import repetitionIcon from "../images/repetition_16x16.gif";

interface Hl7ViewerListProps {
  msg: string;
  onGetProperties: (v: any) => void;
}
export default function Hl7Viewer({
  msg,
  onGetProperties,
}: Hl7ViewerListProps) {
  if (!msg) {
    return null;
  }
  const [parsedData, setParsedData] = useState<HL7Node | null>(null);
  const [resource, setResource] = useState<HL7Resource>();
  useEffect(() => {
    const params = {
      hobj: "hl7lm/resource",
    };

    Cci.util.CciObject.request(params)
      .then(function (jsondata: any) {
        if (jsondata) {
          setResource({
            hl7Attribute: jsondata.hl7Attribute,
            segmentNames: jsondata.segmentNames,
          });
        }
      })
      .catch(function (error: any) {
        console.log("get data failed: " + error);
      });
  }, []);

  useEffect(() => {
    try {
      const parser = new HL7Parser();
      const result = parser.parse(msg, resource);
      setParsedData(result);
    } catch (error) {
      alert(`Error parsing HL7: ${error}`);
    }
  }, [msg, resource]);

  const handleItemClick = (node: HL7Node) => {
    const properties = Object.entries(node)
      .filter(([key]) => key !== "children")
      .map(([key, value]) => ({
        name: key,
        value: value,
      }));

    onGetProperties(properties);
  };

  const renderTreeItem = (node: HL7Node, nodeId: string) => {
    const getIcon = (type: string) => {
      const iconMap: Record<string, string> = {
        Message: messageIcon,
        Segment: segmentIcon,
        Field: fieldIcon,
        Component: componentIcon,
        SubComponent: subcomponentIcon,
        Repetition: repetitionIcon,
      };
      const iconSrc = iconMap[type];

      return (
        <img
          src={iconSrc}
          alt={type}
          width={16}
          height={16}
          style={{
            marginRight: "4px",
            verticalAlign: "middle",
          }}
        />
      );
    };
    return (
      <TreeItem
        key={nodeId}
        nodeId={nodeId}
        label={
          <div
            style={{
              display: "flex",
              alignItems: "center",
              whiteSpace: "nowrap",
              width: "100%",
            }}
          >
            {getIcon(node.type)}
            <span style={{ fontWeight: "bold", color: "#000" }}>
              {node.name}
            </span>
            {node.description && (
              <span style={{ color: "#5A5A5A", marginLeft: "8px" }}>
                {node.description}
              </span>
            )}
          </div>
        }
        onClick={() => handleItemClick(node)}
      >
        {node.children?.map((child, index) =>
          renderTreeItem(child, `${nodeId}-${index}`)
        )}
      </TreeItem>
    );
  };

  return (
    <>
      {parsedData && (
        <TreeView
          aria-label="HL7 Tree Viewer"
          defaultCollapseIcon={<ExpandMoreIcon />}
          defaultExpandIcon={<ChevronRightIcon />}
          sx={{ flexGrow: 1 }}
        >
          {renderTreeItem(parsedData, "root")}
        </TreeView>
      )}
    </>
  );
}
