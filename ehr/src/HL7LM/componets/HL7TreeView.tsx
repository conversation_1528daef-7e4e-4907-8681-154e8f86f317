import { useState, useEffect } from "react";
import { Typography, Box, IconButton } from "@mui/material";
import { useAtomValue, useSetAtom } from "jotai";
import {
  drawerExpandedAtom,
  flipDrawerAtom,
} from "@cci-monorepo/HL7LM/context/HL7LMAtoms";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import HL7Title from "./CommonTitle";
import HL7HTMLPanel from "./HL7HTMLPanel";
import PropertiesGrid from "./PropertiesGrid";
import Hl7Viewer from "./Hl7Viewer";

interface HL7TreeViewProps {
  msg: string;
}
interface TransformedItem {
  name: string;
  value: string;
}
export default function HL7TreeView(props: HL7TreeViewProps) {
  const flipDrawer = useSetAtom(flipDrawerAtom);
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const [properties, setProperties] = useState<TransformedItem[]>([]);
  const getProperties = (v: TransformedItem[]) => {
    setProperties(v);
  };
  useEffect(() => {
    if (!props.msg) {
      setProperties([]);
    }
  }, [props.msg]);
  return (
    <Box sx={{ height: "calc(100% - 30px)" }}>
      <HL7Title title="HL7 TreeView" />
      <Box
        sx={{
          width: "100%",
          height: "100%",
          border: "1px solid #8db2e3",
          borderTop: "none",
          display: "flex",
        }}
      >
        <Box
          sx={{
            width: drawerExpanded ? "250px" : "20px",
            height: "100%",
            border: "1px solid #8db2e3",
            opacity: drawerExpanded ? 1 : 0,
            transition: "all 0.3s ease",
            position: "relative",
            minWidth: drawerExpanded ? "250px" : "20px",
            "& > *": {
              transition: "opacity 0.2s ease",
              opacity: drawerExpanded ? 1 : 0,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "25px",
              display: "flex",
              paddingLeft: "5px",
              alignItems: "center",
              justifyContent: "space-between",
              backgroundColor: "#d2e0f2",
            }}
          >
            <Typography
              sx={{
                color: "#15428b",
                font: "bold 11px tahoma,arial,verdana,sans-serif",
                backgroundColor: "#d2e0f2",
              }}
            >
              HL7 Tree
            </Typography>
            <IconButton onClick={flipDrawer}>
              <KeyboardDoubleArrowLeftIcon sx={{ fontSize: "15px" }} />
            </IconButton>
          </Box>
          <Box
            sx={{
              height: "calc(100% - 25px)",
              overflow: "auto",
            }}
          >
            <Hl7Viewer msg={props.msg} onGetProperties={getProperties} />
          </Box>
        </Box>
        <Box
          sx={{
            visibility: drawerExpanded ? "hidden" : "visible",
            backgroundColor: "#d2e0f2",
            height: "100%",
            width: "20px",
            position: "absolute",
          }}
        >
          <Box
            sx={{
              backgroundColor: "#d2e0f2",
              height: "100%",
              width: "20px",
              display: "flex",
              alignItems: "center",
              flexDirection: "column",
              border: "1px solid #98c0f4",
            }}
            onClick={flipDrawer}
          >
            <IconButton>
              <KeyboardDoubleArrowRightIcon sx={{ fontSize: "15px" }} />
            </IconButton>
            <Typography
              sx={{
                color: "#15428b",
                font: "bold 11px tahoma,arial,verdana,sans-serif",
                writingMode: "vertical-rl",
              }}
            >
              HL7 Tree
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            height: "100%",
            border: "1px solid #8db2e3",
            pl: "5px",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <HL7HTMLPanel msg={props.msg} />
          <PropertiesGrid properties={properties} />
        </Box>
      </Box>
    </Box>
  );
}
