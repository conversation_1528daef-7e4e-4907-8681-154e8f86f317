import React from "react";
import { useState } from "react";
import { Box, Tooltip } from "@mui/material";
import { TextField } from "@mui/material";
import { useAtomValue, useSetAtom } from "jotai";
import {
  panelExpandedAtom,
  flipPanelAtom,
} from "@cci-monorepo/HL7LM/context/HL7LMAtoms";
import { styled } from "@mui/material/styles";
import NumericFormat from "react-number-format";
import SkipPreviousIcon from "@mui/icons-material/SkipPrevious";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import CachedOutlinedIcon from "@mui/icons-material/CachedOutlined";
import NumberInputSelect from "./NumberInputSelect";

interface paginationProps {
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onRefresh: () => void;
  onRefreshAll: () => void;
  onExport: () => void;
  onSearch: (v: string) => void;
}
const CustomTextField = styled(TextField)({
  width: "200px",
  margin: "0 2px",
  "& .MuiInputBase-root": {
    height: "18px",
    fontSize: "11px",
    padding: "0 !important",
    borderRadius: "2px",
    backgroundColor: "#fff",
    "& input": {
      padding: "4px !important",
    },
    "& fieldset": {
      borderColor: "#B5B8C8 !important",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#000 !important",
    },
  },
});
export default React.memo(function Pagination(props: paginationProps) {
  const {
    page,
    pageSize,
    total,
    onPageChange,
    onPageSizeChange,
    onRefresh,
    onRefreshAll,
    onExport,
    onSearch,
  } = props;
  const options = [5, 10, 20, 50, 100, 200, 500, 1000];
  const totalPages = Math.ceil(total / pageSize);
  const [isRotating, setIsRotating] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const flipPanel = useSetAtom(flipPanelAtom);
  const panelExpanded = useAtomValue(panelExpandedAtom);

  const handleClick = () => {
    setIsRotating(true);
    setTimeout(() => setIsRotating(false), 1000);
    onRefresh();
  };
  const handleChange = (values: { floatValue: any }) => {
    const num = values.floatValue;
    onPageChange(num);
  };
  const handlePageSizeChange = (value: any) => {
    onPageChange(1);
    onPageSizeChange(value);
  };
  const handleRefresh = () => {
    setSearchValue("");
    onRefreshAll();
    onSearch("");
  };
  const handleSearch = () => {
    onSearch(searchValue);
  };

  return (
    <Box
      sx={{
        height: "26px",
        width: "100%",
        padding: "4px",
        fontSize: "11px",
        border: "1px solid #8db2e3",
        borderTop: "none",
        bgcolor: "#d2e0f2",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {[
          {
            content: (
              <>
                <Tooltip title="First Page" arrow>
                  <SkipPreviousIcon
                    sx={{
                      fontSize: 22,
                      color: page === 1 ? "#ADB2B9" : "#5D80C7",
                      cursor: "pointer",
                    }}
                    onClick={() => onPageChange(1)}
                  />
                </Tooltip>
                <Tooltip title="Previous Page" arrow>
                  <PlayArrowIcon
                    sx={{
                      fontSize: 18,
                      color: page === 1 ? "#ADB2B9" : "#5D80C7",
                      cursor: "pointer",
                      transform: "rotate(180deg)",
                    }}
                    onClick={() => onPageChange(page > 2 ? page - 1 : 1)}
                  />
                </Tooltip>
              </>
            ),
            showBorder: true,
          },
          {
            content: (
              <>
                Page
                <NumericFormat
                  customInput={TextField}
                  value={page}
                  onValueChange={handleChange}
                  allowNegative={false}
                  decimalScale={0}
                  sx={{ ...pageStyle }}
                  isAllowed={({ floatValue }) => {
                    if (floatValue === undefined) return true;
                    return floatValue >= 1 && floatValue <= totalPages;
                  }}
                />
                of {totalPages}
              </>
            ),
            showBorder: true,
          },
          {
            content: (
              <>
                <Tooltip title="Next Page" arrow>
                  <PlayArrowIcon
                    sx={{
                      fontSize: 18,
                      color: page === totalPages ? "#ADB2B9" : "#5D80C7",
                      cursor: "pointer",
                    }}
                    onClick={() => onPageChange(Math.min(totalPages, page + 1))}
                  />
                </Tooltip>
                <Tooltip title="Last Page" arrow>
                  <SkipPreviousIcon
                    sx={{
                      fontSize: 22,
                      color: page === totalPages ? "#ADB2B9" : "#5D80C7",
                      cursor: "pointer",
                      transform: "rotate(180deg)",
                    }}
                    onClick={() => onPageChange(totalPages)}
                  />
                </Tooltip>
              </>
            ),
            showBorder: true,
          },
          {
            content: (
              <>
                <Tooltip title="Refresh" arrow>
                  <CachedOutlinedIcon
                    onClick={handleClick}
                    sx={{
                      fontSize: 20,
                      color: "#ADB2B9",
                      cursor: "pointer",
                      transform: isRotating ? "rotate(360deg)" : "rotate(0deg)",
                      transition: "transform 1s ease",
                    }}
                  />
                </Tooltip>
              </>
            ),
            showBorder: true,
          },
          {
            content: (
              <>
                Show
                <NumberInputSelect
                  options={options}
                  value={pageSize}
                  onChange={handlePageSizeChange}
                  min={1}
                  max={total}
                />
                per page
              </>
            ),
            showBorder: true,
          },
          {
            content: (
              <>
                <Tooltip title="Refresh" arrow>
                  <Box
                    onClick={handleRefresh}
                    sx={{
                      border: "1px solid #8db2e3",
                      padding: "1px 2px",
                      borderRadius: "4px",
                      bgcolor: "#BDD2EB",
                      cursor: "pointer",
                    }}
                  >
                    Refresh
                  </Box>
                </Tooltip>
                <Tooltip title="Click to export data into txt file" arrow>
                  <Box onClick={onExport} sx={{ ...iconStyle }}>
                    Export
                  </Box>
                </Tooltip>
                <Tooltip title="Click to show/hide bottom panel" arrow>
                  <Box sx={{ ...iconStyle }} onClick={flipPanel}>
                    {panelExpanded ? "Show Bottom Panel" : "Hide Bottom Panel"}
                  </Box>
                </Tooltip>
                <Tooltip title="Click to search data" arrow>
                  <Box onClick={handleSearch} sx={{ ...iconStyle }}>
                    Search
                  </Box>
                </Tooltip>
                <CustomTextField
                  value={searchValue}
                  onChange={(event: any) => {
                    setSearchValue(event.target.value);
                  }}
                />
              </>
            ),
            showBorder: false,
          },
        ].map((item, index) => (
          <Box
            key={index}
            sx={{
              borderRight: item.showBorder ? "1px solid #8db2e3" : "none",
              px: 1,
              height: "15px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {item.content}
          </Box>
        ))}
      </Box>
      {total > 0 ? <Box>Total Rows: {total}</Box> : <Box>No Data</Box>}
    </Box>
  );
});

const iconStyle = {
  padding: "1px 10px",
  border: "1px solid #d2e0f2",
  cursor: "pointer",
  "&:hover": {
    borderColor: "#8db2e3",
    borderRadius: "4px",
    bgcolor: "#BDD2EB",
  },
};
const pageStyle = {
  width: "25px",
  margin: "0 2px",
  "& .MuiInputBase-root": {
    height: "18px",
    fontSize: "11px",
    padding: "0 !important",
    borderRadius: "2px",
    backgroundColor: "#fff",
    "& input": {
      padding: "4px !important",
    },
    "& fieldset": {
      borderColor: "#B5B8C8 !important",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#000 !important",
    },
  },
};
