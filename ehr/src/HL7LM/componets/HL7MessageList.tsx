import { useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";
import { Button as <PERSON><PERSON>Button, CciDialog } from "@cci/mui-components";
import { DataGridPro, GridColDef, GridRowParams } from "@mui/x-data-grid-pro";
import { dbList } from "../util/DataUtil";
import { downloadTxtFile } from "../util/downloadTxt";
import MLToolbar from "./MLToolbar";
import HL7Title from "./CommonTitle";

interface RowData {
  id: string;
  [key: string]: any;
}

interface DbItem {
  db: string;
  column: GridColDef[];
}

interface HL7MessageListProps {
  onGetMessage: (msg: string) => void;
}

export default function HL7MessageList(props: HL7MessageListProps) {
  const { onGetMessage } = props;
  const [rows, setRows] = useState<RowData[]>([]);
  const [columns, setColumns] = useState<GridColDef[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDb] = useState<DbItem>(dbList[0]);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  const [rowValue, setRowValue] = useState<any>();
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);

  const handleRowDoubleClick = (
    params: GridRowParams,
    event: React.MouseEvent
  ) => {
    event.preventDefault();
    onGetMessage(params.row.message);
    setRowValue(params.row);
  };
  const pageChange = (page: number) => {
    setPage(page);
  };
  const pageSizeChange = (pageSize: number) => {
    setPageSize(pageSize || 1);
  };
  const handleRefresh = () => {
    fetchData(selectedDb);
  };
  const handleRefreshAll = () => {
    setPage(1);
    setPageSize(10);
    onGetMessage("");
    setRowValue({});
  };
  const handleExport = () => {
    if (rowValue?.msg_num) {
      const params = {
        hobj: "hl7lm/export",
        params: {
          msg_nums: rowValue?.msg_num,
        },
      };

      Cci.util.CciObject.request(params)
        .then(function (jsondata: any) {
          if (jsondata && jsondata.file_download) {
            let data = jsondata.file_download;
            downloadTxtFile(data[0]?.message);
          }
        })
        .catch(function (error: any) {
          console.log("get data failed: " + error);
        });
    } else {
      setDialogOpen(true);
    }
  };
  const handleClose = () => {
    setDialogOpen(false);
  };
  const handleSearch = (v: string) => {
    fetchData(selectedDb, v);
  };
  const fetchData = async (dbItem: DbItem, search: string = "") => {
    setIsLoading(true);
    setError(null);

    try {
      const params = {
        hobj: "hl7lm/getdata",
        params: {
          table: dbItem.db,
          page,
          pageSize,
          search,
        },
      };

      const jonsdata = await Cci.util.CciObject.request(params);
      const messagesListData = jonsdata.data || [];
      const paginationData = jonsdata.pagination?.[0];
      const formattedData = messagesListData.map((item: any) => ({
        ...item,
        id: item.ID,
      }));
      setColumns(dbItem.column);
      setRows(formattedData);
      setTotal(paginationData.total);
    } catch (err) {
      console.error("Data fetch failed:", err);
      setError("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData(selectedDb);
  }, [selectedDb, page, pageSize]);

  const content = (
    <Box>
      <Typography>Please select a record</Typography>
    </Box>
  );
  const buttons = () => {
    return (
      <>
        <CciButton
          sx={{ mr: 1 }}
          color="secondary"
          size="small"
          onClick={handleClose}
        >
          Cancel
        </CciButton>
        <CciButton color="primary" size="small" onClick={handleClose}>
          OK
        </CciButton>
      </>
    );
  };
  if (error) {
    return (
      <Box sx={{ p: 2, color: "error.main" }}>
        Error: {error}.{" "}
        <button onClick={() => fetchData(selectedDb)}>Retry</button>
      </Box>
    );
  }

  return (
    <>
      <Box sx={{ height: "100%", minWidth: "980px" }}>
        <HL7Title title="HL7 Message List" />
        <Box
          sx={{
            height: "calc(100% - 30px)",
            width: "100%",
            border: "1px solid #8db2e3",
            padding: "5px",
            backgroundColor: "#d2e0f2",
          }}
        >
          <Box
            sx={{ height: "calc(100% - 30px)", border: "1px solid #8db2e3" }}
          >
            <DataGridPro
              loading={isLoading}
              rows={rows}
              columns={columns}
              sx={{
                backgroundColor: "#ffffff",
                "& .MuiDataGrid-cell:focus": { outline: "none" },
                "& .MuiDataGrid-columnHeader:focus": {
                  outline: "none",
                },
                "& .MuiDataGrid-columnHeaders": {
                  backgroundColor: "#EEEFF1",
                  borderBottom: "1px solid #ddd",
                },
              }}
              disableColumnMenu={false}
              density="compact"
              isCellEditable={() => false}
              getRowId={(row) => row.msg_num}
              hideFooterPagination
              hideFooter
              onRowDoubleClick={handleRowDoubleClick}
            />
          </Box>
          <MLToolbar
            onPageChange={pageChange}
            onPageSizeChange={pageSizeChange}
            onRefresh={handleRefresh}
            onRefreshAll={handleRefreshAll}
            onExport={handleExport}
            onSearch={handleSearch}
            page={page}
            pageSize={pageSize}
            total={total}
          />

          {dialogOpen && (
            <CciDialog
              data-testid="non-med-env-dialog-id"
              title="export"
              setOpen={handleClose}
              open={dialogOpen}
              content={content}
              buttons={buttons()}
              draggable
              sx={{
                "& .MuiDialog-paper": {
                  width: "250px",
                  boxShadow: "3px",
                },
                "& .MuiModal-backdrop": {
                  backgroundColor: "rgba(0, 0, 0, 0.25)",
                },
                "& hr": {
                  display: "none",
                },
              }}
            />
          )}
        </Box>
      </Box>
    </>
  );
}
