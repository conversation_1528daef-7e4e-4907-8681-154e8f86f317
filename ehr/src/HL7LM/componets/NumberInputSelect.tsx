import { Autocomplete, TextField, MenuItem } from "@mui/material";

interface NumberInputSelectProps {
  options: number[];
  value: number | null;
  onChange: (value: number | null) => void;
  label?: string;
  disabled?: boolean;
  max?: number;
  min?: number;
}

export default function NumberInputSelect({
  options,
  value,
  onChange,
  label = "",
  disabled = false,
  max,
  min,
}: NumberInputSelectProps) {
  const inputValue = value?.toString() ?? "";

  const handleInputChange = (
    _: React.SyntheticEvent,
    newInputValue: string
  ) => {
    if (newInputValue === "") {
      onChange(null);
      return;
    }
    if (!/^\d+$/.test(newInputValue)) return;

    const numValue = Number(newInputValue);
    if (typeof max === "number" && numValue > max) return;
    if (typeof min === "number" && numValue < min) return;

    onChange(numValue);
  };

  const autocompleteValue = value === null ? undefined : value;

  return (
    <Autocomplete
      freeSolo
      disableClearable
      disabled={disabled}
      options={options}
      value={autocompleteValue}
      onChange={(_, newValue) => {
        if (newValue === undefined || newValue === null) {
          onChange(null);
        } else if (typeof newValue === "number") {
          onChange(newValue);
        }
      }}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      getOptionLabel={(option) => option.toString()}
      renderOption={(props, option) => (
        <MenuItem
          {...props}
          sx={{
            padding: "4px 8px",
            fontSize: "12px",
          }}
        >
          {option}
        </MenuItem>
      )}
      componentsProps={{
        paper: {
          sx: {
            width: "60px",
          },
        },
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          variant="outlined"
          sx={{
            width: "30px",
            margin: "0 2px",
            "& .MuiInputBase-root": {
              height: "18px",
              minHeight: "18px",
              fontSize: "11px",
              padding: "0 !important",
              backgroundColor: "#fff",
              borderRadius: "2px",
            },
            "& .MuiOutlinedInput-input": {
              padding: "4px 6px !important",
            },
            "& fieldset": {
              borderColor: "#B5B8C8 !important",
            },
            "& .Mui-focused fieldset": {
              borderColor: "#000 !important",
            },
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: null,
          }}
          inputProps={{
            ...params.inputProps,
            inputMode: "numeric",
            pattern: "[0-9]*",
          }}
        />
      )}
    />
  );
}
