import { Box, Typography, IconButton } from "@mui/material";
import { DataGridPro } from "@mui/x-data-grid-pro";
import KeyboardDoubleArrowDownIcon from "@mui/icons-material/KeyboardDoubleArrowDown";
import KeyboardDoubleArrowUpIcon from "@mui/icons-material/KeyboardDoubleArrowUp";
import { useAtomValue, useSetAtom } from "jotai";
import {
  propExpandedAtom,
  flipPropAtom,
} from "@cci-monorepo/HL7LM/context/HL7LMAtoms";
import { useState, useEffect } from "react";
interface RowData {
  name: string;
  value: string;
}
interface HL7MessageListProps {
  properties: RowData[];
}
export default function HL7MessageList({ properties }: HL7MessageListProps) {
  const flipProp = useSetAtom(flipPropAtom);
  const propExpanded = useAtomValue(propExpandedAtom);
  const [rows, setRows] = useState<RowData[]>([]);
  const propertiesColumn = [
    {
      field: "name",
      headerName: "Name",
      width: 400,
      sortable: false,
    },
    {
      field: "value",
      headerName: "Value",
      width: 400,
      sortable: false,
    },
  ];
  useEffect(() => {
    if (properties) {
      const transformedRows = properties.map((item) => ({
        name: item.name,
        value: item.value,
      }));
      setRows(transformedRows);
    }
  }, [properties]);
  return (
    <Box
      sx={{
        width: "100%",
        height: propExpanded ? "49%" : "25px",
        border: "1px solid #8db2e3",
        transition: "height 0.3s ease",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "22px",
          display: "flex",
          paddingLeft: "5px",
          alignItems: "center",
          justifyContent: "space-between",
          backgroundColor: "#d2e0f2",
        }}
      >
        <Typography
          sx={{
            color: "#15428b",
            font: "bold 11px tahoma,arial,verdana,sans-serif",
          }}
        >
          Properties Grid
        </Typography>
        <IconButton onClick={flipProp}>
          {propExpanded ? (
            <KeyboardDoubleArrowDownIcon sx={{ fontSize: "15px" }} />
          ) : (
            <KeyboardDoubleArrowUpIcon sx={{ fontSize: "15px" }} />
          )}
        </IconButton>
      </Box>
      <Box
        sx={{
          height: "calc(100% - 22px)",
        }}
      >
        <DataGridPro
          rows={rows}
          columns={propertiesColumn}
          sx={{
            backgroundColor: "#ffffff",
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#EEEFF1",
              borderBottom: "1px solid #ddd",
            },
            "& .MuiDataGrid-cell:focus": {
              outline: "none",
            },
            "& .MuiDataGrid-columnHeader:focus": {
              outline: "none",
            },
          }}
          disableColumnMenu={false}
          density="compact"
          isCellEditable={() => false}
          getRowId={(row) => row.name}
          hideFooterPagination
          hideFooter
        />
      </Box>
    </Box>
  );
}
