import { useState, useRef, useEffect } from "react";
import { useAtomValue } from "jotai";
import { Box } from "@mui/material";
import { panelExpandedAtom } from "@cci-monorepo/HL7LM/context/HL7LMAtoms";
import HL7MessageList from "./HL7MessageList";
import HL7TreeView from "./HL7TreeView";

export default function HomePage() {
  const panelExpanded = useAtomValue(panelExpandedAtom);
  const [msg, setMsg] = useState<string>("");
  const [topHeight, setTopHeight] = useState(40);
  const containerRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);
  const startYRef = useRef(0);
  const startHeightRef = useRef(0);

  const getMessage = (msg: string) => {
    setMsg(msg);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    isDraggingRef.current = true;
    startYRef.current = e.clientY;
    startHeightRef.current = topHeight;
    document.body.style.cursor = "row-resize";
    document.body.style.userSelect = "none";
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current || !containerRef.current) return;

      const container = containerRef.current;
      const containerHeight = container.clientHeight;
      const deltaY = e.clientY - startYRef.current;
      const deltaPercent = (deltaY / containerHeight) * 100;
      let newHeight = startHeightRef.current + deltaPercent;

      newHeight = Math.max(10, Math.min(90, newHeight));
      setTopHeight(newHeight);
    };

    const handleMouseUp = () => {
      isDraggingRef.current = false;
      document.body.style.cursor = "";
      document.body.style.userSelect = "";
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, []);

  return (
    <Box
      ref={containerRef}
      sx={{
        height: "100%",
        minHeight: "780px",
        position: "relative",
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
      }}
    >
      <Box
        sx={{
          height: panelExpanded ? "100%" : `${topHeight}%`,
          flexShrink: 0,
        }}
      >
        <HL7MessageList onGetMessage={getMessage} />
      </Box>

      {!panelExpanded && (
        <Box
          sx={{
            height: "2px",
            cursor: "row-resize",
            "&:hover": {
              backgroundColor: "#D2E0F2",
            },
            position: "relative",
            zIndex: 1,
            flexShrink: 0,
          }}
          onMouseDown={handleMouseDown}
        />
      )}
      <Box
        sx={{
          height: panelExpanded ? "0" : `calc(${100 - topHeight}% - 2px)`,
          flexGrow: 1,
        }}
      >
        <HL7TreeView msg={msg} />
      </Box>
    </Box>
  );
}
