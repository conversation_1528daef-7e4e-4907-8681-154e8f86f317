import React from "react";
import { InputField } from "@cci-monorepo/common/mui-components/src/export";
import { StyledAutoComplete } from "@cci-monorepo/common/mui-components/src/components/layout/AdminToolbar/components/StyledAutoComplete";

export type FormField = {
  type: "select" | "text";
  key: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  options?: any[];
  disabled?: boolean;
};

interface Props {
  field: FormField;
  value: any;

  onChange: (value: any) => void;
  labelStyle?: React.CSSProperties;
  labelPosition?: "left" | "top-left" | "top" | "right";
  disabled?: boolean;
}
const defalutLabelStyle = {
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontWeight: 500,
  whiteSpace: "nowrap",
};

const FormFieldRenderer = ({
  field,
  value,
  onChange,
  labelStyle,
  labelPosition = "left",
  disabled = false,
}: Props) => {
  if (field.type === "select") {
    return (
      <StyledAutoComplete
        options={field.options || []}
        value={value}
        onChange={onChange}
        label={field.label}
        labelPosition={labelPosition}
        labelStyle={{
          marginBottom: labelPosition === "top" ? "4px" : "0",
          ...defalutLabelStyle,
          ...labelStyle,
        }}
        disableClearable
        disabled={disabled}
        required={field.required}
        placeholder={field.placeholder || "Make Selection"}
      />
    );
  }

  return (
    <InputField
      dataType="text"
      data={value || ""}
      label={field.label}
      onDataChange={onChange}
      labelPosition={labelPosition}
      labelStyle={{
        marginBottom: labelPosition === "top" ? "4px" : "0",
        ...defalutLabelStyle,
        ...labelStyle,
      }}
      disabled={disabled}
      required={field.required}
      placeholder={field.placeholder || field.label}
    />
  );
};

export default FormFieldRenderer;
