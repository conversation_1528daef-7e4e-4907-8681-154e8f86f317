import React, { useEffect } from "react";
import {
  useGridApiRef,
  GridColDef,
  GridRowsProp,
  GridRowSelectionModel,
  DataGridPro,
  GridColumnVisibilityModel,
} from "@mui/x-data-grid-pro";
import { Box, Typography } from "@mui/material";
import { PetalLoadingProgress } from "@psat-src/common/PetalLoadingProgress";

interface TableRowData {
  id: number;
  actions: string;
  active?: number | string;
  orderid?: string | number;
  [key: string]: any;
}

interface MainTableProps {
  data: any[];
  columnDefs: GridColDef[];
  overlayNoRowsTemplate: React.ReactNode;
  onSelectionChange?: (selectedIds: GridRowSelectionModel) => void;
  selectedRows?: GridRowSelectionModel;
  height?: string | number;
  loading?: boolean;
  columnVisibilityModel?: GridColumnVisibilityModel;
  getRowClassName?: (params: any) => string;
  [key: string]: any;
}

function LoadingIndicator() {
  return (
    <Box
      sx={{
        height: 32,
        backgroundColor: "#FFFFFF",
        transition: "none",
      }}
    >
      <Box
        className="mui-loading"
        sx={{
          paddingLeft: "12px",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "flex-start",
          height: "100%",
          userSelect: "none",
        }}
      >
        <PetalLoadingProgress />
        <Typography component="span" sx={{ paddingLeft: "12px" }}>
          Loading
        </Typography>
      </Box>
    </Box>
  );
}

export default function MainTable(props: MainTableProps): JSX.Element {
  const GridApi = useGridApiRef();
  const [tableData, setTableData] = React.useState<TableRowData[]>([]);
  const [rowSelectionModel, setRowSelectionModel] =
    React.useState<GridRowSelectionModel>(props.selectedRows || []);

  useEffect(() => {
    setTableData(
      props.data.map(
        (curData: any): TableRowData => ({
          ...curData,
          actions: "",
        })
      )
    );
  }, [props.data]);

  useEffect(() => {
    if (props.selectedRows !== undefined) {
      setRowSelectionModel(props.selectedRows);
    }
  }, [props.selectedRows]);

  const handleSelectionModelChange = (
    newSelectionModel: GridRowSelectionModel
  ) => {
    setRowSelectionModel(newSelectionModel);
    if (props.onSelectionChange) {
      props.onSelectionChange(newSelectionModel);
    }
  };

  return (
    <div style={{ height: props.height || "77.5vh", width: "100%" }}>
      <DataGridPro
        checkboxSelection
        disableRowSelectionOnClick
        apiRef={GridApi}
        autoHeight={false}
        loading={props.loading || false}
        columns={props.columnDefs}
        rows={tableData as GridRowsProp}
        rowSelectionModel={rowSelectionModel}
        onRowSelectionModelChange={handleSelectionModelChange}
        columnVisibilityModel={props.columnVisibilityModel}
        getRowClassName={props.getRowClassName}
        slots={{
          noRowsOverlay: () => props.overlayNoRowsTemplate,
          loadingOverlay: LoadingIndicator,
        }}
        sx={{
          "&.MuiDataGrid-root": {
            height: "100%",
            maxHeight: "100%",
            display: "flex",
            flexDirection: "column",
          },
        }}
        slotProps={{
          cell: {
            onContextMenu: () => null,
          },
        }}
        {...props}
      />
    </div>
  );
}
