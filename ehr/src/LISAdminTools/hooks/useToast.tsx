import React, { useEffect, useRef } from "react";
import { createRoot } from "react-dom/client";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { CciToast } from "@cci/mui-components";
import { TToastContext, TToast } from "../types/types";
import { toastsAtom, addToast<PERSON>tom } from "../atoms/common";

const ToastComponent = (props: TToast) => {
  const { open, handleClose, message, variant } = props;
  return (
    <CciToast
      text={message}
      type={variant}
      open={open}
      setOpen={handleClose ?? (() => {})}
      anchorOrigin={{ horizontal: "center", vertical: "bottom" }}
      alertSx={{ width: "max-content" }}
    />
  );
};

const GlobalToastContainer: React.FC = () => {
  const toasts = useAtomValue(toastsAtom);

  return (
    <>
      {React.Children.toArray(
        toasts.map((toast) => <ToastComponent key={toast.id} {...toast} />)
      )}
    </>
  );
};

class ToastManager {
  private container: HTMLElement | null = null;
  private root: any = null;
  private isInitialized = false;

  init() {
    if (this.isInitialized) return;

    this.container = document.createElement("div");
    this.container.id = "global-toast-container";
    this.container.style.position = "fixed";
    this.container.style.top = "0";
    this.container.style.left = "0";
    this.container.style.width = "100%";
    this.container.style.zIndex = "9999";
    this.container.style.pointerEvents = "none";
    document.body.appendChild(this.container);

    this.root = createRoot(this.container);
    this.root.render(<GlobalToastContainer />);

    this.isInitialized = true;
  }

  destroy() {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }
    this.isInitialized = false;
  }
}

const toastManager = new ToastManager();

export const useToast = () => {
  const addToast = useSetAtom(addToastAtom);
  const initRef = useRef(false);

  useEffect(() => {
    if (!initRef.current) {
      toastManager.init();
      initRef.current = true;
    }

    return () => {
      toastManager.destroy();
    };
  }, []);

  return addToast;
};

export const useToastMethods = () => {
  const createToast = useToast();

  return {
    success: (message: string, options?: Partial<TToastContext>) => {
      createToast({ msg: message, variant: "success", ...options });
    },
    error: (message: string, options?: Partial<TToastContext>) => {
      createToast({ msg: message, variant: "error", ...options });
    },
    warning: (message: string, options?: Partial<TToastContext>) => {
      createToast({ msg: message, variant: "warning", ...options });
    },
    info: (message: string, options?: Partial<TToastContext>) => {
      createToast({ msg: message, variant: "info", ...options });
    },
    create: createToast,
  };
};
