import React, { useEffect, useRef } from "react";
import { createRoot } from "react-dom/client";
import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { Box, Typography } from "@mui/material";
import { CciDialog, Button, StatusIcon } from "@cci/mui-components";
import {
  DialogState,
  MsgDialogConfig,
  DialogButton,
  MsgDialogMethods,
  ConfirmDialogOptions,
  AlertDialogOptions,
  DialogManagerInterface,
} from "../types/types";
import { dialogsAtom, addDialogAtom, removeDialogAtom } from "../atoms/common";
import { Root } from "react-dom/client";

// Re-export types for convenience
export type {
  DialogButton,
  MsgDialogConfig,
  ConfirmDialogOptions,
  AlertDialogOptions,
};

const DialogComponent: React.FC<DialogState> = (props) => {
  const { id, open, title, text, buttons, type } = props;
  const removeDialog = useSet<PERSON>tom(removeDialog<PERSON>tom);

  const handleClose = () => {
    removeDialog(id);
  };

  const content = (
    <Box sx={{ py: "16px" }}>
      <Box display="flex" alignItems="start" gap={2}>
        {type && <StatusIcon type={type} size={40} />}
        <Typography sx={{ fontSize: "16px", maxWidth: "500px" }}>
          {Array.isArray(text)
            ? text.map((line, idx) => (
                <p key={idx} style={{ margin: 0 }}>
                  {line}
                </p>
              ))
            : text}
        </Typography>
      </Box>
    </Box>
  );

  const renderButtons = () => {
    if (!buttons || buttons.length === 0) {
      return (
        <Button color="primary" variant="contained" onClick={handleClose}>
          OK
        </Button>
      );
    }

    return buttons.map((button, index) => (
      <Button
        key={index}
        variant={button.variant || "contained"}
        color={button.color || "primary"}
        disabled={button.disabled}
        autoFocus={button.autoFocus}
        sx={{
          ml: index > 0 ? 1 : 0,
          "&.MuiButton-colorSecondary": { backgroundColor: "#fff" },
        }}
        onClick={() => {
          button.onClick();
          handleClose();
        }}
      >
        {button.label}
      </Button>
    ));
  };

  return (
    <CciDialog
      title={title}
      content={content}
      buttons={<>{renderButtons()}</>}
      open={open}
      setOpen={(isOpen: boolean) => !isOpen && handleClose()}
      contentStyle={{ py: "16px" }}
    />
  );
};

const GlobalDialogContainer: React.FC = () => {
  const dialogs = useAtomValue(dialogsAtom);

  return (
    <>
      {dialogs.map((dialog) => (
        <DialogComponent key={dialog.id} {...dialog} />
      ))}
    </>
  );
};

class DialogManager implements DialogManagerInterface {
  private container: HTMLElement | null = null;
  private root: Root | null = null;
  private isInitialized = false;

  init(): void {
    if (this.isInitialized) return;

    this.container = document.createElement("div");
    this.container.id = "global-dialog-container";
    this.container.style.position = "fixed";
    this.container.style.top = "0";
    this.container.style.left = "0";
    this.container.style.width = "100%";
    this.container.style.zIndex = "10000";
    this.container.style.pointerEvents = "none";
    document.body.appendChild(this.container);

    this.root = createRoot(this.container);
    this.root.render(<GlobalDialogContainer />);

    this.isInitialized = true;
  }

  destroy(): void {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }
    this.isInitialized = false;
  }
}

const dialogManager = new DialogManager();

export const useMsgDialog = () => {
  const addDialog = useSetAtom(addDialogAtom);
  const initRef = useRef(false);

  useEffect(() => {
    if (!initRef.current) {
      dialogManager.init();
      initRef.current = true;
    }
  }, []);

  return addDialog;
};

export const useMsgDialogMethods = (): MsgDialogMethods => {
  const showDialog = useMsgDialog();

  return {
    show: (config: MsgDialogConfig) => {
      return showDialog(config);
    },

    confirm: (options: ConfirmDialogOptions) => {
      const {
        title = "Confirm",
        text,
        onConfirm,
        onCancel,
        confirmLabel = "Confirm",
        cancelLabel = "Cancel",
        type,
      } = options;

      return showDialog({
        title,
        text,
        type,
        buttons: [
          {
            label: cancelLabel,
            onClick: onCancel || (() => {}),
            variant: "outlined",
            color: "secondary",
          },
          {
            label: confirmLabel,
            onClick: onConfirm,
            variant: "contained",
            color: "primary",
          },
        ],
      });
    },

    alert: (options: AlertDialogOptions) => {
      const { title = "Alert", text, onOk, okLabel = "OK", type } = options;

      return showDialog({
        title,
        text,
        type,
        buttons: [
          {
            label: okLabel,
            onClick: onOk || (() => {}),
            variant: "contained",
            color: "primary",
          },
        ],
      });
    },
  };
};
