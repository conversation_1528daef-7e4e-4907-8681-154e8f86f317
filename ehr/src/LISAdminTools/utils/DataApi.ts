import axios from "axios";

export const baseUrl: string = cci.cfg.baseUrl;

export interface LisApiCallbacks {
  onSuccess?: (response: any) => void;
  onFailure?: (error: any) => void;
}

export const postData = ({
  url,
  formData,
  options,
  onSuccess,
  onFailure,
}: {
  url: string;
  formData?: object;
  options?: object;
} & LisApiCallbacks) => {
  let queryUrl = baseUrl + "/index.php/" + url;
  return axios
    .post(queryUrl, formData, {
      headers: {
        "Content-Type": "application/json",
      },
      ...options,
    })
    .then((response) => {
      if (onSuccess) onSuccess(response);
      return response.data;
    })
    .catch((error) => {
      if (onFailure) onFailure(error);
      console.error(error);
    });
};

export const getLisHospitals = (callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/getLisHospitals",
    ...callbacks,
  });
};

export const getLisLabs = (callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/getLisLabs",
    ...callbacks,
  });
};

export const getLisDepartments = (callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/getLisDepartments",
    ...callbacks,
  });
};

export const getLisInstruBench = (callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/getLisInstruBench",
    ...callbacks,
  });
};

export const updateHospital = (formData: any, callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/updateHospital",
    formData,
    ...callbacks,
  });
};
export const updateLab = (formData: any, callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/updateLab",
    formData,
    ...callbacks,
  });
};
export const updateDepartment = (formData: any, callbacks: LisApiCallbacks) => {
  return postData({
    url: "Lis/updateDepartment",
    formData,
    ...callbacks,
  });
};
export const updateInstrumentBench = (
  formData: any,
  callbacks: LisApiCallbacks
) => {
  return postData({
    url: "Lis/updateInstrumentBench",
    formData,
    ...callbacks,
  });
};

export const activateOrInactivate = (
  formData: any,
  callbacks: LisApiCallbacks
) => {
  return postData({
    url: "Lis/activate",
    formData,
    ...callbacks,
  });
};
