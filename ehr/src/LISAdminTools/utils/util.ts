import { isEqual } from "lodash";
import { FormField } from "../common/FormField";

export function intToWeekDay(day: any) {
  switch (day) {
    case 0:
      return "Sunday";
    case 1:
      return "Monday";
    case 2:
      return "Tuesday";
    case 3:
      return "Wednesday";
    case 4:
      return "Thursday";
    case 5:
      return "Friday";
    default:
      return "Saturday";
  }
}

export const isFormValid = (formFields: FormField[], formData: any) => {
  const requiredFields = formFields.filter((f) => f.required).map((f) => f.key);
  return requiredFields.every((key) => {
    const value = formData[key];
    return value !== undefined && value !== null && value !== "";
  });
};

export function parseFormDataToTree(data: any[]) {
  const result: any = {};

  data.forEach((item) => {
    const { hospital, lab, department } = item;

    if (!result[hospital]) {
      result[hospital] = {};
    }

    const labEntry = result[hospital];

    if (!labEntry[lab]) {
      labEntry[lab] = department ? {} : [];
    }

    if (department) {
      const labDepartments = labEntry[lab];
      if (!labDepartments[department]) {
        labDepartments[department] = [];
      }
      labDepartments[department].push(item);
    } else {
      const labArray = labEntry[lab];
      labArray.push(item);
    }
  });

  return result;
}

export const isValidJson = (jsonString: any) => {
  try {
    JSON.parse(jsonString);
    return true;
  } catch (e) {
    return false;
  }
};

export const isFormChanged = (initialForm: any, nowForm: any) => {
  if (!initialForm) return true;
  return !isEqual(nowForm, initialForm);
};
