import { atom } from "jotai";
import { FormField } from "../common/FormField";
import { FormAction } from "../types/types";
import { Department } from "../tabs/DepartmentAdmin/DepartmentAdmin";

export const departmentFormAtom = atom<any>({});
departmentFormAtom.debugLabel = "Department Form Atom";

export const departmentFormFieldsAtom = atom<FormField[]>([
  {
    key: "department",
    label: "Department Name",
    required: true,
    type: "text",
  },
  {
    key: "hospital",
    label: "Hospital",
    required: true,
    type: "select",
    options: [],
  },
  {
    key: "lab",
    label: "Lab",
    required: true,
    type: "select",
    options: [],
  },
]);

export const departmentOptionsMapAtom = atom<
  Record<string, Record<string, Record<string, Department[]>>>
>({});

export const updateDepartmentFormAtom = atom(
  null,
  (get, set, action: FormAction) => {
    const prev = get(departmentFormAtom);
    if (action.type === "SET_FIELD") {
      const updated: any = {
        ...prev,
        [action.fieldName]: action.value,
      };
      if (action.fieldName === "hospital") {
        updated.lab = "";
      }
      set(departmentFormAtom, updated);
    }

    if (action.type === "UPDATE_OPTIONS") {
      const prevFields = get(departmentFormFieldsAtom);
      const updatedFields = prevFields.map((field) =>
        field.key === action.fieldName
          ? { ...field, options: action.options }
          : field
      );
      set(departmentFormFieldsAtom, updatedFields);
    }
  }
);
