import { atom } from "jotai";
import { FormField } from "../common/FormField";
import { FormAction } from "../types/types";

export const hospitalFormAtom = atom<any>({});
hospitalFormAtom.debugLabel = "Hospital Form Atom";

export const hospitalFormFieldsAtom = atom<FormField[]>([
  {
    key: "hospital",
    label: "Hospital",
    required: true,
    type: "text",
    disabled: true,
  },
  {
    key: "deflab",
    label: "Default Lab",
    required: true,
    type: "select",
    options: ["La Jolla Memorial", "Facility B"],
  },
]);

export const updateHospitalFormAtom = atom(
  null,
  (get, set, action: FormAction) => {
    const prev = get(hospitalFormAtom);
    if (action.type === "SET_FIELD") {
      const updated: any = {
        ...prev,
        [action.fieldName]: action.value,
      };

      set(hospitalForm<PERSON>tom, updated);
    }
    if (action.type === "UPDATE_OPTIONS") {
      const prevFields = get(hospitalFormFieldsAtom);
      const updatedFields = prevFields.map((field) =>
        field.key === action.fieldName
          ? { ...field, options: action.options }
          : field
      );
      set(hospitalFormFieldsAtom, updatedFields);
    }
  }
);
