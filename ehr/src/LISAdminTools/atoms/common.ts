import { atom } from "jotai";
import {
  TToast,
  TToastContext,
  DialogState,
  MsgDialogConfig,
} from "../types/types";

export const toastsAtom = atom<TToast[]>([]);
toastsAtom.debugLabel = "Toasts Atom";

export const addToastAtom = atom(null, (get, set, option: TToastContext) => {
  const { msg, variant, ...other } = option;
  const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);

  const newToast: TToast = {
    id,
    variant: variant ? variant : "success",
    message: msg,
    ...other,
    open: true,
    handleClose: () => {
      set(removeToastAtom, id);
    },
  };

  const currentToasts = get(toastsAtom);
  set(toastsAtom, [...currentToasts, newToast]);
});

export const removeToastAtom = atom(null, (get, set, toastId: string) => {
  const currentToasts = get(toastsAtom);
  const toastToRemove = currentToasts.find((d) => d.id === toastId);

  if (toastToRemove?.onClose) {
    toastToRemove.onClose();
  }

  set(
    toastsAtom,
    currentToasts.filter((d) => d.id !== toastId)
  );
});

// MsgDialog atoms
export const dialogsAtom = atom<DialogState[]>([]);
dialogsAtom.debugLabel = "Dialogs Atom";

export const addDialogAtom = atom(null, (get, set, config: MsgDialogConfig) => {
  const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);

  const newDialog: DialogState = {
    id,
    open: true,
    ...config,
  };

  const currentDialogs = get(dialogsAtom);
  set(dialogsAtom, [...currentDialogs, newDialog]);

  return id;
});

export const removeDialogAtom = atom(null, (get, set, dialogId: string) => {
  const currentDialogs = get(dialogsAtom);
  const dialogToRemove = currentDialogs.find((d) => d.id === dialogId);

  if (dialogToRemove?.onClose) {
    dialogToRemove.onClose();
  }

  set(
    dialogsAtom,
    currentDialogs.filter((d) => d.id !== dialogId)
  );
});
