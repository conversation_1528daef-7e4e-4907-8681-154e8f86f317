import { atom } from "jotai";
import { FormField } from "../common/FormField";
import { FormAction } from "../types/types";
import { Instrument } from "../tabs/InstrumentAdmin/InstrumentAdmin";

export const instrumentFormAtom = atom<any>({});
instrumentFormAtom.debugLabel = "Instrument Form Atom";

export const instrumentFormFieldsAtom = atom<FormField[]>([
  {
    key: "description",
    label: "Instrument/Bench Name",
    required: true,
    type: "text",
  },
  {
    key: "thetype",
    label: "Type",
    required: true,
    type: "select",
    options: ["Instrument", "Bench"],
  },
  {
    key: "hospital",
    label: "Hospital",
    required: true,
    type: "select",
    options: [],
  },
  {
    key: "lab",
    label: "Lab",
    required: true,
    type: "select",
    options: [],
  },
  {
    key: "department",
    label: "Department",
    required: true,
    type: "select",
    options: [],
  },
]);

export const instrumentOptionsMapAtom = atom<
  Record<string, Record<string, Record<string, Instrument[]>>>
>({});

export const updateInstrumentFormAtom = atom(
  null,
  (get, set, action: FormAction) => {
    if (action.type === "SET_FIELD") {
      const prev = get(instrumentFormAtom);
      const updated: any = {
        ...prev,
        [action.fieldName]: action.value,
      };
      if (action.fieldName === "hospital") {
        updated.lab = "";
        updated.department = "";
      } else if (action.fieldName === "lab") {
        updated.department = "";
      }
      set(instrumentFormAtom, updated);
    }
    if (action.type === "UPDATE_OPTIONS") {
      const prevFields = get(instrumentFormFieldsAtom);
      const updatedFields = prevFields.map((field) =>
        field.key === action.fieldName
          ? { ...field, options: action.options }
          : field
      );
      set(instrumentFormFieldsAtom, updatedFields);
    }
  }
);
