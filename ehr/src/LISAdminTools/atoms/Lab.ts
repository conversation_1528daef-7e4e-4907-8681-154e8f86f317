import { atom } from "jotai";
import { FormField } from "../common/FormField";
import { LabFormAction } from "../types/types";
import { STATE_LABELS } from "@cci-monorepo/common";

export const initialLabForm = {
  hop: Array.from({ length: 7 }, (_, i) => ({
    Day: i,
    Open: "",
    Close: "",
    TFH: false,
  })),
};

export const labFormAtom = atom<any>(initialLabForm);
labFormAtom.debugLabel = "Lab Form Atom";

export const labFormFieldsAtom = atom<FormField[]>([
  // left
  { key: "lab", label: "Laboratory Name", required: true, type: "text" },
  {
    key: "hospital",
    label: "Parent Facility",
    required: true,
    type: "select",
    options: [],
  },

  // right
  { key: "clia", label: "CLIA #", type: "text" },
  { key: "address", label: "Street Address", type: "text" },
  { key: "city", label: "City", type: "text" },
  {
    key: "thestate",
    label: "State",
    type: "select",
    options: STATE_LABELS.filter((item) => item),
    placeholder: "Select",
  },
  { key: "zipcode", label: "Zip Code", type: "text" },
  {
    key: "phonenum",
    label: "Phone Number",
    type: "text",
    placeholder: "(*************",
  },
]);

export const updateLabFormAtom = atom(
  null,
  (get, set, action: LabFormAction) => {
    const prev = get(labFormAtom);

    switch (action.type) {
      case "SET_FIELD": {
        const updated: any = {
          ...prev,
          [action.fieldName]: action.value,
        };

        // Special case: toggle TFH for all rows
        if (action.fieldName === "TFH") {
          updated.hop = prev.hop.map((row: any) => ({
            ...row,
            Open: action.value ? "" : row.Open,
            Close: action.value ? "" : row.Close,
            TFH: action.value,
          }));
        }

        set(labFormAtom, updated);
        break;
      }

      case "UPDATE_HRS_ROW": {
        const updatedHop = prev.hop.map((row: any, i: number) => {
          if (i !== action.idx) return row;

          const updatedRow = {
            ...row,
            [action.fieldName]: action.value,
          };

          if (action.fieldName === "TFH" && action.value === true) {
            updatedRow.Open = "";
            updatedRow.Close = "";
          }

          return updatedRow;
        });

        const hasAnyFalse = updatedHop.some((row: any) => !row.TFH);
        set(labFormAtom, {
          ...prev,
          TFH: !hasAnyFalse,
          hop: updatedHop,
        });
        break;
      }

      case "UPDATE_OPTIONS": {
        const prevFields = get(labFormFieldsAtom);
        const updatedFields = prevFields.map((field) =>
          field.key === action.fieldName
            ? { ...field, options: action.options }
            : field
        );
        set(labFormFieldsAtom, updatedFields);
        break;
      }
    }
  }
);
