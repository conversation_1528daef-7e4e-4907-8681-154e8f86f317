import React from "react";
import { ThemeProvider } from "@mui/material";
import { theme } from "./theme";

import AdminLayoutDrawer, {
  TMenuItems,
  loadContentFile,
} from "@cci-monorepo/common/mui-components/src/components/layout/AdminLayout/AdminLayoutDrawer";

/**
 * The module for LIS Admin Tools
 */
const LISAdminTools = () => {
  const menuItems: TMenuItems = {
    LISAdminTools: {
      name: "Lab Information System",
      items: {
        HospitalAdmin: {
          name: "Hospital Admin",
          path: () => import("./tabs/HospitalAdmin/HospitalAdmin"),
        },
        LabAdmin: {
          name: "Lab Admin",
          path: () => import("./tabs/LabAdmin/LabAdmin"),
        },
        DepartmentAdmin: {
          name: "Department Admin",
          path: () => import("./tabs/DepartmentAdmin/DepartmentAdmin"),
        },
        InstrumentBenchAdmin: {
          name: "Instrument/Bench Admin",
          path: () => import("./tabs/InstrumentAdmin/InstrumentAdmin"),
        },
      },
    },
  };

  const defaultMenuItem = 0;

  return (
    <ThemeProvider theme={theme}>
      <AdminLayoutDrawer
        pageTitle="Admin Tools"
        menuItems={menuItems}
        loadContent={loadContentFile}
        defaultActiveMenuItem={defaultMenuItem}
        defaultModule={"LISAdminTools"}
      />
    </ThemeProvider>
  );
};

export default LISAdminTools;
