import React, { useState, useEffect, useMemo } from "react";
import { Grid, Stack } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid-pro";
import MainTable from "../../common/MainTable";
import EditIcon from "@mui/icons-material/Edit";
import AdminToolbar, {
  ToolbarItemConfig,
} from "@cci-monorepo/common/mui-components/src/components/layout/AdminToolbar/AdminToolbar";
import { useToastMethods } from "../../hooks/useToast";
import { useMsgDialogMethods } from "../../hooks/useMsgDialog";

import { GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { DepartmentDialog } from "./DepartmentDialog";
import {
  getLisDepartments,
  getLisLabs,
  activateOrInactivate,
} from "../../utils/DataApi";
import { debounce } from "lodash";
import { useSet<PERSON>tom } from "jotai";
import {
  departmentOptionsMap<PERSON>tom,
  updateDepartment<PERSON>orm<PERSON>tom,
} from "../../atoms/Department";
import { parseFormDataToTree } from "../../utils/util";
import AddIcon from "@mui/icons-material/Add";

export interface Department {
  id: number;
  department: string;
  lab: string;
  hospital: string;
  active: "0" | "1";
}

const DepartmentAdmin = () => {
  const createToast = useToastMethods();
  const createMsgDialog = useMsgDialogMethods();
  const [lookup, setLookup] = useState("");
  const [includeInactive, setIncludeInactive] = useState(false);
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [departmentData, setDepartmentData] = useState<Department[]>([]);
  const [editDialog, setEditDialog] = React.useState<any>({
    open: false,
    isCreate: false,
  });
  const [loading, setLoading] = useState(false);
  const setDepartmentOptionsMap = useSetAtom(departmentOptionsMapAtom);
  const updateDepartmentForm = useSetAtom(updateDepartmentFormAtom);

  useEffect(() => {
    fetchDepartmentData();
    getDepartmentOptionsMap();
  }, []);

  const fetchDepartmentData = () => {
    setLoading(true);
    getLisDepartments({
      onSuccess: (response) => {
        if (response.data?.success) {
          setDepartmentData(response.data.data || []);
        } else {
          createToast.error(
            response.data?.errmsg || "Failed to fetch department data"
          );
        }
      },
      onFailure: (error) => {
        createToast.error("Failed to fetch department data");
      },
    }).finally(() => {
      const debouncedSetLoading = debounce(() => setLoading(false), 150);
      debouncedSetLoading();
    });
  };

  const getDepartmentOptionsMap = () => {
    getLisLabs({
      onSuccess: (response) => {
        if (response.data?.success) {
          const parsedTree = parseFormDataToTree(response.data.data);
          setDepartmentOptionsMap(parsedTree);
          const hospitals = Object.keys(parsedTree);
          updateDepartmentForm({
            type: "UPDATE_OPTIONS",
            fieldName: "hospital",
            options: hospitals,
          });
        }
      },
    });
  };

  const filteredData = useMemo(() => {
    let filtered = lookup
      ? departmentData.filter((department) =>
          department.department.toLowerCase().includes(lookup.toLowerCase())
        )
      : departmentData;

    if (!includeInactive) {
      filtered = filtered.filter((department) => department.active === "1");
    }

    return filtered;
  }, [lookup, departmentData, includeInactive]);

  const getColDefs = (): GridColDef[] => [
    {
      field: "actions",
      headerName: "Edit",
      width: 50,
      sortable: false,
      align: "center",
      renderCell: (params) => (
        <EditIcon
          sx={{ color: "#4a5eac", fontSize: 24, cursor: "pointer" }}
          onClick={() => setEditDialog({ row: params.row, open: true })}
        />
      ),
    },
    {
      field: "department",
      headerName: "Departments",
      width: 400,
    },
    {
      field: "lab",
      headerName: "Lab",
      width: 400,
    },
    {
      field: "hospital",
      headerName: "Hospital",
      flex: 1,
    },
    {
      field: "active",
      headerName: "Status",
      width: 200,
      renderCell: (params: any) => {
        const isActive = params.row.active === "1";
        return isActive ? "Active" : "Inactive";
      },
    },
  ];

  const SelectedItems = () => {
    return (
      <ul
        style={{
          color: "#000",
          fontSize: "16px",
          fontWeight: "700",
          listStyleType: "none",
          padding: "0",
          maxHeight: "500px",
          overflow: "auto",
        }}
      >
        {selectedRows
          .map((id) => filteredData.find((item) => item.id === id))
          .filter((item): item is Department => !!item)
          .map((item) => (
            <li key={item.id}>{item.department}</li>
          ))}
      </ul>
    );
  };

  const handleActivationChange = (activate: boolean) => {
    const action = activate ? "Activate" : "Inactivate";
    const value = activate ? "1" : "0";

    createMsgDialog.confirm({
      title: action,
      type: "Question",
      text: (
        <div
          style={{
            color: "#000",
            fontSize: "16px",
            fontWeight: "400",
            maxWidth: "450px",
          }}
        >
          Are you sure you want to {action.toLowerCase()} the following items?
          This change may affect other lab hierarchy configurations.
          <SelectedItems />
        </div>
      ),
      confirmLabel: action,
      onConfirm: () => {
        activateOrInactivate(
          {
            ids: selectedRows.join(","),
            type: "department",
            value,
          },
          {
            onSuccess: () => {
              createToast.success(
                `Department ${action.toLowerCase()}d successfully`
              );
              fetchDepartmentData();
              setSelectedRows([]);
            },
            onFailure: () => {
              createToast.error(`Failed to ${action.toLowerCase()} department`);
            },
          }
        );
      },
    });
  };

  const buttons: ToolbarItemConfig[] = [
    {
      label: "Inactivate",
      color: "secondary",
      disabled:
        selectedRows.length === 0 ||
        !selectedRows.some((id) => {
          const row = filteredData.find((item) => item.id === id);
          return row?.active === "1";
        }),
      onClick: () => handleActivationChange(false),
    },
    {
      label: "Activate",
      color: "secondary",
      disabled:
        selectedRows.length === 0 ||
        !selectedRows.some((id) => {
          const row = filteredData.find((item) => item.id === id);
          return row?.active === "0";
        }),
      onClick: () => handleActivationChange(true),
    },
    {
      label: "Create Department",
      startIcon: <AddIcon />,
      onClick: () => {
        setEditDialog({ open: true, isCreate: true });
      },
    },
  ];

  return (
    <Stack>
      <Grid container sx={{ padding: "30px" }}>
        <Grid item xs={12} sx={{ paddingBottom: "30px" }}>
          <AdminToolbar
            searchValue={lookup}
            onSearchChange={setLookup}
            searchPlaceholder="Search Departments"
            includeInactive={includeInactive}
            onInactiveToggle={() => setIncludeInactive(!includeInactive)}
            items={buttons}
          />
        </Grid>
        <Grid item xs={12}>
          <MainTable
            columnDefs={getColDefs()}
            disableColumnMenu
            data={filteredData}
            selectedRows={selectedRows}
            onSelectionChange={setSelectedRows}
            loading={loading}
            columnVisibilityModel={{
              active: includeInactive,
            }}
            getRowClassName={(params) =>
              params.row.active === "0" ? "inactive-row" : ""
            }
            overlayNoRowsTemplate={
              <span>
                <b>
                  No Departments{" "}
                  {lookup && departmentData.length > 0 ? "Found" : "Available"}
                </b>
              </span>
            }
          />
        </Grid>
      </Grid>
      <DepartmentDialog
        {...editDialog}
        fetchDepartmentData={fetchDepartmentData}
        onClose={() => setEditDialog({ open: false })}
      />
    </Stack>
  );
};

export default DepartmentAdmin;
