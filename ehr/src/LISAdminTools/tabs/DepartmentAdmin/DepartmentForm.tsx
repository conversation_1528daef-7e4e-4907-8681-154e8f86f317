import { useAtom, useAtomValue } from "jotai";
import {
  departmentForm<PERSON>tom,
  departmentFormFieldsAtom,
  departmentOptionsMapAtom,
  updateDepartmentFormAtom,
} from "../../atoms/Department";
import FormFieldRenderer from "../../common/FormField";
import { Box } from "@mui/material";
import { useEffect } from "react";

const DepartmentForm = () => {
  const [departmentForm] = useAtom(departmentFormAtom);
  const [departmentFormFields] = useAtom(departmentFormFieldsAtom);
  const departmentOptionsMap = useAtomValue(departmentOptionsMapAtom);
  const [, updateDepartmentForm] = useAtom(updateDepartmentFormAtom);

  useEffect(() => {
    const hospital = departmentForm.hospital;
    const labs = hospital
      ? Object.keys(departmentOptionsMap[hospital] || {})
      : [];
    updateDepartmentForm({
      type: "UPDATE_OPTIONS",
      fieldName: "lab",
      options: labs,
    });
  }, [departmentForm.hospital]);

  const handleChange = (key: string, value: any) => {
    updateDepartmentForm({ type: "SET_FIELD", fieldName: key, value });
  };

  return (
    <>
      {departmentFormFields.map((field: any) => (
        <Box margin="16px 0" width="560px" key={field.key}>
          <FormFieldRenderer
            field={field}
            value={departmentForm[field.key]}
            onChange={(data) => handleChange(field.key, data)}
            disabled={field.key === "lab" && !departmentForm.hospital}
            labelPosition="left"
            labelStyle={{ width: "200px" }}
          />
        </Box>
      ))}
    </>
  );
};
export default DepartmentForm;
