import { Button as <PERSON><PERSON><PERSON><PERSON>on, CciDialog } from "@cci/mui-components";
import DepartmentForm from "./DepartmentForm";
import { Typography } from "@mui/material";
import { useAtom } from "jotai";
import { useEffect } from "react";
import {
  departmentFormAtom,
  departmentFormFieldsAtom,
} from "../../atoms/Department";
import { isFormChanged, isFormValid } from "../../utils/util";
import { updateDepartment } from "../../utils/DataApi";
import { useToastMethods } from "../../hooks/useToast";

interface DepartmentDialogProps {
  isCreate: boolean;
  open: boolean;
  onClose: () => void;
  fetchDepartmentData: () => void;
  row?: any;
}
export const DepartmentDialog = ({
  isCreate,
  open,
  row,
  fetchDepartmentData,
  onClose,
}: DepartmentDialogProps) => {
  const [departmentForm, setDepartmentForm] = useAtom(departmentFormAtom);
  const [departmentFormFields] = useAtom(departmentFormFieldsAtom);
  const createToast: any = useToastMethods();

  useEffect(() => {
    if (open) {
      if (!isCreate && row) setDepartmentForm(row);
    } else {
      setDepartmentForm({});
    }
  }, [open]);

  const onSave = () => {
    const formData = {
      ...(isCreate ? {} : { dpmtid: departmentForm.id }),
      dpmtname: departmentForm.department,
      hospital: departmentForm.hospital,
      labname: departmentForm.lab,
    };

    updateDepartment(formData, {
      onSuccess: (response) => {
        fetchDepartmentData();
        createToast.success(
          `Department ${isCreate ? "created" : "updated"} successfully`
        );
      },
      onFailure: (error) => {
        createToast.error("Update failed");
      },
    });
  };
  const DepartmentDialogContent = () => {
    return (
      <>
        {isCreate && (
          <Typography sx={{ font: "normal 400 15px Roboto" }}>
            To <b>Create</b> an department, specify the following:
          </Typography>
        )}
        <DepartmentForm></DepartmentForm>
      </>
    );
  };
  const DepartmentDialogButtons = () => {
    return (
      <>
        <CciButton color="secondary" onClick={onClose}>
          Cancel
        </CciButton>
        <CciButton
          color="primary"
          disabled={
            !isFormValid(departmentFormFields, departmentForm) ||
            (!isCreate && !isFormChanged(row, departmentForm))
          }
          onClick={() => {
            onSave();
            onClose();
          }}
        >
          Save
        </CciButton>
      </>
    );
  };
  return (
    <>
      <CciDialog
        title={`${isCreate ? "Create" : "Edit"} Department`}
        open={open}
        setOpen={onClose}
        handleClose={onClose}
        contentStyle={{
          padding: "16px 32px",
          height: "100%",
          width: "100%",
          overflow: "auto",
        }}
        content={DepartmentDialogContent()}
        buttonStyle={{
          display: "flex",
          gap: 1,
        }}
        buttons={DepartmentDialogButtons()}
        sx={{
          "& .MuiDialogTitle-root": {
            padding: "16px 24px",
          },
          "& .MuiDialogActions-root": {
            padding: "16px",
          },
        }}
      ></CciDialog>
    </>
  );
};
