import React, { useState, useEffect, useMemo } from "react";
import { Grid, Stack } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid-pro";
import MainTable from "../../common/MainTable";
import EditIcon from "@mui/icons-material/Edit";
import AdminToolbar, {
  ToolbarItemConfig,
} from "@cci-monorepo/common/mui-components/src/components/layout/AdminToolbar/AdminToolbar";
import { useToastMethods } from "../../hooks/useToast";
import { useMsgDialogMethods } from "../../hooks/useMsgDialog";
import { GridRowSelectionModel } from "@mui/x-data-grid-pro";
import LabLocationDialog from "./LabLocationDialog";
import {
  getLisHospitals,
  getLisLabs,
  activateOrInactivate,
} from "../../utils/DataApi";
import { debounce } from "lodash";
import { updateLab<PERSON><PERSON><PERSON>tom } from "../../atoms/Lab";
import { useSet<PERSON><PERSON> } from "jotai";
import AddIcon from "@mui/icons-material/Add";

interface Lab {
  id: number;
  lab: string;
  hospital: string;
  active: "0" | "1";
}

const LabAdmin = () => {
  const createToast = useToastMethods();
  const createMsgDialog = useMsgDialogMethods();
  const [lookup, setLookup] = useState("");
  const [includeInactive, setIncludeInactive] = useState(false);
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [labData, setLabData] = useState<Lab[]>([]);
  const [editDialog, setEditDialog] = React.useState<any>({
    open: false,
    isCreate: false,
  });
  const [loading, setLoading] = useState(false);
  const updateLabForm = useSetAtom(updateLabFormAtom);

  useEffect(() => {
    fetchLabData();
    getOptions();
  }, []);

  const fetchLabData = () => {
    setLoading(true);
    getLisLabs({
      onSuccess: (response) => {
        if (response.data?.success) {
          setLabData(response.data.data || []);
        } else {
          createToast.error(
            response.data?.errmsg || "Failed to fetch lab data"
          );
        }
      },
      onFailure: (error) => {
        createToast.error("Failed to fetch lab data");
      },
    }).finally(() => {
      const debouncedSetLoading = debounce(() => setLoading(false), 150);
      debouncedSetLoading();
    });
  };

  const getOptions = () => {
    getLisHospitals({
      onSuccess: (response) => {
        if (response.data?.success) {
          const hospitals = Array.from(
            new Set(
              (response.data.data || []).map((item: any) => item.hospital)
            )
          );
          updateLabForm({
            type: "UPDATE_OPTIONS",
            fieldName: "hospital",
            options: hospitals,
          });
        }
      },
    });
  };

  const filteredData = useMemo(() => {
    let filtered = lookup
      ? labData.filter((lab) =>
          lab.lab.toLowerCase().includes(lookup.toLowerCase())
        )
      : labData;

    if (!includeInactive) {
      filtered = filtered.filter((lab) => lab.active === "1");
    }

    return filtered;
  }, [lookup, labData, includeInactive]);

  const getColDefs = (): GridColDef[] => [
    {
      field: "actions",
      headerName: "Edit",
      width: 50,
      sortable: false,
      align: "center",
      renderCell: (params) => (
        <EditIcon
          sx={{ color: "#4a5eac", fontSize: 24, cursor: "pointer" }}
          onClick={() => {
            setEditDialog({ row: params.row, open: true });
          }}
        />
      ),
    },
    {
      field: "lab",
      headerName: "Lab",
      width: 400,
    },
    {
      field: "hospital",
      headerName: "Hospital",
      flex: 1,
    },
    {
      field: "active",
      headerName: "Status",
      width: 200,
      renderCell: (params: any) => {
        const isActive = params.row.active === "1";
        return isActive ? "Active" : "Inactive";
      },
    },
  ];

  const SelectedItems = () => {
    return (
      <ul
        style={{
          color: "#000",
          fontSize: "16px",
          fontWeight: "700",
          listStyleType: "none",
          padding: "0",
          maxHeight: "500px",
          overflow: "auto",
        }}
      >
        {selectedRows
          .map((id) => filteredData.find((item) => item.id === id))
          .filter((item): item is Lab => !!item)
          .map((item) => (
            <li key={item.id}>{item.lab}</li>
          ))}
      </ul>
    );
  };

  const handleActivationChange = (activate: boolean) => {
    const action = activate ? "Activate" : "Inactivate";
    const value = activate ? "1" : "0";

    createMsgDialog.confirm({
      title: action,
      type: "Question",
      text: (
        <div
          style={{
            color: "#000",
            fontSize: "16px",
            fontWeight: "400",
            maxWidth: "450px",
          }}
        >
          Are you sure you want to {action.toLowerCase()} the following items?
          This change may affect other lab hierarchy configurations.
          <SelectedItems />
        </div>
      ),
      confirmLabel: action,
      onConfirm: () => {
        activateOrInactivate(
          {
            ids: selectedRows.join(","),
            type: "lab",
            value,
          },
          {
            onSuccess: () => {
              createToast.success(`Lab ${action.toLowerCase()}d successfully`);
              fetchLabData();
              setSelectedRows([]);
            },
            onFailure: () => {
              createToast.error(`Failed to ${action.toLowerCase()} lab`);
            },
          }
        );
      },
    });
  };

  const buttons: ToolbarItemConfig[] = [
    {
      label: "Inactivate",
      color: "secondary",
      disabled:
        selectedRows.length === 0 ||
        !selectedRows.some((id) => {
          const row = filteredData.find((item) => item.id === id);
          return row?.active === "1";
        }),
      onClick: () => handleActivationChange(false),
    },
    {
      label: "Activate",
      color: "secondary",
      disabled:
        selectedRows.length === 0 ||
        !selectedRows.some((id) => {
          const row = filteredData.find((item) => item.id === id);
          return row?.active === "0";
        }),
      onClick: () => handleActivationChange(true),
    },
    {
      label: "Create Lab Location",
      startIcon: <AddIcon />,
      onClick: () => {
        setEditDialog({ open: true, isCreate: true });
      },
    },
  ];

  return (
    <Stack>
      <Grid container sx={{ padding: "30px" }}>
        <Grid item xs={12} sx={{ paddingBottom: "30px" }}>
          <AdminToolbar
            searchValue={lookup}
            onSearchChange={setLookup}
            searchPlaceholder="Search Labs"
            includeInactive={includeInactive}
            onInactiveToggle={() => setIncludeInactive(!includeInactive)}
            items={buttons}
          />
        </Grid>
        <Grid item xs={12}>
          <MainTable
            columnDefs={getColDefs()}
            disableColumnMenu
            data={filteredData}
            selectedRows={selectedRows}
            onSelectionChange={setSelectedRows}
            loading={loading}
            columnVisibilityModel={{
              active: includeInactive,
            }}
            getRowClassName={(params) =>
              params.row.active === "0" ? "inactive-row" : ""
            }
            overlayNoRowsTemplate={
              <span>
                <b>
                  No Labs {lookup && labData.length > 0 ? "Found" : "Available"}
                </b>
              </span>
            }
          />
        </Grid>
      </Grid>
      <LabLocationDialog
        {...editDialog}
        labData={labData}
        fetchLabData={fetchLabData}
        onClose={() => setEditDialog({ open: false })}
      />
    </Stack>
  );
};

export default LabAdmin;
