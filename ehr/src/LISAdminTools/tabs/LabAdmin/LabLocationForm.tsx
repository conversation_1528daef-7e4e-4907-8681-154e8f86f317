import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import { ifnullblank } from "@cci-monorepo/common/data/util";

import { useAtom } from "jotai";
import {
  labForm<PERSON>tom,
  labFormFieldsAtom,
  updateLabForm<PERSON>tom,
} from "../../atoms/Lab";
import { Checkbox, FormControlLabel, Typography } from "@mui/material";
import LabHrGrid from "./LabHrGrid";
import FormFieldRenderer from "../../common/FormField";
import { addHyphensToPhone } from "@cci-monorepo/common";

const getNextId = (dataArray: any[]) => {
  if (!dataArray || dataArray.length === 0) return 1;
  const maxId = Math.max(...dataArray.map((item) => parseInt(item.id) || 0));
  return maxId + 1;
};

const StyledRootBox = styled(Box)(() => ({
  width: "100%",
  height: "auto",
  padding: "16px 0",
  display: "inline-flex",
  "& .psatpd-titletext": {
    fontSize: "20px",
    color: "black",
  },
  "& .psatpd-headertext": {
    fontSize: "15px",
    marginBottom: "5px",
  },
  "& .MuiAutocomplete-root .MuiTextField-root": {
    width: "100%",
  },
}));

const StyledLeftPanelBox = styled(Box)(() => ({
  width: "auto",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  "& .psatpd-labForm": {
    marginBottom: "15px",
  },
}));

const StyledRightPanelBox = styled(Box)(() => ({
  width: "auto",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  flex: 1,
  "& .psatpd-titletext": {
    marginBottom: "40px",
  },
  "& .psat-nofocusgrid": {
    marginTop: "20px",
    marginBottom: "20px",
  },
  "&  .psatpd-headertext": {
    fontSize: "15px",
  },
}));

const LabLocationForm = ({ labData }: any) => {
  const [labForm] = useAtom(labFormAtom);
  const [, updateLabForm] = useAtom(updateLabFormAtom);
  const [labFormFields] = useAtom(labFormFieldsAtom);

  const nextId = getNextId(labData);

  const handleChange = (key: string, value: any) => {
    updateLabForm({
      type: "SET_FIELD",
      fieldName: key,
      value: key === "phonenum" ? addHyphensToPhone(value) : value,
    });
  };

  return (
    <StyledRootBox>
      <StyledLeftPanelBox>
        <Typography
          sx={{
            font: "normal normal bold 20px/25px Helvetica",
            display: "block",
          }}
        >
          Lab Details
        </Typography>
        <Typography sx={{ font: "normal 400 16px Roboto", padding: "8px 0" }}>
          ID: {ifnullblank(labForm?.id, nextId.toString())}
        </Typography>
        <div style={{ display: "flex" }}>
          <Box
            sx={{
              width: "253px",
              height: "100%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {labFormFields.slice(0, 2).map((field) => (
              <Box sx={{ marginBottom: "16px" }} key={field.key}>
                <FormFieldRenderer
                  field={field}
                  value={labForm[field.key]}
                  onChange={(data: any) => handleChange(field.key, data)}
                  labelPosition="top"
                />
              </Box>
            ))}
          </Box>
          <div style={{ width: 2, margin: "0px 48px", background: "white" }} />
          <Box
            sx={{
              width: "253px",
              height: "100%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {labFormFields.slice(2).map((field, idx, arr) => {
              if (field.key === "thestate") {
                const zipField = arr[idx + 1];
                return (
                  <Box sx={{ display: "flex", gap: 2, mb: 2 }} key={field.key}>
                    {[field, zipField].map((field) => (
                      <Box sx={{ flex: 1 }} key={field.key}>
                        <FormFieldRenderer
                          field={field}
                          value={labForm[field.key]}
                          onChange={(data: any) =>
                            handleChange(field.key, data)
                          }
                          labelPosition="top"
                        />
                      </Box>
                    ))}
                  </Box>
                );
              }
              if (field.key === "zipcode") return null;
              return (
                <Box sx={{ mb: 2 }} key={field.key}>
                  <FormFieldRenderer
                    field={field}
                    value={labForm[field.key]}
                    onChange={(data: any) => handleChange(field.key, data)}
                    labelPosition="top"
                  />
                </Box>
              );
            })}
          </Box>
          <div style={{ width: 2, margin: "0px 48px", background: "white" }} />
        </div>
      </StyledLeftPanelBox>
      <StyledRightPanelBox>
        <Typography
          sx={{
            font: "normal normal bold 20px/25px Helvetica",
            display: "block",
          }}
        >
          Hours of Operation
        </Typography>
        <FormControlLabel
          control={
            <Checkbox
              checked={labForm.TFH ?? false}
              onChange={(event, value) => handleChange("TFH", value)}
              sx={{
                "&.Mui-checked": {
                  color: "#319436",
                },
              }}
            />
          }
          label="24 Hour Laboratory"
          sx={{
            ".MuiTypography-root": {
              fontSize: "16px",
            },
          }}
        />
        <LabHrGrid
          value={labForm.hop}
          disabled={labForm.TFH}
          onChange={(action) => {
            if (action.type === "UPDATE_HRS_ROW") {
              updateLabForm(action);
            }
          }}
        />
      </StyledRightPanelBox>
    </StyledRootBox>
  );
};

export default LabLocationForm;
