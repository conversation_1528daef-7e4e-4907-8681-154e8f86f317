import React from "react";
import { Checkbox, FormControlLabel } from "@mui/material";

interface ActionRendererProps {
  value: {
    value: boolean;
    disabled?: boolean;
  };
  colDef: {
    cellRendererParams: {
      onChange: (value: boolean, props: any) => void;
    };
  };
  api?: any;
  id?: any;
}

const ActionRenderer: React.FC<ActionRendererProps> = (props) => {
  const onChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    value: boolean
  ) => {
    props.colDef.cellRendererParams.onChange(value, props);
  };

  return (
    <FormControlLabel
      control={
        <Checkbox
          checked={props.value.value}
          onChange={onChange}
          disabled={false}
        />
      }
      label="24 Hours"
      sx={{
        ".MuiTypography-root": {
          fontSize: "16px",
        },
      }}
    />
  );
};

export default React.memo(
  ActionRenderer,
  (p: ActionRendererProps, n: ActionRendererProps) =>
    p.value.value === n.value.value && p.value.disabled === n.value.disabled
);
