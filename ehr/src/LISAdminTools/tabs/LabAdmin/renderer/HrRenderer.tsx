import React from "react";
import { nullorblank } from "@cci-monorepo/common";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

interface HrRendererProps {
  value: {
    value: string | null | undefined;
    disabled?: boolean;
  };
  colDef: {
    cellRendererParams: {
      onChange: (formattedDate: string, props: any) => void;
    };
  };
  api?: any;
  id?: any;
}

const HrRenderer: React.FC<HrRendererProps> = (props) => {
  const actualValue = props.value.value;

  const dayjsValue = nullorblank(actualValue)
    ? null
    : dayjs(actualValue, "HH:mm");

  const handleDateChange = (e: dayjs.Dayjs | null) => {
    const formattedDate = dayjs(e).format("HH:mm");

    props.colDef.cellRendererParams.onChange(formattedDate, props);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TimePicker
        ampm={false}
        value={dayjsValue}
        onChange={handleDateChange}
        slotProps={{
          textField: {
            placeholder: "  :  ",
          },
        }}
        disabled={props.value.disabled}
        sx={{
          padding: "0 0 0 0",
          font: "normal normal normal 14px Helvetica",
          "& .MuiInputBase-root": {
            border: "0px",
            "& .MuiOutlinedInput-input": {
              paddingLeft: "8px",
            },
          },
          "& .MuiInputBase-root.Mui-disabled": {
            background: "#EBEBEB",
          },
          "& .MuiInputAdornment-positionEnd": {
            "& .MuiIconButton-root": {
              padding: "2px",
              color: "#426EB6",
            },
          },
        }}
      />
    </LocalizationProvider>
  );
};

export default React.memo(
  HrRenderer,
  (p: HrRendererProps, n: HrRendererProps) =>
    p.value.value === n.value.value && p.value.disabled === n.value.disabled
);
