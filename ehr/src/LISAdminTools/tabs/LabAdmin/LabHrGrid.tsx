import React from "react";
import Box from "@mui/material/Box";
import { intToWeekDay } from "../../utils/util";
import { DataGridPro } from "@mui/x-data-grid-pro";
import TextRenderer from "./renderer/TextRenderer";
import HrRenderer from "./renderer/HrRenderer";
import TFHrRenderer from "./renderer/TFHrRenderer";
import {
  GridRowId,
  GridRowParams,
  GridValueGetterParams,
} from "@mui/x-data-grid-pro";
import { LabFormAction } from "../../types/types";

interface LabHrGridRow {
  Day: number;
  Open: string | null;
  Close: string | null;
  TFH: boolean;
  id?: GridRowId; // Or specific type if known, often same as Day
}

interface LabHrGridProps {
  value?: LabHrGridRow[];
  disabled?: boolean;
  onChange: (action: LabFormAction) => void;
}

const LabHrGrid: React.FC<LabHrGridProps> = ({
  value = [],
  disabled = false,
  onChange,
}) => {
  const columnDefs: any[] = React.useMemo(
    () => [
      {
        field: "Day",
        width: 100,
        sortable: false,
        valueGetter: (params: GridValueGetterParams<LabHrGridRow>) => ({
          value: intToWeekDay(params.row.Day),
          style: { color: "#4B6EAF", fontWeight: "bold" },
        }),
        renderCell: (params: GridRowParams<LabHrGridRow>) => {
          return <TextRenderer {...(params as any)} />;
        },
      },
      {
        field: "Open",
        width: 100,
        sortable: false,
        renderCell: (params: GridRowParams<LabHrGridRow>) => {
          return <HrRenderer {...(params as any)} />;
        },
        cellClassName: "custom-padding-cell",
        cellRendererParams: {
          style: { width: 120, color: "gray" },
          onChange: (cValue: string, params: any) => {
            onChange({
              type: "UPDATE_HRS_ROW",
              fieldName: "Open",
              value: cValue,
              idx: params.api.getRowIndexRelativeToVisibleRows(params.id),
            });
          },
        },
        valueGetter(params: GridValueGetterParams<LabHrGridRow>) {
          return {
            value: params.row.Open,
            disabled: disabled || params.row.TFH,
          };
        },
      },
      {
        field: "Closed",
        width: 100,
        sortable: false,
        renderCell: (params: GridRowParams<LabHrGridRow>) => {
          return <HrRenderer {...(params as any)} />;
        },
        cellClassName: "custom-padding-cell",
        cellRendererParams: {
          style: { width: 120, color: "gray", padding: "0 4px" },
          onChange: (cValue: string, params: any) => {
            onChange({
              type: "UPDATE_HRS_ROW",
              fieldName: "Close",
              value: cValue,
              idx: params.api.getRowIndexRelativeToVisibleRows(params.id),
            });
          },
        },
        valueGetter(params: GridValueGetterParams<LabHrGridRow>) {
          return {
            value: params.row.Close,
            disabled: disabled || params.row.TFH,
          };
        },
      },
      {
        field: "TFH",
        headerName: "",
        sortable: false,
        width: 120,
        renderCell: (params: GridRowParams<LabHrGridRow>) => {
          return <TFHrRenderer {...(params as any)} />;
        },
        cellRendererParams: {
          onChange: (value: boolean, params: any) => {
            onChange({
              type: "UPDATE_HRS_ROW",
              fieldName: "TFH",
              value: value,
              idx: params.api.getRowIndexRelativeToVisibleRows(params.id),
            });
          },
        },
        valueGetter(params: GridValueGetterParams<LabHrGridRow>) {
          return {
            value: params.row.TFH,
            disabled: disabled,
          };
        },
      },
    ],
    [onChange, disabled]
  );

  return (
    <Box
      sx={{
        height: "100%",
        width: "422px",
      }}
    >
      <DataGridPro
        autoHeight
        rowReordering={false}
        columns={columnDefs}
        rows={value}
        getRowId={(row: LabHrGridRow) => row["Day"]}
        rowHeight={32}
        columnHeaderHeight={32}
        hideFooter
        disableColumnMenu
        sx={{
          "& .MuiInputBase-root": {
            height: "28px",
          },
          "& .custom-padding-cell": {
            padding: "2px 4px",
          },
        }}
        isRowSelectable={() => false}
        disableColumnResize={true}
        slotProps={{
          cell: {
            onContextMenu: () => {
              return false;
            },
          },
          noRowsOverlay: {
            style: {
              display: "none",
            },
          },
        }}
      />
    </Box>
  );
};

export default LabHrGrid;
