import { Button as <PERSON><PERSON><PERSON>utton, CciDialog } from "@cci/mui-components";
import LabLocationForm from "./LabLocationForm";
import { isFormChanged, isFormValid, isValidJson } from "../../utils/util";
import {
  initialLabForm,
  labFormAtom,
  labFormFieldsAtom,
} from "../../atoms/Lab";
import { useAtom, useAtomValue } from "jotai";
import { useEffect, useState } from "react";
import { updateLab } from "../../utils/DataApi";
import { useToastMethods } from "../../hooks/useToast";

interface LabLocationDialogProps {
  isCreate: boolean;
  open: boolean;
  onClose: () => void;
  fetchLabData: () => void;
  row?: any;
  labData: any[];
}

const LabLocationDialog = ({
  row,
  isCreate,
  open,
  onClose,
  fetchLabData,
  labData,
}: LabLocationDialogProps) => {
  const [labForm, setLabForm] = useAtom(labFormAtom);
  const labFormFields = useAtomValue(labFormFieldsAtom);
  const [backupLabForm, setBackupLabForm] = useState(null);
  const createToast: any = useToastMethods();

  useEffect(() => {
    if (open) {
      if (!isCreate && row) {
        const parsedHopRaw = isValidJson(row.HoPjson)
          ? JSON.parse(row.HoPjson)
          : [];
        const parsedHop = Array.isArray(parsedHopRaw) ? parsedHopRaw : [];
        const hop = initialLabForm.hop.map((defaultRow) => {
          const match = parsedHop.find(
            (r: any) => Number(r.Day) === defaultRow.Day
          );
          return {
            ...defaultRow,
            ...match,
            TFH: match?.TFH === true || match?.TFH === "true",
          };
        });

        const form = {
          ...row,
          hop,
          TFH: hop.every((r) => r.TFH),
        };

        setLabForm(form);
        setBackupLabForm(form);
      }
    } else {
      setLabForm(initialLabForm);
    }
  }, [open]);

  const onSave = () => {
    const formData = {
      ...(isCreate ? {} : { labid: labForm.id }),
      labname: labForm.lab,
      parentfacility: labForm.hospital,
      clia: labForm.clia,
      address: labForm.address,
      city: labForm.city,
      state: labForm.thestate,
      zipcode: labForm.zipcode,
      phonenum: labForm.phonenum,
      hopjson: JSON.stringify(labForm.hop),
    };

    updateLab(formData, {
      onSuccess: (response) => {
        fetchLabData();
        createToast.success(
          `Lab Location ${isCreate ? "created" : "updated"} successfully`
        );
      },
      onFailure: (error) => {
        createToast.error("Update failed");
      },
    });
  };
  const labLocationDialogContent = () => {
    return <LabLocationForm labData={labData}></LabLocationForm>;
  };
  const labLocationDialogButtons = () => {
    return (
      <>
        <CciButton color="secondary" onClick={onClose}>
          Cancel
        </CciButton>
        <CciButton
          color="primary"
          onClick={() => {
            onSave();
            onClose();
          }}
          disabled={
            !isFormValid(labFormFields, labForm) ||
            (!isCreate && !isFormChanged(backupLabForm, labForm))
          }
        >
          Save
        </CciButton>
      </>
    );
  };
  return (
    <>
      <CciDialog
        title={`${isCreate ? "Create" : "Edit"} Lab Location`}
        open={open}
        setOpen={onClose}
        handleClose={onClose}
        contentStyle={{
          padding: "16px 72px",
          height: "100%",
          width: "100%",
          overflow: "auto",
        }}
        content={labLocationDialogContent()}
        buttonStyle={{
          display: "flex",
          gap: 1,
        }}
        buttons={labLocationDialogButtons()}
        sx={{
          "& .MuiDialogTitle-root": {
            padding: "16px 24px",
          },
          "& .MuiDialogActions-root": {
            padding: "16px",
          },
        }}
      ></CciDialog>
    </>
  );
};

export default LabLocationDialog;
