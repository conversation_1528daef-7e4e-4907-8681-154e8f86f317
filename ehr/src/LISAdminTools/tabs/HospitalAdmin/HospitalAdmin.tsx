import React, { useState, useEffect, useMemo } from "react";
import { Grid, Stack } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid-pro";
import MainTable from "../../common/MainTable";
import EditIcon from "@mui/icons-material/Edit";
import AdminToolbar from "@cci-monorepo/common/mui-components/src/components/layout/AdminToolbar/AdminToolbar";
import { useToastMethods } from "../../hooks/useToast";
import { GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { HospitalDialog } from "./HospitalDialog";
import { getLisHospitals, getLisLabs } from "../../utils/DataApi";
import { debounce } from "lodash";
import { updateHospitalFormAtom } from "../../atoms/Hospital";
import { useAtom } from "jotai";

interface Hospital {
  id: number;
  hospital: string;
  deflab: string;
}

const HospitalAdmin = () => {
  const createToast = useToastMethods();
  const [lookup, setLookup] = useState("");
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [hospitalData, setHospitalData] = useState<Hospital[]>([]);
  const [loading, setLoading] = useState(false);
  const [editDialog, setEditDialog] = React.useState<any>({ open: false });
  const [, updateHospitalForm] = useAtom(updateHospitalFormAtom);

  useEffect(() => {
    fetchHospitalData();
    getLabOptions();
  }, []);

  const fetchHospitalData = () => {
    setLoading(true);
    getLisHospitals({
      onSuccess: (response) => {
        if (response.data?.success) {
          setHospitalData(response.data.data || []);
        } else {
          createToast.error(
            response.data?.errmsg || "Failed to fetch hospital data"
          );
        }
      },
      onFailure: (error) => {
        createToast.error("Failed to fetch hospital data");
      },
    }).finally(() => {
      const debouncedSetLoading = debounce(() => setLoading(false), 150);
      debouncedSetLoading();
    });
  };

  const getLabOptions = () => {
    getLisLabs({
      onSuccess: (response) => {
        if (response.data?.success) {
          const result = response.data.data || [];
          const labs = Array.from(new Set(result.map((item: any) => item.lab)));
          updateHospitalForm({
            type: "UPDATE_OPTIONS",
            fieldName: "deflab",
            options: labs,
          });
        } else {
          createToast.error(
            response.data?.errmsg || "Failed to fetch lab data"
          );
        }
      },
      onFailure: (error) => {
        createToast.error("Failed to fetch lab data");
      },
    });
  };

  const filteredData = useMemo(() => {
    return lookup
      ? hospitalData.filter((hospital) =>
          hospital.hospital.toLowerCase().includes(lookup.toLowerCase())
        )
      : hospitalData;
  }, [lookup, hospitalData]);

  const getColDefs = (): GridColDef[] => [
    {
      field: "actions",
      headerName: "Edit",
      width: 50,
      sortable: false,
      align: "center",
      renderCell: (params: any) => (
        <EditIcon
          sx={{ color: "#4a5eac", fontSize: 24, cursor: "pointer" }}
          onClick={() => {
            setEditDialog({ row: params.row, open: true });
          }}
        />
      ),
    },
    {
      field: "hospital",
      headerName: "Hospital",
      width: 400,
    },
    {
      field: "deflab",
      headerName: "Default Lab",
      flex: 1,
    },
  ];

  return (
    <Stack>
      <Grid container sx={{ padding: "30px" }}>
        <Grid item xs={12} sx={{ paddingBottom: "30px" }}>
          <AdminToolbar
            searchValue={lookup}
            onSearchChange={setLookup}
            searchPlaceholder="Search Hospital"
            showInactiveToggle={false}
          />
        </Grid>
        <Grid item xs={12}>
          <MainTable
            checkboxSelection={false}
            columnDefs={getColDefs()}
            disableColumnMenu
            data={filteredData}
            selectedRows={selectedRows}
            onSelectionChange={setSelectedRows}
            loading={loading}
            overlayNoRowsTemplate={
              <span>
                <b>
                  No Hospitals{" "}
                  {lookup && hospitalData.length > 0 ? "Found" : "Available"}
                </b>
              </span>
            }
          />
        </Grid>
      </Grid>
      <HospitalDialog
        {...editDialog}
        fetchHospitalData={fetchHospitalData}
        onClose={() => setEditDialog({ open: false })}
      />
    </Stack>
  );
};

export default HospitalAdmin;
