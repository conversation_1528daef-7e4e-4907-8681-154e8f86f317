import { Button as <PERSON><PERSON><PERSON><PERSON>on, CciDialog } from "@cci/mui-components";
import HospitalForm from "./HospitalForm";
import { useEffect } from "react";
import { hospitalFormAtom, hospitalFormFieldsAtom } from "../../atoms/Hospital";
import { useAtom } from "jotai";
import { isFormChanged, isFormValid } from "../../utils/util";
import { updateHospital } from "../../utils/DataApi";
import { useToastMethods } from "../../hooks/useToast";

interface HospitalDialogProps {
  row: any;
  open: boolean;
  onClose: () => void;
  fetchHospitalData: () => void;
}
export const HospitalDialog = ({
  row,
  open,
  onClose,
  fetchHospitalData,
}: HospitalDialogProps) => {
  const createToast = useToastMethods();
  const [hospitalForm, setHospitalForm] = useAtom(hospitalFormAtom);
  const [hospitalFormFields] = useAtom(hospitalFormFieldsAtom);
  useEffect(() => {
    if (open) {
      setHospitalForm(row);
    } else {
      setHospitalForm({});
    }
  }, [open, row]);
  const HospitalDialogContent = () => {
    return (
      <>
        <HospitalForm></HospitalForm>
      </>
    );
  };

  const onSave = () => {
    const formData = {
      hospitalid: hospitalForm.id,
      defaultlab: hospitalForm.deflab,
    };
    updateHospital(formData, {
      onSuccess: (response) => {
        fetchHospitalData();
        createToast.success("Hospital updated successfully");
      },
      onFailure: (error) => {
        createToast.error("Update failed");
      },
    });
  };
  const HospitalDialogButtons = () => {
    return (
      <>
        <CciButton color="secondary" onClick={onClose}>
          Cancel
        </CciButton>
        <CciButton
          color="primary"
          disabled={
            !isFormValid(hospitalFormFields, hospitalForm) ||
            !isFormChanged(row, hospitalForm)
          }
          onClick={() => {
            onSave();
            onClose();
          }}
        >
          Save
        </CciButton>
      </>
    );
  };
  return (
    <>
      <CciDialog
        title="Edit Hospital"
        open={open}
        setOpen={onClose}
        handleClose={onClose}
        contentStyle={{
          padding: "16px 32px",
          height: "100%",
          width: "100%",
          overflow: "auto",
        }}
        content={HospitalDialogContent()}
        buttonStyle={{
          display: "flex",
          gap: 1,
        }}
        buttons={HospitalDialogButtons()}
        sx={{
          "& .MuiDialogTitle-root": {
            padding: "16px 24px",
          },
          "& .MuiDialogActions-root": {
            padding: "16px",
          },
        }}
      ></CciDialog>
    </>
  );
};
