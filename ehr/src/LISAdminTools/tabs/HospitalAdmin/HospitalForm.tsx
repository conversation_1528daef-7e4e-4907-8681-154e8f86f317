import { useAtom } from "jotai";
import {
  hospital<PERSON>orm<PERSON>tom,
  hospitalFormFieldsAtom,
  updateHospitalFormAtom,
} from "../../atoms/Hospital";
import FormFieldRenderer, { FormField } from "../../common/FormField";
import { Box } from "@mui/material";

const HospitalForm = () => {
  const [hospitalForm] = useAtom(hospitalFormAtom);
  const [hospitalFormFields] = useAtom(hospitalFormFieldsAtom);
  const [, updateHospitalForm] = useAtom(updateHospitalFormAtom);
  const handleChange = (key: string, value: any) => {
    updateHospitalForm({ type: "SET_FIELD", fieldName: key, value });
  };
  return (
    <>
      {hospitalFormFields.map((field: any) => (
        <Box margin="16px 0" width="400px" key={field.key}>
          <FormFieldRenderer
            field={field}
            value={hospitalForm[field.key]}
            onChange={(data) => handleChange(field.key, data)}
            labelPosition="left"
            labelStyle={{ width: "120px" }}
            disabled={field.disabled}
          />
        </Box>
      ))}
    </>
  );
};
export default HospitalForm;
