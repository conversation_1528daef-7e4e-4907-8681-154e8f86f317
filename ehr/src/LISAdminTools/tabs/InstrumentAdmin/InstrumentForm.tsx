import { useAtom, useAtomValue } from "jotai";
import {
  instrumentForm<PERSON>tom,
  instrumentFormFieldsAtom,
  instrumentOptionsMapAtom,
  updateInstrumentFormAtom,
} from "../../atoms/Instrument";
import FormFieldRenderer from "../../common/FormField";
import { Box } from "@mui/material";
import { useEffect } from "react";

const InstrumentForm = () => {
  const instrumentOptionsMap = useAtomValue(instrumentOptionsMapAtom);
  const [instrumentForm] = useAtom(instrumentFormAtom);
  const [instrumentFormFields] = useAtom(instrumentFormFieldsAtom);
  const [, updateInstrumentForm] = useAtom(updateInstrumentFormAtom);

  useEffect(() => {
    const hospital = instrumentForm.hospital;
    const labs = hospital
      ? Object.keys(instrumentOptionsMap[hospital] || {})
      : [];
    updateInstrumentForm({
      type: "UPDATE_OPTIONS",
      fieldName: "lab",
      options: labs,
    });
  }, [instrumentForm.hospital]);

  useEffect(() => {
    const { hospital, lab } = instrumentForm;
    const depts =
      hospital && lab
        ? Object.keys(instrumentOptionsMap[hospital]?.[lab] || {})
        : [];
    updateInstrumentForm({
      type: "UPDATE_OPTIONS",
      fieldName: "department",
      options: depts,
    });
  }, [instrumentForm.lab]);

  const handleChange = (key: string, value: any) => {
    updateInstrumentForm({ type: "SET_FIELD", fieldName: key, value });
  };

  return (
    <>
      {instrumentFormFields.map((field: any) => {
        const disabled =
          (field.key === "lab" && !instrumentForm.hospital) ||
          (field.key === "department" &&
            (!instrumentForm.hospital || !instrumentForm.lab));
        return (
          <Box margin="16px 0" width="560px" key={field.key}>
            <FormFieldRenderer
              field={field}
              value={instrumentForm[field.key]}
              onChange={(data) => handleChange(field.key, data)}
              labelPosition="left"
              labelStyle={{ width: "260px" }}
              disabled={disabled}
            />
          </Box>
        );
      })}
    </>
  );
};
export default InstrumentForm;
