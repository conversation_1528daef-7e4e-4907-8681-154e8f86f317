import { Button as <PERSON><PERSON><PERSON>utton, CciDialog } from "@cci/mui-components";
import InstrumentForm from "./InstrumentForm";
import { Typography } from "@mui/material";
import {
  instrumentFormAtom,
  instrumentFormFieldsAtom,
} from "../../atoms/Instrument";
import { useEffect } from "react";
import { useAtom, useAtomValue } from "jotai";
import { isFormChanged, isFormValid } from "../../utils/util";
import { updateInstrumentBench } from "../../utils/DataApi";
import { useToastMethods } from "../../hooks/useToast";

interface InstrumentDialogProps {
  isCreate: boolean;
  open: boolean;
  onClose: () => void;
  fetchInstrumentData: () => void;
  row?: any;
}
export const InstrumentDialog = ({
  isCreate,
  open,
  row,
  onClose,
  fetchInstrumentData,
}: InstrumentDialogProps) => {
  const [instrumentForm, setInstrumentForm] = useAtom(instrumentFormAtom);
  const instrumentFormFields = useAtomValue(instrumentFormFieldsAtom);
  const createToast: any = useToastMethods();

  useEffect(() => {
    if (open) {
      if (!isCreate && row) setInstrumentForm(row);
    } else {
      setInstrumentForm({});
    }
  }, [open, row]);

  const onSave = () => {
    const formData = {
      ...(isCreate ? {} : { instrmtid: instrumentForm.id }),
      instrmtname: instrumentForm.description,
      hospital: instrumentForm.hospital,
      labname: instrumentForm.lab,
      dpmtname: instrumentForm.department,
      type: instrumentForm.thetype,
    };

    updateInstrumentBench(formData, {
      onSuccess: (response) => {
        fetchInstrumentData();
        createToast.success(
          `Instrument/Bench ${isCreate ? "created" : "updated"} successfully`
        );
      },
      onFailure: (error) => {
        createToast.error("Update failed");
      },
    });
  };

  const InstrumentDialogContent = () => {
    return (
      <>
        {isCreate && (
          <Typography sx={{ font: "normal 400 15px Roboto" }}>
            To <b>Create</b> an instrument/bench, specify the following:
          </Typography>
        )}
        <InstrumentForm></InstrumentForm>
      </>
    );
  };
  const InstrumentDialogButtons = () => {
    return (
      <>
        <CciButton color="secondary" onClick={onClose}>
          Cancel
        </CciButton>
        <CciButton
          color="primary"
          disabled={
            !isFormValid(instrumentFormFields, instrumentForm) ||
            (!isCreate && !isFormChanged(row, instrumentForm))
          }
          onClick={() => {
            onSave();
            onClose();
          }}
        >
          Save
        </CciButton>
      </>
    );
  };
  return (
    <CciDialog
      title={`${isCreate ? "Create" : "Edit"} Instrument/Bench`}
      open={open}
      setOpen={onClose}
      handleClose={onClose}
      contentStyle={{
        padding: "16px 32px",
        height: "100%",
        width: "100%",
        overflow: "auto",
      }}
      content={InstrumentDialogContent()}
      buttonStyle={{
        display: "flex",
        gap: 1,
      }}
      buttons={InstrumentDialogButtons()}
      sx={{
        "& .MuiDialogTitle-root": {
          padding: "16px 24px",
        },
        "& .MuiDialogActions-root": {
          padding: "16px",
        },
      }}
    ></CciDialog>
  );
};
