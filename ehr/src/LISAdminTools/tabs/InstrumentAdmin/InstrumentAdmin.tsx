import { useState, useEffect, useMemo } from "react";
import { Grid, Stack } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid-pro";
import MainTable from "../../common/MainTable";
import EditIcon from "@mui/icons-material/Edit";
import AdminToolbar, {
  ToolbarItemConfig,
} from "@cci-monorepo/common/mui-components/src/components/layout/AdminToolbar/AdminToolbar";
import { useToastMethods } from "../../hooks/useToast";
import { useMsgDialogMethods } from "../../hooks/useMsgDialog";
import { GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { InstrumentDialog } from "./InstrumentDialog";
import {
  getLisDepartments,
  getLisInstruBench,
  activateOrInactivate,
} from "../../utils/DataApi";
import { debounce } from "lodash";
import { parseFormDataToTree } from "../../utils/util";
import {
  instrumentOptionsMapAtom,
  updateInstrumentFormAtom,
} from "../../atoms/Instrument";
import { useSetAtom } from "jotai";
import AddIcon from "@mui/icons-material/Add";

export interface Instrument {
  id: number;
  description: string;
  hospital: string;
  lab: string;
  department: string;
  thetype: string;
  active: "0" | "1";
}

const InstrumentAdmin = () => {
  const createToast = useToastMethods();
  const createMsgDialog = useMsgDialogMethods();
  const [lookup, setLookup] = useState("");
  const [includeInactive, setIncludeInactive] = useState(false);
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [instrumentData, setInstrumentData] = useState<Instrument[]>([]);
  const [editDialog, setEditDialog] = useState<any>({
    open: false,
    isCreate: false,
  });
  const [loading, setLoading] = useState(false);
  const setInstrumentOptionsMap = useSetAtom(instrumentOptionsMapAtom);
  const updateInstrumentForm = useSetAtom(updateInstrumentFormAtom);

  useEffect(() => {
    fetchInstrumentData();
    getInstrumentOptionsMap();
  }, []);

  const fetchInstrumentData = () => {
    setLoading(true);
    getLisInstruBench({
      onSuccess: (response) => {
        if (response.data?.success) {
          setInstrumentData(response.data.data || []);
        } else {
          createToast.error(
            response.data?.errmsg || "Failed to fetch instrument data"
          );
        }
      },
      onFailure: (error) => {
        createToast.error("Failed to fetch instrument data");
      },
    }).finally(() => {
      const debouncedSetLoading = debounce(() => setLoading(false), 150);
      debouncedSetLoading();
    });
  };

  const getInstrumentOptionsMap = () => {
    getLisDepartments({
      onSuccess: (response) => {
        if (response.data?.success) {
          const parsedTree = parseFormDataToTree(response.data.data);
          setInstrumentOptionsMap(parsedTree);
          const hospitals = Object.keys(parsedTree);
          updateInstrumentForm({
            type: "UPDATE_OPTIONS",
            fieldName: "hospital",
            options: hospitals,
          });
        }
      },
    });
  };

  const filteredData = useMemo(() => {
    let filtered = lookup
      ? instrumentData.filter((instrument) =>
          instrument.description.toLowerCase().includes(lookup.toLowerCase())
        )
      : instrumentData;

    if (!includeInactive) {
      filtered = filtered.filter((instrument) => instrument.active === "1");
    }

    return filtered;
  }, [lookup, instrumentData, includeInactive]);

  const SelectedItems = () => {
    return (
      <ul
        style={{
          color: "#000",
          fontSize: "16px",
          fontWeight: "700",
          listStyleType: "none",
          padding: "0",
          maxHeight: "500px",
          overflow: "auto",
        }}
      >
        {selectedRows
          .map((id) => filteredData.find((item) => item.id === id))
          .filter((item): item is Instrument => !!item)
          .map((item) => (
            <li key={item.id}>{item.description}</li>
          ))}
      </ul>
    );
  };

  const getColDefs = (): GridColDef[] => [
    {
      field: "actions",
      headerName: "Edit",
      width: 50,
      sortable: false,
      align: "center",
      renderCell: (params) => (
        <EditIcon
          sx={{ color: "#4a5eac", fontSize: 24, cursor: "pointer" }}
          onClick={() => setEditDialog({ row: params.row, open: true })}
        />
      ),
    },
    {
      field: "description",
      headerName: "Instrument/Bench",
      width: 300,
    },
    {
      field: "thetype",
      headerName: "Type",
      width: 200,
    },
    {
      field: "hospital",
      headerName: "Hospital",
      width: 300,
    },
    {
      field: "lab",
      headerName: "Lab",
      width: 300,
    },
    {
      field: "department",
      headerName: "Department",
      flex: 1,
    },
    {
      field: "active",
      headerName: "Status",
      width: 200,
      renderCell: (params: any) => {
        const isActive = params.row.active === "1";
        return isActive ? "Active" : "Inactive";
      },
    },
  ];

  const handleActivationChange = (activate: boolean) => {
    const action = activate ? "Activate" : "Inactivate";
    const value = activate ? "1" : "0";

    createMsgDialog.confirm({
      title: action,
      type: "Question",
      text: (
        <div
          style={{
            color: "#000",
            fontSize: "16px",
            fontWeight: "400",
            maxWidth: "450px",
          }}
        >
          Are you sure you want to {action.toLowerCase()} the following items?
          This change may affect other lab hierarchy configurations.
          <SelectedItems />
        </div>
      ),
      confirmLabel: action,
      onConfirm: () => {
        activateOrInactivate(
          {
            ids: selectedRows.join(","),
            type: "instrumentbench",
            value,
          },
          {
            onSuccess: () => {
              createToast.success(
                `Instrument/Bench ${action.toLowerCase()}d successfully`
              );
              fetchInstrumentData();
              setSelectedRows([]);
            },
            onFailure: () => {
              createToast.error(
                `Failed to ${action.toLowerCase()} instrument/bench`
              );
            },
          }
        );
      },
    });
  };

  const buttons: ToolbarItemConfig[] = [
    {
      label: "Inactivate",
      color: "secondary",
      disabled:
        selectedRows.length === 0 ||
        !selectedRows.some((id) => {
          const row = filteredData.find((item) => item.id === id);
          return row?.active === "1";
        }),
      onClick: () => handleActivationChange(false),
    },
    {
      label: "Activate",
      color: "secondary",
      disabled:
        selectedRows.length === 0 ||
        !selectedRows.some((id) => {
          const row = filteredData.find((item) => item.id === id);
          return row?.active === "0";
        }),
      onClick: () => handleActivationChange(true),
    },
    {
      label: "Create Instrument/Bench",
      startIcon: <AddIcon />,
      onClick: () => {
        setEditDialog({ open: true, isCreate: true });
      },
    },
  ];

  return (
    <Stack>
      <Grid container sx={{ padding: "30px" }}>
        <Grid item xs={12} sx={{ paddingBottom: "30px" }}>
          <AdminToolbar
            searchValue={lookup}
            onSearchChange={setLookup}
            searchPlaceholder="Search Instrument/Benches"
            includeInactive={includeInactive}
            onInactiveToggle={() => setIncludeInactive(!includeInactive)}
            items={buttons}
          />
        </Grid>
        <Grid item xs={12}>
          <MainTable
            columnDefs={getColDefs()}
            disableColumnMenu
            data={filteredData}
            selectedRows={selectedRows}
            onSelectionChange={setSelectedRows}
            loading={loading}
            columnVisibilityModel={{
              active: includeInactive,
            }}
            getRowClassName={(params) =>
              params.row.active === "0" ? "inactive-row" : ""
            }
            overlayNoRowsTemplate={
              <span>
                <b>
                  No Instruments/Benches{" "}
                  {lookup && instrumentData.length > 0 ? "Found" : "Available"}
                </b>
              </span>
            }
          />
        </Grid>
      </Grid>
      <InstrumentDialog
        {...editDialog}
        fetchInstrumentData={fetchInstrumentData}
        onClose={() => setEditDialog({ open: false })}
      />
    </Stack>
  );
};

export default InstrumentAdmin;
