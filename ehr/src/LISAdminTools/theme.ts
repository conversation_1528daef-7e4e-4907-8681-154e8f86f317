import { createTheme } from "@mui/material/styles";
import type {} from "@mui/x-data-grid/themeAugmentation";

export const theme = createTheme({
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          fontFamily: "Roboto",
          fontSize: "14px",
          fontStyle: "normal",
          fontWeight: "700",
          lineHeight: "normal",
        },
      },
    },

    MuiDivider: {
      styleOverrides: {
        vertical: {
          backgroundColor: "#B9B9B9",
          color: "#B9B9B9",
          width: "2px",
          height: "18px",
          margin: "8px 0 0 8px",
        },
      },
    },

    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 50,
          height: 24,
          padding: 0,
        },
        switchBase: {
          padding: "3px",
          "&.Mui-checked": {
            transform: "translateX(26px)",
            color: "#fff",
            "& + .MuiSwitch-track": {
              backgroundColor: "#319436",
              opacity: 1,
              border: 0,
            },
          },
          "&.Mui-disabled + .MuiSwitch-track": {
            opacity: 0.5,
          },
        },
        thumb: {
          boxShadow: "none",
          width: 18,
          height: 18,
          borderRadius: 9,
          "&::before": {
            content: '""',
            position: "absolute",
          },
        },
        track: {
          borderRadius: 24 / 2,
          backgroundColor: "#E9E9EA",
          opacity: 1,
          transition: "background-color 300ms",
        },
      },
    },

    MuiTypography: {
      styleOverrides: {
        h6: {
          color: "#000",
          fontFamily: "Roboto",
          fontSize: "14px",
          fontWeight: "500",
          lineHeight: "normal",
        },
      },
    },

    MuiOutlinedInput: {
      styleOverrides: {
        input: {
          padding: "6px 0px",
          width: "100%",
        },
      },
    },

    MuiDataGrid: {
      defaultProps: {
        columnHeaderHeight: 32,
        rowHeight: 32,
        hideFooter: true,
      },
      styleOverrides: {
        root: {
          borderRadius: 0,
          userSelect: "none",
          border: "1px solid #bdc3c7",
          font: "15px/30px Helvetica",
          cursor: "default",
          MozUserSelect: "none",
          msUserSelect: "none",
          WebkitUserSelect: "none",
          KhtmlUserSelect: "none",
          "& .MuiDataGrid-cell:focus, & .MuiDataGrid-columnHeader:focus": {
            outline: "none",
          },
          "& .MuiCheckbox-root": {
            "&.Mui-checked": {
              color: "#319436",
            },
          },
        },
        columnHeaders: {
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          backgroundColor: "#d6dbe3",
          color: "#000",
          fontFamily: "Helvetica",
          fontSize: "13px",
          fontStyle: "normal",
          fontWeight: "700",
        },
        columnHeader: {
          borderRight: "1px solid #cbcaca !important",
        },
        columnHeaderTitleContainer: {
          justifyContent: "space-between",
        },
        columnHeaderTitle: {
          fontWeight: "bold",
        },
        cell: {
          color: "#000",
          fontFamily: "Helvetica",
          fontSize: "15px",
          fontStyle: "normal",
          fontWeight: "400",
          display: "inline-flex",
          width: "100%",
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          borderRight: "1px solid #CBCACA",
          "&:last-child": {
            borderRight: "none",
          },
          "&:focus-within": {
            outline: "none",
          },
          "& .MuiDataGrid-rowReorderCell--draggable": {
            color: "#6d6d6d",
            "& .MuiSvgIcon-root": {
              width: "18px",
              height: "18px",
            },
          },
        },
        row: {
          backgroundColor: "#FFFFFF",
          color: "#000000",
          borderBottomStyle: "solid",
          borderColor: "#D7D9DD",
          borderWidth: "1px",
          "&:hover, &.Mui-hovered": {
            backgroundColor: "#F7EEC3 !important",
          },
          "&.MuiDataGrid-row.inactive-row": {
            "& .MuiDataGrid-cell": {
              fontStyle: "italic !important",
            },
          },
          "&:nth-of-type(odd)": {
            backgroundColor: "#FFFFFF",
          },
          "&:nth-of-type(even)": {
            backgroundColor: "#F8F9FA",
          },
        },
        overlayWrapperInner: {
          backgroundColor: "#FFFFFF",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
        },
      },
    },
  },
});
