import { StatusIconProps } from "@cci-monorepo/common/mui-components/src/components/icons/StatusIcon";

export type ToastVariants = "success" | "warning" | "error" | "info";

export type TToast = {
  id: string;
  variant: ToastVariants;
  message: string;
  open: boolean;
  handleClose?: () => void;
  onClose?: () => void;
  [key: string]: any;
};

export type TToastContext = {
  variant: ToastVariants;
  msg: string;
  [key: string]: any;
};

// Button variant types
export type ButtonVariant = "text" | "outlined" | "contained";

// Button color types
export type ButtonColor =
  | "primary"
  | "secondary"
  | "error"
  | "warning"
  | "info"
  | "success";

export interface DialogButton {
  label: string;
  onClick: () => void;
  variant?: ButtonVariant;
  color?: ButtonColor;
  disabled?: boolean;
  autoFocus?: boolean;
}

export interface MsgDialogConfig {
  title?: string;
  text: React.ReactNode | React.ReactNode[];
  buttons?: DialogButton[];
  type?: StatusIconProps["type"];
  onClose?: () => void;
}

export interface DialogState extends MsgDialogConfig {
  id: string;
  open: boolean;
}

// Base dialog options interface
export interface BaseDialogOptions {
  title?: string;
  text: React.ReactNode;
  type?: StatusIconProps["type"];
}

// Confirm dialog options interface
export interface ConfirmDialogOptions extends BaseDialogOptions {
  onConfirm: () => void;
  onCancel?: () => void;
  confirmLabel?: string;
  cancelLabel?: string;
}

// Alert dialog options interface
export interface AlertDialogOptions extends BaseDialogOptions {
  onOk?: () => void;
  okLabel?: string;
}

// Dialog methods interface
export interface MsgDialogMethods {
  show: (config: MsgDialogConfig) => string;
  confirm: (options: ConfirmDialogOptions) => string;
  alert: (options: AlertDialogOptions) => string;
}

// Dialog manager interface
export interface DialogManagerInterface {
  init(): void;
  destroy(): void;
}

// Dialog type enumeration
export type DialogType = "confirm" | "alert" | "custom";

export type FormAction =
  | { type: "SET_FIELD"; fieldName: string; value: any }
  | { type: "UPDATE_OPTIONS"; fieldName: string; options: any[] };

export type LabFormAction =
  | FormAction
  | { type: "UPDATE_HRS_ROW"; fieldName: string; value: any; idx: number };
