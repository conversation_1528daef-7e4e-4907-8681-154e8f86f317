import { atom } from "jotai";
import { typeMyView, typeProviderGroup, typeUnit } from "../util/CareTeamUtil";

export const drawerExpandedAtom = atom(true);

export const flipDrawerAtom = atom(null, (get, set) => {
  set(drawerExpandedAtom, !get(drawerExpandedAtom));
});

export const expandDrawerAtom = atom(null, (get, set) => {
  set(drawerExpandedAtom, true);
});

// Selected unit
export const selectedUnitIdAtom = atom<string>("");
export const setSelectedUnitAtom = atom(null, (get, set, unit: string) => {
  set(selectedUnitIdAtom, unit);
  set(searchTextAtom, "");
  const myViewList = get(myViewListAtom);
  if (myViewList && myViewList.length > 0) {
    for (let i in myViewList) {
      let listItemData = JSON.parse(JSON.stringify(myViewList[i]));
      if (listItemData.name.split("(")[0] === unit) {
        set(selectedUnitDataAtom, listItemData);
        cci.cfg.careteamselected = listItemData;
        set(selectedTitleNumAtom, "Patients(" + listItemData.number + ")");
        return;
      }
    }
  }
  const groupList = get(groupListAtom);
  if (groupList && groupList.length > 0) {
    for (let i in groupList) {
      let listItemData = JSON.parse(JSON.stringify(groupList[i]));
      if (listItemData.name === unit) {
        set(selectedUnitDataAtom, listItemData);
        cci.cfg.careteamselected = listItemData;
        set(selectedTitleNumAtom, "Providers(" + listItemData.number + ")");
        return;
      }
    }
  }
  const unitList = get(unitListAtom);
  if (unitList && unitList.length > 0) {
    for (let i in unitList) {
      let listItemData = JSON.parse(JSON.stringify(unitList[i]));
      if (listItemData.name.split("(")[0] === unit) {
        set(selectedUnitDataAtom, listItemData);
        cci.cfg.careteamselected = listItemData;
        set(selectedTitleNumAtom, "Patients(" + listItemData.number + ")");
        return;
      }
    }
  }
});

export const selectedUnitDataAtom = atom<any>(null);
export const setSelectedUnitDataAtom = atom(null, (get, set, data: any) => {
  set(selectedUnitDataAtom, data);
  cci.cfg.careteamselected = data;
});

export const selectedTitleNumAtom = atom<any>("");
export const setSelectedTitleNumAtom = atom(null, (get, set, data: any) => {
  set(selectedTitleNumAtom, data);
});

export const selectedTitleTypeAtom = atom<any>("");
export const setSelectedTitleTypeAtom = atom(null, (get, set, data: any) => {
  set(selectedTitleTypeAtom, data);
});

export const myViewListAtom = atom<any>([]);
export const setMyViewListAtom = atom(null, (get, set, data: any) => {
  let ret = [];
  for (var i in data) {
    ret.push({
      type: typeMyView,
      name: data[i].disp,
      number: data[i].patNum,
      id: "0",
    });
  }
  set(myViewListAtom, ret);
});

export const groupListAtom = atom<any>([]);
export const setGroupListAtom = atom(null, (get, set, data: any) => {
  let ret = [];
  for (var i in data) {
    ret.push({
      type: typeProviderGroup,
      name: data[i].name,
      number: data[i].staffnum,
      id: "0",
    });
  }
  set(groupListAtom, ret);
});

export const unitListAtom = atom<any>([]);
export const setUnitListAtom = atom(null, (get, set, data: any) => {
  let ret = [];
  for (var i in data) {
    ret.push({
      type: typeUnit,
      name: data[i].disp,
      number: data[i].patnum,
      id: data[i].unitnum,
      clinic: data[i].unitname,
    });
  }
  set(unitListAtom, ret);
});

export const patientListAtom = atom<any>([]);
export const setPatientListAtom = atom(null, (get, set, data: any) => {
  set(patientListAtom, data);
});

export const providerListAtom = atom<any>([]);
export const setProviderListAtom = atom(null, (get, set, data: any) => {
  set(providerListAtom, data);
});

export const unitPatientListAtom = atom<any>([]);
export const setUnitPatientListAtom = atom(null, (get, set, data: any) => {
  set(unitPatientListAtom, data);
});

export const loadingAtom = atom<boolean>(true);
export const setLoadingAtom = atom(null, (get, set, loading: boolean) => {
  set(loadingAtom, loading);
});

export const listLoadingAtom = atom<boolean>(false);
export const setListLoadingAtom = atom(null, (get, set, loading: boolean) => {
  set(listLoadingAtom, loading);
});

export const providerRowCountAtom = atom<number>(0);
export const setProviderRowCountAtom = atom(null, (get, set, count: number) => {
  set(providerRowCountAtom, count);
});

export const emptyAtom = atom<boolean>(false);
export const setEmptyAtom = atom(null, (get, set, loading: boolean) => {
  set(emptyAtom, loading);
});

export const careTeamErrorAtom = atom({
  show: false,
  title: "",
  content: "",
});

export const hideCareTeamErrorAtom = atom(null, (get, set) => {
  let oldValue = get(careTeamErrorAtom);
  set(careTeamErrorAtom, { ...oldValue, show: false });
});

export const showCareTeamErrorAtom = atom(null, (get, set, content: string) => {
  set(careTeamErrorAtom, { show: true, title: "Alert", content: content });
});

export const searchTextAtom = atom<any>("");
export const setSearchTextAtom = atom(null, (get, set, data: any) => {
  set(searchTextAtom, data);
});

export const searchListAtom = atom<any>([]);
export const setSearchListAtom = atom(null, (get, set, data: any) => {
  set(searchListAtom, data);
});

export const showSearchAtom = atom<boolean>(false);
export const setShowSearchAtom = atom(null, (get, set, search: boolean) => {
  set(showSearchAtom, search);
});

export const refreshCurrentAtom = atom<boolean>(false);
export const setRefreshCurrentAtom = atom(
  null,
  (get, set, refresh: boolean) => {
    set(refreshCurrentAtom, refresh);
    if (refresh) {
      let selectedUnitData = get(selectedUnitDataAtom);
      if (selectedUnitData.type === typeMyView) {
        set(refreshPatAtom, true);
      } else if (selectedUnitData.type === typeProviderGroup) {
        set(refreshProviderPatAtom, true);
      } else {
        set(refreshUnitPatAtom, true);
      }
    }
  }
);

export const refreshPatAtom = atom<boolean>(false);
export const setRefreshPatAtom = atom(null, (get, set, refresh: boolean) => {
  set(refreshPatAtom, refresh);
});

export const refreshProviderPatAtom = atom<boolean>(false);
export const setRefreshProviderPatAtom = atom(
  null,
  (get, set, refresh: boolean) => {
    set(refreshProviderPatAtom, refresh);
  }
);

export const refreshUnitPatAtom = atom<boolean>(false);
export const setRefreshUnitPatAtom = atom(
  null,
  (get, set, refresh: boolean) => {
    set(refreshUnitPatAtom, refresh);
  }
);

export const selectedRowsAtom = atom<any[]>([]);

export const setSelectedRowsAtom = atom(null, (get, set, rows: any[]) => {
  set(selectedRowsAtom, rows);
});

export const openAddSelfDialogAtom = atom<boolean>(false);
export const setOpenAddSelfDialogAtom = atom(
  null,
  (get, set, open: boolean) => {
    set(openAddSelfDialogAtom, open);
  }
);

export const openAddStaffDialogAtom = atom<boolean>(false);
export const setOpenAddStaffDialogAtom = atom(
  null,
  (get, set, open: boolean) => {
    set(openAddStaffDialogAtom, open);
  }
);

export const openRemoveStaffDialogAtom = atom<boolean>(false);
export const setOpenRemoveStaffDialogAtom = atom(
  null,
  (get, set, open: boolean) => {
    set(openRemoveStaffDialogAtom, open);
  }
);

export const addStaffParamsAtom = atom<any>({});
export const setAddStaffParamsAtom = atom(null, (get, set, params: any) => {
  set(addStaffParamsAtom, params);
});

export const showSuccessAlertAtom = atom<boolean>(false);
export const setShowSuccessAlertAtom = atom(null, (get, set, show: boolean) => {
  set(showSuccessAlertAtom, show);
});

export const successInfoAtom = atom<string>("");
export const setSuccessInfoAtom = atom(null, (get, set, message: string) => {
  set(successInfoAtom, message);
});

export const onlineAtom = atom<boolean>(false);
export const setOnlineAtom = atom(null, (get, set, isOnline: boolean) => {
  set(onlineAtom, isOnline);
});

export const selectedPatDataAtom = atom<any>({
  patnames: "",
  patids: "",
  dbpath: "",
});
export const setSelectedPatDataAtom = atom(null, (get, set, data: any) => {
  set(selectedPatDataAtom, data);
});

export const openCareTeamListDialogAtom = atom<boolean>(false);
export const setOpenCareTeamListDialogAtom = atom(
  null,
  (get, set, open: boolean) => {
    set(openCareTeamListDialogAtom, open);
  }
);

export const selectedPatidsAtom = atom<string[]>([]);
export const setSelectedPatidsAtom = atom(
  null,
  (get, set, patids: string[]) => {
    set(selectedPatidsAtom, patids);
  }
);

export const enableAddAnotherAtom = atom<boolean>(false);

export const enableRemoveAnotherAtom = atom<boolean>(false);

export const initSelectionAtom = atom<any>(null);
export const setInitSelectionAtom = atom(null, (get, set, data: any) => {
  set(initSelectionAtom, data);
});

export const careTeamMsgParamAtom = atom({
  show: false,
  title: "",
  content: "",
  type: "",
});

export const showCareTeamMsgAtom = atom(null, (get, set, params: any) => {
  set(careTeamMsgParamAtom, {
    show: true,
    title: params.title,
    content: params.content,
    type: params.type,
  });
});

export const hideCareTeamMsgAtom = atom(null, (get, set) => {
  let oldValue = get(careTeamMsgParamAtom);
  set(careTeamMsgParamAtom, { ...oldValue, show: false });
});

export const disableRemoveSelfButtonAtom = atom(false);
