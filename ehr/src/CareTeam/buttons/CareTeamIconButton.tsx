import React from "react";

import { Tooltip, IconButton } from "@mui/material";

export interface IIconButtonProps {
  tooltip?: string;
  icon: any;
  handleIconBtnClick: () => void;
  color?:
    | "inherit"
    | "default"
    | "primary"
    | "secondary"
    | "error"
    | "info"
    | "success"
    | "warning"
    | undefined;
  style?: any;
  disabled?: boolean;
}

const CareTeamIconButton: React.FunctionComponent<IIconButtonProps> = (
  props
): JSX.Element => {
  return (
    <Tooltip title={props.tooltip}>
      <span>
        <IconButton
          disabled={props.disabled}
          color={props.color}
          aria-label="button"
          onClick={props.handleIconBtnClick}
          sx={{
            marginLeft: "3px",
            padding: "2px",
          }}
          style={props.style}
          size="large"
        >
          {props.icon}
        </IconButton>
      </span>
    </Tooltip>
  );
};

export default CareTeamIconButton;
