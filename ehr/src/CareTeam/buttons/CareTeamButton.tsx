import React from "react";
import { Toolt<PERSON>, But<PERSON> } from "@mui/material";

interface IButtonProps {
  text: string;
  disabled?: boolean;
  handleClick?: () => void;
  type?: "button" | "submit" | "reset" | undefined;
}

const CareTeamButton: React.FunctionComponent<IButtonProps> = ({
  text,
  disabled = false,
  handleClick,
  type = "button",
}: IButtonProps): JSX.Element => {
  return (
    <Tooltip title={text}>
      <span>
        <Button
          variant="outlined"
          disabled={disabled}
          onClick={handleClick}
          type={type}
        >
          {text}
        </Button>
      </span>
    </Tooltip>
  );
};

export default CareTeamButton;
