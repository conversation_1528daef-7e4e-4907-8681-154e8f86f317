import React from "react";

import CloseIcon from "@mui/icons-material/Close";

import CareTeamIconButton, { IIconButtonProps } from "./CareTeamIconButton";

interface ICloseIconButtonProps {
  handleClick: () => void;
}

const CareTeamCloseIconBtn: React.FunctionComponent<ICloseIconButtonProps> = ({
  handleClick,
}) => {
  const iconBtnClose: IIconButtonProps = {
    tooltip: "Close",
    icon: <CloseIcon />,
    handleIconBtnClick: handleClick,
    style: { color: "white" },
  };

  return <CareTeamIconButton {...iconBtnClose} />;
};

export default CareTeamCloseIconBtn;
