import { Box, Typography } from "@mui/material";
import { listLoadingAtom } from "../../context/CareTeamAtoms";
import { useAtomValue } from "jotai";
import Loader from "react-loader-spinner";
import CareTeamProviderList from "./CareTeamProviderList";
import CareTeamEmpty from "./CareTeamEmpty";
import CareTeamUnitPatList from "./CareTeamUnitPatList";
import CareTeamPatList from "./CareTeamPatList";
import { barHeight } from "../../util/CareTeamUtil";

export default function CareTeamMainList() {
  const loading = useAtomValue(listLoadingAtom);

  const LoadingIcon = () => {
    return loading ? (
      <Box
        sx={{
          backgroundColor: "#FFF",
          position: "absolute",
          padding: "200px 10px 80px 10px",
          textAlign: "center",
          height: "100%",
          width: "100%",
          top: "90px",
        }}
      >
        <Typography variant="h4">Loading</Typography>
        <Loader type="ThreeDots" color="#3f51b5" height={100} width={90} />
      </Box>
    ) : null;
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        marginTop: "17px",
        height: `calc(100% - 17px - ${barHeight})`,
      }}
    >
      <CareTeamPatList />
      <CareTeamProviderList />
      <CareTeamUnitPatList />
      <CareTeamEmpty />
      <LoadingIcon />
    </Box>
  );
}
