import * as React from "react";
import {
  DataGridPro,
  GridColDef,
  gridClasses,
  GRID_DETAIL_PANEL_TOGGLE_FIELD,
  GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
  GridRowId,
  useGridApiRef,
  GridEventListener,
} from "@mui/x-data-grid-pro";
import { Box, CircularProgress, Typography } from "@mui/material";
import styled from "@emotion/styled";
import { IconBlueArrowDown, IconBlueArrowUp, IconProfile } from "../../icons";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  listLoadingAtom,
  providerListAtom,
  searchTextAtom,
  selectedUnitDataAtom,
  setEmptyAtom,
  setListLoadingAtom,
  setProviderListAtom,
  setSelectedRowsAtom,
  showCareTeamErrorAtom,
  refreshProviderPatAtom,
  setRefreshProviderPatAtom,
} from "../../context/CareTeamAtoms";
import { getProvidersByGroup, searchPat } from "../../util/CareTeamData";
import {
  formatProviderRows,
  listMaxHeight,
  defaultPageSize,
  typeProviderGroup,
  FormatSearchValue,
} from "../../util/CareTeamUtil";
import CareTeamProviderPatList from "./CareTeamProviderPatList";
import {
  GridPaginationModel,
  GridRenderCellParams,
  GridRowParams,
} from "@mui/x-data-grid-pro";

interface IListProps {
  type: number;
  name: string;
  number: number;
  id: string;
}

const columns: GridColDef[] = [
  {
    field: "name",
    headerName: "Provider",
    minWidth: 180,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            ".rowcls": {
              cursor: "pointer",
            },
          }}
        >
          <IconProfile
            width={28}
            style={{
              whiteSpace: "nowrap",
              overflow: "visible",
            }}
          />
          <Typography
            sx={{
              color: "#000",
              fontSize: "16px",
              fontWeight: "bold",
              marginLeft: "6px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {FormatSearchValue(cellValues.row.name)}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "contact",
    headerName: "Contact",
    width: 250,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
          <Typography
            sx={{
              color: "#000",
              fontSize: "16px",
            }}
          >
            {cellValues.row.contact
              ? FormatSearchValue(cellValues.row.contact.split(" ")[0])
              : ""}
          </Typography>
          <Typography
            sx={{
              color: "#00000099",
              fontSize: "16px",
              marginLeft: "3px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {cellValues.row.contact
              ? FormatSearchValue(cellValues.row.contact.split(" ")[1])
              : ""}
          </Typography>
          <Typography
            sx={{
              color: "#000",
              fontSize: "16px",
              marginLeft: "3px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {cellValues.row.contact
              ? FormatSearchValue(cellValues.row.contact.split(" ")[2])
              : ""}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "start",
    headerName: "Start Date/Time",
    width: 250,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Typography
          sx={{
            color: "#000",
            fontSize: "16px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {FormatSearchValue(cellValues.row.start)}
        </Typography>
      );
    },
  },
  {
    field: "end",
    headerName: "End Date/Time",
    width: 250,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Typography
          sx={{
            color: "#000",
            fontSize: "16px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {FormatSearchValue(cellValues.row.end)}
        </Typography>
      );
    },
  },
  {
    ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
    field: GRID_DETAIL_PANEL_TOGGLE_FIELD,
    flex: 1,
    align: "right",
    renderCell: (params) => (
      <CustomDetailPanelToggle id={params.id} value={params.value} />
    ),
  },
];

const StripedDataGrid = styled(DataGridPro)({
  cursor: "pointer",
  [`& .${gridClasses.row}.even`]: {
    backgroundColor: "#F7F7F7",
    "&:hover, &.Mui-hovered": {
      backgroundColor: "#F6E596",
    },
    "&.Mui-selected": {
      backgroundColor: "#FEC341",
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#FEC341",
      },
    },
  },
  [`& .${gridClasses.row}.odd`]: {
    "&:hover, &.Mui-hovered": {
      backgroundColor: "#F6E596",
    },
    "&.Mui-selected": {
      backgroundColor: "#FEC341",
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#FEC341",
      },
    },
  },
});

const datagridSx = {
  position: "relative",
  width: "100%",
  height: "auto",
  maxHeight: listMaxHeight,
  "& .MuiDataGrid-columnHeaders": {
    backgroundColor: "#D8DCE3",
    color: "#000",
  },
  "& .MuiDataGrid-columnHeaderTitle": {
    fontSize: 16,
    fontWeight: "bold",
  },
  "& .MuiDataGrid-virtualScroller": {
    overflowX: "hidden",
  },
};

const loadingOverlay = () => {
  return <CircularProgress size={0} />;
};

function CustomDetailPanelToggle(
  props: Pick<GridRenderCellParams, "id" | "value">
) {
  const { value: isExpanded } = props;

  return isExpanded ? <IconBlueArrowUp /> : <IconBlueArrowDown />;
}

export default function CareTeamProviderList() {
  const apiRef = useGridApiRef();
  const loading = useAtomValue(listLoadingAtom);
  const setLoading = useSetAtom(setListLoadingAtom);
  const selectedUnit = useAtomValue(selectedUnitDataAtom);
  const providerList = useAtomValue(providerListAtom);
  const setProviderList = useSetAtom(setProviderListAtom);
  const setEmpty = useSetAtom(setEmptyAtom);
  const searchText = useAtomValue(searchTextAtom);
  const setSelectedRows = useSetAtom(setSelectedRowsAtom);
  const showError = useSetAtom(showCareTeamErrorAtom);
  const refreshProviderPat = useAtomValue(refreshProviderPatAtom);
  const setRefreshProviderPat = useSetAtom(setRefreshProviderPatAtom);

  const [rowCount, setRowCount] = React.useState(0);
  const getDetailPanelContent = React.useCallback(
    ({ id }: GridRowParams) => <CareTeamProviderPatList id={id} />,
    []
  );

  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: defaultPageSize,
  });

  const [detailPanelExpandedRowIds, setDetailPanelExpandedRowIds] =
    React.useState<GridRowId[]>([]);

  const autosizeOptions = {
    columns: ["name"],
    expand: true,
    includeHeaders: true,
    includeOutliers: true,
  };

  const loadData = (page: number) => {
    if (selectedUnit === null) {
      return;
    }
    if (selectedUnit.type !== typeProviderGroup) {
      setRowCount(0);
      setProviderList([]);
      return;
    }
    setEmpty(false);
    setLoading(true);
    let done = false;
    let promise: Promise<any>;
    let selectedData: IListProps = JSON.parse(JSON.stringify(selectedUnit));
    if (searchText !== "") {
      promise = searchPat(
        searchText,
        "group",
        selectedData.name,
        page * defaultPageSize,
        defaultPageSize
      );
    } else {
      promise = getProvidersByGroup(
        selectedData.name,
        page * defaultPageSize,
        defaultPageSize
      );
    }
    promise
      .then((data) => {
        if (!done) {
          if (data.success === true) {
            setRowCount(parseInt(data.total));
            if (data.providers && Object.keys(data.providers).length > 0) {
              setProviderList(formatProviderRows(data.providers));
            } else {
              setRowCount(0);
              setProviderList([]);
            }
          } else {
            showError(data.errmsg);
          }
        }
      })
      .catch((error) => {
        showError(
          "Request " +
            (searchText !== "" ? "searchPat" : "getProvidersByGroup") +
            " failed."
        );
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      })
      .finally(() => {
        setLoading(false);
        setTimeout(() => apiRef.current.autosizeColumns(autosizeOptions), 200);
      });
    return () => {
      done = true;
    };
  };

  React.useEffect(() => {
    setPaginationModel({
      page: 0,
      pageSize: defaultPageSize,
    });
    setSelectedRows([]);
    loadData(0);

    if (cci.cfg.careteamgroupExpandedRowIds) {
      setDetailPanelExpandedRowIds(cci.cfg.careteamgroupExpandedRowIds);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedUnit, searchText]);

  React.useEffect(() => {
    if (refreshProviderPat) {
      setRefreshProviderPat(false);
      loadData(paginationModel.page);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshProviderPat]);

  function loadPage(model: GridPaginationModel) {
    setPaginationModel(model);
    loadData(model.page);
  }

  const handleDetailPanelExpandedRowIdsChange = React.useCallback(
    (newIds: GridRowId[]) => {
      let ids = [];
      for (var i = 0; i < newIds.length; i++) {
        if (detailPanelExpandedRowIds.indexOf(newIds[i]) < 0) {
          ids.push(newIds[i]);
        }
      }
      cci.cfg.careteamgroupExpandedRowIds = ids;
      setDetailPanelExpandedRowIds(ids);
    },
    [detailPanelExpandedRowIds]
  );

  const onRowClick = React.useCallback<GridEventListener<"rowClick">>(
    (params) => {
      let rowIds = [];
      if (detailPanelExpandedRowIds.indexOf(params.id) < 0) {
        rowIds.push(params.id);
      }
      handleDetailPanelExpandedRowIdsChange(rowIds);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [detailPanelExpandedRowIds]
  );

  return selectedUnit != null && selectedUnit.type === typeProviderGroup ? (
    <div style={{ maxHeight: listMaxHeight, width: "100%" }}>
      <StripedDataGrid
        sx={datagridSx}
        apiRef={apiRef}
        onRowClick={onRowClick}
        rows={providerList}
        columns={columns}
        rowHeight={40}
        columnHeaderHeight={32}
        hideFooter={rowCount < defaultPageSize}
        rowCount={rowCount}
        loading={loading}
        paginationModel={paginationModel}
        paginationMode="server"
        pagination
        onPaginationModelChange={loadPage}
        disableColumnMenu
        disableRowSelectionOnClick
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even"
        }
        slots={{
          loadingOverlay: loadingOverlay,
        }}
        getDetailPanelContent={getDetailPanelContent}
        getDetailPanelHeight={() => "auto"}
        detailPanelExpandedRowIds={detailPanelExpandedRowIds}
        onDetailPanelExpandedRowIdsChange={
          handleDetailPanelExpandedRowIdsChange
        }
        autosizeOptions={autosizeOptions}
      />
    </div>
  ) : null;
}
