import * as React from "react";
import { Box, Typography, Popover } from "@mui/material";
import { IconProfile } from "../../icons";
import { FormatSearchValue } from "../../util/CareTeamUtil";

import { useSetAtom } from "jotai/react";

import {
  setSelectedRowsAtom,
  setAddStaffParamsAtom,
  setSelectedPatDataAtom,
  setOpenCareTeamListDialogAtom,
} from "../../context/CareTeamAtoms";

interface IManagerProps {
  name: string;
  cate: string;
  ext: string;
  phone: string;
  pager: string;
  staffid: string;
  patname: string;
  patid: string;
  dbpath: string;
  image: string;
  inMember: boolean;
}

export default function CareTeamListManager({
  name,
  cate,
  ext,
  phone,
  staffid,
  pager,
  patname,
  patid,
  dbpath,
  image,
  inMember,
}: IManagerProps) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  const setSelectedRows = useSetAtom(setSelectedRowsAtom);
  const setSelectedPatData = useSetAtom(setSelectedPatDataAtom);
  const setAddStaffParams = useSetAtom(setAddStaffParamsAtom);
  const setOpenCareTeamListDialog = useSetAtom(setOpenCareTeamListDialogAtom);

  const handleClick = () => {
    const params = {
      patnames: patname,
      patids: patid,
      dbpath: dbpath,
      image: image,
      staffname: name,
      selectedstaffid: staffid,
    };
    setSelectedRows([]);
    setSelectedPatData(params);
    setAddStaffParams({
      staffid: Cci.util.Staff.getSid(),
      patids: patid,
      patnames: patname,
    });
    setOpenCareTeamListDialog(true);
  };

  return (
    <div style={{ pointerEvents: "none" }}>
      <div
        onMouseOver={handleOpen}
        onMouseOut={handleClose}
        style={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          pointerEvents: "auto",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            height: inMember ? "30px" : "40px",
            overflow: "hidden",
            ":hover": {
              ".rowcls": {
                color: "#3C6CBB",
              },
            },
          }}
          onClick={handleClick}
        >
          <IconProfile
            width={28}
            style={{
              whiteSpace: "nowrap",
              overflow: "visible",
            }}
          />
          <Typography
            className="rowcls"
            sx={{
              color: "#000",
              fontSize: "16px",
              cursor: "pointer",
              marginLeft: "6px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {FormatSearchValue(name)}
          </Typography>
          <Typography
            className="rowcls"
            sx={{
              color: "#00000099",
              fontSize: "16px",
              cursor: "pointer",
              marginLeft: "6px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: cate !== "" ? "block" : "none",
            }}
          >
            {"(" + cate + ")"}
          </Typography>
        </Box>
      </div>
      <Popover
        sx={{ pointerEvents: "none" }}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box
          sx={{
            backgroundColor: "#FFFFFF",
            borderRadius: "5px",
            display: "flex",
            paddingTop: "5px",
            paddingBottom: "1px",
            paddingRight: "5px",
            paddingLeft: "5px",
          }}
        >
          <Typography
            sx={{
              fontSize: "14px",
              fontWeight: "bold",
              marginLeft: "2px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {"Phone Number:"}
          </Typography>
          <Typography
            sx={{
              fontSize: "14px",
              marginLeft: "5px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {phone}
          </Typography>
        </Box>
        <Box
          sx={{
            backgroundColor: "#FFFFFF",
            borderRadius: "5px",
            display: "flex",
            paddingTop: "1px",
            paddingBottom: "1px",
            paddingRight: "5px",
            paddingLeft: "5px",
          }}
        >
          <Typography
            sx={{
              fontSize: "14px",
              fontWeight: "bold",
              marginLeft: "2px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {"Extension:"}
          </Typography>
          <Typography
            sx={{
              fontSize: "14px",
              marginLeft: "5px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {ext}
          </Typography>
        </Box>
        <Box
          sx={{
            backgroundColor: "#FFFFFF",
            borderRadius: "5px",
            display: "flex",
            paddingTop: "1px",
            paddingBottom: "5px",
            paddingRight: "5px",
            paddingLeft: "5px",
          }}
        >
          <Typography
            sx={{
              fontSize: "14px",
              fontWeight: "bold",
              marginLeft: "2px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {"Pager:"}
          </Typography>
          <Typography
            sx={{
              fontSize: "14px",
              marginLeft: "5px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {pager}
          </Typography>
        </Box>
      </Popover>
    </div>
  );
}
