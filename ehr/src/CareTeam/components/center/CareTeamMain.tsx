import React from "react";

import { Box } from "@mui/material";
import { Theme } from "@mui/material";
import { useAtomValue, useSetAtom } from "jotai/react";
import { drawerMargin, openDrawerMargin } from "../../util/CareTeamUtil";
import {
  drawerExpandedAtom,
  openAddStaffDialogAtom,
  openAddSelfDialogAtom,
  openRemoveStaffDialogAtom,
  addStaffParamsAtom,
  showSuccessAlertAtom,
  selectedPatDataAtom,
  openCareTeamListDialogAtom,
  setOpenCareTeamListDialogAtom,
  setRefreshCurrentAtom,
} from "../../context/CareTeamAtoms";
import CareTeamAppBar from "../appbar/CareTeamAppBar";
import CareTeamMainList from "./CareTeamMainList";
import AddStaff from "../../popup/AddStaff";
import AddSelf from "../../popup/AddSelf";
import RemoveStaff from "../../popup/RemoveStaff";
import CareTeamSuccessAlert from "../../popup/CareTeamSuccessAlert";
import CareTeamListDialog from "../../popup/CareTeamListDialog";

export default function CareTeamMain() {
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const selectedPatData = useAtomValue(selectedPatDataAtom);
  const openCareTeamListDialog = useAtomValue(openCareTeamListDialogAtom);
  const setOpenCareTeamListDialog = useSetAtom(setOpenCareTeamListDialogAtom);
  const openAddStaffDialog = useAtomValue(openAddStaffDialogAtom);
  const openAddSelfDialog = useAtomValue(openAddSelfDialogAtom);
  const openRemoveStaffDialog = useAtomValue(openRemoveStaffDialogAtom);
  const addStaffParams = useAtomValue(addStaffParamsAtom);
  const showSuccessAlert = useAtomValue(showSuccessAlertAtom);
  const setRefreshCurrent = useSetAtom(setRefreshCurrentAtom);
  const normalStyle = (theme: Theme) => ({
    position: "absolute",
    display: "flex",
    flexDirection: "column",
    top: "10px",
    bottom: "10px",
    right: "10px",
    left: openDrawerMargin,
    padding: "8px 16px",
    height: "100%",
  });
  const shiftStyle = (theme: Theme) => ({
    position: "absolute",
    display: "flex",
    flexDirection: "column",
    top: "10px",
    bottom: "10px",
    right: "10px",
    left: drawerMargin,
    padding: "8px 16px",
    height: "100%",
  });

  return (
    <Box sx={!drawerExpanded ? normalStyle : shiftStyle}>
      <CareTeamAppBar />
      <CareTeamMainList />
      {openCareTeamListDialog ? (
        <CareTeamListDialog
          params={{
            ...selectedPatData,
            staffid: Cci.util.Staff.getSid(),
            setOpenCareTeamListDialog: setOpenCareTeamListDialog,
            callback2: () => {
              setRefreshCurrent(true);
            },
          }}
          alertType="react"
        />
      ) : null}
      {openAddStaffDialog && (
        <AddStaff params={addStaffParams} alertType="react" />
      )}
      {openAddSelfDialog && (
        <AddSelf params={addStaffParams} alertType="react" />
      )}
      {openRemoveStaffDialog && (
        <RemoveStaff params={addStaffParams} alertType="react" />
      )}
      {showSuccessAlert && <CareTeamSuccessAlert />}
    </Box>
  );
}
