import * as React from "react";
import { debounce } from "lodash";
import {
  DataGridPro,
  GridColDef,
  GridPaginationModel,
  GridRowSelectionModel,
  gridClasses,
  GridCallbackDetails,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import { Box, styled, CircularProgress, Typography } from "@mui/material";
import { IconStatusPending } from "../../icons";
import CareTeamListManager from "./CareTeamListManager";
import CareTeamMembers from "./CareTeamMembers";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  listLoadingAtom,
  patientListAtom,
  selectedUnitData<PERSON>tom,
  setListLoading<PERSON>tom,
  setPatientListAtom,
  setEmptyAtom,
  selectedTitleNumAtom,
  selectedRowsAtom,
  searchTextAtom,
  refreshPatAtom,
  setRefreshPatAtom,
  setSelectedRowsAtom,
  setAddStaffParams<PERSON>tom,
  setSelectedPatData<PERSON>tom,
  openCareTeamListDialog<PERSON>tom,
  setOpenCareTeamListDialog<PERSON>tom,
  showCareTeamErrorAtom,
  initSelectionAtom,
  setInitSelectionAtom,
} from "../../context/CareTeamAtoms";
import {
  getMyPatientList,
  getMyPatientListById,
  getPatientImage,
  searchPat,
  getMyPatientListPageNumByPatid,
} from "../../util/CareTeamData";
import {
  formatRows,
  listMaxHeight,
  defaultPageSize,
  typeMyView,
  FormatSearchValue,
  formatAddStaffParams,
  addPatImage,
  getRowHeight,
  autosizeOptions,
  DEFAULTPROVIDERLISTUNITID,
  getInOut,
} from "../../util/CareTeamUtil";
import CareTeamPatientInfo from "./CareTeamPatInfo";
import Config from "@cci-monorepo/config/Config";
import PatientAvatar from "../other/avatar/PatientAvatar";

interface IProvider {
  staffname: string;
  licensetype: string;
  ext: string;
  phone: string;
  pager: string;
  staffid: string;
}

const PatNameComponent = (cellValues: any) => {
  let pending: boolean = cellValues.row.pending;
  const handleClick = debounce(() => {
    const row = {
      dbpath: cellValues.row.dbpath,
      patid: cellValues.row.patid,
      Patient: cellValues.row.name,
      Unit: cellValues.row.unit,
      Bed: cellValues.row.bed,
    };
    Cci.Patient.loadPatientInfo(row.dbpath, "", true);
    Config.gotoPatientPage(row, {
      isCareTeam: true,
    });
  }, 500);

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        ":hover": {
          ".rowcls": {
            color: "#3C6CBB",
          },
        },
      }}
      onClick={handleClick}
    >
      <IconStatusPending
        width={24}
        style={{
          display: pending ? "block" : "none",
          marginRight: "12px",
          whiteSpace: "nowrap",
          overflow: "visible",
        }}
      />
      <PatientAvatar image={cellValues.row.image} />
      <Typography
        className="rowcls"
        sx={{
          cursor: "pointer",
          color: "#000",
          fontSize: "16px",
          fontWeight: "bold",
          marginLeft: "6px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {FormatSearchValue(cellValues.row.name)}
      </Typography>
      <CareTeamPatientInfo age={cellValues.row.age} sex={cellValues.row.sex} />
    </Box>
  );
};

const ProviderComponent = (cellValues: any) => {
  let patname = cellValues.row.name,
    patid = cellValues.row.patid,
    dbpath = cellValues.row.dbpath,
    image = cellValues.row.image;
  return (
    <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }}>
      {cellValues.row[cellValues.type]?.map(
        (
          { staffname, licensetype, ext, phone, pager, staffid }: IProvider,
          idx: number
        ) => (
          <CareTeamListManager
            name={staffname}
            cate={licensetype}
            ext={ext}
            phone={phone}
            pager={pager}
            staffid={staffid}
            patname={patname}
            patid={patid}
            dbpath={dbpath}
            image={image}
            inMember={false}
            key={"care_team_list_manager_" + idx}
          />
        )
      )}
    </Box>
  );
};

const patientColumn = {
  field: "name",
  headerName: "Patient",
  minWidth: 220,
  sortable: false,
  renderCell: (props: any) => <PatNameComponent {...props} />,
};

const unitbedColumn = {
  field: "unitbed",
  headerName: "Bed/Unit",
  minWidth: 120,
  sortable: false,
  renderCell: (cellValues: any) => {
    return (
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <Typography
          sx={{
            color: "#000",
            fontSize: "16px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {FormatSearchValue(cellValues.row.bed)}
        </Typography>
        <Typography
          sx={{
            color: "#00000099",
            fontSize: "16px",
            marginLeft: "3px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {FormatSearchValue(cellValues.row.unit)}
        </Typography>
      </Box>
    );
  },
};

const pcpColumn = {
  field: "pcp",
  headerName: "Primary Care Provider",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "Primary Care Provider" }} />
  ),
};

const providerColumn = {
  field: "providers",
  headerName: "Provider",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "providers" }} />
  ),
};

const PANPMAColumn = {
  field: "PA_NP_MA",
  headerName: "PA/NP/MA",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "PA_NP_MA" }} />
  ),
};

const attendingProviderColumn = {
  field: "Attending Provider",
  headerName: "Attending Provider",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "Attending Provider" }} />
  ),
};

const admittingProviderColumn = {
  field: "Admitting Provider",
  headerName: "Admitting Provider",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "Admitting Provider" }} />
  ),
};

const primaryNurseColumn = {
  field: "Primary Nurse",
  headerName: "Primary Nurse",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "Primary Nurse" }} />
  ),
};

const secondaryNurseColumn = {
  field: "Secondary Nurse",
  headerName: "Secondary Nurse",
  width: 210,
  sortable: false,
  renderCell: (props: any) => (
    <ProviderComponent {...{ ...props, type: "Secondary Nurse" }} />
  ),
};

const othersColumn = {
  field: "members",
  headerName: "Others",
  flex: 1,
  minWidth: 220,
  sortable: false,
  renderCell: (cellValues: any) => {
    return cellValues.row.members != null &&
      cellValues.row.members.length > 0 ? (
      <CareTeamMembers members={cellValues.row.members} />
    ) : null;
  },
};

const generateColumns = (cofig: any[]) => {
  const columns: GridColDef[] = [patientColumn];
  cofig.forEach((element) => {
    if (element === "Bed/Unit" || element === "Room/Unit") {
      unitbedColumn.headerName = element;
      columns.push(unitbedColumn);
    } else if (element === "Attending Provider") {
      columns.push(attendingProviderColumn);
    } else if (element === "Admitting Provider") {
      columns.push(admittingProviderColumn);
    } else if (element === "Primary Nurse") {
      columns.push(primaryNurseColumn);
    } else if (element === "Secondary Nurse") {
      columns.push(secondaryNurseColumn);
    } else if (element === "Primary Care Provider") {
      columns.push(pcpColumn);
    } else if (element === "Provider") {
      columns.push(providerColumn);
    } else if (element === "PA/NP/MA") {
      columns.push(PANPMAColumn);
    } else if (element === "Others") {
      columns.push(othersColumn);
    }
  });
  return columns;
};

const StripedDataGrid = styled(DataGridPro)({
  [`& .${gridClasses.row}.even`]: {
    backgroundColor: "#F7F7F7",
    "&.Mui-selected": {
      backgroundColor: "#F9EEBC",
    },
  },
  [`& .${gridClasses.row}.odd`]: {
    "&:hover, &.Mui-hovered": {
      backgroundColor: "transparent",
    },
    "&.Mui-selected": {
      backgroundColor: "#F9EEBC",
    },
  },
});

const datagridSx = {
  position: "relative",
  width: "100%",
  height: "auto",
  maxHeight: listMaxHeight,
  "& .MuiDataGrid-columnHeaders": {
    backgroundColor: "#D8DCE3",
    color: "#000",
  },
  "& .MuiDataGrid-columnHeaderTitle": {
    fontSize: 16,
    fontWeight: "bold",
  },
  "& .MuiDataGrid-checkboxInput": {
    "&.Mui-checked": {
      color: "rgb(49, 148, 54)",
    },
  },
};

const loadingOverlay = () => {
  return <CircularProgress size={0} />;
};

export default function CareTeamPatList() {
  const apiRef = useGridApiRef();
  const loading = useAtomValue(listLoadingAtom);
  const setLoading = useSetAtom(setListLoadingAtom);
  const selectedUnit = useAtomValue(selectedUnitDataAtom);
  const patientList = useAtomValue(patientListAtom);
  const setPatientList = useSetAtom(setPatientListAtom);
  const setEmpty = useSetAtom(setEmptyAtom);
  const setSelectedTitleNum = useSetAtom(selectedTitleNumAtom);
  const searchText = useAtomValue(searchTextAtom);
  const refreshPat = useAtomValue(refreshPatAtom);
  const setRefreshPat = useSetAtom(setRefreshPatAtom);
  const selectedRows = useAtomValue(selectedRowsAtom);
  const setSelectedRows = useSetAtom(setSelectedRowsAtom);
  const setAddStaffParams = useSetAtom(setAddStaffParamsAtom);
  const showError = useSetAtom(showCareTeamErrorAtom);
  const initSelection = useAtomValue(initSelectionAtom);
  const setInitSelection = useSetAtom(setInitSelectionAtom);
  const setSelectedPatData = useSetAtom(setSelectedPatDataAtom);
  const openCareTeamListDialog = useAtomValue(openCareTeamListDialogAtom);
  const setOpenCareTeamListDialog = useSetAtom(setOpenCareTeamListDialogAtom);

  const [rowCount, setRowCount] = React.useState(0);
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: defaultPageSize,
  });

  const inOut = getInOut();
  const columns = React.useMemo(
    () =>
      generateColumns(
        inOut === "out"
          ? cci.cfg.careteamOutPatColumns
          : cci.cfg.careteamInPatColumns
      ),
    [inOut]
  );

  const loadData = (page: number) => {
    if (selectedUnit === null) {
      return;
    }
    if (selectedUnit.type !== typeMyView) {
      setRowCount(0);
      setPatientList([]);
      return;
    }
    setLoading(true);
    let done = false;
    let promise: Promise<any>;
    let extraval =
      selectedUnit.id === "0" ? Cci.util.Staff.getSid() : selectedUnit.id;
    if (searchText !== "") {
      promise = searchPat(
        searchText,
        "provider",
        extraval,
        page * defaultPageSize,
        defaultPageSize
      );
    } else if (selectedUnit.id === "0") {
      promise = getMyPatientList(page * defaultPageSize, defaultPageSize);
    } else {
      promise = getMyPatientListById(
        selectedUnit.id,
        page * defaultPageSize,
        defaultPageSize
      );
    }
    promise
      .then((data) => {
        if (!done) {
          if (data.success === true) {
            setRowCount(parseInt(data.total));
            setSelectedTitleNum("Patients(" + data.total + ")");
            if (data.patients && Object.keys(data.patients).length > 0) {
              loadImage(formatRows(data.patients, data.signoninfo, inOut));
              setEmpty(false);
            } else {
              setEmpty(searchText === "");
              setRowCount(0);
              setPatientList([]);
            }
          } else {
            setRowCount(0);
            setPatientList([]);
            showError(data.errmsg);
          }
        }
      })
      .catch((error) => {
        setRowCount(0);
        setPatientList([]);
        showError(
          "Request " +
            (searchText !== ""
              ? "searchPat"
              : selectedUnit.id === "0"
                ? "getMyPatientList"
                : "getMyPatientListById") +
            " failed."
        );
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      })
      .finally(() => {
        setLoading(false);
        setTimeout(() => apiRef.current.autosizeColumns(autosizeOptions), 200);
      });
    return () => {
      done = true;
    };
  };

  const loadImage = (patients: any[]) => {
    if (patients.length > 0) {
      let params: any = {};
      let dbpaths: string = "";
      for (let i in patients) {
        dbpaths = dbpaths + patients[i].dbpath + ",";
      }
      dbpaths = dbpaths.substring(0, dbpaths.length - 1);
      params["type"] = "small";
      params["strDbpath"] = dbpaths;
      getPatientImage(params)
        .then((data) => {
          if (data && Object.keys(data).length > 0) {
            setPatientList(addPatImage(patients, data));
            return true;
          }
        })
        .catch((error) => {
          showError("Request getPatientImage failed.");
          if (error.response.status === 422) {
            Cci.util.RunTime.lockScreen();
          }
        });
    }
    setPatientList(patients);
    return true;
  };

  React.useEffect(() => {
    setPaginationModel({
      page: 0,
      pageSize: defaultPageSize,
    });
    setSelectedRows([]);
    if (
      initSelection &&
      initSelection.unitnum === DEFAULTPROVIDERLISTUNITID &&
      initSelection.patid > 0
    ) {
      getMyPatientListPageNumByPatid({
        patid: initSelection.patid,
        pagesize: defaultPageSize,
      })
        .then((ret) => {
          let page = 0;
          if (ret && ret.success) {
            page = ret.page;
          } else {
            setInitSelection(null);
          }
          setPaginationModel({
            page: page,
            pageSize: defaultPageSize,
          });
          loadData(page);
        })
        .catch((error) => {
          showError("Request getMyPatientListPageNumByPatid failed.");
          if (error.response.status === 422) {
            Cci.util.RunTime.lockScreen();
          }
        });
    } else {
      loadData(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedUnit, searchText]);

  React.useEffect(() => {
    if (refreshPat) {
      setSelectedRows([]);
      setRefreshPat(false);
      loadData(paginationModel.page);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshPat]);

  React.useEffect(() => {
    if (
      initSelection &&
      initSelection.patid &&
      apiRef &&
      apiRef.current &&
      apiRef.current.getAllRowIds
    ) {
      const curRecord = patientList.filter(
        (item: any) => parseInt(item.patid) === initSelection.patid
      )[0];
      if (!curRecord) {
        return;
      }
      setInitSelection(null);
      const params = {
        patnames: curRecord.name,
        patids: curRecord.id,
        dbpath: curRecord.dbpath,
        image: curRecord.image,
      };
      setSelectedRows([]);
      setSelectedPatData(params);
      setAddStaffParams({
        staffid: Cci.util.Staff.getSid(),
        patids: curRecord.id,
        patnames: curRecord.name,
      });
      setOpenCareTeamListDialog(true);
      const culAllRowIds = apiRef.current.getAllRowIds();
      const curSelRowIdx = culAllRowIds.indexOf(curRecord.id);
      apiRef.current.setCellFocus(curRecord.id, "name");
      setTimeout(() => {
        apiRef.current.scrollToIndexes({ rowIndex: curSelRowIdx });
      }, 200);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [apiRef, patientList, initSelection]);

  function loadPage(model: GridPaginationModel) {
    setPaginationModel(model);
    loadData(model.page);
  }

  function handleRowSelectionChange(
    rowSelectionModel: GridRowSelectionModel,
    details: GridCallbackDetails
  ) {
    setSelectedRows(rowSelectionModel);
    if (!openCareTeamListDialog) {
      setAddStaffParams(formatAddStaffParams(patientList, rowSelectionModel));
    }
  }

  return selectedUnit != null && selectedUnit.type === typeMyView ? (
    <div style={{ height: listMaxHeight, width: "100%" }}>
      <StripedDataGrid
        sx={datagridSx}
        apiRef={apiRef}
        rows={patientList}
        columns={columns}
        columnHeaderHeight={32}
        getRowHeight={({ id }) => getRowHeight(id, patientList)}
        rowCount={rowCount}
        loading={loading}
        hideFooter={rowCount < defaultPageSize}
        paginationModel={paginationModel}
        paginationMode="server"
        pagination
        onPaginationModelChange={loadPage}
        disableColumnMenu
        checkboxSelection
        onRowSelectionModelChange={handleRowSelectionChange}
        disableRowSelectionOnClick
        rowSelectionModel={selectedRows}
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0 ? "odd" : "even"
        }
        slots={{
          loadingOverlay: loadingOverlay,
          noRowsOverlay: () => <></>,
        }}
        autosizeOnMount={true}
        autosizeOptions={autosizeOptions}
      />
    </div>
  ) : null;
}
