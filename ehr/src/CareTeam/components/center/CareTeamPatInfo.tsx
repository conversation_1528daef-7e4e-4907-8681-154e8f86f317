import { Typography } from "@mui/material";
import { FormatSearchValue } from "../../util/CareTeamUtil";

interface IProps {
  age: string;
  sex: string;
}

const value = (age: string, sex: string) => {
  if (age === "" && sex === "") {
    return "";
  } else if (age === "") {
    return "(" + sex + ")";
  } else if (sex === "") {
    return "(" + age + "y)";
  } else {
    return "(" + age + "y, " + sex + ")";
  }
};

export default function CareTeamPatientInfo({ age, sex }: IProps) {
  return (
    <Typography
      className="rowcls"
      sx={{
        color: "#00000099",
        fontSize: "16px",
        marginLeft: "4px",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
        visibility: age === "" && sex === "" ? "hidden" : "visible",
      }}
    >
      {FormatSearchValue(value(age, sex))}
    </Typography>
  );
}
