import { Box, Typography } from "@mui/material";
import { emptyAtom } from "../../context/CareTeamAtoms";
import { useAtomValue } from "jotai";

export default function CareTeamEmpty() {
  const empty = useAtomValue(emptyAtom);

  return empty ? (
    <Box
      sx={{
        backgroundColor: "#FFF",
        position: "absolute",
        display: "flex",
        justifyContent: "center",
        height: "100%",
        width: "100%",
        top: "90px",
      }}
    >
      <Typography
        sx={{
          marginTop: "20%",
          color: "#999999",
          fontSize: "24px",
        }}
      >
        Currently No Patients Assigned.
      </Typography>
    </Box>
  ) : null;
}
