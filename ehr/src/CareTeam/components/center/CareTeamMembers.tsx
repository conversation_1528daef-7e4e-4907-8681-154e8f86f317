import * as React from "react";
import { useAtomValue } from "jotai/react";
import {
  Box,
  List,
  ListItemButton,
  ListItemText,
  Popover,
} from "@mui/material";
import { IconProfile, IconSelectProfile } from "../../icons";
import { CheckHasSearch } from "../../util/CareTeamUtil";
import CareTeamListManager from "./CareTeamListManager";
import { searchTextAtom } from "../../context/CareTeamAtoms";

interface IMember {
  providertype: string;
  staffname: string;
  licensetype: string;
}

export default function CareTeamMembers(props: any) {
  const members = props.members;
  const searchText: string = useAtomValue(searchTextAtom);
  const [ret, setRet] = React.useState<any[]>([]);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  React.useEffect(() => {
    let result = [];
    for (var i = 0; i < members.length; i++) {
      let member: IMember = members[i];
      if (i === 0) {
        if (CheckHasSearch(member, searchText)) {
          result.push(
            <IconSelectProfile style={{ pointerEvents: "none" }} width={28} />
          );
        } else {
          result.push(
            <IconProfile style={{ pointerEvents: "none" }} width={28} />
          );
        }
      } else {
        if (CheckHasSearch(member, searchText)) {
          result.push(
            <IconSelectProfile
              style={{ marginLeft: "-10px", pointerEvents: "none" }}
              width={28}
            />
          );
        } else {
          result.push(
            <IconProfile
              style={{ marginLeft: "-10px", pointerEvents: "none" }}
              width={28}
            />
          );
        }
      }
    }
    setRet(result);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [members]);

  return (
    <div style={{ pointerEvents: "none" }}>
      <div
        onMouseOver={handleOpen}
        onMouseOut={handleClose}
        style={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          pointerEvents: "auto",
        }}
      >
        {ret}
      </div>
      <Popover
        sx={{ pointerEvents: "none" }}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box
          sx={{
            backgroundColor: "#FFFFFF",
            borderRadius: "5px",
            display: "flex",
            paddingTop: "4px",
            paddingBottom: "4px",
            paddingRight: "8px",
          }}
        >
          <List disablePadding>
            {members.map((listItemData: IMember) => (
              <ListItemButton alignItems="center" sx={{ height: "30px" }}>
                <ListItemText
                  primary={listItemData.providertype + ":"}
                  primaryTypographyProps={{
                    color: "#000",
                    fontSize: "14px",
                    letterSpacing: 0,
                    fontWeight: "bold",
                  }}
                ></ListItemText>
              </ListItemButton>
            ))}
          </List>
          <List disablePadding sx={{ marginLeft: "10px" }}>
            {members.map((listItemData: IMember, idx: number) => (
              <CareTeamListManager
                name={listItemData.staffname}
                cate={listItemData.licensetype}
                ext=""
                phone=""
                pager=""
                staffid=""
                patname=""
                patid=""
                dbpath=""
                image=""
                inMember={true}
                key={"care_team_list_manager_" + idx}
              />
            ))}
          </List>
        </Box>
      </Popover>
    </div>
  );
}
