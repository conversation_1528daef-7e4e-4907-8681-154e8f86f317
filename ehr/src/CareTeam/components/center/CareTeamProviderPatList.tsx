import {
  <PERSON>,
  Button,
  CircularProgress,
  Typography,
  styled,
} from "@mui/material";
import React, { useState } from "react";
import { debounce } from "lodash";
import {
  getPatientImage,
  getProviderPatList,
  isMyPatientList,
} from "../../util/CareTeamData";
import { IconBlueAdd, IconGrayAdd } from "../../icons";
import {
  DataGridPro,
  GridColDef,
  GridRowSelectionModel,
  GridCallbackDetails,
  useGridApiRef,
} from "@mui/x-data-grid-pro";
import CareTeamPatientInfo from "./CareTeamPatInfo";
import {
  formatProviderPatRows,
  formatAddStaffParams,
  addPatImage,
  check_perms,
} from "../../util/CareTeamUtil";
import Config from "@cci-monorepo/config/Config";
import { useAtomValue, useSetAtom } from "jotai";
import {
  refreshProvider<PERSON>at<PERSON>tom,
  setRefresh<PERSON><PERSON><PERSON><PERSON>at<PERSON><PERSON>,
  selected<PERSON>ows<PERSON>tom,
  setSelected<PERSON>ows<PERSON>tom,
  setAddStaffParamsAtom,
  setOpenAddSelfDialogAtom,
  showCareTeamErrorAtom,
  selectedUnitDataAtom,
} from "../../context/CareTeamAtoms";
import { typeProviderGroup, autosizeOptions } from "../../util/CareTeamUtil";
import PatientAvatar from "../other/avatar/PatientAvatar";

const StripedDataGrid = styled(DataGridPro)({
  "& .MuiDataGrid-row": {
    "&:hover, &.Mui-hovered": {
      backgroundColor: "transparent",
    },
    "&.Mui-selected": {
      backgroundColor: "#F9EEBC",
      "&:hover, &.Mui-hovered": {
        backgroundColor: "#F9EEBC",
      },
    },
  },
});

const datagridSx = {
  marginTop: "8px",
  cursor: "default",
  position: "relative",
  width: "100%",
  //height: "auto",
  maxHeight: "300px",
  overflow: "auto",
  "& .MuiDataGrid-columnHeaders": {
    backgroundColor: "#D8DCE3",
    color: "#000",
  },
  "& .MuiDataGrid-columnHeaderTitle": {
    fontSize: 16,
    fontWeight: "bold",
  },
  "& .MuiDataGrid-virtualScroller": {
    height: "auto",
    overflow: "hidden",
  },
  ".MuiDataGrid-main > div:nth-child(2)": {
    overflowY: "auto !important",
    flex: "unset !important",
  },
  "& .MuiDataGrid-checkboxInput": {
    "&.Mui-checked": {
      color: "rgb(49, 148, 54)",
    },
  },
};

const loadingOverlay = () => {
  return (
    <CircularProgress
      size={30}
      sx={{
        position: "relative",
        left: "50%",
        top: "50%",
        zIndex: 2,
      }}
    />
  );
};

const CustomNoRowsOverlay = () => {
  return (
    <Box
      sx={{
        mt: 1,
        background: "#fff",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
      }}
    >
      <Typography
        sx={{
          color: "#999999",
          fontSize: "24px",
        }}
      >
        Currently No Patients Assigned.
      </Typography>
    </Box>
  );
};

const PatNameComponent = (cellValues: any) => {
  const handleClick = debounce(() => {
    const row = {
      dbpath: cellValues.row.dbpath,
      patid: cellValues.row.patid,
      Patient: cellValues.row.name,
      Unit: cellValues.row.unit,
      Bed: cellValues.row.bed,
    };
    Cci.Patient.loadPatientInfo(row.dbpath, "", true);
    Config.gotoPatientPage(row, {
      isCareTeam: true,
    });
  }, 500);

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        ":hover": {
          ".rowcls": {
            color: "#3C6CBB",
          },
        },
      }}
      onClick={handleClick}
    >
      <PatientAvatar image={cellValues.row.image} />
      <Typography
        className="rowcls"
        sx={{
          color: "#000",
          cursor: "pointer",
          fontSize: "16px",
          fontWeight: "bold",
          marginLeft: "6px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {cellValues.row.name}
      </Typography>
      <CareTeamPatientInfo age={cellValues.row.age} sex={cellValues.row.sex} />
    </Box>
  );
};

const columns: GridColDef[] = [
  {
    field: "name",
    headerName: "Patient",
    minWidth: 250,
    sortable: false,
    renderCell: (props) => <PatNameComponent {...props} />,
  },
  {
    field: "unitbed",
    headerName: "Bed/Unit",
    minWidth: 150,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography
            sx={{
              color: "#000",
              fontSize: "16px",
            }}
          >
            {cellValues.row.bed}
          </Typography>
          <Typography
            sx={{
              color: "#00000099",
              fontSize: "16px",
              marginLeft: "3px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {cellValues.row.unit}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: "dob",
    headerName: "DOB",
    width: 250,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Typography
          sx={{
            color: "#000",
            fontSize: "16px",
          }}
        >
          {cellValues.row.dob}
        </Typography>
      );
    },
  },
  {
    field: "admitdate",
    headerName: "Admit Date",
    width: 250,
    sortable: false,
    renderCell: (cellValues) => {
      return (
        <Typography
          sx={{
            color: "#000",
            fontSize: "16px",
          }}
        >
          {cellValues.row.admitdate}
        </Typography>
      );
    },
  },
];

export default function CareTeamProviderPatList(props: any) {
  const apiRef = useGridApiRef();
  const [loading, setLoading] = useState(true);
  const [rows, setRows] = useState<any[]>([]);
  const [isMyPatList, setIsMyPatList] = useState<boolean>(false);
  const refreshProviderPat = useAtomValue(refreshProviderPatAtom);
  const setRefreshProviderPat = useSetAtom(setRefreshProviderPatAtom);
  const selectedRows = useAtomValue(selectedRowsAtom);
  const setSelectedRows = useSetAtom(setSelectedRowsAtom);
  const setAddStaffParams = useSetAtom(setAddStaffParamsAtom);
  const setOpenAddSelfDialog = useSetAtom(setOpenAddSelfDialogAtom);
  const showError = useSetAtom(showCareTeamErrorAtom);
  const selectedUnit = useAtomValue(selectedUnitDataAtom);

  let isMe = props.id === Cci.util.Staff.getSid().toString();

  const loadData = () => {
    let done = false;
    if (refreshProviderPat) {
      setRefreshProviderPat(false);
    }
    getProviderPatList(props.id)
      .then((data) => {
        if (!done) {
          if (data.success === true) {
            if (data.patlist && Object.keys(data.patlist).length > 0) {
              loadImage(formatProviderPatRows(data.patlist));
            } else {
              setLoading(false);
              setRows([]);
            }
          } else {
            setLoading(false);
            setRows([]);
            showError(data.errmsg);
          }
        }
      })
      .catch((error) => {
        setRows([]);
        showError("Request getProviderPatList failed.");
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      })
      .finally(() => {
        setLoading(false);
        setTimeout(() => apiRef.current.autosizeColumns(autosizeOptions), 200);
      });
    return () => {
      done = true;
    };
  };
  const loadImage = (patients: any[]) => {
    if (patients.length > 0) {
      let params: any = {};
      let dbpaths: string = "";
      for (let i in patients) {
        dbpaths = dbpaths + patients[i].dbpath + ",";
      }
      dbpaths = dbpaths.substring(0, dbpaths.length - 1);
      params["type"] = "small";
      params["strDbpath"] = dbpaths;
      getPatientImage(params)
        .then((data) => {
          if (data && Object.keys(data).length > 0) {
            setRows(addPatImage(patients, data));
            setLoading(false);
            return true;
          }
        })
        .catch((error) => {
          showError("Request getPatientImage failed.");
          if (error.response.status === 422) {
            Cci.util.RunTime.lockScreen();
          }
        });
    }
    setLoading(false);
    setRows(patients);
    return true;
  };
  React.useEffect(() => {
    setSelectedRows([]);
    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  React.useEffect(() => {
    if (refreshProviderPat) {
      setSelectedRows([]);
      setRefreshProviderPat(false);
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshProviderPat]);

  React.useEffect(() => {
    if (selectedUnit.type === typeProviderGroup && selectedRows.length > 0) {
      isMyPatientList({ patids: selectedRows.join("|") })
        .then((data) => {
          if (data.success) {
            if (data.ismypatlist) {
              setIsMyPatList(true);
            } else {
              setIsMyPatList(false);
            }
          } else {
            showError(data.errmsg);
          }
        })
        .catch((error) => {
          showError("Request isMyPatientList falied.");
          if (error.response.status === 422) {
            Cci.util.RunTime.lockScreen();
          }
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRows]);

  function handleRowSelectionModelChange(
    rowSelectionModel: GridRowSelectionModel,
    details: GridCallbackDetails
  ) {
    setSelectedRows(rowSelectionModel);
    setAddStaffParams(formatAddStaffParams(rows, rowSelectionModel));
  }

  let show_self = check_perms("228", cci.cfg.perms);
  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        height: "auto",

        padding: "15px 12px 10px 12px",
        background: "#fff",
        flexDirection: "column",
        flex: 1,
        mx: "auto",
      }}
    >
      <Box
        sx={{
          display: "flex",
          width: "100%",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          sx={{
            color: "#000",
            fontSize: "14px",
            fontWeight: "bold",
            cursor: "default",
          }}
        >
          Patients({rows.length})
        </Typography>
        <Button
          sx={{
            backgroundColor: "#FFFFFF",
            borderRadius: "4px",
            border:
              isMe || selectedRows.length === 0 || isMyPatList || !show_self
                ? "1px solid #C0C0C0"
                : "1px solid #486EAF",
            fontSize: "14px",
            fontWeight: "bold",
            color:
              isMe || selectedRows.length === 0 || isMyPatList || !show_self
                ? "#ACACAC"
                : "#486EAF",
            height: "32px",
            textTransform: "none",
          }}
          disabled={
            isMe || selectedRows.length === 0 || isMyPatList || !show_self
          }
          startIcon={
            isMe || selectedRows.length === 0 || isMyPatList || !show_self ? (
              <IconGrayAdd />
            ) : (
              <IconBlueAdd />
            )
          }
          onClick={() => setOpenAddSelfDialog(true)}
        >
          {isMe || selectedRows.length === 0 || !isMyPatList
            ? "Add Self to Provider's Patients"
            : "Already Added to Provider's Patients"}
        </Button>
      </Box>
      <StripedDataGrid
        sx={datagridSx}
        apiRef={apiRef}
        rows={loading ? [] : rows}
        rowCount={rows.length}
        columns={columns}
        rowHeight={40}
        columnHeaderHeight={32}
        hideFooter={true}
        disableColumnMenu
        checkboxSelection
        autoHeight
        isRowSelectable={() => !isMe}
        disableRowSelectionOnClick
        rowSelectionModel={selectedRows}
        onRowSelectionModelChange={handleRowSelectionModelChange}
        loading={loading}
        slots={{
          loadingOverlay: loadingOverlay,
          noRowsOverlay: CustomNoRowsOverlay,
        }}
        autosizeOptions={autosizeOptions}
      />
    </Box>
  );
}
