import * as React from "react";
import Avatar from "@mui/material/Avatar";
import { IconBlueProfile } from "../../../icons";

export default function PatientAvatar(props: any) {
  let image: string = props.image;
  return image === "" || image === null || image === undefined ? (
    <IconBlueProfile
      width={24}
      style={{
        whiteSpace: "nowrap",
        overflow: "visible",
      }}
    />
  ) : (
    <Avatar
      sx={{
        width: "24px",
        height: "24px",
        whiteSpace: "nowrap",
        overflow: "visible",
      }}
      variant="square"
      src={image}
    ></Avatar>
  );
}
