import * as React from "react";
import { Theme } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";

import Box from "@mui/material/Box";
import { useAtomValue, useSetAtom } from "jotai";
import {
  searchTextAtom,
  setSearchTextAtom,
} from "../../../context/CareTeamAtoms";
import { useDebounce } from "../../../../Spl/util/SplUtil";

export default function CareTeamSearchPatientInput() {
  const searchText = useAtomValue(searchTextAtom);
  const setSearchText = useSetAtom(setSearchTextAtom);
  const [searchTextLocal, setSearchTextLocal] = React.useState("");
  const handleChange = (text: string) => {
    setSearchTextLocal(text);
    debouncedRequest();
  };
  const debouncedRequest = useDebounce(() => {
    setSearchText(searchTextLocal);
  }, 500);

  React.useEffect(() => {
    if (searchText === "") {
      setSearchTextLocal("");
    }
  }, [searchText]);

  return (
    <Box
      sx={(theme: Theme) => ({
        position: "relative",
        display: "flex",
        alignItems: "center",
        backgroundColor: "#FFF",
        borderRadius: "4px",
        border: "1px solid #B1B1B1",
        height: "32px",
        width: "321px",
        marginLeft: "16px",
      })}
    >
      <InputBase
        placeholder="Search patients and Care Team members"
        value={searchTextLocal}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
          handleChange(event.target.value);
        }}
        sx={(theme: Theme) => ({
          width: "100%",
          fontSize: "15px",
          "& input": {
            padding: theme.spacing(1, 1, 1, 1),
            fontSize: "15px",
          },
        })}
        inputProps={{ "aria-label": "search" }}
      />
    </Box>
  );
}
