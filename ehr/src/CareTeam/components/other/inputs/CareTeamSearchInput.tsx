import * as React from "react";
import { Theme } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";

import Box from "@mui/material/Box";
import { Popover } from "@mui/material";
import CareTeamSearchList from "../../menu/CareTeamSearchList";
import { useAtomValue, useSetAtom } from "jotai";
import {
  setSearchListAtom,
  setShowSearchAtom,
  showSearchAtom,
} from "../../../context/CareTeamAtoms";
import { search } from "../../../util/CareTeamData";
import { formatSearchRows } from "../../../util/CareTeamUtil";
import { useDebounce } from "../../../../Spl/util/SplUtil";

export default function CareTeamSearchInput() {
  const [searchTextLocal, setSearchTextLocal] = React.useState("");
  const [loading, setLoading] = React.useState(false);
  const showSearch = useAtomValue(showSearchAtom);
  const setShowSearch = useSetAtom(setShowSearchAtom);
  const setSearchList = useSetAtom(setSearchListAtom);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const eleRef = React.useRef<null | HTMLElement>();

  const handleChange = (text: string) => {
    setSearchTextLocal(text);
    debouncedRequest();
  };
  const debouncedRequest = useDebounce(() => {
    handleInputChange(searchTextLocal);
  }, 500);
  const handleClose = () => {
    setAnchorEl(null);
    setShowSearch(false);
  };
  const open = Boolean(anchorEl);
  const id = open ? "simple-popper" : undefined;

  const handleInputChange = (text: string) => {
    if (!loading) {
      if (text !== "") {
        let done = false;
        setLoading(true);
        search(text)
          .then((data) => {
            if (!done) {
              if (data.success === true) {
                if (
                  (data.providers && Object.keys(data.providers).length > 0) ||
                  (data.groups && Object.keys(data.groups).length > 0) ||
                  (data.units && Object.keys(data.units).length > 0)
                ) {
                  setShowSearch(true);
                  setSearchList(
                    formatSearchRows(data.providers, data.groups, data.units)
                  );
                  setAnchorEl(
                    eleRef.current === undefined ? null : eleRef.current
                  );
                } else {
                  setShowSearch(false);
                  setSearchList([]);
                }
              } else {
                setShowSearch(false);
                setSearchList([]);
              }
            }
          })
          .catch((error) => {
            setShowSearch(false);
            setSearchList([]);
            if (error.response.status === 422) {
              Cci.util.RunTime.lockScreen();
            }
          })
          .finally(() => {
            setLoading(false);
          });

        return () => {
          done = true;
        };
      } else {
        setAnchorEl(null);
      }
    }
  };

  React.useEffect(() => {
    if (!showSearch) {
      setAnchorEl(null);
    }
  }, [showSearch]);

  return (
    <div>
      <Box
        ref={eleRef}
        sx={(theme: Theme) => ({
          position: "relative",
          backgroundColor: "#FFFFFF",
          borderRadius: "4px",
          border: "1px solid #B1B1B1",
          display: "flex",
          height: "32px",
          marginTop: "16px",
          marginLeft: "10px",
          marginRight: "10px",
          justifyContent: "space-between",
        })}
      >
        <Box
          sx={(theme: Theme) => ({
            marginLeft: "5px",
            height: "100%",
            pointerEvents: "none",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          })}
        >
          <SearchIcon color="disabled" fontSize="small" />
        </Box>
        <InputBase
          aria-describedby={id}
          value={searchTextLocal}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
            handleChange(event.target.value);
          }}
          placeholder="Search Staff, Provider Groups, Units"
          sx={(theme: Theme) => ({
            fontSize: "15px",
            "& input": {
              padding: theme.spacing(1, 1, 1, 1),
              fontSize: "15px",
              [theme.breakpoints.up("sm")]: {
                width: "30ch",
                "&:focus": {
                  width: "30ch",
                },
              },
            },
          })}
          inputProps={{ "aria-label": "search" }}
        />
      </Box>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        disableAutoFocus={true}
        disableEnforceFocus={true}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box
          sx={{
            width: "284px",
            backgroundColor: "#FFFFFF",
            borderRadius: "5px",
          }}
        >
          <CareTeamSearchList />
        </Box>
      </Popover>
    </div>
  );
}
