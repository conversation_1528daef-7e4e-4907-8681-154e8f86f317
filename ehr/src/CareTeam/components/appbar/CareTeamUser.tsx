import * as React from "react";
import { Box, Typography } from "@mui/material";
import { IconDropDownArrow, IconProfile } from "../../icons";
import { useSetAtom, useAtomValue } from "jotai";
import {
  selectedUnitDataAtom,
  showCareTeamErrorAtom,
  setOnlineAtom,
} from "../../context/CareTeamAtoms";
import { typeProviderGroup } from "../../util/CareTeamUtil";
import CareTeamIconButton, {
  IIconButtonProps,
} from "../../buttons/CareTeamIconButton";
import SignOn from "../../popup/SignOn";
import { isStaffOnline } from "../../util/CareTeamData";

export default function CareTeamUser() {
  const selectedUnit = useAtomValue(selectedUnitDataAtom);
  const showError = useSetAtom(showCareTeamErrorAtom);
  const setOnline = useSetAtom(setOnlineAtom);
  const [open, setOpen] = React.useState(false);
  const [licensetype, setLicensetype] = React.useState("");
  const [name, setName] = React.useState("");
  const sid = Cci.util.Staff.getSid() ? Cci.util.Staff.getSid() : -1;

  React.useEffect(() => {
    isStaffOnline()
      .then((data) => {
        if (data.success) {
          if (!data.online) {
            setOpen(true);
          } else {
            setOnline(true);
          }
          if (data.name) {
            setName(data.name);
          }
          if (data.licensetype) {
            setLicensetype(data.licensetype);
          }
        } else {
          showError(data.errmsg);
        }
      })
      .catch((error) => {
        showError("Failed to connect server");
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const callback = (values: any) => {
    setOnline(true);
    setName(values.name);
    setLicensetype(values.licensetype);
  };

  const handleClick = () => {
    setOpen(true);
  };

  const iconBtnDropDownArrow: IIconButtonProps = {
    icon: <IconDropDownArrow width={24}></IconDropDownArrow>,
    handleIconBtnClick: handleClick,
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          borderRadius: "3px",
          border: "1px solid #CBCACA",
          paddingLeft: "4px",
          paddingRight: "4px",
          height: "28px",
          visibility:
            selectedUnit && selectedUnit.type === typeProviderGroup
              ? "hidden"
              : "visible",
          cursor: "pointer",
        }}
        onClick={() => handleClick()}
      >
        <IconProfile height={26} width={26}></IconProfile>
        {name !== "" && (
          <Typography
            sx={{
              color: "#000",
              fontSize: "15px",
              fontWeight: "bold",
              marginLeft: "8px",
            }}
          >
            {name}
          </Typography>
        )}
        {licensetype !== "" && (
          <Typography
            sx={{
              color: "var(--black-60, rgba(0, 0, 0, 0.60))",
              fontSize: "15px",
              fontWeight: "bold",
              marginLeft: "6px",
              marginRight: "16px",
            }}
          >
            {"(" + licensetype + ")"}
          </Typography>
        )}
        <CareTeamIconButton {...iconBtnDropDownArrow} />
      </Box>
      {open && (
        <SignOn
          type="type1"
          sid={sid}
          setOpenEditContactDialog={setOpen}
          setNameAndLicensetype={callback}
        />
      )}
    </>
  );
}
