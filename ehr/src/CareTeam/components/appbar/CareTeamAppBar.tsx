import { Box, Typography } from "@mui/material";
import {
  barHeight,
  typeProviderGroup,
  check_perms,
} from "../../util/CareTeamUtil";
import CareTeamUser from "./CareTeamUser";
import CareTeamSearchPatientInput from "../other/inputs/CareTeamSearchPatInput";
import CareTeamAppBarBtn from "./CareTeamAppBarBtn";
import { useAtomValue } from "jotai";
import {
  selectedTitleNumAtom,
  selectedTitleTypeAtom,
  selectedUnitDataAtom,
  selectedUnitIdAtom,
} from "../../context/CareTeamAtoms";

/**
 *
 * @description Top AppBar that contains header and an icon button to helps collapse/expand the left drawer
 * @returns AppBar component
 */
export default function CareTeamAppBar() {
  const selectedUnit = useAtomValue(selectedUnitIdAtom);
  const selectedUnitData = useAtomValue(selectedUnitDataAtom);
  const selectedTitleNum = useAtomValue(selectedTitleNumAtom);
  const selectedTitleType = useAtomValue(selectedTitleTypeAtom);
  let show_self = check_perms("228", cci.cfg.perms),
    show_staff = check_perms("229", cci.cfg.perms);

  return (
    <Box
      sx={{
        position: "relative",
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: barHeight,
      }}
    >
      <Box
        sx={{
          height: "32px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box
          sx={{
            height: "32px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            sx={{
              color: "#000",
              fontSize: "24px",
              fontWeight: "bold",
            }}
          >
            {selectedUnit}
          </Typography>
          <Typography
            sx={{
              color: "#00000099",
              marginLeft: "8px",
              fontSize: "24px",
              fontWeight: "bold",
            }}
          >
            {selectedTitleType}
          </Typography>
        </Box>
        <CareTeamUser />
      </Box>
      <Box
        sx={{
          height: "32px",
          marginTop: "10px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            sx={{
              color: "#000",
              fontSize: "18px",
              fontWeight: "bold",
            }}
          >
            {selectedTitleNum}
          </Typography>
          <CareTeamSearchPatientInput />
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            visibility:
              (selectedUnitData &&
                selectedUnitData.type === typeProviderGroup) ||
              !(show_staff || show_self)
                ? "hidden"
                : "visible",
          }}
        >
          {show_staff && (
            <Box>
              <CareTeamAppBarBtn id={0} name={"Add Staff"} />
            </Box>
          )}
          {show_self && (
            <Box>
              <CareTeamAppBarBtn id={1} name={"Add Self"} />
            </Box>
          )}
          <Box
            sx={{
              marginLeft: "14px",
              marginRight: "14px",
              width: "2px",
              height: "32px",
              backgroundColor: "#C0C0C0",
            }}
          />
          {show_staff && (
            <Box>
              <CareTeamAppBarBtn id={2} name={"Remove Staff"} />
            </Box>
          )}
          {show_self && (
            <Box>
              <CareTeamAppBarBtn id={3} name={"Remove Self"} />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
}
