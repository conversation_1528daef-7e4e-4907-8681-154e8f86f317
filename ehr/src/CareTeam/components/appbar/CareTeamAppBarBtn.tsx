import { useAtomValue, useSetAtom } from "jotai/react";
import Button from "@mui/material/Button";
import { Theme } from "@mui/material";
import {
  IconGrayAdd,
  IconGrayRemove,
  IconWhiteAdd,
  IconWhiteRemove,
  IconMinimizeDefault,
  IconMaximizeDefault,
} from "../../icons";
import { removeFromCareTeam } from "../../util/CareTeamData";
import {
  showCareTeamMsgAtom,
  selectedRowsAtom,
  setOnlineAtom,
  setRefreshCurrentAtom,
  selectedUnitDataAtom,
  setOpenAddStaffDialogAtom,
  setOpenAddSelfDialogAtom,
  setOpenRemoveStaffDialogAtom,
  setShowSuccessAlertAtom,
  setSuccessInfoAtom,
  disableRemoveSelfButtonAtom,
} from "../../context/CareTeamAtoms";
import { typeUnit, typeMyView } from "../../util/CareTeamUtil";

interface IBtnProps {
  id: number;
  name: string;
}

export default function CareTeamAppBarBtn({ id, name }: IBtnProps) {
  const showError = useSetAtom(showCareTeamMsgAtom);
  const selectedRows = useAtomValue(selectedRowsAtom);
  const setOnline = useSetAtom(setOnlineAtom);
  const setRefreshCurrent = useSetAtom(setRefreshCurrentAtom);
  const selectedUnit = useAtomValue(selectedUnitDataAtom);
  const setOpenAddStaffDialog = useSetAtom(setOpenAddStaffDialogAtom);
  const setOpenRemoveStaffDialog = useSetAtom(setOpenRemoveStaffDialogAtom);
  const setOpenAddSelfDialog = useSetAtom(setOpenAddSelfDialogAtom);
  const setShowSuccessAlert = useSetAtom(setShowSuccessAlertAtom);
  const setSuccessInfo = useSetAtom(setSuccessInfoAtom);
  const disableRemoveSelfButton = useAtomValue(disableRemoveSelfButtonAtom);

  const staffButtonStyle = (theme: Theme) => ({
    backgroundColor: "#FFFFFF",
    borderRadius: "4px",
    border: "1px solid #C0C0C0",
    fontSize: "14px",
    fontWeight: "bold",
    color: "rgb(71, 137, 200)",
    textTransform: "none",
    "&:hover": {
      backgroundColor: "#FFFFFF",
    },
    "&:disabled": {
      color: "#ACACAC",
      backgroundColor: "#FFFFFF",
    },
  });
  const selfButtonStyle = (theme: Theme) => ({
    backgroundColor: "rgb(71, 137, 200)",
    borderRadius: "4px",
    fontSize: "14px",
    fontWeight: "bold",
    color: "#FFFFFF",
    marginLeft: "8px",
    textTransform: "none",
    "&:hover": {
      backgroundColor: "#4789C8",
    },
    "&:disabled": {
      color: "#FFFFFF",
      backgroundColor: "#C0C0C0",
    },
  });
  const startIcon = (id: number) => {
    let length = selectedRows.length;
    switch (id) {
      case 0:
        return length > 0 ? <IconMaximizeDefault /> : <IconGrayAdd />;
      case 1:
        return <IconWhiteAdd />;
      case 2:
        return length > 0 ? <IconMinimizeDefault /> : <IconGrayRemove />;
      case 3:
        return <IconWhiteRemove />;
    }
  };
  const currentStyle = (id: number) => {
    switch (id) {
      case 0:
        return staffButtonStyle;
      case 1:
        return selfButtonStyle;
      case 2:
        return staffButtonStyle;
      case 3:
        return selfButtonStyle;
    }
  };
  const handleClick = (id: number) => {
    switch (id) {
      case 0:
        setShowSuccessAlert(false);
        setOpenAddStaffDialog(true);
        return;
      case 1:
        setShowSuccessAlert(false);
        setOpenAddSelfDialog(true);
        return;
      case 2:
        setShowSuccessAlert(false);
        setOpenRemoveStaffDialog(true);
        return;
      case 3: {
        setShowSuccessAlert(false);
        let params: any = { patids: selectedRows.join("|") };
        if (selectedUnit.type === typeMyView) {
          params.frommypatlist = 1;
        } else if (selectedUnit.type === typeUnit) {
          params.frommypatlist = 0;
          params.rmvsids = Cci.util.Staff.getSid();
        } else {
          const params = {
            title: "Error",
            content: "Unknow selected type",
          };
          showError(params);
        }
        removeFromCareTeam(params)
          .then((data) => {
            if (data.success) {
              setRefreshCurrent(true);
              setShowSuccessAlert(true);
              setSuccessInfo("Removed self from selected patients");
              if (selectedUnit.type === typeMyView && data.signoff === true) {
                setOnline(false);
              }
            } else {
              const params = {
                title: "Error",
                content: data["message"],
              };
              showError(params);
            }
          })
          .catch((error) => {
            const params = {
              title: "Error",
              content: "Failed to remove from CareTeam.",
            };
            showError(params);
            if (error.response.status === 422) {
              Cci.util.RunTime.lockScreen();
            }
          });
      }
    }
  };
  const disableRemoveSelf = () => {
    if (selectedRows.length === 0) return true;
    if (selectedUnit.type === typeMyView) {
      return false;
    } else if (selectedUnit.type === typeUnit) {
      return id === 3 && disableRemoveSelfButton;
    } else {
      return true;
    }
  };
  return (
    <Button
      sx={currentStyle(id)}
      startIcon={startIcon(id)}
      disabled={disableRemoveSelf()}
      onClick={() => handleClick(id)}
    >
      {name}
    </Button>
  );
}
