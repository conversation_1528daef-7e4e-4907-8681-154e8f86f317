import * as React from "react";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  selectedUnitIdAtom,
  setSelectedTitleTypeAtom,
  setSelectedUnitAtom,
  setSelectedUnitDataAtom,
  setShowSearchAtom,
} from "../../context/CareTeamAtoms";
import { Box, ListItemButton, Typography } from "@mui/material";
import { IconProfile } from "../../icons";
import { typeMyView } from "../../util/CareTeamUtil";

interface IListItemProps {
  type: number;
  id: number;
  name: string;
  licensetype: string;
}

export default function CareTeamSearchListItem({
  type,
  id,
  name,
  licensetype,
}: IListItemProps) {
  const selectedUnit = useAtomValue(selectedUnitIdAtom);
  const setSelectedUnit = useSetAtom(setSelectedUnitAtom);
  const setSelectedUnitData = useSetAtom(setSelectedUnitDataAtom);
  const setShowSearch = useSetAtom(setShowSearchAtom);
  const setSelectedTitleType = useSetAtom(setSelectedTitleTypeAtom);

  const setStateSelected = (event: object, value: string) => {
    setShowSearch(false);
    if (selectedUnit === value) {
      return;
    }
    setSelectedUnit(value);
    if (type === typeMyView) {
      setSelectedUnitData({
        type: typeMyView,
        name: name,
        number: 0,
        id: id,
      });
      setSelectedTitleType(licensetype !== "" ? "(" + licensetype + ")" : "");
    }
  };

  return (
    <ListItemButton
      sx={{ height: "30px" }}
      alignItems="center"
      onClick={(event) => setStateSelected(event, name)}
    >
      <Box sx={{ display: "flex", alignItems: "center", width: "284px" }}>
        <IconProfile
          width={28}
          style={{
            whiteSpace: "nowrap",
            overflow: "visible",
            display: type === typeMyView ? "block" : "none",
          }}
        />
        <Typography
          sx={{
            color: "#000",
            fontSize: "15px",
            marginLeft: "6px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {name}
        </Typography>
        <Typography
          sx={{
            color: "#00000099",
            fontSize: "15px",
            marginLeft: "6px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            display:
              licensetype !== "" && licensetype !== undefined
                ? "block"
                : "none",
          }}
        >
          {"(" + licensetype + ")"}
        </Typography>
      </Box>
    </ListItemButton>
  );
}
