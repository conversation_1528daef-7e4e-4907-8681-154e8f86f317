import { List } from "@mui/material";
import { useAtomValue } from "jotai";
import { searchListAtom } from "../../context/CareTeamAtoms";
import CareTeamSearchListItem from "./CareTeamSearchListItem";

interface IListProps {
  type: number;
  id: number;
  name: string;
  licensetype: string;
}

export default function CareTeamSearchList() {
  const searchList = useAtomValue(searchListAtom);

  return (
    <List
      component="nav"
      sx={{
        width: "284px",
        overflow: "auto",
        minHeight: "38px",
        maxHeight: "300px",
        "&.MuiList-padding": {
          paddingTop: "4px",
          paddingBottom: "4px",
        },
      }}
    >
      {searchList.map((listItemData: IListProps, idx: number) => (
        <CareTeamSearchListItem
          key={"care_team_search_list_item_" + idx}
          type={listItemData.type}
          id={listItemData.id}
          name={listItemData.name}
          licensetype={listItemData.licensetype}
        />
      ))}
    </List>
  );
}
