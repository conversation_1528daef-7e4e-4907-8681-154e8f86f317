import * as React from "react";
import List from "@mui/material/List";
import { Typography } from "@mui/material";
import CareTeamListItem from "./CareTeamListItem";
import { styled } from "@mui/material/styles";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  groupList<PERSON>tom,
  loading<PERSON>tom,
  setGroupList<PERSON>tom,
  setLoading<PERSON>tom,
  showCareTeamErrorAtom,
} from "../../context/CareTeamAtoms";
import { getProviderGroup } from "../../util/CareTeamData";

interface IListProps {
  type: number;
  name: string;
  number: number;
}

export default function CareTeamGroupList() {
  const MyComponent = styled("div")({
    width: "100%",
    height: "156px",
    marginTop: "14px",
    marginBottom: "5px",
  });

  const loading = useAtomValue(loadingAtom);
  const setLoading = useSetAtom(setLoadingAtom);
  const groupList = useAtomValue(groupListAtom);
  const setGroupList = useSetAtom(setGroupListAtom);
  const showCareTeamError = useSetAtom(showCareTeamErrorAtom);

  React.useEffect(() => {
    let done = false;
    setLoading(true);

    getProviderGroup()
      .then((data) => {
        if (!done) {
          if (data.success === true) {
            if (data.groups && Object.keys(data.groups).length > 0) {
              setGroupList(data.groups);
            } else {
              showCareTeamError("Empty provider group data!");
            }
          } else {
            showCareTeamError(data.errmsg);
          }
        }
      })
      .catch((error) => {
        showCareTeamError("Failed to connect server!");
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      })
      .finally(() => {
        setLoading(false);
      });

    return () => {
      done = true;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return !loading ? (
    <MyComponent>
      <Typography
        style={{
          color: "#000000",
          fontSize: "16px",
          fontWeight: "bold",
          marginLeft: "12px",
          marginBottom: "4px",
        }}
      >
        Provider Group
      </Typography>
      <List
        component="nav"
        sx={{
          width: "100%",
          maxWidth: "284px",
          backgroundColor: "#FFFFFF",
          position: "relative",
          overflow: "auto",
          maxHeight: "calc(100% - 25px)",
          borderRadius: "5px",
          marginLeft: "10px",
          marginRight: "10px",
          "&.MuiList-padding": {
            paddingTop: "4px",
            paddingBottom: "4px",
          },
          "&& .Mui-selected, && .Mui-selected:hover": {
            bgcolor: "#FEC341",
          },
        }}
      >
        {groupList.map((listItemData: IListProps, idx: number) => (
          <CareTeamListItem
            name={listItemData.name + "(" + listItemData.number + ")"}
            key={"care_team_list_item_" + idx}
          />
        ))}
      </List>
    </MyComponent>
  ) : null;
}
