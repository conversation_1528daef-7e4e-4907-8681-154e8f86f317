import * as React from "react";
import List from "@mui/material/List";
import { Box, Typography } from "@mui/material";
import CareTeamListItem from "./CareTeamListItem";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  loadingAtom,
  setLoadingAtom,
  setUnitListAtom,
  showCareTeamErrorAtom,
  unitListAtom,
  setSelectedUnitAtom,
  initSelectionAtom,
  setInitSelectionAtom,
} from "../../context/CareTeamAtoms";
import { getUnitList } from "../../util/CareTeamData";
import { DEFAULTPROVIDERLISTUNITID, getInOut } from "../../util/CareTeamUtil";

interface IListProps {
  type: number;
  name: string;
}

export default function CareTeamUnitList() {
  const loading = useAtomValue(loadingAtom);
  const setLoading = useSetAtom(setLoadingAtom);
  const unitList = useAtomValue(unitListAtom);
  const setUnitList = useSetAtom(setUnitListAtom);
  const showCareTeamError = useSetAtom(showCareTeamErrorAtom);
  const setSelectedUnit = useSetAtom(setSelectedUnitAtom);
  const initSelection = useAtomValue(initSelectionAtom);
  const setInitSelection = useSetAtom(setInitSelectionAtom);

  React.useEffect(() => {
    let done = false;
    setLoading(true);

    getUnitList()
      .then((data) => {
        if (!done) {
          if (data) {
            if (data.children && Object.keys(data.children).length > 0) {
              let inOut = getInOut();
              const curData = data.children.filter(
                (item: any) => item.in_out === inOut
              );
              if (
                curData.length > 0 &&
                Object.keys(curData[0].children).length > 0
              ) {
                setUnitList(curData[0].children);
                if (
                  initSelection &&
                  initSelection.unitnum < DEFAULTPROVIDERLISTUNITID
                ) {
                  if (initSelection.unitnum) {
                    const selectedItem = curData[0].children.filter(
                      (item: any) => {
                        return (
                          item &&
                          parseInt(item.unitnum) === initSelection.unitnum
                        );
                      }
                    )[0];
                    if (selectedItem) {
                      setSelectedUnit(selectedItem.unitname);
                    } else {
                      setInitSelection(null);
                    }
                  } else {
                    setInitSelection(null);
                  }
                }
              } else {
                showCareTeamError("Empty unit data!");
              }
            } else {
              showCareTeamError("Empty unit data!");
            }
          } else {
            showCareTeamError(data.errmsg);
          }
        }
      })
      .catch((error) => {
        showCareTeamError("Failed to connect server!");
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      })
      .finally(() => {
        setLoading(false);
      });

    return () => {
      done = true;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return !loading ? (
    <Box
      sx={{
        width: "100%",
        height: "calc(100% - 320px)",
        marginTop: "14px",
        marginBottom: "5px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Typography
        style={{
          color: "#000000",
          fontSize: "16px",
          fontWeight: "bold",
          marginLeft: "12px",
          marginBottom: "4px",
        }}
      >
        Unit
      </Typography>
      <List
        component="nav"
        sx={{
          width: "100%",
          maxWidth: "284px",
          maxHeight: "calc(100% - 50px)",
          backgroundColor: "#FFFFFF",
          overflow: "auto",
          borderRadius: "5px",
          marginLeft: "10px",
          marginRight: "10px",
          "&.MuiList-padding": {
            paddingTop: "4px",
            paddingBottom: "4px",
          },
          "&& .Mui-selected, && .Mui-selected:hover": {
            bgcolor: "#FEC341",
          },
        }}
      >
        {unitList.map((listItemData: IListProps, idx: number) => (
          <CareTeamListItem
            name={listItemData.name}
            key={"care_team_list_item_" + idx}
          />
        ))}
      </List>
    </Box>
  ) : null;
}
