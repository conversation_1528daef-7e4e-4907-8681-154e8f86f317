import * as React from "react";
import List from "@mui/material/List";
import { Typography } from "@mui/material";
import CareTeamListItem from "./CareTeamListItem";
import { styled } from "@mui/material/styles";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  loadingAtom,
  myViewListAtom,
  refreshCurrentAtom,
  setLoadingAtom,
  setMyViewListAtom,
  setRefreshCurrentAtom,
  setSelectedUnitAtom,
  setSelectedUnitDataAtom,
  showCareTeamErrorAtom,
  initSelectionAtom,
  setInitSelectionAtom,
} from "../../context/CareTeamAtoms";
import { getPatientList } from "../../util/CareTeamData";
import { DEFAULTPROVIDERLISTUNITID, typeMyView } from "../../util/CareTeamUtil";

interface IListProps {
  type: number;
  name: string;
}

export default function CareTeamMyViewList() {
  const MyComponent = styled("div")({
    width: "100%",
    height: "65px",
    marginTop: "14px",
    marginBottom: "5px",
  });

  const loading = useAtomValue(loadingAtom);
  const setLoading = useSetAtom(setLoadingAtom);
  const myViewList = useAtomValue(myViewListAtom);
  const setMyViewList = useSetAtom(setMyViewListAtom);
  const showCareTeamError = useSetAtom(showCareTeamErrorAtom);
  const setSelectedUnit = useSetAtom(setSelectedUnitAtom);
  const setSelectedData = useSetAtom(setSelectedUnitDataAtom);
  const refreshCurrent = useAtomValue(refreshCurrentAtom);
  const setRefreshCurrent = useSetAtom(setRefreshCurrentAtom);
  const initSelection = useAtomValue(initSelectionAtom);
  const setInitSelection = useSetAtom(setInitSelectionAtom);

  const loadData = (setSelect: boolean) => {
    let done = false;
    setLoading(true);

    getPatientList()
      .then((data) => {
        if (!done) {
          if (data.ret === true) {
            if (data.groups && Object.keys(data.groups).length > 0) {
              setMyViewList(data.groups);
              if (initSelection) {
                if (initSelection.unitnum === DEFAULTPROVIDERLISTUNITID) {
                  if (
                    initSelection.unitnum === parseInt(data.groups[0].groupId)
                  ) {
                    setSelectedUnit(data.groups[0].disp.split("(")[0]);
                    setSelectedData({
                      type: typeMyView,
                      name: data.groups[0].disp,
                      number: data.groups[0].patNum,
                      id: "0",
                    });
                  } else {
                    if (setSelect) {
                      setSelectedUnit(data.groups[0].disp.split("(")[0]);
                      setSelectedData({
                        type: typeMyView,
                        name: data.groups[0].disp,
                        number: data.groups[0].patNum,
                        id: "0",
                      });
                    }
                    setInitSelection(null);
                  }
                }
              } else {
                if (setSelect) {
                  if (cci.cfg.careteamselected) {
                    setSelectedUnit(
                      cci.cfg.careteamselected.name.split("(")[0]
                    );
                    setSelectedData(cci.cfg.careteamselected);
                  } else {
                    setSelectedUnit(data.groups[0].disp.split("(")[0]);
                    setSelectedData({
                      type: typeMyView,
                      name: data.groups[0].disp,
                      number: data.groups[0].patNum,
                      id: "0",
                    });
                  }
                }
              }
            } else {
              showCareTeamError("Empty my view data!");
            }
          } else {
            showCareTeamError(data.errmsg);
          }
        }
      })
      .catch((error) => {
        showCareTeamError("Failed to connect server!");
        if (error.response.status === 422) {
          Cci.util.RunTime.lockScreen();
        }
      })
      .finally(() => {
        setLoading(false);
      });

    return () => {
      done = true;
    };
  };
  React.useEffect(() => {
    loadData(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  React.useEffect(() => {
    if (refreshCurrent) {
      setRefreshCurrent(false);
      loadData(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshCurrent]);
  return !loading ? (
    <MyComponent>
      <Typography
        style={{
          color: "#000000",
          fontSize: "16px",
          fontWeight: "bold",
          marginLeft: "12px",
          marginBottom: "4px",
        }}
      >
        My View
      </Typography>
      <List
        component="nav"
        sx={{
          width: "100%",
          maxWidth: "284px",
          backgroundColor: "#FFFFFF",
          position: "relative",
          overflow: "auto",
          maxHeight: `calc(10vh - 10px)`,
          borderRadius: "5px",
          marginLeft: "10px",
          marginRight: "10px",
          "&.MuiList-padding": {
            paddingTop: "4px",
            paddingBottom: "4px",
          },
          "&& .Mui-selected, && .Mui-selected:hover": {
            bgcolor: "#FEC341",
          },
        }}
      >
        {myViewList.map((listItemData: IListProps, idx: number) => (
          <CareTeamListItem
            name={listItemData.name}
            key={"care_team_list_item_" + idx}
          />
        ))}
      </List>
    </MyComponent>
  ) : null;
}
