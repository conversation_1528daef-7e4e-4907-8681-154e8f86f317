import { useAtomValue, useSet<PERSON>tom } from "jotai/react";
import {
  drawerExpandedAtom,
  flipDrawerAtom,
} from "../../context/CareTeamAtoms";
import { Box, Typography } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import { oepnDrawerWdith } from "../../util/CareTeamUtil";

import { IconTriggerCollapsed } from "../../icons";

export default function CareTeamOpenDrawer() {
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const flipDrawer = useSetAtom(flipDrawerAtom);

  return (
    <Box
      sx={{
        visibility: drawerExpanded ? "hidden" : "visible",
        backgroundColor: "#2E4B75",
        position: "absolute",
        height: "100%",
        width: oepnDrawerWdith,
        display: "flex",
        top: "10px",
        justifyItems: "center",
        flexDirection: "column",
        paddingTop: "5px",
      }}
    >
      <IconButton onClick={flipDrawer} sx={{ color: "white" }} size="medium">
        <IconTriggerCollapsed width={15} height={13} />
      </IconButton>
      <Typography
        style={{
          marginTop: "5px",
          whiteSpace: "nowrap",
          color: "#FFFFFF",
          fontSize: "15px",
          fontWeight: "bold",
          transform: "rotate(90deg)",
        }}
      >
        Care Teams
      </Typography>
    </Box>
  );
}
