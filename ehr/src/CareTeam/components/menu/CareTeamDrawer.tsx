import * as React from "react";
import Drawer from "@mui/material/Drawer";
import { Box, Typography } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import { drawerWidth } from "../../util/CareTeamUtil";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  drawerExpandedAtom,
  flipDrawer<PERSON>tom,
} from "../../context/CareTeamAtoms";
import CareTeamSearchInput from "../other/inputs/CareTeamSearchInput";
import CareTeamUnitList from "./CareTeamUnitList";
import CareTeamMyViewList from "./CareTeamMyViewList";
import CareTeamGroupList from "./CareTeamGroupList";

import { IconTriggerExpanded } from "../../icons";

export default function CareTeamDrawer() {
  const drawerExpanded = useAtomValue(drawerExpandedAtom);
  const flipDrawer = useSet<PERSON>tom(flipDrawerAtom);

  return (
    <Drawer
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          position: "absolute",
          height: "calc(100vh-10px)",
          flexShrink: 0,
          whiteSpace: "nowrap",
          top: "10px",
          left: "10px",
          backgroundColor: "#E3EAF5",
          width: drawerWidth,
          boxSizing: "border-box",
          overflow: "hidden",
        },
      }}
      variant="persistent"
      anchor="left"
      open={drawerExpanded}
    >
      <Box
        sx={{
          height: "25px",
          backgroundColor: "#2E4B75",
          display: "flex",
          paddingLeft: "10px",
          paddingRight: "5px",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          style={{
            color: "#FFFFFF",
            fontSize: "15px",
            fontWeight: "bold",
          }}
        >
          View and Edit Care Teams
        </Typography>
        <IconButton onClick={flipDrawer} sx={{ color: "white" }} size="medium">
          <IconTriggerExpanded width={15} height={13} />
        </IconButton>
      </Box>
      <Box
        sx={{
          height: "calc(100% - 25px)",
          width: "100%",
          position: "relative",
          display: "flex",
          justifyItems: "center",
          flexDirection: "column",
        }}
      >
        <CareTeamSearchInput />
        <CareTeamMyViewList />
        <CareTeamGroupList />
        <CareTeamUnitList />
      </Box>
    </Drawer>
  );
}
