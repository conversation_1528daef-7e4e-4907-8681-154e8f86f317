import * as React from "react";
import ListItemText from "@mui/material/ListItemText";
import { useAtomValue, useSetAtom } from "jotai/react";
import {
  selectedUnitIdAtom,
  setSelectedTitleTypeAtom,
  setSelectedUnitAtom,
  setRefreshCurrentAtom,
} from "../../context/CareTeamAtoms";
import { ListItemButton } from "@mui/material";

interface IListItemProps {
  name: string;
}

export default function CareTeamListItem({ name }: IListItemProps) {
  const selectedUnit = useAtomValue(selectedUnitIdAtom);
  const setSelectedUnit = useSetAtom(setSelectedUnitAtom);
  const setSelectedTitleType = useSetAtom(setSelectedTitleTypeAtom);
  const setRefreshCurrent = useSetAtom(setRefreshCurrentAtom);

  const setStateSelected = (event: object, value: string) => {
    if (selectedUnit === value.split("(")[0]) {
      setRefreshCurrent(true);
      return;
    }
    setSelectedUnit(value.split("(")[0]);
    setSelectedTitleType("");
  };

  return (
    <ListItemButton
      sx={{ height: "30px" }}
      alignItems="center"
      autoFocus={selectedUnit === name?.split("(")[0]}
      selected={selectedUnit === name?.split("(")[0]}
      onClick={(event) => setStateSelected(event, name)}
    >
      <ListItemText
        primary={name}
        primaryTypographyProps={{
          color: "#000",
          fontSize: "16px",
          letterSpacing: 0,
        }}
      />
    </ListItemButton>
  );
}
