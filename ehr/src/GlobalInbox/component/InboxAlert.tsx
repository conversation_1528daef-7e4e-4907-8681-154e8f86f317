import { useEffect } from "react";

import Alert from "@mui/material/Alert";
import { useAtom } from "jotai/react";
import { showAlertAtom, alertInfoAtom } from "../atoms/GlobalInboxAtoms";
import { IconSuccessAlert, ErrorIcon } from "@cci-monorepo/common";

export default function InboxAlert(props: any) {
  const [alertInfo, setAlertInfo] = useAtom(alertInfoAtom);
  const [showAlert, setShowAlert] = useAtom(showAlertAtom);

  const successAlertStyle = {
    height: "40px",
    minWidth: "330px",
    marginLeft: "-100px",
    position: "absolute",
    zIndex: 300,
    border: "1px solid #A8D7A9",
    borderRadius: "4px",
    boxShadow: "0px 0px 15px 0px rgba(0, 0, 0, 0.20)",
    backgroundColor: "#E9F5E9",
    alignItems: "center",
    bottom: "20px",
    "& .MuiAlert-message": {
      height: "40px",
      fontSize: "14px",
      fontWeight: "700",
      display: "flex",
      alignItems: "center",
    },
  };

  const failureAlertStyle = {
    height: "40px",
    minWidth: "330px",
    marginLeft: "-100px",
    position: "absolute",
    zIndex: 300,
    border: "1px solid #E7A5A7",
    borderRadius: "4px",
    boxShadow: "0px 0px 15px 0px rgba(0, 0, 0, 0.20)",
    backgroundColor: "#F9E7E8",
    alignItems: "center",
    bottom: "20px",
    "& .MuiAlert-message": {
      height: "40px",
      fontSize: "14px",
      fontWeight: "700",
      display: "flex",
      alignItems: "center",
    },
  };

  useEffect(() => {
    if (showAlert > 0) {
      setTimeout(() => setShowAlert(0), 3000);
    }
    // eslint-disable-next-line
  }, [showAlert]);

  return (
    <Alert
      variant="outlined"
      severity="success"
      sx={showAlert === 1 ? successAlertStyle : failureAlertStyle}
      icon={showAlert === 1 ? <IconSuccessAlert /> : <ErrorIcon />}
      onClose={() => {
        setShowAlert(0);
        setAlertInfo("");
      }}
    >
      {alertInfo}
    </Alert>
  );
}
