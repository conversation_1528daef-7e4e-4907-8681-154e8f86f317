import { useState, useRef, useEffect, useContext } from "react";
import {
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  ClickAwayListener,
  useTheme,
  styled,
  Chip,
  InputAdornment,
  Box,
  IconButton,
} from "@mui/material";
import SearchIcon from "@cci-monorepo/common/mui-components/src/components/icons/SearchIcon";
import CloseIcon from "@mui/icons-material/Close";
import InputField from "@cci-monorepo/common/mui-components/src/components/inputfields/InputField";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import { IconSmallAvatar, IconDropdownArrow } from "../icons";
import { highlightText } from "../util/GlobalInboxUtil";
import { usePatientLookupQuery } from "../util/GlobalInboxHooks";
import { IPatientDataGridColumn } from "../util/GlobalInboxData";
import _ from "lodash";
import { InboxContext } from "@cci-monorepo/GlobalInbox/context/GlobalInboxContext";

const DropTable = styled(Table)(({ theme, ...props }: any) => ({
  "& .MuiTable-root": {
    width: "100%",
  },
  "& .MuiTableCell-root": {
    border: "none",
    font: "normal 15px Roboto",
    boxSizing: "border-box",
    height: "24px !important",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
    overflow: "hidden",
    margin: "0px",
    padding: "0px",
  },
  "& .MuiTableCell-head": {
    font: "700 15px Roboto",
    background: "#D8DCE3",
    height: "24px !important",
    maxHeight: "24px !important",
    boxSizing: "border-box",
    padding: "0px",
  },
  "& .MuiTableRow-root": {
    width: "100%",
    cursor: "pointer",
    "&:hover": { backgroundColor: "rgba(247, 247, 247, 0.50)" },
  },
  "& .MuiTableRow-root, .MuiTableCell-root": {
    borderBottom: "none",
  },
}));

const inputFieldSx = {
  "& .MuiInputBase-root": {
    maxHeight: "32px",
    backgroundColor: "#FFFFFF",
    "& .MuiInputBase-root": {
      maxHeight: "32px",
      fontSize: "14px",
      paddingTop: "4px",
      backgroundColor: "#FFFFFF",
      fieldset: {
        whiteSpace: "nowrap",
        border: "none",
      },
    },
    "& .MuiInputBase-input": {
      width: "100%",
      height: "32px",
      padding: "0px 16px 0px 0px",
      margin: "0px",
    },
    "&.Mui-focused fieldset": {
      border: "none",
    },
    "&.Mui-disabled fieldset": {
      border: "none",
    },
    "&.Mui-disabled.Mui-error fieldset": {
      border: "none",
    },
    "&.Mui-disabled": {
      color: "inherit",
      backgroundColor: "#e9e9e9",
      "& .MuiAutocomplete-endAdornment": {
        visibility: "visible",
      },
    },
  },
  "& .MuiAutocomplete-endAdornment": {
    marginRight: "-5px",
  },
  "& .Mui-focused": {
    maxHeight: "32px",
  },
};

const chipStyle = {
  "& .MuiChip-avatarSmall": {
    marginLeft: "0px !important",
  },
  margin: "0px",
  position: "relative" as "relative",
  height: "20px",
  borderRadius: "24px",
  color: "#000000",
  border: "1px solid #B1B1B1",
  backgroundColor: "#FFFFFF",
  padding: "0px",
};

const deleteIcon = <HighlightOffIcon sx={{ color: "#606060 !important" }} />;

function CustomTableSelect({ onSelectedPatient, onDeletePatient }: any) {
  const theme = useTheme();
  const [open, setOpen] = useState<boolean>(false);
  const [esStr, setEsStr] = useState<string>("");
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const anchorRef = useRef(null);

  const { ambFilter, envType, allAmbUnits } = useContext(InboxContext);
  const { data, isFetched, error, isError } = usePatientLookupQuery(
    esStr.toUpperCase(),
    envType === "Ambulatory" ? ambFilter.join(",") : "",
    envType === "Inpatient" ? allAmbUnits.join(",") : ""
  );

  const [filteredOptions, setFilteredOptions] = useState(data?.rows);

  useEffect(() => {
    setFilteredOptions(data?.rows);
  }, [data]);

  const handleOptionClick = (option: any) => {
    setSelectedItems([option]);
    setEsStr("");
    setOpen(false);
    onSelectedPatient(option);
  };

  const handleClickAway = () => {
    setOpen(false);
  };

  const handleInputFocus = () => {
    setOpen(true);
  };

  const handleClearInput = () => {
    setSelectedItems([]);
    setEsStr("");
    onDeletePatient();
  };

  const handleDeleteChip = (itemToDelete: any) => {
    setSelectedItems(
      selectedItems.filter((item: any) => item.dbpath !== itemToDelete.dbpath)
    );
    onDeletePatient();
  };

  const handleDropdownInput = () => {
    setOpen(!open);
  };

  const handleKeyDown = (e: any) => {
    if (e.key === "Backspace" && esStr === "") {
      setSelectedItems([]);
    }
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: "34px",
          display: "flex",
          alignItems: "center",
          border: open ? "1px solid #6599FF" : "1px solid #B1B1B1",
          borderRadius: "4px",
          "&:hover": {
            border: open ? "1px solid #6599FF" : "1px solid #000",
          },
        }}
      >
        <Box sx={{ flex: 1 }}>
          <InputField
            variant="outlined"
            size="small"
            sx={[{ width: "100%" }, inputFieldSx]}
            placeholder=""
            noBox={true}
            value={esStr}
            onFocus={handleInputFocus}
            inputRef={anchorRef}
            onChange={(e: any) => setEsStr(e.target.value)}
            onKeyDown={handleKeyDown}
            InputProps={{
              startAdornment: (
                <>
                  <InputAdornment
                    position="start"
                    sx={{ width: "16px", marginRight: "0px" }}
                  >
                    <SearchIcon
                      sx={{ fontSize: 16, color: "#CBCACA !important" }}
                    />
                  </InputAdornment>
                  <InputAdornment
                    position="start"
                    style={{ display: "flex", flexWrap: "nowrap", gap: 4 }}
                  >
                    {selectedItems.map((item: any, index: number) => (
                      <Chip
                        key={`chip-${index}`}
                        sx={chipStyle}
                        size="small"
                        onDelete={() => handleDeleteChip(item)}
                        label={item.name}
                        avatar={
                          <IconSmallAvatar
                            style={{ width: "18px", height: "18px" }}
                          />
                        }
                        deleteIcon={deleteIcon}
                      />
                    ))}
                  </InputAdornment>
                </>
              ),
              endAdornment: (
                <>
                  {(esStr || selectedItems?.length > 0) && (
                    <InputAdornment
                      position="end"
                      sx={{
                        marginLeft: "0px",
                        width: "24px",
                      }}
                    >
                      <IconButton
                        sx={{ p: "2px" }}
                        onClick={() => {
                          handleClearInput();
                        }}
                      >
                        <CloseIcon
                          sx={{
                            color: "#4C6EAF",
                            height: "20px",
                            width: "20px",
                          }}
                        />
                      </IconButton>
                    </InputAdornment>
                  )}
                  <InputAdornment
                    position="end"
                    sx={{
                      marginLeft: "0px",
                      width: "24px",
                    }}
                  >
                    <IconButton
                      sx={{ p: "2px" }}
                      onClick={() => {
                        handleDropdownInput();
                      }}
                    >
                      <IconDropdownArrow
                        style={{
                          height: "20px",
                          width: "20px",
                          transform: open ? "rotate(180deg)" : "rotate(0deg)",
                        }}
                      />
                    </IconButton>
                  </InputAdornment>
                </>
              ),
              style: { flexWrap: "nowrap" },
            }}
          />
        </Box>

        {open && (
          <Paper
            style={{
              position: "absolute",
              width: "100%",
              maxHeight: 300,
              overflow: "auto",
              zIndex: theme.zIndex.modal,
              marginTop: 4,
              top: "100%",
            }}
          >
            <Box sx={{ width: "100%", padding: "8px" }}>
              <DropTable>
                <TableHead>
                  <TableRow>
                    {data?.columns.map(
                      (column: IPatientDataGridColumn, idx: number) => {
                        return <TableCell>{column.headerName}</TableCell>;
                      }
                    )}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!_.isEmpty(filteredOptions) ? (
                    filteredOptions?.map((option: any, index: number) => (
                      <TableRow
                        key={`row-${index}`}
                        onClick={() => handleOptionClick(option)}
                        sx={{
                          backgroundColor: index % 2 ? "#F7F7F7" : "white",
                        }}
                      >
                        {data?.columns.map(
                          (col: IPatientDataGridColumn, index: number) => (
                            <TableCell key={`cell-${index}-${col}`}>
                              {highlightText(option[col.field], esStr)}
                            </TableCell>
                          )
                        )}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell key="cell-no-data" sx={{ color: "#999" }}>
                        No data
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </DropTable>
            </Box>
          </Paper>
        )}
      </Box>
    </ClickAwayListener>
  );
}

export default CustomTableSelect;
