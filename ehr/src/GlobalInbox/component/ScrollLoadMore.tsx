import React, { useCallback, RefObject, useEffect, useContext } from "react";
import { Box, CircularProgress } from "@mui/material";
import { InboxContext } from "../context/GlobalInboxContext";

interface IProps {
  children: React.ReactNode;
  dataType: string;
  containerRef: RefObject<HTMLElement>;
  threshold?: number;
  pageNum: number;
  setPageNum: React.Dispatch<React.SetStateAction<number>>;
  pageSize?: number;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  hasMore: boolean;
  setHasMore?: React.Dispatch<React.SetStateAction<boolean>>;
}

const ScrollLoadMore = ({
  children,
  dataType,
  containerRef,
  threshold = 50,
  pageNum,
  setPageNum,
  isLoading,
  setIsLoading,
  hasMore,
}: IProps) => {
  const { inboxConfig, dataList, setScrollTop } = useContext(InboxContext);

  useEffect(() => {
    if (containerRef.current && pageNum == 1 && !inboxConfig) {
      containerRef.current.scrollTop = 0;
    }
  }, [pageNum]);

  useEffect(() => {
    if (containerRef.current && inboxConfig && dataList.length > 0) {
      containerRef.current.scrollTop = inboxConfig.scrollTop;
    }
  }, [inboxConfig, dataList]);

  const loadMore = useCallback(async () => {
    if (
      dataType !== "message" ||
      !containerRef.current ||
      !hasMore ||
      isLoading
    )
      return;
    setIsLoading(true);
    setPageNum(pageNum + 1);
  }, [hasMore, isLoading]);

  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    setScrollTop(scrollTop);
    if (
      dataType === "message" &&
      scrollHeight - (scrollTop + clientHeight) < threshold
    ) {
      loadMore();
    }
  }, [loadMore]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: "100%",
        flex: 1,
        overflowY: "auto",
        scrollbarWidth: "thin",
        scrollbarGutter: "stable",
      }}
      onScroll={handleScroll}
    >
      {children}
      {dataType === "message" && isLoading && (
        <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
          <CircularProgress size={24} />
        </Box>
      )}
    </Box>
  );
};

export default ScrollLoadMore;
