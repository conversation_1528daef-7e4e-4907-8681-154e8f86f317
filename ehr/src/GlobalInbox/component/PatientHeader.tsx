import { ReactElement, FC } from "react";
import { styled } from "@mui/material/styles";
import { Box, Typography, Avatar } from "@mui/material";

const styledBox = {
  height: "100%",
  display: "flex",
  minWidth: "0px",
  justifyContent: "flex-start",
  alignItems: "center",
};

const xstyledBox = {
  width: "100%",
  display: "flex",
  minWidth: "0px",
  justifyContent: "start",
  alignItems: "center",
};

const yStyledBox = {
  height: "100%",
  minWidth: "0px",
  display: "flex",
  flexDirection: "column",
  justifyContent: "start",
  alignItems: "center",
  gap: "6px",
};

const StyledName = styled(Typography)({
  maxWidth: "220px",
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "20px",
  fontStyle: "normal",
  fontWQeight: 700,
  lineHeight: "22px",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
});

const StyledLabel = styled(Typography)({
  color: "rgba(0, 0, 0, 0.60)",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWQeight: 400,
  lineHeight: "18px",
  whiteSpace: "nowrap",
});

const StyledValue = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWQeight: 500,
  lineHeight: "18px",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
});

const PatientHeader: FC<any> = (props: any): ReactElement => {
  const itemView = (label: string, value: any) => {
    return (
      <>
        <StyledLabel sx={{ mr: "2px" }}>{`${label}:`}</StyledLabel>
        <StyledValue sx={{ mr: "4px" }}>{value ?? "N/A"}</StyledValue>
      </>
    );
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        minWidth: "0px",
        display: "flex",
        flexShrink: 0,
        flexGrow: 0,
      }}
    >
      <Box sx={{ ...styledBox, flex: 50, gap: "8px", padding: "8px" }}>
        <Avatar
          data-testid="patient-header-photo"
          alt="CCI Avatar"
          variant="rounded"
          sx={{
            borderRadius: props.infoStyle.borderRadius,
            height: props.infoStyle.boxWidth,
            width: props.infoStyle.boxWidth,
          }}
          src={typeof props.photo === "string" ? props.photo : undefined}
        >
          {typeof props.photo === "string" ? undefined : props.photo}
        </Avatar>
        <Box
          sx={{
            height: "100%",
            minWidth: "0px",
            display: "flex",
            flexDirection: "column",
            gap: "6px",
          }}
        >
          <StyledName>{props?.patInfo?.patName || "N/A"}</StyledName>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
            }}
          >
            {itemView("DOB", props.patInfo?.dob)}
            {itemView("SEX", props.patInfo?.sex)}
          </Box>
        </Box>
        <Box
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "top",
          }}
        >
          <StyledLabel>{props?.patInfo?.unit || "N/A"}</StyledLabel>
        </Box>
      </Box>
      <Box sx={{ ...styledBox, flex: 69, gap: "24px", padding: "8px" }}>
        <Box sx={{ ...yStyledBox, flex: 1 }}>
          <Box sx={{ ...xstyledBox }}>
            {itemView("MRN", props.patInfo?.mrn)}
          </Box>
          <Box sx={{ ...xstyledBox }}>
            {itemView("Diagnosis", props.patInfo?.diagnosis)}
          </Box>
        </Box>
        <Box sx={{ ...yStyledBox, flex: 1 }}>
          <Box sx={{ ...xstyledBox }}>
            {itemView("Emergency Contact", props.patInfo?.emergencyContact)}
          </Box>
          <Box sx={{ ...xstyledBox }}>
            {itemView("Contact Number", props.patInfo?.contactNum)}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
export default PatientHeader;
