import { useState, useRef, useEffect, useContext } from "react";
import {
  Paper,
  Checkbox,
  ClickAwayListener,
  useTheme,
  Chip,
  Box,
  IconButton,
  Typography,
} from "@mui/material";
import SearchIcon from "@cci-monorepo/common/mui-components/src/components/icons/SearchIcon";
import CloseIcon from "@mui/icons-material/Close";
import InputField from "@cci-monorepo/common/mui-components/src/components/inputfields/InputField";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import { IconSmallAvatar, IconDropdownArrow } from "../icons";
import { useRecipientLookupQuery } from "../util/GlobalInboxHooks";
import { StyledNoData } from "./InboxListView";
import { InboxContext } from "../context/GlobalInboxContext";
import { CciDropdownChoice } from "@cci-monorepo/common/mui-components/src/export";
import { highlightText } from "../util/GlobalInboxUtil";
import _ from "lodash";

const inputFieldSx = {
  "& .MuiInputBase-root": {
    minHeight: "24px !important",
    backgroundColor: "#FFFFFF",
    padding: "0px",
    "& .MuiInputBase-root": {
      minHeight: "24px !important",
      fontSize: "14px",
      backgroundColor: "#FFFFFF",
      fieldset: {
        whiteSpace: "nowrap",
        border: "none",
      },
    },
    "& .MuiInputBase-input": {
      width: "100%",
      minHeight: "24px !important",
      padding: "0px 16px 0px 4px",
      margin: "0px",
    },
    "&.Mui-focused fieldset": {
      border: "none",
    },
    "&.Mui-disabled fieldset": {
      border: "none",
    },
    "&.Mui-disabled.Mui-error fieldset": {
      border: "none",
    },
    "&.Mui-disabled": {
      color: "inherit",
      backgroundColor: "#e9e9e9",
      "& .MuiAutocomplete-endAdornment": {
        visibility: "visible",
      },
    },
  },
  "& .MuiOutlinedInput-root": {
    padding: "0px !important",
  },
};

const chipStyle = {
  "& .MuiChip-avatarSmall": {
    marginLeft: "0px !important",
  },
  margin: "0px",
  position: "relative" as "relative",
  height: "20px",
  borderRadius: "24px",
  color: "#000000",
  border: "1px solid #B1B1B1",
  backgroundColor: "#FFFFFF",
  padding: "0px",
};

const optionStyle = {
  width: "100%",
  height: "28px",
  backgroundColor: "#FFF",
  display: "flex",
  alignItems: "center",
};

const titleStyle = {
  height: "28px",
  fontColor: "#000",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "28px",
  textAlign: "center",
};

const choiceStyle = {
  fontColor: "#000",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normarl",
  textAlign: "center",
};

const deleteIcon = <HighlightOffIcon sx={{ color: "#606060 !important" }} />;

interface IGroup {
  title: string;
  choices: CciDropdownChoice[];
}

function CustomMultiSelect({
  onSelectedPatient,
  onDeletePatient,
  disabled = false,
}: any) {
  const theme = useTheme();
  const [open, setOpen] = useState<boolean>(false);
  const anchorRef = useRef(null);
  const [keyword, setKeyword] = useState<string>("");
  const [debouncedKeyword, setDebouncedKeyword] = useState("");
  const [recipientGroups, setRecipientGroups] = useState<IGroup[]>([]);
  const { dbpath, selectedToPersons, setSelectedToPersons } =
    useContext(InboxContext);

  const { data: recipientData } = useRecipientLookupQuery(
    dbpath,
    debouncedKeyword
  );

  useEffect(() => {
    convertChoicesFromData(recipientData);
  }, [recipientData]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedKeyword(keyword);
    }, 500);
    return () => clearTimeout(timer);
  }, [keyword]);

  const convertChoicesFromData = (data: any) => {
    let groups: IGroup[] = [];
    if (data?.careteam && data.careteam.length > 0) {
      let choices: CciDropdownChoice[] = [];
      data.careteam.map((item: any) =>
        choices.push({
          group: "Care Team",
          label: item.name,
          id: item.staffID,
          tagLabel: item.licenseType,
        })
      );
      groups.push({
        title: "Care Team",
        choices: choices,
      });
    }
    if (data?.groups && data.groups.length > 0) {
      let choices2: CciDropdownChoice[] = [];
      data.groups.map((item: any) =>
        choices2.push({
          group: "Groups",
          label: item.name,
          id: Infinity,
          tagLabel: "",
        })
      );
      groups.push({
        title: "Groups",
        choices: choices2,
      });
    }
    if (data?.allstaff && data.allstaff.length > 0) {
      let choices3: CciDropdownChoice[] = [];
      data.allstaff.map((item: any) =>
        choices3.push({
          group: "All Staff",
          label: item.name,
          id: item.staffID,
          tagLabel: item.licenseType,
        })
      );
      groups.push({
        title: "All Staff",
        choices: choices3,
      });
    }
    setRecipientGroups(groups);
  };

  const handleClickAway = () => {
    setOpen(false);
    setKeyword("");
  };

  const handleInputFocus = () => {
    setOpen(true);
  };

  const handleClearInput = () => {
    setSelectedToPersons([]);
    setKeyword("");
  };

  const handleDeleteChip = (choice: any) => {
    setSelectedToPersons(
      selectedToPersons.filter((item: any) => item.label !== choice.label)
    );
  };

  const handleDropdownInput = () => {
    setOpen(!open);
  };

  const handleKeyDown = (e: any) => {
    if (e.key === "Backspace" && keyword === "") {
      setSelectedToPersons(selectedToPersons.slice(0, -1));
    }
  };

  const handleItemSelect = (e: any, choice: any) => {
    e.stopPropagation();
    setSelectedToPersons((prev) =>
      prev.map((item) => item.label).includes(choice.label)
        ? prev.filter((ch) => ch.label !== choice.label)
        : [...prev, choice]
    );
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box
        sx={{
          position: "relative",
          width: "100%",
          display: "flex",
          alignItems: "flex-top",
          border: open ? "1px solid #6599FF" : "1px solid #B1B1B1",
          borderRadius: "4px",
          "&:hover": {
            border: open ? "1px solid #6599FF" : "1px solid #000",
          },
          padding: "4px",
          cursor: "pointer",
        }}
      >
        {!disabled && (
          <IconButton sx={{ p: "4px" }}>
            <SearchIcon
              sx={{
                height: "16px",
                width: "16px",
                color: "#CBCACA !important",
              }}
            />
          </IconButton>
        )}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexWrap: "wrap",
            flexDirection: "row",
            alignItems: "flex-top",
            gap: "4px",
            minHeight: "24px",
          }}
        >
          {selectedToPersons.map((person: any, index: number) => (
            <Box sx={{ height: "24px", display: "flex", alignItems: "center" }}>
              <Chip
                key={`chip-${index}`}
                sx={chipStyle}
                size="small"
                onDelete={disabled ? undefined : () => handleDeleteChip(person)}
                label={person.label}
                avatar={
                  <IconSmallAvatar style={{ width: "18px", height: "18px" }} />
                }
                deleteIcon={deleteIcon}
              />
            </Box>
          ))}
          <Box sx={{ flex: 1 }}>
            <InputField
              variant="outlined"
              size="small"
              sx={[{ width: "100%" }, inputFieldSx]}
              placeholder=""
              noBox={true}
              value={keyword}
              onFocus={handleInputFocus}
              inputRef={anchorRef}
              onChange={(e: any) => setKeyword(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </Box>
        </Box>

        {!disabled && (
          <>
            <IconButton
              sx={{ p: "2px" }}
              onClick={() => {
                handleClearInput();
              }}
            >
              <CloseIcon
                sx={{
                  color: "#4C6EAF",
                  height: "20px",
                  width: "20px",
                }}
              />
            </IconButton>

            <IconButton
              sx={{ p: "2px" }}
              onClick={() => {
                handleDropdownInput();
              }}
            >
              <IconDropdownArrow
                style={{
                  height: "20px",
                  width: "20px",
                  transform: open ? "rotate(180deg)" : "rotate(0deg)",
                }}
              />
            </IconButton>
          </>
        )}
        {open && !disabled && (
          <Paper
            style={{
              position: "absolute",
              width: "100%",
              maxHeight: 300,
              overflow: "auto",
              scrollbarWidth: "thin",
              zIndex: theme.zIndex.modal,
              marginTop: 4,
              top: "100%",
              left: 0,
              right: 0,
            }}
          >
            <Box
              sx={{
                width: "100%",
                height: "100%",
                display: "flex",
                flexDirection: "column",
                padding: "8px",
                cursor: "pointer",
              }}
            >
              {!_.isEmpty(recipientGroups) ? (
                recipientGroups?.map((group: IGroup, index: number) => (
                  <>
                    <Box key={`row-${index}`} sx={optionStyle}>
                      <Typography
                        component="span"
                        sx={{ ...titleStyle, paddingLeft: "8px" }}
                      >
                        {group.title}
                      </Typography>
                    </Box>
                    {group?.choices.map((choice: CciDropdownChoice) => (
                      <Box
                        key={`${group.title}-${index}`}
                        sx={{
                          ...optionStyle,
                          paddingLeft: "24px",
                          gap: "14px",
                          "&:hover": {
                            backgroundColor: "rgba(249,238,188, 0.50)",
                          },
                        }}
                        onClick={(e) => handleItemSelect(e, choice)}
                      >
                        <Checkbox
                          edge="end"
                          sx={{
                            "&.MuiCheckbox-root": {
                              padding: "0px",
                            },
                            "&.Mui-checked": {
                              color: "#319436",
                            },
                          }}
                          checked={selectedToPersons
                            .map((person) => person.label)
                            .includes(choice.label)}
                          onClick={(e) => handleItemSelect(e, choice)}
                        />
                        <Typography sx={choiceStyle}>
                          {highlightText(choice.label, keyword)}
                        </Typography>
                      </Box>
                    ))}
                  </>
                ))
              ) : (
                <Box>
                  <StyledNoData sx={{ fontSize: "14px" }}>
                    No Options
                  </StyledNoData>
                </Box>
              )}
            </Box>
          </Paper>
        )}
      </Box>
    </ClickAwayListener>
  );
}

export default CustomMultiSelect;
