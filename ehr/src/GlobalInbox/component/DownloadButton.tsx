import { useState } from "react";
import { Box } from "@mui/material";
import { showAlertAtom, alertInfoAtom } from "../atoms/GlobalInboxAtoms";
import { useSetAtom } from "jotai";

const StyledButton = {
  background: "#FFF",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
};

const DownloadButton = (props: any) => {
  const [loading, setLoading] = useState(false);
  const setShowAlert = useSetAtom(showAlertAtom);
  const setAlertInfo = useSetAtom(alertInfoAtom);

  const handleDownload = async () => {
    if (loading) return;
    setLoading(true);
    try {
      const response = await fetch(props.url);
      if (!response.ok) throw new Error("Download failed");
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = props.fileName;
      document.body.appendChild(link);
      link.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error("download error:", error);
      setShowAlert(2);
      setAlertInfo("Download failed, please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={StyledButton} onClick={handleDownload}>
      {props.children}
    </Box>
  );
};

export default DownloadButton;
