import {
  ReactElement,
  FC,
  useEffect,
  useContext,
  useState,
  useMemo,
} from "react";
import { styled } from "@mui/material/styles";
import {
  Box,
  Typography,
  Menu,
  RadioGroup,
  Radio,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useSet<PERSON><PERSON> } from "jotai";
import {
  alertInfo<PERSON>tom,
  showAlert<PERSON>tom,
  showDiscardDialog<PERSON>tom,
} from "../atoms/GlobalInboxAtoms";
import { InboxContext } from "../context/GlobalInboxContext";
import { IconButton } from "@cci/mui-components";
import {
  IconNewMessage,
  IconSearchSettings,
  IconMenuReceived,
  IconMenuSend,
  IconMenuArchived,
  IconMenuLabResults,
  IconMenuFolder,
  IconMenuReports,
  IconMenuRadiology,
  IconMenuConsults,
  IconMenuRx,
  IconMenuOrderEntry,
  IconMenuNotes,
  IconMenuSchedule,
  IconMenuEncounters,
} from "../icons";
import _ from "lodash";
import {
  INBOX_TYPE,
  MESSAGE_TYPE,
  NOTIFICATION_TYPE,
  INBOX_CONFIG,
} from "../constants/Constants";
import {
  useGetUserPreferenceQuery,
  useSaveUserPreferenceMutation,
} from "../util/GlobalInboxHooks";
import { EDITABLE_REG_SECTIONS } from "@cci-monorepo/Charge/ChargeServices/ChargePatient/registration/constants";
import { useGetChoiceListQueries } from "@cci-monorepo/common";

export interface IMenuItem {
  type: string;
  pType?: string;
  name: string;
  total: number;
  unread: number;
  data: any[];
}

const StyledHeader = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "normal",
});

const StyledItem = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
});

const StyledUnread = styled(Typography)({
  color: "rgba(0, 0, 0, 0.60)",
  fontFamily: "Roboto",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "normal",
});

const StyledTitle = styled(Typography)({
  height: "28px",
  padding: "0px 16px",
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "28px",
});

const Divider = styled("div")({
  margin: "13px 16px",
  background: "#C4C4C4",
  height: "2px",
  borderRadius: "1px",
});

const radioStyle = {
  "&.Mui-disabled": {
    color: "#B1B1B1",
  },
  "&.Mui-checked": {
    color: "#319436",
  },
  "&.Mui-checked.Mui-disabled": {
    color: "#D6D6D6",
  },
};

export const menuImage = (name: string, isGrayscale: boolean) => {
  let sx = isGrayscale ? { filter: "grayscale(100%) opacity(0.5)" } : {};
  if (_.includes([MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED], name)) {
    return <IconMenuReceived style={sx} />;
  } else if (name === MESSAGE_TYPE.SENT) {
    return <IconMenuSend style={sx} />;
  } else if (name === MESSAGE_TYPE.ARCHIVED) {
    return <IconMenuArchived style={sx} />;
  } else if (name === NOTIFICATION_TYPE.LAB_RESULTS) {
    return <IconMenuLabResults style={sx} />;
  } else if (name === NOTIFICATION_TYPE.REPORTS) {
    return <IconMenuReports style={sx} />;
  } else if (name === NOTIFICATION_TYPE.IMAGING_REPORTS) {
    return <IconMenuRadiology style={sx} />;
  } else if (name === NOTIFICATION_TYPE.CONSULTS) {
    return <IconMenuConsults style={sx} />;
  } else if (name === NOTIFICATION_TYPE.RX_REQUESTS) {
    return <IconMenuRx style={sx} />;
  } else if (name === NOTIFICATION_TYPE.ORDERS) {
    return <IconMenuOrderEntry style={sx} />;
  } else if (name === NOTIFICATION_TYPE.NOTES) {
    return <IconMenuNotes style={sx} />;
  } else if (name === NOTIFICATION_TYPE.SCHEDULING_REQUESTS) {
    return <IconMenuSchedule style={sx} />;
  } else if (name === NOTIFICATION_TYPE.ENCOUNTERS) {
    return <IconMenuEncounters style={sx} />;
  } else {
    return <IconMenuFolder style={sx} />;
  }
};

const InboxMenuView: FC<any> = (props): ReactElement => {
  const { onItemClick, queryClient } = props;
  const setShowAlert = useSetAtom(showAlertAtom);
  const setAlertInfo = useSetAtom(alertInfoAtom);
  const setShowDiscardDialog = useSetAtom(showDiscardDialogAtom);
  const [staffID] = useState<string>(Cci.util.Staff.getSid());
  const [anchorEl, setAnchorEl] = useState(null);
  const {
    detailPanelStatus,
    setDetailPanelStatus,
    hasDraft,
    unitFilter,
    setUnitFilter,
    ambFilter,
    setAmbFilter,
    menuData,
    selectedMenuIdx,
    setSelectedMenuIdx,
    setSelectedIdx,
    setSelectedSort,
    setSort,
    setSortBy,
    inboxConfig,
    dataType,
    setDataType,
    setPageNum,
    setHasMore,
    envType,
    setEnvType,
    allAmbUnits,
    setAllAmbUnits,
  } = useContext(InboxContext);

  const { mutate: saveUserPreference } = useSaveUserPreferenceMutation();
  const { data: userPreferences } = useGetUserPreferenceQuery(staffID);
  const facilityLists = useGetChoiceListQueries([{ chc: "facility" }]);

  let radioGroups = useMemo(() => {
    let allGroups = [
      {
        title: "Inpatient",
        options: [
          { value: "All", label: "All", type: "radio" },
          { value: "Active", label: "Active", type: "radio" },
          { value: "Discharge", label: "Discharge", type: "radio" },
        ],
      },
    ];
    if (facilityLists?.facility?.data) {
      let options: any[] = [];
      let ambUnits: any[] = [];
      facilityLists.facility.data.forEach((item: any) => {
        if (item["Facility Type"] === "Outpatient") {
          options.push({
            value: item["Facility Abbreviation"],
            label: item["Facility Name"],
            type: "checkbox",
          });
          ambUnits.push(item["Facility Abbreviation"]);
        }
      });
      if (options.length > 0) {
        const ambGroups = {
          title: "Ambulatory",
          options: options,
        };
        allGroups.push(ambGroups);
      }
      if (allAmbUnits.length === 0 && ambUnits.length > 0) {
        setAllAmbUnits(ambUnits);
        if (envType != "" && ambFilter.length === 0) {
          setAmbFilter(ambUnits);
        }
      }
    }
    return allGroups;
  }, [facilityLists.facility]);

  useEffect(() => {
    if (inboxConfig) {
      if (inboxConfig.selectedMenuIdx) {
        setUnitFilter(inboxConfig.unitFilter);
        setSelectedSort(inboxConfig.selectedSort);
        setSort(inboxConfig.sort);
        setSortBy(inboxConfig.sortBy);
        setSelectedMenuIdx(inboxConfig.selectedMenuIdx);
        setSelectedIdx(inboxConfig.selectedIdx);
        setDataType(inboxConfig.type);
        setEnvType(inboxConfig.envType);
        setAmbFilter(inboxConfig.ambFilter);
      }
      Cci?.RunTime?.removeCache(INBOX_CONFIG);
    }
    return () => resetQuery();
  }, []);

  useEffect(() => {
    if (inboxConfig) {
      if (
        dataType === INBOX_TYPE.MESSAGE ||
        (dataType === INBOX_TYPE.NOTIFICATION &&
          inboxConfig.selectedMenuIdx < menuData.length)
      ) {
        onItemClick(menuData[inboxConfig.selectedMenuIdx]);
      }
    }
  }, [menuData, dataType]);

  useEffect(() => {
    if (userPreferences === null) return;
    let unitFilterValue = "All";
    let ambFilterValue = allAmbUnits;
    let envTypeValue = "Inpatient";
    if (userPreferences.length > 0) {
      userPreferences.map((userPreference: any) => {
        if (userPreference.type === "filter") {
          if (userPreference.prefKey === "Inpatient") {
            unitFilterValue = userPreference.keyValue;
          } else if (userPreference.prefKey === "Ambulatory") {
            ambFilterValue = userPreference.keyValue.split(",");
          }
        } else if (userPreference.type === "env") {
          envTypeValue = userPreference.keyValue;
        }
      });
    }
    if (unitFilter != unitFilterValue) {
      setUnitFilter(unitFilterValue);
    }
    if (ambFilter != ambFilterValue) {
      setAmbFilter(ambFilterValue);
    }
    if (envType != envTypeValue) {
      setEnvType(envTypeValue);
    }
  }, [userPreferences, allAmbUnits]);

  const resetQuery = () => {
    queryClient.resetQueries({
      queryKey: ["getNotifications", staffID],
      exact: false,
    });
  };

  const handleChange = (event: any, title: string) => {
    event.stopPropagation();
    if (hasDraft()) {
      setShowDiscardDialog(
        detailPanelStatus[detailPanelStatus.length - 1] === 0 ? 1 : 2
      );
      return;
    }
    setPageNum(1);
    setHasMore(false);
    setUnitFilter(event.target.value);
    handleClose(event.target.value);
  };

  const handleAmbChange = (e: any, option: any) => {
    e.stopPropagation();
    if (e.target.checked) {
      setAmbFilter([...ambFilter, option.value]);
    } else {
      const filterResult = ambFilter.filter((v) => v !== option.value);
      if (filterResult.length > 0) {
        setAmbFilter(filterResult);
      } else {
        setShowAlert(2);
        setAlertInfo("At least one option remains");
      }
    }
  };

  const handleNewMessageClick = (e: any) => {
    e.stopPropagation();
    if (hasDraft()) {
      setShowDiscardDialog(2);
      return;
    }
    if (detailPanelStatus[detailPanelStatus.length - 1] !== 0) {
      setDetailPanelStatus([...detailPanelStatus, 0]);
    }
  };

  const handleSettingsClick = (event: any) => {
    event.stopPropagation();
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleMenuClick = (menu: IMenuItem, index: number) => {
    if (selectedMenuIdx === index) return;
    if (hasDraft()) {
      setShowDiscardDialog(
        detailPanelStatus[detailPanelStatus.length - 1] === 0 ? 1 : 2
      );
      return;
    }
    if (!_.includes([INBOX_TYPE.ACTION, INBOX_TYPE.HEADER], menu.type)) {
      setSelectedMenuIdx(index);
      onItemClick(menu);
    }
  };

  const handleClose = (unit: string) => {
    const prefKey = envType;
    let keyValue = unit;
    if (envType === "Ambulatory") {
      keyValue = ambFilter.join(",");
    }
    saveUserPreference({
      userID: staffID,
      userType: 0,
      type: "filter",
      prefKey: prefKey,
      keyValue: keyValue,
    });
    saveUserPreference({
      userID: staffID,
      userType: 0,
      type: "env",
      prefKey: "envType",
      keyValue: envType,
    });
    setAnchorEl(null);
  };

  const handleEncTypeChange = (e: any) => {
    const type = e.target.value;
    setEnvType(type);
    resetQuery();
  };

  const itemView = (menu: IMenuItem, i: number) => {
    return (
      <Box
        key={i}
        sx={{
          width: "100%",
          height: menu.type === INBOX_TYPE.HEADER ? "28px" : "32px",
          marginTop: menu.type === INBOX_TYPE.HEADER ? "16px" : "0px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderTop:
            menu.type !== INBOX_TYPE.ACTION && menu.type !== INBOX_TYPE.HEADER
              ? "1px solid #F7F7F7"
              : "none",
          cursor: _.includes([INBOX_TYPE.ACTION, INBOX_TYPE.HEADER], menu.type)
            ? "default"
            : "pointer",
          background:
            selectedMenuIdx === i &&
            !_.includes([INBOX_TYPE.ACTION, INBOX_TYPE.HEADER], menu.type)
              ? "#F9EEBC"
              : "#FFF",
          "&:hover": {
            background: !_.includes(
              [INBOX_TYPE.ACTION, INBOX_TYPE.HEADER],
              menu.type
            )
              ? "rgba(249,238,188, 0.50)"
              : "#FFF",
          },
        }}
        onClick={() => handleMenuClick(menu, i)}
      >
        {menu.type === INBOX_TYPE.ACTION ? (
          <IconButton
            data-testid="new-message-btn"
            color="primary"
            onClick={(e: any) => handleNewMessageClick(e)}
            sx={{
              width: "131px",
              height: "32px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <IconNewMessage style={{ width: "24px", height: "24px" }} />
            <Typography
              sx={{
                color: "#FFF",
                textAlign: "center",
                fontFamily: "Roboto",
                fontSize: "14px",
                fontStyle: "normal",
                fontWeight: "700",
                lineHeight: "normal",
                marginLeft: "4px",
                marginRight: "6px",
              }}
            >
              New Message
            </Typography>
          </IconButton>
        ) : menu.type === INBOX_TYPE.HEADER ? (
          <StyledHeader sx={{ ml: "6px" }}>{menu.name}</StyledHeader>
        ) : (
          <Box
            sx={{
              display: "flex",
              height: "100%",
              alignItems: "center",
              marginLeft:
                menu.type === INBOX_TYPE.NOTIFICATION ? "40px" : "8px",
              marginRight: "4px",
            }}
          >
            <Box
              sx={{
                width: "24px",
                height: "24px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {menuImage(menu.name, false)}
            </Box>
            <StyledItem sx={{ ml: "8px" }}>{menu.name}</StyledItem>
          </Box>
        )}
        {menu.type === INBOX_TYPE.ACTION && (
          <>
            <IconButton
              data-testid="search-settings-btn"
              color="secondary"
              onClick={(e: any) => handleSettingsClick(e)}
              sx={{
                width: "32px",
                height: "32px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <IconSearchSettings />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={(e) => handleClose(unitFilter)}
              disableAutoFocusItem={true}
              aria-hidden={!Boolean(anchorEl)}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              sx={{
                "& .MuiPaper-root": {
                  minWidth: 123,
                },
              }}
            >
              <RadioGroup
                value={envType}
                onChange={(e) => handleEncTypeChange(e)}
                sx={{}}
              >
                {radioGroups.map((group, index) => (
                  <Box
                    key={group.title}
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <FormControlLabel
                      key={group.title}
                      value={group.title}
                      control={
                        <Radio
                          onDoubleClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                          sx={radioStyle}
                          //disableRipple
                        />
                      }
                      label={group.title}
                      sx={{
                        ml: 0.5,
                        "& .MuiRadio-root": {
                          padding: "3px 4px 3px 9px",
                          "&.Mui-focusVisible": { outline: "none" },
                        },
                        "&:hover": {
                          backgroundColor: "#F9EEBC",
                        },
                      }}
                    />
                    {group.title === "Inpatient" ? (
                      <Box>
                        <RadioGroup
                          value={unitFilter}
                          onChange={(e) => handleChange(e, group.title)}
                          sx={{
                            marginLeft: "20px",
                          }}
                        >
                          {group.options.map((option) => (
                            <FormControlLabel
                              key={option.value}
                              value={option.value}
                              control={
                                <Radio
                                  disabled={envType === "Ambulatory"}
                                  onDoubleClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                  }}
                                  sx={radioStyle}
                                  //disableRipple
                                />
                              }
                              label={option.label}
                              sx={{
                                ml: 0.5,
                                "& .MuiRadio-root": {
                                  padding: "3px 4px 3px 9px",
                                  "&.Mui-focusVisible": { outline: "none" },
                                },
                                "& .MuiCheckbox-root": {
                                  padding: "3px 4px 3px 9px",
                                  "&.Mui-focusVisible": { outline: "none" },
                                },
                                "&:hover": {
                                  backgroundColor: "#F9EEBC",
                                },
                              }}
                            />
                          ))}
                        </RadioGroup>
                        {radioGroups.length > 1 && <Divider />}
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "start",
                          flexDirection: "column",
                          marginLeft: "20px",
                        }}
                      >
                        {group.options.map((option) => (
                          <FormControlLabel
                            key={option.value}
                            value={option.value}
                            control={
                              <Checkbox
                                disabled={envType === "Inpatient"}
                                checked={ambFilter.includes(option.value)}
                                onChange={(e) => handleAmbChange(e, option)}
                                sx={radioStyle}
                                //disableRipple
                              />
                            }
                            label={option.label}
                            sx={{
                              width: "100%",
                              ml: 0.5,
                              "& .MuiRadio-root": {
                                padding: "3px 4px 3px 9px",
                                "&.Mui-focusVisible": { outline: "none" },
                              },
                              "& .MuiCheckbox-root": {
                                padding: "3px 4px 3px 9px",
                                "&.Mui-focusVisible": { outline: "none" },
                              },
                              "&:hover": {
                                backgroundColor: "#F9EEBC",
                              },
                            }}
                          />
                        ))}
                      </Box>
                    )}
                  </Box>
                ))}
              </RadioGroup>
            </Menu>
          </>
        )}
        {_.includes(
          [INBOX_TYPE.MESSAGE, "group", INBOX_TYPE.NOTIFICATION],
          menu.type
        ) &&
          (menu.type === INBOX_TYPE.MESSAGE ? (
            menu.unread > 0 ? (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "0px 8px",
                  marginRight: "6px",
                  border: "1px solid rgba(0, 0, 0, 0.60)",
                  borderRadius: menu.unread > 9 ? "8px" : "16px",
                }}
              >
                <StyledUnread>{menu.unread}</StyledUnread>
              </Box>
            ) : (
              <></>
            )
          ) : (
            <StyledUnread sx={{ mr: "14px" }}>{menu.total}</StyledUnread>
          ))}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        width: "298px",
        padding: "8px",
        display: "flex",
        flexDirection: "column",
        flexShrink: 0,
        borderRadius: "8px",
        background: "#FFF",
        boxShadow: "0px 2px 6px 0px rgba(0, 0, 0, 0.15)",
        overflowY: "auto",
        scrollbarWidth: "thin",
      }}
    >
      {menuData &&
        menuData.map((menu: IMenuItem, i: number) => itemView(menu, i))}
    </Box>
  );
};
export default InboxMenuView;
