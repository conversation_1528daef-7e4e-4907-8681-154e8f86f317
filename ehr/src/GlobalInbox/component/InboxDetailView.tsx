import React, {
  ReactElement,
  FC,
  useRef,
  useEffect,
  useState,
  useContext,
} from "react";
import { showAlert<PERSON>tom, alertInfoAtom } from "../atoms/GlobalInboxAtoms";
import { useSet<PERSON>tom, useAtom } from "jotai";
import {
  showDiscardDialogAtom,
  discard<PERSON>raft<PERSON>tom,
} from "../atoms/GlobalInboxAtoms";
import { Box, Typography, Chip, TextField, Avatar } from "@mui/material";
import { styled } from "@mui/material/styles";
import {
  IconSend,
  IconBtnAttachment,
  IconDelete,
  IconReply,
  IconReplyAll,
  IconForward,
  IconPrint,
  IconExternalLink,
  IconDoctor,
  IconLocation,
  IconSchedule,
  IconApprove,
  IconDeny,
} from "../icons";
import { PatientPhotoIcon, CciPdfViewer } from "@cci/mui-components";
import { SingleDropdownMenu } from "@cci/mui-components";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import CustomTableSelect from "./CustomTableSelect";
import CustomMultiSelect from "./CustomMultiSelect";
import { IconSmallAvatar, IconProfile } from "../icons";
import PatientHeader from "./PatientHeader";
import {
  StyledNoData,
  StyledFrom,
  StyledFromSuffix,
  StyledTime,
  StyledButton,
  StyledReview,
  labView,
  notHasPerm,
  handleLinkClick,
} from "./InboxListView";
import { menuImage } from "./InboxMenuView";
import { InboxContext } from "../context/GlobalInboxContext";
import { useGetChoiceListQueries } from "@cci-monorepo/common";
import {
  useNewMessageMutation,
  useUploadFileMutation,
  useDownloadAttachmentMutation,
  markReviewed,
  saveOrderAction,
  useRxRefillRequestMutation,
} from "../util/GlobalInboxHooks";
import {
  getPatApptData,
  updateAppointment,
} from "@cci-monorepo/Scheduling/util/DataAPI";
import _, { isEmpty } from "lodash";
import { ORDER_TYPE } from "@cci-monorepo/LabsReview/config/value";
import { signNote, coSignNote } from "@cci-monorepo/Provider/util/ProviderData";
import { setEhrDataDirty } from "../util/GlobalInboxUtil";
import { b64toBlob } from "@cci-monorepo/Registration/util/Transform";
import { highlightText } from "../util/GlobalInboxUtil";
import {
  INBOX_TYPE,
  MESSAGE_TYPE,
  NOTIFICATION_TYPE,
} from "../constants/Constants";
import {
  needUpdateCalendarAtom,
  rescheduleApptDataAtom,
  rescheduleOpenAtom,
} from "@cci-monorepo/Scheduling/context/SchedulingAtoms";
import {
  cancelReschedule,
  getEncounterData,
  getPatInfo,
} from "../util/GlobalInboxData";
import { stepIndexAtom } from "@cci-monorepo/Registration/state/atoms/scheduling";
import { useGetGuarantorsQuery } from "@cci-monorepo/Registration";
import DownloadButton from "./DownloadButton";

export const StyledLabel = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontStyle: "normal",
  fontWeight: "500",
  lineHeight: "normal",
});

const StyledAction = styled(Typography)({
  color: "#3C6CBB",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
});

const StyledContent = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "15px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
});

const StyledSubject = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "24px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "24px",
});

const StyledSubjectSmall = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
});

export const StyledComment = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "18px",
});

const StyledSize = styled(Typography)({
  color: "#7C7C7C",
  fontFamily: "Roboto",
  fontSize: "12px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
  padding: "4px 8px",
});

export const StyledLabel2 = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWeight: "500",
  lineHeight: "24px",
});

const styleCard = {
  width: "100%",
  minWidth: "0px",
  display: "flex",
  alignItems: "center",
  borderRadius: "8px",
  background: "#FFF",
  boxShadow: "0px 0px 5px 0px rgba(0, 0, 0, 0.25)",
};

const chipStyle = {
  "& .MuiChip-avatarSmall": {
    marginLeft: "0px !important",
  },
  margin: "0px",
  position: "relative" as "relative",
  height: "20px",
  borderRadius: "24px",
  color: "#000000",
  border: "1px solid #B1B1B1",
  backgroundColor: "#FFFFFF",
  padding: "0px",
};

const chipStyle2 = {
  margin: "0px",
  position: "relative" as "relative",
  height: "24px",
  borderRadius: "18px",
  color: "#000000",
  fontSize: "14px",
  fontWeight: "500",
  border: "1px solid #B1B1B1",
  backgroundColor: "#FFFFFF",
  padding: "0px 8px",
};

const chipStyle3 = {
  margin: "0px",
  position: "relative" as "relative",
  height: "24px",
  color: "#000000",
  fontSize: "14px",
  fontWeight: "500",
  border: "none",
  backgroundColor: "#FFFFFF",
  padding: "0px",
};

const InboxDetailView: FC<any> = (props: any): ReactElement => {
  const setShowAlert = useSetAtom(showAlertAtom);
  const setAlertInfo = useSetAtom(alertInfoAtom);
  const {
    detailPanelStatus,
    setDetailPanelStatus,
    hasDraft,
    clearDraft,
    dbpath,
    setDbpath,
    selectedToPersons,
    setSelectedToPersons,
    subject,
    setSubject,
    comment,
    setComment,
    attachments,
    setAttachments,
    isReply,
    setIsReply,
    isReplyAll,
    setIsReplyAll,
    isForward,
    setIsForward,
    message,
    setMessage,
    dataType,
    msgFolder,
    menuData,
    setMenuData,
    setDataTotal,
    lastMenuData,
    setLastMenuData,
    dataList,
    setDataList,
    relativeOrders,
    hasSignOrderPerm,
    hasCosignOrderPerm,
    hasSignNotePerm,
    hasCosignNotePerm,
    unitFilter,
    selectedSort,
    sort,
    sortBy,
    pageNum,
    hasMore,
    selectedMenuIdx,
    selectedIdx,
    setSelectedIdx,
    messageList,
    keyword,
    specialIndices,
    scrollTop,
    dataTotal,
    envType,
    ambFilter,
  } = useContext(InboxContext);
  const [, setShowDiscardDialog] = useAtom(showDiscardDialogAtom);
  const [discardDraft, setDiscardDraft] = useAtom(discardDraftAtom);
  const setRescheduleOpen = useSetAtom(rescheduleOpenAtom);
  const setRescheduleApptData = useSetAtom(rescheduleApptDataAtom);
  const setActiveStep = useSetAtom(stepIndexAtom);
  const [needUpdateCalendar, setNeedUpdateCalendar] = useAtom(
    needUpdateCalendarAtom
  );
  const [fromID] = useState<number>(Cci.util.Staff.getSid());
  const [fromPerson] = useState<string>(Cci.util.Staff.getFullName());
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showPdf, setShowPdf] = useState<boolean>(false);
  const [pdfTitle, setPdfTitle] = useState<string>("");
  const [pdfURL, setPdfURL] = useState<string>("");
  const [regid, setRegid] = useState<any>(null);
  const maxLength = 400;

  const deleteIcon = <HighlightOffIcon sx={{ color: "#606060 !important" }} />;

  const textAreaStyle = {
    width: "100%",
    "& .MuiInputBase-root": {
      height: isReply || isReplyAll || isForward ? "150px" : "350px",
      padding: "6px",
      borderRadius: "4px",
      border: "1px solid #B1B1B1",
    },
    "& textarea": {
      height: "100% !important",
      overflowY: "auto !important",
      scrollbarWidth: "thin",
      resize: "none",
      color: "#000",
      fontFamily: "Roboto",
      fontSize: "16px",
      fontStyle: "normal",
      fontWeight: "400",
      lineHeight: "18px",
    },
    "& .MuiInputBase-input": {},
    "&.Mui-focused fieldset": {
      border: "1px solid #6599FF",
    },
  };

  const [patInfo, setPatInfo] = useState<any>(null);
  const { mutate: createMessage } = useNewMessageMutation();
  const { mutate: uploadFile } = useUploadFileMutation();
  const { mutate: downloadFile } = useDownloadAttachmentMutation();
  const { mutate: rxRefillRequest } = useRxRefillRequestMutation();

  const { data: guarantors } = useGetGuarantorsQuery(regid);

  const choiceLists = useGetChoiceListQueries([
    { chc: "globalinbox_message_subjects" },
    { chc: "guarantor_type" },
  ]);

  useEffect(() => {
    if (message?.dbpath) {
      getPatInfo({ dbpath: message?.dbpath }).then((res: any) => {
        setPatInfo(res);
      });
    }
  }, [message?.dbpath]);

  useEffect(() => {
    let flag = hasDraft() ? true : false;
    setEhrDataDirty("globalInbox", flag);
  }, [hasDraft]);

  useEffect(() => {
    clearDraft();
    if (
      dataType === INBOX_TYPE.NOTIFICATION &&
      message &&
      message.module == NOTIFICATION_TYPE.SCHEDULING_REQUESTS
    ) {
      setRegid(message?.regID);
    }
  }, [detailPanelStatus, message]);

  useEffect(() => {
    if (discardDraft) {
      clearDraft();
      setDiscardDraft(0);
      if (
        detailPanelStatus[detailPanelStatus.length - 1] === 0 &&
        discardDraft === 1
      ) {
        setDetailPanelStatus(detailPanelStatus.slice(0, -1));
      }
    }
  }, [discardDraft]);

  useEffect(() => {
    if (needUpdateCalendar && selectedIdx >= 0) {
      setNeedUpdateCalendar(false);
      cancelReschedule(message.accessionNumber);
      setDataList((preDataList) =>
        preDataList.filter((item: any, idx: number) => idx != selectedIdx)
      );
      setDataTotal((preDataTotal) => preDataTotal - 1);
      setDetailPanelStatus(detailPanelStatus.slice(0, -1));
      setMenuData((preMenuData) =>
        preMenuData.map((menu: any, idx: number) =>
          menu.name == message?.module ||
          (menu.name == message?.groupName && menu.pType == message?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != message.accessionNumber
                ),
              }
            : menu
        )
      );
      clearDraft();
      setMessage(null);
      setSelectedIdx(-1);
    }
  }, [needUpdateCalendar]);

  const handleSendClick = () => {
    createMessage(
      {
        senderType: 0,
        senderID: fromID,
        senderName: fromPerson,
        to: convertToPersonStr(),
        licenseType: Cci.util.Staff.getLicensetype(),
        msgType:
          isReply || isReplyAll || isForward ? message?.msgType ?? "" : "",
        msgSubject:
          isReply || isReplyAll
            ? msgFolder === MESSAGE_TYPE.PP_RECEIVED
              ? message.msgSubject
              : "RE:" + message.msgSubject
            : isForward
              ? "FW:" +
                (dataType === INBOX_TYPE.MESSAGE
                  ? message.msgSubject
                  : message.module == NOTIFICATION_TYPE.NOTES
                    ? "(" + message?.groupName + ") " + message?.subject
                    : message.module == NOTIFICATION_TYPE.ENCOUNTERS
                      ? "(" + message?.groupName + ") " + message?.title
                      : message?.subject)
              : subject?.label ?? "",
        msgContent: comment,
        dbpath: isReply || isReplyAll || isForward ? message?.dbpath : dbpath,
        attachments: attachments.length > 0 ? JSON.stringify(attachments) : "",
        sourceType:
          isReply || isReplyAll
            ? 1
            : isForward && dataType === INBOX_TYPE.MESSAGE
              ? 2
              : isForward && dataType === INBOX_TYPE.NOTIFICATION
                ? 3
                : 0,
        sourceID:
          isReply ||
          isReplyAll ||
          (isForward && dataType === INBOX_TYPE.MESSAGE)
            ? message?.msgID
            : 0,
        source:
          isForward && dataType === INBOX_TYPE.NOTIFICATION
            ? JSON.stringify(message)
            : "",
      },
      {
        onSuccess: (resp) => {
          setShowAlert(Boolean(resp.success) ? 1 : 2);
          setAlertInfo(resp.msg);
          if (Boolean(resp.success)) {
            clearDraft();
            if (detailPanelStatus[detailPanelStatus.length - 1] === 0) {
              setDetailPanelStatus(detailPanelStatus.slice(0, -1));
            }
          }
        },
      }
    );
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFile(
        { file: file, filename: file.name },
        {
          onSuccess: (resp) => {
            if (resp?.success) {
              const attachment = {
                fileName: resp.fileName,
                filePath: resp.filePath,
              };
              attachments.push(attachment);
              setAttachments(attachments);
            } else {
              setShowAlert(2);
              setAlertInfo(resp?.msg);
            }
          },
        }
      );
    }
  };

  const handleDeleteAttachment = (filePath: string) => {
    const filterAttachments = attachments.filter(
      (item: any) => item.filePath != filePath
    );
    setAttachments(filterAttachments);
  };

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  const handleDiscardClick = () => {
    if (hasDraft()) {
      setShowDiscardDialog(
        detailPanelStatus[detailPanelStatus.length - 1] === 0 ? 1 : 2
      );
    } else {
      if (detailPanelStatus[detailPanelStatus.length - 1] === 0) {
        setDetailPanelStatus(detailPanelStatus.slice(0, -1));
      } else {
        clearDraft();
      }
    }
  };

  const handleReplyClick = () => {
    if (dataType === INBOX_TYPE.MESSAGE) {
      setIsReply(true);
      if (!isEmpty(message?.attachments)) {
        setAttachments(message.attachments);
      }
      setSelectedToPersons([
        {
          group: "All Staff",
          type: message?.senderType,
          id: message?.senderID,
          label: message?.senderName,
          tagLabel: message?.senderLicenseType,
        },
      ]);
    }
  };

  const handleReplyAllClick = () => {
    setIsReplyAll(true);
    if (dataType === INBOX_TYPE.MESSAGE && !isEmpty(message?.attachments)) {
      setAttachments(message.attachments);
    }
    let recipientsArr = JSON.parse(message?.recipients);
    let persons = [] as any[];
    recipientsArr.forEach((recipient: any) => {
      if (recipient.staffID != fromID) {
        persons.push({
          group:
            recipient.type == "careteam"
              ? "Care Team"
              : recipient.type == "Groups"
                ? "groups"
                : "All Staff",
          id: recipient?.staffID,
          label: recipient?.name,
          tagLabel: recipient.licenseType,
        });
      }
    });
    persons.push({
      group: "All Staff",
      id: message?.senderID,
      label: message?.senderName,
      tagLabel: message?.senderLicenseType,
    });
    let uniqPersons = persons.reduce((acc: any[], cur: any) => {
      return acc.map((item) => item.id).includes(cur.id) ? acc : [...acc, cur];
    }, []);
    setSelectedToPersons(uniqPersons);
  };

  const handleForwardClick = () => {
    setIsForward(true);
    if (!isEmpty(message?.attachments)) {
      setAttachments(message.attachments);
    }
  };

  const handleRescheduleClick = () => {
    Promise.all([
      getPatApptData(message?.content?.id),
      getEncounterData(message?.regID, message?.patID),
    ]).then(([apptDataRet, encounterData]) => {
      if (apptDataRet && apptDataRet.success) {
        if (!apptDataRet.data.groupId) {
          setRescheduleApptData({
            ...apptDataRet.data,
            guarantorType: encounterData?.guarantorType || "",
            guarantor: encounterData?.guarantor || "",
          });
          setActiveStep(3);
          setRescheduleOpen(true);
        }
        handleUpdateApptStatus("Canceled", apptDataRet.data);
      }
    });
  };

  const handleRefillRequest = (action: number) => {
    rxRefillRequest(
      {
        dbpath: message?.dbpath,
        module: message?.module,
        groupName: message?.groupName,
        orderId: message?.orderID,
        action: action,
      },
      {
        onSuccess: (resp) => {
          setShowAlert(Boolean(resp.success) ? 1 : 2);
          setAlertInfo(resp.msg);
          if (Boolean(resp.success)) {
            if (selectedIdx < dataTotal - 1) {
              setMessage(dataList[selectedIdx + 1].data);
            } else {
              setDetailPanelStatus(detailPanelStatus.slice(0, -1));
            }
            setDataList((preDataList) =>
              preDataList.filter(
                (item: any, idx: number) =>
                  item?.data.orderID !== message.orderID
              )
            );
            setDataTotal((preDataTotal) => preDataTotal - 1);
            setMenuData((preMenuData) =>
              preMenuData.map((menu: any, idx: number) =>
                menu.name == message?.module ||
                (menu.name == message?.groupName &&
                  menu.pType == message?.module)
                  ? {
                      ...menu,
                      total: menu.total - 1,
                      data: menu.data.filter(
                        (item: any) => item.orderID != message.orderID
                      ),
                    }
                  : menu
              )
            );
          }
        },
      }
    );
  };

  const handlePrintClick = () => {};

  const handleUpdateApptStatus = (newStatus: string, apptData: any) => {
    let params = { ...apptData };
    params.apptStatus = newStatus;
    params.checkInCheckOutTime = -1;
    let admissiondetail = {
      cancel_reason: "Patient Requested",
    };
    params.admissiondetail = JSON.stringify(admissiondetail);
    let onSuccess = () => {};
    updateAppointment(params, onSuccess);
  };

  const signedAndCosignedCallback = (isSigned: boolean) => {
    setDataList((preDataList) =>
      preDataList.map((item: any, idx: number) =>
        item.type == INBOX_TYPE.NOTIFICATION
          ? {
              ...item,
              data:
                item?.data.accessionNumber == message.accessionNumber
                  ? {
                      ...item.data,
                      cosigned: message.groupName == "Cosign",
                      signed: message.groupName == "Sign",
                    }
                  : item.data,
            }
          : item
      )
    );
    if (!isEmpty(message)) {
      setMessage((preMessage: any) => {
        return {
          ...preMessage,
          cosigned: preMessage.groupName == "Cosign",
          signed: preMessage.groupName == "Sign",
        };
      });
    }
    if (isEmpty(lastMenuData)) {
      setLastMenuData(
        menuData.map((menu: any, idx: number) =>
          menu.name == message?.module ||
          (menu.name == message?.groupName && menu.pType == message?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != message.accessionNumber
                ),
              }
            : menu
        )
      );
    } else {
      setLastMenuData((preMenuData) =>
        preMenuData.map((menu: any, idx: number) =>
          menu.name == message?.module ||
          (menu.name == message?.groupName && menu.pType == message?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != message.accessionNumber
                ),
              }
            : menu
        )
      );
    }
  };

  const handleActionClick = (event: any, data: any) => {
    if (
      dataType === INBOX_TYPE.MESSAGE ||
      notHasPerm(
        data,
        hasSignOrderPerm,
        hasCosignOrderPerm,
        hasSignNotePerm,
        hasCosignNotePerm
      ) ||
      (dataType === INBOX_TYPE.NOTIFICATION &&
        (data?.reviewed || data?.signed || data?.cosigned))
    )
      return;
    event.stopPropagation();
    if (
      _.includes(
        [
          NOTIFICATION_TYPE.LAB_RESULTS,
          NOTIFICATION_TYPE.REPORTS,
          NOTIFICATION_TYPE.IMAGING_REPORTS,
        ],
        data.module
      )
    ) {
      markReviewed(
        data?.module == NOTIFICATION_TYPE.LAB_RESULTS
          ? ORDER_TYPE.LABORATORY
          : data?.groupName == "Microbiology"
            ? ORDER_TYPE.MICROBIOLOGY
            : data?.groupName == "Pathology"
              ? ORDER_TYPE.PATHOLOGY
              : "",
        data?.key,
        data?.orderID,
        data?.nit,
        data?.nits,
        data?.title,
        data?.dbpath
      )
        .then(() => {
          setShowAlert(1);
          setAlertInfo("Labs Marked as Reviewed");
          setDataList((preDataList) =>
            preDataList.map((item: any, idx: number) =>
              item.type == INBOX_TYPE.NOTIFICATION
                ? {
                    ...item,
                    data:
                      item?.data.accessionNumber == data.accessionNumber
                        ? {
                            ...item.data,
                            reviewed: true,
                          }
                        : item.data,
                  }
                : item
            )
          );
          setMessage((preMessage: any) => {
            return { ...preMessage, reviewed: true };
          });
          if (isEmpty(lastMenuData)) {
            setLastMenuData(
              menuData.map((menu: any, idx: number) =>
                menu.name == data?.module ||
                (menu.name == data?.groupName && menu.pType == data?.module)
                  ? {
                      ...menu,
                      total: menu.total - 1,
                      data: menu.data.filter(
                        (item: any) =>
                          item.accessionNumber != data.accessionNumber
                      ),
                    }
                  : menu
              )
            );
          } else {
            setLastMenuData((preMenuData) =>
              preMenuData.map((menu: any, idx: number) =>
                menu.name == data?.module ||
                (menu.name == data?.groupName && menu.pType == data?.module)
                  ? {
                      ...menu,
                      total: menu.total - 1,
                      data: menu.data.filter(
                        (item: any) =>
                          item.accessionNumber != data.accessionNumber
                      ),
                    }
                  : menu
              )
            );
          }
        })
        .catch((e) => {
          setShowAlert(2);
          setAlertInfo("Failed to mark as reviewed!");
        });
    } else if (NOTIFICATION_TYPE.ORDERS == message.module) {
      let actions =
        message.groupName == "Sign"
          ? { SIGN: 1, btn: "action" }
          : { COSIGN: 1, btn: "action" };
      saveOrderAction(
        [
          {
            _action: actions,
            key: message.key,
            order_id: message.orderID,
          },
        ],
        message.dbpath
      )
        .then(() => {
          setShowAlert(1);
          setAlertInfo(
            "Order " +
              (data.groupName == "Sign" ? "signed" : "cosigned") +
              " success"
          );
          signedAndCosignedCallback(data.groupName == "Sign");
        })
        .catch(() => {
          setShowAlert(2);
          setAlertInfo(
            "Order " +
              (data.groupName == "Sign" ? "signed" : "cosigned") +
              " fail"
          );
        });
    } else if (
      NOTIFICATION_TYPE.NOTES == message.module &&
      message.groupName !== "Missing"
    ) {
      if ("Sign" == message.groupName) {
        signNote({
          dbpath: message?.dbpath,
          query: "query.sign.ycql",
          output: "output.signCosign.ycql",
          notedata: [{ nit: message?.nit, key: message?.key }],
        })
          .then(() => {
            setShowAlert(1);
            setAlertInfo("Note signed success");
            signedAndCosignedCallback(true);
          })
          .catch(() => {
            setShowAlert(2);
            setAlertInfo("Note signed fail");
          });
      } else {
        coSignNote({
          dbpath: message?.dbpath,
          query: "query.cosign.ycql",
          output: "output.signCosign.ycql",
          notedata: [{ nit: message?.nit, key: message?.key }],
        })
          .then(() => {
            setShowAlert(1);
            setAlertInfo("Note cosigned success");
            signedAndCosignedCallback(false);
          })
          .catch(() => {
            setShowAlert(2);
            setAlertInfo("Note cosigned fail");
          });
      }
    }
  };

  const onSelectedPatient = (patient: any) => {
    setDbpath(patient.dbpath);
    setPatInfo("");
    getPatInfo({ dbpath: patient.dbpath }).then((res) => {
      setPatInfo(res);
    });
  };

  const onDeletePatient = () => {
    setDbpath("");
  };

  const convertToPersonStr = () => {
    if (!selectedToPersons || selectedToPersons.length === 0) return "[]";
    let toPersons: any[] = [];
    selectedToPersons.forEach((person: any) => {
      if (person.group === "Groups") {
        toPersons.push({ type: "groups", name: person.label });
      } else {
        toPersons.push({
          type: person.group === "Care Team" ? "careteam" : "allstaff",
          recipientType: person.type || 0, // 0. staff 1. patient
          recipientID: person.id,
          name: person.label,
          licenseType: person.tagLabel,
        });
      }
    });
    return JSON.stringify(toPersons);
  };

  const handleOpenClick = (event: any, fileName: string, filePath: string) => {
    event.stopPropagation();
    if (fileName && filePath) {
      setPdfTitle(fileName);
      downloadFile(
        { fileName: fileName, filePath: filePath, isOpen: true },
        {
          onSuccess: (resp) => {
            if (resp.msg && !Boolean(resp.success)) {
              setShowAlert(2);
              setAlertInfo(resp.msg);
            } else {
              const pdfBlob = b64toBlob(resp, "application/pdf");
              setPdfURL(window.URL.createObjectURL(pdfBlob));
              setShowPdf(true);
            }
          },
        }
      );
    }
  };

  const setRealLink = (fileName: string, filePath: string) => {
    let baseUrl: string = cci.cfg.baseUrl;
    const encodedFileName = encodeURIComponent(fileName || fileName);
    const encodedFilePath = encodeURIComponent(filePath || filePath);
    return `${baseUrl}/index.php/globalInbox/downloadAttachment?fileName=${encodedFileName}&filePath=${encodedFilePath}&isOpen=false`;
  };

  const handleCommentChange = (event: any) => {
    if (event.target.value.length <= maxLength) {
      setComment(event.target.value);
    }
  };

  const getToPersonsView = (personsStr: string) => {
    if (isEmpty(personsStr)) return <></>;
    let recipientsArr = JSON.parse(personsStr);
    return (
      <>
        {recipientsArr.map((recipient: any) => (
          <Chip
            data-testid={`to-person-tag-${recipient.name}`}
            sx={chipStyle3}
            size="small"
            label={recipient.name}
            avatar={
              <IconSmallAvatar style={{ width: "20px", height: "20px" }} />
            }
          />
        ))}
      </>
    );
  };

  const getLabResultContent = (content: string) => {
    if (isEmpty(content)) return <></>;
    let labs = JSON.parse(content.replace(/\\"/g, '"'));
    return (
      <>
        {labs.map((lab: any) => (
          <Box
            sx={{
              height: "44px",
              display: "flex",
              alignItems: "center",
              borderRadius: "10px",
              border: "1px solid #C4C4C4",
              padding: "16px",
              gap: "4px",
            }}
          >
            {labView(lab)}
          </Box>
        ))}
      </>
    );
  };

  const messageView: any = (message: any, index: number) => {
    return (
      <>
        <Box
          sx={[
            styleCard,
            {
              width:
                isReply || isReplyAll || isForward || index > 0
                  ? "calc(100% - 32px)"
                  : "100%",
              maxHeight:
                isReply || isReplyAll || isForward || index > 0
                  ? "324px"
                  : "364px",
              ml:
                isReply || isReplyAll || isForward || index > 0
                  ? "32px"
                  : "0px",
              padding: "8px",
              gap: "8px",
            },
          ]}
        >
          <Box
            sx={{
              height: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-top",
            }}
          >
            <Avatar
              data-testid="patient-header-photo"
              alt="CCI Avatar"
              variant="rounded"
              sx={{
                borderRadius: "50px",
                height: "32px",
                width: "32px",
              }}
              src={
                message?.dbpath
                  ? `${cci.cfg.baseUrl}/index.php/ImgUtil/getPatImage?dbpath=${message?.dbpath}`
                  : undefined
              }
            >
              {message?.dbpath ? undefined : (
                <IconProfile style={{ width: "32px", height: "32px" }} />
              )}
            </Avatar>
          </Box>
          <Box
            sx={{
              flex: 1,
              height: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-top",
              gap: "16px",
            }}
          >
            <Box
              sx={{
                width: "100%",
                height: "32px",
                display: "flex",
                alignItems: "center",
                flexShrink: 0,
                flexGrow: 0,
              }}
            >
              <Box
                sx={{
                  flex: 1,
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <StyledFrom>
                  {highlightText(
                    message?.senderName,
                    keyword && keyword.length >= 3 ? keyword : ""
                  )}
                </StyledFrom>
                <StyledFromSuffix sx={{ ml: "4px" }}>
                  {message?.senderLicenseType &&
                    `(${message?.senderLicenseType})`}
                </StyledFromSuffix>
              </Box>
              <StyledTime sx={{ paddingRight: "16px" }}>
                {message?.sendTimeStr}
              </StyledTime>
            </Box>
            <Box
              sx={{
                width: "100%",
                minHeight: "24px",
                display: "flex",
                alignItems: "top",
                gap: "12px",
              }}
            >
              <StyledLabel sx={{ mt: "3px" }}>To</StyledLabel>
              <Box
                sx={{
                  flex: 1,
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "0px",
                  overflowY: "auto",
                  scrollbarWidth: "thin",
                }}
              >
                {getToPersonsView(message?.recipients)}
              </Box>
            </Box>
            {message?.attachments &&
              message?.attachments?.map((attachment: any, index: number) => (
                <Box
                  sx={{
                    width: "100%",
                    height: "24px",
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                    cursor: "pointer",
                  }}
                >
                  <Chip
                    sx={chipStyle2}
                    size="small"
                    label={attachment.fileName}
                  />
                  <StyledAction
                    onClick={(e) =>
                      handleOpenClick(
                        e,
                        attachment.fileName,
                        attachment.filePath
                      )
                    }
                  >
                    Open
                  </StyledAction>
                  <DownloadButton
                    url={setRealLink(attachment.fileName, attachment.filePath)}
                    fileName={attachment.fileName}
                  >
                    <StyledAction>Download</StyledAction>
                  </DownloadButton>
                </Box>
              ))}
            <Box
              sx={{
                flex: 1,
                width: "100%",
                padingTop: "16px",
                display: "flex",
                alignItems: "flex-top",
                overflowY: "auto",
                scrollbarWidth: "thin",
                scrollbarGutter: "stable",
              }}
            >
              <StyledComment
                sx={{
                  whiteSpace: "pre-line",
                  overflowWrap: "break-word",
                  wordBreak: "break-all",
                }}
              >
                {highlightText(
                  message?.msgContent,
                  keyword && keyword.length >= 3 ? keyword : ""
                )}
              </StyledComment>
            </Box>
          </Box>
        </Box>
        {message?.message && messageView(message.message, index + 1)}
        {message?.source && notificationView(message.source, index + 1)}
      </>
    );
  };

  const sysMessageView: any = (message: any) => {
    return (
      <Box
        sx={[
          styleCard,
          {
            width: "100%",
            minHeight: "650px",
            padding: "8px",
            gap: "8px",
          },
        ]}
      >
        <Box
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-top",
          }}
        >
          <Avatar
            data-testid="patient-header-photo"
            alt="CCI Avatar"
            variant="rounded"
            sx={{
              borderRadius: "50px",
              height: "32px",
              width: "32px",
            }}
            src={
              message?.dbpath
                ? `${cci.cfg.baseUrl}/index.php/ImgUtil/getPatImage?dbpath=${message?.patient?.dbpath}`
                : undefined
            }
          >
            {message?.dbpath ? undefined : (
              <IconProfile style={{ width: "32px", height: "32px" }} />
            )}
          </Avatar>
        </Box>
        <Box
          sx={{
            flex: 1,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-top",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "32px",
              display: "flex",
              alignItems: "center",
              flexShrink: 0,
              flexGrow: 0,
            }}
          >
            <Box
              sx={{
                flex: 1,
                display: "flex",
                alignItems: "center",
              }}
            >
              <StyledFrom>
                {highlightText(
                  message?.patName,
                  keyword && keyword.length >= 3 ? keyword : ""
                )}
              </StyledFrom>
            </Box>
            <StyledTime sx={{ paddingRight: "16px" }}>
              {message?.createTimeStr}
            </StyledTime>
          </Box>
          <Box
            sx={{
              width: "100%",
              height: "32px",
              display: "flex",
              alignItems: "center",
              flexShrink: 0,
              flexGrow: 0,
              gap: "12px",
            }}
          >
            <StyledLabel>To</StyledLabel>
            <Chip
              data-testid={`to-person-tag-${message.senderName}`}
              sx={chipStyle3}
              size="small"
              label={message.senderName}
              avatar={
                <IconSmallAvatar style={{ width: "20px", height: "20px" }} />
              }
            />
          </Box>
          <StyledComment sx={{ mt: "32px" }}>Reschedule Details:</StyledComment>
          <Box
            sx={{
              padding: "10px 16px",
              mt: "16px",
              mr: "40px",
              backgroundColor: "#EFF8FF",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <StyledSubject>{message.content.apptType}</StyledSubject>
            <StyledComment
              sx={{
                lineHeight: "24px",
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
                color: "#717171",
              }}
            >
              {message.content.reasonForVisit}
            </StyledComment>
            <Box
              sx={{
                height: "24px",
                mt: "8px",
                mb: "8px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <IconDoctor style={{ width: "24px", height: "24px" }} />
              <StyledComment sx={{ color: "#202020" }}>
                {message.content.doctorName}
              </StyledComment>
              {message.content.doctorLicenseType && (
                <StyledLabel2 sx={{ color: "#717171" }}>
                  ({message.content.doctorLicenseType})
                </StyledLabel2>
              )}
            </Box>
            <Box
              sx={{
                height: "24px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <IconLocation style={{ width: "24px", height: "24px" }} />
              <StyledComment sx={{ color: "#202020" }}>
                {message.content.facility}
              </StyledComment>
            </Box>
          </Box>
          <StyledLabel2 sx={{ ml: "16px", mt: "16px" }}>
            Reason for Reschedule
          </StyledLabel2>
          <StyledComment sx={{ ml: "16px", mr: "40px", color: "#474747" }}>
            {message.content.rescheduleReason}
          </StyledComment>
          <StyledLabel2 sx={{ ml: "16px", mt: "24px" }}>
            Additional Comments
          </StyledLabel2>
          <StyledComment sx={{ ml: "16px", mr: "40px", color: "#474747" }}>
            {message.content.rescheduleComments}
          </StyledComment>
          <StyledLabel2 sx={{ ml: "16px", mt: "24px" }}>
            Preferred Times
          </StyledLabel2>
          <StyledComment sx={{ ml: "16px", mr: "40px", color: "#474747" }}>
            {message.content.availableDays}:{message.content.availableTimes}
          </StyledComment>
          <StyledLabel2 sx={{ ml: "16px", mt: "24px" }}>
            Preferred Contact
          </StyledLabel2>
          <StyledComment sx={{ ml: "16px", mr: "40px", color: "#474747" }}>
            {message.content.phone}
          </StyledComment>
        </Box>
      </Box>
    );
  };

  const rxRequestView: any = (message: any) => {
    let rxInfo = message.content ? JSON.parse(message.content) : {};
    return (
      <Box
        sx={[
          styleCard,
          {
            width: "100%",
            minHeight: "650px",
            padding: "8px",
            gap: "8px",
          },
        ]}
      >
        <Box
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-top",
          }}
        >
          <Avatar
            data-testid="patient-header-photo"
            alt="CCI Avatar"
            variant="rounded"
            sx={{
              borderRadius: "50px",
              height: "32px",
              width: "32px",
            }}
            src={
              message?.dbpath
                ? `${cci.cfg.baseUrl}/index.php/ImgUtil/getPatImage?dbpath=${message?.patient?.dbpath}`
                : undefined
            }
          >
            {message?.dbpath ? undefined : (
              <IconProfile style={{ width: "32px", height: "32px" }} />
            )}
          </Avatar>
        </Box>
        <Box
          sx={{
            flex: 1,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-top",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "32px",
              display: "flex",
              alignItems: "center",
              flexShrink: 0,
              flexGrow: 0,
            }}
          >
            <Box
              sx={{
                flex: 1,
                display: "flex",
                alignItems: "center",
              }}
            >
              <StyledFrom>
                {highlightText(
                  message?.patient?.patName,
                  keyword && keyword.length >= 3 ? keyword : ""
                )}
              </StyledFrom>
            </Box>
            <StyledTime sx={{ paddingRight: "16px" }}>
              {message?.createTimeStr}
            </StyledTime>
          </Box>
          <Box
            sx={{
              width: "100%",
              height: "32px",
              display: "flex",
              alignItems: "center",
              flexShrink: 0,
              flexGrow: 0,
              gap: "12px",
            }}
          >
            <StyledLabel>To</StyledLabel>
            <Chip
              data-testid={`to-person-tag-${message.createdBy}`}
              sx={chipStyle3}
              size="small"
              label={message.createdBy}
              avatar={
                <IconSmallAvatar style={{ width: "20px", height: "20px" }} />
              }
            />
          </Box>
          <StyledComment sx={{ mt: "32px" }}>
            Medication Refill Request:
          </StyledComment>
          <StyledSubject sx={{ ml: "16px", mt: "24px" }}>
            {message?.title}
          </StyledSubject>
          {rxInfo.Frequency ? (
            <>
              <StyledLabel2 sx={{ ml: "16px", mt: "24px" }}>
                Frequency
              </StyledLabel2>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                {rxInfo.Frequency}
              </StyledComment>
            </>
          ) : null}
          {rxInfo.Reason ? (
            <>
              <StyledLabel2 sx={{ ml: "16px", mt: "16px" }}>
                Reason
              </StyledLabel2>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                {rxInfo.Reason}
              </StyledComment>
            </>
          ) : null}
          {rxInfo.Ordering_Provider ? (
            <>
              <StyledLabel2 sx={{ ml: "16px", mt: "16px" }}>
                Ordering Provider
              </StyledLabel2>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                {rxInfo.Ordering_Provider}
              </StyledComment>
            </>
          ) : null}
          {rxInfo.Pharmacy ? (
            <>
              <StyledLabel2 sx={{ ml: "16px", mt: "16px" }}>
                Pharmacy
              </StyledLabel2>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                {rxInfo?.Pharmacy?.name}
              </StyledComment>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                {rxInfo?.Pharmacy?.addr1}, {rxInfo?.Pharmacy?.city},{" "}
                {rxInfo?.Pharmacy?.state}, {rxInfo?.Pharmacy?.zip}
              </StyledComment>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                Fax {rxInfo?.Pharmacy?.fax}
              </StyledComment>
              <StyledComment
                sx={{ mt: "8px", ml: "16px", mr: "40px", color: "#474747" }}
              >
                Phone {rxInfo?.Pharmacy?.phone}
              </StyledComment>
            </>
          ) : null}
        </Box>
      </Box>
    );
  };

  const notificationView: any = (notification: any, index: number) => {
    if (typeof notification === "string")
      notification = JSON.parse(notification);
    return (
      <Box
        sx={[
          styleCard,
          {
            width:
              isReply || isReplyAll || isForward || index > 0
                ? "calc(100% - 32px)"
                : "100%",
            ml:
              isReply || isReplyAll || isForward || index > 0 ? "32px" : "0px",
            padding: "8px",
            gap: "8px",
            background:
              dataType === INBOX_TYPE.NOTIFICATION &&
              (notification?.reviewed ||
                notification?.cosigned ||
                notification?.signed)
                ? "#D6EAD7"
                : "#FFF",
          },
        ]}
      >
        <Box
          sx={{
            width: "8px",
            height: "100%",
            background:
              dataType === INBOX_TYPE.NOTIFICATION &&
              (notification?.reviewed ||
                notification?.cosigned ||
                notification?.signed)
                ? "#98C99A"
                : notification?.module == NOTIFICATION_TYPE.LAB_RESULTS &&
                    notification.flag == 2
                  ? "#EC1C24"
                  : notification?.module == NOTIFICATION_TYPE.LAB_RESULTS &&
                      notification.flag == 1
                    ? "#832522"
                    : notification?.status == 0
                      ? "#426EB6"
                      : "#CBCACA",
            borderRadius: "16px",
          }}
        />
        {(_.includes(
          [
            NOTIFICATION_TYPE.LAB_RESULTS,
            NOTIFICATION_TYPE.REPORTS,
            NOTIFICATION_TYPE.IMAGING_REPORTS,
            NOTIFICATION_TYPE.ORDERS,
          ],
          notification?.module
        ) ||
          (NOTIFICATION_TYPE.NOTES == notification?.module &&
            notification?.groupName !== "Missing" &&
            _.includes(["Sign", "Cosign"], notification?.groupName))) && (
          <StyledReview
            sx={{
              backgroundColor:
                dataType === INBOX_TYPE.NOTIFICATION &&
                (notification?.reviewed ||
                  notification?.cosigned ||
                  notification?.signed)
                  ? "#98C99A"
                  : "#FFF",
              border:
                dataType === INBOX_TYPE.NOTIFICATION &&
                (notification?.reviewed ||
                  notification?.cosigned ||
                  notification?.signed)
                  ? "none"
                  : "1px solid #B1B1B1",
              color:
                notHasPerm(
                  notification,
                  hasSignOrderPerm,
                  hasCosignOrderPerm,
                  hasSignNotePerm,
                  hasCosignNotePerm
                ) || dataType === INBOX_TYPE.MESSAGE
                  ? "#ACACAC"
                  : dataType === INBOX_TYPE.NOTIFICATION &&
                      (notification?.reviewed ||
                        notification?.cosigned ||
                        notification?.signed)
                    ? "#FFF"
                    : "#000",
              "&:hover": {
                backgroundColor:
                  notHasPerm(
                    notification,
                    hasSignOrderPerm,
                    hasCosignOrderPerm,
                    hasSignNotePerm,
                    hasCosignNotePerm
                  ) || dataType === INBOX_TYPE.MESSAGE
                    ? "#FFF"
                    : dataType === INBOX_TYPE.NOTIFICATION &&
                        (notification?.reviewed ||
                          notification?.cosigned ||
                          notification?.signed)
                      ? "#98C99A"
                      : "rgba(66, 110, 182, 0.4)",
              },
            }}
            onClick={(e: any) => handleActionClick(e, notification)}
          >
            {_.includes(
              [
                NOTIFICATION_TYPE.LAB_RESULTS,
                NOTIFICATION_TYPE.REPORTS,
                NOTIFICATION_TYPE.IMAGING_REPORTS,
              ],
              notification.module
            )
              ? notification?.reviewed
                ? "Reviewed"
                : "Review"
              : notification?.cosigned
                ? "Cosigned"
                : notification?.signed
                  ? "Signed"
                  : notification.groupName}
          </StyledReview>
        )}
        <Box
          sx={{
            flex: 1,
            minWidth: "0px",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            gap:
              notification?.module == NOTIFICATION_TYPE.LAB_RESULTS
                ? "10px"
                : "4px",
            ml:
              (notification?.module == NOTIFICATION_TYPE.NOTES &&
                notification?.groupName !== "Missing") ||
              notification?.module == NOTIFICATION_TYPE.ENCOUNTERS
                ? "6px"
                : "14px",
          }}
        >
          <Box
            sx={{
              width: "100%",
              minWidth: "0px",
              height: "24px",
              display: "flex",
              alignItems: "center",
              gap: "4px",
            }}
          >
            {notification.module !== NOTIFICATION_TYPE.ENCOUNTERS &&
              menuImage(notification?.module, true)}
            <Box
              sx={{
                flex: 1,
                display: "flex",
                alignItems: "center",
              }}
            >
              <StyledLabel2>
                {_.includes(
                  [NOTIFICATION_TYPE.ORDERS, NOTIFICATION_TYPE.NOTES],
                  notification?.module
                )
                  ? notification?.subject
                  : notification?.title}
              </StyledLabel2>
            </Box>
            {!(
              notification?.module === NOTIFICATION_TYPE.NOTES &&
              notification?.groupName === "Missing"
            ) && (
              <IconExternalLink
                style={{ width: "18px", height: "18px" }}
                onClick={(e) =>
                  handleLinkClick(
                    e,
                    messageList,
                    notification,
                    dataType,
                    unitFilter,
                    selectedSort,
                    sort,
                    sortBy,
                    pageNum,
                    hasMore,
                    specialIndices,
                    selectedMenuIdx,
                    selectedIdx,
                    notification.accessionNumber,
                    dataType === INBOX_TYPE.NOTIFICATION
                      ? message.accessionNumber
                      : msgFolder === MESSAGE_TYPE.SENT
                        ? message.msgID
                        : message.receiveID,
                    scrollTop,
                    dataTotal,
                    envType,
                    ambFilter
                  )
                }
              />
            )}
          </Box>
          <Box
            sx={{
              width: "100%",
              minWidth: "0px",
              minHeight:
                notification?.module == NOTIFICATION_TYPE.LAB_RESULTS
                  ? "44px"
                  : "0px",
              display: "flex",
              flexWrap:
                notification?.module == NOTIFICATION_TYPE.LAB_RESULTS
                  ? "wrap"
                  : "nowrap",
              alignItems: "center",
              gap: 1,
              overflowY: "auto",
              scrollbarWidth: "thin",
            }}
          >
            {notification?.module == NOTIFICATION_TYPE.LAB_RESULTS ? (
              getLabResultContent(notification?.content)
            ) : notification?.content ? (
              <>
                <StyledComment
                  sx={{
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                  }}
                >
                  {notification?.content}
                </StyledComment>
              </>
            ) : (
              <></>
            )}
          </Box>
          <Box
            sx={{
              width: "100%",
              minWidth: "0px",
              height: "24px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {notification.module == NOTIFICATION_TYPE.ENCOUNTERS ? (
              <Box
                sx={{
                  flex: 1,
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                }}
              >
                <StyledComment>{notification.subject}</StyledComment>
              </Box>
            ) : (
              <Box
                sx={{
                  flex: 1,
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                }}
              >
                <IconProfile style={{ width: "18px", height: "18px" }} />
                <StyledComment>{notification.createdBy}</StyledComment>
                {notification?.licenseType && (
                  <StyledFromSuffix>
                    ({notification?.licenseType})
                  </StyledFromSuffix>
                )}
              </Box>
            )}
            <StyledTime>{notification?.createTimeStr}</StyledTime>
          </Box>
        </Box>
      </Box>
    );
  };

  const relativeOrdersView: any = () => {
    return (
      <>
        <StyledLabel2>Additional Orders for This Patient</StyledLabel2>
        {relativeOrders.map((order) => notificationView(order, 0))}
      </>
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        width: "100%",
        height: "100%",
        minWidth: "0px",
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        padding: "8px 0px 8px 8px",
        flexShrink: 0,
        borderRadius: "8px",
        background: "#F7F7F7",
        boxShadow: "0px 2px 6px 0px rgba(0, 0, 0, 0.15)",
      }}
    >
      {detailPanelStatus[detailPanelStatus.length - 1] === -1 ? (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <StyledNoData>No Message Selected</StyledNoData>
        </Box>
      ) : (
        <>
          <Box
            sx={{
              height: "32px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {detailPanelStatus[detailPanelStatus.length - 1] === 0 ||
            isReply ||
            isReplyAll ||
            isForward ? (
              <>
                <StyledButton
                  data-testid="send-btn"
                  color="secondary"
                  disabled={
                    isEmpty(selectedToPersons) ||
                    (detailPanelStatus[detailPanelStatus.length - 1] === 0 &&
                      (isEmpty(subject?.label) || isEmpty(dbpath))) ||
                    isEmpty(comment)
                  }
                  onClick={handleSendClick}
                  sx={{ width: "78px" }}
                  startIcon={<IconSend />}
                >
                  Send
                </StyledButton>
                <Box
                  sx={{
                    width: "1px",
                    height: "32px",
                    borderRadius: "10px",
                    background: "#B9B9B9",
                    ml: "16px",
                  }}
                />
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  style={{ display: "none" }}
                  accept=".pdf"
                />
                <StyledButton
                  data-testid="attachment-btn"
                  color="secondary"
                  onClick={handleAttachClick}
                  sx={{ width: "114px", ml: "16px" }}
                  startIcon={<IconBtnAttachment />}
                >
                  Attach File
                </StyledButton>
                <StyledButton
                  data-testid="delete-btn"
                  color="secondary"
                  onClick={() => {
                    handleDiscardClick();
                  }}
                  sx={{ width: "94px", ml: "8px" }}
                  startIcon={<IconDelete />}
                >
                  Discard
                </StyledButton>
              </>
            ) : dataType === INBOX_TYPE.MESSAGE ? (
              <>
                <StyledButton
                  data-testid="reply-btn"
                  color="secondary"
                  onClick={handleReplyClick}
                  sx={{ width: "82px" }}
                  startIcon={<IconReply />}
                >
                  Reply
                </StyledButton>
                {MESSAGE_TYPE.PP_RECEIVED != msgFolder && (
                  <>
                    <StyledButton
                      data-testid="reply-all-btn"
                      color="secondary"
                      onClick={handleReplyAllClick}
                      sx={{ width: "102px", ml: "8px" }}
                      startIcon={<IconReplyAll />}
                    >
                      Reply All
                    </StyledButton>
                    <StyledButton
                      data-testid="delete-btn"
                      color="secondary"
                      onClick={handleForwardClick}
                      sx={{ width: "98px", ml: "8px" }}
                      startIcon={<IconForward />}
                    >
                      Forward
                    </StyledButton>
                  </>
                )}
              </>
            ) : message?.module === NOTIFICATION_TYPE.SCHEDULING_REQUESTS ? (
              <StyledButton
                data-testid="reschedule-btn"
                color="secondary"
                onClick={handleRescheduleClick}
                sx={{ width: "210px" }}
                startIcon={<IconSchedule />}
              >
                Reschedule Appointment
              </StyledButton>
            ) : message?.module === NOTIFICATION_TYPE.RX_REQUESTS &&
              message?.groupName === "Rx Refill" ? (
              <>
                <StyledButton
                  data-testid="approve-refill-btn"
                  color="secondary"
                  onClick={() => handleRefillRequest(1)}
                  sx={{ width: "200px", marginRight: "8px" }}
                  startIcon={<IconApprove />}
                >
                  Approve Refill Request
                </StyledButton>
                <StyledButton
                  data-testid="deny-refill-btn"
                  color="secondary"
                  onClick={() => handleRefillRequest(2)}
                  sx={{ width: "190px" }}
                  startIcon={<IconDeny />}
                >
                  Deny Refill Request
                </StyledButton>
              </>
            ) : (
              <>
                <StyledButton
                  data-testid="delete-btn"
                  color="secondary"
                  onClick={handleForwardClick}
                  sx={{ width: "98px" }}
                  startIcon={<IconForward />}
                >
                  Forward
                </StyledButton>
                <Box
                  sx={{
                    width: "1px",
                    height: "32px",
                    borderRadius: "10px",
                    background: "#B9B9B9",
                    ml: "16px",
                  }}
                />
                <StyledButton
                  data-testid="delete-btn"
                  color="secondary"
                  onClick={handlePrintClick}
                  sx={{ width: "144px", ml: "16px" }}
                  startIcon={<IconPrint />}
                >
                  Generate Letter
                </StyledButton>
              </>
            )}
          </Box>
          <Box
            sx={{
              width: "100%",
              height: "100%",
              minWidth: "0px",
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              overflowY: "auto",
              scrollbarWidth: "thin",
              paddingRight: "8px",
            }}
          >
            <Box
              sx={{
                width: "100%",
                minHeight: "32px",
                padding: "4px 0px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <StyledSubject>
                {isReply || isReplyAll ? "RE: " : isForward ? "FW: " : ""}
                {detailPanelStatus[detailPanelStatus.length - 1] === 0
                  ? subject?.label
                  : dataType === INBOX_TYPE.MESSAGE
                    ? highlightText(
                        message?.msgSubject,
                        keyword && keyword.length >= 3 ? keyword : ""
                      )
                    : message.module == NOTIFICATION_TYPE.NOTES &&
                        message?.groupName !== "Missing"
                      ? "(" + message?.groupName + ") " + message?.subject
                      : message.module == NOTIFICATION_TYPE.ENCOUNTERS
                        ? "(" + message?.groupName + ") " + message?.title
                        : message?.subject}
              </StyledSubject>
            </Box>
            <Box sx={[styleCard, { height: "64px", justifyContent: "center" }]}>
              {(detailPanelStatus[detailPanelStatus.length - 1] === 1 &&
                message) ||
              (detailPanelStatus[detailPanelStatus.length - 1] === 0 &&
                patInfo &&
                dbpath.length > 0) ? (
                <PatientHeader
                  patInfo={
                    detailPanelStatus[detailPanelStatus.length - 1] === 0
                      ? patInfo?.data
                      : patInfo?.data
                  }
                  infoStyle={{ borderRadius: "8px", boxWidth: "48px" }}
                  photo={
                    patInfo?.data?.encdbpath ? (
                      `${cci.cfg.baseUrl}/index.php/ImgUtil/getPatImage?dbpath=${patInfo?.data?.encdbpath}`
                    ) : (
                      <PatientPhotoIcon />
                    )
                  }
                ></PatientHeader>
              ) : (
                <StyledNoData>No patient selected.</StyledNoData>
              )}
            </Box>
            {detailPanelStatus[detailPanelStatus.length - 1] === 0 && (
              <Box
                sx={[
                  styleCard,
                  {
                    height: "64px",
                    paddingLeft: "8px",
                    paddingRight: "8px",
                  },
                ]}
              >
                <StyledLabel sx={{ width: "65px", mr: "8px" }}>
                  Patient
                </StyledLabel>
                <CustomTableSelect
                  onSelectedPatient={onSelectedPatient}
                  onDeletePatient={onDeletePatient}
                />
              </Box>
            )}
            {(detailPanelStatus[detailPanelStatus.length - 1] === 0 ||
              isReply ||
              isReplyAll ||
              isForward) && (
              <Box
                sx={{
                  ...styleCard,
                  padding: "8px",
                  flexDirection: "column",
                  alignItems: "flex-start",
                  gap: "16px",
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <StyledLabel sx={{ width: "65px", mr: "8px" }}>
                    From
                  </StyledLabel>
                  <StyledContent>{fromPerson}</StyledContent>
                </Box>
                <Box
                  sx={{
                    width: "100%",
                    minHeight: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <StyledLabel sx={{ width: "65px", mr: "8px" }}>
                    To
                  </StyledLabel>
                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <CustomMultiSelect
                      disabled={
                        isReply && msgFolder === MESSAGE_TYPE.PP_RECEIVED
                      }
                    />
                  </Box>
                </Box>
                <Box
                  sx={{
                    width: "100%",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <StyledLabel sx={{ width: "65px", mr: "8px" }}>
                    Subject
                  </StyledLabel>
                  {isReply || isReplyAll || isForward ? (
                    <StyledSubjectSmall>
                      {isReply || isReplyAll ? "RE: " : "FW: "}
                      {dataType === INBOX_TYPE.MESSAGE
                        ? message?.msgSubject
                        : message.module == NOTIFICATION_TYPE.NOTES &&
                            message?.groupName !== "Missing"
                          ? "(" +
                            message?.groupName +
                            ") " +
                            message?.subjectmessage?.subject
                          : message.module == NOTIFICATION_TYPE.ENCOUNTERS
                            ? "(" + message?.groupName + ") " + message?.title
                            : message.subject}
                    </StyledSubjectSmall>
                  ) : (
                    <Box sx={{ flex: 1 }}>
                      <SingleDropdownMenu
                        data-testid="inbox-subject-dropdown"
                        placeholder=""
                        value={subject}
                        onSelectionChange={setSubject}
                        comboBoxChoices={
                          choiceLists?.globalinbox_message_subjects?.data ?? []
                        }
                        inputReadOnly={true}
                      />
                    </Box>
                  )}
                </Box>
                {attachments &&
                  attachments?.map((attachment: any, index: number) => (
                    <Chip
                      key={`chip-upload-file-${index}`}
                      sx={{ ...chipStyle, height: "24px", ml: "73px" }}
                      size="small"
                      onDelete={() =>
                        handleDeleteAttachment(attachment.filePath)
                      }
                      label={attachment.fileName}
                      deleteIcon={deleteIcon}
                    />
                  ))}
                <Box
                  sx={{
                    width: "100%",
                    height:
                      isReply || isReplyAll || isForward ? "172px" : "372px",
                    paddingLeft: "73px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "end",
                  }}
                >
                  <TextField
                    multiline
                    sx={textAreaStyle}
                    key="inbox-send-comment"
                    value={comment}
                    onChange={handleCommentChange}
                  />
                  <StyledSize>{`${comment.length}/${maxLength}`}</StyledSize>
                </Box>
              </Box>
            )}
            {detailPanelStatus[detailPanelStatus.length - 1] === 1 &&
              dataType === INBOX_TYPE.MESSAGE &&
              messageView(message, 0)}
            {detailPanelStatus[detailPanelStatus.length - 1] === 1 &&
              dataType === INBOX_TYPE.NOTIFICATION &&
              (message?.module === NOTIFICATION_TYPE.SCHEDULING_REQUESTS
                ? sysMessageView(message)
                : message?.module === NOTIFICATION_TYPE.RX_REQUESTS &&
                    message?.groupName === "Rx Refill"
                  ? rxRequestView(message)
                  : message?.module === NOTIFICATION_TYPE.NOTES &&
                      message?.groupName === "Missing"
                    ? messageView(
                        {
                          ...message,
                          senderName: message?.createdBy || "Unknown",
                          senderLicenseType: message?.licenseType || "",
                          sendTimeStr: message?.createTimeStr || "",
                          recipients: message?.assignedto
                            ? JSON.stringify([
                                {
                                  type: "allstaff",
                                  recipientType: 0,
                                  recipientID: 0,
                                  name: message.assignedto,
                                  licenseType: "",
                                },
                              ])
                            : "[]",
                          attachments: [],
                          msgContent:
                            message?.content || message?.subject || "",
                        },
                        0
                      )
                    : notificationView(message))}
            {relativeOrders.length > 0 && relativeOrdersView()}
            <CciPdfViewer
              title={pdfTitle}
              pdfUrl={pdfURL}
              open={showPdf}
              setOpen={setShowPdf}
            />
          </Box>
        </>
      )}
    </Box>
  );
};
export default InboxDetailView;
