import {
  ReactElement,
  FC,
  useRef,
  useEffect,
  useState,
  useContext,
} from "react";
import { useSet<PERSON><PERSON> } from "jotai";
import {
  showDiscardDialog<PERSON>tom,
  showAlertAtom,
  alertInfoAtom,
} from "../atoms/GlobalInboxAtoms";
import { IconButton, SearchField } from "@cci/mui-components";
import CciButton from "@cci-monorepo/common/mui-components/src/components/button/Button";
import { styled } from "@mui/material/styles";
import { Box, Typography, Menu, MenuItem, Avatar } from "@mui/material";
import { StyledLabel, StyledComment } from "./InboxDetailView";
import { InboxContext, sortList } from "../context/GlobalInboxContext";
import {
  IconGroup,
  IconPath,
  IconRectangle,
  IconBtnArchive,
  IconMarkUnread,
  IconProfile,
  IconAttachment,
  IconExternalLink,
} from "../icons";
import {
  useGetMessagesQuery,
  useMarkReadMessageMutation,
  useMarkReadNotificationMutation,
  useArchiveMessageMutation,
  markReviewed,
  saveOrderAction,
  useGetNotificationsQuery,
} from "../util/GlobalInboxHooks";
import _, { cloneDeep, isEmpty } from "lodash";
import ScrollLoadMore from "./ScrollLoadMore";
import { ORDER_TYPE } from "@cci-monorepo/LabsReview/config/value";
import { signNote, coSignNote } from "@cci-monorepo/Provider/util/ProviderData";
import Config from "@cci-monorepo/config/Config";
import { highlightText } from "../util/GlobalInboxUtil";
import {
  INBOX_TYPE,
  MESSAGE_TYPE,
  NOTIFICATION_TYPE,
  ALL_MESSAGE_TYPE,
  WITHOUT_RECEIVED_MESSAGE_TYPE,
  INBOX_CONFIG,
  INBOX_TITLE,
  ENV_TYPE,
} from "../constants/Constants";
import { getPatInfo } from "../util/GlobalInboxData";

export const StyledNoData = styled(Typography)({
  color: "#999",
  fontFamily: "Roboto",
  fontSize: "24px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
});

const headerStyle = {
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
  whiteSpace: "nowrap",
};

const StyledHeader = styled(Typography)(headerStyle);

const StyledTitle = styled(Typography)({
  height: "24px",
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "24px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "16px",
  display: "flex",
  alignItems: "flex-end",
  margin: "16px 8px",
});

export const StyledFrom = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "18px",
  fontStyle: "normal",
  fontWeight: "500",
  lineHeight: "normal",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
});

export const StyledFromSuffix = styled(Typography)({
  color: "rgba(0, 0, 0, 0.60)",
  fontFamily: "Roboto",
  fontSize: "18px",
  fontStyle: "normal",
  fontWeight: "500",
  lineHeight: "normal",
  textOverflow: "ellipsis",
});

const StyledSubject = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "18px",
});

const timeStyle = {
  color: "rgba(0, 0, 0, 0.70)",
  fontFamily: "Roboto",
  fontSize: "16px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "18px",
};

export const StyledTime = styled(Typography)(timeStyle);

export const StyledIconButton = styled(IconButton)({
  "&:disabled": {
    svg: {
      filter: "grayscale(100%) opacity(0.5)",
    },
  },
});

export const StyledButton = styled(CciButton)({
  height: "32px",
  minHeight: "32px",
  padding: "4px 0px 4px 6px",
  alignItems: "center",
  justifyContent: "flex-start",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "24px",
  textAlign: "center",
  "&:disabled": {
    svg: {
      filter: "grayscale(100%) opacity(0.5)",
    },
  },
});

const styledItemBox = {
  width: "100%",
  height: "100%",
  minWidth: "0px",
  display: "flex",
  alignItems: "center",
  gap: "8px",
  padding: "8px 8px 8px 0px",
};

export const StyledTitle2 = styled(Typography)({
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "18px",
  fontStyle: "normal",
  fontWeight: "500",
  lineHeight: "normal",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
});

export const StyledReview = styled(Typography)({
  height: "24px",
  padding: "0px 8px",
  color: "#000",
  fontFamily: "Roboto",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "500",
  lineHeight: "24px",
  textAlign: "center",
  borderRadius: "18px",
  border: "1px solid #B1B1B1",
  cursor: "pointer",
  "&:hover": {
    backgroundColor: "rgba(66, 110, 182, 0.4)",
  },
});

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  color: "#000",
  backgroundColor: "#FFF",
  fontFamily: "Roboto",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "400",
  lineHeight: "normal",
  "&.MuiMenuItem-root": {
    "&:hover": {
      backgroundColor: "rgba(254,195,65, 0.50)",
    },

    "&.Mui-selected": {
      backgroundColor: "#FEC341",
    },
  },
}));

export const notHasPerm = (
  data: any,
  hasSignOrderPerm: boolean,
  hasCosignOrderPerm: boolean,
  hasSignNotePerm: boolean,
  hasCosignNotePerm: boolean
) => {
  return (
    (_.includes(
      [
        NOTIFICATION_TYPE.LAB_RESULTS,
        NOTIFICATION_TYPE.REPORTS,
        NOTIFICATION_TYPE.IMAGING_REPORTS,
      ],
      data.module
    ) &&
      !hasSignOrderPerm) ||
    ("Orders" == data.module &&
      "Sign" == data.groupName &&
      !hasSignOrderPerm) ||
    ("Orders" == data.module &&
      "Cosign" == data.groupName &&
      !hasCosignOrderPerm) ||
    (NOTIFICATION_TYPE.NOTES == data.module &&
      "Sign" == data.groupName &&
      !hasSignNotePerm) ||
    (NOTIFICATION_TYPE.NOTES == data.module &&
      "Cosign" == data.groupName &&
      !hasCosignNotePerm)
  );
};

export const handleLinkClick = (
  event: any,
  messageList: any[],
  data: any,
  type: string,
  unitFilter: string,
  selectedSort: string,
  sort: string,
  sortBy: string,
  pageNum: number,
  hasMore: boolean,
  specialIndices: number[],
  selectedMenuIdx: number,
  selectedIdx: number,
  accessionNumber: string,
  selectedAccessionNumber: string,
  scrollTop: number,
  total: number,
  envType: string,
  ambFilter: string[]
) => {
  event.stopPropagation();
  let params = {};
  const row = {
    dbpath: data?.patient?.dbpath,
    patid: data?.patient?.patid,
    Patient: data?.patient?.patName,
    Unit: data?.patient?.unit,
    Bed: data?.patient?.bed,
  };
  Cci.Patient.loadPatientInfo(row.dbpath, "", true);
  const module = data?.module;
  let pageConfig = {};
  if (
    _.includes(
      [NOTIFICATION_TYPE.LAB_RESULTS, NOTIFICATION_TYPE.REPORTS],
      module
    )
  ) {
    pageConfig = {
      list: "labs",
      app: "Labs to Review",
    };
  } else if (NOTIFICATION_TYPE.IMAGING_REPORTS == module) {
    pageConfig = {
      list: "labs",
      app: "Radiology",
    };
  } else if ("Orders" == module) {
    pageConfig = {
      list: "cpoe_psat",
      app: "ORDER_ENTRY",
    };
    params = {
      entry: "OE",
      targetTab: {
        tabValue: "Actions",
      },
    };
  } else if (NOTIFICATION_TYPE.NOTES == module) {
    pageConfig = {
      list: "notes",
      app: "Notes Menu",
    };
    params = {
      notename: data?.groupName === "Missing" ? null : data?.subject,
      minorIT: data?.nit,
      notetime: data?.key,
    };
  } else if (NOTIFICATION_TYPE.ENCOUNTERS == module) {
    pageConfig = {
      list: "default",
      app: "Discharge Summary",
    };
  }
  Cci?.RunTime?.setCache(INBOX_CONFIG, {
    params: params,
    module: data?.module,
    groupName: data?.groupName,
    name: data?.title,
    key: data?.key,
    orderID: data?.orderID,
    nit: data?.nit,
    labType: data?.type,
    messageList: messageList,
    type: type,
    unitFilter: unitFilter,
    selectedSort: selectedSort,
    sort: sort,
    sortBy: sortBy,
    pageNum: pageNum,
    hasMore: hasMore,
    specialIndices: specialIndices,
    selectedMenuIdx: selectedMenuIdx,
    selectedIdx: selectedIdx,
    accessionNumber: accessionNumber,
    selectedAccessionNumber: selectedAccessionNumber,
    scrollTop: Math.max(0, scrollTop - 50),
    total: total,
    envType: envType,
    ambFilter: ambFilter,
  });
  Config.gotoPatientPage(row, pageConfig);
};

export const labView = (lab: any) => {
  return (
    <>
      {!isEmpty(lab.name) && (
        <Typography component="span" sx={{ ...headerStyle, mr: "4px" }}>
          {lab.name}
        </Typography>
      )}
      {!isEmpty(lab.data) && (
        <Typography
          component="span"
          sx={{
            ...headerStyle,
            fontWeight: "700",
            color:
              lab?.flag === "HH" || lab?.flag === "LL"
                ? "#EC1C24"
                : lab?.flag === "H" || lab?.flag === "L"
                  ? "#832522"
                  : "#000",
            mr: "4px",
          }}
        >
          {lab.data}
        </Typography>
      )}
      {!isEmpty(lab.unit) && (
        <Typography component="span" sx={{ ...timeStyle, mr: "4px" }}>
          {lab.unit}
        </Typography>
      )}
      {lab?.flag && (
        <Typography
          component="span"
          sx={{
            ...headerStyle,
            color:
              lab?.flag === "HH" || lab?.flag === "LL"
                ? "#EC1C24"
                : lab?.flag === "H" || lab?.flag === "L"
                  ? "#832522"
                  : "#000",
            mr: "4px",
          }}
        >
          {lab.flag}
        </Typography>
      )}
    </>
  );
};

const InboxListView: FC<any> = (props): ReactElement => {
  const { menu, type } = props;
  const setShowDiscardDialog = useSetAtom(showDiscardDialogAtom);
  const setShowAlert = useSetAtom(showAlertAtom);
  const setAlertInfo = useSetAtom(alertInfoAtom);
  const [anchorEl, setAnchorEl] = useState(null);
  const [msgTitle, setMsgTitle] = useState<string>("");
  const listRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const specialItemRefs = useRef<any>([]);
  const [stickyIdx, setStickyIdx] = useState<number>(-1);
  const [stickyWidth, setStickyWidth] = useState<number>(577);
  const [staffID] = useState<number>(Cci.util.Staff.getSid());
  const [debouncedKeyword, setDebouncedKeyword] = useState("");
  const [selectedIds, setSelectedIds] = useState<any[]>([]);
  const [lastSelectedId, setLastSelectedId] = useState(null);
  const [lastReadData, setLastReadData] = useState(null);
  const [lastReadIdx, setLastReadIdx] = useState(-1);
  const [pageSize] = useState<number>(10);
  const [storePageNum, setStorePageNum] = useState<number>(0);
  const {
    detailPanelStatus,
    setDetailPanelStatus,
    hasDraft,
    clearDraft,
    message,
    setMessage,
    setDataType,
    msgFolder,
    setMsgFolder,
    unitFilter,
    ambFilter,
    menuData,
    setMenuData,
    lastMenuData,
    setLastMenuData,
    dataList,
    setDataList,
    messageList,
    setMessageList,
    dataTotal,
    setDataTotal,
    setRelativeOrders,
    hasSignOrderPerm,
    hasCosignOrderPerm,
    hasSignNotePerm,
    hasCosignNotePerm,
    selectedIdx,
    setSelectedIdx,
    selectedMenuIdx,
    selectedSort,
    setSelectedSort,
    sort,
    setSort,
    sortBy,
    setSortBy,
    inboxConfig,
    setInboxConfig,
    isReply,
    isReplyAll,
    isForward,
    pageNum,
    setPageNum,
    hasMore,
    setHasMore,
    keyword,
    setKeyword,
    specialIndices,
    setSpecialIndices,
    scrollTop,
    envType,
    allAmbUnits,
  } = useContext(InboxContext);

  const { data: messagesData } = useGetMessagesQuery(
    0,
    staffID,
    msgFolder,
    debouncedKeyword,
    sort,
    sortBy,
    pageNum,
    pageSize,
    unitFilter,
    envType === ENV_TYPE.AMBULATORY ? ambFilter.join(",") : "",
    envType === ENV_TYPE.INPATIENT ? allAmbUnits.join(",") : "",
    envType
  );
  const { mutate: markReadMessage } = useMarkReadMessageMutation();
  const { mutate: markReadNotification } = useMarkReadNotificationMutation();
  const { mutate: archiveMessage } = useArchiveMessageMutation();
  const { data: notificationData } = useGetNotificationsQuery(staffID, envType);

  useEffect(() => {
    const notificationIndex = menuData.findIndex(
      (data: any) => data.name === INBOX_TITLE.NOTIFICATION
    );
    const groupMenu = menuData.filter((data: any) => data.type === "group");
    if (
      notificationData?.data?.length > 0 &&
      (notificationIndex < 0 ||
        groupMenu.length != notificationData.data.length)
    ) {
      const messageMenu = menuData.filter(
        (data: any) =>
          data.type !== "group" && data.type !== INBOX_TYPE.NOTIFICATION
      );
      let cloneMenuData = cloneDeep(messageMenu);
      if (notificationIndex < 0) {
        cloneMenuData.push({
          type: INBOX_TYPE.HEADER,
          name: INBOX_TITLE.NOTIFICATION,
          unread: 0,
          total: 0,
          data: [],
          pType: "",
        });
      }
      notificationData.data.forEach((module: any) => {
        let groupData = module?.groups.map((group: any) => group.data);
        cloneMenuData.push({
          type: "group",
          name: module.module,
          unread: module.unread ?? 0,
          total: module.total ?? 0,
          data: groupData.flat(),
          pType: "",
        });
        module?.groups.forEach((notification: any) => {
          cloneMenuData.push({
            type: INBOX_TYPE.NOTIFICATION,
            pType: module.module,
            name: notification.groupName,
            unread: notification.unread ?? 0,
            total: notification.total ?? 0,
            data: notification.data,
          });
        });
      });
      setMenuData(cloneMenuData);
    }
  }, [notificationData]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedKeyword(keyword);
    }, 500);
    if (!inboxConfig) {
      setPageNum(1);
      setHasMore(false);
      clearSelected();
    }
    return () => clearTimeout(timer);
  }, [keyword]);

  useEffect(() => {
    const container = listRef.current;
    if (!container) return;
    const updateStickyWidth = () => {
      if (container.scrollHeight > container.clientHeight) {
        // has scrollbar
        setStickyWidth(572);
      } else {
        setStickyWidth(586);
      }
    };
    const handleScroll = () => {
      requestAnimationFrame(() => {
        let maxTop = -Infinity;
        let lastVisibleIndex = -1;
        let listTop = container.getBoundingClientRect().top;
        specialItemRefs.current.forEach((item: any, index: number) => {
          if (!item) return;
          const rect = item.getBoundingClientRect();
          if (rect.top <= listTop) {
            if (rect.top > maxTop) {
              maxTop = rect.top;
              lastVisibleIndex = index;
            }
          }
        });
        setStickyIdx(
          lastVisibleIndex >= 0
            ? specialIndices[lastVisibleIndex]
            : lastVisibleIndex
        );
      });
    };
    updateStickyWidth();
    window.addEventListener("resize", updateStickyWidth);
    container?.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("resize", updateStickyWidth);
      container?.removeEventListener("scroll", handleScroll);
    };
  }, [specialIndices]);

  useEffect(() => {
    if (!_.isEmpty(menu)) {
      if (!isEmpty(lastMenuData)) {
        setMenuData(lastMenuData);
        setLastMenuData([]);
      }
      resetState(_.includes(ALL_MESSAGE_TYPE, menu.name) ? menu.name : "");
      if (!_.includes(ALL_MESSAGE_TYPE, menu.name)) {
        filterData(menu.data);
        setMsgTitle((menu?.pType ? menu.pType + ", " : "") + menu.name);
      } else if (inboxConfig && type === INBOX_TYPE.MESSAGE) {
        setStorePageNum(inboxConfig.pageNum);
        setMessageList(inboxConfig.messageList);
        filterData(inboxConfig.messageList);
        setStickyIdx(-1);
      }
    }
  }, [menu, ambFilter, envType]);

  useEffect(() => {
    setMenuData((prevData) =>
      prevData.map((item) => {
        let data = [];
        let unreadNum = 0;
        let total = 0;
        let hasProcess = false;
        if (item.type === INBOX_TYPE.MESSAGE) {
          if (
            item.name === MESSAGE_TYPE.GC_RECEIVED &&
            messagesData?.GC_Received
          ) {
            unreadNum = messagesData?.GC_Received.unread;
            data = messagesData?.GC_Received?.data;
            hasProcess = true;
          } else if (
            item.name === MESSAGE_TYPE.PP_RECEIVED &&
            messagesData?.PP_Received
          ) {
            unreadNum = messagesData?.PP_Received.unread;
            data = messagesData?.PP_Received?.data;
            hasProcess = true;
          } else if (item.name === MESSAGE_TYPE.SENT && messagesData?.Sent) {
            unreadNum = messagesData?.Sent.unread;
            data = messagesData?.Sent?.data;
            hasProcess = true;
          } else if (
            item.name === MESSAGE_TYPE.ARCHIVED &&
            messagesData?.Archived
          ) {
            unreadNum = messagesData?.Archived.unread;
            data = messagesData?.Archived?.data;
            hasProcess = true;
          }
        }
        if (item.type === "group" || item.type === INBOX_TYPE.NOTIFICATION) {
          if (notificationData?.data?.length > 0) {
            unreadNum = item.unread;
            total = item.total;
            data = item.data;
            hasProcess = true;
          }
        }
        if (hasProcess) {
          if (item.type === "group" || item.type === INBOX_TYPE.NOTIFICATION) {
            if (envType === "Inpatient") {
              if (unitFilter != "All") {
                unreadNum = data.filter((msg: any) => {
                  return (
                    msg.status == 0 &&
                    ((unitFilter === "Active" &&
                      _.toUpper(msg.patient.unit) !== "DISCH") ||
                      (unitFilter === "Discharge" &&
                        _.toUpper(msg.patient.unit) === "DISCH"))
                  );
                }).length;
                total = data.filter((msg: any) => {
                  return (
                    (unitFilter === "Active" &&
                      _.toUpper(msg.patient.unit) !== "DISCH") ||
                    (unitFilter === "Discharge" &&
                      _.toUpper(msg.patient.unit) === "DISCH")
                  );
                }).length;
              } else {
                unreadNum = data?.length;
                total = data?.length;
              }
            } else {
              unreadNum = data.filter((msg: any) => {
                return msg.status == 0 && ambFilter.includes(msg.patient.unit);
              }).length;
              total = data.filter((msg: any) => {
                return ambFilter.includes(msg.patient.unit);
              }).length;
            }
          }
          return { ...item, unread: unreadNum, total: total };
        } else {
          return item;
        }
      })
    );
  }, [messagesData, notificationData, unitFilter, ambFilter, envType]);

  useEffect(() => {
    if (inboxConfig || pageNum <= storePageNum) return;

    const folderDataMap = {
      [MESSAGE_TYPE.GC_RECEIVED]: messagesData?.GC_Received,
      [MESSAGE_TYPE.PP_RECEIVED]: messagesData?.PP_Received,
      [MESSAGE_TYPE.SENT]: messagesData?.Sent,
      [MESSAGE_TYPE.ARCHIVED]: messagesData?.Archived,
    };

    const currentFolderData = folderDataMap[msgFolder];
    const currentData = currentFolderData?.data;
    const isDataValid = Boolean(currentData);

    if (!isDataValid) return;

    const shouldReplace = pageNum === 1;
    setMessageList((prev) =>
      shouldReplace ? currentData : [...prev, ...currentData]
    );

    if (shouldReplace) clearSelected();

    setHasMore(currentData.length >= 10);
    setDataTotal(currentFolderData.total || 0);
    setMsgTitle(keyword?.length >= 3 ? "Search Results" : msgFolder);

    setStickyIdx(-1);
  }, [messagesData, unitFilter]);

  useEffect(() => {
    if (inboxConfig || pageNum <= storePageNum) {
      return;
    }
    if (type === INBOX_TYPE.MESSAGE) filterData(messageList);
  }, [messageList]);

  useEffect(() => {
    if (type === INBOX_TYPE.MESSAGE) setIsLoading(false);
    if (inboxConfig && dataList.length > 0) {
      if (type === INBOX_TYPE.MESSAGE) {
        if (inboxConfig.pageNum > 1) {
          setPageNum(inboxConfig.pageNum);
        }
        setSpecialIndices(inboxConfig.specialIndices);
        setDataTotal(inboxConfig.total);
        setMsgTitle(msgFolder);
        setHasMore(true);
      }
      if (inboxConfig.selectedIdx >= 0) {
        let idx = dataList.findIndex(
          (msg) =>
            (type === INBOX_TYPE.NOTIFICATION &&
              msg.data?.accessionNumber ==
                inboxConfig.selectedAccessionNumber) ||
            (type === INBOX_TYPE.MESSAGE &&
              (msgFolder === MESSAGE_TYPE.SENT
                ? msg.data?.msgID == inboxConfig.selectedAccessionNumber
                : msg.data?.receiveID == inboxConfig.selectedAccessionNumber))
        );
        setSelectedIdx(idx);
        if (idx >= 0) {
          setMessage(dataList[idx].data);
          if (detailPanelStatus[detailPanelStatus.length - 1] === -1) {
            setDetailPanelStatus([...detailPanelStatus, 1]);
          }
        }
      }
      setInboxConfig(null);
    }
  }, [dataList]);

  useEffect(() => {
    if (!inboxConfig) {
      setStickyIdx(-1);
      clearSelected();
      filterData(type === INBOX_TYPE.MESSAGE ? messageList : menu?.data);
    }
  }, [unitFilter]);

  const resetState = (msgFolder: string) => {
    if (!inboxConfig) {
      setPageNum(1);
    }
    setDataTotal(0);
    setStorePageNum(0);
    setHasMore(false);
    setStickyIdx(-1);
    clearSelected();
    setSpecialIndices([]);
    setMessageList([]);
    setKeyword("");
    setMsgFolder(msgFolder);
  };

  const getTimePeriod = (timestamp: number) => {
    const now = new Date();
    const date = new Date(timestamp * 1000);

    const isSameDay = (d1: Date, d2: Date) =>
      d1.getFullYear() === d2.getFullYear() &&
      d1.getMonth() === d2.getMonth() &&
      d1.getDate() === d2.getDate();

    if (isSameDay(date, now)) {
      return "Today";
    }
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (isSameDay(date, yesterday)) {
      return "Yesterday";
    }
    const getWeekMonday = (d: Date) => {
      const newDate = new Date(d);
      const day = newDate.getDay();
      const diff = day >= 1 ? day - 1 : 6;
      newDate.setDate(newDate.getDate() - diff);
      newDate.setHours(0, 0, 0, 0);
      return newDate;
    };
    const currentWeekMonday = getWeekMonday(now);
    const dateWeekMonday = getWeekMonday(date);
    if (currentWeekMonday.getTime() === dateWeekMonday.getTime()) {
      return "This Week";
    }
    const lastWeekMonday = new Date(currentWeekMonday);
    lastWeekMonday.setDate(currentWeekMonday.getDate() - 7);
    if (dateWeekMonday.getTime() === lastWeekMonday.getTime()) {
      return "Last Week";
    }
    const isSameMonth = (d1: Date, d2: Date) =>
      d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth();

    if (isSameMonth(date, now)) {
      return "This Month";
    }
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    if (
      date.getFullYear() === lastMonth.getFullYear() &&
      date.getMonth() === lastMonth.getMonth()
    ) {
      return "Last Month";
    }
    if (date.getFullYear() === now.getFullYear()) {
      return "This Year";
    }
    return "Older";
  };

  const filterData = (data: any) => {
    let messages = [] as any;
    let indices = [] as any;
    let dateLabel = "";
    for (let i = 0; i < data?.length; i++) {
      if (type === INBOX_TYPE.MESSAGE) {
        let label =
          sortBy === "Date"
            ? getTimePeriod(
                _.includes(
                  [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
                  msgFolder
                )
                  ? data[i].receiveTime
                  : msgFolder === MESSAGE_TYPE.SENT
                    ? data[i].sendTime
                    : data[i].archiveTime
              )
            : sortBy === "Sender"
              ? data[i].senderName + " (" + data[i].senderLicenseType + ")"
              : data[i].patName +
                " (" +
                data[i].patient?.age +
                ", " +
                data[i].patient?.sex +
                ")";
        if (dateLabel !== label) {
          let msg = { type: INBOX_TYPE.HEADER, label: label, data: null };
          messages.push(msg);
          let idx = messages.indexOf(msg);
          indices.push(idx);
        }
        dateLabel = label;
      } else {
        const msg = data[i];
        if (
          (envType === ENV_TYPE.AMBULATORY &&
            ambFilter &&
            !ambFilter.includes(msg.patient.unit)) ||
          (envType === ENV_TYPE.INPATIENT &&
            unitFilter &&
            ((unitFilter === "Active" &&
              _.toUpper(msg.patient.unit) === "DISCH") ||
              (unitFilter === "Discharge" &&
                _.toUpper(msg.patient.unit) !== "DISCH")))
        ) {
          continue;
        }
      }
      messages.push({
        type: type,
        label: "",
        isLabResults:
          type === INBOX_TYPE.NOTIFICATION &&
          (menu?.name === NOTIFICATION_TYPE.LAB_RESULTS ||
            menu?.pType === NOTIFICATION_TYPE.LAB_RESULTS),
        data: data[i],
      });
    }
    setDataList(messages);
    if (type === INBOX_TYPE.MESSAGE) {
      setSpecialIndices(indices);
    }
    if (type === INBOX_TYPE.NOTIFICATION) setDataTotal(messages.length);
  };

  const parseLabResults = (content: string) => {
    if (_.isEmpty(content)) return <></>;
    let jsonArr = JSON.parse(content);

    return (
      <>
        {jsonArr.map((lab: any, idx: number) => {
          return idx <= 1 ? labView(lab) : <></>;
        })}
        {jsonArr.length > 2 && (
          <>
            <Typography
              component="span"
              sx={{ ...headerStyle, fontWeight: "700" }}
            >
              +{jsonArr.length - 2}
            </Typography>
            <Typography component="span" sx={headerStyle}>
              normal results
            </Typography>
          </>
        )}
      </>
    );
  };

  const handleSortByClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelectSort = (option: any) => {
    if (hasDraft()) {
      setShowDiscardDialog(
        detailPanelStatus[detailPanelStatus.length - 1] === 0 ? 1 : 2
      );
      return;
    }
    setSort(option.sort);
    setSortBy(option.sortBy);
    setSelectedSort(option.label);
    setPageNum(1);
    setHasMore(false);
    setStickyIdx(-1);
    clearSelected();
    handleClose();
  };

  const handleArchiveClick = () => {
    archiveMessage(
      {
        receiveids: selectedIds.join(","),
      },
      {
        onSuccess: (resp) => {
          if (resp?.success) {
            setDataList((preDataList) => {
              let filterList = preDataList.filter(
                (item: any) =>
                  item.type === INBOX_TYPE.HEADER ||
                  !_.includes(selectedIds, item.data.receiveID)
              );
              return filterList.filter(
                (item2: any, idx: number) =>
                  item2.type !== INBOX_TYPE.HEADER ||
                  (idx === filterList.length - 1 &&
                    item2.type !== INBOX_TYPE.HEADER) ||
                  (idx < filterList.length - 1 &&
                    filterList[idx + 1].type !== INBOX_TYPE.HEADER)
              );
            });
            setDataTotal(dataTotal - selectedIds.length);
          }
          clearSelected();
          if (!isEmpty(resp.msg)) {
            setShowAlert(resp.success ? 1 : 2);
            setAlertInfo(resp.msg);
          }
        },
      }
    );
  };

  const handleMarkReadClick = () => {
    markRead(false, message, selectedIdx);
  };

  const updateUnreadStatus = (data: any, index: number) => {
    setDataList((preDataList) =>
      preDataList.map((item: any, idx: number) =>
        index == idx
          ? {
              ...item,
              data: {
                ...item.data,
                status: data.status == 1 ? 0 : 1,
              },
            }
          : item
      )
    );
    if (!isEmpty(message)) {
      setMessage((preMessage: any) => {
        return {
          ...preMessage,
          status:
            (type === INBOX_TYPE.MESSAGE &&
              preMessage.receiveID == data.receiveID) ||
            (type === INBOX_TYPE.NOTIFICATION &&
              preMessage.accessionNumber == data.accessionNumber)
              ? data.status == 1
                ? 0
                : 1
              : preMessage.status,
        };
      });
    }
    if (type == INBOX_TYPE.NOTIFICATION) {
      setMenuData((preMenuData) =>
        preMenuData.map((menu: any, idx: number) =>
          menu.name == data?.module ||
          (menu.name == data?.groupName && menu.pType == data?.module)
            ? {
                ...menu,
                data: menu.data.map((item: any) =>
                  item.accessionNumber == data.accessionNumber
                    ? { ...item, status: data.status == 1 ? 0 : 1 }
                    : item
                ),
              }
            : menu
        )
      );
    }
    setMenuData((prevData) =>
      prevData.map((item) =>
        item.name === msgFolder
          ? {
              ...item,
              unread: data.status == 1 ? item.unread + 1 : item.unread - 1,
            }
          : item
      )
    );
  };

  const markRead = (isItemClick: boolean, data: any, index: number) => {
    if (
      _.includes(WITHOUT_RECEIVED_MESSAGE_TYPE, msgFolder) ||
      (isItemClick && data?.status == 1)
    )
      return;
    if (type === INBOX_TYPE.MESSAGE) {
      markReadMessage(
        {
          receiveID: data.receiveID,
          status: data.status == 1 ? 0 : 1,
        },
        {
          onSuccess: (resp) => {
            if (resp?.success) {
              updateUnreadStatus(data, index);
            }
          },
        }
      );
    } else {
      markReadNotification(
        {
          staffID: staffID,
          dbpath: data?.dbpath,
          module: data?.module,
          groupName: data?.groupName,
          accessionNumber: data?.accessionNumber,
          status: data?.status == 1 ? 0 : 1,
        },
        {
          onSuccess: (resp) => {
            if (resp?.success) {
              updateUnreadStatus(data, index);
            }
          },
        }
      );
    }
  };

  const toDetail = (data: any, index: number) => {
    data.time = _.includes(
      [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
      msgFolder
    )
      ? data?.receiveTimeStr
      : msgFolder === MESSAGE_TYPE.SENT
        ? data?.sendTimeStr
        : data?.archiveTimeStr;
    if (detailPanelStatus[detailPanelStatus.length - 1] === 0) {
      setDetailPanelStatus([...detailPanelStatus.slice(0, -1), 1]);
    }
    setMessage(data);
    setDataType(type);
    if (data.module === "Orders") {
      setRelativeOrders(
        dataList
          .map((item) => item.data)
          .filter(
            (order) =>
              order?.accessionNumber != data.accessionNumber &&
              order?.dbpath == data.dbpath
          )
      );
    }
    if (detailPanelStatus[detailPanelStatus.length - 1] === -1) {
      setDetailPanelStatus([...detailPanelStatus, 1]);
    }
    if (lastReadData && lastReadIdx >= 0 && lastReadIdx != index) {
      markRead(true, lastReadData, lastReadIdx);
    }
    setLastReadData(data);
    setLastReadIdx(index);
  };

  const deleteDetail = () => {
    if (detailPanelStatus[detailPanelStatus.length - 1] === 1) {
      setDetailPanelStatus(detailPanelStatus.slice(0, -1));
      clearDraft();
    }
  };

  const clearSelected = () => {
    setSelectedIds([]);
    setLastSelectedId(null);
    setLastReadData(null);
    setLastReadIdx(-1);
    if (!inboxConfig) {
      setSelectedIdx(-1);
    }
    setDetailPanelStatus([-1]);
    clearDraft();
  };

  const handleDetail = (newSelectedIds: any[]) => {
    if (newSelectedIds.length == 1) {
      let idx = dataList.findIndex(
        (item) =>
          item.type !== INBOX_TYPE.HEADER &&
          item.data.receiveID == newSelectedIds[newSelectedIds.length - 1]
      );
      toDetail(dataList[idx].data, idx);
    } else {
      deleteDetail();
    }
  };

  const handleItemClick = (
    event: any,
    type: string,
    data: any,
    index: number
  ) => {
    if (hasDraft()) {
      setShowDiscardDialog(
        detailPanelStatus[detailPanelStatus.length - 1] === 0 ? 1 : 2
      );
      return;
    }

    if (
      _.includes(
        [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
        msgFolder
      )
    ) {
      const { ctrlKey, shiftKey } = event;
      let newSelectedIds = [] as any;

      if (shiftKey) {
        if (!lastSelectedId) {
          newSelectedIds = [data.receiveID];
        } else {
          const itemIds = dataList
            .filter((item) => item.type !== INBOX_TYPE.HEADER)
            .map((item) => item.data.receiveID);
          const startIndex = itemIds.indexOf(lastSelectedId);
          const endIndex = itemIds.indexOf(data.receiveID);

          if (startIndex === -1 || endIndex === -1) {
            newSelectedIds = [data.receiveID];
          } else {
            const start = Math.min(startIndex, endIndex);
            const end = Math.max(startIndex, endIndex);
            newSelectedIds = itemIds.slice(start, end + 1);
          }
        }
        handleDetail(newSelectedIds);
      } else if (ctrlKey) {
        const wasSelected = selectedIds.includes(data.receiveID);
        newSelectedIds = wasSelected
          ? selectedIds.filter((id) => id !== data.receiveID)
          : [...selectedIds, data.receiveID];

        if (!wasSelected) {
          setLastSelectedId(data.receiveID);
        } else if (data.receiveID === lastSelectedId) {
          if (newSelectedIds.length === 0) {
            setLastSelectedId(null);
          } else {
            const lastItem = newSelectedIds.reduce(
              (acc: any, id: any) => {
                const index = dataList
                  .filter((item) => item.type !== INBOX_TYPE.HEADER)
                  .findIndex((item) => item.data.receiveID === id);
                return index > acc.index ? { id, index } : acc;
              },
              { id: null, index: -1 }
            ).id;
            setLastSelectedId(lastItem);
          }
        }
        handleDetail(newSelectedIds);
      } else {
        newSelectedIds = [data.receiveID];
        setLastSelectedId(data.receiveID);
        toDetail(data, index);
      }
      setSelectedIdx(
        dataList.findIndex(
          (item) =>
            item.type !== INBOX_TYPE.HEADER &&
            item.data.receiveID == newSelectedIds[newSelectedIds.length - 1]
        )
      );
      setSelectedIds(newSelectedIds);
    } else {
      setSelectedIds([]);
      setLastSelectedId(null);
      setSelectedIdx(index);
      toDetail(data, index);
    }
  };

  const reviewedCallback = (data: any, index: number) => {
    setDataList((preDataList) =>
      preDataList.map((item: any, idx: number) =>
        index == idx
          ? {
              ...item,
              data: {
                ...item.data,
                reviewed: true,
              },
            }
          : item
      )
    );
    if (!isEmpty(message) && message?.accessionNumber == data.accessionNumber) {
      setMessage((preMessage: any) => {
        return { ...preMessage, reviewed: true };
      });
    }
    if (isEmpty(lastMenuData)) {
      setLastMenuData(
        menuData.map((menu: any, idx: number) =>
          menu.name == data?.module ||
          (menu.name == data?.groupName && menu.pType == data?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != data.accessionNumber
                ),
              }
            : menu
        )
      );
    } else {
      setLastMenuData((preMenuData) =>
        preMenuData.map((menu: any, idx: number) =>
          menu.name == data?.module ||
          (menu.name == data?.groupName && menu.pType == data?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != data.accessionNumber
                ),
              }
            : menu
        )
      );
    }
  };

  const signedAndCosignedCallback = (
    data: any,
    index: number,
    isSigned: boolean
  ) => {
    setDataList((preDataList) =>
      preDataList.map((item: any, idx: number) =>
        index == idx
          ? {
              ...item,
              data: {
                ...item.data,
                cosigned: data.groupName == "Cosign",
                signed: data.groupName == "Sign",
              },
            }
          : item
      )
    );
    if (!isEmpty(message)) {
      setMessage((preMessage: any) => {
        return {
          ...preMessage,
          cosigned:
            preMessage.orderID == data.orderID &&
            preMessage.key == data.key &&
            preMessage.groupName == "Cosign",
          signed:
            preMessage.orderID == data.orderID &&
            preMessage.key == data.key &&
            preMessage.groupName == "Sign",
        };
      });
    }
    if (isEmpty(lastMenuData)) {
      setLastMenuData(
        menuData.map((menu: any, idx: number) =>
          menu.name == data?.module ||
          (menu.name == data?.groupName && menu.pType == data?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != data.accessionNumber
                ),
              }
            : menu
        )
      );
    } else {
      setLastMenuData((preMenuData) =>
        preMenuData.map((menu: any, idx: number) =>
          menu.name == data?.module ||
          (menu.name == data?.groupName && menu.pType == data?.module)
            ? {
                ...menu,
                total: menu.total - 1,
                data: menu.data.filter(
                  (item: any) => item.accessionNumber != data.accessionNumber
                ),
              }
            : menu
        )
      );
    }
  };

  const handleActionClick = (event: any, data: any, index: number) => {
    if (
      notHasPerm(
        data,
        hasSignOrderPerm,
        hasCosignOrderPerm,
        hasSignNotePerm,
        hasCosignNotePerm
      ) ||
      (type === INBOX_TYPE.NOTIFICATION &&
        (data?.reviewed || data?.signed || data?.cosigned))
    )
      return;
    event.stopPropagation();
    if (
      _.includes(
        [
          NOTIFICATION_TYPE.LAB_RESULTS,
          NOTIFICATION_TYPE.REPORTS,
          NOTIFICATION_TYPE.IMAGING_REPORTS,
        ],
        data.module
      )
    ) {
      markReviewed(
        data?.module == NOTIFICATION_TYPE.LAB_RESULTS
          ? ORDER_TYPE.LABORATORY
          : data?.groupName == "Microbiology"
            ? ORDER_TYPE.MICROBIOLOGY
            : data?.groupName == "Pathology"
              ? ORDER_TYPE.PATHOLOGY
              : "",
        data?.key,
        data?.orderID,
        data?.nit,
        data?.nits,
        data?.title,
        data?.dbpath
      )
        .then(() => {
          setShowAlert(1);
          setAlertInfo("Labs Marked as Reviewed");
          reviewedCallback(data, index);
        })
        .catch((e) => {
          setShowAlert(2);
          setAlertInfo("Failed to mark as reviewed!");
        });
    } else if ("Orders" == data.module) {
      let actions =
        data.groupName == "Sign"
          ? { SIGN: 1, btn: "action" }
          : { COSIGN: 1, btn: "action" };
      saveOrderAction(
        [
          {
            _action: actions,
            key: data.key,
            order_id: data.orderID,
          },
        ],
        data.dbpath
      )
        .then(() => {
          setShowAlert(1);
          setAlertInfo(
            "Order " +
              (data.groupName == "Sign" ? "signed" : "cosigned") +
              " success"
          );
          signedAndCosignedCallback(data, index, data.groupName == "Sign");
        })
        .catch(() => {
          setShowAlert(2);
          setAlertInfo(
            "Order " +
              (data.groupName == "Sign" ? "signed" : "cosigned") +
              " fail"
          );
        });
    } else if (NOTIFICATION_TYPE.NOTES == data.module) {
      if ("Sign" == data.groupName) {
        signNote({
          dbpath: data?.dbpath,
          query: "query.sign.ycql",
          output: "output.signCosign.ycql",
          notedata: [{ nit: data?.nit, key: data?.key }],
        })
          .then(() => {
            setShowAlert(1);
            setAlertInfo("Note signed success");
            signedAndCosignedCallback(data, index, true);
          })
          .catch(() => {
            setShowAlert(2);
            setAlertInfo("Note signed fail");
          });
      } else {
        coSignNote({
          dbpath: data?.dbpath,
          query: "query.cosign.ycql",
          output: "output.signCosign.ycql",
          notedata: [{ nit: data?.nit, key: data?.key }],
        })
          .then(() => {
            setShowAlert(1);
            setAlertInfo("Note cosigned success");
            signedAndCosignedCallback(data, index, false);
          })
          .catch(() => {
            setShowAlert(2);
            setAlertInfo("Note cosigned fail");
          });
      }
    }
  };

  const convertToContent = (content: any) => {
    return (
      "Reschedule Details: " +
      content.apptType +
      "," +
      content.reasonForVisit +
      "," +
      content.doctorName +
      (content.doctorLicenseType ? "(" + content.doctorLicenseType + ")" : "") +
      "," +
      content.facility
    );
  };

  const messageView = (data: any) => {
    return (
      <>
        <Box
          sx={{ height: "100%", display: "flex", flexDirection: "flex-top" }}
        >
          <Avatar
            data-testid="patient-header-photo"
            alt="CCI Avatar"
            variant="rounded"
            sx={{
              borderRadius: "50px",
              height: "32px",
              width: "32px",
            }}
            src={
              data?.dbpath
                ? `${cci.cfg.baseUrl}/index.php/ImgUtil/getPatImage?dbpath=${data?.dbpath}`
                : undefined
            }
          >
            {data?.dbpath ? undefined : (
              <IconProfile style={{ width: "32px", height: "32px" }} />
            )}
          </Avatar>
        </Box>
        <Box
          sx={{
            flex: 1,
            height: "100%",
            display: "flex",
            minWidth: "0px",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "32px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                flex: 1,
                minWidth: "0px",
                display: "flex",
                alignItems: "center",
                gap: "4px",
              }}
            >
              <Box
                sx={{
                  minWidth: "0px",
                  maxWidth: "260px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <StyledFrom>
                  {highlightText(
                    data?.patient?.patName,
                    keyword && keyword.length >= 3 ? keyword : ""
                  )}
                </StyledFrom>
              </Box>
              <StyledFromSuffix>
                ({data?.patient?.age}, {data?.patient?.sex})
              </StyledFromSuffix>
              {_.toUpper(data?.patient.unit) == "DISCH" && (
                <StyledLabel
                  sx={{
                    backgroundColor: "#FBD60D",
                    borderRadius: "100px",
                    padding: "0px 8px",
                    ml: "8px",
                  }}
                >
                  DISCH
                </StyledLabel>
              )}
            </Box>
            {data?.attachments.length > 0 && (
              <IconAttachment style={{ width: "24px", height: "24px" }} />
            )}
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <StyledComment
              sx={{
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
              }}
            >
              {highlightText(
                data?.senderName,
                keyword && keyword.length >= 3 ? keyword : ""
              )}
              {data?.senderLicenseType && `(${data?.senderLicenseType})`}
            </StyledComment>
            {data?.msgType &&
              _.includes(
                [MESSAGE_TYPE.SENT, MESSAGE_TYPE.ARCHIVED],
                msgFolder
              ) && (
                <StyledLabel
                  sx={{
                    backgroundColor: "#A24AE84D",
                    borderRadius: "8px",
                    padding: "1px 8px",
                    ml: "8px",
                  }}
                >
                  Patient Portal
                </StyledLabel>
              )}
          </Box>
          <Box
            sx={{
              width: "100%",
              display: "flex",
              minWidth: "0px",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <StyledSubject
              sx={{
                maxWidth: "300px",
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
              }}
            >
              {highlightText(
                data?.module === NOTIFICATION_TYPE.SCHEDULING_REQUESTS
                  ? data?.subject
                  : data?.msgSubject,
                keyword && keyword.length >= 3 ? keyword : ""
              )}
            </StyledSubject>
            <StyledTime>
              {_.includes(
                [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
                msgFolder
              )
                ? data?.receiveTimeStr
                : msgFolder === MESSAGE_TYPE.SENT
                  ? data?.sendTimeStr
                  : data?.module === NOTIFICATION_TYPE.SCHEDULING_REQUESTS
                    ? data?.createTimeStr
                    : data?.archiveTimeStr}
            </StyledTime>
          </Box>
          <StyledComment
            sx={{
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              overflow: "hidden",
            }}
          >
            {highlightText(
              data?.module === NOTIFICATION_TYPE.SCHEDULING_REQUESTS
                ? convertToContent(data?.content)
                : data?.msgContent,
              keyword && keyword.length >= 3 ? keyword : ""
            )}
          </StyledComment>
        </Box>
      </>
    );
  };

  const notificationView = (
    data: any,
    isLabResults: boolean,
    index: number
  ) => {
    if (!data.patient.patName) {
      getPatInfo({ dbpath: data.dbpath }).then((res) => {
        setDataList((prevDataList) =>
          prevDataList.map((item: any, idx: number) =>
            idx === index && item.type === INBOX_TYPE.NOTIFICATION
              ? { ...item, data: { ...item.data, patient: res.data } }
              : item
          )
        );
      });
    }
    return (
      <>
        {_.includes(
          [
            NOTIFICATION_TYPE.LAB_RESULTS,
            NOTIFICATION_TYPE.REPORTS,
            NOTIFICATION_TYPE.IMAGING_REPORTS,
            "Orders",
          ],
          data.module
        ) && (
          <StyledReview
            sx={{
              backgroundColor:
                type === INBOX_TYPE.NOTIFICATION &&
                (data?.reviewed || data?.cosigned || data?.signed)
                  ? "#98C99A"
                  : "#FFF",
              border:
                type === INBOX_TYPE.NOTIFICATION &&
                (data?.reviewed || data?.cosigned || data?.signed)
                  ? "none"
                  : "1px solid #B1B1B1",
              color: notHasPerm(
                data,
                hasSignOrderPerm,
                hasCosignOrderPerm,
                hasSignNotePerm,
                hasCosignNotePerm
              )
                ? "#ACACAC"
                : type === INBOX_TYPE.NOTIFICATION &&
                    (data?.reviewed || data?.cosigned || data?.signed)
                  ? "#FFF"
                  : "#000",
              "&:hover": {
                backgroundColor: notHasPerm(
                  data,
                  hasSignOrderPerm,
                  hasCosignOrderPerm,
                  hasSignNotePerm,
                  hasCosignNotePerm
                )
                  ? "#ACACAC"
                  : type === INBOX_TYPE.NOTIFICATION &&
                      (data?.reviewed || data?.cosigned || data?.signed)
                    ? "#98C99A"
                    : "rgba(66, 110, 182, 0.4)",
              },
            }}
            onClick={(e: any) => handleActionClick(e, data, index)}
          >
            {_.includes(
              [
                NOTIFICATION_TYPE.LAB_RESULTS,
                NOTIFICATION_TYPE.REPORTS,
                NOTIFICATION_TYPE.IMAGING_REPORTS,
              ],
              data.module
            )
              ? data?.reviewed
                ? "Reviewed"
                : "Review"
              : data?.cosigned
                ? "Cosigned"
                : data?.signed
                  ? "Signed"
                  : data.groupName}
          </StyledReview>
        )}
        <Box
          sx={{
            flex: 1,
            minWidth: "0px",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            ml:
              data.module == NOTIFICATION_TYPE.NOTES ||
              data.module == NOTIFICATION_TYPE.ENCOUNTERS
                ? "0px"
                : "14px",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "32px",
              minWidth: "0px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Avatar
              data-testid="patient-header-photo"
              alt="CCI Avatar"
              variant="rounded"
              sx={{
                borderRadius: "50px",
                height: "32px",
                width: "32px",
              }}
              src={
                data?.dbpath
                  ? `${cci.cfg.baseUrl}/index.php/ImgUtil/getPatImage?dbpath=${data?.dbpath}`
                  : undefined
              }
            >
              {data?.dbpath ? undefined : (
                <IconProfile style={{ width: "32px", height: "32px" }} />
              )}
            </Avatar>
            <Box
              sx={{
                flex: 1,
                minWidth: "0px",
                display: "flex",
                alignItems: "center",
                gap: "4px",
                ml: "8px",
              }}
            >
              <Box
                sx={{
                  minWidth: "0px",
                  maxWidth: "200px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <StyledFrom>{data?.patient?.patName}</StyledFrom>
              </Box>
              <StyledFromSuffix>
                ({data?.patient?.age}, {data?.patient?.sex})
              </StyledFromSuffix>
              {_.toUpper(data?.patient.unit) == "DISCH" && (
                <StyledLabel
                  sx={{
                    backgroundColor: "#FBD60D",
                    borderRadius: "100px",
                    padding: "0px 8px",
                    ml: "8px",
                  }}
                >
                  DISCH
                </StyledLabel>
              )}
            </Box>
            {data?.module !== NOTIFICATION_TYPE.RX_REQUESTS &&
              data?.groupName !== "Rx Refill" && (
                <IconExternalLink
                  style={{ width: "18px", height: "18px" }}
                  onClick={(e) =>
                    handleLinkClick(
                      e,
                      messageList,
                      data,
                      type,
                      unitFilter,
                      selectedSort,
                      sort,
                      sortBy,
                      pageNum,
                      hasMore,
                      specialIndices,
                      selectedMenuIdx,
                      selectedIdx,
                      data.accessionNumber,
                      data.accessionNumber,
                      scrollTop,
                      dataTotal,
                      envType,
                      ambFilter
                    )
                  }
                />
              )}
          </Box>
          <Box
            sx={{
              width: "100%",
              height: "21px",
              minWidth: "0px",
              display: "flex",
              alignItems: "center",
              gap: "8px",
              paddingLeft: "8px",
            }}
          >
            {isLabResults && data?.flag > 0 && (
              <Box
                sx={{
                  width: "16px",
                  height: "16px",
                  background: data?.flag == 1 ? "#832522" : "#EC1C24",
                }}
              />
            )}
            {NOTIFICATION_TYPE.RX_REQUESTS == data?.module &&
            "Rx Refill" == data?.groupName ? (
              <StyledComment>
                {data?.createdBy + " (" + data?.licenseType + ")"}
              </StyledComment>
            ) : (
              <StyledTitle2>
                {NOTIFICATION_TYPE.NOTES == data?.module
                  ? "(" + data?.groupName + ") " + data?.subject
                  : NOTIFICATION_TYPE.ENCOUNTERS == data?.module
                    ? "(" + data?.groupName + ") " + data?.title
                    : "Orders" == data?.module
                      ? data?.subject
                      : data?.title}
              </StyledTitle2>
            )}
          </Box>
          <Box
            sx={{
              width: "100%",
              height: "18px",
              minWidth: "0px",
              display: "flex",
              alignItems: "center",
              paddingLeft: "8px",
            }}
          >
            <Box
              sx={{
                flex: 1,
                height: "100%",
                display: "flex",
                alignItems: "center",
                gap: "4px",
              }}
            >
              {_.includes(["Orders", NOTIFICATION_TYPE.NOTES], data?.module) ? (
                <>
                  <IconProfile style={{ width: "18px", height: "18px" }} />
                  <StyledComment
                    sx={{
                      maxWidth: "180px",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    }}
                  >
                    {data?.createdBy}
                  </StyledComment>
                  {data?.licenseType && (
                    <StyledFromSuffix>({data?.licenseType})</StyledFromSuffix>
                  )}
                </>
              ) : (
                <StyledSubject>{data?.subject}</StyledSubject>
              )}
            </Box>
            <StyledTime>{data?.createTimeStr}</StyledTime>
          </Box>
          {(isLabResults || "Rx Refill" == data?.groupName) && (
            <Box
              sx={{
                width: "100%",
                minWidth: "0px",
                height: "18px",
                paddingLeft: "8px",
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
              }}
            >
              {isLabResults ? (
                parseLabResults(data?.content)
              ) : "Rx Refill" == data?.groupName ? (
                <StyledHeader
                  sx={{
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                  }}
                >
                  {data?.title}
                </StyledHeader>
              ) : data?.content ? (
                <StyledHeader
                  sx={{
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                  }}
                >
                  {data?.content}
                </StyledHeader>
              ) : (
                <></>
              )}
            </Box>
          )}
        </Box>
      </>
    );
  };

  const itemView = (item: any, index: number) => {
    return (
      <Box
        key={index}
        ref={(el: HTMLElement) => {
          if (specialIndices.includes(index)) {
            specialItemRefs.current[specialIndices.indexOf(index)] = el;
          }
        }}
        sx={{
          width: `${stickyWidth}px`,
          height:
            item.type === INBOX_TYPE.HEADER
              ? "26px"
              : type === INBOX_TYPE.MESSAGE
                ? "126px"
                : item.data?.module == NOTIFICATION_TYPE.NOTES ||
                    item.data?.module == NOTIFICATION_TYPE.ENCOUNTERS
                  ? "103px"
                  : "129px",
          display: "flex",
          alignItems: "center",
          borderRadius: item.type === INBOX_TYPE.HEADER ? "0px" : "8px",
          background:
            type === INBOX_TYPE.NOTIFICATION && item.data?.reviewed
              ? "#D6EAD7"
              : "#FFF",
          boxShadow:
            item.type === INBOX_TYPE.HEADER
              ? null
              : "0px 0px 5px 0px rgba(0, 0, 0, 0.25)",
          position: "relative",
          zIndex: 1,
          marginLeft: "8px",
          marginBottom: index === dataList.length - 1 ? "8px" : "0px",
          flexGrow: 0,
          flexShrink: 0,
        }}
      >
        {item.type === INBOX_TYPE.HEADER ? (
          <StyledHeader>{item.label}</StyledHeader>
        ) : (
          <Box
            sx={{
              ...styledItemBox,
              background:
                type === INBOX_TYPE.NOTIFICATION &&
                (item.data?.reviewed ||
                  item.data?.cosigned ||
                  item.data?.signed)
                  ? "#D6EAD7"
                  : (_.includes(
                        [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
                        msgFolder
                      ) &&
                        _.includes(selectedIds, item.data.receiveID)) ||
                      (selectedIds.length == 0 && selectedIdx === index)
                    ? "#F9EEBC"
                    : "#FFF",
              userSelect: "none",
            }}
            onClick={(event) =>
              handleItemClick(event, item.type, item.data, index)
            }
          >
            <Box
              sx={{
                width: "8px",
                height:
                  type === INBOX_TYPE.NOTIFICATION &&
                  (item.data?.module == NOTIFICATION_TYPE.NOTES ||
                    item.data?.module == NOTIFICATION_TYPE.ENCOUNTERS)
                    ? "87px"
                    : "110px",
                background:
                  type === INBOX_TYPE.NOTIFICATION &&
                  (item.data?.reviewed ||
                    item.data?.cosigned ||
                    item.data?.signed)
                    ? "#98C99A"
                    : _.includes(
                          [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
                          msgFolder
                        ) && item.data.status == 0
                      ? "#426EB6"
                      : type === INBOX_TYPE.NOTIFICATION
                        ? item?.isLabResults && item.data.flag == 2
                          ? "#EC1C24"
                          : item?.isLabResults && item.data.flag == 1
                            ? "#832522"
                            : item.data.status == 0
                              ? "#426EB6"
                              : "#CBCACA"
                        : "#CBCACA",
                borderRadius: "16px",
                margin: "0px 4px",
              }}
            />
            {item.type === INBOX_TYPE.MESSAGE ||
            item.data.module === NOTIFICATION_TYPE.SCHEDULING_REQUESTS
              ? messageView(item.data)
              : notificationView(item.data, item?.isLabResults, index)}
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        width: "604px",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        padding: "8px 8px 8px 0px",
        flexShrink: 0,
        borderRadius: "8px",
        background: "#fff",
        boxShadow: "0px 2px 6px 0px rgba(0, 0, 0, 0.15)",
      }}
    >
      <Box
        sx={{
          height: "32px",
          display: "flex",
          alignItems: "center",
          marginLeft: "8px",
        }}
      >
        <SearchField
          data-testid="inbox-search-field"
          data={keyword}
          setData={setKeyword}
          placeholder="Search"
          disabled={type === INBOX_TYPE.NOTIFICATION || hasDraft()}
          sx={{ width: "310px", height: "32px" }}
        />
        <StyledIconButton
          data-testid="search-settings-btn"
          color="secondary"
          disabled={type === INBOX_TYPE.NOTIFICATION}
          onClick={handleSortByClick}
          sx={{ width: "32px", height: "32px", ml: "8px" }}
        >
          <Box
            sx={{
              width: "100%",
              height: "100%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconGroup />
            <Box
              sx={{
                height: "100%",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                marginLeft: "2px",
              }}
            >
              <IconRectangle />
              <IconPath />
            </Box>
          </Box>
        </StyledIconButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          aria-hidden={!Boolean(anchorEl)}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "left",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "left",
          }}
          sx={{
            "& .MuiPaper-root": {
              minWidth: 168,
            },
          }}
        >
          {sortList.map((option) => (
            <StyledMenuItem
              key={option.label}
              selected={option.label === selectedSort}
              onClick={() => handleSelectSort(option)}
            >
              {option.label}
            </StyledMenuItem>
          ))}
        </Menu>
        <StyledButton
          data-testid="archive-btn"
          color="secondary"
          onClick={handleArchiveClick}
          disabled={
            !_.includes(
              [MESSAGE_TYPE.GC_RECEIVED, MESSAGE_TYPE.PP_RECEIVED],
              msgFolder
            ) ||
            selectedIdx < 0 ||
            detailPanelStatus[detailPanelStatus.length - 1] === 0 ||
            isReply ||
            isReplyAll ||
            isForward
          }
          sx={{ width: "94px", ml: "8px" }}
          startIcon={<IconBtnArchive />}
        >
          Archive
        </StyledButton>
        <StyledButton
          data-testid="mark-read-btn"
          color="secondary"
          onClick={handleMarkReadClick}
          disabled={
            _.includes(WITHOUT_RECEIVED_MESSAGE_TYPE, msgFolder) ||
            type === INBOX_TYPE.NOTIFICATION ||
            selectedIds.length > 1 ||
            selectedIdx < 0 ||
            detailPanelStatus[detailPanelStatus.length - 1] === 0 ||
            isReply ||
            isReplyAll ||
            isForward
          }
          sx={{ width: "127px", ml: "8px" }}
          startIcon={<IconMarkUnread />}
        >
          {selectedIdx >= 0 && message?.status == 0
            ? "Mark Read"
            : "Mark Unread"}
        </StyledButton>
      </Box>
      <StyledTitle>
        {!isEmpty(msgTitle)
          ? msgTitle +
            (Number(dataTotal) > 0 ? "(" + String(dataTotal) + ")" : "")
          : ""}
      </StyledTitle>
      {dataList.length === 0 ? (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <StyledNoData>This Folder is Empty</StyledNoData>
        </Box>
      ) : (
        <>
          <ScrollLoadMore
            dataType={type}
            containerRef={listRef}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
            hasMore={hasMore}
            pageNum={pageNum}
            setPageNum={setPageNum}
          >
            <Box
              sx={{
                width: "100%",
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                position: "relative",
                mt: type === INBOX_TYPE.NOTIFICATION ? "1px" : "0px",
              }}
            >
              {stickyIdx >= 0 && (
                <Box
                  sx={{
                    width: `${stickyWidth + 12}px`,
                    height: "26px",
                    paddingLeft: "8px",
                    display: "flex",
                    alignItems: "center",
                    background: "#FFF",
                    position: "fixed",
                    zIndex: 200,
                  }}
                >
                  <StyledHeader>{dataList[stickyIdx].label}</StyledHeader>
                </Box>
              )}
              {dataList &&
                dataList.map((item: any, i: number) => itemView(item, i))}
            </Box>
          </ScrollLoadMore>
        </>
      )}
    </Box>
  );
};
export default InboxListView;
