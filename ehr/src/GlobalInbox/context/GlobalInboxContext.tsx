import React, { useCallback, useState } from "react";
import { CciDropdownChoice } from "@cci-monorepo/common/mui-components/src/export";
import { isEmpty } from "lodash";
import {
  INBOX_TYPE,
  INBOX_TITLE,
  MESSAGE_TYPE,
  INBOX_CONFIG,
} from "../constants/Constants";

interface InboxContextValue {
  detailPanelStatus: number[];
  setDetailPanelStatus: React.Dispatch<React.SetStateAction<number[]>>;
  hasDraft: () => boolean;
  clearDraft: () => void;
  message: any;
  setMessage: React.Dispatch<React.SetStateAction<any>>;
  dataType: string;
  setDataType: React.Dispatch<React.SetStateAction<string>>;
  msgFolder: string;
  setMsgFolder: React.Dispatch<React.SetStateAction<string>>;
  dbpath: string;
  setDbpath: React.Dispatch<React.SetStateAction<string>>;
  selectedToPersons: any[];
  setSelectedToPersons: React.Dispatch<React.SetStateAction<any[]>>;
  subject: CciDropdownChoice | null;
  setSubject: React.Dispatch<React.SetStateAction<CciDropdownChoice | null>>;
  comment: string;
  setComment: React.Dispatch<React.SetStateAction<string>>;
  attachments: any[];
  setAttachments: React.Dispatch<React.SetStateAction<any[]>>;
  isForward: boolean;
  setIsForward: React.Dispatch<React.SetStateAction<boolean>>;
  isReply: boolean;
  setIsReply: React.Dispatch<React.SetStateAction<boolean>>;
  isReplyAll: boolean;
  setIsReplyAll: React.Dispatch<React.SetStateAction<boolean>>;
  unitFilter: string;
  setUnitFilter: React.Dispatch<React.SetStateAction<string>>;
  ambFilter: string[];
  setAmbFilter: React.Dispatch<React.SetStateAction<string[]>>;
  selectedSort: string;
  setSelectedSort: React.Dispatch<React.SetStateAction<string>>;
  sort: string;
  setSort: React.Dispatch<React.SetStateAction<string>>;
  sortBy: string;
  setSortBy: React.Dispatch<React.SetStateAction<string>>;
  menuData: any[];
  setMenuData: React.Dispatch<React.SetStateAction<any[]>>;
  lastMenuData: any[];
  setLastMenuData: React.Dispatch<React.SetStateAction<any[]>>;
  dataList: any[];
  setDataList: React.Dispatch<React.SetStateAction<any[]>>;
  messageList: any[];
  setMessageList: React.Dispatch<React.SetStateAction<any[]>>;
  dataTotal: number;
  setDataTotal: React.Dispatch<React.SetStateAction<number>>;
  relativeOrders: any[];
  setRelativeOrders: React.Dispatch<React.SetStateAction<any[]>>;
  hasSignOrderPerm: boolean;
  hasCosignOrderPerm: boolean;
  hasSignNotePerm: boolean;
  hasCosignNotePerm: boolean;
  selectedMenuIdx: number;
  setSelectedMenuIdx: React.Dispatch<React.SetStateAction<number>>;
  selectedIdx: number;
  setSelectedIdx: React.Dispatch<React.SetStateAction<number>>;
  keyword: string;
  setKeyword: React.Dispatch<React.SetStateAction<string>>;
  inboxConfig: any;
  setInboxConfig: React.Dispatch<React.SetStateAction<any>>;
  pageNum: number;
  setPageNum: React.Dispatch<React.SetStateAction<number>>;
  hasMore: boolean;
  setHasMore: React.Dispatch<React.SetStateAction<boolean>>;
  scrollTop: number;
  setScrollTop: React.Dispatch<React.SetStateAction<number>>;
  specialIndices: number[];
  setSpecialIndices: React.Dispatch<React.SetStateAction<number[]>>;
  envType: string;
  setEnvType: React.Dispatch<React.SetStateAction<string>>;
  allAmbUnits: string[];
  setAllAmbUnits: React.Dispatch<React.SetStateAction<string[]>>;
}

export const defaultMenuData = [
  {
    type: INBOX_TYPE.ACTION,
    name: "",
    unread: 0,
    total: 0,
    data: [],
    pType: "",
  },
  {
    type: INBOX_TYPE.HEADER,
    name: INBOX_TITLE.MESSAGE_TITLE,
    unread: 0,
    total: 0,
    data: [],
    pType: "",
  },
  {
    type: INBOX_TYPE.MESSAGE,
    name: MESSAGE_TYPE.GC_RECEIVED,
    unread: 0,
    total: 0,
    data: [],
    pType: "",
  },
  {
    type: INBOX_TYPE.MESSAGE,
    name: MESSAGE_TYPE.PP_RECEIVED,
    unread: 0,
    total: 0,
    data: [],
    pType: "",
  },
  {
    type: INBOX_TYPE.MESSAGE,
    name: MESSAGE_TYPE.SENT,
    unread: 0,
    total: 0,
    data: [],
    pType: "",
  },
  {
    type: INBOX_TYPE.MESSAGE,
    name: MESSAGE_TYPE.ARCHIVED,
    unread: 0,
    total: 0,
    data: [],
    pType: "",
  },
];

export const sortList = [
  { label: "Date (Newest on Top)", sort: "desc", sortBy: "Date" },
  { label: "Date (Oldest on Top)", sort: "asc", sortBy: "Date" },
  { label: "Sender (A to Z)", sort: "asc", sortBy: "Sender" },
  { label: "Sender (Z to A)", sort: "desc", sortBy: "Sender" },
  { label: "Patient (A to Z)", sort: "asc", sortBy: "Patient" },
  { label: "Patient (Z to A)", sort: "desc", sortBy: "Patient" },
];

const defaultContextValue = {
  detailPanelStatus: [-1],
  setDetailPanelStatus: () => {},
  hasDraft: () => false,
  clearDraft: () => {},
  message: null,
  setMessage: () => {},
  dataType: "",
  setDataType: () => {},
  msgFolder: MESSAGE_TYPE.GC_RECEIVED,
  setMsgFolder: () => {},
  dbpath: "",
  setDbpath: () => {},
  selectedToPersons: [],
  setSelectedToPersons: () => {},
  subject: null,
  setSubject: () => {},
  comment: "",
  setComment: () => {},
  attachments: [],
  setAttachments: () => {},
  isForward: false,
  setIsForward: () => {},
  isReply: false,
  setIsReply: () => {},
  isReplyAll: false,
  setIsReplyAll: () => {},
  unitFilter: "",
  setUnitFilter: () => {},
  ambFilter: [],
  setAmbFilter: () => {},
  selectedSort: sortList[0].label,
  setSelectedSort: () => {},
  sort: "desc",
  setSort: () => {},
  sortBy: "Date",
  setSortBy: () => {},
  menuData: defaultMenuData,
  setMenuData: () => {},
  lastMenuData: [],
  setLastMenuData: () => {},
  dataList: [],
  setDataList: () => {},
  messageList: [],
  setMessageList: () => {},
  dataTotal: 0,
  setDataTotal: () => {},
  relativeOrders: [],
  setRelativeOrders: () => {},
  hasSignOrderPerm: false,
  hasCosignOrderPerm: false,
  hasSignNotePerm: false,
  hasCosignNotePerm: false,
  selectedMenuIdx: 2,
  setSelectedMenuIdx: () => {},
  selectedIdx: -1,
  setSelectedIdx: () => {},
  keyword: "",
  setKeyword: () => {},
  inboxConfig: Cci?.RunTime?.getCache("globalinbox_config"),
  setInboxConfig: () => {},
  pageNum: 1,
  setPageNum: () => {},
  hasMore: false,
  setHasMore: () => {},
  scrollTop: 0,
  setScrollTop: () => {},
  specialIndices: [],
  setSpecialIndices: () => {},
  envType: "",
  setEnvType: () => {},
  allAmbUnits: [],
  setAllAmbUnits: () => {},
};

export const InboxContext =
  React.createContext<InboxContextValue>(defaultContextValue);

export const InboxContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [detailPanelStatus, setDetailPanelStatus] = useState<number[]>([-1]);
  const [message, setMessage] = useState<any>(null);
  const [dataType, setDataType] = useState<string>("");
  const [msgFolder, setMsgFolder] = useState<string>(MESSAGE_TYPE.GC_RECEIVED);
  const [dbpath, setDbpath] = useState<string>("");
  const [selectedToPersons, setSelectedToPersons] = useState<any[]>([]);
  const [subject, setSubject] = useState<CciDropdownChoice | null>(null);
  const [comment, setComment] = useState<string>("");
  const [attachments, setAttachments] = useState<any[]>([]);
  const [isForward, setIsForward] = useState<boolean>(false);
  const [isReply, setIsReply] = useState<boolean>(false);
  const [isReplyAll, setIsReplyAll] = useState<boolean>(false);
  const [unitFilter, setUnitFilter] = useState<string>("All");
  const [ambFilter, setAmbFilter] = useState<string[]>([]);
  const [selectedSort, setSelectedSort] = useState<string>(sortList[0].label);
  const [sort, setSort] = useState<string>("desc");
  const [sortBy, setSortBy] = useState<string>("Date");
  const [menuData, setMenuData] = useState<any[]>(defaultMenuData);
  const [dataList, setDataList] = useState<any[]>([]);
  const [messageList, setMessageList] = useState<any[]>([]);
  const [dataTotal, setDataTotal] = useState<number>(0);
  const [relativeOrders, setRelativeOrders] = useState<any[]>([]);
  const hasSignOrderPerm = cci.cfg.perms.split(" ").includes("67:E");
  const hasCosignOrderPerm = cci.cfg.perms.split(" ").includes("29:E");
  const hasSignNotePerm = cci.cfg.perms.split(" ").includes("116:E");
  const hasCosignNotePerm = cci.cfg.perms.split(" ").includes("117:E");
  const [selectedMenuIdx, setSelectedMenuIdx] = useState(2);
  const [selectedIdx, setSelectedIdx] = useState<number>(-1);
  const [lastMenuData, setLastMenuData] = useState<any[]>([]);
  const [keyword, setKeyword] = useState<string>("");
  const [pageNum, setPageNum] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [scrollTop, setScrollTop] = useState<number>(0);
  const [specialIndices, setSpecialIndices] = useState<number[]>([]);
  const [inboxConfig, setInboxConfig] = useState<any>(
    Cci?.RunTime?.getCache(INBOX_CONFIG)
  );
  const [envType, setEnvType] = useState<string>("");
  const [allAmbUnits, setAllAmbUnits] = useState<string[]>([]);

  const isEqualRecipents = useCallback(() => {
    let recipientsArr = JSON.parse(message?.recipients);
    let persons = [] as any[];
    recipientsArr.forEach((recipient: any) => {
      if (recipient.staffID != Cci.util.Staff.getSid()) {
        persons.push({
          group:
            recipient.type == "careteam"
              ? "Care Team"
              : recipient.type == "Groups"
                ? "groups"
                : "All Staff",
          id: recipient?.staffID,
          label: recipient?.name,
          tagLabel: recipient.licenseType,
        });
      }
    });
    persons.push({
      group: "All Staff",
      id: message?.senderID,
      label: message?.senderName,
      tagLabel: message?.senderLicenseType,
    });
    let uniqPersons = persons.reduce((acc: any[], cur: any) => {
      return acc.map((item) => item.id).includes(cur.id) ? acc : [...acc, cur];
    }, []);
    return (
      selectedToPersons.map((person) => person.id).join(",") !=
      uniqPersons.map((p) => p.id).join(",")
    );
  }, [message?.recipients, selectedToPersons]);

  const hasDraft = useCallback(() => {
    if (detailPanelStatus[detailPanelStatus.length - 1] === 0) {
      return (
        !isEmpty(dbpath) ||
        !isEmpty(selectedToPersons) ||
        !isEmpty(subject) ||
        !isEmpty(comment) ||
        !isEmpty(attachments)
      );
    } else if (detailPanelStatus[detailPanelStatus.length - 1] === 1) {
      return (
        (isReply
          ? selectedToPersons.map((person) => person.label).join(",") !=
            message?.senderName
          : isReplyAll
            ? isEqualRecipents()
            : !isEmpty(selectedToPersons)) ||
        !isEmpty(comment) ||
        ((isReply || isReplyAll || isForward) &&
          attachments !== (message?.attachments || ""))
      );
    }
    return false;
  }, [
    detailPanelStatus,
    dbpath,
    selectedToPersons,
    subject,
    comment,
    attachments,
    isReply,
    isReplyAll,
    isForward,
    message,
    isEqualRecipents,
  ]);

  const clearDraft = useCallback(() => {
    setDbpath("");
    setSubject(null);
    setComment("");
    setAttachments([]);
    setSelectedToPersons([]);
    setIsReply(false);
    setIsReplyAll(false);
    setIsForward(false);
  }, []);

  const contextValue = React.useMemo<InboxContextValue>(
    () => ({
      detailPanelStatus,
      setDetailPanelStatus,
      hasDraft,
      clearDraft,
      message,
      setMessage,
      dataType,
      setDataType,
      msgFolder,
      setMsgFolder,
      dbpath,
      setDbpath,
      selectedToPersons,
      setSelectedToPersons,
      subject,
      setSubject,
      comment,
      setComment,
      attachments,
      setAttachments,
      isForward,
      setIsForward,
      isReply,
      setIsReply,
      isReplyAll,
      setIsReplyAll,
      unitFilter,
      setUnitFilter,
      ambFilter,
      setAmbFilter,
      selectedSort,
      setSelectedSort,
      sort,
      setSort,
      sortBy,
      setSortBy,
      menuData,
      setMenuData,
      lastMenuData,
      setLastMenuData,
      dataList,
      setDataList,
      messageList,
      setMessageList,
      dataTotal,
      setDataTotal,
      relativeOrders,
      setRelativeOrders,
      hasSignOrderPerm,
      hasCosignOrderPerm,
      hasSignNotePerm,
      hasCosignNotePerm,
      selectedMenuIdx,
      setSelectedMenuIdx,
      selectedIdx,
      setSelectedIdx,
      keyword,
      setKeyword,
      inboxConfig,
      setInboxConfig,
      pageNum,
      setPageNum,
      hasMore,
      setHasMore,
      scrollTop,
      setScrollTop,
      specialIndices,
      setSpecialIndices,
      envType,
      setEnvType,
      allAmbUnits,
      setAllAmbUnits,
    }),
    [
      detailPanelStatus,
      setDetailPanelStatus,
      hasDraft,
      clearDraft,
      message,
      setMessage,
      dataType,
      setDataType,
      msgFolder,
      setMsgFolder,
      dbpath,
      setDbpath,
      selectedToPersons,
      setSelectedToPersons,
      subject,
      setSubject,
      comment,
      setComment,
      attachments,
      setAttachments,
      isForward,
      setIsForward,
      isReply,
      setIsReply,
      isReplyAll,
      setIsReplyAll,
      unitFilter,
      setUnitFilter,
      ambFilter,
      setAmbFilter,
      selectedSort,
      setSelectedSort,
      sort,
      setSort,
      sortBy,
      setSortBy,
      menuData,
      setMenuData,
      lastMenuData,
      setLastMenuData,
      dataList,
      setDataList,
      messageList,
      setMessageList,
      dataTotal,
      setDataTotal,
      relativeOrders,
      setRelativeOrders,
      hasSignOrderPerm,
      hasCosignOrderPerm,
      hasSignNotePerm,
      hasCosignNotePerm,
      selectedMenuIdx,
      setSelectedMenuIdx,
      selectedIdx,
      setSelectedIdx,
      keyword,
      setKeyword,
      inboxConfig,
      setInboxConfig,
      pageNum,
      setPageNum,
      hasMore,
      setHasMore,
      scrollTop,
      setScrollTop,
      specialIndices,
      setSpecialIndices,
      envType,
      setEnvType,
      allAmbUnits,
      setAllAmbUnits,
    ]
  );

  return (
    <InboxContext.Provider value={contextValue}>
      {children}
    </InboxContext.Provider>
  );
};
