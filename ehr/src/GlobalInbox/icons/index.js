import { ReactComponent as IconAttachment } from "./IconAttachment.svg";
import { ReactComponent as IconAvatar } from "./IconAvatar.svg";
import { ReactComponent as IconBtnArchive } from "./IconBtnArchive.svg";
import { ReactComponent as IconExternalLink } from "./IconExternalLink.svg";
import { ReactComponent as IconForward } from "./IconForward.svg";
import { ReactComponent as IconMarkUnread } from "./IconMarkUnread.svg";
import { ReactComponent as IconMenuArchived } from "./IconMenuArchived.svg";
import { ReactComponent as IconMenuConsults } from "./IconMenuConsults.svg";
import { ReactComponent as IconMenuFolder } from "./IconMenuFolder.svg";
import { ReactComponent as IconMenuLabResults } from "./IconMenuLabResults.svg";
import { ReactComponent as IconMenuNotes } from "./IconMenuNotes.svg";
import { ReactComponent as IconMenuOrderEntry } from "./IconMenuOrderEntry.svg";
import { ReactComponent as IconMenuRadiology } from "./IconMenuRadiology.svg";
import { ReactComponent as IconMenuReceived } from "./IconMenuReceived.svg";
import { ReactComponent as IconMenuReports } from "./IconMenuReports.svg";
import { ReactComponent as IconMenuRx } from "./IconMenuRx.svg";
import { ReactComponent as IconMenuSend } from "./IconMenuSend.svg";
import { ReactComponent as IconNewMessage } from "./IconNewMessage.svg";
import { ReactComponent as IconReply } from "./IconReply.svg";
import { ReactComponent as IconReplyAll } from "./IconReplyAll.svg";
import { ReactComponent as IconSearchSettings } from "./IconSearchSettings.svg";
import { ReactComponent as IconGroup } from "./IconGroup.svg";
import { ReactComponent as IconPath } from "./IconPath.svg";
import { ReactComponent as IconRectangle } from "./IconRectangle.svg";
import { ReactComponent as IconSend } from "./IconSend.svg";
import { ReactComponent as IconBtnAttachment } from "./IconBtnAttachment.svg";
import { ReactComponent as IconDelete } from "./IconDelete.svg";
import { ReactComponent as IconSmallAvatar } from "./IconSmallAvatar.svg";
import { ReactComponent as IconDropdownArrow } from "./IconDropdownArrow.svg";
import { ReactComponent as IconProfile } from "./IconProfile.svg";
import { ReactComponent as IconPrint } from "./IconPrint.svg";
import { ReactComponent as IconDoctor } from "./IconDoctor.svg";
import { ReactComponent as IconLocation } from "./IconLocation.svg";
import { ReactComponent as IconSchedule } from "./IconSchedule.svg";
import { ReactComponent as IconMenuSchedule } from "./IconMenuSchedule.svg";
import { ReactComponent as IconApprove } from "./IconApprove.svg";
import { ReactComponent as IconDeny } from "./IconDeny.svg";
import { ReactComponent as IconMenuEncounters } from "./IconMenuEncounters.svg";

export {
  IconAttachment,
  IconAvatar,
  IconBtnArchive,
  IconExternalLink,
  IconForward,
  IconMarkUnread,
  IconMenuArchived,
  IconMenuConsults,
  IconMenuFolder,
  IconMenuLabResults,
  IconMenuNotes,
  IconMenuOrderEntry,
  IconMenuRadiology,
  IconMenuReceived,
  IconMenuReports,
  IconMenuRx,
  IconMenuSend,
  IconNewMessage,
  IconReply,
  IconReplyAll,
  IconSearchSettings,
  IconGroup,
  IconPath,
  IconRectangle,
  IconSend,
  IconBtnAttachment,
  IconDelete,
  IconSmallAvatar,
  IconDropdownArrow,
  IconProfile,
  IconPrint,
  IconDoctor,
  IconLocation,
  IconSchedule,
  IconMenuSchedule,
  IconApprove,
  IconDeny,
  IconMenuEncounters,
};
