export const INBOX_TYPE = {
  MESSAGE: "message",
  NOTIFICATION: "notification",
  HEADER: "header",
  ACTION: "action",
};

export const INBOX_TITLE = {
  MESSAGE_TITLE: "Messages",
  NOTIFICATION: "System Notifications",
};

export const MESSAGE_TYPE = {
  GC_RECEIVED: "General Care",
  PP_RECEIVED: "Patient Portal",
  SENT: "Sent",
  ARCHIVED: "Archived",
};

export const ALL_MESSAGE_TYPE = [
  MESSAGE_TYPE.GC_RECEIVED,
  MESSAGE_TYPE.PP_RECEIVED,
  MESSAGE_TYPE.SENT,
  MESSAGE_TYPE.ARCHIVED,
];

export const WITHOUT_RECEIVED_MESSAGE_TYPE = [
  MESSAGE_TYPE.SENT,
  MESSAGE_TYPE.ARCHIVED,
];

export const NOTIFICATION_TYPE = {
  LAB_RESULTS: "Lab Results",
  REPORTS: "Reports",
  IMAGING_REPORTS: "Imaging Reports",
  CONSULTS: "Consults",
  RX_REQUESTS: "Rx Requests",
  ORDERS: "Orders",
  NOTES: "Notes",
  SCHEDULING_REQUESTS: "Scheduling Requests",
  ENCOUNTERS: "Encounters",
};

export const INBOX_CONFIG = "globalinbox_config";

export const ENV_TYPE = {
  INPATIENT: "Inpatient",
  AMBULATORY: "Ambulatory",
};
