import { useQuery, useMutation, keepPreviousData } from "@tanstack/react-query";
import { postData } from "@cci-monorepo/common";
import {
  patientLookup,
  recipientLookup,
  getPatInfo,
  getMessages,
  getNotifications,
  getUserPreference,
} from "./GlobalInboxData";
import { processPatientLookupResults } from "./GlobalInboxUtil";
import { MIN_NUM_PATIENT_LOOKUP_CHARS } from "./GlobalInboxData";
import { ORDER_TYPE } from "@cci-monorepo/LabsReview/config/value";

export const usePatientLookupQuery = (
  esStr: string,
  ambFilter: string,
  allAmbUnits: string
) => {
  let searchStr = esStr.length >= MIN_NUM_PATIENT_LOOKUP_CHARS ? esStr : "";
  return useQuery({
    queryKey: ["patientlist", searchStr, ambFilter, allAmbUnits],
    queryFn: () => {
      const params = {
        esStr:
          searchStr === "" || isNaN(+searchStr)
            ? searchStr
            : parseInt(searchStr),
        ambFilter: ambFilter,
        excludeUnits: allAmbUnits,
      };

      return patientLookup(params);
    },
    select: (data) => {
      return processPatientLookupResults(data);
    },
    placeholderData: keepPreviousData,
  });
};

export const useRecipientLookupQuery = (dbpath: string, keyword: string) => {
  let searchStr = keyword.length >= MIN_NUM_PATIENT_LOOKUP_CHARS ? keyword : "";
  return useQuery({
    queryKey: ["getpatinfo", dbpath, keyword],
    queryFn: () => recipientLookup({ dbpath: dbpath, keyword: searchStr }),
    placeholderData: {},
  });
};

export const useGetPatInfoQuery = (dbpath: string) => {
  return useQuery({
    queryKey: ["getpatinfo", dbpath],
    queryFn: () => getPatInfo({ dbpath: dbpath }),
    placeholderData: {},
  });
};

export const useGetMessagesQuery = (
  userType: number,
  userID: number,
  msgFolder: string,
  keyword: string,
  sort: string,
  sortBy: string,
  pageNum: number,
  pageSize: number,
  unitFilter: string,
  ambFilter: string,
  excludeUnits: string,
  envType: string
) => {
  return useQuery({
    queryKey: [
      "getMessages",
      userType,
      userID,
      msgFolder,
      keyword,
      sort,
      sortBy,
      pageNum,
      pageSize,
      unitFilter,
      ambFilter,
      excludeUnits,
    ],
    queryFn: () =>
      getMessages({
        userType: userType,
        userID: userID,
        msgFolder: msgFolder,
        keyword: keyword,
        sort: sort,
        sortBy: sortBy,
        pageNum: pageNum,
        pageSize: pageSize,
        unitFilter: unitFilter,
        ambFilter: ambFilter,
        excludeUnits: excludeUnits,
      }),
    placeholderData: {},
    enabled:
      envType != "" &&
      (keyword.length >= MIN_NUM_PATIENT_LOOKUP_CHARS || keyword.length === 0),
  });
};

export const useGetNotificationsQuery = (staffID: number, envType: string) => {
  return useQuery({
    queryKey: ["getNotifications", staffID, envType],
    queryFn: () =>
      getNotifications({
        staffID: staffID,
        envType: envType,
      }),
    placeholderData: {},
    enabled: envType != "",
  });
};

export const useNewMessageMutation = () => {
  return useMutation({
    mutationFn: (data: {
      senderType: number;
      senderID: number;
      to: string;
      senderName: string;
      licenseType: string;
      msgType: string;
      msgSubject: string;
      msgContent: string;
      dbpath: string;
      attachments: string; // [{"fileName": "", "filePath": ""}]
      sourceType: number; // 1: reply, 2: message forward, 3: notification forward
      sourceID: number;
      source: string;
    }) => {
      return postData("globalInbox/newMessage", data);
    },
  });
};

export const useUploadFileMutation = () => {
  return useMutation({
    mutationFn: (data: { file: File; filename: string }) => {
      const formData = new FormData();
      formData.append("file", data.file);
      formData.append("filename", data.filename);
      return postData("globalInbox/uploadfile", formData);
    },
  });
};

export const useMarkReadMessageMutation = () => {
  return useMutation({
    mutationFn: (data: { receiveID: number; status: number }) => {
      return postData("globalInbox/markReadMessage", data);
    },
  });
};

export const useMarkReadNotificationMutation = () => {
  return useMutation({
    mutationFn: (data: {
      staffID: number;
      dbpath: string;
      module: string;
      groupName: string;
      accessionNumber: string;
      status: number;
    }) => {
      return postData("globalInbox/markReadNotification", data);
    },
  });
};

export const useArchiveMessageMutation = () => {
  return useMutation({
    mutationFn: (data: { receiveids: string }) => {
      return postData("globalInbox/archiveMessage", data);
    },
  });
};

export const useDownloadAttachmentMutation = () => {
  return useMutation({
    mutationFn: (data: {
      fileName: string;
      filePath: string;
      isOpen: boolean;
    }) => {
      return postData("globalInbox/downloadAttachment", data);
    },
  });
};

export async function markReviewed(
  type: string,
  key: string,
  orderID: string,
  nit: string,
  nits: string,
  title: string,
  dbpath: string
) {
  if (type === ORDER_TYPE.LABORATORY) {
    await Cci.util.Hobj.requestRecords({
      hobj: "labresults/review/labresults/bytestnames",
      params: {
        ccitoken: window.sessionStorage.getItem("webtoken"),
        testname: title,
        key: key,
        nits: nits,
      },
      noBatch: true,
      dbs: dbpath,
    });
  } else {
    await Cci.util.Hobj.requestRecords({
      hobj: "labresults/review/labreports",
      params: {
        ccitoken: window.sessionStorage.getItem("webtoken"),
        type: type,
        nit: nit,
        key: key,
      },
      noBatch: true,
      dbs: dbpath,
    });
  }
}

export async function saveOrderAction(data: any[], dbpath: string) {
  return await Cci.util.Hobj.requestRecords({
    dbs: [dbpath],
    hobj: "cpoe2/actions/save",
    noBatch: true,
    params: {
      data: data,
      sid: Cci.util.Staff.getSid(),
      dbpath: dbpath,
    },
  });
}

export const useRxRefillRequestMutation = () => {
  return useMutation({
    mutationFn: (data: {
      dbpath: string;
      module: string;
      groupName: string;
      orderId: string;
      action: number;
    }) => {
      return postData("globalInbox/handleRefillRequest", data);
    },
  });
};

export const useSaveUserPreferenceMutation = () => {
  return useMutation({
    mutationFn: (data: {
      userID: string;
      userType: number;
      type: string;
      prefKey: string;
      keyValue: string;
    }) => {
      return postData("globalInbox/saveUserPreference", data);
    },
  });
};

export const useGetUserPreferenceQuery = (staffID: string) => {
  return useQuery({
    queryKey: ["getUserPreference", staffID],
    queryFn: () =>
      getUserPreference({
        userID: staffID,
      }).then((ret: any) => {
        if (ret.success) {
          return ret.data;
        } else {
          return [];
        }
      }),
    placeholderData: null,
  });
};
