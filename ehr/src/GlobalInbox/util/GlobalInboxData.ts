import { postData } from "@cci-monorepo/common";
import { GridColDef } from "@mui/x-data-grid";
import axios from "axios";

export const MIN_NUM_PATIENT_LOOKUP_CHARS = 3;

export type IPatientDataGridColumn = GridColDef & {
  required?: boolean;
  [key: string]: any;
};

export interface IPatientDataGridRow {
  [key: string]: string | number | boolean;
}

export interface IPatientDataGridData {
  columns: IPatientDataGridColumn[];
  rows: IPatientDataGridRow[];
  total?: number;
}

const baseUrl: string = cci.cfg.baseUrl;

const getData = (url: string, formData?: object) => {
  let queryUrl = baseUrl + "/index.php/" + url;
  return axios
    .get(queryUrl, { params: formData })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      if (error.response.status === 422) {
        throw error;
      } else {
        console.log("Error:", error);
      }
    });
};

export const patientLookup = (params: any) => {
  const url: string = "globalInbox/patientLookup";
  return postData(url, params);
};

export const recipientLookup = (params: any) => {
  const url: string = "globalInbox/recipientLookup";
  return postData(url, params);
};

export const getPatInfo = (params: any) => {
  const url: string = "globalInbox/getPatInfo";
  return getData(url, params);
};

export const getMessages = (params: any) => {
  const url: string = "globalInbox/getMessages";
  return getData(url, params);
};

export const getNotifications = (params: any) => {
  const url: string = "globalInbox/getNotifications";
  return getData(url, params);
};

export const newMessage = (params: any) => {
  const url: string = "globalInbox/newMessage";
  return postData(url, params);
};

export const getUserPreference = (params: any) => {
  const url: string = "globalInbox/getUserPreference";
  return getData(url, params);
};

export const getEncounterData = (regid: string, encid: string) => {
  return postData("registration/getencounter", { regid }).then((data) => {
    if (data?.[0]?.Result && data?.[0]?.Result.length > 0) {
      let encounterIdIdx = data?.[0]?.Result[0].findIndex(
        (item: any) => item == "encounterId"
      );
      let guarantorIdx = data?.[0]?.Result[0].findIndex(
        (item: any) => item == "guarantorID"
      );
      let guarantorTypeIdx = data?.[0]?.Result[0].findIndex(
        (item: any) => item == "guarantorType"
      );
      let guarantorNameIdx = data?.[0]?.Result[0].findIndex(
        (item: any) => item == "guarantorName"
      );
      let enc = data?.[0]?.Result?.find(
        (item: any) => item[encounterIdIdx] == encid
      );
      return {
        guarantor: enc[guarantorIdx],
        guarantorType: enc[guarantorTypeIdx],
        guarantorName: enc[guarantorNameIdx],
      };
    }
    return {};
  });
};

export const cancelReschedule = (accessionNumber: string) => {
  const url: string = "globalInbox/cancelReschedule";
  return postData(url, { accessionNumber: accessionNumber });
};
