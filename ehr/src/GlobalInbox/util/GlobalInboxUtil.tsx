import {
  IPatientDataGridData,
  IPatientDataGridRow,
  IPatientDataGridColumn,
} from "./GlobalInboxData";
import Config from "@cci-monorepo/config/Config";

export const processPatientLookupResults = (apiData: any) => {
  let patientListData: IPatientDataGridData = {
    columns: [],
    rows: [],
    total: 0,
  };

  // error handling
  if (!apiData) return patientListData;

  // columns expected to be returned no matter what
  if (apiData.columns && apiData.columns.length > 0)
    patientListData.columns = apiData.columns as IPatientDataGridColumn[];

  // obtain the records returned by api
  if (apiData.success) {
    patientListData.total = apiData.total;
    if (apiData.rows && apiData.rows.length > 0) {
      patientListData.rows = apiData.rows as IPatientDataGridRow[];
    }
  }

  return patientListData;
};

const escapeRegExp = (string: any) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

export const highlightText = (text: any, inputValue: any) => {
  if (!inputValue.trim()) return text;

  const escapedInput = escapeRegExp(inputValue);
  const regex = new RegExp(`(${escapedInput})`, "gi");
  const parts = text.split(regex);

  return parts.map((part: any, index: any) =>
    regex.test(part) ? (
      <span key={index} style={{ backgroundColor: "#FFFD54" }}>
        {part}
      </span>
    ) : (
      part
    )
  );
};

export const setEhrDataDirty = (
  moduleName: string,
  flag: boolean,
  saveFn?: any
): void => {
  if (!Config.inDevMode) {
    const configName = Cci.RunTime.appCtnController.configname;
    Cci.util.RunTime.wantDirtySaveFuncList[configName] = () => {};
    Cci.util.RunTime.setDirtyFlag(moduleName, flag);
  }
};
