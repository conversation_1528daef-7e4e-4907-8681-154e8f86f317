import { useState } from "react";
import { Box } from "@mui/material";
import { styled } from "@mui/material/styles";
import { Typography } from "@mui/material";
import { useAtom, useSetAtom, useAtomValue } from "jotai";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import InboxMenuView from "./component/InboxMenuView";
import InboxListView from "./component/InboxListView";
import InboxDetailView from "./component/InboxDetailView";
import {
  showAlertAtom,
  showDiscardDialogAtom,
  discardDraftAtom,
} from "./atoms/GlobalInboxAtoms";
import { InboxContextProvider } from "./context/GlobalInboxContext";
import { CciUnsavedDataDialog } from "@cci-monorepo/common/mui-components/src/export";
import { IMenuItem } from "./component/InboxMenuView";
import InboxAlert from "./component/InboxAlert";
import { INBOX_TYPE, ALL_MESSAGE_TYPE } from "./constants/Constants";
import RescheduleApptDialog from "@cci-monorepo/Scheduling/components/Reschedule/RescheduleApptDialog";
import {
  rescheduleApptDataAtom,
  rescheduleOpenAtom,
} from "@cci-monorepo/Scheduling/context/SchedulingAtoms";
import { useSetToastFamily } from "@cci-monorepo/common";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      retry: false,
    },
  },
});

export const StyledPrimary = styled(Typography)({
  color: "#4B6EAF",
  fontFamily: "Roboto",
  fontSize: "14px",
  fontStyle: "normal",
  fontWeight: "700",
  lineHeight: "normal",
});

export default function GlobalInbox(props: any) {
  const showAlert = useAtomValue(showAlertAtom);
  const [showDiscardDialog, setShowDiscardDialog] = useAtom(
    showDiscardDialogAtom
  );
  const setDiscardDraft = useSetAtom(discardDraftAtom);
  const [rescheduleOpen, setRescheduleOpen] = useAtom(rescheduleOpenAtom);
  const [rescheduleApptData, setRescheduleApptData] = useAtom(
    rescheduleApptDataAtom
  );
  const setShowToast = useSetToastFamily("AmbApptManagement");
  const [menu, setMenu] = useState<IMenuItem>();
  const [type, setType] = useState<string>("message");

  const onItemClick = (menu: any) => {
    setMenu(menu);
    setType(
      ALL_MESSAGE_TYPE.includes(menu.name)
        ? INBOX_TYPE.MESSAGE
        : INBOX_TYPE.NOTIFICATION
    );
  };

  const setOpenDialog = (open: boolean) => {
    setShowDiscardDialog(open ? 1 : 0);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <InboxContextProvider>
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <Box
            sx={{
              width: "98.5%",
              height: "97.5%",
              display: "flex",
              gap: "8px",
            }}
          >
            <InboxMenuView
              onItemClick={onItemClick}
              queryClient={queryClient}
            />
            <InboxListView menu={menu} type={type} />
            <InboxDetailView />
          </Box>
          {showAlert > 0 && <InboxAlert />}
          <CciUnsavedDataDialog
            data-testid="global-inbox-confirm-dialog"
            title={"Unsaved Changes"}
            open={Boolean(showDiscardDialog)}
            setOpen={setOpenDialog}
            handleDiscard={() => {
              setDiscardDraft(showDiscardDialog);
              setShowDiscardDialog(0);
            }}
            handleCancel={() => {
              setShowDiscardDialog(0);
            }}
          />
          {rescheduleApptData?.patient?.dbpath && (
            <RescheduleApptDialog
              open={rescheduleOpen}
              setOpen={setRescheduleOpen}
              apptData={rescheduleApptData}
              setApptData={setRescheduleApptData}
              setShowToast={setShowToast}
            />
          )}
        </Box>
      </InboxContextProvider>
    </QueryClientProvider>
  );
}
