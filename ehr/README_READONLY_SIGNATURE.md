# Read-Only Signature Functionality for UiNotes

## Overview

This implementation adds read-only functionality to notes that have required signatures and have been signed. When a note has `required_signature: true` and `signed: true`, the note becomes read-only and cannot be modified.

## Changes Made

### 1. Updated Type Definitions

**File:** `src/UiNotes/reports/types/NoteVersion.ts`

- Added `SignatureInfo` interface to define signature data structure
- Updated `NoteReport` interface to include signature-related fields:
  - `required_signature?: boolean`
  - `signed?: boolean`
  - `signatures?: SignatureInfo[]`
  - `lastSignedAt?: string`
  - `lastSignedBy?: string`

### 2. Enhanced SplitEditor Component

**File:** `src/UiNotes/reports/components/SplitEditor.tsx`

- Added `selectedReport` prop to access signature information
- Implemented `isSaveDisabled()` function that checks:
  - Basic conditions (no editor, no content, generating, no report selected)
  - Signature conditions (`required_signature && signed`)
- Updated save button to use the new disabled logic
- Enhanced tooltip to show explanatory message when save is disabled due to signatures
- Added `data-testid="save-button"` for testing

### 3. Enhanced EditorContent Component

**File:** `src/UiNotes/reports/components/EditorContent.tsx`

- Added `selectedReport` prop to access signature information
- Added read-only indicator banner that appears when note is signed with required signature
- Implemented `useEffect` to programmatically disable editor when note is read-only
- Added visual styling (opacity and pointer-events) for read-only state
- Added `data-testid="readonly-indicator"` for testing

### 4. Updated Editor Component

**File:** `src/UiNotes/reports/Editor.tsx`

- Modified `SplitEditor` usage to pass `selectedReport` prop
- This enables the signature checking functionality in the editor

## Functionality

### Read-Only Conditions

A note becomes read-only when:
1. `selectedReport.required_signature === true`
2. `selectedReport.signed === true`

### Visual Indicators

1. **Save Button**: Disabled with explanatory tooltip
2. **Read-Only Banner**: Yellow warning banner at top of editor
3. **Editor State**: Programmatically disabled and visually dimmed

### User Experience

- **Clear Feedback**: Users see why the note cannot be edited
- **Consistent Behavior**: All editing actions are disabled
- **Visual Cues**: Multiple indicators show read-only state

## Testing

### Test File: `tests/UiNotes/readonly-signature.test.ts`

Three test scenarios:

1. **Signed Note with Required Signature**: Verifies save button is disabled and read-only indicator is shown
2. **Unsigned Note with Required Signature**: Verifies editing is allowed
3. **Note without Required Signature**: Verifies editing is allowed regardless of signature status

### Test Coverage

- Save button disabled/enabled state
- Read-only indicator visibility
- Tooltip messages
- Editor editability
- Content modification

## Backend Integration

The frontend expects the backend API to return notes with the following signature fields:

```json
{
  "noteID": "1234567890",
  "title": "Progress Note",
  "type": "Progress Note",
  "content": "...",
  "required_signature": true,
  "signed": true,
  "signatures": [
    {
      "signedBy": 12345,
      "signedByName": "Dr. John Smith",
      "signedAt": "2024-01-01T10:00:00Z",
      "comment": "Reviewed and approved"
    }
  ],
  "lastSignedAt": "2024-01-01T10:00:00Z",
  "lastSignedBy": "Dr. John Smith"
}
```

## Configuration

Note types that require signatures are configured in the backend at:
`dataServer/app/config/note_types_config.json`

Example:
```json
{
  "Progress Note": { "nit": 0, "required_signature": true },
  "ED Progress Note": { "nit": 1, "required_signature": true },
  "Discharge Summary": { "nit": 2, "required_signature": false }
}
```

## Future Enhancements

1. **Audit Trail**: Show signature history in a dedicated panel
2. **Partial Signatures**: Allow editing of unsigned sections
3. **Signature Workflow**: Multi-step signature process
4. **Digital Signatures**: Integration with digital signature providers
5. **Signature Validation**: Verify signature authenticity 