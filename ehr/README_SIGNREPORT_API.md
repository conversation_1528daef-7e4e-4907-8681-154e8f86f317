# ReportAPI.signReport Function Documentation

## Overview

The `signReport` function in the `ReportAPI` class allows you to sign notes/reports using the backend's sign_note endpoint. This function integrates with the CCIDB signature system and supports all storage backends (CCIDB, local disk, GitLab).

## Function Signature

```typescript
static async signReport(
  campus: string,
  patID: string,
  noteID: string,
  noteType: string,
  comment: string,
  staffid?: number,
  ccitoken?: string
): Promise<SignNoteResponse>
```

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `campus` | `string` | Yes | The campus identifier |
| `patID` | `string` | Yes | The patient ID |
| `noteID` | `string` | Yes | The note ID to sign |
| `noteType` | `string` | Yes | The type of note (e.g., "Progress Note", "ED Progress Note") |
| `comment` | `string` | Yes | The signature comment |
| `staffid` | `number` | No | Staff ID (uses `Cci.util.Staff.getSid()` if not provided) |
| `ccitoken` | `string` | No | CCIToken (uses localStorage/sessionStorage if not provided) |

## Return Type

```typescript
interface SignNoteResponse {
  message: string;
}
```

## Usage Examples

### Basic Usage

```typescript
import { ReportAPI } from '../services/ReportAPI';

try {
  const result = await ReportAPI.signReport(
    'campus1',
    '12345',
    '**********',
    'Progress Note',
    'Reviewed and approved by Dr. Smith'
  );
  console.log(result.message); // "Note signed successfully"
} catch (error) {
  console.error('Failed to sign note:', error);
}
```

### With Custom Credentials

```typescript
const result = await ReportAPI.signReport(
  'campus1',
  '12345',
  '**********',
  'Progress Note',
  'Reviewed and approved by Dr. Smith',
  99999, // Custom staffid
  'custom-token' // Custom ccitoken
);
```

### In a React Component

```typescript
import React, { useState } from 'react';
import { ReportAPI } from '../services/ReportAPI';

const SignNoteComponent = ({ report, campus, patID }) => {
  const [isSigning, setIsSigning] = useState(false);
  const [error, setError] = useState(null);

  const handleSign = async () => {
    setIsSigning(true);
    setError(null);

    try {
      const result = await ReportAPI.signReport(
        campus,
        patID,
        report.noteID,
        report.type,
        'Reviewed and approved'
      );
      
      console.log('Note signed successfully:', result.message);
      // Handle success (e.g., refresh note list, show success message)
    } catch (error) {
      setError(error.message);
      console.error('Failed to sign note:', error);
    } finally {
      setIsSigning(false);
    }
  };

  return (
    <div>
      <button onClick={handleSign} disabled={isSigning}>
        {isSigning ? 'Signing...' : 'Sign Note'}
      </button>
      {error && <div className="error">{error}</div>}
    </div>
  );
};
```

## Backend Integration

The function calls the following backend endpoint:

```
POST /api/patients/{campus}/{patID}/notes/{noteID}/sign
```

### Request Body

```json
{
  "note_type": "Progress Note",
  "comment": "Reviewed and approved by Dr. Smith",
  "staffid": 12345,
  "ccitoken": "your-ccitoken"
}
```

### Response

```json
{
  "message": "Note signed successfully"
}
```

## Error Handling

The function throws errors for various scenarios:

- **Network errors**: Connection issues, timeouts
- **HTTP errors**: 4xx and 5xx status codes
- **Validation errors**: Invalid parameters
- **Authentication errors**: Missing or invalid credentials

### Error Response Example

```json
{
  "detail": "Failed to sign note"
}
```

## Authentication

The function automatically handles authentication in the following order:

1. **staffid**: Uses provided value, then `Cci.util.Staff.getSid()`, then `undefined`
2. **ccitoken**: Uses provided value, then `localStorage.getItem("webtoken")`, then `sessionStorage.getItem("webtoken")`, then empty string

## Storage Backend Support

The function works with all configured storage backends:

- **CCIDB**: Primary storage with signature tables
- **Local Disk**: File-based storage
- **GitLab**: Git-based storage with version control

## TypeScript Types

```typescript
// Request interface
interface SignNoteRequest {
  note_type: string;
  comment: string;
  staffid?: number;
  ccitoken?: string;
}

// Response interface
interface SignNoteResponse {
  message: string;
}
```

## Configuration

Note types that require signatures are configured in the backend at:
`dataServer/app/config/note_types_config.json`

```json
{
  "Progress Note": { "nit": 0, "required_signature": true },
  "ED Progress Note": { "nit": 1, "required_signature": true },
  "Discharge Summary": { "nit": 2, "required_signature": false }
}
```

## Testing

The function includes comprehensive tests in `tests/UiNotes/sign-report.test.ts`:

- Success scenarios
- Error handling
- Custom credentials
- Request validation

## Related Components

- **SignButton**: React component for signing notes with UI
- **Read-only functionality**: Automatic read-only state for signed notes
- **Signature display**: Shows signature information in note details

## Best Practices

1. **Always handle errors**: Use try-catch blocks
2. **Validate input**: Ensure required parameters are provided
3. **Show loading states**: Disable UI during signing process
4. **Provide user feedback**: Show success/error messages
5. **Refresh data**: Update note list after successful signing
6. **Use meaningful comments**: Provide descriptive signature comments

## Security Considerations

- Credentials are automatically handled but can be overridden
- All requests include proper authentication headers
- CCIDB integration requires valid staffid and ccitoken
- Signature data is stored securely in CCIDB tables 