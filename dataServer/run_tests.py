#!/usr/bin/env python3
"""
Simple test runner for dataServer.
This script runs all tests from the tests directory.
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Run all tests."""
    tests_dir = Path(__file__).parent / "tests"
    test_runner = tests_dir / "run_all_tests.py"
    
    if not test_runner.exists():
        print("❌ Test runner not found. Please ensure tests/run_all_tests.py exists.")
        return 1
    
    print("🧪 Running dataServer tests...\n")
    
    try:
        result = subprocess.run([sys.executable, str(test_runner)], 
                              cwd=tests_dir)
        return result.returncode
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 