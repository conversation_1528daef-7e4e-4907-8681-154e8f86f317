# YCQL Testing Guide

## Overview

Testing YCQL (YottaDB Query Command Language) code requires different strategies than regular Python unit tests. This guide covers multiple approaches for testing YCQL code effectively.

## 🧪 **Testing Strategies**

### **1. Mock-Based Testing (Recommended for CI/CD)**

**Best for:** Unit testing YCQL logic without CCIDB connection
**Pros:** Fast, reliable, no external dependencies
**Cons:** Doesn't test actual database interactions

```python
# Example: test_ycql_queries.py
class YCQLQueryTester:
    def __init__(self):
        self.mock_db = {}
        
    def mock_ppdo_query(self, jit, key=None, data=None):
        # Mock database queries
        pass

class TestYCQLNoteTypes(unittest.TestCase):
    def test_getnit_existing_note_type(self):
        # Test YCQL logic with mocked data
        pass
```

**Run with:**
```bash
python3 tests/test_ycql_queries.py
```

### **2. Syntax Validation Testing**

**Best for:** Ensuring YCQL files have correct syntax structure
**Pros:** Catches syntax errors early
**Cons:** Doesn't test runtime behavior

```python
# Example: test_ycql_syntax.py
class YCQLSyntaxValidator:
    def validate_file(self, file_path):
        # Check YCQL syntax patterns
        pass
```

**Run with:**
```bash
python3 tests/test_ycql_syntax.py
```

### **3. Integration Testing (With CCIDB)**

**Best for:** Testing actual YCQL execution
**Pros:** Tests real database interactions
**Cons:** Requires CCIDB environment

```python
# Example: test_ycql_integration.py
class TestYCQLIntegration(unittest.TestCase):
    def setUp(self):
        # Setup CCIDB connection
        self.ycql_client = create_ycql_client()
        
    def test_note_types_integration(self):
        # Test actual YCQL execution
        result = await self.ycql_client.execute({
            "type": "HOBJ",
            "HOBJ": "ainotes/config/notetypes/getnit",
            "qargv": {":note_type_name": "Progress Note"}
        })
        self.assertIsNotNone(result)
```

### **4. Parameterized Testing**

**Best for:** Testing multiple scenarios with different inputs
**Pros:** Comprehensive coverage
**Cons:** More complex setup

```python
class TestYCQLParameterized(unittest.TestCase):
    @parameterized.expand([
        ("Progress Note", 0),
        ("ED Progress Note", 1),
        ("Custom Type", 5),  # New type
    ])
    def test_getnit_various_types(self, note_type, expected_nit):
        # Test different note types
        pass
```

## 🔧 **Testing Tools**

### **YCQL Query Tester**

```python
class YCQLQueryTester:
    """Helper class to test YCQL queries without CCIDB connection"""
    
    def __init__(self):
        self.mock_db = {}
        
    def setup_mock_data(self):
        """Setup mock database data for testing"""
        self.mock_db['AI Notes Types'] = {
            0: '"Progress Note"',
            1: '"ED Progress Note"',
            # ... more data
        }
        
    def mock_itlist(self, table_name):
        """Mock itlist function"""
        return hash(table_name) % 1000
        
    def mock_ppdo_query(self, jit, key=None, data=None):
        """Mock ppdo table query"""
        # Simulate database queries
        pass
```

### **YCQL Syntax Validator**

```python
class YCQLSyntaxValidator:
    """Validates YCQL syntax and structure"""
    
    def validate_file(self, file_path):
        """Validate a single YCQL file"""
        # Check syntax patterns
        pass
        
    def _check_select_statements(self, content, file_path):
        """Check for valid SELECT statements"""
        pass
        
    def _check_file_includes(self, content, file_path):
        """Check for valid .file: includes"""
        pass
```

## 📋 **Test Categories**

### **1. Logic Testing**

Test the business logic of YCQL queries:

```python
def test_getnit_new_note_type(self):
    """Test getnit.ycql with new note type"""
    note_type_name = "Custom Note Type"
    
    # First query should return None (not found)
    existing_nit = self.ycql_tester.mock_ppdo_query(
        self.ycql_tester.mock_itlist('AI Notes Types'),
        data=f'"{note_type_name}"'
    )
    self.assertIsNone(existing_nit)
    
    # Should get next available NIT
    next_nit = self.ycql_tester.mock_getMinIT('AI Notes')
    self.assertEqual(next_nit, 5)
```

### **2. Data Flow Testing**

Test how data flows through YCQL queries:

```python
def test_signature_signing_flow(self):
    """Test the complete signature signing flow"""
    # Step 1: Get NIT for note type
    nit = self.ycql_tester.mock_ppdo_query(
        self.ycql_tester.mock_itlist('AI Notes Types'),
        data=f'"{note_type_name}"'
    )
    self.assertEqual(nit, 0)
    
    # Step 2: Create signature data
    signature_data = {
        'signedBy': 12345,
        'noteTypeName': note_type_name,
        'noteTypeNit': nit
    }
    
    # Step 3: Store signature
    # ... verification logic
```

### **3. Error Handling Testing**

Test error conditions and edge cases:

```python
def test_getnit_invalid_input(self):
    """Test getnit.ycql with invalid input"""
    # Test with empty string
    result = self.ycql_tester.mock_ppdo_query(
        self.ycql_tester.mock_itlist('AI Notes Types'),
        data='""'
    )
    # Should handle gracefully
    
    # Test with null input
    result = self.ycql_tester.mock_ppdo_query(
        self.ycql_tester.mock_itlist('AI Notes Types'),
        data=None
    )
    # Should handle gracefully
```

### **4. Performance Testing**

Test query performance (with real CCIDB):

```python
def test_query_performance(self):
    """Test YCQL query performance"""
    import time
    
    start_time = time.time()
    result = await self.ycql_client.execute({
        "type": "HOBJ",
        "HOBJ": "ainotes/config/notetypes/getall"
    })
    end_time = time.time()
    
    execution_time = end_time - start_time
    self.assertLess(execution_time, 1.0)  # Should complete within 1 second
```

## 🚀 **Running Tests**

### **Individual Test Files**

```bash
# Run syntax validation
python3 tests/test_ycql_syntax.py

# Run logic testing
python3 tests/test_ycql_queries.py

# Run integration tests (requires CCIDB)
python3 tests/test_ycql_integration.py
```

### **All YCQL Tests**

```bash
# Run comprehensive YCQL test suite
python3 tests/run_ycql_tests.py
```

### **CI/CD Integration**

```yaml
# Example GitHub Actions workflow
- name: Run YCQL Tests
  run: |
    python3 tests/test_ycql_syntax.py
    python3 tests/test_ycql_queries.py
```

## 📊 **Test Coverage**

### **What to Test**

✅ **YCQL Logic**: Business logic and data transformations  
✅ **Syntax**: Valid YCQL syntax and structure  
✅ **Error Handling**: Edge cases and error conditions  
✅ **Data Flow**: How data moves through queries  
✅ **Performance**: Query execution time  
✅ **Integration**: Real database interactions  

### **Test Metrics**

- **Syntax Coverage**: All YCQL files have valid syntax
- **Logic Coverage**: All business logic paths tested
- **Error Coverage**: All error conditions handled
- **Performance Coverage**: Queries meet performance requirements

## 🛠 **Best Practices**

### **1. Use Mocks for Unit Tests**

```python
# Good: Mock database interactions
def test_getnit_existing_note_type(self):
    with patch('ycql.ppdo') as mock_ppdo:
        mock_ppdo.query.return_value = 0
        # Test logic here
```

### **2. Test Edge Cases**

```python
# Test boundary conditions
def test_getnit_edge_cases(self):
    # Empty string
    # Very long string
    # Special characters
    # Null values
    pass
```

### **3. Validate Syntax Early**

```python
# Check syntax before running logic tests
def test_ycql_syntax(self):
    validator = YCQLSyntaxValidator()
    self.assertTrue(validator.validate_file('path/to/file.ycql'))
```

### **4. Use Parameterized Tests**

```python
@parameterized.expand([
    ("normal_case", "Progress Note", 0),
    ("edge_case", "", None),
    ("special_chars", "Note-Type_123", 5),
])
def test_getnit_various_inputs(self, name, input_value, expected):
    # Test multiple scenarios
    pass
```

## 🔍 **Debugging YCQL Tests**

### **Common Issues**

1. **Path Problems**: Ensure correct file paths
2. **Mock Setup**: Verify mock data is properly configured
3. **Syntax Errors**: Use syntax validator to catch issues early
4. **Environment Issues**: Check CCIDB connection for integration tests

### **Debug Tools**

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Print mock data
print(f"Mock DB: {self.ycql_tester.mock_db}")

# Validate YCQL syntax
validator = YCQLSyntaxValidator()
print(validator.get_report())
```

## 📈 **Continuous Improvement**

### **Test Maintenance**

- Update tests when YCQL logic changes
- Add new tests for new functionality
- Remove obsolete tests
- Monitor test performance

### **Test Documentation**

- Document test scenarios
- Explain mock data setup
- Document expected results
- Keep test examples up to date

This comprehensive testing approach ensures YCQL code is reliable, maintainable, and performs correctly in production environments. 