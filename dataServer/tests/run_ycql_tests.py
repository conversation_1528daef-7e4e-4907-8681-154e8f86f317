#!/usr/bin/env python3
"""
Comprehensive YCQL Test Runner
Runs all YCQL tests including syntax validation, logic testing, and integration tests
"""

import sys
import subprocess
from pathlib import Path

def run_test(test_file, description):
    """Run a single test file"""
    print(f"\n{'='*60}")
    print(f"Running {description}...")
    print(f"{'='*60}")
    
    try:
        test_path = Path(__file__).parent / test_file
        result = subprocess.run([sys.executable, str(test_path)], 
                              capture_output=True, 
                              text=True, 
                              cwd=Path(__file__).parent)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running {test_file}: {e}")
        return False

def main():
    """Run all YCQL tests"""
    print("🧪 Running Comprehensive YCQL Tests...\n")
    
    # Define test files and their descriptions
    test_files = [
        ("test_ycql_syntax.py", "YCQL Syntax Validation"),
        ("test_ycql_queries.py", "YCQL Logic Testing")
    ]
    
    results = []
    
    for test_file, description in test_files:
        test_path = Path(__file__).parent / test_file
        if test_path.exists():
            success = run_test(test_file, description)
            results.append((description, success))
        else:
            print(f"⚠️  Test file {test_file} not found, skipping...")
            results.append((description, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("YCQL TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {description}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All YCQL tests passed! The YCQL code is working correctly.")
        return 0
    else:
        print("❌ Some YCQL tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 