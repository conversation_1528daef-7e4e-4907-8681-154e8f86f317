#!/usr/bin/env python3
"""
YCQL Syntax Validator
Validates YCQL file syntax and structure
"""

import unittest
from pathlib import Path
import sys
import os

# Add the parent directory to the path so we can import the YCQL modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestYCQLSyntaxValidation(unittest.TestCase):
    """Test YCQL syntax validation"""
    
    def test_ycql_file_syntax(self):
        """Test that YCQL files have valid syntax structure"""
        ycql_files = [
            '../../HOBJS/ainotes/signature/sign/init.ycql',
            '../../HOBJS/ainotes/signature/getsignatures/query.ycql',
            '../../HOBJS/ainotes/signature/setrequiredsignature/init.ycql',
            '../../HOBJS/ainotes/signature/setrequiredsignature/query.ycql'
        ]
        
        # Files that should have .file: includes (modules that include other files)
        files_with_includes = [
            '../../HOBJS/ainotes/signature/sign/init.ycql',
            '../../HOBJS/ainotes/signature/getsignatures/query.ycql',
            '../../HOBJS/ainotes/signature/setrequiredsignature/query.ycql'
        ]
        
        for ycql_file in ycql_files:
            with self.subTest(file=ycql_file):
                file_path = Path(__file__).parent / ycql_file
                self.assertTrue(file_path.exists(), f"YCQL file {ycql_file} should exist")
                
                # Read file and check basic syntax
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                # Check for required YCQL elements
                self.assertIn('select', content.lower(), f"YCQL file {ycql_file} should contain select statements")
                
                # Only check for .file: includes if the file should have them
                if ycql_file in files_with_includes:
                    self.assertIn('.file:', content, f"YCQL file {ycql_file} should contain file includes")
                
                # Check for proper structure
                lines = content.split('\n')
                self.assertGreater(len(lines), 5, f"YCQL file {ycql_file} should have reasonable length")
                
                # Check for valid YCQL syntax patterns
                self._check_ycql_syntax_patterns(content, ycql_file)
    
    def _check_ycql_syntax_patterns(self, content, file_path):
        """Check for valid YCQL syntax patterns"""
        # Check for proper variable usage (only if both setvar and getvar are used)
        if 'setvar(' in content and 'getvar(' in content:
            # If both are used, they should be used together
            pass  # This is good
        
        # Check for proper SQL syntax
        if 'select' in content.lower():
            # Should have proper FROM clause or function calls
            has_from = 'from' in content.lower()
            has_function = any(func in content.lower() for func in ['itlist(', 'getvar(', 'setvar(', 'seteditflag(', 'tabletojson('])
            has_exec = '.exec:' in content
            self.assertTrue(has_from or has_function or has_exec, f"YCQL file {file_path} should have FROM clause, function calls, or .exec statements")
        
        # Check for proper comment style (just warnings, not failures)
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('--') and not stripped.startswith('-- '):
                # This is just a warning, not a failure
                pass

def run_ycql_syntax_tests():
    """Run YCQL syntax tests"""
    print("🧪 Running YCQL Syntax Tests...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestYCQLSyntaxValidation))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("=" * 60)
    if result.wasSuccessful():
        print("✅ All YCQL syntax tests passed!")
    else:
        print("❌ Some YCQL syntax tests failed!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_ycql_syntax_tests() 