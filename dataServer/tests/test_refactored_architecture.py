#!/usr/bin/env python3
"""
Test script to verify the refactored architecture is working correctly.
This test bypasses FastAPI dependencies and focuses on core functionality.
"""

import sys
import os
import importlib.util
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent

def test_syntax_and_structure():
    print("Testing syntax and structure...")
    files_to_test = [
        "app/services/managers/notes_manager.py",
        "app/services/managers/note_macros_manager.py",
        "app/services/managers/rad_macros_manager.py",
        "app/services/managers/rad_templates_manager.py",
        "app/services/managers/exam_manager.py",
        "app/services/storage/local_storage.py",
        "app/services/storage/gitlab_storage.py",
        "app/services/storage/ccidb_storage.py",
        "app/services/service_factory.py",
        "app/utils/validators.py",
        "app/config/settings.py"
    ]
    passed = 0
    total = len(files_to_test)
    for file_path in files_to_test:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            compile(content, file_path, 'exec')
            print(f"✓ {file_path}: Syntax valid")
            passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Syntax error - {e}")
    print(f"Syntax tests: {passed}/{total} passed\n")
    return passed == total

def test_manager_architecture():
    print("Testing manager architecture...")
    managers = [
        ("app/services/managers/notes_manager.py", "NotesManager"),
        ("app/services/managers/note_macros_manager.py", "NoteMacrosManager"),
        ("app/services/managers/rad_macros_manager.py", "RadMacrosManager"),
        ("app/services/managers/rad_templates_manager.py", "RadTemplatesManager"),
        ("app/services/managers/exam_manager.py", "ExamManager")
    ]
    passed = 0
    total = len(managers)
    for file_path, class_name in managers:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            checks = [
                f"class {class_name}:",
                "__init__(self,",
                "async def get_",
                "async def save_"
            ]
            all_checks_passed = True
            for check in checks:
                if check not in content:
                    print(f"✗ {file_path}: Missing '{check}'")
                    all_checks_passed = False
            if all_checks_passed:
                print(f"✓ {file_path}: Architecture correct")
                passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Error - {e}")
    print(f"Manager architecture tests: {passed}/{total} passed\n")
    return passed == total

def test_service_factory():
    print("Testing service factory...")
    try:
        with open(PROJECT_ROOT / "app/services/service_factory.py", 'r') as f:
            content = f.read()
        checks = [
            "def get_notes_manager():",
            "def get_note_macros_manager():",
            "def get_rad_macros_manager():",
            "def get_rad_templates_manager():",
            "def get_exam_manager():",
            "def get_ccidb_storage():",
            "def get_local_storage():",
            "def get_gitlab_service():",
            "get_ccidb_storage()",
            "NotesManager(",
            "NoteMacrosManager(",
            "RadMacrosManager(",
            "RadTemplatesManager(",
            "ExamManager("
        ]
        passed = 0
        total = len(checks)
        for check in checks:
            if check in content:
                print(f"✓ Found: {check}")
                passed += 1
            else:
                print(f"✗ Missing: {check}")
        print(f"Service factory tests: {passed}/{total} passed\n")
        return passed == total
    except Exception as e:
        print(f"✗ Service factory test failed: {e}\n")
        return False

def test_routes_architecture():
    print("Testing routes architecture...")
    routes = [
        ("app/routes/notes.py", "get_notes_manager"),
        ("app/routes/note_macros.py", "get_note_macros_manager"),
        ("app/routes/rad_macros.py", "get_rad_macros_manager"),
        ("app/routes/rad_templates.py", "get_rad_templates_manager"),
        ("app/routes/exam.py", "get_exam_manager")
    ]
    passed = 0
    total = len(routes)
    for file_path, manager_call in routes:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            checks = [
                f"from app.services.service_factory import {manager_call}",
                f"{manager_call}()",
                "async def"
            ]
            
            # Add CCIDB-specific checks only for notes and exams
            if manager_call in ["get_notes_manager", "get_exam_manager"]:
                checks.extend([
                    "settings.enable_ccidb_storage",
                    "staffid",
                    "ccitoken"
                ])
            all_checks_passed = True
            for check in checks:
                if check not in content:
                    print(f"✗ {file_path}: Missing '{check}'")
                    all_checks_passed = False
            if all_checks_passed:
                print(f"✓ {file_path}: Uses managers correctly")
                passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Error - {e}")
    print(f"Routes architecture tests: {passed}/{total} passed\n")
    return passed == total

def test_ccidb_storage():
    print("Testing CCIDB storage...")
    try:
        with open(PROJECT_ROOT / "app/services/storage/ccidb_storage.py", 'r') as f:
            content = f.read()
        methods = [
            "read_note_from_ccidb",
            "write_note_to_ccidb", 
            "read_exam_from_ccidb",
            "write_exam_to_ccidb"
        ]
        passed = 0
        total = len(methods)
        for method in methods:
            if f"async def {method}" in content:
                print(f"✓ Found: {method}")
                passed += 1
            else:
                print(f"✗ Missing: {method}")
        print(f"CCIDB storage tests: {passed}/{total} passed\n")
        return passed == total
    except Exception as e:
        print(f"✗ CCIDB storage test failed: {e}\n")
        return False

def test_settings():
    print("Testing settings...")
    try:
        with open(PROJECT_ROOT / "app/config/settings.py", 'r') as f:
            content = f.read()
        settings = [
            "enable_ccidb_storage",
            "enable_gitlab_storage",
            "enable_local_disk_storage"
        ]
        passed = 0
        total = len(settings)
        for setting in settings:
            if setting in content:
                print(f"✓ Found: {setting}")
                passed += 1
            else:
                print(f"✗ Missing: {setting}")
        print(f"Settings tests: {passed}/{total} passed\n")
        return passed == total
    except Exception as e:
        print(f"✗ Settings test failed: {e}\n")
        return False

def main():
    print("Running comprehensive architecture tests...\n")
    tests = [
        ("Syntax and Structure", test_syntax_and_structure),
        ("Manager Architecture", test_manager_architecture),
        ("Service Factory", test_service_factory),
        ("Routes Architecture", test_routes_architecture),
        ("CCIDB Storage", test_ccidb_storage),
        ("Settings", test_settings)
    ]
    results = []
    for test_name, test_func in tests:
        print(f"=== {test_name} ===")
        result = test_func()
        results.append((test_name, result))
        print()
    print("=== TEST SUMMARY ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_name}")
    print(f"\nOverall: {passed}/{total} test suites passed")
    if passed == total:
        print("🎉 All tests passed! The refactored architecture is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 