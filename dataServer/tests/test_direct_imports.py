#!/usr/bin/env python3
"""
Test script to verify that direct imports of storage and manager modules are not present in routes (should use service_factory only).
"""
import sys
import re
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent

def test_no_direct_imports(file_path):
    print(f"Testing for direct imports in {file_path}...")
    try:
        with open(PROJECT_ROOT / file_path, 'r') as f:
            content = f.read()
        # Should not import storage or manager modules directly
        bad_patterns = [
            r"from app.services.storage",
            r"from app.services.managers",
            r"import app.services.storage",
            r"import app.services.managers"
        ]
        found = False
        for pattern in bad_patterns:
            if re.search(pattern, content):
                print(f"✗ Found direct import: {pattern}")
                found = True
        if not found:
            print(f"✓ No direct storage/manager imports found")
        return True
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def main():
    print("Running direct import tests...\n")
    files = [
        "app/routes/notes.py",
        "app/routes/note_macros.py",
        "app/routes/rad_macros.py",
        "app/routes/rad_templates.py",
        "app/routes/exam.py"
    ]
    passed = 0
    total = len(files)
    for file_path in files:
        if test_no_direct_imports(file_path):
            passed += 1
    print(f"\nDirect import check: {passed}/{total} files passed")
    if passed == total:
        print("🎉 All direct import tests passed!")
        return 0
    else:
        print("❌ Some direct import tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 