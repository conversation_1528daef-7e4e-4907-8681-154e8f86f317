#!/usr/bin/env python3
"""
Test script to verify that all imports in the refactored code are correct and there are no circular imports.
"""
import sys
import ast
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent

def test_imports(file_path):
    print(f"Testing imports in {file_path}...")
    try:
        with open(PROJECT_ROOT / file_path, 'r') as f:
            content = f.read()
        tree = ast.parse(content)
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")
        # Check for circular or problematic imports
        problematic = []
        if 'app.services.service_factory' in imports and 'managers' in file_path:
            problematic.append('service_factory in manager')
        if 'app.services.managers' in imports and 'service_factory' in file_path:
            problematic.append('manager in service_factory')
        if problematic:
            print(f"✗ Problematic imports: {problematic}")
            return False
        print(f"✓ Imports OK")
        return True
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def main():
    print("Running import structure tests...\n")
    files = [
        "app/services/service_factory.py",
        "app/services/managers/notes_manager.py",
        "app/services/managers/note_macros_manager.py",
        "app/services/managers/rad_macros_manager.py",
        "app/services/managers/rad_templates_manager.py",
        "app/services/managers/exam_manager.py",
        "app/routes/notes.py",
        "app/routes/note_macros.py",
        "app/routes/rad_macros.py",
        "app/routes/rad_templates.py",
        "app/routes/exam.py"
    ]
    passed = 0
    total = len(files)
    for file_path in files:
        if test_imports(file_path):
            passed += 1
    print(f"\nImport structure: {passed}/{total} files passed")
    if passed == total:
        print("🎉 All import structure tests passed!")
        return 0
    else:
        print("❌ Some import structure tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 