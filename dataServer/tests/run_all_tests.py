#!/usr/bin/env python3
"""
Main test runner for dataServer tests.
This script runs all tests in the correct order.
"""

import sys
import os
import subprocess
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.insert(0, str(Path(__file__).parent.parent))

def run_test(test_file):
    """Run a single test file and return the result."""
    print(f"\n{'='*60}")
    print(f"Running {test_file}...")
    print(f"{'='*60}")
    
    try:
        test_path = Path(__file__).parent / test_file
        result = subprocess.run([sys.executable, str(test_path)], 
                              capture_output=True, 
                              text=True, 
                              cwd=Path(__file__).parent)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running {test_file}: {e}")
        return False

def main():
    """Run all tests in the correct order."""
    print("🧪 Running all dataServer tests...\n")
    
    # Define test files in the order they should be run
    test_files = [
        "test_core_functionality.py",
        "test_refactored_architecture.py", 
        "test_refactoring_specifics.py",
        "test_imports.py",
        "test_direct_imports.py",
        "test_sign_note.py",
        "test_ccidb_note_types.py"
    ]
    
    results = []
    
    for test_file in test_files:
        test_path = Path(__file__).parent / test_file
        if test_path.exists():
            success = run_test(test_file)
            results.append((test_file, success))
        else:
            print(f"⚠️  Test file {test_file} not found, skipping...")
            results.append((test_file, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_file, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_file}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All tests passed! The dataServer is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 