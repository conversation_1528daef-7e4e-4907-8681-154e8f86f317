#!/usr/bin/env python3
"""
Test script for CCIDB note types implementation
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Set the working directory to the dataServer root for proper path resolution
os.chdir(os.path.join(os.path.dirname(__file__), '..'))

from app.services.storage.ccidb_storage import CCIDBStorage
from app.utils.logger import logger

async def test_note_types():
    """Test the note types functionality"""
    
    # Initialize CCIDB storage
    ccidb = CCIDBStorage()
    
    # Test parameters
    campus = "campus1"
    pat_id = "12345"
    staffid = 12345
    ccitoken = "test_token"
    
    print("🧪 Testing CCIDB Note Types Implementation")
    print("=" * 50)
    
    # Test 1: Test note type configuration loading
    print("\n1. Testing note type configuration...")
    try:
        from app.services.storage.ccidb_storage import NOTE_TYPES_CONFIG
        print("✅ Successfully loaded note types configuration")
        print(f"Available note types: {list(NOTE_TYPES_CONFIG.keys())}")
        for note_type, config in NOTE_TYPES_CONFIG.items():
            print(f"  - {note_type}: NIT={config['nit']}, Required Signature={config['required_signature']}")
    except Exception as e:
        print(f"❌ Error loading note types configuration: {e}")
    
    # Test 2: Test get_required_signature with existing type
    print("\n2. Testing get_required_signature with existing type...")
    try:
        result = await ccidb.get_required_signature(
            campus, pat_id, "Progress Note", 1234567890
        )
        print(f"✅ get_required_signature result: {result}")
    except Exception as e:
        print(f"❌ Error getting required signature: {e}")
    
    # Test 3: Test set_required_signature with existing type
    print("\n3. Testing set_required_signature with existing type...")
    try:
        result = await ccidb.set_required_signature(
            campus, pat_id, "Progress Note", 1234567890, 
            "Test signature requirement", staffid, ccitoken
        )
        print(f"✅ set_required_signature result: {result}")
    except Exception as e:
        print(f"❌ Error setting required signature: {e}")
    
    # Test 4: Test sign_note with existing type
    print("\n4. Testing sign_note with existing type...")
    try:
        result = await ccidb.sign_note(
            campus, pat_id, "Progress Note", 1234567890, 
            "Test signature", staffid, ccitoken
        )
        print(f"✅ sign_note result: {result}")
    except Exception as e:
        print(f"❌ Error signing note with existing type: {e}")
    
    # Test 5: Test with non-existent note type
    print("\n5. Testing with non-existent note type...")
    try:
        result = await ccidb.sign_note(
            campus, pat_id, "Non-existent Note Type", 1234567891, 
            "Test signature with non-existent type", staffid, ccitoken
        )
        print(f"✅ sign_note result: {result}")
    except Exception as e:
        print(f"❌ Error signing note with non-existent type: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")

if __name__ == "__main__":
    asyncio.run(test_note_types()) 