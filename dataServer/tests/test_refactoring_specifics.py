#!/usr/bin/env python3
"""
Test script to verify specific refactoring changes made to the architecture.
This test focuses on the key changes: async methods, CCIDB integration, and manager patterns.
"""

import sys
import re
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent

def test_async_methods():
    print("Testing async methods in managers...")
    managers = [
        ("app/services/managers/notes_manager.py", ["get_note", "save_note"]),
        ("app/services/managers/note_macros_manager.py", ["get_macro", "save_macro"]),
        ("app/services/managers/rad_macros_manager.py", ["get_macro", "save_macro"]),
        ("app/services/managers/rad_templates_manager.py", ["get_template", "save_template"]),
        ("app/services/managers/exam_manager.py", ["get_exam", "save_exam"])
    ]
    passed = 0
    total = len(managers)
    for file_path, methods in managers:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            all_methods_found = True
            for method in methods:
                pattern = rf"async def {method}\("
                if not re.search(pattern, content):
                    print(f"✗ {file_path}: Missing async {method}")
                    all_methods_found = False
            if all_methods_found:
                print(f"✓ {file_path}: All async methods present")
                passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Error - {e}")
    print(f"Async methods tests: {passed}/{total} passed\n")
    return passed == total

def test_ccidb_integration():
    print("Testing CCIDB integration in managers...")
    managers = [
        ("app/services/managers/notes_manager.py", True),
        ("app/services/managers/note_macros_manager.py", False),
        ("app/services/managers/rad_macros_manager.py", False),
        ("app/services/managers/rad_templates_manager.py", False),
        ("app/services/managers/exam_manager.py", True)
    ]
    passed = 0
    total = len(managers)
    for file_path, uses_ccidb in managers:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            
            if uses_ccidb:
                # Check for CCIDB integration
                checks = [
                    "self.ccidb_storage",
                    "settings.enable_ccidb_storage",
                    "ccidb_storage=None",
                    "await self.ccidb_storage",
                    "staffid=",
                    "ccitoken="
                ]
                all_checks_passed = True
                for check in checks:
                    if check not in content:
                        print(f"✗ {file_path}: Missing '{check}'")
                        all_checks_passed = False
                if all_checks_passed:
                    print(f"✓ {file_path}: CCIDB integration correct")
                    passed += 1
            else:
                # Check that CCIDB is NOT used
                ccidb_checks = [
                    "self.ccidb_storage",
                    "settings.enable_ccidb_storage",
                    "ccidb_storage=None",
                    "await self.ccidb_storage"
                ]
                ccidb_found = False
                for check in ccidb_checks:
                    if check in content:
                        print(f"✗ {file_path}: Should not contain '{check}'")
                        ccidb_found = True
                if not ccidb_found:
                    print(f"✓ {file_path}: No CCIDB integration (as expected)")
                    passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Error - {e}")
    print(f"CCIDB integration tests: {passed}/{total} passed\n")
    return passed == total



def test_service_factory_injection():
    print("Testing service factory dependency injection...")
    try:
        with open(PROJECT_ROOT / "app/services/service_factory.py", 'r') as f:
            content = f.read()
        manager_patterns = [
            r"NotesManager\([^)]*get_ccidb_storage\(\)[^)]*\)",
            r"MacrosManager\([^)]*get_gitlab_service\(\)[^)]*get_local_storage\(\)[^)]*\)",
            r"ExamManager\([^)]*get_ccidb_storage\(\)[^)]*\)"
        ]
        passed = 0
        total = len(manager_patterns)
        for pattern in manager_patterns:
            if re.search(pattern, content):
                print(f"✓ Found: {pattern}")
                passed += 1
            else:
                if "NotesManager(get_gitlab_service(), get_local_storage(), get_ccidb_storage())" in content:
                    print(f"✓ Found: NotesManager with CCIDB injection")
                    passed += 1
                elif "MacrosManager(get_gitlab_service(), get_local_storage())" in content:
                    print(f"✓ Found: MacrosManager without CCIDB injection")
                    passed += 1
                elif "ExamManager(get_gitlab_service(), get_local_storage(), get_ccidb_storage())" in content:
                    print(f"✓ Found: ExamManager with CCIDB injection")
                    passed += 1
                else:
                    print(f"✗ Missing: {pattern}")
        print(f"Service factory injection tests: {passed}/{total} passed\n")
        return passed == total
    except Exception as e:
        print(f"✗ Service factory injection test failed: {e}\n")
        return False

def test_credential_validation():
    print("Testing credential validation in routes...")
    routes = [
        ("app/routes/notes.py", True),
        ("app/routes/note_macros.py", False),
        ("app/routes/rad_macros.py", False),
        ("app/routes/rad_templates.py", False),
        ("app/routes/exam.py", True)
    ]
    passed = 0
    total = len(routes)
    for file_path, needs_ccidb in routes:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            
            if needs_ccidb:
                # Check for CCIDB credential validation
                validation_patterns = [
                    r"settings\.enable_ccidb_storage",
                    r"staffid.*None.*ccitoken.*None",
                    r"staffid and ccitoken are required for CCIDB operations"
                ]
                all_patterns_found = True
                for pattern in validation_patterns:
                    if not re.search(pattern, content):
                        print(f"✗ {file_path}: Missing credential validation '{pattern}'")
                        all_patterns_found = False
                if all_patterns_found:
                    print(f"✓ {file_path}: Credential validation present")
                    passed += 1
            else:
                # Check that CCIDB validation is NOT present
                ccidb_patterns = [
                    r"settings\.enable_ccidb_storage",
                    r"staffid.*None.*ccitoken.*None",
                    r"staffid and ccitoken are required for CCIDB operations"
                ]
                ccidb_found = False
                for pattern in ccidb_patterns:
                    if re.search(pattern, content):
                        print(f"✗ {file_path}: Should not contain CCIDB validation '{pattern}'")
                        ccidb_found = True
                if not ccidb_found:
                    print(f"✓ {file_path}: No CCIDB validation (as expected)")
                    passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Error - {e}")
    print(f"Credential validation tests: {passed}/{total} passed\n")
    return passed == total

def test_ccidb_storage_methods():
    print("Testing CCIDB storage methods...")
    try:
        with open(PROJECT_ROOT / "app/services/storage/ccidb_storage.py", 'r') as f:
            content = f.read()
        required_methods = [
            "read_note_from_ccidb",
            "write_note_to_ccidb",
            "read_exam_from_ccidb",
            "write_exam_to_ccidb"
        ]
        passed = 0
        total = len(required_methods)
        for method in required_methods:
            if f"async def {method}" in content:
                print(f"✓ Found: {method}")
                passed += 1
            else:
                print(f"✗ Missing: {method}")
        print(f"CCIDB storage methods tests: {passed}/{total} passed\n")
        return passed == total
    except Exception as e:
        print(f"✗ CCIDB storage methods test failed: {e}\n")
        return False

def test_consistency_patterns():
    print("Testing consistency patterns across managers...")
    managers = [
        "app/services/managers/notes_manager.py",
        "app/services/managers/macros_manager.py",
        "app/services/managers/exam_manager.py"
    ]
    passed = 0
    total = len(managers)
    for file_path in managers:
        try:
            with open(PROJECT_ROOT / file_path, 'r') as f:
                content = f.read()
            patterns = [
                r"def __init__\(self,",
                r"try:",
                r"except Exception as e:",
                r"logger\.warning"
            ]
            
            all_patterns_found = True
            for pattern in patterns:
                if not re.search(pattern, content):
                    print(f"✗ {file_path}: Missing consistency pattern '{pattern}'")
                    all_patterns_found = False
            
            # Add return pattern check (either "return success" or "return True/False")
            if not re.search(r"return success", content) and not re.search(r"return (True|False)", content):
                print(f"✗ {file_path}: Missing consistency pattern 'return success' or 'return True/False'")
                all_patterns_found = False
            if all_patterns_found:
                print(f"✓ {file_path}: Follows consistent patterns")
                passed += 1
        except Exception as e:
            print(f"✗ {file_path}: Error - {e}")
    print(f"Consistency patterns tests: {passed}/{total} passed\n")
    return passed == total

def main():
    print("Running refactoring-specific tests...\n")
    tests = [
        ("Async Methods", test_async_methods),
        ("CCIDB Integration", test_ccidb_integration),
        ("Service Factory Injection", test_service_factory_injection),
        ("Credential Validation", test_credential_validation),
        ("CCIDB Storage Methods", test_ccidb_storage_methods),
        ("Consistency Patterns", test_consistency_patterns)
    ]
    results = []
    for test_name, test_func in tests:
        print(f"=== {test_name} ===")
        result = test_func()
        results.append((test_name, result))
        print()
    print("=== REFACTORING TEST SUMMARY ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_name}")
    print(f"\nOverall: {passed}/{total} test suites passed")
    if passed == total:
        print("🎉 All refactoring tests passed! The architecture changes are working correctly.")
        return 0
    else:
        print("❌ Some refactoring tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 