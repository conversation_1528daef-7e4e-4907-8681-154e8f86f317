#!/usr/bin/env python3
"""
YCQL Unit Testing Framework
Tests YCQL queries without requiring actual CCIDB connection
"""

import unittest
from pathlib import Path
import sys
import os

# Add the parent directory to the path so we can import the YCQL modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class YCQLQueryTester:
    """Mock YCQL query tester for unit testing"""
    
    def __init__(self):
        self.mock_db = {}
        self.setup_mock_data()
    
    def setup_mock_data(self):
        """Setup mock database tables"""
        # Mock AI Notes table
        self.mock_db['AI Notes'] = {}
        self.mock_db['AI Notes Signature'] = {}
        self.mock_db['AI Notes ReqSignature'] = {}
        
        # Mock itlist function
        self.mock_itlist_map = {
            'AI Notes': 1,
            'AI Notes Signature': 2,
            'AI Notes ReqSignature': 3,
        }
    
    def mock_itlist(self, table_name):
        """Mock itlist function"""
        return self.mock_itlist_map.get(table_name, 0)
    
    def mock_getMinIT(self, table_name):
        """Mock getMinIT function"""
        return 0  # Always return 0 for simplicity
    
    def mock_ppdo_query(self, jit, key=None, data=None, nit=None):
        """Mock ppdo query function"""
        table_name = None
        for name, table_jit in self.mock_itlist_map.items():
            if table_jit == jit:
                table_name = name
                break
        
        if not table_name:
            return None
        
        table = self.mock_db.get(table_name, {})
        
        if key is not None:
            return table.get(key)
        elif data is not None:
            # Search by data
            for k, v in table.items():
                if v == data:
                    return k
        elif nit is not None:
            # Search by nit (for tables that use nit as key)
            return table.get(nit)
        
        return None

class TestYCQLNoteTypes(unittest.TestCase):
    """Test YCQL note type functionality (now config-driven)"""
    
    def setUp(self):
        """Setup test environment"""
        self.ycql_tester = YCQLQueryTester()
        self.ycql_tester.setup_mock_data()
        
    def test_note_type_config_driven(self):
        """Test that note types are now config-driven (no dynamic table)"""
        # In the new config-driven approach, note types are defined in config file
        # and NITs are determined at the backend level, not in YCQL
        note_type_name = "Progress Note"
        
        # Should not find note type in dynamic table (it doesn't exist anymore)
        existing_nit = self.ycql_tester.mock_ppdo_query(
            self.ycql_tester.mock_itlist('AI Notes'),
            data=f'"{note_type_name}"'
        )
        
        self.assertIsNone(existing_nit)
        
    def test_signature_signing_flow(self):
        """Test the complete signature signing flow (config-driven)"""
        # Test parameters
        note_type_name = "Progress Note"
        key = 1234567890
        comment = "Test signature"
        nit = 0  # NIT comes from config, not dynamic lookup
        
        # Step 1: Create signature data (no dynamic NIT lookup)
        signature_data = {
            'signedBy': 12345,  # Mock staff ID
            'signedAt': 1234567890,  # Mock timestamp
            'comment': comment,
            'noteTypeNit': nit  # NIT from config
        }
        
        # Step 2: Store signature (mock)
        signature_key = f"{key}_{nit}"
        self.ycql_tester.mock_db['AI Notes Signature'][signature_key] = signature_data
        
        # Step 3: Verify signature was stored
        stored_signature = self.ycql_tester.mock_db['AI Notes Signature'].get(signature_key)
        self.assertIsNotNone(stored_signature)
        self.assertEqual(stored_signature['noteTypeNit'], nit)
        
    def test_signature_retrieval(self):
        """Test signature retrieval functionality"""
        # Setup test signature
        key = 1234567890
        nit = 0  # NIT from config
        note_type_name = "Progress Note"
        
        signature_data = {
            'signedBy': 12345,
            'signedAt': 1234567890,
            'comment': "Test signature",
            'noteTypeNit': nit  # NIT from config
        }
        
        # Store signature
        signature_key = f"{key}_{nit}"
        self.ycql_tester.mock_db['AI Notes Signature'][signature_key] = signature_data
        
        # Retrieve signature
        stored_signature = self.ycql_tester.mock_db['AI Notes Signature'].get(signature_key)
        
        self.assertIsNotNone(stored_signature)
        self.assertEqual(stored_signature['signedBy'], 12345)
        self.assertEqual(stored_signature['noteTypeNit'], nit)

class TestYCQLSignatureSigning(unittest.TestCase):
    """Test YCQL signature signing functionality"""
    
    def setUp(self):
        """Setup test environment"""
        self.ycql_tester = YCQLQueryTester()
        self.ycql_tester.setup_mock_data()
        
    def test_signature_signing_flow(self):
        """Test the complete signature signing flow"""
        # Test parameters
        note_type_name = "Progress Note"
        key = 1234567890
        comment = "Test signature"
        nit = 0  # NIT from config
        
        # Step 1: Create signature data (no dynamic NIT lookup needed)
        signature_data = {
            'signedBy': 12345,  # Mock staff ID
            'signedAt': 1234567890,  # Mock timestamp
            'comment': comment,
            'noteTypeNit': nit  # NIT from config
        }
        
        # Step 2: Store signature (mock)
        signature_key = f"{key}_{nit}"
        self.ycql_tester.mock_db['AI Notes Signature'][signature_key] = signature_data
        
        # Step 3: Verify signature was stored
        stored_signature = self.ycql_tester.mock_db['AI Notes Signature'].get(signature_key)
        self.assertIsNotNone(stored_signature)
        self.assertEqual(stored_signature['noteTypeNit'], nit)
        
    def test_signature_retrieval(self):
        """Test signature retrieval functionality"""
        # Setup test signature
        key = 1234567890
        nit = 0  # NIT from config
        note_type_name = "Progress Note"
        
        signature_data = {
            'signedBy': 12345,
            'signedAt': 1234567890,
            'comment': "Test signature",
            'noteTypeNit': nit  # NIT from config
        }
        
        # Store signature
        signature_key = f"{key}_{nit}"
        self.ycql_tester.mock_db['AI Notes Signature'][signature_key] = signature_data
        
        # Retrieve signature
        stored_signature = self.ycql_tester.mock_db['AI Notes Signature'].get(signature_key)
        
        self.assertIsNotNone(stored_signature)
        self.assertEqual(stored_signature['signedBy'], 12345)
        self.assertEqual(stored_signature['noteTypeNit'], nit)

class TestYCQLSyntaxValidation(unittest.TestCase):
    """Test YCQL syntax validation"""
    
    def test_ycql_file_syntax(self):
        """Test that YCQL files have valid syntax structure"""
        ycql_files = [
            '../../HOBJS/ainotes/signature/sign/init.ycql',
            '../../HOBJS/ainotes/signature/getsignatures/query.ycql',
            '../../HOBJS/ainotes/signature/setrequiredsignature/init.ycql',
            '../../HOBJS/ainotes/signature/setrequiredsignature/query.ycql'
        ]
        
        # Files that should have .file: includes (modules that include other files)
        files_with_includes = [
            '../../HOBJS/ainotes/signature/sign/init.ycql',
            '../../HOBJS/ainotes/signature/getsignatures/query.ycql',
            '../../HOBJS/ainotes/signature/setrequiredsignature/query.ycql'
        ]
        
        for ycql_file in ycql_files:
            with self.subTest(file=ycql_file):
                file_path = Path(__file__).parent / ycql_file
                self.assertTrue(file_path.exists(), f"YCQL file {ycql_file} should exist")
                
                # Read file and check basic syntax
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                # Check for required YCQL elements
                self.assertIn('select', content.lower(), f"YCQL file {ycql_file} should contain select statements")
                
                # Only check for .file: includes if the file should have them
                if ycql_file in files_with_includes:
                    self.assertIn('.file:', content, f"YCQL file {ycql_file} should contain file includes")
                
                # Check for proper structure
                lines = content.split('\n')
                self.assertGreater(len(lines), 5, f"YCQL file {ycql_file} should have reasonable length")
                
                # Check for valid YCQL syntax patterns
                self._check_ycql_syntax_patterns(content, ycql_file)
    
    def _check_ycql_syntax_patterns(self, content, file_path):
        """Check for valid YCQL syntax patterns"""
        # Check for proper variable usage (only if both setvar and getvar are used)
        if 'setvar(' in content and 'getvar(' in content:
            # If both are used, they should be used together
            pass  # This is good
        
        # Check for proper SQL syntax
        if 'select' in content.lower():
            # Should have proper FROM clause or function calls
            has_from = 'from' in content.lower()
            has_function = any(func in content.lower() for func in ['itlist(', 'getvar(', 'setvar(', 'seteditflag(', 'tabletojson('])
            has_exec = '.exec:' in content
            self.assertTrue(has_from or has_function or has_exec, f"YCQL file {file_path} should have FROM clause, function calls, or .exec statements")
        
        # Check for proper comment style (just warnings, not failures)
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('--') and not stripped.startswith('-- '):
                # This is just a warning, not a failure
                pass

def run_ycql_tests():
    """Run all YCQL tests"""
    print("🧪 Running YCQL Unit Tests...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestYCQLNoteTypes))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestYCQLSignatureSigning))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestYCQLSyntaxValidation))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("=" * 60)
    if result.wasSuccessful():
        print("✅ All YCQL tests passed!")
    else:
        print("❌ Some YCQL tests failed!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_ycql_tests() 