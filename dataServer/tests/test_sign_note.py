#!/usr/bin/env python3
"""
Test script to verify the sign_note functionality is working correctly.
"""

import sys
import os
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent

def test_sign_note_implementation():
    """Test that sign_note is properly implemented in all required places."""
    print("Testing sign_note implementation...")
    
    passed = 0
    total = 0
    
    # Test 1: Check CCIDB storage has sign_note method
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/storage/ccidb_storage.py", 'r') as f:
            content = f.read()
        
        if "async def sign_note" in content:
            print("✓ CCIDB storage has sign_note method")
            passed += 1
        else:
            print("✗ CCIDB storage missing sign_note method")
    except Exception as e:
        print(f"✗ Error checking CCIDB storage: {e}")
    
    # Test 2: Check Local storage has sign_note method
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/storage/local_storage.py", 'r') as f:
            content = f.read()
        
        if "def sign_note" in content:
            print("✓ Local storage has sign_note method")
            passed += 1
        else:
            print("✗ Local storage missing sign_note method")
    except Exception as e:
        print(f"✗ Error checking Local storage: {e}")
    
    # Test 3: Check GitLab storage has sign_note method
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/storage/gitlab_storage.py", 'r') as f:
            content = f.read()
        
        if "def sign_note" in content:
            print("✓ GitLab storage has sign_note method")
            passed += 1
        else:
            print("✗ GitLab storage missing sign_note method")
    except Exception as e:
        print(f"✗ Error checking GitLab storage: {e}")
    
    # Test 4: Check NotesManager has sign_note method
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/managers/notes_manager.py", 'r') as f:
            content = f.read()
        
        if "async def sign_note" in content:
            print("✓ NotesManager has sign_note method")
            passed += 1
        else:
            print("✗ NotesManager missing sign_note method")
    except Exception as e:
        print(f"✗ Error checking NotesManager: {e}")
    
    # Test 5: Check notes routes has sign_note endpoint
    total += 1
    try:
        with open(PROJECT_ROOT / "app/routes/notes.py", 'r') as f:
            content = f.read()
        
        if "@notes_router.post" in content and "sign_note" in content:
            print("✓ Notes routes has sign_note endpoint")
            passed += 1
        else:
            print("✗ Notes routes missing sign_note endpoint")
    except Exception as e:
        print(f"✗ Error checking notes routes: {e}")
    
    # Test 6: Check SignNoteRequest model exists
    total += 1
    try:
        with open(PROJECT_ROOT / "app/routes/notes.py", 'r') as f:
            content = f.read()
        
        if "class SignNoteRequest" in content:
            print("✓ SignNoteRequest model exists")
            passed += 1
        else:
            print("✗ SignNoteRequest model missing")
    except Exception as e:
        print(f"✗ Error checking SignNoteRequest model: {e}")
    
    # Test 7: Check NotesManager sign_note calls multiple storage backends
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/managers/notes_manager.py", 'r') as f:
            content = f.read()
        
        if "self.ccidb_storage.sign_note" in content and "self.local_storage.sign_note" in content and "self.gitlab_service.sign_note" in content:
            print("✓ NotesManager sign_note calls multiple storage backends")
            passed += 1
        else:
            print("✗ NotesManager sign_note doesn't call multiple storage backends")
    except Exception as e:
        print(f"✗ Error checking NotesManager storage calls: {e}")
    
    # Test 8: Check sign_note endpoint validates storage backends
    total += 1
    try:
        with open(PROJECT_ROOT / "app/routes/notes.py", 'r') as f:
            content = f.read()
        
        if "storage_enabled" in content and "enable_ccidb_storage" in content and "enable_local_disk_storage" in content:
            print("✓ Sign note endpoint validates storage backends")
            passed += 1
        else:
            print("✗ Sign note endpoint missing storage backend validation")
    except Exception as e:
        print(f"✗ Error checking sign note endpoint validation: {e}")
    
    # Test 9: Check sign_note endpoint makes credentials optional
    total += 1
    try:
        with open(PROJECT_ROOT / "app/routes/notes.py", 'r') as f:
            content = f.read()
        
        if "staffid: Optional[int]" in content and "ccitoken: Optional[str]" in content:
            print("✓ Sign note endpoint makes credentials optional")
            passed += 1
        else:
            print("✗ Sign note endpoint doesn't make credentials optional")
    except Exception as e:
        print(f"✗ Error checking sign note endpoint credential validation: {e}")
    
    print(f"\nSign note implementation tests: {passed}/{total} passed")
    return passed == total

def test_sign_note_architecture():
    """Test that sign_note follows the correct architecture patterns."""
    print("\nTesting sign_note architecture...")
    
    passed = 0
    total = 0
    
    # Test 1: Check that sign_note is available in all storage backends
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/storage/local_storage.py", 'r') as f:
            content = f.read()
        
        if "def sign_note" in content:
            print("✓ Local storage has sign_note method")
            passed += 1
        else:
            print("✗ Local storage missing sign_note method")
    except Exception as e:
        print(f"✗ Error checking local storage: {e}")
    
    # Test 2: Check that gitlab storage has sign_note
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/storage/gitlab_storage.py", 'r') as f:
            content = f.read()
        
        if "def sign_note" in content:
            print("✓ GitLab storage has sign_note method")
            passed += 1
        else:
            print("✗ GitLab storage missing sign_note method")
    except Exception as e:
        print(f"✗ Error checking gitlab storage: {e}")
    
    # Test 3: Check that sign_note endpoint uses manager pattern
    total += 1
    try:
        with open(PROJECT_ROOT / "app/routes/notes.py", 'r') as f:
            content = f.read()
        
        if "get_notes_manager().sign_note" in content:
            print("✓ Sign note endpoint uses manager pattern")
            passed += 1
        else:
            print("✗ Sign note endpoint doesn't use manager pattern")
    except Exception as e:
        print(f"✗ Error checking manager pattern: {e}")
    
    # Test 4: Check that sign_note method has proper error handling
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/managers/notes_manager.py", 'r') as f:
            content = f.read()
        
        if "except Exception as e:" in content and "logger.error" in content:
            print("✓ Sign note method has proper error handling")
            passed += 1
        else:
            print("✗ Sign note method missing proper error handling")
    except Exception as e:
        print(f"✗ Error checking error handling: {e}")
    
    # Test 5: Check that sign_note supports multiple storage backends
    total += 1
    try:
        with open(PROJECT_ROOT / "app/services/managers/notes_manager.py", 'r') as f:
            content = f.read()
        
        if "settings.enable_ccidb_storage" in content and "settings.enable_local_disk_storage" in content and "settings.enable_gitlab_storage" in content:
            print("✓ Sign note supports multiple storage backends")
            passed += 1
        else:
            print("✗ Sign note doesn't support multiple storage backends")
    except Exception as e:
        print(f"✗ Error checking multi-storage support: {e}")
    
    print(f"\nSign note architecture tests: {passed}/{total} passed")
    return passed == total

def main():
    """Run all sign_note tests."""
    print("Running sign_note functionality tests...\n")
    
    tests = [
        ("Sign Note Implementation", test_sign_note_implementation),
        ("Sign Note Architecture", test_sign_note_architecture)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"=== {test_name} ===")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print("=== TEST SUMMARY ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All sign_note tests passed! The functionality is implemented correctly.")
        return 0
    else:
        print("❌ Some sign_note tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 