# Data Server

A FastAPI-based server for managing medical notes, macros, and exam configurations with multiple storage backends.

## Architecture

The application follows a layered architecture with clear separation of concerns:

### Storage Layer
- **Local Storage** (`app/services/storage/local_storage.py`): Handles all local disk operations
  - **Note/Exam Methods**: `save_note`, `get_note`, `get_notes_for_patient`, `save_exam`, `get_exam`, `delete_note`
  - **Macro Methods**: `save_macro(macro_type)`, `get_macro(macro_type)`, `get_macros_for_staff(macro_type)`, `delete_macro(macro_type)`
  - **Template Methods**: `save_template`, `get_template`, `get_templates_for_staff`, `delete_template`
  - **Type-Specific Directories**: Each macro type uses separate local directories to prevent cross-contamination
- **GitLab Storage** (`app/services/storage/gitlab_storage.py`): Manages GitLab repository operations
- **CCIDB Storage** (`app/services/storage/ccidb_storage.py`): Handles CCIDB database operations

### Manager Layer
- **Notes Manager** (`app/services/managers/notes_manager.py`): Orchestrates all note operations across storage backends
- **Note Macros Manager** (`app/services/managers/note_macros_manager.py`): Orchestrates all note macro operations across storage backends
- **Rad Macros Manager** (`app/services/managers/rad_macros_manager.py`): Orchestrates all rad macro operations across storage backends
- **Rad Templates Manager** (`app/services/managers/rad_templates_manager.py`): Orchestrates all rad template operations across storage backends
- **Exam Manager** (`app/services/managers/exam_manager.py`): Orchestrates all exam operations across storage backends

### Service Factory
- **Service Factory** (`app/services/service_factory.py`): Provides singleton instances of all services and managers

### API Layer
- **Notes Routes** (`app/routes/notes.py`): REST API endpoints for note operations
- **Note Macros Routes** (`app/routes/note_macros.py`): REST API endpoints for note macro operations
- **Rad Macros Routes** (`app/routes/rad_macros.py`): REST API endpoints for rad macro operations
- **Rad Templates Routes** (`app/routes/rad_templates.py`): REST API endpoints for rad template operations
- **Exam Routes** (`app/routes/exam.py`): REST API endpoints for exam operations

## Key Features

### Multi-Storage Support
Each data type uses different storage backends:

**Notes and Exams:**
- **CCIDB**: Primary database storage (configurable) - prioritized for read operations when enabled
- **GitLab**: Version control and backup (configurable)
- **Local Disk**: Local file storage (configurable)

**Note Macros, Rad Macros, and Rad Templates:**
- **GitLab**: Version control and backup (configurable)
- **Local Disk**: Local file storage (configurable)
- *Note: Macros and templates do not use CCIDB storage*

### Consistent Architecture
- All managers follow the same pattern for storage operations
- Routes use managers exclusively - no direct storage calls
- CCIDB operations (notes and exams only) require `staffid` and `ccitoken` credentials for write operations
- Graceful fallback between storage backends
- **Storage Priority**: When multiple storage backends are enabled, the system follows this priority order:
  - **Notes**: CCIDB → Local Disk → GitLab
  - **Exams**: CCIDB → Local Disk → GitLab
  - **Note Macros**: Local Disk → GitLab
  - **Rad Macros**: Local Disk → GitLab
  - **Rad Templates**: Local Disk → GitLab

### Configuration
Storage backends can be enabled/disabled via settings:
- `enable_ccidb_storage`: Enable/disable CCIDB storage
- `enable_gitlab_storage`: Enable/disable GitLab storage
- `enable_local_disk_storage`: Enable/disable local disk storage

#### Note Types Configuration
Note types and their signature requirements are configured in `app/config/note_types_config.json`:
```json
{
  "Progress Note": { "nit": 0, "required_signature": true },
  "ED Progress Note": { "nit": 1, "required_signature": true },
  "Discharge Summary": { "nit": 2, "required_signature": false },
  "Consultation Note": { "nit": 3, "required_signature": false },
  "Procedure Note": { "nit": 4, "required_signature": false },
  "Radiology Note": { "nit": 5, "required_signature": true }
}
```

- **nit**: Node Item Type (CCIDB identifier)
- **required_signature**: Whether this note type requires signatures

#### Exam Card Layout Configuration
The exam card layout structure is defined in `app/config/examCardLayout.json`:
- **Purpose**: Defines the structure and fields for physical examination cards
- **Usage**: Used by the exam system to render examination forms
- **Structure**: Contains sections for HEENT, Cardiovascular, Pulmonary, Abdominal, etc.
- **Fallback**: Serves as default layout when CCIDB exam data is not available

### Macros and Templates Support
- The system supports three types of reusable content, each with independent management:
  - **Note Macros**: Used for general note-taking and documentation, managed by `NoteMacrosManager`
  - **Rad Macros**: Used for radiology-specific templates and workflows, managed by `RadMacrosManager`
  - **Rad Templates**: Used for radiology report templates, managed by `RadTemplatesManager`
- Each type is stored and managed separately, with its own API endpoints, storage paths, and manager
- Complete separation ensures no cross-contamination between different macro and template types
- **Template-Specific Storage Methods**: Templates use dedicated storage methods (`save_template`, `get_template`, `delete_template`) separate from macro methods
- **Backward Compatibility**: Existing templates with `macroID` fields are automatically converted to `templateID` for consistency
- **Local Storage Separation**: All macro types use separate local directories to prevent cross-contamination:
  - **Note Macros**: `data/note-macros/{staff_prefix}/`
  - **Rad Macros**: `data/rad-macros/{staff_prefix}/`
  - **Rad Templates**: `data/rad-templates/{staff_prefix}/`

## API Endpoints

### Notes
- `GET /api/patients/{campus}/{pat_id}/notes` - Get all notes for a patient (prioritizes CCIDB when enabled)
- `POST /api/patients/{campus}/{pat_id}/notes` - Create a new note
- `GET /api/patients/{campus}/{pat_id}/notes/{note_id}` - Get a specific note
- `PUT /api/patients/{campus}/{pat_id}/notes/{note_id}` - Update a note
- `DELETE /api/patients/{campus}/{pat_id}/notes/{note_id}` - Delete a note
- `POST /api/patients/{campus}/{pat_id}/notes/{note_id}/sign` - Sign a note (requires staffid and ccitoken)
- `GET /api/patients/{campus}/{pat_id}/notes/{note_id}/versions` - Get note version history
- `GET /api/patients/{campus}/{pat_id}/notes/{note_id}/versions/{commit_hash}` - Get specific version
- `GET /api/patients/{campus}/{pat_id}/notes/{note_id}/compare` - Compare versions

### Note Macros
- `GET /api/staff/{staff_id}/note-macros` - Get all note macros for a staff member
- `POST /api/staff/{staff_id}/note-macros` - Create a new note macro
- `GET /api/staff/{staff_id}/note-macros/{macro_id}` - Get a specific note macro
- `PUT /api/staff/{staff_id}/note-macros/{macro_id}` - Update a note macro
- `DELETE /api/staff/{staff_id}/note-macros/{macro_id}` - Delete a note macro
- `GET /api/staff/{staff_id}/note-macros/{macro_id}/versions` - Get note macro version history
- `GET /api/staff/{staff_id}/note-macros/{macro_id}/versions/{commit_hash}` - Get specific version
- `GET /api/staff/{staff_id}/note-macros/{macro_id}/compare` - Compare versions

### Rad Macros
- `GET /api/staff/{staff_id}/rad-macros` - Get all rad macros for a staff member
- `POST /api/staff/{staff_id}/rad-macros` - Create a new rad macro
- `GET /api/staff/{staff_id}/rad-macros/{macro_id}` - Get a specific rad macro
- `PUT /api/staff/{staff_id}/rad-macros/{macro_id}` - Update a rad macro
- `DELETE /api/staff/{staff_id}/rad-macros/{macro_id}` - Delete a rad macro
- `GET /api/staff/{staff_id}/rad-macros/{macro_id}/versions` - Get rad macro version history
- `GET /api/staff/{staff_id}/rad-macros/{macro_id}/versions/{commit_hash}` - Get specific version
- `GET /api/staff/{staff_id}/rad-macros/{macro_id}/compare` - Compare versions

### Rad Templates
- `GET /api/staff/{staff_id}/rad-templates` - Get all rad templates for a staff member
- `POST /api/staff/{staff_id}/rad-templates` - Create a new rad template
- `GET /api/staff/{staff_id}/rad-templates/{template_id}` - Get a specific rad template
- `PUT /api/staff/{staff_id}/rad-templates/{template_id}` - Update a rad template
- `DELETE /api/staff/{staff_id}/rad-templates/{template_id}` - Delete a rad template
- `GET /api/staff/{staff_id}/rad-templates/{template_id}/versions` - Get rad template version history
- `GET /api/staff/{staff_id}/rad-templates/{template_id}/versions/{commit_hash}` - Get specific version
- `GET /api/staff/{staff_id}/rad-templates/{template_id}/compare` - Compare versions

### Exams
- `GET /api/patients/{campus}/{pat_id}/exam/{visitkey}` - Get exam configuration for a patient
- `PUT /api/patients/{campus}/{pat_id}/exam/{visitkey}` - Update exam configuration for a patient

**Example Request:**
```bash
curl -X PUT "http://localhost:8000/api/patients/NORTH/12345/exam/1" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "ALL": [
        {
          "id": "general_appearance",
          "label": "General Appearance",
          "value": "Patient appears alert and oriented x3",
          "category": "general"
        }
      ],
      "HEENT": [
        {
          "id": "head",
          "label": "Head",
          "value": "Normocephalic, atraumatic",
          "category": "heent"
        }
      ]
    },
    "staffid": 123,
    "ccitoken": "abc123def456"
  }'
```

**Example Response:**
```json
{
  "data": {
    "ALL": [
      {
        "id": "general_appearance",
        "label": "General Appearance",
        "value": "Patient appears alert and oriented x3",
        "category": "general"
      }
    ],
    "HEENT": [
      {
        "id": "head",
        "label": "Head",
        "value": "Normocephalic, atraumatic",
        "category": "heent"
      }
    ]
  },
  "lastModified": "2024-01-15T10:30:00Z"
}
```

### Notes with Signature Information

**Example Note Response with Signatures:**
```json
{
  "patID": "12345",
  "noteID": "67890",
  "type": "Progress Note",
  "title": "Daily Progress Note",
  "content": "Patient is doing well...",
  "creationTime": "2024-01-15T10:00:00Z",
  "modifiedTime": "2024-01-15T10:30:00Z",
  "updatedBy": "Dr. Smith",
  "deleted": false,
  "required_signature": true,
  "signed": true,
  "signatures": [
    {
      "signedBy": 12345,
      "signedByName": "Dr. John Smith",
      "signedAt": "2024-01-15T10:25:00Z",
      "comment": "Reviewed and approved"
    },
    {
      "signedBy": 67890,
      "signedByName": "Dr. Jane Doe",
      "signedAt": "2024-01-15T10:30:00Z",
      "comment": "Final review"
    }
  ],
  "lastSignedAt": "2024-01-15T10:30:00Z",
  "lastSignedBy": "Dr. Jane Doe"
}
```

**Example Sign Note Request:**
```json
{
  "note_type": "Progress Note",
  "comment": "Reviewed and approved",
  "staffid": 12345,
  "ccitoken": "abc123def456"
}
```

## Authentication

For CCIDB operations (notes and exams only), the following credentials are required:
- `staffid`: Staff ID (integer)
- `ccitoken`: CCIToken (string)

These credentials are only required when `enable_ccidb_storage` is enabled and only for note and exam operations. Macro and template operations do not require CCIDB credentials.

## Staff Database Integration

The system integrates with the CCIDB staff database (`staff.db`) to provide full staff names in signature information:

### Staff Database Schema
- **Table**: `t_staff`
- **Key Fields**:
  - `sid`: Staff ID (primary key)
  - `fullname`: Full staff name
  - `area`: Department/area
  - `roles`: Staff roles and permissions

### Signature Name Resolution
- **Automatic Lookup**: When retrieving signatures, the system automatically joins with `staff.db` to get full names
- **Fallback Handling**: If a staff record is not found, "Unknown Staff" is displayed
- **Performance**: Staff database is attached only when needed for signature operations
- **Security**: Staff database is read-only during signature operations

### Benefits
- **User-Friendly**: Users see readable names instead of staff IDs
- **Complete Information**: Both staff ID and full name are available
- **Reliable**: Graceful handling of missing staff records
- **Secure**: No modification of staff database during operations

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export CCSYSDIR=/path/to/ccsys
export GITLAB_TOKEN=your_gitlab_token
export GITLAB_URL=your_gitlab_url
export GITLAB_PROJECT_ID=your_project_id
```

3. Run the server:
```bash
python run.py
```

## Development

### Running Tests
```bash
# Run all tests
python3 tests/run_all_tests.py

# Run individual test files
python3 tests/test_core_functionality.py
python3 tests/test_ccidb_note_types.py
```

### Test Organization
- **Comprehensive Test Suite**: Multiple test files covering different aspects of the system
- **Architecture Tests**: Validate the refactored architecture and patterns
- **Import Tests**: Ensure proper import structure and dependency injection
- **Sign Note Tests**: Validate signature functionality across all storage backends
- **YCQL Tests**: Validate YCQL syntax and query structure
- **CCIDB Note Types Tests**: Validate note type configuration and CCIDB integration

### Project Structure
```
dataServer/
├── app/
│   ├── config/
│   │   ├── settings.py              # Application settings
│   │   ├── note_types_config.json   # Note types and signature configuration
│   │   └── examCardLayout.json      # Exam card layout configuration
│   ├── routes/
│   │   ├── notes.py                 # Notes API endpoints
│   │   ├── note_macros.py           # Note Macros API endpoints
│   │   ├── rad_macros.py            # Rad Macros API endpoints
│   │   ├── rad_templates.py         # Rad Templates API endpoints
│   │   └── exam.py                  # Exam API endpoints
│   ├── services/
│   │   ├── managers/                # Business logic managers
│   │   │   ├── notes_manager.py
│   │   │   ├── note_macros_manager.py
│   │   │   ├── rad_macros_manager.py
│   │   │   ├── rad_templates_manager.py
│   │   │   └── exam_manager.py
│   │   ├── storage/                 # Storage implementations
│   │   │   ├── local_storage.py
│   │   │   ├── gitlab_storage.py
│   │   │   └── ccidb_storage.py
│   │   └── service_factory.py       # Service factory
│   └── utils/
│       ├── logger.py                # Logging configuration
│       └── validators.py            # Input validation
├── tests/                           # Test files
├── logs/                            # Application logs
├── requirements.txt                 # Python dependencies
├── run.py                          # Application entry point
└── README.md                       # This file
```

## Recent Changes

### Note Type Management System Overhaul
- **Architecture Change**: Migrated from dynamic CCIDB-based note type management to config-driven approach
- **New Configuration**: `app/config/note_types_config.json` contains all note type definitions:
  ```json
  {
    "Progress Note": { "nit": 0, "required_signature": true },
    "ED Progress Note": { "nit": 1, "required_signature": true },
    "Discharge Summary": { "nit": 2, "required_signature": false },
    "Consultation Note": { "nit": 3, "required_signature": false },
    "Procedure Note": { "nit": 4, "required_signature": false },
    "Radiology Note": { "nit": 5, "required_signature": true }
  }
  ```
- **Benefits**: 
  - Improved performance (no database lookups for note types)
  - Simplified architecture
  - Easy configuration management
  - Consistent NIT (Node Item Type) assignments
- **Legacy Cleanup**: Removed obsolete YCQL files and CCIDB table:
  - Deleted `HOBJS/ainotes/config/notetypes/` directory
  - Removed `AI Notes Types` table references
  - Cleaned up dynamic NIT lookup logic

### Enhanced Signature System
- **Full Name Display**: `signedBy` and `lastSignedBy` now return full staff names instead of staff IDs
- **Staff Database Integration**: YCQL queries now join with `staff.db` to retrieve `fullname` from `t_staff` table
- **Enhanced Signature Data**: Each signature now includes:
  - `signedBy`: Staff ID (for system operations)
  - `signedByName`: Full staff name (for display)
  - `signedAt`: Timestamp
  - `comment`: Signature comment
- **API Response Enhancement**: Updated `NoteResponse` model with proper `SignatureInfo` typing
- **Fallback Handling**: Shows "Unknown Staff" if staff record not found in database
- **Backward Compatibility**: Maintains staff IDs in detailed signature objects for system operations

### Signature Management Features
- **Required Signature Configuration**: Note types can be configured to require signatures via config file
- **Signature Status Tracking**: Notes return `required_signature`, `signed`, `signatures`, `lastSignedAt`, `lastSignedBy` fields
- **Multi-Signature Support**: System tracks all signatures on a note with timestamps and comments
- **CCIDB Integration**: Signature data is stored in `AI Notes Signature` and `AI Notes ReqSignature` tables
- **API Endpoints**: 
  - `POST /api/patients/{campus}/{pat_id}/notes/{note_id}/sign` - Sign a note
  - Signature info included in note retrieval responses

### Legacy Code Cleanup
- **Removed Obsolete Files**:
  - `HOBJS/ainotes/config/notetypes/init.ycql`
  - `HOBJS/ainotes/config/notetypes/getnit.ycql`
  - `HOBJS/ainotes/config/notetypes/getall.ycql`
  - `HOBJS/ainotes/config/notetypes/getname.ycql`
  - Entire `HOBJS/ainotes/config/notetypes/` directory
- **Updated YCQL Files**: Removed all references to dynamic note type management
- **Python Backend**: Cleaned up obsolete note type lookup methods
- **Documentation**: Updated `CCIDB_DATA_TABLES.md` to reflect new architecture
- **Test Suite**: Removed obsolete tests and updated existing ones

### Configuration File Organization
- **Moved `examCardLayout.json`**: Relocated from `app/services/storage/` to `app/config/`
- **Fixed `note_types_config.json` location**: Moved from incorrect parent directory to proper `app/config/` location
- **Architectural Improvement**: Configuration files now properly organized in dedicated config directory
- **Separation of Concerns**: Storage folder now contains only storage implementations, not configuration data
- **Consistency**: All configuration files (`settings.py`, `note_types_config.json`, `examCardLayout.json`) now in same location

### Storage Priority Update
- **Change**: Updated storage priority for notes, macros, templates, and exams
- **Notes**: CCIDB → Local Disk → GitLab (previously was CCIDB → GitLab → Local Disk)
- **Exams**: CCIDB → Local Disk → GitLab (previously was CCIDB → GitLab → Local Disk)
- **Affected Types**: Notes, Exams, Note Macros, Rad Macros, and Rad Templates
- **New Priority**: 
  - **Notes**: CCIDB → Local Disk → GitLab
  - **Exams**: CCIDB → Local Disk → GitLab
  - **Note Macros**: Local Disk → GitLab (previously was GitLab → Local Disk)
  - **Rad Macros**: Local Disk → GitLab (previously was GitLab → Local Disk)
  - **Rad Templates**: Local Disk → GitLab (previously was GitLab → Local Disk)
- **Benefits**: 
  - Faster access to frequently used data
  - Reduced network latency for local operations
  - Better performance for offline scenarios
  - GitLab still serves as backup and version control
  - CCIDB remains primary for notes (when enabled)
- **Implementation**: Updated all manager `get_note`/`get_macro`/`get_template` methods and route handlers to check storage in the new priority order

### Local Storage Separation Fix
- **Problem Resolved**: Fixed issue where `get_macros` functions were returning all macros for both note and rad types when using local storage
- **Root Cause**: Note macros and rad macros were being stored in the same local directory (`data/macros/{staff_prefix}/`)
- **Solution Implemented**: Updated local storage to use separate directories matching GitLab storage structure:
  - **Note Macros**: `data/note-macros/{staff_prefix}/`
  - **Rad Macros**: `data/rad-macros/{staff_prefix}/`
  - **Rad Templates**: `data/rad-templates/{staff_prefix}/`
- **Methods Updated**: All local storage methods now accept `macro_type` parameter:
  - `_get_macros_path()` - Uses type-specific directories
  - `save_macro()` - Saves to type-specific directories
  - `get_macro()` - Retrieves from type-specific directories
  - `get_macros_for_staff()` - Only returns macros of specified type
  - `delete_macro()` - Deletes from type-specific directories
- **Cross-Contamination Prevention**: Each macro type can only access its own storage directory, ensuring complete isolation
- **Consistency**: Local storage now matches GitLab storage behavior for all macro types

### Manager Separation
- **Note Macros Manager**: Separated from the original macros manager to handle note-specific macros independently
- **Rad Macros Manager**: Dedicated manager for radiology macros with separate storage and versioning
- **Rad Templates Manager**: Independent manager for radiology templates with template-specific operations
- Each manager maintains its own storage paths and version control

### Template-Specific Storage Methods
- **Local Storage**: Added template-specific methods (`save_template`, `get_template`, `get_templates_for_staff`, `delete_template`) separate from macro methods
- **GitLab Storage**: Added `get_files_in_folder` method to support template operations
- **Complete Method Separation**: Templates no longer use any macro-related methods, ensuring full isolation
- **Backward Compatibility**: Automatic conversion of `macroID` to `templateID` for existing data

### API Endpoint Organization
- **Note Macros**: `/api/staff/{staff_id}/note-macros` - General note-taking macros
- **Rad Macros**: `/api/staff/{staff_id}/rad-macros` - Radiology-specific macros
- **Rad Templates**: `/api/staff/{staff_id}/rad-templates` - Radiology report templates
- Each endpoint type has its own route file and manager for complete isolation

### Data Model Consistency
- **Note Macros**: Use `macro_id` field for identification
- **Rad Macros**: Use `macro_id` field for identification
- **Rad Templates**: Use `template_id` field for identification (no macro references)
- Consistent field naming across each type while maintaining separation

## Notes API Usage

### Endpoints

| Endpoint                                      | note_id | note_type | Returns                |
|-----------------------------------------------|---------|-----------|------------------------|
| `/api/patients/{campus}/{pat_id}/notes`       | ❌      | ❌        | All notes              |
| `/api/patients/{campus}/{pat_id}/notes/{note_id}?note_type=...` | ✅      | ✅        | Specific note (by key+type) |

- `note_id` is required as a path parameter to fetch a specific note.
- `note_type` is required as a query parameter when fetching a specific note (so the backend can look up the NIT).
- If `note_id` is present but `note_type` is missing, the API returns a 400 error.

#### Example

- Get all notes for a patient:
  ```http
  GET /api/patients/campus1/12345/notes
  ```
- Get a specific note (must provide both note_id and note_type):
  ```http
  GET /api/patients/campus1/12345/notes/**********?note_type=Progress%20Note
  ```

If you call `/notes/{note_id}` without `note_type`, you will receive:
```json
{
  "detail": "note_type is required to fetch a specific note. Both note_id and note_type must be provided."
}
```
