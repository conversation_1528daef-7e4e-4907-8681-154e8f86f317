{"ALL": [{"ref": "general", "column": "left"}, {"ref": "heent", "column": "left"}, {"ref": "neck", "column": "left"}, {"ref": "cardio", "column": "left"}, {"ref": "pulmonary", "column": "left"}, {"ref": "abdominal", "column": "right"}, {"ref": "genitourinary_anorectal", "column": "right"}, {"ref": "musculoskeletal", "column": "right"}, {"ref": "upper", "column": "right"}, {"ref": "lower", "column": "right"}], "HEENT": [{"id": "heent", "title": "HEENT", "additionalNotes": "", "sections": [{"title": "Head", "rows": [{"type": "checkbox", "label": "Atraumatic", "value": false}, {"type": "checkbox", "label": "Norma<PERSON>phalic", "value": false}]}, {"title": "Ears", "indepth": true, "rows": [{"type": "plus<PERSON>us", "label": "Right TM normal", "value": ""}, {"type": "plus<PERSON>us", "label": "Left TM normal", "value": ""}, {"type": "plus<PERSON>us", "label": "Right canal normal", "value": ""}, {"type": "plus<PERSON>us", "label": "Left canal normal", "value": ""}, {"type": "plus<PERSON>us", "label": "Right external ear normal", "value": ""}, {"type": "plus<PERSON>us", "label": "Left external ear normal", "value": ""}, {"type": "checkbox", "label": "Right impacted cerumen", "value": false}, {"type": "checkbox", "label": "Left impacted cerumen", "value": false}, {"type": "checkbox", "label": "Right TM clear", "value": false, "indepth": true}, {"type": "checkbox", "label": "Left TM clear", "value": false, "indepth": true}, {"type": "checkbox", "label": "Right TM bulging", "value": false, "indepth": true}, {"type": "checkbox", "label": "Left TM bulging", "value": false, "indepth": true}, {"type": "checkbox", "label": "Right hematotympanum", "value": false, "indepth": true}, {"type": "checkbox", "label": "Left hematotympanum", "value": false, "indepth": true}, {"type": "checkbox", "label": "Right TM ruptured ", "value": false, "indepth": true}, {"type": "checkbox", "label": "Left TM ruptured", "value": false, "indepth": true}, {"type": "checkbox", "label": "Right ear drainage", "value": false, "indepth": true}, {"type": "checkbox", "label": "Left ear drainage", "value": false, "indepth": true}]}, {"title": "Eyes", "indepth": true, "rows": [{"type": "checkbox", "label": "PERRLA", "value": false}, {"type": "checkbox", "label": "EOM intact", "value": false}, {"type": "checkbox", "label": "Conjunctive normal", "value": false}, {"type": "checkbox", "label": "Convergence abnormal", "value": false, "indepth": true}, {"type": "checkbox", "label": "Dilatation", "value": false, "indepth": true}, {"type": "checkbox", "label": "Fundi normal", "value": false, "indepth": true}, {"type": "plus<PERSON>us", "label": "Scleral icterus", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Right eye discharge", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Left eye discharge", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Nystag<PERSON>", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Sclera injected", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Right papilledema", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Left papilledema", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Right corneal abrasion", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Left corneal abrasion", "value": "", "indepth": true}]}, {"title": "Nose", "rows": [{"type": "checkbox", "label": "Normal", "value": false}, {"type": "plus<PERSON>us", "label": "Nostril patent bilaterally", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Deviated septum", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Swollen", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Congestion", "value": ""}, {"type": "plus<PERSON>us", "label": "Rhinorrhea", "value": ""}]}, {"title": "Mouth/Throat", "rows": [{"type": "checkbox", "label": "<PERSON>ist", "value": false}, {"type": "checkbox", "label": "Clear", "value": false}, {"type": "checkbox", "label": "Dry", "value": false}, {"type": "checkbox", "label": "Poor dentition", "value": false, "indepth": true}, {"type": "checkbox", "label": "Uvula midline", "value": false, "indepth": true}, {"type": "plus<PERSON>us", "label": "<PERSON><PERSON><PERSON>a", "value": ""}, {"type": "plus<PERSON>us", "label": "Exudate", "value": ""}, {"type": "plus<PERSON>us", "label": "Plaques", "value": "", "indepth": true}]}, {"title": "Tonsils", "rows": [{"type": "checkbox", "label": "Non-enlarged", "value": false}, {"type": "checkbox", "label": "Hypertrophied", "value": false}, {"type": "checkbox", "label": "Kissing", "value": false}]}]}], "Neck": [{"id": "neck", "title": "Neck", "additionalNotes": "", "sections": [{"title": "", "rows": [{"type": "checkbox", "label": "ROM normal", "value": false}, {"type": "checkbox", "label": "Supple", "value": false}, {"type": "plus<PERSON>us", "label": "Neck rigidity", "value": ""}, {"type": "plus<PERSON>us", "label": "Tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Carotid bruit", "value": ""}, {"type": "plus<PERSON>us", "label": "Cervical adenopathy", "value": ""}, {"type": "plus<PERSON>us", "label": "Trapezius tender", "value": ""}]}]}], "Cardio": [{"id": "cardio", "title": "Cardiovascular", "additionalNotes": "", "sections": [{"title": "Rate", "rows": [{"type": "checkbox", "label": "Normal", "value": false}, {"type": "checkbox", "label": "Ta<PERSON>cardia", "value": false}, {"type": "plus<PERSON>us", "label": "Bradycardia", "value": ""}]}, {"title": "Rhythm", "rows": [{"type": "checkbox", "label": "Regular", "value": false}, {"type": "checkbox", "label": "Irregular", "value": false}]}, {"title": "Pulses and Heart Sounds", "rows": [{"type": "checkbox", "label": "Pulses normal", "value": false}, {"type": "checkbox", "label": "Heart sounds normal", "value": false}]}, {"title": "", "rows": [{"type": "plus<PERSON>us", "label": "<PERSON><PERSON><PERSON>", "value": ""}, {"type": "plus<PERSON>us", "label": "Friction rub", "value": ""}, {"type": "plus<PERSON>us", "label": "Gallop", "value": ""}]}]}], "PULM": [{"id": "pulmonary", "title": "Pulmonary", "additionalNotes": "", "sections": [{"title": "", "rows": [{"type": "checkbox", "label": "Effort normal", "value": false}, {"type": "checkbox", "label": "Breath sounds normal", "value": false}, {"type": "plus<PERSON>us", "label": "Respiratory distress", "value": ""}, {"type": "plus<PERSON>us", "label": "Chest tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "<PERSON><PERSON><PERSON>", "value": ""}, {"type": "plus<PERSON>us", "label": "Wheezes", "value": ""}, {"type": "multiCheckbox", "label": "Inspiratory", "showWhen": {"field": "Wheezes", "value": "+"}, "checkboxes": [{"label": "RUL", "value": false}, {"label": "RML", "value": false}, {"label": "RLL", "value": false}, {"label": "LUL", "value": false}, {"label": "LLL", "value": false}]}, {"type": "multiCheckbox", "label": "Expiratory", "showWhen": {"field": "Wheezes", "value": "+"}, "checkboxes": [{"label": "RUL", "value": false}, {"label": "RML", "value": false}, {"label": "RLL", "value": false}, {"label": "LUL", "value": false}, {"label": "LLL", "value": false}]}, {"type": "plus<PERSON>us", "label": "<PERSON><PERSON>", "value": ""}, {"type": "multiCheckbox", "label": "", "showWhen": {"field": "<PERSON><PERSON>", "value": "+"}, "checkboxes": [{"label": "RUL", "value": false}, {"label": "RML", "value": false}, {"label": "RLL", "value": false}, {"label": "LUL", "value": false}, {"label": "LLL", "value": false}]}, {"type": "plus<PERSON>us", "label": "<PERSON><PERSON><PERSON>", "value": ""}, {"type": "multiCheckbox", "label": "", "showWhen": {"field": "<PERSON><PERSON><PERSON>", "value": "+"}, "checkboxes": [{"label": "RUL", "value": false}, {"label": "RML", "value": false}, {"label": "RLL", "value": false}, {"label": "LUL", "value": false}, {"label": "LLL", "value": false}]}]}]}], "Abd": [{"id": "abdominal", "title": "Abdominal", "additionalNotes": "", "sections": [{"title": "", "rows": [{"type": "checkbox", "label": "Flat", "value": false}, {"type": "checkbox", "label": "Soft", "value": false}, {"type": "checkbox", "label": "Bowel sounds normal", "value": false}, {"type": "checkbox", "label": "BS hypo", "value": "", "indepth": true}, {"type": "checkbox", "label": "BS hyper", "value": "", "indepth": true}, {"type": "plus<PERSON>us", "label": "Distension", "value": ""}, {"type": "plus<PERSON>us", "label": "Mass", "value": ""}, {"type": "plus<PERSON>us", "label": "Tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Guarding", "value": ""}, {"type": "plus<PERSON>us", "label": "Hernia", "value": ""}, {"type": "plus<PERSON>us", "label": "Rebound", "value": ""}, {"type": "plus<PERSON>us", "label": "Left CVA tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right CVA tenderness", "value": ""}]}, {"title": "Location", "indepth": true, "rows": [{"type": "checkbox", "label": "Diffuse", "value": false}, {"type": "checkbox", "label": "Focal", "value": false}, {"type": "checkbox", "label": "RUQ", "value": false}, {"type": "checkbox", "label": "LUQ", "value": false}, {"type": "checkbox", "label": "RLQ", "value": false}, {"type": "checkbox", "label": "LLQ", "value": false}, {"type": "checkbox", "label": "Epigastric", "value": false}, {"type": "checkbox", "label": "Umbilical", "value": false}, {"type": "checkbox", "label": "Suprapubic", "value": false}, {"type": "plus<PERSON>us", "label": "Bruit", "value": ""}, {"type": "plus<PERSON>us", "label": "<PERSON> sign", "value": ""}, {"type": "plus<PERSON>us", "label": "Umbilical hernia", "value": ""}, {"type": "multiCheckbox", "label": "", "showWhen": {"field": "Umbilical hernia", "value": "+"}, "checkboxes": [{"label": "Incarcerrated", "value": false}, {"label": "Reduceable", "value": false}]}, {"type": "plus<PERSON>us", "label": "Left inguinal hernia", "value": ""}, {"type": "multiCheckbox", "label": "", "showWhen": {"field": "Left inguinal hernia", "value": "+"}, "checkboxes": [{"label": "Incarcerrated", "value": false}, {"label": "Reduceable", "value": false}]}, {"type": "plus<PERSON>us", "label": "Right inguinal hernia", "value": ""}, {"type": "multiCheckbox", "label": "", "showWhen": {"field": "Right inguinal hernia", "value": "+"}, "checkboxes": [{"label": "Incarcerrated", "value": false}, {"label": "Reduceable", "value": false}]}, {"type": "plus<PERSON>us", "label": "Bilateral inguinal hernia", "value": ""}, {"type": "multiCheckbox", "label": "", "showWhen": {"field": "Bilateral inguinal hernia", "value": "+"}, "checkboxes": [{"label": "Incarcerrated", "value": false}, {"label": "Reduceable", "value": false}]}]}]}], "GU/AR": [{"id": "genitourinary_anorectal", "title": "Genitourinary/Anorectal", "additionalNotes": "", "genderSpecific": true, "male": {"sections": [{"title": "<PERSON><PERSON>", "rows": [{"type": "plus<PERSON>us", "label": "Circumcised", "value": ""}, {"type": "plus<PERSON>us", "label": "Warts", "value": ""}, {"type": "plus<PERSON>us", "label": "Discharge", "value": ""}, {"type": "checkbox", "label": "Lesions", "value": false}]}, {"title": "Testes", "rows": [{"type": "checkbox", "label": "Left cremasteric reflex absent", "value": false}, {"type": "checkbox", "label": "Right cremasteric reflex absent", "value": false}, {"type": "plus<PERSON>us", "label": "Left undescended", "value": ""}, {"type": "plus<PERSON>us", "label": "Right undescended", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Left hydrocele", "value": ""}, {"type": "plus<PERSON>us", "label": "Right hydrocele", "value": ""}, {"type": "plus<PERSON>us", "label": "Left mass", "value": ""}, {"type": "plus<PERSON>us", "label": "Right mass", "value": ""}]}, {"title": "Prostate", "rows": [{"type": "plus<PERSON>us", "label": "Enlarged", "value": ""}, {"type": "plus<PERSON>us", "label": "Mass", "value": ""}]}, {"title": "Rectal", "rows": [{"type": "checkbox", "label": "Rectum normal", "value": false}, {"type": "checkbox", "label": "Sphincter tone normal", "value": false}, {"type": "plus<PERSON>us", "label": "Guac result", "value": ""}, {"type": "plus<PERSON>us", "label": "Hemorrhoids", "value": ""}, {"type": "plus<PERSON>us", "label": "Fissure", "value": ""}]}]}, "female": {"sections": [{"title": "", "rows": [{"type": "checkbox", "label": "Vulva", "value": false}, {"type": "checkbox", "label": "Adnexa normal", "value": false}, {"type": "plus<PERSON>us", "label": "Vaginal discharge", "value": ""}, {"type": "plus<PERSON>us", "label": "Vaginal moist", "value": ""}, {"type": "plus<PERSON>us", "label": "Vaginal lesions", "value": ""}, {"type": "plus<PERSON>us", "label": "Vaginal bleeding", "value": ""}, {"type": "plus<PERSON>us", "label": "Atrophic", "value": ""}]}, {"title": "Cervix", "rows": [{"type": "plus<PERSON>us", "label": "Tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Bleeding", "value": ""}, {"type": "plus<PERSON>us", "label": "Lesion", "value": ""}]}, {"title": "Rectal", "rows": [{"type": "checkbox", "label": "Rectum normal", "value": false}, {"type": "checkbox", "label": "Sphincter tone normal", "value": false}, {"type": "plus<PERSON>us", "label": "Guac result", "value": ""}, {"type": "plus<PERSON>us", "label": "Hemorrhoids", "value": ""}, {"type": "plus<PERSON>us", "label": "Fissure", "value": ""}]}]}}], "Musc": [{"id": "musculoskeletal", "title": "Musculoskeletal", "additionalNotes": "", "sections": [{"title": "", "rows": [{"type": "checkbox", "label": "ROM normal", "value": false}, {"type": "plus<PERSON>us", "label": "Swelling", "value": ""}, {"type": "plus<PERSON>us", "label": "Tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Injury", "value": ""}, {"type": "plus<PERSON>us", "label": "RLE edema", "value": ""}, {"type": "plus<PERSON>us", "label": "LLE edema", "value": ""}]}, {"title": "", "rows": [{"type": "multiCheckbox", "label": "Pitting", "checkboxes": [{"label": "1", "value": false}, {"label": "2", "value": false}, {"label": "3", "value": false}]}, {"type": "checkbox", "label": "Non-pitting", "value": false}]}]}], "UPPER": [{"id": "upper", "title": "Upper Extremity", "additionalNotes": "", "sections": [{"title": "Shoulders", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Left abduction", "value": ""}, {"type": "plus<PERSON>us", "label": "Right abduction", "value": ""}]}, {"title": "Elbows", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}]}, {"title": "Forearms", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}]}, {"title": "Wrists", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Left abduction pulses", "value": ""}, {"type": "plus<PERSON>us", "label": "Right abduction pulses", "value": ""}, {"type": "plus<PERSON>us", "label": "Left Tinel's", "value": ""}, {"type": "plus<PERSON>us", "label": "Right Tinel's", "value": ""}, {"type": "plus<PERSON>us", "label": "Left Phalen's", "value": ""}, {"type": "plus<PERSON>us", "label": "Right Phalen's", "value": ""}, {"type": "plus<PERSON>us", "label": "<PERSON>", "value": ""}, {"type": "plus<PERSON>us", "label": "Right <PERSON><PERSON>", "value": ""}]}, {"title": "Fingers", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}]}]}], "LOWER": [{"id": "lower", "title": "Lower Extremity", "additionalNotes": "", "sections": [{"title": "Hips", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Left abduction", "value": ""}, {"type": "plus<PERSON>us", "label": "Right abduction", "value": ""}]}, {"title": "Knees", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Left McMurray", "value": ""}, {"type": "plus<PERSON>us", "label": "Right <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": ""}, {"type": "plus<PERSON>us", "label": "Left crepitus", "value": ""}, {"type": "plus<PERSON>us", "label": "Right crepitus", "value": ""}, {"type": "plus<PERSON>us", "label": "Left swelling", "value": ""}, {"type": "plus<PERSON>us", "label": "Right swelling", "value": ""}]}, {"title": "<PERSON><PERSON>", "rows": [{"type": "checkbox", "label": "Full ROM", "value": false}, {"type": "plus<PERSON>us", "label": "Left limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Right limited ROM", "value": ""}, {"type": "plus<PERSON>us", "label": "Left deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Right deformity", "value": ""}, {"type": "plus<PERSON>us", "label": "Left tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Right tenderness", "value": ""}, {"type": "plus<PERSON>us", "label": "Left Achilles injury", "value": ""}, {"type": "plus<PERSON>us", "label": "Right Achilles injury", "value": ""}]}]}], "cards": {"general": {"id": "general", "title": "General", "additionalNotes": "", "sections": [{"title": "", "rows": [{"type": "checkbox", "label": "<PERSON><PERSON>", "value": false}, {"type": "checkbox", "label": "Normal appearance", "value": false}, {"type": "checkbox", "label": "Normal weight", "value": false}, {"type": "checkbox", "label": "Obese", "value": false}]}, {"title": "", "rows": [{"type": "plus<PERSON>us", "label": "Acute Distress", "value": ""}, {"type": "plus<PERSON>us", "label": "Toxic Appearing", "value": ""}, {"type": "plus<PERSON>us", "label": "Ill-appearing", "value": ""}, {"type": "plus<PERSON>us", "label": "Diaphoretic", "value": ""}]}]}}}