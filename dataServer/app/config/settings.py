import os
from typing import Optional
from app.utils.logger import logger


class Settings:
    """Application settings and configuration."""

    def __init__(self):
        # Storage configuration
        self.enable_gitlab_storage = self._get_bool_env("ENABLE_GITLAB_STORAGE", True)
        self.enable_local_disk_storage = self._get_bool_env(
            "ENABLE_LOCAL_DISK_STORAGE", True
        )
        self.enable_ccidb_storage = self._get_bool_env(
            "ENABLE_CCIDB_STORAGE", True
        )

        # GitLab configuration
        self.gitlab_project_id = os.getenv("GITLAB_PROJECT_ID")
        self.gitlab_private_token = os.getenv("GITLAB_PRIVATE_TOKEN")
        self.gitlab_url = os.getenv(
            "GITLAB_URL", "https://ycql1.clinicomp.com:80/gitlab"
        )

        # Data path configuration
        self.data_path = os.getenv("DATA_PATH", "./data")

        # Log configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = os.getenv("LOG_FILE", "./logs/dataServer.log")

        # Log the configuration
        self._log_configuration()

    def _get_bool_env(self, key: str, default: bool) -> bool:
        """Get boolean value from environment variable."""
        value = os.getenv(key, str(default)).lower()
        return value in ("true", "1", "yes", "on")

    def _log_configuration(self):
        """Log the current configuration."""
        logger.info("=== Application Configuration ===")
        logger.info(
            f"GitLab Storage: {'ENABLED' if self.enable_gitlab_storage else 'DISABLED'}"
        )
        logger.info(
            f"Local Disk Storage: {'ENABLED' if self.enable_local_disk_storage else 'DISABLED'}"
        )
        logger.info(
            f"CCIDB Storage: {'ENABLED' if self.enable_ccidb_storage else 'DISABLED'}"
        )
        logger.info(f"Data Path: {self.data_path}")
        logger.info(f"Log Level: {self.log_level}")
        logger.info(f"Log File: {self.log_file}")

        if self.enable_gitlab_storage:
            if self.gitlab_project_id and self.gitlab_private_token:
                logger.info("GitLab credentials: CONFIGURED")
            else:
                logger.warning("GitLab storage enabled but credentials not configured")
        logger.info("==================================")


# Create global settings instance
settings = Settings()
