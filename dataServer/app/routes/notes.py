import time
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from app.utils.logger import logger
from app.utils.validators import validate_pat_id
from app.services.service_factory import get_notes_manager
from app.config.settings import settings
from datetime import datetime
from app.services.storage.ccidb_storage import NOTE_TYPES_CONFIG

notes_router = APIRouter()

# Pydantic models for request/response validation
class NoteCreate(BaseModel):
    type: str
    title: str
    content: str
    updatedBy: Optional[str] = ""
    staffid: Optional[int] = None
    ccitoken: Optional[str] = None
    required_signature: Optional[bool] = False


class NoteUpdate(BaseModel):
    type: Optional[str] = None
    title: Optional[str] = None
    content: Optional[str] = None
    updatedBy: Optional[str] = None


class SignatureInfo(BaseModel):
    signedBy: Optional[int] = None
    signedByName: Optional[str] = None
    signedAt: Optional[str] = None
    comment: Optional[str] = ""

class NoteResponse(BaseModel):
    patID: str
    noteID: str
    type: str
    title: str
    content: str
    creationTime: str
    modifiedTime: str
    updatedBy: Optional[str] = ""
    deleted: Optional[bool] = False
    required_signature: Optional[bool] = False
    signed: Optional[bool] = False
    signatures: Optional[List[SignatureInfo]] = None
    lastSignedAt: Optional[str] = None
    lastSignedBy: Optional[str] = None


class NoteVersion(BaseModel):
    commit_hash: str
    author: str
    date: str
    message: str
    updatedBy: Optional[str] = ""


class CompareResponse(BaseModel):
    diff: str


class SignNoteRequest(BaseModel):
    note_type: str  # Changed from type to note_type
    comment: str
    staffid: Optional[int] = None
    ccitoken: Optional[str] = None


@notes_router.get("/{campus}/{pat_id}/notes", response_model=List[NoteResponse])
async def get_notes(
    campus: str,
    pat_id: str,
    staffid: Optional[int] = None,
    ccitoken: Optional[str] = None
):
    """Get all notes for a patient."""
    logger.info(f"Getting notes for patient {pat_id} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        notes = []
        
        # Try to get from CCIDB first if enabled
        if settings.enable_ccidb_storage and get_notes_manager().ccidb_storage:
            try:
                logger.debug(f"Fetching notes from CCIDB for patient {pat_id}")
                ccidb_notes = await get_notes_manager().ccidb_storage.read_note_from_ccidb(campus, pat_id)
                for note in ccidb_notes:
                    # Ensure updatedBy field is present
                    if "updatedBy" not in note:
                        note["updatedBy"] = ""
                    # Set required_signature
                    type_cfg = NOTE_TYPES_CONFIG.get(note.get("type"), {})
                    note["required_signature"] = type_cfg.get("required_signature", False)
                    # Filter out deleted notes
                    if not note.get("deleted", False):
                        notes.append(note)
                if notes:
                    logger.info(f"Successfully retrieved {len(notes)} notes from CCIDB for patient {pat_id}")
                    return [NoteResponse(**n) for n in notes]
            except Exception as e:
                logger.warning(f"Could not fetch from CCIDB: {e}")

        # Second try local storage if enabled
        if settings.enable_local_disk_storage:
            try:
                logger.debug(f"Fetching notes from local storage for patient {pat_id}")
                local_notes = get_notes_manager().local_storage.get_notes_for_patient(campus, pat_id)
                for note in local_notes:
                    # Ensure updatedBy field is present
                    if "updatedBy" not in note:
                        note["updatedBy"] = ""
                    # Set required_signature
                    type_cfg = NOTE_TYPES_CONFIG.get(note.get("type"), {})
                    note["required_signature"] = type_cfg.get("required_signature", False)
                    # Filter out deleted notes
                    if not note.get("deleted", False):
                        notes.append(note)
                if notes:
                    logger.info(f"Successfully retrieved {len(notes)} notes from local storage for patient {pat_id}")
                    return [NoteResponse(**n) for n in notes]
            except Exception as e:
                logger.warning(f"Could not read local files: {e}")

        # Third try GitLab if enabled and service is available
        if get_notes_manager().gitlab_service:
            try:
                last_digit = pat_id[-1]
                path = f"data/notes/{campus}/vol{last_digit}/p{pat_id}"
                logger.debug(f"Fetching files from GitLab path: {path}")
                files = get_notes_manager().gitlab_service.project.repository_tree(
                    path=path, ref="main", get_all=True
                )

                for file in files:
                    if file["name"].endswith(".json"):
                        note_id = file["name"].replace(".json", "")
                        note = await get_notes_manager().get_note(campus, pat_id, note_id, staffid=staffid, ccitoken=ccitoken)
                        if note:
                            # Ensure updatedBy field is present
                            if "updatedBy" not in note:
                                note["updatedBy"] = ""
                            # Set required_signature
                            type_cfg = NOTE_TYPES_CONFIG.get(note.get("type"), {})
                            note["required_signature"] = type_cfg.get("required_signature", False)
                            # Filter out deleted notes
                            if not note.get("deleted", False):
                                notes.append(note)
                if notes:
                    logger.info(f"Successfully retrieved {len(notes)} notes from GitLab for patient {pat_id}")
                    return [NoteResponse(**n) for n in notes]
            except Exception as e:
                logger.warning(f"Could not fetch from GitLab: {e}")

        logger.info(f"Successfully retrieved {len(notes)} notes for patient {pat_id}")
        return [NoteResponse(**n) for n in notes]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error getting notes for patient {pat_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))


@notes_router.post(
    "/{campus}/{pat_id}/notes", response_model=NoteResponse, status_code=201
)
async def create_note(campus: str, pat_id: str, note_data: NoteCreate):
    """Create a new note for a patient."""
    logger.info(f"Creating new note for patient {pat_id} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        logger.debug(f"Received note data: {note_data.dict()}")

        # Validate that credentials are provided for CCIDB operations
        if settings.enable_ccidb_storage:
            if note_data.staffid is None or note_data.ccitoken is None:
                logger.error("staffid and ccitoken are required for CCIDB operations")
                raise HTTPException(
                    status_code=400,
                    detail="staffid and ccitoken are required for CCIDB operations",
                )

        # Create note object (exclude credentials)
        note_id = str(int(time.time()))  # Use timestamp as note_id/key
        note = {
            "patID": pat_id,
            "noteID": note_id,
            "type": note_data.type,
            "title": note_data.title,
            "content": note_data.content,
            "creationTime": datetime.utcnow().isoformat(),
            "modifiedTime": datetime.utcnow().isoformat(),
            "updatedBy": note_data.updatedBy,
        }

        # Save note using manager (handles all storage backends)
        success = await get_notes_manager().save_note(
            campus, 
            pat_id, 
            note_id, 
            note,
            update=False,
            staffid=note_data.staffid,
            ccitoken=note_data.ccitoken
        )
        
        if success:
            logger.info(f"Successfully created note {note_id} for patient {pat_id}")
            return note
        else:
            logger.error(f"Failed to save note {note_id} for patient {pat_id}")
            raise HTTPException(status_code=500, detail="Failed to save note")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error creating note for patient {pat_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))


@notes_router.get("/{campus}/{pat_id}/notes/{note_id}", response_model=NoteResponse)
async def get_note(
    campus: str,
    pat_id: str,
    note_id: str,
    note_type: Optional[str] = None,  # note_type must be provided as a query parameter
    staffid: Optional[int] = None,
    ccitoken: Optional[str] = None,
):
    """Get a specific note by note_id and note_type."""
    logger.info(f"Getting note {note_id} for patient {pat_id} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        note = await get_notes_manager().get_note(campus, pat_id, note_id, staffid, ccitoken, note_type)

        if note:
            # Ensure updatedBy field is present
            if "updatedBy" not in note:
                note["updatedBy"] = ""
            # Attach required_signature and signature info
            type_cfg = NOTE_TYPES_CONFIG.get(note["type"], {})
            note["required_signature"] = type_cfg.get("required_signature", False)
            if note["required_signature"]:
                # Attach signature info if not present
                if settings.enable_ccidb_storage and get_notes_manager().ccidb_storage:
                    # Fetch signature info from CCIDB if not present
                    if "signatures" not in note:
                        pass  # Placeholder for actual fetch
                note.setdefault("signed", False)
                note.setdefault("signatures", [])
                note.setdefault("lastSignedAt", None)
                note.setdefault("lastSignedBy", None)
            logger.info(f"Successfully retrieved note {note_id}")
            return NoteResponse(**note)
        logger.warning(f"Note {note_id} not found for patient {pat_id}")
        raise HTTPException(status_code=404, detail="Note not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting note {note_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@notes_router.put("/{campus}/{pat_id}/notes/{note_id}", response_model=NoteResponse)
async def update_note(
    campus: str, 
    pat_id: str, 
    note_id: str, 
    note_data: NoteUpdate,
    note_type: Optional[str] = None,
    staffid: Optional[int] = None,
    ccitoken: Optional[str] = None
):
    """Update a note (creates new version)."""
    logger.info(f"Updating note {note_id} for patient {pat_id} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        logger.debug(f"Received update data: {note_data.dict()}")

        # Validate that credentials are provided for CCIDB operations
        if settings.enable_ccidb_storage:
            if staffid is None or ccitoken is None:
                logger.error("staffid and ccitoken are required for CCIDB operations")
                raise HTTPException(
                    status_code=400,
                    detail="staffid and ccitoken are required for CCIDB operations",
                )

        # Get current note - use note_type if provided, otherwise try to get from note_data
        note_type_to_use = note_type or note_data.type
        current_note = await get_notes_manager().get_note(campus, pat_id, note_id, staffid, ccitoken, note_type_to_use)
        if not current_note:
            logger.warning(f"Note {note_id} not found for patient {pat_id}")
            raise HTTPException(status_code=404, detail="Note not found")

        # Update note
        update_data = note_data.dict(exclude_unset=True)
        if update_data:
            current_note.update(update_data)
            current_note["modifiedTime"] = datetime.utcnow().isoformat()

        # Save updated note using manager (handles all storage backends)
        success = await get_notes_manager().save_note(
            campus, 
            pat_id, 
            note_id, 
            current_note,
            update=True,
            staffid=staffid,
            ccitoken=ccitoken
        )
        
        if success:
            logger.info(f"Successfully updated note {note_id}")
            return current_note
        else:
            logger.error(f"Failed to update note {note_id}")
            raise HTTPException(status_code=500, detail="Failed to update note")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating note {note_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@notes_router.get(
    "/{campus}/{pat_id}/notes/{note_id}/versions", response_model=List[NoteVersion]
)
async def get_note_history(campus: str, pat_id: str, note_id: str):
    """Get version history of a note."""
    logger.info(f"Getting version history for note {note_id} of patient {pat_id}")

    history = get_notes_manager().get_note_history(campus, pat_id, note_id)
    logger.info(f"Retrieved {len(history)} versions for note {note_id}")
    return history


@notes_router.get(
    "/{campus}/{pat_id}/notes/{note_id}/versions/{commit_hash}",
    response_model=NoteResponse,
)
async def get_note_version(campus: str, pat_id: str, note_id: str, commit_hash: str):
    """Get a specific version of a note."""
    logger.info(
        f"Getting version {commit_hash} of note {note_id} for patient {pat_id}"
    )
    version = get_notes_manager().get_note_version(campus, pat_id, note_id, commit_hash)
    if version:
        # Ensure updatedBy field is present
        if "updatedBy" not in version:
            version["updatedBy"] = ""
        logger.info(f"Successfully retrieved version {commit_hash} of note {note_id}")
        return version
    logger.warning(f"Version {commit_hash} of note {note_id} not found")
    raise HTTPException(status_code=404, detail="Version not found")


@notes_router.get(
    "/{campus}/{pat_id}/notes/{note_id}/compare", response_model=CompareResponse
)
async def compare_versions(
    campus: str, pat_id: str, note_id: str, commit1: str, commit2: str
):
    """Compare two versions of a note."""
    logger.debug(f"Comparing commits: {commit1} and {commit2}")

    diff = get_notes_manager().compare_note_versions(campus, pat_id, note_id, commit1, commit2)
    if diff:
        logger.info(f"Successfully compared versions of note {note_id}")
        return CompareResponse(diff=diff)
    logger.warning(f"Could not compare versions of note {note_id}")
    raise HTTPException(status_code=404, detail="Could not compare versions")


@notes_router.delete("/{campus}/{pat_id}/notes/{note_id}")
async def delete_note(
    campus: str, pat_id: str, note_id: str, note_type: Optional[str] = None, staffid: Optional[int] = None, ccitoken: Optional[str] = None
):
    """Delete a note. Requires both note_id and note_type (to determine NIT)."""
    logger.info(f"Deleting note {note_id} for patient {pat_id} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        if note_type is None:
            logger.warning("note_type is required to delete a specific note (for NIT lookup)")
            raise HTTPException(
                status_code=400,
                detail="note_type is required to delete a specific note. Both note_id and note_type must be provided.",
            )

        # Validate that credentials are provided for CCIDB operations
        if settings.enable_ccidb_storage:
            if staffid is None or ccitoken is None:
                logger.error("staffid and ccitoken are required for CCIDB operations")
                raise HTTPException(
                    status_code=400,
                    detail="staffid and ccitoken are required for CCIDB operations",
                )

        # Get current note
        current_note = await get_notes_manager().get_note(campus, pat_id, note_id, note_type=note_type)
        if not current_note:
            logger.warning(f"Note {note_id} not found for patient {pat_id}")
            raise HTTPException(status_code=404, detail="Note not found")

        # Mark as deleted
        current_note["deleted"] = True
        current_note["modifiedTime"] = datetime.utcnow().isoformat()

        # Save deleted note using manager (handles all storage backends)
        success = await get_notes_manager().save_note(
            campus, 
            pat_id, 
            note_id, 
            current_note,
            update=True,
            staffid=staffid,
            ccitoken=ccitoken
        )
        
        if success:
            logger.info(f"Successfully deleted note {note_id}")
            return {"message": "Note deleted successfully"}
        else:
            logger.error(f"Failed to delete note {note_id}")
            raise HTTPException(status_code=500, detail="Failed to delete note")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting note {note_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@notes_router.post("/{campus}/{pat_id}/notes/{note_id}/sign")
async def sign_note(
    campus: str, 
    pat_id: str, 
    note_id: str, 
    sign_data: SignNoteRequest
):
    """Sign a note using available storage backends."""
    logger.info(f"Signing note {note_id} for patient {pat_id} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        logger.debug(f"Received sign data: {sign_data.dict()}")

        # Check if any storage backend is enabled
        storage_enabled = (
            settings.enable_ccidb_storage or 
            settings.enable_local_disk_storage or 
            settings.enable_gitlab_storage
        )
        
        if not storage_enabled:
            logger.error("No storage backends are enabled. Cannot sign note.")
            raise HTTPException(
                status_code=400,
                detail="No storage backends are enabled. Cannot sign note.",
            )

        # Validate credentials for CCIDB if it's enabled
        if settings.enable_ccidb_storage:
            if sign_data.staffid is None or sign_data.ccitoken is None:
                logger.warning("staffid and ccitoken are recommended for CCIDB storage")
            elif not note_id.isdigit():
                logger.warning(f"Note ID {note_id} is not numeric, CCIDB signing may fail")

        # Sign the note using manager (handles all storage backends)
        success = await get_notes_manager().sign_note(
            campus,
            pat_id,
            sign_data.note_type,  # Changed from sign_data.type
            note_id,
            sign_data.comment,
            sign_data.staffid,
            sign_data.ccitoken
        )
        
        if success:
            logger.info(f"Successfully signed note {note_id} for patient {pat_id}")
            return {"message": "Note signed successfully"}
        else:
            logger.error(f"Failed to sign note {note_id} for patient {pat_id}")
            raise HTTPException(status_code=500, detail="Failed to sign note")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error signing note {note_id} for patient {pat_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
