from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.utils.logger import logger
from app.utils.validators import validate_pat_id
from app.services.service_factory import get_rad_templates_manager
from app.config.settings import settings
import json
from datetime import datetime
import uuid

rad_templates_router = APIRouter()

# Pydantic models for request/response validation
class RadTemplateCreate(BaseModel):
    type: str
    name: str
    description: str
    owner: str

class RadTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    owner: Optional[str] = None

class RadTemplateResponse(BaseModel):
    staffID: str
    templateID: str
    type: str
    name: str
    description: str
    owner: str
    creationTime: str
    modifiedTime: str

class RadTemplateVersion(BaseModel):
    commit_hash: str
    owner: str
    date: str
    message: str

class CompareResponse(BaseModel):
    diff: str

@rad_templates_router.get("/{staff_id}/rad-templates", response_model=List[RadTemplateResponse])
async def get_rad_templates(staff_id: str):
    """Get all rad templates for a staff."""
    logger.info(f"Getting rad templates for staff {staff_id}")
    try:
        templates = await get_rad_templates_manager().get_templates_for_staff(staff_id)
        logger.info(f"Successfully retrieved {len(templates)} rad templates for staff {staff_id}")
        return templates
    except Exception as e:
        logger.error(
            f"Error getting rad templates for staff {staff_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))

@rad_templates_router.post("/{staff_id}/rad-templates", response_model=RadTemplateResponse, status_code=201)
async def create_rad_template(staff_id: str, template_data: RadTemplateCreate):
    """Create a new rad template for a staff."""
    logger.info(f"Creating new rad template for staff {staff_id}")
    try:
        logger.debug(f"Received rad template data: {template_data.dict()}")
        template_id = str(uuid.uuid4())
        template = {
            "staffID": staff_id,
            "templateID": template_id,
            "type": template_data.type,
            "name": template_data.name,
            "description": template_data.description,
            "owner": template_data.owner,
            "creationTime": datetime.utcnow().isoformat(),
            "modifiedTime": datetime.utcnow().isoformat(),
        }
        success = await get_rad_templates_manager().save_template(
            staff_id,
            template_id,
            template
        )
        if success:
            logger.info(f"Successfully created rad template {template_id} for staff {staff_id}")
            return template
        else:
            logger.error(f"Failed to save rad template {template_id} for staff {staff_id}")
            raise HTTPException(status_code=500, detail="Failed to save rad template")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error creating rad template for staff {staff_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))

@rad_templates_router.get("/{staff_id}/rad-templates/{template_id}", response_model=RadTemplateResponse)
async def get_rad_template(staff_id: str, template_id: str):
    """Get a specific rad template."""
    logger.info(f"Getting rad template {template_id} for staff {staff_id}")
    template = await get_rad_templates_manager().get_template(staff_id, template_id)
    if template:
        logger.info(f"Successfully retrieved rad template {template_id}")
        return template
    logger.warning(f"Rad template {template_id} not found for staff {staff_id}")
    raise HTTPException(status_code=404, detail="Rad template not found")

@rad_templates_router.put("/{staff_id}/rad-templates/{template_id}", response_model=RadTemplateResponse)
async def update_rad_template(staff_id: str, template_id: str, template_data: RadTemplateUpdate):
    """Update a rad template (creates new version)."""
    logger.info(f"Updating rad template {template_id} for staff {staff_id}")
    try:
        logger.debug(f"Received update data: {template_data.dict()}")
        current_template = await get_rad_templates_manager().get_template(staff_id, template_id)
        if not current_template:
            logger.warning(f"Rad template {template_id} not found for staff {staff_id}")
            raise HTTPException(status_code=404, detail="Rad template not found")
        update_data = template_data.dict(exclude_unset=True)
        if update_data:
            current_template.update(update_data)
            current_template["modifiedTime"] = datetime.utcnow().isoformat()
        success = await get_rad_templates_manager().save_template(
            staff_id,
            template_id,
            current_template
        )
        if success:
            logger.info(f"Successfully updated rad template {template_id}")
            return current_template
        else:
            logger.error(f"Failed to update rad template {template_id}")
            raise HTTPException(status_code=500, detail="Failed to update rad template")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating rad template {template_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@rad_templates_router.get(
    "/{staff_id}/rad-templates/{template_id}/versions", response_model=List[RadTemplateVersion]
)
async def get_rad_template_history(staff_id: str, template_id: str):
    """Get version history of a rad template."""
    logger.info(f"Getting version history for rad template {template_id} of staff {staff_id}")
    history = get_rad_templates_manager().get_template_history(staff_id, template_id)
    logger.info(f"Retrieved {len(history)} versions for rad template {template_id}")
    return history

@rad_templates_router.get(
    "/{staff_id}/rad-templates/{template_id}/versions/{commit_hash}", response_model=RadTemplateResponse
)
async def get_rad_template_version(staff_id: str, template_id: str, commit_hash: str):
    """Get a specific version of a rad template."""
    logger.info(
        f"Getting version {commit_hash} of rad template {template_id} for staff {staff_id}"
    )
    version = get_rad_templates_manager().get_template_version(staff_id, template_id, commit_hash)
    if version:
        logger.info(f"Successfully retrieved version {commit_hash} of rad template {template_id}")
        return version
    logger.warning(f"Version {commit_hash} of rad template {template_id} not found")
    raise HTTPException(status_code=404, detail="Version not found")

@rad_templates_router.get(
    "/{staff_id}/rad-templates/{template_id}/compare", response_model=CompareResponse
)
async def compare_rad_template_versions(staff_id: str, template_id: str, commit1: str, commit2: str):
    """Compare two versions of a rad template."""
    logger.debug(f"Comparing commits: {commit1} and {commit2}")
    diff = get_rad_templates_manager().compare_template_versions(
        staff_id, template_id, commit1, commit2
    )
    if diff:
        logger.info(f"Successfully compared versions of rad template {template_id}")
        return CompareResponse(diff=diff)
    logger.warning(f"Could not compare versions of rad template {template_id}")
    raise HTTPException(status_code=404, detail="Could not compare versions")

@rad_templates_router.delete("/{staff_id}/rad-templates/{template_id}")
async def delete_rad_template(staff_id: str, template_id: str):
    """Delete a rad template."""
    logger.info(f"Deleting rad template {template_id} for staff {staff_id}")
    try:
        current_template = await get_rad_templates_manager().get_template(staff_id, template_id)
        if not current_template:
            logger.warning(f"Rad template {template_id} not found for staff {staff_id}")
            raise HTTPException(status_code=404, detail="Rad template not found")
        
        success = await get_rad_templates_manager().delete_template(staff_id, template_id)
        if success:
            logger.info(f"Successfully deleted rad template {template_id} for staff {staff_id}")
            return {"message": "Rad template deleted successfully"}
        else:
            logger.error(f"Failed to delete rad template {template_id} for staff {staff_id}")
            raise HTTPException(status_code=500, detail="Failed to delete rad template")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting rad template {template_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e)) 