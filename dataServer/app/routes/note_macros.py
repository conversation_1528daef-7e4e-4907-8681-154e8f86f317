from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.utils.logger import logger
from app.utils.validators import validate_pat_id
from app.services.service_factory import get_note_macros_manager
from app.config.settings import settings
import json
from datetime import datetime
import uuid

note_macros_router = APIRouter()


# Pydantic models for request/response validation
class MacroCreate(BaseModel):
    type: str
    name: str
    description: str


class MacroUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None


class MacroResponse(BaseModel):
    staffID: str
    macroID: str
    type: str
    name: str
    description: str
    creationTime: str
    modifiedTime: str


class MacroVersion(BaseModel):
    commit_hash: str
    author: str
    date: str
    message: str


class CompareResponse(BaseModel):
    diff: str


@note_macros_router.get("/{staff_id}/note-macros", response_model=List[MacroResponse])
async def get_macros(staff_id: str):
    """Get all note macros for a staff."""
    logger.info(f"Getting note macros for staff {staff_id}")
    try:
        macros = []
        
        # Try local storage first
        if settings.enable_local_disk_storage:
            try:
                macros = get_note_macros_manager().local_storage.get_macros_for_staff(staff_id, "note")
                logger.debug(f"Retrieved {len(macros)} note macros from local storage")
            except Exception as e:
                logger.warning(f"Could not read local files: {e}")
        
        # If no macros found locally, try GitLab
        if not macros and get_note_macros_manager().gitlab_service:
            try:
                staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                path = f"data/note-macros/{staff_prefix}"
                logger.debug(f"Fetching files from GitLab path: {path}")
                files = get_note_macros_manager().gitlab_service.project.repository_tree(
                    path=path, ref="main"
                )
                for file in files:
                    if file["name"].endswith(".json"):
                        macro_id = file["name"].replace(".json", "")
                        macro = await get_note_macros_manager().get_macro(staff_id, macro_id)
                        if macro:
                            macros.append(macro)
                logger.debug(f"Retrieved {len(macros)} note macros from GitLab")
            except Exception as e:
                logger.warning(f"Could not fetch from GitLab: {e}")
        
        logger.info(f"Successfully retrieved {len(macros)} note macros for staff {staff_id}")
        return macros
    except Exception as e:
        logger.error(
            f"Error getting note macros for staff {staff_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))

@note_macros_router.post("/{staff_id}/note-macros", response_model=MacroResponse, status_code=201)
async def create_macro(staff_id: str, macro_data: MacroCreate):
    """Create a new note macro for a staff."""
    logger.info(f"Creating new note macro for staff {staff_id}")
    try:
        logger.debug(f"Received macro data: {macro_data.dict()}")
        macro_id = str(uuid.uuid4())
        macro = {
            "staffID": staff_id,
            "macroID": macro_id,
            "type": macro_data.type,
            "name": macro_data.name,
            "description": macro_data.description,
            "creationTime": datetime.utcnow().isoformat(),
            "modifiedTime": datetime.utcnow().isoformat(),
        }
        success = await get_note_macros_manager().save_macro(
            staff_id,
            macro_id,
            macro
        )
        if success:
            logger.info(f"Successfully created note macro {macro_id} for staff {staff_id}")
            return macro
        else:
            logger.error(f"Failed to save note macro {macro_id} for staff {staff_id}")
            raise HTTPException(status_code=500, detail="Failed to save note macro")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error creating note macro for staff {staff_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))

@note_macros_router.get("/{staff_id}/note-macros/{macro_id}", response_model=MacroResponse)
async def get_macro(staff_id: str, macro_id: str):
    """Get a specific note macro."""
    logger.info(f"Getting note macro {macro_id} for staff {staff_id}")
    macro = await get_note_macros_manager().get_macro(staff_id, macro_id)
    if macro:
        logger.info(f"Successfully retrieved note macro {macro_id}")
        return macro
    logger.warning(f"Note macro {macro_id} not found for staff {staff_id}")
    raise HTTPException(status_code=404, detail="Note macro not found")

@note_macros_router.put("/{staff_id}/note-macros/{macro_id}", response_model=MacroResponse)
async def update_macro(staff_id: str, macro_id: str, macro_data: MacroUpdate):
    """Update a note macro (creates new version)."""
    logger.info(f"Updating note macro {macro_id} for staff {staff_id}")
    try:
        logger.debug(f"Received update data: {macro_data.dict()}")
        current_macro = await get_note_macros_manager().get_macro(staff_id, macro_id)
        if not current_macro:
            logger.warning(f"Note macro {macro_id} not found for staff {staff_id}")
            raise HTTPException(status_code=404, detail="Note macro not found")
        update_data = macro_data.dict(exclude_unset=True)
        if update_data:
            current_macro.update(update_data)
            current_macro["modifiedTime"] = datetime.utcnow().isoformat()
        success = await get_note_macros_manager().save_macro(
            staff_id,
            macro_id,
            current_macro
        )
        if success:
            logger.info(f"Successfully updated note macro {macro_id}")
            return current_macro
        else:
            logger.error(f"Failed to update note macro {macro_id}")
            raise HTTPException(status_code=500, detail="Failed to update note macro")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating note macro {macro_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@note_macros_router.get(
    "/{staff_id}/note-macros/{macro_id}/versions", response_model=List[MacroVersion]
)
async def get_macro_history(staff_id: str, macro_id: str):
    """Get version history of a note macro."""
    logger.info(f"Getting version history for note macro {macro_id} of staff {staff_id}")
    history = get_note_macros_manager().get_macro_history(staff_id, macro_id)
    logger.info(f"Retrieved {len(history)} versions for note macro {macro_id}")
    return history

@note_macros_router.get(
    "/{staff_id}/note-macros/{macro_id}/versions/{commit_hash}", response_model=MacroResponse
)
async def get_macro_version(staff_id: str, macro_id: str, commit_hash: str):
    """Get a specific version of a note macro."""
    logger.info(
        f"Getting version {commit_hash} of note macro {macro_id} for staff {staff_id}"
    )
    version = get_note_macros_manager().get_macro_version(staff_id, macro_id, commit_hash)
    if version:
        logger.info(f"Successfully retrieved version {commit_hash} of note macro {macro_id}")
        return version
    logger.warning(f"Version {commit_hash} of note macro {macro_id} not found")
    raise HTTPException(status_code=404, detail="Version not found")

@note_macros_router.get(
    "/{staff_id}/note-macros/{macro_id}/compare", response_model=CompareResponse
)
async def compare_versions(staff_id: str, macro_id: str, commit1: str, commit2: str):
    """Compare two versions of a note macro."""
    logger.debug(f"Comparing commits: {commit1} and {commit2}")
    diff = get_note_macros_manager().compare_macro_versions(
        staff_id, macro_id, commit1, commit2
    )
    if diff:
        logger.info(f"Successfully compared versions of note macro {macro_id}")
        return CompareResponse(diff=diff)
    logger.warning(f"Could not compare versions of note macro {macro_id}")
    raise HTTPException(status_code=404, detail="Could not compare versions")

@note_macros_router.delete("/{staff_id}/note-macros/{macro_id}")
async def delete_macro(staff_id: str, macro_id: str):
    """Delete a note macro."""
    logger.info(f"Deleting note macro {macro_id} for staff {staff_id}")
    try:
        current_macro = await get_note_macros_manager().get_macro(staff_id, macro_id)
        if not current_macro:
            logger.warning(f"Note macro {macro_id} not found for staff {staff_id}")
            raise HTTPException(status_code=404, detail="Note macro not found")
        # Delete from GitLab if enabled
        if settings.enable_gitlab_storage and get_note_macros_manager().gitlab_service:
            try:
                staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                gitlab_file_path = f"data/note-macros/{staff_prefix}/{macro_id}.json"
                logger.debug(f"Deleting note macro {macro_id} from GitLab")
                get_note_macros_manager().gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Delete note macro {macro_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "delete",
                                "file_path": gitlab_file_path,
                            }
                        ],
                    }
                )
                logger.info(f"Successfully deleted note macro {macro_id} from GitLab")
            except Exception as e:
                logger.warning(f"Could not delete note macro from GitLab: {e}")
        # Delete from local disk if enabled
        if settings.enable_local_disk_storage:
            try:
                logger.debug(f"Deleting note macro {macro_id} from local disk")
                if get_note_macros_manager().local_storage.delete_macro(staff_id, macro_id, "note"):
                    logger.info(f"Successfully deleted note macro {macro_id} from local disk")
                else:
                    logger.warning(f"Could not delete note macro {macro_id} from local disk")
            except Exception as e:
                logger.warning(f"Could not delete note macro from local disk: {e}")
        logger.info(f"Successfully deleted note macro {macro_id} for staff {staff_id}")
        return {"message": "Note macro deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting note macro {macro_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
