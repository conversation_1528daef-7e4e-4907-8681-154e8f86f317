from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.utils.logger import logger
from app.utils.validators import validate_pat_id
from app.services.service_factory import get_rad_macros_manager
from app.config.settings import settings
import json
from datetime import datetime
import uuid

rad_macros_router = APIRouter()


# Pydantic models for request/response validation
class RadMacroCreate(BaseModel):
    type: str
    name: str
    description: str


class RadMacroUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None


class RadMacroResponse(BaseModel):
    staffID: str
    macroID: str
    type: str
    name: str
    description: str
    creationTime: str
    modifiedTime: str


class RadMacroVersion(BaseModel):
    commit_hash: str
    owner: str
    date: str
    message: str


class CompareResponse(BaseModel):
    diff: str


@rad_macros_router.get("/{staff_id}/rad-macros", response_model=List[RadMacroResponse])
async def get_rad_macros(staff_id: str):
    """Get all rad macros for a staff."""
    logger.info(f"Getting rad macros for staff {staff_id}")
    try:
        macros = []
        
        # Try local storage first
        if settings.enable_local_disk_storage:
            try:
                macros = get_rad_macros_manager().local_storage.get_macros_for_staff(staff_id, "rad")
                logger.debug(f"Retrieved {len(macros)} rad macros from local storage")
            except Exception as e:
                logger.warning(f"Could not read local files: {e}")

        # If no macros found locally, try GitLab
        if not macros and get_rad_macros_manager().gitlab_service:
            try:
                staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                path = f"data/rad-macros/{staff_prefix}"
                logger.debug(f"Fetching files from GitLab path: {path}")
                files = get_rad_macros_manager().gitlab_service.project.repository_tree(
                    path=path, ref="main"
                )

                for file in files:
                    if file["name"].endswith(".json"):
                        macro_id = file["name"].replace(".json", "")
                        macro = await get_rad_macros_manager().get_macro(staff_id, macro_id)
                        if macro:
                            macros.append(macro)
                logger.debug(f"Retrieved {len(macros)} rad macros from GitLab")
            except Exception as e:
                logger.warning(f"Could not fetch from GitLab: {e}")

        logger.info(f"Successfully retrieved {len(macros)} rad macros for staff {staff_id}")
        return macros
    except Exception as e:
        logger.error(
            f"Error getting rad macros for staff {staff_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))


@rad_macros_router.post("/{staff_id}/rad-macros", response_model=RadMacroResponse, status_code=201)
async def create_rad_macro(staff_id: str, macro_data: RadMacroCreate):
    """Create a new rad macro for a staff."""
    logger.info(f"Creating new rad macro for staff {staff_id}")
    try:
        logger.debug(f"Received rad macro data: {macro_data.dict()}")

        # Create macro object
        macro_id = str(uuid.uuid4())
        macro = {
            "staffID": staff_id,
            "macroID": macro_id,
            "type": macro_data.type,
            "name": macro_data.name,
            "description": macro_data.description,
            "creationTime": datetime.utcnow().isoformat(),
            "modifiedTime": datetime.utcnow().isoformat(),
        }

        # Save macro using manager (handles GitLab and local storage)
        success = await get_rad_macros_manager().save_macro(
            staff_id, 
            macro_id, 
            macro
        )
        
        if success:
            logger.info(f"Successfully created rad macro {macro_id} for staff {staff_id}")
            return macro
        else:
            logger.error(f"Failed to save rad macro {macro_id} for staff {staff_id}")
            raise HTTPException(status_code=500, detail="Failed to save rad macro")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error creating rad macro for staff {staff_id}: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=str(e))


@rad_macros_router.get("/{staff_id}/rad-macros/{macro_id}", response_model=RadMacroResponse)
async def get_rad_macro(staff_id: str, macro_id: str):
    """Get a specific rad macro."""
    logger.info(f"Getting rad macro {macro_id} for staff {staff_id}")

    macro = await get_rad_macros_manager().get_macro(staff_id, macro_id)
    if macro:
        logger.info(f"Successfully retrieved rad macro {macro_id}")
        return macro
    logger.warning(f"Rad macro {macro_id} not found for staff {staff_id}")
    raise HTTPException(status_code=404, detail="Rad macro not found")


@rad_macros_router.put("/{staff_id}/rad-macros/{macro_id}", response_model=RadMacroResponse)
async def update_rad_macro(staff_id: str, macro_id: str, macro_data: RadMacroUpdate):
    """Update a rad macro (creates new version)."""
    logger.info(f"Updating rad macro {macro_id} for staff {staff_id}")
    try:
        logger.debug(f"Received update data: {macro_data.dict()}")

        # Get current macro
        current_macro = await get_rad_macros_manager().get_macro(staff_id, macro_id)
        if not current_macro:
            logger.warning(f"Rad macro {macro_id} not found for staff {staff_id}")
            raise HTTPException(status_code=404, detail="Rad macro not found")

        # Update macro
        update_data = macro_data.dict(exclude_unset=True)
        if update_data:
            current_macro.update(update_data)
            current_macro["modifiedTime"] = datetime.utcnow().isoformat()

        # Save updated macro using manager (handles GitLab and local storage)
        success = await get_rad_macros_manager().save_macro(
            staff_id, 
            macro_id, 
            current_macro
        )
        
        if success:
            logger.info(f"Successfully updated rad macro {macro_id}")
            return current_macro
        else:
            logger.error(f"Failed to update rad macro {macro_id}")
            raise HTTPException(status_code=500, detail="Failed to update rad macro")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating rad macro {macro_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@rad_macros_router.get(
    "/{staff_id}/rad-macros/{macro_id}/versions", response_model=List[RadMacroVersion]
)
async def get_rad_macro_history(staff_id: str, macro_id: str):
    """Get version history of a rad macro."""
    logger.info(f"Getting version history for rad macro {macro_id} of staff {staff_id}")

    history = get_rad_macros_manager().get_macro_history(staff_id, macro_id)
    logger.info(f"Retrieved {len(history)} versions for rad macro {macro_id}")
    return history


@rad_macros_router.get(
    "/{staff_id}/rad-macros/{macro_id}/versions/{commit_hash}", response_model=RadMacroResponse
)
async def get_rad_macro_version(staff_id: str, macro_id: str, commit_hash: str):
    """Get a specific version of a rad macro."""
    logger.info(
        f"Getting version {commit_hash} of rad macro {macro_id} for staff {staff_id}"
    )
    version = get_rad_macros_manager().get_macro_version(staff_id, macro_id, commit_hash)
    if version:
        logger.info(f"Successfully retrieved version {commit_hash} of rad macro {macro_id}")
        return version
    logger.warning(f"Version {commit_hash} of rad macro {macro_id} not found")
    raise HTTPException(status_code=404, detail="Version not found")


@rad_macros_router.get(
    "/{staff_id}/rad-macros/{macro_id}/compare", response_model=CompareResponse
)
async def compare_rad_versions(staff_id: str, macro_id: str, commit1: str, commit2: str):
    """Compare two versions of a rad macro."""
    logger.debug(f"Comparing commits: {commit1} and {commit2}")

    diff = get_rad_macros_manager().compare_macro_versions(
        staff_id, macro_id, commit1, commit2
    )
    if diff:
        logger.info(f"Successfully compared versions of rad macro {macro_id}")
        return CompareResponse(diff=diff)
    logger.warning(f"Could not compare versions of rad macro {macro_id}")
    raise HTTPException(status_code=404, detail="Could not compare versions")


@rad_macros_router.delete("/{staff_id}/rad-macros/{macro_id}")
async def delete_rad_macro(staff_id: str, macro_id: str):
    """Delete a rad macro."""
    logger.info(f"Deleting rad macro {macro_id} for staff {staff_id}")
    try:
        # Get current macro
        current_macro = await get_rad_macros_manager().get_macro(staff_id, macro_id)
        if not current_macro:
            logger.warning(f"Rad macro {macro_id} not found for staff {staff_id}")
            raise HTTPException(status_code=404, detail="Rad macro not found")

        # Delete from GitLab if enabled
        if settings.enable_gitlab_storage and get_rad_macros_manager().gitlab_service:
            try:
                staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                gitlab_file_path = f"data/rad-macros/{staff_prefix}/{macro_id}.json"

                logger.debug(f"Deleting rad macro {macro_id} from GitLab")
                get_rad_macros_manager().gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Delete rad macro {macro_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "delete",
                                "file_path": gitlab_file_path,
                            }
                        ],
                    }
                )
                logger.info(f"Successfully deleted rad macro {macro_id} from GitLab")
            except Exception as e:
                logger.warning(f"Could not delete rad macro from GitLab: {e}")

        # Delete from local disk if enabled
        if settings.enable_local_disk_storage:
            try:
                logger.debug(f"Deleting rad macro {macro_id} from local disk")
                if get_rad_macros_manager().local_storage.delete_macro(staff_id, macro_id, "rad"):
                    logger.info(f"Successfully deleted rad macro {macro_id} from local disk")
                else:
                    logger.warning(f"Could not delete rad macro {macro_id} from local disk")
            except Exception as e:
                logger.warning(f"Could not delete rad macro from local disk: {e}")

        logger.info(f"Successfully deleted rad macro {macro_id} for staff {staff_id}")
        return {"message": "Rad macro deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting rad macro {macro_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e)) 