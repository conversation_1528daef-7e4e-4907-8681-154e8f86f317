from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
from app.utils.logger import logger
from app.utils.validators import validate_pat_id
from app.config.settings import settings
from app.services.service_factory import get_exam_manager
from datetime import datetime

exam_router = APIRouter()

# Pydantic model for exam configuration
class ExamUpdate(BaseModel):
    data: Dict[str, Any]
    staffid: Optional[int] = None
    ccitoken: Optional[str] = None

class ExamResponse(BaseModel):
    data: Dict[str, Any]
    lastModified: str

@exam_router.get("/{campus}/{pat_id}/exam/{visitkey}", response_model=ExamResponse)
async def get_exam(campus: str, pat_id: str, visitkey: int):
    """Get the current exam configuration for a patient."""
    logger.info(f"Getting exam configuration for patient {pat_id}, visitkey {visitkey} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        # Get exam from manager (handles CCIDB, GitLab, local storage, and default layout)
        exam_data = await get_exam_manager().get_exam(campus, pat_id, visitkey)
        
        if exam_data is None:
            logger.error(f"Failed to retrieve exam configuration for patient {pat_id}, visitkey {visitkey}")
            raise HTTPException(status_code=500, detail="Failed to retrieve exam configuration")
        
        logger.info(f"Successfully retrieved exam configuration for patient {pat_id}, visitkey {visitkey}")
        
        # Handle legacy double-nested data structure
        exam_data_content = exam_data.get("data", {})
        if isinstance(exam_data_content, dict) and "data" in exam_data_content:
            # This is legacy double-nested data, extract the inner data
            logger.info(f"Found legacy double-nested data structure for patient {pat_id}, extracting inner data")
            exam_data_content = exam_data_content.get("data", {})
        
        return ExamResponse(
            data=exam_data_content,
            lastModified=exam_data.get("lastModified", datetime.utcnow().isoformat())
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exam configuration for patient {pat_id}, visitkey {visitkey}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@exam_router.put("/{campus}/{pat_id}/exam/{visitkey}", response_model=ExamResponse)
async def update_exam(campus: str, pat_id: str, visitkey: int, exam_update: ExamUpdate):
    """Update the exam configuration for a patient."""
    logger.info(f"Updating exam configuration for patient {pat_id}, visitkey {visitkey} in campus {campus}")
    try:
        if not validate_pat_id(pat_id):
            logger.warning(f"Invalid patID format: {pat_id}")
            raise HTTPException(
                status_code=400,
                detail="Invalid patID format. Must be a string ending with a number.",
            )

        logger.debug(f"Received exam update data: {exam_update.dict()}")

        # Validate that credentials are provided for CCIDB operations
        if settings.enable_ccidb_storage:
            if exam_update.staffid is None or exam_update.ccitoken is None:
                logger.error("staffid and ccitoken are required for CCIDB operations")
                raise HTTPException(
                    status_code=400,
                    detail="staffid and ccitoken are required for CCIDB operations",
                )

        # Get current exam configuration
        current_exam = await get_exam_manager().get_exam(campus, pat_id, visitkey)
        if not current_exam:
            # Create new exam configuration if none exists
            current_exam = {"data": {}, "lastModified": datetime.utcnow().isoformat()}

        # Handle legacy double-nested data structure in existing data
        existing_data = current_exam.get("data", {})
        if isinstance(existing_data, dict) and "data" in existing_data:
            # This is legacy double-nested data, extract the inner data
            logger.info(f"Found legacy double-nested data structure in existing data for patient {pat_id}, extracting inner data")
            existing_data = existing_data.get("data", {})

        # Update exam configuration with correct structure
        current_exam["data"] = exam_update.data
        current_exam["lastModified"] = datetime.utcnow().isoformat()

        # Save exam using manager (handles all storage backends)
        success = await get_exam_manager().save_exam(
            campus, 
            pat_id, 
            visitkey,
            current_exam["data"],
            staffid=exam_update.staffid,
            ccitoken=exam_update.ccitoken
        )
        
        if success:
            logger.info(f"Successfully updated exam configuration for patient {pat_id}, visitkey {visitkey}")
            return ExamResponse(
                data=current_exam["data"],
                lastModified=current_exam["lastModified"]
            )
        else:
            logger.error(f"Failed to save exam configuration for patient {pat_id}, visitkey {visitkey}")
            raise HTTPException(status_code=500, detail="Failed to save exam configuration")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating exam configuration for patient {pat_id}, visitkey {visitkey}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

 