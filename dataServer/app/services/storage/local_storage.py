import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
from app.utils.logger import logger
from app.config.settings import settings


class LocalStorage:
    def __init__(self):
        self.data_dir = Path(settings.data_path)

    def _get_notes_path(self, campus: str, pat_id: str, note_id: str) -> Path:
        """Get the path for a patient note."""
        if not pat_id or not pat_id[-1].isdigit():
            raise ValueError("patID must be a string ending with a number")

        last_digit = pat_id[-1]
        return (
            self.data_dir
            / "notes"
            / campus
            / f"vol{last_digit}"
            / f"p{pat_id}"
            / f"{note_id}.json"
        )

    def _get_macros_path(self, staff_id: str, macro_id: str, macro_type: str = "note") -> Path:
        """Get the path for a staff macro."""
        staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
        # Use different paths for different macro types
        if macro_type == "rad":
            return self.data_dir / "rad-macros" / staff_prefix / f"{macro_id}.json"
        elif macro_type == "rad-template":
            return self.data_dir / "rad-templates" / staff_prefix / f"{macro_id}.json"
        else:  # note macros (default)
            return self.data_dir / "note-macros" / staff_prefix / f"{macro_id}.json"

    def _get_templates_path(self, staff_id: str, template_id: str) -> Path:
        """Get the path for a staff template."""
        staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
        return self.data_dir / "templates" / staff_prefix / f"{template_id}.json"

    def _get_exam_path(self, campus: str, pat_id: str, visitkey: int) -> Path:
        """Get the path for a patient's exam configuration."""
        if not pat_id or not pat_id[-1].isdigit():
            raise ValueError("patID must be a string ending with a number")

        last_digit = pat_id[-1]
        return (
            self.data_dir
            / "exams"
            / campus
            / f"vol{last_digit}"
            / f"p{pat_id}"
            / f"{visitkey}.json"
        )

    def save_note(self, campus: str, pat_id: str, note_id: str, content: dict) -> bool:
        """Save note to local disk."""
        try:
            # Create patient directory if it doesn't exist
            last_digit = pat_id[-1]
            patient_dir = (
                self.data_dir / "notes" / campus / f"vol{last_digit}" / f"p{pat_id}"
            )
            patient_dir.mkdir(parents=True, exist_ok=True)

            # Save note to file
            file_path = self._get_notes_path(campus, pat_id, note_id)
            with open(file_path, "w") as f:
                f.write(json.dumps(content, indent=2))

            logger.info(f"Successfully saved note {note_id} to local disk")
            return True
        except Exception as e:
            logger.error(f"Error saving note to local disk: {e}")
            return False

    def get_note(self, campus: str, pat_id: str, note_id: str) -> Optional[dict]:
        """Get note from local disk."""
        try:
            note_path = self._get_notes_path(campus, pat_id, note_id)
            if note_path.exists():
                with open(note_path, "r") as f:
                    return json.loads(f.read())
            return None
        except Exception as e:
            logger.error(f"Error getting note from local disk: {e}")
            return None

    def get_notes_for_patient(self, campus: str, pat_id: str) -> list:
        """Get all notes for a patient from local disk."""
        try:
            last_digit = pat_id[-1]
            patient_dir = (
                self.data_dir / "notes" / campus / f"vol{last_digit}" / f"p{pat_id}"
            )
            notes = []
            
            if patient_dir.exists():
                for file_path in patient_dir.glob("*.json"):
                    note_id = file_path.stem
                    note = self.get_note(campus, pat_id, note_id)
                    if note:
                        notes.append(note)
            
            return notes
        except Exception as e:
            logger.error(f"Error getting notes for patient from local disk: {e}")
            return []

    def save_macro(self, staff_id: str, macro_id: str, content: dict, macro_type: str = "note") -> bool:
        """Save macro to local disk."""
        try:
            # Create staff directory if it doesn't exist
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            # Use different paths for different macro types
            if macro_type == "rad":
                staff_dir = self.data_dir / "rad-macros" / staff_prefix
            elif macro_type == "rad-template":
                staff_dir = self.data_dir / "rad-templates" / staff_prefix
            else:  # note macros (default)
                staff_dir = self.data_dir / "note-macros" / staff_prefix
            staff_dir.mkdir(parents=True, exist_ok=True)

            # Save macro to file
            file_path = self._get_macros_path(staff_id, macro_id, macro_type)
            with open(file_path, "w") as f:
                f.write(json.dumps(content, indent=2))

            logger.info(f"Successfully saved {macro_type} macro {macro_id} to local disk")
            return True
        except Exception as e:
            logger.error(f"Error saving {macro_type} macro to local disk: {e}")
            return False

    def save_template(self, staff_id: str, template_id: str, content: dict) -> bool:
        """Save template to local disk."""
        try:
            # Create staff directory if it doesn't exist
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            staff_dir = self.data_dir / "templates" / staff_prefix
            staff_dir.mkdir(parents=True, exist_ok=True)

            # Save template to file
            file_path = self._get_templates_path(staff_id, template_id)
            with open(file_path, "w") as f:
                f.write(json.dumps(content, indent=2))

            logger.info(f"Successfully saved template {template_id} to local disk")
            return True
        except Exception as e:
            logger.error(f"Error saving template to local disk: {e}")
            return False

    def get_macro(self, staff_id: str, macro_id: str, macro_type: str = "note") -> Optional[dict]:
        """Get macro from local disk."""
        try:
            macro_path = self._get_macros_path(staff_id, macro_id, macro_type)
            if macro_path.exists():
                with open(macro_path, "r") as f:
                    return json.loads(f.read())
            return None
        except Exception as e:
            logger.error(f"Error getting {macro_type} macro from local disk: {e}")
            return None

    def get_template(self, staff_id: str, template_id: str) -> Optional[dict]:
        """Get template from local disk."""
        try:
            template_path = self._get_templates_path(staff_id, template_id)
            if template_path.exists():
                with open(template_path, "r") as f:
                    return json.loads(f.read())
            return None
        except Exception as e:
            logger.error(f"Error getting template from local disk: {e}")
            return None

    def get_macros_for_staff(self, staff_id: str, macro_type: str = "note") -> list:
        """Get all macros for a staff member from local disk."""
        try:
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            # Use different paths for different macro types
            if macro_type == "rad":
                staff_dir = self.data_dir / "rad-macros" / staff_prefix
            elif macro_type == "rad-template":
                staff_dir = self.data_dir / "rad-templates" / staff_prefix
            else:  # note macros (default)
                staff_dir = self.data_dir / "note-macros" / staff_prefix
            macros = []
            
            if staff_dir.exists():
                for file_path in staff_dir.glob("*.json"):
                    macro_id = file_path.stem
                    macro = self.get_macro(staff_id, macro_id, macro_type)
                    if macro:
                        macros.append(macro)
            
            return macros
        except Exception as e:
            logger.error(f"Error getting {macro_type} macros for staff from local disk: {e}")
            return []

    def get_templates_for_staff(self, staff_id: str) -> list:
        """Get all templates for a staff member from local disk."""
        try:
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            staff_dir = self.data_dir / "templates" / staff_prefix
            templates = []
            
            if staff_dir.exists():
                for file_path in staff_dir.glob("*.json"):
                    template_id = file_path.stem
                    template = self.get_template(staff_id, template_id)
                    if template:
                        templates.append(template)
            
            return templates
        except Exception as e:
            logger.error(f"Error getting templates for staff from local disk: {e}")
            return []

    def save_exam(self, campus: str, pat_id: str, visitkey: int, content: dict) -> bool:
        """Save exam to local disk."""
        try:
            # Create patient directory if it doesn't exist
            last_digit = pat_id[-1]
            patient_dir = (
                self.data_dir / "exams" / campus / f"vol{last_digit}" / f"p{pat_id}"
            )
            patient_dir.mkdir(parents=True, exist_ok=True)

            # Save exam to file
            file_path = self._get_exam_path(campus, pat_id, visitkey)
            with open(file_path, "w") as f:
                f.write(json.dumps(content, indent=2))

            logger.info(f"Successfully saved exam for patient {pat_id}, visitkey {visitkey} to local disk")
            return True
        except Exception as e:
            logger.error(f"Error saving exam to local disk: {e}")
            return False

    def get_exam(self, campus: str, pat_id: str, visitkey: int) -> Optional[dict]:
        """Get exam from local disk."""
        try:
            exam_path = self._get_exam_path(campus, pat_id, visitkey)
            if exam_path.exists():
                with open(exam_path, "r") as f:
                    return json.loads(f.read())
            return None
        except Exception as e:
            logger.error(f"Error getting exam from local disk: {e}")
            return None

    def delete_note(self, campus: str, pat_id: str, note_id: str) -> bool:
        """Delete note from local disk."""
        try:
            note_path = self._get_notes_path(campus, pat_id, note_id)
            if note_path.exists():
                note_path.unlink()
                logger.info(f"Successfully deleted note {note_id} from local disk")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting note from local disk: {e}")
            return False

    def delete_macro(self, staff_id: str, macro_id: str, macro_type: str = "note") -> bool:
        """Delete macro from local disk."""
        try:
            macro_path = self._get_macros_path(staff_id, macro_id, macro_type)
            if macro_path.exists():
                macro_path.unlink()
                logger.info(f"Successfully deleted {macro_type} macro {macro_id} from local disk")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting {macro_type} macro from local disk: {e}")
            return False

    def delete_template(self, staff_id: str, template_id: str) -> bool:
        """Delete template from local disk."""
        try:
            template_path = self._get_templates_path(staff_id, template_id)
            if template_path.exists():
                template_path.unlink()
                logger.info(f"Successfully deleted template {template_id} from local disk")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting template from local disk: {e}")
            return False

    def sign_note(self, campus: str, pat_id: str, note_id: str, note_type: str, comment: str, staffid: int, signature_time: str = None) -> bool:
        """Sign a note by adding signature information to the note file."""
        try:
            # Get the current note
            note = self.get_note(campus, pat_id, note_id)
            if not note:
                logger.error(f"Note {note_id} not found for patient {pat_id}")
                return False

            # Create signature information
            if signature_time is None:
                from datetime import datetime
                signature_time = datetime.utcnow().isoformat()

            signature_info = {
                "signedBy": staffid,
                "signedAt": signature_time,
                "comment": comment,
                "noteType": note_type
            }

            # Add signature to note
            if "signatures" not in note:
                note["signatures"] = []
            
            note["signatures"].append(signature_info)
            
            # Update the note's status to indicate it's signed
            note["signed"] = True
            note["lastSignedAt"] = signature_time
            note["lastSignedBy"] = staffid

            # Save the updated note
            success = self.save_note(campus, pat_id, note_id, note)
            
            if success:
                logger.info(f"Successfully signed note {note_id} for patient {pat_id} in local storage")
            else:
                logger.error(f"Failed to save signed note {note_id} for patient {pat_id} in local storage")
            
            return success

        except Exception as e:
            logger.error(f"Error signing note {note_id} for patient {pat_id} in local storage: {e}")
            return False 