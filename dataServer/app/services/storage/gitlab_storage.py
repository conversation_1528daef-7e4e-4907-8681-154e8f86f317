import base64
import os
from app.utils.logger import logger
import gitlab
from pathlib import Path
import json
from typing import Optional, List, Dict
import difflib
import requests
import urllib3
import urllib.parse
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)


class GitLabStorage:
    def __init__(
        self,
        project_id: str,
        private_token: str,
        gitlab_url: str,
    ):
        self.gl = gitlab.Gitlab(
            gitlab_url, private_token=private_token, ssl_verify=False, keep_base_url=True
        )
        self.project = self.gl.projects.get(project_id)
        self.data_dir = Path(os.getenv("DATA_PATH", "./data"))

    def get_file_from_server(self, file_path: str, ref: str = "main") -> Optional[str]:
        """Get file content from GitLab repository using repository_tree."""
        try:
            # Get the directory contents using repository_tree
            path = Path(file_path)
            tree = self.project.repository_tree(
                path.parent, ref=ref, get_all=True)

            # Find the file in the tree
            file_name = path.name
            for item in tree:
                if item["name"] == file_name:
                    # Get the file content using the blob ID
                    file = self.project.repository_blob(item["id"])
                    decoded_content = base64.b64decode(
                        file["content"]).decode("utf-8")
                    return decoded_content

            logger.debug(f"File {file_path} not found in repository")
            return None

        except Exception as e:
            logger.error(f"Error getting file from server: {e}")
            return None

    def get_files_in_folder(self, folder_path: str, ref: str = "main") -> List[str]:
        """Get all files in a folder from GitLab repository."""
        try:
            # Get the directory contents using repository_tree
            tree = self.project.repository_tree(folder_path, ref=ref, get_all=True)
            
            files = []
            for item in tree:
                if item["type"] == "blob":  # Only include files, not directories
                    files.append(f"{folder_path}/{item['name']}")
            
            return files

        except Exception as e:
            logger.error(f"Error getting files in folder {folder_path}: {e}")
            return []

    def get_history(self, file_path: str) -> List[Dict]:
        """Get the commit history for a file."""
        try:
            # Construct the API URL for commits
            project_id = os.getenv("GITLAB_PROJECT_ID")
            private_token = os.getenv("GITLAB_PRIVATE_TOKEN")
            gitlab_url = os.getenv("GITLAB_URL")

            # URL encode the file path
            encoded_path = urllib.parse.quote(file_path, safe="")
            commits_url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/commits?ref_name=main&path={encoded_path}"

            # Make the request
            headers = {"PRIVATE-TOKEN": private_token}
            response = requests.get(commits_url, headers=headers, verify=False)
            response.raise_for_status()

            commits = response.json()

            history = []
            for commit in commits:
                # Check if this commit actually modified the specific file
                try:
                    # Get the diff for this commit to see what files were changed
                    diff_url = f"{gitlab_url}/api/v4/projects/{project_id}/repository/commits/{commit['id']}/diff"
                    diff_response = requests.get(
                        diff_url, headers=headers, verify=False
                    )
                    diff_response.raise_for_status()

                    diff_data = diff_response.json()
                    file_modified = False

                    # Check if our specific file was modified in this commit
                    for change in diff_data:
                        if (
                            change.get("new_path") == file_path
                            or change.get("old_path") == file_path
                        ):
                            file_modified = True
                            break

                    # Only include commits that actually modified our file
                    if file_modified:
                        # Get the file content for this version
                        file_content = self.get_file_from_server(
                            file_path, commit["id"]
                        )
                        note_data = json.loads(
                            file_content) if file_content else {}

                        history.append(
                            {
                                "commit_hash": commit["id"],
                                "owner": commit["author_name"],
                                "date": note_data.get(
                                    "modifiedTime", commit["created_at"]
                                ),
                                "message": commit["message"],
                                "updatedBy": note_data.get(
                                    "updatedBy", ""
                                ),  # Include updatedBy field
                            }
                        )
                except Exception as e:
                    logger.warning(
                        f"Could not check diff for commit {commit['id']}: {e}"
                    )
                    # If we can't check the diff, skip this commit to be safe
                    continue

            return history
        except Exception as e:
            logger.error(f"Error getting history: {e}")
            return []

    def compare_versions(
        self, file_path: str, commit1: str, commit2: str
    ) -> Optional[str]:
        """Compare two versions of a file."""
        try:
            # Get content from both commits
            content1 = self.get_file_from_server(file_path, commit1)
            content2 = self.get_file_from_server(file_path, commit2)

            if not content1 or not content2:
                logger.error(
                    f"Could not retrieve one or both versions of the file")
                return None

            # Parse JSON content for better comparison
            try:
                json1 = json.loads(content1)
                json2 = json.loads(content2)

                # Generate a user-friendly diff
                diff_lines = []
                diff_lines.append(
                    f"Changes between version {commit1[:8]} and {commit2[:8]}:"
                )
                diff_lines.append("=" * 80)

                # Compare each field
                all_keys = set(json1.keys()) | set(json2.keys())
                for key in sorted(all_keys):
                    if key not in json1:
                        diff_lines.append(f"\n[ADDED] {key}:")
                        diff_lines.append(
                            f"  {json.dumps(json2[key], indent=2)}")
                    elif key not in json2:
                        diff_lines.append(f"\n[REMOVED] {key}:")
                        diff_lines.append(
                            f"  {json.dumps(json1[key], indent=2)}")
                    elif json1[key] != json2[key]:
                        diff_lines.append(f"\n[MODIFIED] {key}:")
                        diff_lines.append(
                            f"  Old value: {json.dumps(json1[key], indent=2)}"
                        )
                        diff_lines.append(
                            f"  New value: {json.dumps(json2[key], indent=2)}"
                        )

                return "\n".join(diff_lines)

            except json.JSONDecodeError:
                # Fallback to line-by-line diff if JSON parsing fails
                diff = difflib.unified_diff(
                    content1.splitlines(keepends=True),
                    content2.splitlines(keepends=True),
                    fromfile=f"{file_path}@{commit1[:8]}",
                    tofile=f"{file_path}@{commit2[:8]}",
                    n=3,
                )
                return "".join(diff)

        except Exception as e:
            logger.error(f"Error comparing versions: {e}")
            return None

    def sign_note(self, campus: str, pat_id: str, note_id: str, note_type: str, comment: str, staffid: int, signature_time: str = None) -> bool:
        """Sign a note by updating the note file in GitLab with signature information."""
        try:
            # Construct the file path
            last_digit = pat_id[-1]
            file_path = f"data/notes/{campus}/vol{last_digit}/p{pat_id}/{note_id}.json"
            
            # Get the current note content
            current_content = self.get_file_from_server(file_path)
            if not current_content:
                logger.error(f"Note {note_id} not found for patient {pat_id} in GitLab")
                return False

            # Parse the current note
            note = json.loads(current_content)
            
            # Create signature information
            if signature_time is None:
                from datetime import datetime
                signature_time = datetime.utcnow().isoformat()

            signature_info = {
                "signedBy": staffid,
                "signedAt": signature_time,
                "comment": comment,
                "noteType": note_type
            }

            # Add signature to note
            if "signatures" not in note:
                note["signatures"] = []
            
            note["signatures"].append(signature_info)
            
            # Update the note's status to indicate it's signed
            note["signed"] = True
            note["lastSignedAt"] = signature_time
            note["lastSignedBy"] = staffid

            # Update the file in GitLab
            try:
                self.project.commits.create({
                    "branch": "main",
                    "commit_message": f"Sign note {note_id} for patient {pat_id} - {comment}",
                    "actions": [
                        {
                            "action": "update",
                            "file_path": file_path,
                            "content": json.dumps(note, indent=2),
                        }
                    ],
                })
                
                logger.info(f"Successfully signed note {note_id} for patient {pat_id} in GitLab")
                return True
                
            except Exception as e:
                logger.error(f"Failed to update note {note_id} in GitLab: {e}")
                return False

        except Exception as e:
            logger.error(f"Error signing note {note_id} for patient {pat_id} in GitLab: {e}")
            return False
