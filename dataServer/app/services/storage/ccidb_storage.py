from http.client import HTTPException
import json
import os
import sys
import time
from datetime import datetime
from app.utils.ycql import get_ycql_client, create_ycql_client
from app.utils.logger import logger
from typing import Optional, List

# Robust, portable config path resolution
# Get the dataServer root directory (4 levels up from this file)
APP_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))  # .../dataServer/
CONFIG_PATH = os.path.join(APP_DIR, "app", "config", "note_types_config.json")
with open(CONFIG_PATH, "r") as f:
    NOTE_TYPES_CONFIG = json.load(f)

class CCIDBStorage:
    def __init__(self):
        pass

    def _load_default_exam_layout(self) -> dict:
        """Load the default exam layout from the JSON file."""
        try:
            # Use relative path to config directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_dir = os.path.join(current_dir, "..", "config")
            default_layout_path = os.path.join(config_dir, "examCardLayout.json")
            
            if os.path.exists(default_layout_path):
                with open(default_layout_path, 'r') as f:
                    default_layout = json.load(f)
                    logger.info("Successfully loaded default exam layout")
                    return default_layout
            else:
                logger.error(f"Default exam layout file not found at: {default_layout_path}")
                return {}
        except Exception as e:
            logger.error(f"Error loading default exam layout: {str(e)}")
            return {}

    async def write_note_to_ccidb(self, note: dict, campus: str, pat_id: str, update: bool, staffid: Optional[int] = None, ccitoken: Optional[str] = None):
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Writing note to CCIDB: {note}")

        if staffid is None or ccitoken is None:
            logger.warning(
                "staffid and ccitoken are required for CCIDB operations")
            return False

        auth_staffid = staffid
        auth_ccitoken = ccitoken

        # Lookup NIT and required_signature from config
        note_type = note["type"]
        type_cfg = NOTE_TYPES_CONFIG.get(note_type)
        if not type_cfg:
            logger.error(f"Note type '{note_type}' not found in config.")
            return False
        nit = type_cfg["nit"]
        required_signature = type_cfg.get("required_signature", False)

        try:
            hobj = {
                "type": "HOBJ",
                "HOBJ": "ainotes/updatenotedata" if update else "ainotes/storenotedata",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                    ":staffid": auth_staffid,
                    ":ccitoken": auth_ccitoken,
                    ":ipaddr": "127.0.0.1",
                    ":key": int(note["noteID"]),
                    ":data": note,
                    ":nit": nit,
                },
            }
            if not update and required_signature:
                hobj["qargv"][":required_signature"] = 1

            logger.info("Executing YCQL query for storing note data", hobj)
            ycql_client = await create_ycql_client()
            ret = await ycql_client.execute(hobj)
            if ret and 0 != len(ret) and "Result" in ret:
                logger.info(f"Note written to CCIDB: {ret}")
                return True
            else:
                logger.error(f"Failed to write note to CCIDB: {ret}")
                return False
        except Exception as e:
            logger.error("YCQL operation failed for %s: %s",
                          "write_note_to_ccidb", str(e))
            return False

    async def read_note_from_ccidb(self, campus, pat_id, key: Optional[int] = None, nit: Optional[int] = None):
        # Use default path if CCSYSDIR is not set
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Reading note from CCIDB: {dbpath}")
        notes = []

        try:
            hobj = {
                "type": "HOBJ",
                "HOBJ": "ainotes/getnotedata",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                }
            }

            # Only add key and nit if both are provided
            if key is not None and nit is not None:
                hobj["qargv"][":key"] = key
                hobj["qargv"][":nit"] = nit

            logger.info("Executing YCQL query for getting note data", hobj)
            async with get_ycql_client() as ycql_client:
                ret = await ycql_client.execute(hobj)
                if ret and 0 != len(ret) and "Result" in ret:
                    for item in ret["Result"]:
                        try:
                            note_data = json.loads(item['data'])
                            note_data['noteID'] = str(item['key'])
                            # Parse signature_data if present
                            signature_data = item.get('signature_data')
                            if signature_data:
                                try:
                                    sig = json.loads(signature_data)
                                    note_data['signed'] = True
                                    note_data['signatures'] = [sig] if isinstance(sig, dict) else sig
                                    note_data['lastSignedAt'] = sig.get('signedAt') if isinstance(sig, dict) else None
                                    note_data['lastSignedBy'] = sig.get('signedByName', sig.get('signedBy')) if isinstance(sig, dict) else None
                                except Exception as e:
                                    logger.warning(f"Failed to parse signature_data: {e}")
                                    note_data['signed'] = False
                                    note_data['signatures'] = []
                                    note_data['lastSignedAt'] = None
                                    note_data['lastSignedBy'] = None
                            else:
                                note_data['signed'] = False
                                note_data['signatures'] = []
                                note_data['lastSignedAt'] = None
                                note_data['lastSignedBy'] = None
                            notes.append(note_data)
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse note data: {e}")
                            continue
        except Exception as e:
            logger.error("YCQL operation failed for %s: %s",
                          "read_note_from_ccidb", str(e))
            # Don't raise the exception - just log it and continue

        # Return single note if both key and nit were provided, otherwise return list
        if key is not None and nit is not None and notes:
            return notes[0]  # Return first (and should be only) note
        return notes

    async def sign_note(self, campus, pat_id, note_type: str, key: int, comment: str, staffid: Optional[int] = None, ccitoken: Optional[str] = None):
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Signing note in CCIDB: {dbpath}, note_type: {note_type}, key: {key}, comment: {comment}")

        if staffid is None or ccitoken is None:
            logger.warning(
                "staffid and ccitoken are required for CCIDB operations")
            return False

        # Get NIT from config
        type_cfg = NOTE_TYPES_CONFIG.get(note_type)
        if not type_cfg:
            logger.error(f"Note type '{note_type}' not found in config.")
            return False
        nit = type_cfg["nit"]

        try:      
            hobj = {
                "type": "HOBJ",
                "HOBJ": "ainotes/signature/sign",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                    ":key": key,
                    ":comment": comment,
                    ":nit": nit,
                    ":staffid": staffid,
                    ":ccitoken": ccitoken,
                },
            }

            logger.info("Executing YCQL query for signing note", hobj)  
            async with get_ycql_client() as ycql_client:
                ret = await ycql_client.execute(hobj)
                if ret and 0 != len(ret) and "Result" in ret:
                    logger.info(f"Note signed in CCIDB: {ret}")
                    return True
                else:
                    logger.error(f"Failed to sign note in CCIDB: {ret}")
                    return False
        except Exception as e:  
            logger.error("YCQL operation failed for %s: %s",
                          "sign_note", str(e))
            # Don't raise the exception -  just log it and continue
            # This allows the note to be saved locally even if YCQL fails
            return False

    async def get_required_signature(self, campus, pat_id, note_type: str, key: int):
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Getting required signature from CCIDB: {dbpath}, note_type: {note_type}, key: {key}")

        # Get NIT from config
        type_cfg = NOTE_TYPES_CONFIG.get(note_type)
        if not type_cfg:
            logger.error(f"Note type '{note_type}' not found in config.")
            return False
        nit = type_cfg["nit"]

        try:
            hobj = {
                "type": "HOBJ",
                "HOBJ": "ainotes/signature/getrequiredsignature",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                    ":key": key,
                    ":nit": nit,
                },
            }

            logger.info("Executing YCQL query for getting required signature", hobj)
            async with get_ycql_client() as ycql_client:
                ret = await ycql_client.execute(hobj)
                if ret and 0 != len(ret) and "Result" in ret:
                    logger.info(f"Required signature found in CCIDB: {ret}")
                    return ret
                else:
                    logger.error(f"Failed to get required signature from CCIDB: {ret}")
                    return False
        except Exception as e:
            logger.error("YCQL operation failed for %s: %s",
                          "get_required_signature", str(e))
            # Don't raise the exception - just log it and continue
            return False
        
    async def set_required_signature(self, campus, pat_id, note_type: str, key: int, signature: str, staffid: Optional[int] = None, ccitoken: Optional[str] = None):
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Setting required signature in CCIDB: {dbpath}, note_type: {note_type}, key: {key}, signature: {signature}")

        if staffid is None or ccitoken is None:
            logger.warning(
                "staffid and ccitoken are required for CCIDB operations")
            return False

        # Get NIT from config
        type_cfg = NOTE_TYPES_CONFIG.get(note_type)
        if not type_cfg:
            logger.error(f"Note type '{note_type}' not found in config.")
            return False
        nit = type_cfg["nit"]

        try:
            hobj = {
                "type": "HOBJ",
                "HOBJ": "ainotes/signature/setrequiredsignature",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                    ":key": key,
                    ":signature": signature,
                    ":nit": nit,
                    ":staffid": staffid,
                    ":ccitoken": ccitoken,
                },
            }

            logger.info("Executing YCQL query for setting required signature", hobj)
            async with get_ycql_client() as ycql_client:
                ret = await ycql_client.execute(hobj)
                if ret and 0 != len(ret) and "Result" in ret:
                    logger.info(f"Required signature set in CCIDB: {ret}")
                    return True
                else:
                    logger.error(f"Failed to set required signature in CCIDB: {ret}")
                    return False
        except Exception as e:
            logger.error("YCQL operation failed for %s: %s",
                          "set_required_signature", str(e))
            # Don't raise the exception - just log it and continue
            return False

    # Remove the obsolete get_all_note_types method - no longer needed with config-driven approach

    async def read_exam_from_ccidb(self, campus, pat_id, visitkey: int):
        # Use default path if CCSYSDIR is not set
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Reading exam from CCIDB: {dbpath}, visitkey: {visitkey}")
        exam = None

        try:
            hobj = {
                "type": "HOBJ",
                "HOBJ": "exam/getexamdata",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                    ":key": visitkey,
                }
            }

            logger.info("Executing YCQL query for getting exam data", hobj)
            async with get_ycql_client() as ycql_client:
                ret = await ycql_client.execute(hobj)
                if ret and 0 != len(ret) and "Result" in ret:
                    # Check if Result contains None (no data found)
                    if ret["Result"] == [None]:
                        logger.info(f"No exam data found for patient {pat_id}, visitkey {visitkey}, returning default layout")
                        default_layout = self._load_default_exam_layout()
                        exam = {
                            "data": default_layout,
                            "lastModified": datetime.utcnow().isoformat()
                        }
                    else:
                        # Process the actual exam data
                        for item in ret["Result"]:
                            try:
                                exam_data = json.loads(item['data'])
                                
                                # Check if exam data is empty and return default layout if so
                                if not exam_data or (isinstance(exam_data, dict) and len(exam_data) == 0):
                                    logger.info(f"Empty exam data found for patient {pat_id}, visitkey {visitkey}, returning default layout")
                                    default_layout = self._load_default_exam_layout()
                                    exam = {
                                        "data": default_layout,
                                        "lastModified": datetime.utcnow().isoformat()
                                    }
                                else:
                                    # Return the exam data in the expected format with data field
                                    exam = {
                                        "data": exam_data,
                                        "lastModified": datetime.utcnow().isoformat()
                                    }
                                break  # Take the first exam found
                            except json.JSONDecodeError as e:
                                logger.error(f"Failed to parse exam data: {e}")
                                continue
        except Exception as e:
            logger.error("YCQL operation failed for %s: %s",
                          "read_exam_from_ccidb", str(e))
            # Don't raise the exception - just log it and continue

        return exam
    
    async def write_exam_to_ccidb(self, exam: dict, campus: str, pat_id: str, visitkey: int, staffid: Optional[int] = None, ccitoken: Optional[str] = None):
        # Use default path if CCSYSDIR is not set
        ccsysdir = os.environ.get("CCSYSDIR", "./data")
        dbpath = ccsysdir + f"/vol{pat_id[-1]}/p{pat_id}"
        logger.info(f"Writing exam to CCIDB: {exam}, visitkey: {visitkey}")

        # Use provided credentials - no fallback to defaults
        if staffid is None or ccitoken is None:
            logger.warning(
                "staffid and ccitoken are required for CCIDB operations")
            return False

        auth_staffid = staffid
        auth_ccitoken = ccitoken

        try:
            hobj = {
                "type": "HOBJ",
                "HOBJ": "exam/storeexamdata",
                "dbs": [dbpath],
                "qargv": {
                    ":campus": campus,
                    ":staffid": auth_staffid,
                    ":ccitoken": auth_ccitoken,
                    ":ipaddr": "127.0.0.1",
                    ":key": visitkey,
                    ":data": exam,
                },
            }

            logger.info("Executing YCQL query for storing exam data", hobj)
            # Use the singleton client directly
            ycql_client = await create_ycql_client()
            ret = await ycql_client.execute(hobj)
            if ret and 0 != len(ret) and "Result" in ret:
                logger.info(f"Exam written to CCIDB: {ret}")
                return True
            else:
                logger.error(f"Failed to write exam to CCIDB: {ret}")
                return False
        except Exception as e:
            logger.error("YCQL operation failed for %s: %s",
                          "write_exam_to_ccidb", str(e))
            # Don't raise the exception - just log it and continue
            # This allows the exam to be saved locally even if YCQL fails
            return False



