import os
from app.utils.logger import logger
from app.config.settings import settings

def get_gitlab_service():
    if not hasattr(get_gitlab_service, "_instance"):
        try:
            from app.services.storage.gitlab_storage import GitLabStorage
            if not settings.enable_gitlab_storage:
                get_gitlab_service._instance = None
                logger.info("GitLab storage is disabled in configuration")
            elif settings.gitlab_project_id and settings.gitlab_private_token:
                get_gitlab_service._instance = GitLabStorage(
                    project_id=settings.gitlab_project_id,
                    private_token=settings.gitlab_private_token,
                    gitlab_url=settings.gitlab_url,
                )
                logger.info("GitLab storage initialized successfully")
            else:
                get_gitlab_service._instance = None
                logger.warning(
                    "GitLab storage enabled but credentials not found. GitLab storage will be disabled."
                )
        except Exception as e:
            get_gitlab_service._instance = None
            logger.warning(
                f"Failed to initialize GitLab storage: {e}. GitLab storage will be disabled."
            )
    return get_gitlab_service._instance

def get_local_storage():
    if not hasattr(get_local_storage, "_instance"):
        from app.services.storage.local_storage import LocalStorage
        get_local_storage._instance = LocalStorage()
    return get_local_storage._instance

def get_ccidb_storage():
    if not hasattr(get_ccidb_storage, "_instance"):
        if settings.enable_ccidb_storage:
            try:
                from app.services.storage.ccidb_storage import CCIDBStorage
                get_ccidb_storage._instance = CCIDBStorage()
                logger.info("CCIDB storage initialized successfully")
            except Exception as e:
                get_ccidb_storage._instance = None
                logger.warning(
                    f"Failed to initialize CCIDB storage: {e}. CCIDB storage will be disabled."
                )
        else:
            get_ccidb_storage._instance = None
            logger.info("CCIDB storage is disabled in configuration")
    return get_ccidb_storage._instance

def get_notes_manager():
    if not hasattr(get_notes_manager, "_instance"):
        from app.services.managers.notes_manager import NotesManager
        get_notes_manager._instance = NotesManager(get_gitlab_service(), get_local_storage(), get_ccidb_storage())
    return get_notes_manager._instance

def get_note_macros_manager():
    if not hasattr(get_note_macros_manager, "_instance"):
        from app.services.managers.note_macros_manager import NoteMacrosManager
        get_note_macros_manager._instance = NoteMacrosManager(get_gitlab_service(), get_local_storage())
    return get_note_macros_manager._instance

def get_rad_macros_manager():
    if not hasattr(get_rad_macros_manager, "_instance"):
        from app.services.managers.rad_macros_manager import RadMacrosManager
        get_rad_macros_manager._instance = RadMacrosManager(get_gitlab_service(), get_local_storage())
    return get_rad_macros_manager._instance

def get_rad_templates_manager():
    if not hasattr(get_rad_templates_manager, "_instance"):
        from app.services.managers.rad_templates_manager import RadTemplatesManager
        get_rad_templates_manager._instance = RadTemplatesManager(get_gitlab_service(), get_local_storage())
    return get_rad_templates_manager._instance

# Keep the old method for backward compatibility
def get_macros_manager():
    if not hasattr(get_macros_manager, "_instance"):
        from app.services.managers.macros_manager import MacrosManager
        get_macros_manager._instance = MacrosManager(get_gitlab_service(), get_local_storage())
    return get_macros_manager._instance

def get_exam_manager():
    if not hasattr(get_exam_manager, "_instance"):
        from app.services.managers.exam_manager import ExamManager
        get_exam_manager._instance = ExamManager(get_gitlab_service(), get_local_storage(), get_ccidb_storage())
    return get_exam_manager._instance
