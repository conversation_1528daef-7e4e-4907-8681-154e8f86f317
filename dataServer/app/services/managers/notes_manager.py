from app.utils.logger import logger
from app.config.settings import settings
import json
from typing import Optional, Dict
from datetime import datetime
from app.services.storage.ccidb_storage import NOTE_TYPES_CONFIG


class NotesManager:
    def __init__(self, gitlab_service, local_storage, ccidb_storage=None):
        self.gitlab_service = gitlab_service
        self.local_storage = local_storage
        self.ccidb_storage = ccidb_storage

    async def get_note(self, campus: str, pat_id: str, note_id: str, staffid: Optional[int] = None, ccitoken: Optional[str] = None, note_type: Optional[str] = None) -> Optional[dict]:
        """Get the current version of a note. Requires both note_id and note_type (to determine NIT)."""
        try:
            note = None
            # Only fetch from CCIDB if note_type is provided
            if self.ccidb_storage and settings.enable_ccidb_storage and note_type is not None:
                try:
                    key = int(note_id) if note_id.isdigit() else None
                    type_cfg = NOTE_TYPES_CONFIG.get(note_type)
                    if key is not None and type_cfg is not None:
                        nit = type_cfg["nit"]
                        note = await self.ccidb_storage.read_note_from_ccidb(campus, pat_id, key, nit)
                except Exception as e:
                    logger.warning(f"Could not get note from CCIDB: {e}")
            # Second try local disk if enabled
            if note is None and settings.enable_local_disk_storage:
                note = self.local_storage.get_note(campus, pat_id, note_id)
            # Third try GitLab if enabled and service is available
            if note is None and settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    last_digit = pat_id[-1]
                    file_path = (
                        f"data/notes/{campus}/vol{last_digit}/p{pat_id}/{note_id}.json"
                    )
                    file_content = self.gitlab_service.get_file_from_server(file_path)
                    if file_content:
                        if settings.enable_local_disk_storage:
                            self.local_storage.save_note(campus, pat_id, note_id, json.loads(file_content))
                        note = json.loads(file_content)
                except Exception as e:
                    logger.error(f"Error getting note from GitLab: {e}")
            if note:
                type_cfg = NOTE_TYPES_CONFIG.get(note["type"], {})
                note["required_signature"] = type_cfg.get("required_signature", False)
                # No need to fetch signature info for CCIDB, it's already attached
                note.setdefault("signed", False)
                note.setdefault("signatures", [])
                note.setdefault("lastSignedAt", None)
                note.setdefault("lastSignedBy", None)
            return note

        except Exception as e:
            logger.error(f"Error getting note: {e}")
            return None

    async def save_note(self, campus: str, pat_id: str, note_id: str, content: dict, update: bool, staffid: Optional[int] = None, ccitoken: Optional[str] = None) -> bool:
        """Save a note to all available storage backends."""
        try:
            success = True

            # Save to CCIDB if enabled
            if self.ccidb_storage and settings.enable_ccidb_storage:
                try:
                    ccidb_success = await self.ccidb_storage.write_note_to_ccidb(
                        content,
                        campus,
                        pat_id,
                        update=update,  # Assume update for now
                        staffid=staffid,
                        ccitoken=ccitoken,
                    )
                    success = ccidb_success and success
                    if ccidb_success:
                        logger.info(f"Successfully saved note {note_id} to CCIDB")
                    else:
                        logger.warning(f"Failed to save note {note_id} to CCIDB")
                except Exception as e:
                    logger.warning(f"CCIDB service failed, continuing with other storage: {e}")

            # Save to local disk if enabled
            if settings.enable_local_disk_storage:
                local_success = self._save_to_local_disk(campus, pat_id, note_id, content)
                success = local_success and success
                if not local_success:
                    logger.warning(f"Failed to save note {note_id} to local disk")

            # Save to GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                gitlab_success = self._save_to_gitlab(campus, pat_id, note_id, content)
                success = gitlab_success and success
                if not gitlab_success:
                    logger.warning(f"Failed to save note {note_id} to GitLab")

            return success
        except Exception as e:
            logger.error(f"Error saving note: {e}")
            return False

    def _save_to_local_disk(
        self, campus: str, pat_id: str, note_id: str, content: dict
    ) -> bool:
        """Save note to local disk."""
        return self.local_storage.save_note(campus, pat_id, note_id, content)

    def _save_to_gitlab(
        self, campus: str, pat_id: str, note_id: str, content: dict
    ) -> bool:
        """Save note to GitLab."""
        try:
            last_digit = pat_id[-1]
            gitlab_file_path = (
                f"data/notes/{campus}/vol{last_digit}/p{pat_id}/{note_id}.json"
            )

            # Check if file exists in GitLab
            try:
                existing_content = self.gitlab_service.get_file_from_server(
                    gitlab_file_path
                )
                file_exists = existing_content is not None
            except:
                file_exists = False

            if file_exists:
                # Update existing file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Update note {note_id} for patient {pat_id} in campus {campus}",
                        "actions": [
                            {
                                "action": "update",
                                "file_path": gitlab_file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )
            else:
                # Create new file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Create note {note_id} for patient {pat_id} in campus {campus}",
                        "actions": [
                            {
                                "action": "create",
                                "file_path": gitlab_file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )

            logger.info(f"Successfully saved note {note_id} to GitLab")
            return True
        except Exception as e:
            logger.error(f"Error saving note to GitLab: {e}")
            return False

    def get_note_history(self, campus: str, pat_id: str, note_id: str) -> list:
        """Get the commit history for a note."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get note history."
                )
                return []

            last_digit = pat_id[-1]
            file_path = f"data/notes/{campus}/vol{last_digit}/p{pat_id}/{note_id}.json"
            return self.gitlab_service.get_history(file_path)
        except Exception as e:
            logger.error(f"Error getting note history: {e}")
            return []

    def get_note_version(
        self, campus: str, pat_id: str, note_id: str, commit_hash: str
    ) -> Optional[dict]:
        """Get a specific version of a note."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get note version."
                )
                return None

            last_digit = pat_id[-1]
            file_path = f"data/notes/{campus}/vol{last_digit}/p{pat_id}/{note_id}.json"
            file_content = self.gitlab_service.get_file_from_server(
                file_path, commit_hash
            )
            if file_content:
                return json.loads(file_content)
            return None

        except Exception as e:
            logger.error(f"Error getting note version: {e}")
            return None

    def compare_note_versions(
        self, campus: str, pat_id: str, note_id: str, commit1: str, commit2: str
    ) -> Optional[str]:
        """Compare two versions of a note."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot compare note versions."
                )
                return None

            last_digit = pat_id[-1]
            file_path = f"data/notes/{campus}/vol{last_digit}/p{pat_id}/{note_id}.json"
            return self.gitlab_service.compare_versions(file_path, commit1, commit2)
        except Exception as e:
            logger.error(f"Error comparing note versions: {e}")
            return None

    async def sign_note(self, campus: str, pat_id: str, note_type: str, note_id: str, comment: str, staffid: Optional[int] = None, ccitoken: Optional[str] = None) -> bool:
        """Sign a note using available storage backends."""
        try:
            success = False
            from datetime import datetime
            signature_time = datetime.utcnow().isoformat()

            # Sign in CCIDB if enabled
            if self.ccidb_storage and settings.enable_ccidb_storage:
                try:
                    # Validate that credentials are provided for CCIDB
                    if staffid is None or ccitoken is None:
                        logger.warning("staffid and ccitoken are required for CCIDB signing")
                    else:
                        # Convert note_id to integer key for CCIDB
                        key = int(note_id) if note_id.isdigit() else None
                        if key is not None:
                            ccidb_success = await self.ccidb_storage.sign_note(
                                campus, 
                                pat_id, 
                                note_type, 
                                key, 
                                comment, 
                                staffid, 
                                ccitoken
                            )
                            if ccidb_success:
                                logger.info(f"Successfully signed note {note_id} in CCIDB")
                                success = True
                            else:
                                logger.warning(f"Failed to sign note {note_id} in CCIDB")
                        else:
                            logger.warning(f"Invalid note_id format for CCIDB: {note_id}")
                except Exception as e:
                    logger.warning(f"CCIDB signing failed: {e}")

            # Sign in local storage if enabled
            if settings.enable_local_disk_storage:
                try:
                    local_success = self.local_storage.sign_note(
                        campus,
                        pat_id,
                        note_id,
                        note_type,
                        comment,
                        staffid or 0,  # Use 0 as default if staffid is None
                        signature_time
                    )
                    if local_success:
                        logger.info(f"Successfully signed note {note_id} in local storage")
                        success = True
                    else:
                        logger.warning(f"Failed to sign note {note_id} in local storage")
                except Exception as e:
                    logger.warning(f"Local storage signing failed: {e}")

            # Sign in GitLab if enabled
            if settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    gitlab_success = self.gitlab_service.sign_note(
                        campus,
                        pat_id,
                        note_id,
                        note_type,
                        comment,
                        staffid or 0,  # Use 0 as default if staffid is None
                        signature_time
                    )
                    if gitlab_success:
                        logger.info(f"Successfully signed note {note_id} in GitLab")
                        success = True
                    else:
                        logger.warning(f"Failed to sign note {note_id} in GitLab")
                except Exception as e:
                    logger.warning(f"GitLab signing failed: {e}")

            if success:
                logger.info(f"Note {note_id} signed successfully in at least one storage backend")
            else:
                logger.error(f"Failed to sign note {note_id} in any storage backend")

            return success

        except Exception as e:
            logger.error(f"Error signing note {note_id} for patient {pat_id}: {e}")
            return False
