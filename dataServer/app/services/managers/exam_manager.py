from app.utils.logger import logger
from app.config.settings import settings
import json
from typing import Optional, Dict
from datetime import datetime


class ExamManager:
    def __init__(self, gitlab_service, local_storage, ccidb_storage=None):
        self.gitlab_service = gitlab_service
        self.local_storage = local_storage
        self.ccidb_storage = ccidb_storage

    async def get_exam(self, campus: str, pat_id: str, visitkey: int) -> Optional[dict]:
        """Get the current version of an exam configuration."""
        try:
            # First try to get from CCIDB if enabled
            if self.ccidb_storage and settings.enable_ccidb_storage:
                try:
                    exam_data = await self.ccidb_storage.read_exam_from_ccidb(campus, pat_id, visitkey)
                    if exam_data:
                        logger.info(f"Successfully retrieved exam from CCIDB for patient {pat_id}, visitkey {visitkey}")
                        return exam_data
                except Exception as e:
                    logger.warning(f"Could not get exam from CCIDB: {e}")

            # Then try local disk if enabled
            if settings.enable_local_disk_storage:
                exam_data = self.local_storage.get_exam(campus, pat_id, visitkey)
                if exam_data:
                    logger.info(f"Successfully retrieved exam from local storage for patient {pat_id}, visitkey {visitkey}")
                    return {
                        "data": exam_data,
                        "lastModified": datetime.utcnow().isoformat()
                    }

            # Finally fallback to GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    last_digit = pat_id[-1]
                    file_path = (
                        f"data/exams/{campus}/vol{last_digit}/p{pat_id}/{visitkey}.json"
                    )

                    file_content = self.gitlab_service.get_file_from_server(file_path)
                    if file_content:
                        exam_data = json.loads(file_content)
                        # Save the file locally for future use if local storage is enabled
                        if settings.enable_local_disk_storage:
                            self.local_storage.save_exam(campus, pat_id, visitkey, exam_data)
                        # Return in consistent format
                        logger.info(f"Successfully retrieved exam from GitLab for patient {pat_id}, visitkey {visitkey}")
                        return {
                            "data": exam_data,
                            "lastModified": datetime.utcnow().isoformat()
                        }
                except Exception as e:
                    logger.error(f"Error getting exam from GitLab: {e}")

            # If no exam data found in any storage, return None
            logger.info(f"No exam configuration found for patient {pat_id}, visitkey {visitkey}")
            return None

        except Exception as e:
            logger.error(f"Error getting exam: {e}")
            return None

    async def save_exam(self, campus: str, pat_id: str, visitkey: int, content: dict, staffid: Optional[int] = None, ccitoken: Optional[str] = None) -> bool:
        """Save an exam configuration to all available storage backends."""
        try:
            any_success = False

            # Save to CCIDB if enabled
            if self.ccidb_storage and settings.enable_ccidb_storage:
                try:
                    # Create exam object with metadata
                    exam_data = {
                        "data": content,
                        "lastModified": datetime.utcnow().isoformat()
                    }
                    
                    ccidb_success = await self.ccidb_storage.write_exam_to_ccidb(
                        exam_data,
                        campus,
                        pat_id,
                        visitkey,
                        staffid=staffid,
                        ccitoken=ccitoken,
                    )
                    if ccidb_success:
                        logger.info(f"Successfully saved exam to CCIDB for patient {pat_id}, visitkey {visitkey}")
                        any_success = True
                    else:
                        logger.warning(f"Failed to save exam to CCIDB for patient {pat_id}, visitkey {visitkey}")
                except Exception as e:
                    logger.warning(f"CCIDB service failed, continuing with other storage: {e}")

            # Save to local disk if enabled
            if settings.enable_local_disk_storage:
                local_success = self._save_to_local_disk(campus, pat_id, visitkey, content)
                if local_success:
                    any_success = True
                else:
                    logger.warning(f"Failed to save exam for patient {pat_id}, visitkey {visitkey} to local disk")

            # Save to GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                gitlab_success = self._save_to_gitlab(campus, pat_id, visitkey, content)
                if gitlab_success:
                    any_success = True
                else:
                    logger.warning(f"Failed to save exam for patient {pat_id}, visitkey {visitkey} to GitLab")

            return any_success
        except Exception as e:
            logger.error(f"Error saving exam: {e}")
            return False

    def _save_to_local_disk(
        self, campus: str, pat_id: str, visitkey: int, content: dict
    ) -> bool:
        """Save exam to local disk."""
        return self.local_storage.save_exam(campus, pat_id, visitkey, content)

    def _save_to_gitlab(
        self, campus: str, pat_id: str, visitkey: int, content: dict
    ) -> bool:
        """Save exam to GitLab."""
        try:
            last_digit = pat_id[-1]
            gitlab_file_path = (
                f"data/exams/{campus}/vol{last_digit}/p{pat_id}/{visitkey}.json"
            )

            # Check if file exists in GitLab
            try:
                existing_content = self.gitlab_service.get_file_from_server(
                    gitlab_file_path
                )
                file_exists = existing_content is not None
            except:
                file_exists = False

            if file_exists:
                # Update existing file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Update exam for patient {pat_id}, visitkey {visitkey} in campus {campus}",
                        "actions": [
                            {
                                "action": "update",
                                "file_path": gitlab_file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )
            else:
                # Create new file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Create exam for patient {pat_id}, visitkey {visitkey} in campus {campus}",
                        "actions": [
                            {
                                "action": "create",
                                "file_path": gitlab_file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )

            logger.info(f"Successfully saved exam for patient {pat_id}, visitkey {visitkey} to GitLab")
            return True
        except Exception as e:
            logger.error(f"Error saving exam to GitLab: {e}")
            return False

    def get_exam_history(self, campus: str, pat_id: str, visitkey: int) -> list:
        """Get the commit history for an exam configuration."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get exam history."
                )
                return []

            last_digit = pat_id[-1]
            file_path = f"data/exams/{campus}/vol{last_digit}/p{pat_id}/{visitkey}.json"
            return self.gitlab_service.get_history(file_path)
        except Exception as e:
            logger.error(f"Error getting exam history: {e}")
            return []

    def get_exam_version(
        self, campus: str, pat_id: str, visitkey: int, commit_hash: str
    ) -> Optional[dict]:
        """Get a specific version of an exam configuration."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get exam version."
                )
                return None

            last_digit = pat_id[-1]
            file_path = f"data/exams/{campus}/vol{last_digit}/p{pat_id}/{visitkey}.json"
            file_content = self.gitlab_service.get_file_from_server(
                file_path, commit_hash
            )
            if file_content:
                return json.loads(file_content)
            return None
        except Exception as e:
            logger.error(f"Error getting exam version: {e}")
            return None

    def compare_exam_versions(
        self, campus: str, pat_id: str, visitkey: int, commit1: str, commit2: str
    ) -> Optional[str]:
        """Compare two versions of an exam configuration."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot compare exam versions."
                )
                return None

            last_digit = pat_id[-1]
            file_path = f"data/exams/{campus}/vol{last_digit}/p{pat_id}/{visitkey}.json"
            return self.gitlab_service.compare_versions(file_path, commit1, commit2)
        except Exception as e:
            logger.error(f"Error comparing exam versions: {e}")
            return None 