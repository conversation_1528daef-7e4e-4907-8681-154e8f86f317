from app.utils.logger import logger
from app.config.settings import settings
import json
from typing import Optional, Dict
from datetime import datetime


class NoteMacrosManager:
    def __init__(self, gitlab_service, local_storage):
        self.gitlab_service = gitlab_service
        self.local_storage = local_storage

    async def get_macro(self, staff_id: str, macro_id: str) -> Optional[dict]:
        """Get the current version of a note macro."""
        try:
            # Try local disk first if enabled
            if settings.enable_local_disk_storage:
                local_macro = self.local_storage.get_macro(staff_id, macro_id, "note")
                if local_macro:
                    return local_macro

            # If not found locally or local storage is disabled, try GitLab
            if settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                    file_path = f"data/note-macros/{staff_prefix}/{macro_id}.json"

                    file_content = self.gitlab_service.get_file_from_server(file_path)
                    if file_content:
                        # Save the file locally for future use if local storage is enabled
                        if settings.enable_local_disk_storage:
                            self.local_storage.save_macro(staff_id, macro_id, json.loads(file_content), "note")
                        return json.loads(file_content)
                except Exception as e:
                    logger.error(f"Error getting note macro from GitLab: {e}")

            return None

        except Exception as e:
            logger.error(f"Error getting note macro: {e}")
            return None

    async def save_macro(
        self,
        staff_id: str,
        macro_id: str,
        content: dict,
    ) -> bool:
        """Save a note macro to available storage backends."""
        try:
            success = True

            # Add macro type to content
            content["type"] = "note"

            # Save to local disk if enabled
            if settings.enable_local_disk_storage:
                local_success = self._save_to_local_disk(staff_id, macro_id, content)
                success = local_success and success
                if not local_success:
                    logger.warning(f"Failed to save note macro {macro_id} to local disk")

            # Save to GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                gitlab_success = self._save_to_gitlab(staff_id, macro_id, content)
                success = gitlab_success and success
                if not gitlab_success:
                    logger.warning(f"Failed to save note macro {macro_id} to GitLab")

            return success
        except Exception as e:
            logger.error(f"Error saving note macro: {e}")
            return False

    def _save_to_local_disk(self, staff_id: str, macro_id: str, content: dict) -> bool:
        """Save note macro to local disk."""
        return self.local_storage.save_macro(staff_id, macro_id, content, "note")

    def _save_to_gitlab(self, staff_id: str, macro_id: str, content: dict) -> bool:
        """Save note macro to GitLab."""
        try:
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/note-macros/{staff_prefix}/{macro_id}.json"

            # Check if file exists in GitLab
            try:
                existing_content = self.gitlab_service.get_file_from_server(file_path)
                file_exists = existing_content is not None
            except:
                file_exists = False

            if file_exists:
                # Update existing file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Update note macro {macro_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "update",
                                "file_path": file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )
            else:
                # Create new file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Create note macro {macro_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "create",
                                "file_path": file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )

            logger.info(f"Successfully saved note macro {macro_id} to GitLab")
            return True
        except Exception as e:
            logger.error(f"Error saving note macro to GitLab: {e}")
            return False

    def get_macro_history(self, staff_id: str, macro_id: str) -> list:
        """Get the commit history for a note macro."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get note macro history."
                )
                return []

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/note-macros/{staff_prefix}/{macro_id}.json"
            return self.gitlab_service.get_history(file_path)
        except Exception as e:
            logger.error(f"Error getting note macro history: {e}")
            return []

    def get_macro_version(
        self, staff_id: str, macro_id: str, commit_hash: str
    ) -> Optional[dict]:
        """Get a specific version of a note macro."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get note macro version."
                )
                return None

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/note-macros/{staff_prefix}/{macro_id}.json"
            file_content = self.gitlab_service.get_file_from_server(
                file_path, commit_hash
            )
            if file_content:
                return json.loads(file_content)
            return None

        except Exception as e:
            logger.error(f"Error getting note macro version: {e}")
            return None

    def compare_macro_versions(
        self, staff_id: str, macro_id: str, commit1: str, commit2: str
    ) -> Optional[str]:
        """Compare two versions of a note macro."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot compare note macro versions."
                )
                return None

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/note-macros/{staff_prefix}/{macro_id}.json"
            return self.gitlab_service.compare_versions(file_path, commit1, commit2)
        except Exception as e:
            logger.error(f"Error comparing note macro versions: {e}")
            return None 