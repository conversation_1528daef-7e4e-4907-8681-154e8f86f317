from app.utils.logger import logger
from app.config.settings import settings
import json
from typing import Optional, Dict
from datetime import datetime


class RadTemplatesManager:
    def __init__(self, gitlab_service, local_storage):
        self.gitlab_service = gitlab_service
        self.local_storage = local_storage

    async def get_template(self, staff_id: str, template_id: str) -> Optional[dict]:
        """Get the current version of a rad template."""
        try:
            template = None
            
            # Try local disk first if enabled
            if settings.enable_local_disk_storage:
                template = self.local_storage.get_template(staff_id, template_id)
                if template:
                    if 'owner' not in template:
                        template['owner'] = ''
                    return template

            # If not found locally or local storage is disabled, try GitLab
            if settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                    file_path = f"data/rad-templates/{staff_prefix}/{template_id}.json"

                    file_content = self.gitlab_service.get_file_from_server(file_path)
                    if file_content:
                        template = json.loads(file_content)
                        if 'owner' not in template:
                            template['owner'] = ''
                        # Save the file locally for future use if local storage is enabled
                        if settings.enable_local_disk_storage:
                            self.local_storage.save_template(staff_id, template_id, template)
                        return template
                except Exception as e:
                    logger.error(f"Error getting rad template from GitLab: {e}")

            return template

        except Exception as e:
            logger.error(f"Error getting rad template: {e}")
            return None

    async def get_templates_for_staff(self, staff_id: str) -> list:
        """Get all rad templates for a staff member."""
        try:
            templates = []
            
            # Try local disk first if enabled
            if settings.enable_local_disk_storage:
                templates = self.local_storage.get_templates_for_staff(staff_id)
                if templates:
                    for template in templates:
                        if 'owner' not in template:
                            template['owner'] = ''
                    return templates

            # If not found locally or local storage is disabled, try GitLab
            if settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                    folder_path = f"data/rad-templates/{staff_prefix}"
                    
                    # Get all files in the folder
                    files = self.gitlab_service.get_files_in_folder(folder_path)
                    for file_info in files:
                        if file_info.endswith('.json'):
                            template_id = file_info.split('/')[-1].replace('.json', '')
                            template_content = self.gitlab_service.get_file_from_server(file_info)
                            if template_content:
                                template = json.loads(template_content)
                                if 'owner' not in template:
                                    template['owner'] = ''
                                
                                # Backward compatibility: convert macroID to templateID if needed
                                if 'macroID' in template and 'templateID' not in template:
                                    template['templateID'] = template.pop('macroID')
                                
                                templates.append(template)
                                
                                # Save the file locally for future use if local storage is enabled
                                if settings.enable_local_disk_storage:
                                    self.local_storage.save_template(staff_id, template_id, template)
                except Exception as e:
                    logger.error(f"Error getting rad templates from GitLab: {e}")

            return templates

        except Exception as e:
            logger.error(f"Error getting rad templates for staff: {e}")
            return []

    async def save_template(
        self,
        staff_id: str,
        template_id: str,
        content: dict,
    ) -> bool:
        """Save a rad template to available storage backends."""
        try:
            success = True

            # Add template type to content
            content["type"] = "rad-template"
            
            # Ensure templateID is set correctly
            if "templateID" not in content:
                content["templateID"] = template_id

            # Save to local disk if enabled
            if settings.enable_local_disk_storage:
                local_success = self._save_to_local_disk(staff_id, template_id, content)
                success = local_success and success
                if not local_success:
                    logger.warning(f"Failed to save rad template {template_id} to local disk")

            # Save to GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                gitlab_success = self._save_to_gitlab(staff_id, template_id, content)
                success = gitlab_success and success
                if not gitlab_success:
                    logger.warning(f"Failed to save rad template {template_id} to GitLab")

            return success
        except Exception as e:
            logger.error(f"Error saving rad template: {e}")
            return False

    async def delete_template(self, staff_id: str, template_id: str) -> bool:
        """Delete a rad template from available storage backends."""
        try:
            success = True

            # Delete from local disk if enabled
            if settings.enable_local_disk_storage:
                local_success = self.local_storage.delete_template(staff_id, template_id)
                success = local_success and success
                if not local_success:
                    logger.warning(f"Failed to delete rad template {template_id} from local disk")

            # Delete from GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                gitlab_success = self._delete_from_gitlab(staff_id, template_id)
                success = gitlab_success and success
                if not gitlab_success:
                    logger.warning(f"Failed to delete rad template {template_id} from GitLab")

            return success
        except Exception as e:
            logger.error(f"Error deleting rad template: {e}")
            return False

    def _save_to_local_disk(self, staff_id: str, template_id: str, content: dict) -> bool:
        """Save rad template to local disk."""
        return self.local_storage.save_template(staff_id, template_id, content)

    def _save_to_gitlab(self, staff_id: str, template_id: str, content: dict) -> bool:
        """Save rad template to GitLab."""
        try:
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-templates/{staff_prefix}/{template_id}.json"

            # Check if file exists in GitLab
            try:
                existing_content = self.gitlab_service.get_file_from_server(file_path)
                file_exists = existing_content is not None
            except:
                file_exists = False

            if file_exists:
                # Update existing file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Update rad template {template_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "update",
                                "file_path": file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )
            else:
                # Create new file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Create rad template {template_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "create",
                                "file_path": file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )

            logger.info(f"Successfully saved rad template {template_id} to GitLab")
            return True
        except Exception as e:
            logger.error(f"Error saving rad template to GitLab: {e}")
            return False

    def _delete_from_gitlab(self, staff_id: str, template_id: str) -> bool:
        """Delete rad template from GitLab."""
        try:
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-templates/{staff_prefix}/{template_id}.json"

            # Check if file exists in GitLab
            try:
                existing_content = self.gitlab_service.get_file_from_server(file_path)
                file_exists = existing_content is not None
            except:
                file_exists = False

            if file_exists:
                # Delete existing file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Delete rad template {template_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "delete",
                                "file_path": file_path,
                            }
                        ],
                    }
                )
                logger.info(f"Successfully deleted rad template {template_id} from GitLab")
                return True
            else:
                logger.warning(f"Rad template {template_id} not found in GitLab")
                return False

        except Exception as e:
            logger.error(f"Error deleting rad template from GitLab: {e}")
            return False

    def get_template_history(self, staff_id: str, template_id: str) -> list:
        """Get the commit history for a rad template."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get rad template history."
                )
                return []

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-templates/{staff_prefix}/{template_id}.json"
            return self.gitlab_service.get_history(file_path)
        except Exception as e:
            logger.error(f"Error getting rad template history: {e}")
            return []

    def get_template_version(
        self, staff_id: str, template_id: str, commit_hash: str
    ) -> Optional[dict]:
        """Get a specific version of a rad template."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get rad template version."
                )
                return None

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-templates/{staff_prefix}/{template_id}.json"
            file_content = self.gitlab_service.get_file_from_server(
                file_path, commit_hash
            )
            if file_content:
                return json.loads(file_content)
            return None

        except Exception as e:
            logger.error(f"Error getting rad template version: {e}")
            return None

    def compare_template_versions(
        self, staff_id: str, template_id: str, commit1: str, commit2: str
    ) -> Optional[str]:
        """Compare two versions of a rad template."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot compare rad template versions."
                )
                return None

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-templates/{staff_prefix}/{template_id}.json"
            return self.gitlab_service.compare_versions(file_path, commit1, commit2)
        except Exception as e:
            logger.error(f"Error comparing rad template versions: {e}")
            return None 