from app.utils.logger import logger
from app.config.settings import settings
import json
from typing import Optional, Dict
from datetime import datetime


class RadMacrosManager:
    def __init__(self, gitlab_service, local_storage):
        self.gitlab_service = gitlab_service
        self.local_storage = local_storage

    async def get_macro(self, staff_id: str, macro_id: str) -> Optional[dict]:
        """Get the current version of a rad macro."""
        try:
            # Try local disk first if enabled
            if settings.enable_local_disk_storage:
                local_macro = self.local_storage.get_macro(staff_id, macro_id, "rad")
                if local_macro:
                    return local_macro

            # If not found locally or local storage is disabled, try GitLab
            if settings.enable_gitlab_storage and self.gitlab_service:
                try:
                    staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
                    file_path = f"data/rad-macros/{staff_prefix}/{macro_id}.json"

                    file_content = self.gitlab_service.get_file_from_server(file_path)
                    if file_content:
                        # Save the file locally for future use if local storage is enabled
                        if settings.enable_local_disk_storage:
                            self.local_storage.save_macro(staff_id, macro_id, json.loads(file_content), "rad")
                        return json.loads(file_content)
                except Exception as e:
                    logger.error(f"Error getting rad macro from GitLab: {e}")

            return None

        except Exception as e:
            logger.error(f"Error getting rad macro: {e}")
            return None

    async def save_macro(
        self,
        staff_id: str,
        macro_id: str,
        content: dict,
    ) -> bool:
        """Save a rad macro to available storage backends."""
        try:
            success = True

            # Preserve the original type (text or dropdown) - don't override with "rad"
            # The type should already be set correctly from the frontend

            # Save to local disk if enabled
            if settings.enable_local_disk_storage:
                local_success = self._save_to_local_disk(staff_id, macro_id, content)
                success = local_success and success
                if not local_success:
                    logger.warning(f"Failed to save rad macro {macro_id} to local disk")

            # Save to GitLab if enabled and service is available
            if settings.enable_gitlab_storage and self.gitlab_service:
                gitlab_success = self._save_to_gitlab(staff_id, macro_id, content)
                success = gitlab_success and success
                if not gitlab_success:
                    logger.warning(f"Failed to save rad macro {macro_id} to GitLab")

            return success
        except Exception as e:
            logger.error(f"Error saving rad macro: {e}")
            return False

    def _save_to_local_disk(self, staff_id: str, macro_id: str, content: dict) -> bool:
        """Save rad macro to local disk."""
        return self.local_storage.save_macro(staff_id, macro_id, content, "rad")

    def _save_to_gitlab(self, staff_id: str, macro_id: str, content: dict) -> bool:
        """Save rad macro to GitLab."""
        try:
            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-macros/{staff_prefix}/{macro_id}.json"

            # Check if file exists in GitLab
            try:
                existing_content = self.gitlab_service.get_file_from_server(file_path)
                file_exists = existing_content is not None
            except:
                file_exists = False

            if file_exists:
                # Update existing file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Update rad macro {macro_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "update",
                                "file_path": file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )
            else:
                # Create new file
                self.gitlab_service.project.commits.create(
                    {
                        "branch": "main",
                        "commit_message": f"Create rad macro {macro_id} for staff {staff_id}",
                        "actions": [
                            {
                                "action": "create",
                                "file_path": file_path,
                                "content": json.dumps(content, indent=2),
                            }
                        ],
                    }
                )

            logger.info(f"Successfully saved rad macro {macro_id} to GitLab")
            return True
        except Exception as e:
            logger.error(f"Error saving rad macro to GitLab: {e}")
            return False

    def get_macro_history(self, staff_id: str, macro_id: str) -> list:
        """Get the commit history for a rad macro."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get rad macro history."
                )
                return []

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-macros/{staff_prefix}/{macro_id}.json"
            return self.gitlab_service.get_history(file_path)
        except Exception as e:
            logger.error(f"Error getting rad macro history: {e}")
            return []

    def get_macro_version(
        self, staff_id: str, macro_id: str, commit_hash: str
    ) -> Optional[dict]:
        """Get a specific version of a rad macro."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot get rad macro version."
                )
                return None

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-macros/{staff_prefix}/{macro_id}.json"
            file_content = self.gitlab_service.get_file_from_server(
                file_path, commit_hash
            )
            if file_content:
                return json.loads(file_content)
            return None

        except Exception as e:
            logger.error(f"Error getting rad macro version: {e}")
            return None

    def compare_macro_versions(
        self, staff_id: str, macro_id: str, commit1: str, commit2: str
    ) -> Optional[str]:
        """Compare two versions of a rad macro."""
        try:
            if not settings.enable_gitlab_storage or not self.gitlab_service:
                logger.warning(
                    "GitLab storage is disabled or service not available. Cannot compare rad macro versions."
                )
                return None

            staff_prefix = "global" if staff_id == "global" else f"s{staff_id}"
            file_path = f"data/rad-macros/{staff_prefix}/{macro_id}.json"
            return self.gitlab_service.compare_versions(file_path, commit1, commit2)
        except Exception as e:
            logger.error(f"Error comparing rad macro versions: {e}")
            return None 