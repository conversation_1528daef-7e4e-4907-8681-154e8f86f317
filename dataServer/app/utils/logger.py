import logging
import os
from logging.handlers import RotatingFileHandler
from pythonjsonlogger import jsonlogger
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

# Default logging configuration
DEFAULT_LOG_LEVEL = "INFO"


def setup_logger():
    """
    Configure and return a logger instance with both file and console handlers.
    Logging configuration is read from environment variables:
    - LOG_LEVEL: The logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    - LOG_FILE: The name of the log file (optional, if not set logs will only go to console)
    """
    # Get configuration from environment variables
    log_level = os.getenv("LOG_LEVEL", DEFAULT_LOG_LEVEL).upper()
    log_file = os.getenv("LOG_FILE", "./logs/dataServer.log")

    # Create logger
    logger = logging.getLogger("app")
    logger.setLevel(getattr(logging, log_level))

    # Clear any existing handlers
    logger.handlers = []

    # Standard log format
    standard_format = "%(asctime)s %(levelname)-8s [%(name)s:%(lineno)d] %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"

    # Create formatters
    json_formatter = jsonlogger.JsonFormatter(
        "%(asctime)s %(levelname)s %(name)s %(lineno)d %(message)s", datefmt=date_format
    )

    # Console formatter
    console_formatter = logging.Formatter(standard_format, datefmt=date_format)

    # File handler with rotation (create directory if it doesn't exist)
    try:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = RotatingFileHandler(
            log_file, maxBytes=10 * 1024 * 1024, backupCount=5  # 10MB
        )
        file_handler.setFormatter(json_formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        # If file logging fails, just log to console
        print(f"Warning: Could not set up file logging: {e}")

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    return logger


# Create a default logger instance
logger = setup_logger()
