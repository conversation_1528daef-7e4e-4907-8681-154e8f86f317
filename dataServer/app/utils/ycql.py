from typing import Async<PERSON>enerator
from contextlib import asynccontextmanager
import asyncio
import json
import os
from typing import Dict, Any, Optional
from app.utils.logger import logger

YCQLD_COMM_END = b"\nYQ\x17\x03\x04\n"


class AsyncYCQLClient:
    """Asynchronous client for YCQL operations using UNIX domain sockets"""

    def __init__(self, socket_path: str):
        self.socket_path = socket_path
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        self._lock = asyncio.Lock()  # For thread-safe socket operations

    async def connect(self):
        """Establish connection to YCQL daemon"""
        if not self.writer or self.writer.is_closing():
            self.reader, self.writer = await asyncio.open_unix_connection(
                self.socket_path
            )

    async def close(self):
        """Close the connection gracefully"""
        if self.writer:
            self.writer.close()
            await self.writer.wait_closed()

    async def _send_recv(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Atomic send-receive operation with error handling"""
        async with self._lock:
            await self.connect()

            # Serialize and send data
            data_bytes = json.dumps(data).encode() + YCQLD_COMM_END
            if self.writer is None:
                raise ConnectionError("Writer is not initialized")
            self.writer.write(data_bytes)
            await self.writer.drain()

            # Receive data until terminator
            buffer = b""
            if self.reader is None:
                raise ConnectionError("Reader is not initialized")
            while True:
                chunk = await self.reader.read(4096)
                if not chunk:
                    break
                buffer += chunk
                if YCQLD_COMM_END in buffer:
                    term_pos = buffer.find(YCQLD_COMM_END)
                    json_data = buffer[:term_pos]
                    return json.loads(json_data.decode())

            raise ConnectionError("Connection closed unexpectedly")

    async def execute(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a query and return parsed results"""
        try:
            response = await self._send_recv(query)
            self._validate_response(response)
            return self._parse_response(response)
        except (json.JSONDecodeError, ConnectionError) as e:
            raise RuntimeError(f"YCQL operation failed: {str(e)}") from e

    def _validate_response(self, response):
        """Validate server response structure"""
        if not isinstance(response, list):
            raise ValueError("Unexpected response format")

        for result in response:
            if "ErrorMsg" in result:
                raise RuntimeError(result["ErrorMsg"])

    def _parse_response(self, response) -> Dict[str, Any]:
        """Parse normalized response structure"""
        return {
            item.get("ResId", "Result"): item.get("Result", []) for item in response
        }


async def create_ycql_client() -> AsyncYCQLClient:
    """Factory function for dependency injection"""
    socket_path = (
        os.environ.get("YCQLD_SOCKET_PATH")
        or f"{os.environ['CCRUN']}/workdir/ycql/ycqlsocket"
    )

    if not os.path.exists(socket_path):
        raise RuntimeError(f"YCQL socket not found at {socket_path}")

    logger.info(f"YCQL socket path: {socket_path}")

    client = AsyncYCQLClient(socket_path)
    await client.connect()
    return client


@asynccontextmanager
async def get_ycql_client() -> AsyncGenerator[AsyncYCQLClient, None]:
    client = await create_ycql_client()
    try:
        yield client
    finally:
        await client.close()
