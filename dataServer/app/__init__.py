from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routes.notes import notes_router
from app.routes.note_macros import note_macros_router
from app.routes.rad_macros import rad_macros_router
from app.routes.exam import exam_router
from app.routes.rad_templates import rad_templates_router
from app.utils.logger import logger


def create_app():
    app = FastAPI(title="Data Server API", version="1.0.0")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure this properly for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(notes_router, prefix="/api/patients", tags=["notes"])
    app.include_router(note_macros_router, prefix="/api/staff", tags=["note-macros"])
    app.include_router(rad_macros_router, prefix="/api/staff", tags=["rad-macros"])
    app.include_router(exam_router, prefix="/api/patients", tags=["exam"])
    app.include_router(rad_templates_router, prefix="/api/staff", tags=["rad-templates"])

    @app.get("/health")
    def health_check():
        return {"status": "healthy"}

    return app
