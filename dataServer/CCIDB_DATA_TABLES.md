# CCIDB Data Tables Documentation

## Overview

This document lists all data tables used in the CCIDB (YottaDB) codebase, organized by category and purpose. CCIDB uses a hierarchical table structure with JIT (Job Item Type) and NIT (Node Item Type) identifiers.

## 📊 **Core Data Tables**

### **AI Notes System Tables**

| Table Name | JIT Function | Purpose | Description |
|------------|--------------|---------|-------------|
| **AI Notes** | `itlist('AI Notes')` | Primary note storage | Stores the actual note content and metadata |
| **AI Notes Signature** | `itlist('AI Notes Signature')` | Note signatures | Stores digital signatures for notes with note type awareness |
| **AI Notes ReqSignature** | `itlist('AI Notes ReqSignature')` | Required signatures | Tracks which notes require signatures |

### **Physical Exam System Tables**

| Table Name | JIT Function | Purpose | Description |
|------------|--------------|---------|-------------|
| **Physical Exam** | `itlist('Physical Exam')` | Exam data storage | Stores physical examination data and results |

## 🏗️ **Table Structure**

### **Standard CCIDB Table Schema**

All CCIDB tables follow this structure:
```sql
ppdo (jit, nit, key, data, lgname, lgtime, edflag)
```

**Fields:**
- **jit**: Job Item Type (table identifier)
- **nit**: Node Item Type (sub-table identifier, now mapped via config)
- **key**: Primary key for the record
- **data**: Actual data content (JSON, text, etc.)
- **lgname**: Last modified by (user/system)
- **lgtime**: Last modified timestamp
- **edflag**: Edit flag for concurrency control

### **Table Access Patterns**

```ycql
-- Query by JIT (table)
select * from ppdo where jit = itlist('AI Notes');

-- Query by JIT and NIT (sub-table)
select * from ppdo where jit = itlist('AI Notes Signature') and nit = 0;

-- Query by JIT and key
select * from ppdo where jit = itlist('AI Notes') and key = :key;

-- Query by JIT and NIT (note type)
select * from ppdo where jit = itlist('AI Notes') and nit = :nit;
```

## 🔧 **Table Operations**

### **1. AI Notes Table**

**Purpose**: Primary note content storage
**Structure**: `(nit, key, data)` where `nit` is the note type (from config) and `data` contains the note content

**Operations:**
```ycql
-- Store note data
insert into p{itlist('AI Notes')} (nit, key, data) values (:nit, :key, :data);

-- Retrieve note data
select data from ppdo where jit = itlist('AI Notes') and key = :key and nit = :nit;

-- Update note data
update p{itlist('AI Notes')} set data = :data where key = :key and nit = :nit;
```

### **2. AI Notes Signature Table**

**Purpose**: Digital signature storage with note type awareness
**Structure**: `(nit, key, data)` where `nit` is the note type NIT and `data` contains signature JSON

**Operations:**
```ycql
-- Store signature with note type NIT
insert into p{itlist('AI Notes Signature')} (nit, key, data) values 
(:nit, :key, json_object(
    'signedBy', getrwinfo('staffid'),
    'signedAt', lgtime(),
    'comment', :comment,
    'noteTypeNit', :nit
));

-- Retrieve signatures filtered by note type
select lgsid, lgtime, data, nit from ppdo where jit = itlist('AI Notes Signature') and key = :key;
```

### **3. AI Notes ReqSignature Table**

**Purpose**: Track required signatures for notes
**Structure**: `(nit, key, data)` where `nit` is the note type NIT and `data` contains requirement information

**Operations:**
```ycql
-- Set required signature
insert into p{itlist('AI Notes ReqSignature')} (nit, key, data) values (:nit, :key, :requirement_data);

-- Check required signature
select data from ppdo where jit = itlist('AI Notes ReqSignature') and key = :key and nit = :nit;
```

### **4. Physical Exam Table**

**Purpose**: Physical examination data storage
**Structure**: `(nit, key, data)` where `data` contains exam results

**Operations:**
```ycql
-- Store exam data
insert into p{itlist('Physical Exam')} (nit, key, data) values (0, :key, :exam_data);

-- Retrieve exam data
select data from ppdo where jit = itlist('Physical Exam') and key = :key;
```

## 🔄 **Table Relationships**

### **Note Type System**
```
Config (type → NIT, required_signature)
    ↓
AI Notes (content storage)
    ↓
AI Notes Signature (signatures with NIT)
    ↓
AI Notes ReqSignature (requirements)
```

### **Data Flow**
1. **Note Type Configuration**: Config file maps type to NIT and required signature
2. **Note Storage**: `AI Notes` table stores content with correct NIT
3. **Signature Storage**: `AI Notes Signature` stores signatures with note type NIT
4. **Requirement Tracking**: `AI Notes ReqSignature` tracks signature requirements

## 🛠️ **Table Management**

### **Table Creation**
Tables are created using the `make_group_vtables.lib` library:
```ycql
.file:lib/make_group_vtables.lib
select setvar('_mkvts_', 'dbis', 'AI Notes|AI Notes Signature|AI Notes ReqSignature');
.exec:_mkvts_;
```

### **Table Cleanup**
```ycql
-- Drop temporary tables
drop table if exists temp._dbi_data;
drop table if exists temp._note_types;
drop table if exists temp._signatures;
```

## 📈 **Performance Considerations**

### **Indexing Strategy**
- **Primary Index**: `(jit, nit, key)` for fast lookups
- **Secondary Index**: `(jit, data)` for content searches
- **Temporal Index**: `(jit, lgtime)` for time-based queries

### **Query Optimization**
```ycql
-- Efficient: Use specific JIT, key, and nit
select data from ppdo where jit = itlist('AI Notes') and key = :key and nit = :nit;
```

### **Batch Operations**
```ycql
-- Batch insert for better performance
begin;
insert or rollback into p{itlist('AI Notes')} (nit, key, data) values (:nit, :key, :data);
commit;
```

## 🔒 **Security and Permissions**

### **Authentication**
All table operations require authentication:
```ycql
select setvar('authen/gettoken', 'appname', 'NotesConfig');
select setvar('authen/gettoken', 'perm', 'W');
.file:authen/gettoken/output.ycql
select case when ifnull(getrwinfo('staffid'), -1) = -1 then errorout('Authentication is required!') end noauthen;
```

### **Permission Levels**
- **R**: Read access
- **W**: Write access
- **D**: Delete access
- **A**: Admin access

### **Data Validation**
```ycql
-- Validate required parameters
select ifnull(:key, errorout('key is required'));
select ifnull(:nit, errorout('nit is required'));

-- Validate data exists before update
select case when (select data from ppdo where jit = 'AI Notes' and key = :key and nit = :nit) is null 
    then errorout('Note does not exist') end;
```

## 📋 **Table Usage Examples**

### **Complete Note Workflow**
```ycql
-- 1. Lookup NIT and required signature from config (in backend)
-- 2. Store note content
insert into p{itlist('AI Notes')} (nit, key, data) values (:nit, :key, :note_content);

-- 3. Set signature requirement if needed
insert into p{itlist('AI Notes ReqSignature')} (nit, key, data) values (:nit, :key, :requirement);

-- 4. Sign the note
insert into p{itlist('AI Notes Signature')} (nit, key, data) values 
(:nit, :key, json_object('signedBy', getrwinfo('staffid'), 'signedAt', lgtime()));

-- 5. Retrieve note with signatures
select n.data as note_content, s.data as signature_data
from ppdo n left join ppdo s on n.key = s.key and n.nit = s.nit
where n.jit = itlist('AI Notes') and s.jit = itlist('AI Notes Signature') and n.key = :key and n.nit = :nit;
```

### **Note Type Management**
- Note types and NITs are now managed via backend config, not a database table.

## 🔍 **Monitoring and Maintenance**

### **Table Statistics**
```ycql
-- Count records per table
select jit, count(*) as record_count from ppdo group by jit;

-- Count records per note type
select nit, count(*) as signature_count 
from ppdo where jit = itlist('AI Notes Signature') group by nit;
```

### **Data Integrity Checks**
```ycql
-- Check for orphaned signatures
select s.key from ppdo s 
left join ppdo n on s.key = n.key and s.nit = n.nit
where s.jit = itlist('AI Notes Signature') 
and n.jit = itlist('AI Notes') 
and n.key is null;
```

This comprehensive documentation provides a complete overview of all data tables used in the CCIDB codebase, their relationships, and usage patterns. 