# CCIDB Note Types Implementation

## Overview

This implementation provides dynamic note type management in CCIDB using a dedicated "AI Notes Types" table. The system automatically creates new note types as needed and maps them to NIT (Node Item Type) values.

## Architecture

### Database Structure

```
AI Notes Types Table:
- JIT: 'AI Notes Types'
- NIT: 0 (always)
- Key: NIT value (0, 1, 2, 3, 4, ...)
- Data: Note type name as simple string
```

### Default Note Types

The system comes with these default note types:
- Progress Note (NIT: 0)
- ED Progress Note (NIT: 1)
- Discharge Summary (NIT: 2)
- Consultation Note (NIT: 3)
- Procedure Note (NIT: 4)

## YCQL Files

### 1. Initialization
**File:** `HOBJS/ainotes/config/notetypes/init.ycql`
- Creates the "AI Notes Types" table
- Inserts default note types
- Sets up the basic mapping structure

### 2. Dynamic NIT Lookup
**File:** `HOBJS/ainotes/config/notetypes/getnit.ycql`
- Gets NIT for existing note type
- Creates new note type with next available NIT if not found
- Returns the NIT value

### 3. Get All Note Types
**File:** `HOBJS/ainotes/config/notetypes/getall.ycql`
- Retrieves all note type mappings
- Returns JSON format with NIT and name pairs

### 4. Get Note Type Name
**File:** `HOBJS/ainotes/config/notetypes/getname.ycql`
- Gets note type name by NIT value

## Updated Signature System

### Signature Signing
**File:** `HOBJS/ainotes/signature/sign/init.ycql`
- Uses dynamic NIT lookup
- Stores signature with note type information
- Supports both existing and new note types

### Signature Retrieval
**File:** `HOBJS/ainotes/signature/getsignatures/query.ycql`
- Retrieves signatures by note type
- Returns note type information in results

## Python Integration

### API Changes

The API now uses `note_type` instead of `type`:

```python
class SignNoteRequest(BaseModel):
    note_type: str  # Changed from type to note_type
    comment: str
    staffid: Optional[int] = None
    ccitoken: Optional[str] = None
```

### CCIDB Storage Methods

```python
# Sign note with dynamic note type
await ccidb_storage.sign_note(
    campus, pat_id, note_type, key, comment, staffid, ccitoken
)

# Get all note types
await ccidb_storage.get_all_note_types(campus, staffid, ccitoken)
```

## Usage Examples

### 1. Initialize Note Types Table
```bash
# Run the initialization script
ycql HOBJS/ainotes/config/notetypes/init.ycql
```

### 2. Sign Note with Existing Type
```bash
curl -X POST "http://localhost:8000/campus1/12345/notes/1234567890/sign" \
  -H "Content-Type: application/json" \
  -d '{
    "note_type": "Progress Note",
    "comment": "Reviewed and approved",
    "staffid": 12345,
    "ccitoken": "abc123"
  }'
```

### 3. Sign Note with New Type
```bash
curl -X POST "http://localhost:8000/campus1/12345/notes/1234567890/sign" \
  -H "Content-Type: application/json" \
  -d '{
    "note_type": "Custom Note Type",
    "comment": "Reviewed and approved",
    "staffid": 12345,
    "ccitoken": "abc123"
  }'
```

## Benefits

1. **Dynamic Note Types**: No hardcoding, automatically creates new types
2. **Simple Mapping**: Just NIT → name, no complex attributes
3. **ACID Transactions**: Safe concurrent access
4. **Persistent Storage**: Survives restarts
5. **Backward Compatible**: Works with existing note types
6. **Multi-Process Safe**: Shared across all processes
7. **Easy Maintenance**: Simple table structure

## Testing

Run the test script to verify the implementation:

```bash
python3 tests/test_ccidb_note_types.py
```

## Migration Notes

- Existing signatures will continue to work
- New signatures will use the dynamic note type system
- The API parameter changed from `type` to `note_type`
- All storage backends (CCIDB, Local, GitLab) support the new parameter name 