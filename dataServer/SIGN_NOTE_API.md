# Sign Note API Documentation

## Overview

The Sign Note API allows you to sign notes using any available storage backend (CCIDB, Local Disk, or GitLab). The system will attempt to sign the note in all enabled storage backends, providing maximum compatibility and redundancy.

## Endpoint

```
POST /api/patients/{campus}/{pat_id}/notes/{note_id}/sign
```

## Parameters

### Path Parameters
- `campus` (string): The campus identifier
- `pat_id` (string): The patient ID (must end with a number)
- `note_id` (string): The note ID (can be any string format)

### Request Body
```json
{
  "type": "string",
  "comment": "string", 
  "staffid": "integer (optional)",
  "ccitoken": "string (optional)"
}
```

### Request Body Fields
- `type` (string, required): The type of note being signed
- `comment` (string, required): The comment to include with the signature
- `staffid` (integer, optional): The staff ID for authentication (required for CCIDB)
- `ccitoken` (string, optional): The CCIDB token for authentication (required for CCIDB)

## Response

### Success Response (200 OK)
```json
{
  "message": "Note signed successfully"
}
```

### Error Responses

#### 400 Bad Request
- Invalid patID format (must end with a number)
- No storage backends are enabled

#### 404 Not Found
- Note not found

#### 500 Internal Server Error
- Failed to sign note
- Other server errors

## Prerequisites

1. **At Least One Storage Backend Enabled**: At least one of the following must be enabled:
   - `enable_ccidb_storage`: Enable/disable CCIDB storage
   - `enable_local_disk_storage`: Enable/disable local disk storage
   - `enable_gitlab_storage`: Enable/disable GitLab storage

2. **CCIDB Credentials** (if CCIDB is enabled): Valid `staffid` and `ccitoken` for CCIDB authentication

3. **Note ID Format**: 
   - For CCIDB: Must be numeric (e.g., "12345")
   - For Local/GitLab: Can be any string format

## Example Usage

### cURL Example
```bash
curl -X POST "http://localhost:8000/api/patients/main/12345/67890/sign" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "progress_note",
    "comment": "Reviewed and approved",
    "staffid": 12345,
    "ccitoken": "your_ccitoken_here"
  }'
```

### Python Example
```python
import requests

url = "http://localhost:8000/api/patients/main/12345/67890/sign"
data = {
    "type": "progress_note",
    "comment": "Reviewed and approved", 
    "staffid": 12345,
    "ccitoken": "your_ccitoken_here"
}

response = requests.post(url, json=data)
if response.status_code == 200:
    print("Note signed successfully")
else:
    print(f"Error: {response.status_code} - {response.text}")
```

## Implementation Details

### Storage Backend Support
- **CCIDB Storage**: ✅ Fully supported (requires staffid/ccitoken)
- **Local Storage**: ✅ Fully supported (no credentials required)
- **GitLab Storage**: ✅ Fully supported (no credentials required)

### Multi-Storage Operation
The system operates on a "best effort" basis:
1. **CCIDB**: If enabled and credentials provided, attempts to sign via CCIDB
2. **Local Storage**: If enabled, adds signature to local JSON file
3. **GitLab Storage**: If enabled, commits signature changes to GitLab repository

The API returns success if **at least one** storage backend successfully signs the note.

### Signature Data Structure
When a note is signed, the following signature information is added:

```json
{
  "signatures": [
    {
      "signedBy": 12345,
      "signedAt": "2024-01-15T10:30:00.000Z",
      "comment": "Reviewed and approved",
      "noteType": "progress_note"
    }
  ],
  "signed": true,
  "lastSignedAt": "2024-01-15T10:30:00.000Z",
  "lastSignedBy": 12345
}
```

### Architecture
The sign_note functionality follows the established architecture patterns:

1. **API Route** (`/app/routes/notes.py`): Handles HTTP requests and validation
2. **Manager** (`/app/services/managers/notes_manager.py`): Orchestrates the operation across all storage backends
3. **Storage** (`/app/services/storage/*.py`): Performs the actual storage operations

### Validation
The API performs several validations:
- Patient ID format validation
- Storage backend availability check
- Credential validation (for CCIDB only)
- Note existence validation

### Error Handling
- Graceful error handling with appropriate HTTP status codes
- Detailed error messages for debugging
- Logging of all operations for audit trails
- Partial success handling (if some storage backends fail)

## Notes

- **Backward Compatibility**: The API maintains backward compatibility with existing CCIDB-only implementations
- **Flexible Credentials**: staffid/ccitoken are optional for non-CCIDB storage
- **Audit Trail**: All signature operations are logged for audit purposes
- **Version Control**: GitLab storage provides automatic version control for signatures
- **Local Backup**: Local storage provides immediate backup of signature data 