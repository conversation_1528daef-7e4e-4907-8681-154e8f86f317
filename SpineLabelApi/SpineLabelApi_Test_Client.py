#!/eerun/Python3/bin/python3

import requests

# Define the API endpoint
url = "http://127.0.0.1:8000/SpineLabelApi"

# Create the data to be sent in the POST request
data = {
    "campus": "nndmc",
    "patientid": "123456789",
    "studyid": "1.2.840.113754.1.4.549.6749788.8841.1.549.21125.1609",
    "seriesid": "1.3.12.2.1107.5.2.51.184124.30000025021012413367400012159",
}

# Send the POST request
response = requests.post(url, json=data)

# Check the response
if response.status_code == 200:
    print("Request successful!")
    print("Response:", response.json())
else:
    print("Request failed with status code:", response.status_code)
    print("Response:", response.text)
