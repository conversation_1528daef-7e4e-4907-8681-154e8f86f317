#!/eerun/Python3/bin/python3

import os
import json
import uvicorn
from fastapi import FastAPI, Body, HTTPException
from typing import Optional
import logging

app = FastAPI()

logging.basicConfig(level=logging.INFO,
                    filename='/tmp/SpineLabelApi.log',
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_labels(campus: str, patientid: str, studyid: str, seriesid: str):
    TOP_DIR = f"/eerun/workdir/dicom/spinelabel/{campus}"
    labels_file = f"{TOP_DIR}/{patientid}/{studyid}/{seriesid}/labels/all_labels.json"
    logger.info(f"Attempting to read labels from: {labels_file}")
    try:
        if os.path.exists(labels_file):
            with open(labels_file, "r") as fp:
                labels = json.load(fp)
                return labels
        else:
            logger.warning(f"Label file does not exist: {labels_file}")
            return []
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {labels_file}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error reading labels file")
    except Exception as e:
        logger.error(f"Unexpected error reading {labels_file}: {str(e)}")
        raise HTTPException(status_code=500, detail="Unexpected error reading labels file")


@app.post("/SpineLabelApi")
def spine_label_api(
    campus: str = Body(min_length=1, max_length=64, description="Campus"),
    patientid: str = Body(min_length=1, max_length=64, description="Patient ID"),
    studyid: str = Body(min_length=1, max_length=128, description="Study ID"),
    seriesid: str = Body(min_length=1, max_length=128, description="Series ID")
):
    logger.info(f"Received request for patient: {patientid}, study: {studyid}, series: {seriesid}")
    
    try:
        return get_labels(patientid, studyid, seriesid)
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
