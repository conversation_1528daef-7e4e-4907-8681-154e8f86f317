#-- check perms
select setvar('authen/gettoken', 'appname', 'NotesSign');
select setvar('authen/gettoken', 'perm', (select case when (select data from ppdo where jit = 'Physical Exam' and key = :key) is null
    then 'W'
    else 'E'
end));
.file:authen/gettoken/output.ycql
select case when ifnull(getrwinfo( 'staffid' ), -1) = -1 then errorout( 'Authentication is required!' ) end noauthen;
select case when nullorblank(getrwinfo('tty')) then errorout( 'Terminal name is required!' ) end notty;

select setvar('stmt', "insert into p" || itlist('Physical Exam') || "(nit, key, data) values " ||
    "(0, " || quote(:key) || ", " || quote(:data) || ");");

.exec:stmt;
