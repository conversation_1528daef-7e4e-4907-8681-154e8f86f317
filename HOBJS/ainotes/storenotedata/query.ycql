#-- check perms
select setvar('authen/gettoken', 'appname', 'NotesSign');
select setvar('authen/gettoken', 'perm', (select case when (select data from ppdo where jit = 'AI Notes' and key = :key) is null
    then 'W'
    else 'E'
end));
.file:authen/gettoken/output.ycql
select case when ifnull(getrwinfo( 'staffid' ), -1) = -1 then errorout( 'Authentication is required!' ) end noauthen;
select case when nullorblank(getrwinfo('tty')) then errorout( 'Terminal name is required!' ) end notty;

select setvar('stmt', "insert into p" || itlist('AI Notes') || "(nit, key, data) values " ||
    "(" || quote(:nit) || ", " || quote(:key) || ", " || quote(:data) || ");");
.exec:stmt;

select var('required_signature', ifnull(:required_signature, 0));

.if:required_signature;
    select setvar('stmt', "insert into p" || itlist('AI Notes ReqSignature') || "(nit, key, data) values " ||
        "(" || quote(:nit) || ", " || quote(:key) || ", null);");
    .exec:stmt;
.endif;

create table temp._result as select data from ppdo where jit = 'AI Notes' and key = :key and nit = :nit;
