select var('key', ifnull(:key, ''));
.if:key;
    -- First, get the note from AI Notes having the same key and nit
    create table temp._notes as
      select key, nit, data
      from ppdo 
    where jit = itlist('AI Notes') and key = :key and nit = :nit;

.else;
    -- First, get all AI Notes
    create table temp._notes as
      select key, nit, data
    from ppdo 
    where jit = itlist('AI Notes');

.endif;

-- Then, get signatures separately
create table temp._signatures as
  select key, nit, data as signature_data
  from ppdo 
  where jit = itlist('AI Notes Signature');

-- Finally, combine them
create table temp._result as
  select n.key, n.nit, n.data, s.signature_data
  from temp._notes n
  left join temp._signatures s on n.key = s.key and n.nit = s.nit;

