--check key and nit
select ifnull(:key, errorout('key is required'));
select ifnull(:nit, errorout('nit is required'));

--use reportTime to scope the current signatures
select setvar('reportTime', (select lgtime from ppdo where jit = itlist('AI Notes') and nit = :nit and key = :key));

select seteditflag(1);

-- Attach staff database to get full names
attach '$CCSYSDIR/staff.db' as staffDb;

drop table if exists temp._signatures;
create temp table _signatures(signedBy integer, signedByName text, time integer, comment text, noteTypeNit integer);
insert into temp._signatures
select s.lgsid, ifnull(st.fullname, 'Unknown Staff'), s.lgtime, s.data, s.nit 
from ppdo s
left join staffDb.t_staff st on s.lgsid = st.sid
where s.jit = itlist('AI Notes Signature')
    and s.key = :key
    and s.nit = :nit
    and s.lgtime >= getvar('reportTime');

select setvar('jresult', (select tableto<PERSON>son(
    "select signedBy 'signed_by', signedByName 'signed_by_name', time, comment, noteTypeNit 'note_type_nit' from temp._signatures",
'fields', 'records')));

detach staffDb;
select seteditflag(0);
