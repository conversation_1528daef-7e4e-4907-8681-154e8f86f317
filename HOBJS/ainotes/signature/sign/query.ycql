.file:ainotes/signature/getsignatures/query.ycql

select errorout('Signature already exists: signedBy=' || signedBy) from temp._signatures
where signedBy = getrwinfo('staff');

select setvar('stmt', (select
    'begin;' ||
    group_concat('insert or rollback into p' || jit || ' (nit, key, data) ' ||
    'select nit, key, data from temp._dbi_data where jit = ' || jit || ';', '') ||
    'commit;'
from (select distinct jit from temp._dbi_data)));

.exec:stmt;
