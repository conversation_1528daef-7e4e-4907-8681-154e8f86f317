#-- check perms
select setvar('authen/gettoken', 'appname', 'NotesSign');
select setvar('authen/gettoken', 'perm', 'W');
.file:authen/gettoken/output.ycql
select case when ifnull(getrwinfo( 'staffid' ), -1) = -1 then errorout( 'Authentication is required!' ) end noauthen;
select case when nullorblank(getrwinfo('tty')) then errorout( 'Terminal name is required!' ) end notty;

.file:lib/make_group_vtables.lib
select setvar('_mkvts_', 'dbis', 'AI Notes Signature|AI Notes ReqSignature');
.exec:_mkvts_;

--check key
select ifnull(:key, errorout('key is required'));

drop table if exists temp._dbi_data;
create temp table _dbi_data (jit int, nit int, key int, data text);

-- Store signature with NIT from backend
select var('now', currenthosttime());

-- Attach staff database to get full name
attach '$CCSYSDIR/staff.db' as staffDb;
select setvar('signedByName', (select fullname from staffDb.t_staff where sid = getrwinfo('staffid')));

insert into temp._dbi_data
select itlist('AI Notes Signature'), :nit, :key, 
    json_object(
        'signedBy', getrwinfo('staffid'),
        'signedByName', getvar('signedByName'),
        'signedAt', var('now'),
        'comment', :comment,
        'noteTypeNit', :nit
    );

detach staffDb;

select openargs(0,1);

