#-- check perms
select setvar('authen/gettoken', 'appname', 'NotesSign');
select setvar('authen/gettoken', 'perm', 'E');
.file:authen/gettoken/output.ycql
select case when ifnull(getrwinfo( 'staffid' ), -1) = -1 then errorout( 'Authentication is required!' ) end noauthen;
select case when nullorblank(getrwinfo('tty')) then errorout( 'Terminal name is required!' ) end notty;

.file:lib/make_group_vtables.lib
select setvar('_mkvts_', 'dbis', 'AI Notes ReqSignature');
.exec:_mkvts_;

--check reqsid and nit
select ifnull(:reqsid, errorout('reqsid is requried'));
select ifnull(:nit, errorout('nit is required'));

select setvar('sid', :reqsid);
attach '$CCSYSDIR/staff.db' as staffDb;
select case :reqsid != ''
    when 1 then ifnull((select sid from staffDb.t_staff where sid = :reqsid), errorout('reqsid is an unknown sid: ' || :reqsid))
    end;
detach staffDb;

--check key
select ifnull(:key, errorout('key is requried'));

select openargs(0,1);

