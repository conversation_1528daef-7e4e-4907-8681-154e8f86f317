#-- check perms
select setvar('authen/gettoken', 'appname', 'NotesSign');
select setvar('authen/gettoken', 'perm', (select case when (select data from ppdo where jit = 'AI Notes' and key = :key) is null
    then 'W'
    else 'E'
end));
.file:authen/gettoken/output.ycql
select case when ifnull(getrwinfo( 'staffid' ), -1) = -1 then errorout( 'Authentication is required!' ) end noauthen;
select case when nullorblank(getrwinfo('tty')) then errorout( 'Terminal name is required!' ) end notty;

select setvar('stmt', "update p" || itlist('AI Notes') || " set data = " || quote(:data) || " where key = " || quote(:key) || " and nit = " || quote(:nit) || ";");
.exec:stmt;

create table temp._result as select data from ppdo where jit = 'AI Notes' and key = :key and nit = :nit;
